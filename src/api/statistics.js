/*
 * @Description: 统计模块的js文件
 */

import http from '@/axios'

/* -------------------------------特殊需求的接口 ------------------------------- */
// 图表中心业绩看板穿透接口 '/dsValue/sys/result/through'
export function getAchievementSpecialThrough(data) {
  return http({
    url: '/dsValue/sys/result/through',
    data
  })
}

// 特殊的导出接口 '/dsValue/sys/export/data'
export function specialExport(data) {
  return http({
    url: '/dsValue/sys/export/data',
    data
  })
}

// 特殊的穿透页导出接口 '/dsValue/sys/export/through'
export function specialPenetrateExport(data) {
  return http({
    url: '/dsValue/sys/export/through',
    data
  })
}

// 获取员工业绩目标/客户资产盘点 dsValue/sys/result
export function getSpecialPerformance(data) {
  return http({
    url: '/dsValue/sys/result',
    data
  })
}

// 获取业绩目标看板统计规则字段可选列表
export function getStatisticsRule(data) {
  return http({
    url: '/chart/customerTextAttr',
    data
  })
}

// 展示页菜单列表
export function menuListGet(data) {
  return http({
    url: '/chart/category/list',
    data
  })
}
// 保存&编辑分组
export function groupOperate(data) {
  return http({
    url: '/chart/category/save',
    data
  })
}
// 分组开关
export function groupSwitch(data) {
  return http({
    url: '/chart/category/enable',
    data
  })
}
// 删除分组
export function groupDelete(data) {
  return http({
    url: '/chart/category/delete',
    data
  })
}
// 菜单排序
export function menuSort(data) {
  return http({
    url: '/chart/category/sort',
    data
  })
}
// 图表发布位置
export function chartRelease(data) {
  return http({
    url: '/chart/publish',
    data
  })
}
// 获取数据来源
export function getAllFormList(data) {
  return http({
    url: '/form/allList',
    data
  })
}
// 获取待选字段
export function getDataSourceAttr(data) {
  return http({
    url: '/chart/dataSourceAttr',
    data
  })
}
// 自助数据集待选字段
export function getdataSetAttr(data) {
  return http({
    url: '/dataSet/dataSourceAttr',
    data
  })
}
// 图表表单数据限制
export function chartDataLimit(data) {
  return http({
    url: '/chart/chartDataLimit',
    data
  })
}

// 图表基本信息列表
export function getChartBaseList(data) {
  return http({
    url: '/chart/baseList',
    data
  })
}

// scrm-图表基本信息列表
export function getChartWechatBaseList(data) {
  return http({
    url: '/chart/chartWechatBaseList',
    data
  })
}
// 删除指标
export function deleteChartBase(data) {
  return http({
    url: '/chart/delete',
    data
  })
}
// 指标保存
export function saveChartBase(data) {
  return http({
    url: '/chart/value/save',
    data
  })
}
// 指标编辑
export function editChartBase(data) {
  return http({
    url: '/chart/edit',
    data
  })
}
// 系统指标保存
export function saveChartSystemBase(data) {
  return http({
    url: '/chart/system/update',
    data
  })
}

// 看板设置左侧菜单展示
export function getCategoryList(data) {
  return http({
    url: '/chart/category/index/list',
    data
  })
}

export function searchSourceAttr(data) {
  return http({
    url: '/chart/searchSourceAttr',
    data
  })
}
// 保存报表
export function saveChart(data) {
  return http({
    url: '/chart/save',
    data
  })
}
// 保存报表位置信息
export function saveChartPosition(data) {
  return http({
    url: '/chart/update/dashboard',
    data
  })
}

// 2.2.14逻辑比值条件（在流转条件所有筛选项的基础上做了修改）
export function getConditionList(data) {
  return http({
    url: '/condition/list',
    data
  })
}
// 获取图表展示类型
export function getShowType(data) {
  return http({
    url: '/chart/getShowType',
    data
  })
}

// 获取二级分组下的图表列表
export function getChartList(data) {
  return http({
    url: '/chart/list',
    data
  })
}
// 获取联动设置显示的图表
export function getLinkageList(data) {
  return http({
    url: '/chart/linkage/list',
    data
  })
}

// 获取报表结果
export function getChartResult(data) {
  return http({
    url: '/chart/result',
    data
  })
}

// 获取数据集报表结果
export function getDataSetChartResult(data) {
  return http({
    url: '/dsValue/result',
    data
  })
}

// 保存排序信息
export function submitSortInfo(data) {
  return http({
    url: '/chart/update/sort',
    data
  })
}
// 获取员工呼入呼出统计分析结果
export function getChartResultCallStatistics(data) {
  return http({
    url: '/chart/result/call/statistics',
    data
  })
}
// 获取员工呼入呼出统计分析结果
export function getChartResultCallStatisticsTotal(data) {
  return http({
    url: '/chart/result/call/statistics/total',
    data
  })
}
// export function getChartResult (data) {
//   return http({
//     url: '/chart/result/copy',
//     method: 'post',
//     data
//   })
// }

// 简报看板数据，遗忘提醒/客户关怀
export function getChartResultValue(data) {
  return http({
    url: '/chart/result/value',
    data
  })
}

// scrm简报看板数据
export function getScrmChartResultValue(data) {
  return http({
    url: '/scrm/report/getInfo',
    data
  })
}

// 快捷操作
export function getShortcutResultValue(data) {
  return http({
    url: '/uipaas/home/<USER>/shortCut',
    data
  })
}

// 自定义图表编辑
export function chartListEdit(data) {
  return http({
    url: '/chart/listEdit',
    data
  })
}

// 获取自定义图表新建时可新建的图表列表
export function getAddChartType(data) {
  return http({
    url: '/chart/chartType/list',
    data
  })
}

// 获取pk方案列表
export function getPKList(data) {
  return http({
    url: '/chart/pk/department/list',
    data
  })
}

// 新建pk方案
export function addPKProject(data) {
  return http({
    url: '/chart/pk/department/add',
    data
  })
}

// 编辑pk方案
export function editPKProject(data) {
  return http({
    url: '/chart/pk/department/edit',
    data
  })
}

// 删除、排序pk方案
export function deleteSortPKProject(data) {
  return http({
    url: '/chart/pk/department/set/save',
    data
  })
}

// 获取绩效PK数据
export function getPKData(data) {
  return http({
    url: '/chart/result/rule/pk',
    data
  })
}

// 保存当前的pk榜TOP3开关状态
export function pkTopSwitch(data) {
  return http({
    url: '/chart/pk/custom/editTop3Switch',
    data
  })
}

// 获取多模板
export function getForm(data) {
  return http({
    url: '/chart/category/form',
    data
  })
}

// 设置PK方案选中的部门是否包含下属部门
export function setPKDepartmentSub(data) {
  return http({
    url: '/chart/pk/department/sub/save',
    data
  })
}

// 获取目标完成情况列表
export function getPerformanceFinish(data) {
  return http({
    url: '/chart/result/performance/finish',
    data
  })
}

// 获取目标完成情况详情
export function getPerformanceDetail(data) {
  return http({
    url: '/chart/result/performance',
    data
  })
}

// 获取自定义查询表汇总信息
export function getCustomQueryAgg(data) {
  return http({
    url: '/chart/result/agg',
    data
  })
}

// 获取自定义穿透表
export function getCustomPenetrate(data) {
  return http({
    url: '/chart/result/penetrate',
    data
  })
}

// 获取自定义穿透表(数据集数据源的图表使用)
export function getDataSetCustomPenetrate(data) {
  return http({
    url: '/dsValue/result/penetrate',
    data
  })
}

// 系统图表穿透获取数据
export function getThroughtResult(data) {
  return http({
    url: '/chart/result/through',
    data
  })
}

// 系统图表权限获取
export function getSystemAuth(data, url) {
  return http({
    url: url,
    data
  })
}

// 系统图表的权限编辑
export function editSystemAuth(data, url) {
  return http({
    url: url,
    data
  })
}

// 首页自定义指标穿透
export function getCustomThrough(data) {
  return http({
    url: '/indexThrough/customThrough',
    data
  })
}

// 图表中心自定义PK表格数据穿透
export function getCustomPKThrough(data) {
  return http({
    url: '/chart/pk/custom/through',
    data
  })
}

// 首页跟进记录指标穿透
export function getCommunicateThrough(data) {
  return http({
    url: '/indexThrough/customerCommunicate/through',
    data
  })
}

// 指标穿透
export function getIndexThrough(data) {
  return http({
    url: '/indexThrough/through',
    data
  })
}

// 导出－系统表、自定义表、自定义表穿透等
export function exportExcel(data) {
  return http({
    url: '/chart/export/data',
    data
  })
}

// 导出自助数据集表
export function exportDataSetExcel(data) {
  return http({
    url: '/dsValue/export/data',
    data
  })
}
// 导出－呼叫中心员工统计
export function exportExcelStatistics(data) {
  return http({
    url: '/chart/export/data/call/statistics',
    data
  })
}

// 导出－穿透后的表（系统表穿透、首页系统指标穿透）
export function penetrateExport(data) {
  return http({
    url: '/chart/export/through/data',
    data
  })
}

// 导出-穿透后的表（首页自定义指标穿透）
export function custompPenetrateExport(data) {
  return http({
    url: '/file/excel/chart/export/through/data',
    data
  })
}

// 导出－绩效pk
export function pkExport(data) {
  return http({
    url: '/chart/export/data/pk',
    data
  })
}

// 导出－目标完成情况
export function performanceExport(data) {
  return http({
    url: '/chart/export/data/performance',
    data
  })
}

// 删除图表 - 套餐降版本加入的
export function deleteChart(data) {
  return http({
    url: '/chart/deleteCustom',
    data
  })
}

// 仪表盘发布到首页
export function publishToIndex(data) {
  return http({
    url: '/chart/dashboard/publish',
    data
  })
}

// 仪表盘升级
export function upgradeDashboard(data) {
  return http({
    url: '/chart/dashboard/upgrade',
    data
  })
}

// BI 子表单查看全部
export function getSubFormList(data) {
  return http({
    url: '/chart/subFormList',
    data
  })
}

// BI 子表单（数据集为数据源）查看全部
export function getDataSetSubFormList(data) {
  return http({
    url: '/dsValue/subFormList',
    data
  })
}

// BI 系统图表 获取数值格式
export function getNumFormat(data) {
  return http({
    url: '/chart/get/numFormat',
    data
  })
}

// BI 系统图表 保存数值格式
export function saveNumFormat(data) {
  return http({
    url: '/chart/save/numFormat',
    data
  })
}

// 获取文本组件列表
export function getTextList(data) {
  return http({
    url: '/chart/dashboard/text/getList',
    data
  })
}

// 保存文本组件
export function saveTextList(data) {
  return http({
    url: '/chart/dashboard/text/saveBatch',
    data
  })
}

// 获取显示离职人员开关的状态
export function getPerformanceLeave(data) {
  return http({
    url: '/companyConfig/getPerformanceLeave',
    data
  })
}

// 【接口改造】选择已有图表-根据分类id获取当下图表
export function getChartByCategoryId(data) {
  return http({
    url: '/chart/byCategory/list',
    data
  })
}

// scrm迁移企微图表
export function wechatBoard(data) {
  return http({
    url: '/scrm/scrmStatistics/board',
    data
  })
}
// 【钻取】获取图表的钻取层级列表
export function getDrillList(data) {
  return http({
    url: '/chart/drill/list',
    data
  })
}

// 获取群筛选条件
export function getGroupChatList(data) {
  return http({
    url: '/scrm/scrmStatistics/getGroupChatList',
    data
  })
}
// 自定义图表，图表交互变更
export function chartOperateChange(data) {
  return http({
    url: '/chart/operate/enable',
    data
  })
}

// 自定义图表，联动设置保存
export function saveLinkageList(data) {
  return http({
    url: '/chart/linkage/save',
    data
  })
}

// 是否为私有化 true正常版 false私有化
export function getCkFlag(data) {
  return http({
    url: '/chart/getCkFlag',
    data
  })
}

// 获取bi 的运营配置
export function getBIConfig(data) {
  return http({
    url: '/chart/biConfig/list',
    data
  })
}
