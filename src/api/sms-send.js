/*
 * @Description: 首页-短信群发模块api
 */
import http from '@/axios'

// 发送短信初始化
export function smsSendInit(data) {
  return http({
    url: '/sms/send/init',
    data
  })
}

// 发送短信
export function smsSend(data) {
  return http({
    url: '/sms/send',
    data
  })
}

// 列表页发送短信
export function smsSendList(data) {
  return http({
    url: '/sms/sendForBusiness',
    data
  })
}

// 获取导入客户电话结果
export function importExcelResult(data) {
  return http({
    url: '/sms/importExcel/result',
    data
  })
}

// 获取短信日志统计
export function getStatistic(data) {
  return http({
    url: '/sms/log/getStatistic',
    data
  })
}

// 获取短信日志列表
export function getLogList(data) {
  return http({
    url: '/sms/log/list',
    data
  })
}

// 重新发送短信
export function smsResend(data) {
  return http({
    url: '/sms/reSend',
    data
  })
}

// 取消定时发送
export function smscancleSend(data) {
  return http({
    url: '/sms/cancelSend',
    data
  })
}

// 获取可用短信数
export function getRealCount(data) {
  return http({
    url: '/sms/getRealCount',
    data
  })
}

// 获取发送短信可用的短信模板
export function getSmsTemplateList(data) {
  return http({
    url: '/sms/templateList',
    data
  })
}

// 模块筛选
export function getSmsSearchList(data) {
  return http({
    url: '/sms/reply/getSmsSearchList',
    data
  })
}

// 对象对应的号码
export function smsMobileList(data) {
  return http({
    url: '/sms/reply/smsMobileList',
    data
  })
}

// 根据号码查询短信往来
export function smsDealingList(data) {
  return http({
    url: '/sms/reply/smsDealingList',
    data
  })
}

// 获取回复短信日志列表
export function getReplyLogList(data) {
  return http({
    url: '/sms/reply/logList',
    data
  })
}
