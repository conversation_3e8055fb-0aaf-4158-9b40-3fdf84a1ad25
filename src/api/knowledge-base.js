import request from '@/axios'

export function getKnowledgeBaseList(data) {
  return request({
    url: '/knowledge/base/list',
    data
  })
}

export function createKnowledgeBase(data) {
  return request({
    url: '/knowledge/base/add',
    data
  })
}

export function updateKnowledgeBase(data) {
  return request({
    url: '/knowledge/base/update',
    data
  })
}

export function deleteKnowledgeBase(data) {
  return request({
    url: '/knowledge/base/delete',
    data
  })
}

export function getKnowledgeList(data) {
  return request({
    url: '/list/knowledge',
    data
  })
}

export function getKnowledgeDetail(data) {
  return request({
    url: '/knowledge/updateGet',
    data
  })
}

export function createKnowledge(data) {
  return request({
    url: '/knowledge/add',
    data
  })
}

export function updateKnowledge(data) {
  return request({
    url: '/knowledge/update',
    data
  })
}

export function deleteKnowledge(data) {
  return request({
    url: '/knowledge/delete',
    data
  })
}

export function toggleEnableKnowledge(data) {
  return request({
    url: '/knowledge/setup',
    data
  })
}

export function getCatalogueList(data) {
  return request({
    url: '/knowledge/catalogue/list',
    data
  })
}

export function deleleCatalogue(data) {
  return request({
    url: '/knowledge/catalogue/delete',
    data
  })
}

export function createCatalogue(data) {
  return request({
    url: '/knowledge/catalogue/add',
    data
  })
}

export function updateCatalogue(data) {
  return request({
    url: '/knowledge/catalogue/update',
    data
  })
}

export function saveCatalogueSort(data) {
  return request({
    url: '/knowledge/catalogue/sort',
    data
  })
}

export function getKnowledgeTagList(data) {
  return request({
    url: '/knowledge/tagSet/tagList',
    data
  })
}

export function knowledgeAddTag(data) {
  return request({
    url: '/knowledge/batchAddTag',
    data
  })
}

export function knowledgeRemoveTag(data) {
  return request({
    url: '/knowledge/batchRemoveTag',
    data
  })
}
