import request from '@/axios'
// 【运营标签库】相关API

// 标签组---列表
export function getTagGroupList(data) {
  return request({
    url: '/scrm/tagSet/group/list',
    data
  })
}

// 标签组---新建/编辑 (ADD/EDIT)
export function setTagGroup(data) {
  return request({
    url: '/scrm/tagSet/group/save',
    data
  })
}

// 标签组---删除
export function deleteTagGroup(data) {
  return request({
    url: '/scrm/tagSet/group/delete',
    data
  })
}

// 标签---列表
export function getTagList(data) {
  return request({
    url: '/scrm/tagSet/list',
    data
  })
}

// 标签---新建/编辑 (ADD/EDIT)
export function setTag(data) {
  return request({
    url: '/scrm/tagSet/save',
    data
  })
}

// 标签---删除
export function deleteTag(data) {
  return request({
    url: '/scrm/tagSet/delete',
    data
  })
}

// 标签组---列表排序
export function tagSort(data) {
  return request({
    url: '/scrm/tagSet/sort',
    data
  })
}
