/*
 * @Description: 客户阶段相关api
 */
import http from '@/axios'
/**
 * 获取客户阶段列表
 * @param {*} data
 */
export function getCustomerStageList(data) {
  return http({
    url: '/customerStage/list',
    data: data
  })
}

/**
 * 保存客户阶段
 * @param {*} data
 */
export function saveCustomerStage(data) {
  return http({
    url: '/customerStage/save',
    data: data
  })
}
/**
 * 启用
 * @param {*} data
 */
export function enableCustomerStage(data) {
  return http({
    url: '/customerStage/enable',
    data: data
  })
}
/**
 * 排序
 * @param {*} data
 */
export function sortCustomerStage(data) {
  return http({
    url: '/customerStage/sort',
    data: data
  })
}
/**
 * 获取流失原因
 * @param {*} params
 */
export function getLossList(data) {
  return http({
    url: '/formDataDictionary/list',
    data: data
  })
}
/**
 * 保存流失原因
 */
export function saveFormDataDictionary(data) {
  return http({
    url: '/formDataDictionary/save',
    data: data
  })
}
/**
 * 唤起企微会话列表
 */
export function weCharFriend(data) {
  return http({
    url: '/list/weCharFriend',
    data: data
  })
}
