/*
 * @Description: e 签宝相关接口
 */
import request from '@/axios/'

// 充值记录列表
export function feeLogList(data) {
  return request({
    url: '/esign/fee/log/list',
    data
  })
}
// 获取可充值的e签宝套餐信息（费用充值-充值金额的选项）
export function feeGetInfo(data) {
  return request({
    url: '/esign/fee/getInfo',
    data
  })
}
// 查询套餐余量
export function getEsignContractMargin(data) {
  return request({
    url: '/esign/fee/getEsignContractMargin',
    data
  })
}

// 企业信息 公司名/未认证已认证的状态
export function getEsignCorpInfo(data) {
  return request({
    url: '/esign/getEsignCorpInfo',
    data
  })
}
// 获取跳转到企业实名的地址
export function getEsignCorpRealname(data) {
  return request({
    url: '/esign/getEsignCorpRealname',
    data
  })
}
// 获取其他设置跳转的 url
export function getEsignCorpConsole(data) {
  return request({
    url: '/esign/getEsignCorpConsole',
    data
  })
}
// 短信套餐充值
export function feeCharge(data) {
  return request({
    url: '/esign/fee/charge',
    data
  })
}
// 表单中心-表单设置电子合同的开关状态
export function getSwitch(data) {
  return request({
    url: '/esign/getSwitch',
    data
  })
}

// 表单中心-表单设置电子合同的 switch 设置
export function setSwitch(data) {
  return request({
    url: '/esign/setSwitch',
    data
  })
}

// 表单中心-查询表单是否可以开启电子合同
export function hasEsign(data) {
  return request({
    url: '/esign/hasEsign',
    data
  })
}

// 发起签署
export function getEsignProcessStart(data) {
  return request({
    url: '/esign/getEsignProcessStart',
    data
  })
}

// 下载合同
export function downLoadEsign(data) {
  return request({
    url: '/esign/getEsignFileDetail',
    data
  })
}

// 表单详情 查询签署状态
export function getEsignFlowStatus(data) {
  return request({
    url: '/esign/getEsignFlowStatus',
    data
  })
}

// 表单详情 点击查找跳转到 E 签宝
export function getEsignNoticeUrl(data) {
  return request({
    url: '/esign/signNoticeUrl',
    data
  })
}

// 首页获取 E 签宝状态
export function getEsignRemind(data) {
  return request({
    url: '/esign/remind',
    data
  })
}

export function getEsignFlowInfo(data) {
  return request({
    url: '/esign/getEsignFlowInfo',
    data
  })
}

// 点击发起签署 前置接口
export function getEsignPreCheck(data) {
  return request({
    url: '/esign/preCheck',
    data
  })
}

// 其他设置前置接口
export function getEsignCheckRealName(data) {
  return request({
    url: '/esign/checkRealname',
    data
  })
}

// 请求个人认证地址接口
export function getEsignPsnRealname(data) {
  return request({
    url: '/esign/getEsignPsnRealname',
    data
  })
}

// 解除绑定
export function esignUnauthenticate() {
  return request({
    url: '/esign/unbind'
  })
}
