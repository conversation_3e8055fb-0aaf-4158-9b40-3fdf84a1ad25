/*
 * @Description: 数据集相关接口
 */
import request from '@/axios'

// 获取数据集列表（含列表页的名称和创建人搜索）
export function getDataSetList(data) {
  return request({
    url: '/dataSet/list',
    data
  })
}

// 新建数据集保存
export function saveDataSet(data) {
  return request({
    url: '/dataSet/save',
    data
  })
}

// 数据集重命名
export function renameDataSet(data) {
  return request({
    url: '/dataSet/rename',
    data
  })
}

// 数据集删除
export function deleteDataSet(data) {
  return request({
    url: '/dataSet/delete',
    data
  })
}

// 数据集复制
export function copyDataSet(data) {
  return request({
    url: '/dataSet/copy',
    data
  })
}

// 数据集权限获取（编辑者权限、查看者权限)
export function getDataSetPermission(data) {
  return request({
    url: '/dataSet/permission/get',
    data
  })
}

// 数据集权限保存（编辑者权限、查看者权限)
export function saveDataSetPermission(data) {
  return request({
    url: '/dataSet/permission/save',
    data
  })
}

// 数据集更新规则获取
export function getDataSetConfig(data) {
  return request({
    url: '/dataSet/config/get',
    data
  })
}

// 数据集更新规则保存
export function saveDataSetConfig(data) {
  return request({
    url: '/dataSet/config/save',
    data
  })
}

// 数据集手动触发更新
export function executeDataSetConfig(data) {
  return request({
    url: '/dataSet/config/execute',
    data
  })
}

// 数据集画布获取
export function getDataSetBoard(data) {
  return request({
    url: '/dataSet/board/get',
    data
  })
}

// 数据集画布保存
export function saveDataSetBoard(data) {
  return request({
    url: '/dataSet/board/save',
    data
  })
}

// 数据集画布的规则获取
export function getDataSetBoardRule(data) {
  return request({
    url: '/dataSet/rule',
    data
  })
}

// 数据集某个节点的字段集合获取
export function getDataSetFormAttr(data) {
  return request({
    url: '/dataSet/form/dataSourceAttr',
    data
  })
}

// 数据集-合并节点-生成映射Attr
export function getDataSetUnionAttr(data) {
  return request({
    url: '/dataSet/attrReflect/union',
    data
  })
}

// 数据集-连接节点-生成映射Field
export function getDataSetJoinField(data) {
  return request({
    url: '/dataSet/attrReflect/join',
    data
  })
}

// 数据集-分组节点-生成映射Field
export function getDataSetGroupField(data) {
  return request({
    url: '/dataSet/attrReflect/group',
    data
  })
}

// 数据集-列转行-生成列字段、值字段attr
export function getDataSetUnpivotField(data) {
  return request({
    url: '/dataSet/attrReflect/unpivot',
    data
  })
}

// 数据集-列表页-获取下拉选项
export function getConditionList(data) {
  return request({
    url: '/form/data/organization/conditionList',
    data
  })
}

// 数据集-结果预览
export function getPreview(data) {
  return request({
    url: '/dsValue/preview',
    data
  })
}

// 数据集-验证权限权限
export function checkDataSetPermission(data) {
  return request({
    url: '/dataSet/checkPermission',
    data
  })
}
