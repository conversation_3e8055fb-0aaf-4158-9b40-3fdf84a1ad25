/*
 * @Description: 微信客服
 */
import request from '@/axios/'

// 获取质检模板列表
export function getQualityInspectSettingList(data) {
  return request({
    url: '/im/qualityInspect/setting/list',
    data
  })
}

// 更新质检模板状态
export function updateQualityInspectSettingStatus(data) {
  return request({
    url: '/im/qualityInspect/setting/updateStatus',
    data
  })
}

// 删除质检模板
export function deleteQualityInspectSetting(data) {
  return request({
    url: '/im/qualityInspect/setting/delete',
    data
  })
}

// 复制质检模板
export function copyQualityInspectSetting(data) {
  return request({
    url: '/im/qualityInspect/setting/copyAdd',
    data
  })
}

// 新增质检模板
export function addQualityInspectSetting(data) {
  return request({
    url: '/im/qualityInspect/setting/add',
    data
  })
}

// 新增质检模板
export function editQualityInspectSetting(data) {
  return request({
    url: '/im/qualityInspect/setting/edit',
    data
  })
}

// 坐席列表查询
export function getAgentManagement(data) {
  return request({
    url: '/im/config/agentManagement/page',
    data
  })
}

// 坐席批量编辑座席数与技能组
export function editAgentManagement(data) {
  return request({
    url: '/im/config/agentManagement/edit',
    data
  })
}

// 坐席批量编辑座席数与技能组
export function getSkillGroupPage(data) {
  return request({
    url: '/im/config/skillGroup/page',
    data
  })
}

// 技能组新增
export function addSkillGroup(data) {
  return request({
    url: '/im/config/skillGroup/add',
    data
  })
}

// 敏感词列表查询
export function getSensitiveWordPage(data) {
  return request({
    url: '/im/config/sensitiveWord/page',
    data
  })
}

// 敏感词新增
export function addSensitiveWord(data) {
  return request({
    url: '/im/config/sensitiveWord/add',
    data
  })
}

// 敏感词删除
export function delSensitiveWord(data) {
  return request({
    url: '/im/config/sensitiveWord/del',
    data
  })
}

// 敏感词编辑
export function editSensitiveWord(data) {
  return request({
    url: '/im/config/sensitiveWord/update',
    data
  })
}

// im列表-获取列表数据
export function getImList(data) {
  return request({
    url: '/im/common/list',
    data
  })
}

// im列表-获取卡片视图列表数据
export function getImCardViewList(data) {
  return request({
    url: '/im/qualityInspect/view/list',
    data
  })
}

// 会话列表-详情页-右侧操作栏
export function getSessionSidebarInfo(data) {
  return request({
    url: '/im/session/record/customer/sidebar/get',
    data
  })
}

// 会话列表-详情页-关联客户
export function goLinkCustomer(data) {
  return request({
    url: '/im/session/record/relate/customer',
    data
  })
}

// im表单获取详情页信息
export function imFormDetailFrame(data) {
  return request({
    url: '/im/common/detail/get',
    data
  })
}

// 质检工作台-质检操作栏-获取数据
export function getqualityInspectData(data) {
  return request({
    url: '/im/qualityInspect/get/data',
    data
  })
}

// 质检工作台-质检操作栏-获取质检模板列表
export function getqualityInspectTemplate(data) {
  return request({
    url: '/im/qualityInspect/get/template',
    data
  })
}

// 质检工作台-批量操作-获取质检模板列表
export function getQITemplateList(data) {
  return request({
    url: '/im/session/record/transfer/get',
    data
  })
}

// 质检工作台-批量操作-分配质检负责人
export function transferSupervisor(data) {
  return request({
    url: '/im/session/record/transfer',
    data
  })
}

// 质检工作台-质检操作栏- 保存质检数据
export function saveQualityInspect(data) {
  return request({
    url: '/im/qualityInspect/add',
    data
  })
}

// 技能组删除
export function delSkillGroup(data) {
  return request({
    url: '/im/config/skillGroup/del',
    data
  })
}

// 技能组新增
export function updateSkillGroup(data) {
  return request({
    url: '/im/config/skillGroup/update',
    data
  })
}

// 获取websocket url
export function getWebsocketUrl(data) {
  return request({
    url: '/im/common/getWebsocket/url',
    data
  })
}

// 查询会话记录
export function getSessionRecord(data) {
  return request({
    url: '/im/msg/record/list',
    data
  })
}

// 查询会话列表
export function getSessionList(data) {
  return request({
    url: '/im/session/records',
    data
  })
}

// 查询客户列表
export function getCustomerList(data) {
  return request({
    url: '/im/session/list',
    data
  })
}

// im上线
export function onlineIm(data) {
  return request({
    url: '/im/agent/online',
    data
  })
}

// 客服管理-获取授权链接
export function getKfAuthUrl(data) {
  return request({
    url: '/im/config/kf/authUrl',
    data
  })
}

// 客服管理-获得授权信息
export function getKfAuthInfo(data) {
  return request({
    url: '/im/config/kf/authInfo',
    data
  })
}

// 客服管理-客服列表查询
export function getKfAuthPage(data) {
  return request({
    url: '/im/config/kf/page',
    data
  })
}

// 客服管理-客服新增接口
export function addKf(data) {
  return request({
    url: '/im/config/kf/add',
    data
  })
}

// 客服管理-客服批量删除
export function delKf(data) {
  return request({
    url: '/im/config/kf/del',
    data
  })
}

// 客服管理-判断客服是否被删除
export function isKfDel(data) {
  return request({
    url: '/im/config/kf/isDel',
    data
  })
}

// 客服管理-客服编辑
export function updateKf(data) {
  return request({
    url: '/im/config/kf/update',
    data
  })
}

// 客服管理-取消授权接口
export function proCancelAuth(data) {
  return request({
    url: '/im/config/proCancelAuth',
    data
  })
}

// 客服管理-判断客服是否授权成功
export function getKfAuthStatus(data) {
  return request({
    url: '/im/config/kfAuth/status',
    data
  })
}

// 获取移动端token（多平台需要）
export function getDingtalkToken(data) {
  return request({
    url: '/im/session/record/get/token',
    method: 'post',
    data
  })
}

// 获取ai回答
export function getAiChatPrompt(data) {
  return request({
    url: '/im/msg/record/prompt',
    data
  })
}

// 查询会话工厂列表
export function getFactoryList(data) {
  return request({
    url: '/im/factory/list',
    data
  })
}

// 保存/更新会话工厂
export function saveFactory(data) {
  return request({
    url: '/im/factory/save',
    data
  })
}

// 删除会话工厂
export function deleteFactory(data) {
  return request({
    url: '/im/factory/delete',
    data
  })
}

// 获取画布
export function getBoard(data) {
  return request({
    url: '/im/factory/board/get',
    data
  })
}

// 保存画布
export function saveBoard(data) {
  return request({
    url: '/im/factory/board/save',
    data
  })
}

// 复制会话工厂
export function copyFactory(data) {
  return request({
    url: '/im/factory/copy',
    data
  })
}

// 获取字段列表(新建数据/信息输出时候用)
export function getFactoryFields(data) {
  return request({
    url: '/im/factory/fields/get',
    data
  })
}

// 添加字段列表(新建数据添加字段和查询字段数据范围)
export function getOperatorFields(data) {
  return request({
    url: '/im/factory/operator/fields',
    data
  })
}

// 查询意图列表
export function getIntentList(data) {
  return request({
    url: '/im/intent/list',
    data
  })
}

// 保存/更新意图
export function saveIntent(data) {
  return request({
    url: '/im/intent/save',
    data
  })
}

// 获取意图详情
export function getIntent(data) {
  return request({
    url: '/im/intent/get',
    data
  })
}

// 删除意图
export function deleteIntent(data) {
  return request({
    url: '/im/intent/delete',
    data
  })
}

// 机器人列表页
export function getRobotList(data) {
  return request({
    url: '/im/robot/setting/list',
    data
  })
}

// 新建机器人
export function addRobot(data) {
  return request({
    url: '/im/robot/setting/add',
    data
  })
}

// 设置编辑机器人
export function updateRobot(data) {
  return request({
    url: '/im/robot/setting/update',
    data
  })
}

// 删除机器人
export function deleteRobot(data) {
  return request({
    url: '/im/robot/setting/delete',
    data
  })
}

// 机器人关联客服Get
export function linkCustomerServiceGet(data) {
  return request({
    url: '/im/robot/setting/linkCustomerServiceGet',
    data
  })
}

// 机器人关联客服
export function linkCustomerService(data) {
  return request({
    url: '/im/robot/setting/linkCustomerService',
    data
  })
}

// 设置编辑机器人Get
export function updateGet(data) {
  return request({
    url: '/im/robot/setting/updateGet',
    data
  })
}

// 机器人启用/关闭
export function updateRobotStatus(data) {
  return request({
    url: '/im/robot/setting/updateStatus',
    data
  })
}

// 机器人关联知识库列表
export function knowledgeBaseList(data) {
  return request({
    url: '/im/robot/setting/knowledgeBaseList',
    data
  })
}

// 关联客服列表
export function customerServiceList(data) {
  return request({
    url: '/im/robot/setting/customerServiceList',
    data
  })
}

// 关联客服列表
export function getSensitiveWordList(data) {
  return request({
    url: '/im/robot/sensitiveWord/list',
    data
  })
}

// 新建转人工敏感词
export function addSensitiveWordRobot(data) {
  return request({
    url: '/im/robot/sensitiveWord/add',
    data
  })
}

// 编辑转人工敏感词
export function updateSensitiveWordRobot(data) {
  return request({
    url: '/im/robot/sensitiveWord/update',
    data
  })
}

// 关联客服列表
export function deleteSensitiveWord(data) {
  return request({
    url: '/im/robot/sensitiveWord/delete',
    data
  })
}

// 判断公司是否在智能客服白名单
export function isWhitelist(data) {
  return request({
    url: '/im/robot/isWhitelist',
    data
  })
}

// 关联机器人列表
export function linkList(data) {
  return request({
    url: '/im/robot/linkList',
    data
  })
}

// im知识库上传
export function uploadKnowledge(data) {
  return request({
    url: '/im/knowledge/upload',
    data
  })
}

// 知识库同步
export function syncKnowledge(data) {
  return request({
    url: '/im/knowledge/sync',
    data
  })
}

// 获取状态
export function getStatus(data) {
  return request({
    url: '/im/knowledge/get/status',
    data
  })
}
