import request from '@/axios'

// 地图列表
export function getWorkOrderV2MapList(data) {
  return request({
    url: '/workOrderV2/view/mapList',
    data
  })
}

// 地图附近员工列表
export function getWorkOrderV2NearbyStaffList(data) {
  return request({
    url: '/workOrderV2/view/nearbyUser',
    data
  })
}

// 甘特图列表
export function getWorkOrderV2GanttList(data) {
  return request({
    url: '/workOrderV2/workOrderList/gant',
    data
  })
}
// 指派第图点击用户
export function getMapUserInfo(data) {
  return request({
    url: '/workOrderV2/view/click/user',
    data
  })
}

// 指派第图点击工单/聚合用户
export function getMapUserList(data) {
  return request({
    url: '/workOrderV2/view/click/workOrder',
    data
  })
}

// 规则设置
export function getWorkOrderV2FilterField(data) {
  return request({
    url: '/workOrderV2/process/getFilterField',
    data
  })
}

// 查看评价
export function getreturnVisitComment(data) {
  return request({
    url: '/workOrderV2/service/evaluation/getDetail',
    data
  })
}
