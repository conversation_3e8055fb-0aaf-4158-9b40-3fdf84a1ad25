/*
 * @Description: 多地址API
 */

import request from '@/axios'

// 多地址列表接口
export function getAddress(data) {
  return request({
    url: '/detail/tab/multipleAddress/list',
    data
  })
}

// 多地址解释接口
export function tabAddAddress(data) {
  return request({
    url: '/detail/tab/multipleAddress/explain/get',
    data
  })
}

// 设置主地址接口
export function setMain(data) {
  return request({
    url: '/detail/tab/multipleAddress/setMain',
    data
  })
}

// 多地址新建编辑接口
export function updateTabAddress(data) {
  return request({
    url: '/detail/tab/multipleAddress/save',
    data
  })
}

// 多地址删除
export function deteleMultipleAddress(data) {
  return request({
    url: '/detail/tab/multipleAddress/delete',
    data
  })
}
