/*
 * @Description: 导入
 */
import request from '@/axios'

const headers = {
  'Content-Type': 'multipart/form-data'
}
// 获取导入状态
export function getFileExcelImportStatus(data) {
  return request({
    url: '/file/excel/getImportStatus',
    data
  })
}

// 清除导入的缓存
export function cleanImportCache(data) {
  return request({
    url: '/file/excel/delImportStatus',
    data
  })
}

// 通用列表下载模板
export function downloadTemplate(data) {
  return request({
    url: '/file/excel/template/download',
    data,
    responseType: 'blob'
  })
}

// 业绩目标下载模板
export function downloadPerformanceTemplate(data) {
  return request({
    url: '/file/excel/template/download/performance',
    data,
    responseType: 'blob'
  })
}

// 重复错误下载
export function downloadRepeatError(data) {
  return request({
    url: '/file/excel/update/download',
    data,
    responseType: 'blob'
  })
}

// 导入文件错误下载
export function downloadExcelError(data) {
  return request({
    url: '/file/excel/error/download',
    data,
    responseType: 'blob'
  })
}

// 签订人不存在的错误下载
export function downloadExcelNoSignError(data) {
  return request({
    url: '/file/excel/noSign/download',
    data,
    responseType: 'blob'
  })
}

// 业绩目标导入文件错误下载
export function performanceDownloadExcelError(data) {
  return request({
    url: '/file/excel/error/download/performance',
    data,
    responseType: 'blob'
  })
}

// 通用列表的开始导入
export function startFormImport(data) {
  return request({
    url: '/file/excel/import/form/data',
    data
  })
}

// 业绩目标的开始导入
export function startPerformanceImport(data) {
  return request({
    url: '/file/excel/performance/importExcel',
    data,
    headers
  })
}

// 覆盖导入
export function restartRepeatImport(data) {
  return request({
    url: '/file/excel/import/coverFormData',
    data
  })
}

// 签订人不存在重新导入
export function restartNoSignImport(data) {
  return request({
    url: '/file/excel/import/noSignFormData',
    data
  })
}

// 导入客户电话模板
export function importSmsExcel(data) {
  return request({
    url: '/file/excel/sms/importExcel',
    data,
    headers
  })
}

// 导入客户电话模板
export function downloadSmsTemplate(data = {}) {
  return request({
    url: '/file/excel/template/download/sms',
    data,
    responseType: 'blob'
  })
}

// 独立版导入员工模板
export function importStaffTemplate(data) {
  return request({
    url: '/file/excel/template/download/staff',
    data,
    responseType: 'blob'
  })
}

// 独立版员工的开始导入
export function startStaffImport(data) {
  return request({
    url: '/file/excel/import/staff',
    data,
    headers
  })
}

// 独立版员工导入文件错误下载
export function downloadStaffExcelError(data) {
  return request({
    url: '/file/excel/error/download/staff',
    data,
    responseType: 'blob'
  })
}
// 获取导入字段
export function getAttrs(data = {}) {
  return request({
    url: '/file/excel/import/getAttrs',
    data
  })
}

// 获取导入字段
export function getAttrs4CoverById(data = {}) {
  return request({
    url: '/file/excel/import/getAttrs4CoverById',
    data
  })
}

// 导入前置获取是否继续执行
export function isImageOrFile(data = {}) {
  return request({
    url: '/file/excel/subForm/isImageOrFile',
    data
  })
}
