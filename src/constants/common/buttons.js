/*
 * @Description: 按钮的枚举
 */
// 表格中的操作列按钮
const tableButtons = {
  option_look: {
    // 查看
    type: '',
    icon: ''
  },
  option_red: {
    type: 'danger',
    icon: ''
  },
  option_del: {
    // 删除
    type: 'danger',
    icon: ''
  },
  option_complete_del: {
    // 彻底删除
    type: 'danger',
    icon: ''
  },
  option_add_payment: {
    // 添加回款单
    type: '',
    icon: '',
    businessType: 702
  }
}

// 列表页头部按钮
const headButtons = {
  add: {
    // 新建
    type: 'primary',
    icon: ''
  }
}

// 列表底部批量按钮
const footerButtons = {
  archiveBatch: {
    // 归档
    type: 'danger',
    icon: 'download'
  },
  cancelArchive: {
    // 取消归档
    type: 'danger',
    icon: 'upload2'
  },
  removeCompletely: {
    // 彻底删除
    type: 'danger',
    icon: ''
  }
}
// 按钮对应信息
export default {
  ...tableButtons,
  ...headButtons,
  ...footerButtons,
  // 下面是公共的按钮
  del: {
    // 删除
    type: 'danger',
    icon: 'delete'
  },
  edit: {
    // 编辑
    type: 'primary',
    icon: 'edit'
  }
}
