/*
 * @Description: 新工单中心
 */
import i18n from '@/lang'

export default {
  path: '/work-order-v2',
  name: 'workOrderV2',
  redirect: '/work-order-v2/index',
  meta: {
    pathName: i18n.t('router.workOrderCenter')
  },
  component: () => import(/* webpackChunkName: "app-layout" */ '@/components/layout/app-layout/'),
  children: [
    {
      path: 'setting',
      name: 'workOrderV2ForSetting',
      meta: {
        pathName: i18n.t('router.appManagement.workOrderManagement')
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/application-manage/work-order-v2/work-order-set.vue'
        )
    },
    {
      path: 'all',
      name: 'allWorkOrderList',
      meta: {
        pathName: '全部工单'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/all-work-order')
    },
    {
      path: 'assign',
      name: 'assignWorkOrderList',
      meta: {
        pathName: '工单指派'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/assign-work-order')
    },
    {
      path: 'return-visit-order',
      name: 'returnVisitOrderList',
      meta: {
        pathName: '回访工单'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/return-visit-order')
    },
    {
      path: 'service-evaluation',
      name: 'serviceEvaluationList',
      meta: {
        pathName: '服务评价'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/service-evaluation')
    },
    {
      path: 'customer-asset',
      name: 'customerAsset',
      meta: {
        pathName: '客户资产'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/customer-asset')
    },
    {
      path: 'pool',
      name: 'workOrderPool',
      meta: {
        pathName: '工单池'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/work-order-pool')
    },
    {
      path: 'slaLog',
      name: 'slaLog',
      meta: {
        pathName: i18n.t('router.slaLog')
      },
      component: () => import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/sla-log')
    },
    {
      path: 'receiptOrder',
      name: 'receiptOrder',
      meta: {
        pathName: i18n.t('router.receiptOrder')
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/receipt-order')
    },
    {
      path: 'serviceProject',
      name: 'serviceProject',
      meta: {
        pathName: i18n.t('router.serviceProject')
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/service-project/index.vue'
        )
    },
    {
      path: 'all-project',
      name: 'allProject',
      meta: {
        pathName: '全部项目'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/all-project/index.vue')
    },
    {
      path: 'resource-management',
      name: 'resourceManagement',
      meta: {
        pathName: '资源管理'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/resource-management/index.vue'
        )
    },
    {
      path: 'task',
      name: 'todoTask',
      meta: {
        pathName: '待办任务'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/task/index.vue')
    },
    {
      path: 'project-task',
      name: 'projectTask',
      meta: {
        pathName: '项目任务'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/project-risk/index.vue')
    },
    {
      path: 'project-risk',
      name: 'projectRisk',
      meta: {
        pathName: '项目风险'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/project-risk/index.vue')
    },
    {
      path: 'time-record',
      name: 'timeRecord',
      meta: {
        pathName: '工时记录'
      },
      component: () =>
        import(/* webpackChunkName: "work-order" */ '@/views/work-order-v2/time-record/index.vue')
    },
    {
      path: 'service-report',
      name: 'serviceReport',
      meta: {
        pathName: '服务报告'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/service-report/index.vue'
        )
    },
    {
      path: 'personal-warehouse',
      name: 'personalWarehouse',
      meta: {
        pathName: '员工个人仓库'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/personal-warehouse/index.vue'
        )
    },
    {
      path: 'material-requisition',
      name: 'materialRequisitione',
      meta: {
        pathName: '工单领料单'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/material-requisition/index.vue'
        )
    },
    {
      path: 'materials-returned',
      name: 'materialsReturned',
      meta: {
        pathName: '工单退料单'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/materials-returned/index.vue'
        )
    },
    {
      path: 'personal-spare-part',
      name: 'personalSparePart',
      meta: {
        pathName: '个人备件库查询'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/personal-spare-part/index.vue'
        )
    },
    {
      path: 'spare-part-consumption',
      name: 'sparePartConsumption',
      meta: {
        pathName: '备件消耗明细'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/spare-part-consumption/index.vue'
        )
    },
    {
      path: 'spare-part-out-in',
      name: 'sparePartOutIn',
      meta: {
        pathName: '备件出入库明细'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/spare-part-out-in/index.vue'
        )
    },
    {
      path: 'portal-access-record',
      name: 'portalAccessRecord',
      meta: {
        pathName: '用户访问记录'
      },
      component: () =>
        import(
          /* webpackChunkName: "work-order" */ '@/views/work-order-v2/portal-access-record/index.vue'
        )
    }
  ]
}
