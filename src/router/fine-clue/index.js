/*
 * @Author: wei.fu
 * @Date: 2021-11-15 14:22:12
 * @LastEditors: wei.fu
 * @LastEditTime: 2021-11-24 13:40:01
 * @Description:精线索路由
 */
export default {
  path: '/fineClue',
  name: 'fineClue',
  redirect: '/fineClue/index',
  component: () => import('@/components/layout/app-layout/'),
  children: [
    {
      path: 'index',
      name: 'fineClueIndex',
      component: () => import(/* webpackChunkName: "FINECLUE" */ '@/views/fineClue/index')
    },
    {
      // 精线索路由
      path: 'detail',
      name: 'fineClueDetail',
      component: () => import(/* webpackChunkName: "SOUKE" */ '@/views/fineClue/detail')
    }
  ]
}
