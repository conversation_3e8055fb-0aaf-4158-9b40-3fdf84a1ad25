/*
 * @Author: luoteen
 * @Date: 2021-07-14 09:58:01
 * @LastEditors: zihao.chen
 * @LastEditTime: 2021-09-09 00:00:10
 * @Description: 企微配置菜单
 */
import i18n from '@/lang'

export default [
  {
    path: 'wx-synchro',
    name: 'wxSynchro',
    meta: {
      pathName: i18n.t('router.wechatConfig.syncSettings')
    },
    component: () =>
      import(
        // /* webpackChunkName: "wxSynchro" */ '@/views/application-manage/wechat-config/synchro.vue' // TODO: 旧版页面
        /* webpackChunkName: "wxSynchro" */ '@/views/scrm/manage-center/wechat-config/sync-setting/index.vue' // TODO: 联系人改版（开发完后舍弃上述页面）
      )
  },
  {
    path: 'bind-app',
    name: 'bindApp',
    meta: {
      pathName: i18n.t('router.wechatConfig.bindApp')
    },
    component: () =>
      import(
        /* webpackChunkName: "bindApp" */ '@/views/application-manage/wechat-config/bindApp.vue'
      )
  },
  {
    path: 'sidebar-authorize',
    name: 'sidebarSetting',
    meta: {
      pathName: i18n.t('router.wechatConfig.sidebarAuthorize')
    },
    component: () =>
      import(
        /* webpackChunkName: "sidebarSetting" */ '@/views/application-manage/wechat-config/sidebar-setting/index.vue'
      )
  }
]
