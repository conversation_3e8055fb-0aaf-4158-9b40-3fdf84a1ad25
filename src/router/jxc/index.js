/*
 * @Description: 项目的主要路由
 */
/* webpackChunkName: "JXC" */
import i18n from '@/lang'

export default {
  path: '/jxc',
  name: 'jxc',
  redirect: '/jxc/khgl',
  meta: {
    pathName: 'jxc'
  },
  component: () => import(/* webpackChunkName: "app-layout" */ '@/components/layout/app-layout/'),
  children: [
    {
      path: 'supplier',
      name: 'supplierList',
      meta: {
        pathName: i18n.t('store.supplier')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/supplier')
    },
    {
      path: 'purchaseContract',
      name: 'purchaseContractList',
      meta: {
        pathName: i18n.t('business.procurementContract')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/purchase-contract')
    },
    {
      path: 'waitPurchaseContract',
      name: 'waitPurchaseContractList',
      meta: {
        pathName: i18n.t('router.jxc.toBePurchased')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/wait-purchase-contract')
    },
    {
      path: 'smartReplenishment',
      name: 'smartReplenishmentList',
      meta: {
        pathName: i18n.t('router.jxc.smartReplenishment')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/smart-replenishment')
    },
    {
      path: 'purchaseReturn',
      name: 'purchaseReturnList',
      meta: {
        pathName: i18n.t('router.jxc.purchaseReturns')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/purchase-return')
    },
    {
      path: 'BOM',
      name: 'BOMList',
      meta: {
        pathName: 'bom'
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/bom')
    },
    {
      path: 'productionOrder',
      name: 'productionOrderList',
      meta: {
        pathName: i18n.t('router.jxc.productionList')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/production-order')
    },
    {
      path: 'simulationProduce',
      name: 'simulationProduce',
      meta: {
        pathName: i18n.t('router.jxc.simulatedProduction')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/simulation-produce')
    },
    {
      path: 'waitProductionOrder',
      name: 'waitProductionOrderList',
      meta: {
        pathName: i18n.t('router.jxc.prepareProduction')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/wait-production-order')
    },
    {
      path: 'assemble',
      name: 'assembleList',
      meta: {
        pathName: i18n.t('router.jxc.assemInOutOrder')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/assemble')
    },
    {
      path: 'cost-adjust',
      name: 'costAdjustList',
      meta: {
        pathName: i18n.t('router.jxc.costAdjust')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/cost-adjust')
    },
    {
      path: 'InWarehouse',
      name: 'InWarehouseList',
      meta: {
        pathName: i18n.t('business.warehouseReceipt')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/in-warehouse')
    },
    {
      path: 'waitInWarehouse',
      name: 'waitInWarehouseList',
      meta: {
        pathName: i18n.t('router.jxc.prepareStorage')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/wait-in-warehouse')
    },
    {
      path: 'OutWarehouse',
      name: 'OutWarehouseList',
      meta: {
        pathName: i18n.t('business.outstock')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/out-warehouse')
    },
    {
      path: 'waitOutWarehouse',
      name: 'waitOutWarehouseList',
      meta: {
        pathName: i18n.t('router.jxc.toBeOutOfStock')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/wait-out-warehouse')
    },
    {
      path: 'inventory',
      name: 'inventoryList',
      meta: {
        pathName: i18n.t('constants.common.checkList')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/inventory')
    },
    {
      path: 'allot',
      name: 'allotList',
      meta: {
        pathName: i18n.t('constants.common.allocationForm')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/allot')
    },
    {
      path: 'warehouse',
      name: 'warehouseList',
      meta: {
        pathName: i18n.t('router.jxc.warehouse')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/warehouse')
    },
    {
      path: 'productStock',
      name: 'productStockList',
      meta: {
        pathName: i18n.t('router.jxc.stockInquiry')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/product-stock')
    },
    {
      path: 'stockFlowBill',
      name: 'stockFlowBillList',
      meta: {
        pathName: i18n.t('router.jxc.stockBill')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/stock-flow-bill')
    },
    {
      path: 'stockStandingBook',
      name: 'stockStandingBookList',
      meta: {
        pathName: i18n.t('router.jxc.stockBook')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/stock-standing-book')
    },
    {
      path: 'seqSearch',
      name: 'seqSearchList',
      meta: {
        pathName: i18n.t('router.jxc.seqSearch')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/seq-search')
    },
    {
      path: 'batchFlowBill',
      name: 'batchFlowList',
      meta: {
        pathName: i18n.t('router.jxc.batchFlow')
      },
      component: () => import(/* webpackChunkName: "JXC" */ '@/views/jxc/batch-flow')
    }
  ]
}
