// 套餐枚举
// FeeTypeEnum需要严格按照套餐等级顺序进行定义
// FREE < STANDARD < ADVANCED < ENTERPRISE < ULTIMATE < ULTIMATE_V2
// https://alidocs.dingtalk.com/i/nodes/NZQYprEoWoe25PymfoD7vmBdJ1waOeDk?utm_scene=team_space

export const FeeTypeEnum = {
  // 免费版
  FREE: -1,
  // 标准版
  STANDARD: 0,
  // 高级版
  ADVANCED: 1,
  // 企业版
  ENTERPRISE: 64,
  // pro旗舰版（老旗舰版）
  ULTIMATE: 3,
  // 新旗舰版
  ULTIMATE_V2: 65
}

export const FeeTypeNameEnum = new Map([
  [-1, 'FREE'],
  [0, 'STANDARD'],
  [1, 'ADVANCED'],
  [64, 'ENTERPRISE'],
  [3, 'ULTIMATE'],
  [65, 'ULTIMATE_V2']
])
