/*
 * @Description: 文件类型相关的公共方法
 */

/**
 * @description 获取文件类型，用于显示文件图标
 * @param {String} ext 文件后缀名
 * @returns fileType
 */
export function getFileType(ext) {
  const fileType = {
    image: 'jpg/png/jpeg/gif/bmp/raw',
    txt: 'txt/log',
    zip: 'zip/rar/tar/gz',
    sounds: 'mp3/wav/wma/cda/midi/ogg/ape/flac/aac',
    excel: 'xls/xlsx/csv/et/ett/xlt',
    pdf: 'pdf',
    word: 'doc/docx/docm/dotx/wps/wpt/rtf',
    ppt: 'ppt/pptx/dps/dpt/pot/pps',
    video: 'mp4/avi/wmv/mov/mkv',
    html: 'html/xml/dhtml'
  }

  if (!ext) return 'other'
  for (const key in fileType) {
    if (fileType[key].indexOf(ext) > -1) {
      return key
    }
  }
  return 'other'
}

/**
 * @description 获取用于预览的文件类型
 * @param {String} ext 文件后缀名
 * @returns fileViewType
 */
export function getFileViewType(ext) {
  ext = ext.toLowerCase()
  const viewFile = {
    image: 'jpg/png/jpeg/gif/bmp',
    office: 'xlsx/xlsb/xlsm/docx/dotx/pptx/ppsx/potx/ppsm',
    audio: 'mp3/aac/wav/ogg',
    video: 'mp4',
    pdf: 'pdf',
    txt: 'txt'
  }

  if (!ext) return ''
  for (const key in viewFile) {
    if (viewFile[key].indexOf(ext) > -1) {
      return key
    }
  }
  return ''
}
