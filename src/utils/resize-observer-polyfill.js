// write by AI
export class SimpleResizeObserver {
  constructor(callback) {
    this.callback = callback
    this.observedElements = new Map()
    this.checkResizes = this.throttle(this.checkResizes.bind(this), 200)
    this.init()
  }

  init() {
    window.addEventListener('resize', this.checkResizes)
  }

  observe(element) {
    const rect = element.getBoundingClientRect()
    this.observedElements.set(element, { width: rect.width, height: rect.height })
  }

  unobserve(element) {
    this.observedElements.delete(element)
  }

  disconnect() {
    this.observedElements.clear()
    window.removeEventListener('resize', this.checkResizes)
  }

  checkResizes() {
    for (const [element, lastSize] of this.observedElements.entries()) {
      try {
        const rect = element.getBoundingClientRect()
        if (rect.width !== lastSize.width || rect.height !== lastSize.height) {
          this.callback([{ target: element, contentRect: rect }])
          this.observedElements.set(element, { width: rect.width, height: rect.height })
        }
      } catch (error) {
        console.error('Error checking resize:', error)
        this.observedElements.delete(element)
      }
    }
  }

  throttle(fn, wait) {
    let lastTime = 0
    return function (...args) {
      const now = new Date().getTime()
      if (now - lastTime >= wait) {
        lastTime = now
        fn.apply(this, args)
      }
    }
  }
}
