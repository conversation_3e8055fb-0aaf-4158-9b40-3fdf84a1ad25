/*
 * @Description:
 */
/*
 *  DESC: 表单设计模块公共方法
 * */
import i18n from '@/lang'

import store from '@/store/index'

import { Message } from 'element-ui'
import xbb from '@xbb/xbb-utils'
import { FieldTypeEnum } from '@/constants/enum/fieldType.js'
import {
  InputText,
  InputTextarea,
  InputNumber,
  InputDate,
  InputRadio,
  InputCheckbox,
  SelectRadio,
  SelectCheckbox,
  SplitLine,
  TypeLong,
  InputAddress,
  InputLocation,
  InputImage,
  InputFile,
  DescriptionText,
  SubForm,
  RelyData,
  UserRadio,
  UserCheckbox,
  DeptRadio,
  DeptCheckbox,
  CreateUser,
  CreateTime,
  UpdateTime,
  RelyDept,
  Owner,
  CcUser,
  SerialNumber,
  StageProcess,
  StageProportion,
  TotalStayTime,
  StageStayTime,
  TagTabs,
  ManualSign,
  RichText,
  MultiRelyData,
  ContractTemplate,
  LowCodeComponent,
  ReferenceField
} from '@/fields/entity/index.js'
import { fieldAttrMap } from '@/views/edit/new-form-design/fields-design-map'

export function addWeightAttr(item) {
  const {
    fieldType,
    attrType,
    attrName,
    icon,
    isSub,
    parentAttr,
    columnWidth,
    minColumnWidth,
    attr
  } = item
  const options = {
    attr,
    attrType,
    fieldType,
    icon,
    attrName,
    isSub,
    parentAttr,
    columnWidth,
    minColumnWidth
  }
  const fieldStrategy = {
    [FieldTypeEnum.inputText]: InputText,
    [FieldTypeEnum.inputTextArea]: InputTextarea,
    [FieldTypeEnum.long]: TypeLong,
    [FieldTypeEnum.inputNumber]: InputNumber,
    [FieldTypeEnum.inputDate]: InputDate,
    [FieldTypeEnum.inputRadio]: InputRadio,
    [FieldTypeEnum.inputCheckbox]: InputCheckbox,
    [FieldTypeEnum.selectRadio]: SelectRadio,
    [FieldTypeEnum.selectCheckbox]: SelectCheckbox,
    [FieldTypeEnum.splitLine]: SplitLine,
    [FieldTypeEnum.inputAddress]: InputAddress,
    [FieldTypeEnum.inputLocation]: InputLocation,
    [FieldTypeEnum.inputImage]: InputImage,
    [FieldTypeEnum.inputFile]: InputFile,
    [FieldTypeEnum.descriptionText]: DescriptionText,
    [FieldTypeEnum.subForm]: SubForm,
    [FieldTypeEnum.relyData]: RelyData,
    [FieldTypeEnum.userRadio]: UserRadio,
    [FieldTypeEnum.userCheckbox]: UserCheckbox,
    [FieldTypeEnum.deptRadio]: DeptRadio,
    [FieldTypeEnum.deptCheckbox]: DeptCheckbox,
    [FieldTypeEnum.createUser]: CreateUser,
    [FieldTypeEnum.updateTime]: UpdateTime,
    [FieldTypeEnum.relyDept]: RelyDept,
    [FieldTypeEnum.owner]: Owner,
    [FieldTypeEnum.ccUser]: CcUser,
    [FieldTypeEnum.serialNumber]: SerialNumber,
    [FieldTypeEnum.tagTabs]: TagTabs,
    [FieldTypeEnum.stageProcess]: StageProcess,
    [FieldTypeEnum.stageProportion]: StageProportion,
    [FieldTypeEnum.totalStayTime]: TotalStayTime,
    [FieldTypeEnum.stageStayTime]: StageStayTime,
    [FieldTypeEnum.createTime]: CreateTime,
    [FieldTypeEnum.manualSign]: ManualSign,
    [FieldTypeEnum.richText]: RichText,
    [FieldTypeEnum.multiRelyData]: MultiRelyData,
    [FieldTypeEnum.contractTemplate]: ContractTemplate,
    [FieldTypeEnum.lowCodeComponent]: LowCodeComponent,
    [FieldTypeEnum.referenceField]: ReferenceField
  }
  const fieldConstructor = fieldStrategy[fieldType]
  return fieldConstructor ? new fieldConstructor(options) : undefined
}
// 用来设置公共默认属性 表单设计拖拽模块
export function addWeightAttrOld(item) {
  const { attrName, attrType, memo, type, fieldType, icon, setType, subForm } = item
  const weightAttr = {
    subForm,
    setType,
    fieldId: xbb.guid(),
    icon,
    attrName,
    attrType,
    memo,
    type,
    fieldType,
    deleteConfig: 0,
    visible: 1, // 是否允许可见 1:允许 0：不允许
    visibleScopeEnable: 0, // 是否启用可见高级设置 1:启用 0：不启用
    // 可见高级设置规则，格式{"type":1,"relative":[2],"role":[],"dep":[],"user":[]
    visibleScopeRule: {
      type: 2, // 1 不给谁看，2 部分可见
      relative: [], // 相关人员，1 负责人，2 协同人，3 创建人
      role: [], // 授权角色id
      dep: [], // 授权部门
      user: [], // 授权用户
      userList: [] // 授权用户信息列表
    },
    editable: 1, // 是否允许编辑 1:允许 0：不允许
    editableAdvanceEnable: 0,
    editableRule: {
      type: 2, // 1 不给谁看，2 部分可见
      relative: [], // 相关人员，1 负责人，2 协同人，3 创建人
      role: [], // 授权角色id
      dep: [], // 授权部门
      user: [], // 授权用户
      userList: [] // 授权用户信息列表
    },
    showAdd: 0,
    add: 1, // 是否允许新增
    addAdvanceEnable: 1,
    addRule: {
      type: 2, // 1:不给谁看;2:部分可见;
      dep: [],
      relative: [1, 3, 4],
      role: [1, 2],
      roleList: [],
      user: [],
      userList: []
    },
    showRemove: 0,
    remove: 1, // 是否可删除
    removeAdvanceEnable: 1,
    removeRule: {
      type: 2, // 1:不给谁看;2:部分可见;
      dep: [],
      relative: [1, 3, 4], // 相关人员，1 负责人，2 协同人，3 创建人, 4 负责人主管
      role: [1, 2],
      roleList: [],
      user: [],
      userList: []
    },
    isRedundant: 1, // 是否为扩展字段
    noRepeat: 0, // 不允许重复值 1:不允许 0：允许；
    required: 0, // 是否必填 1:必填 0：选填
    defaultAttr: {
      defaultType: 'custom', // 默认值类型 custom：自定义 dataRely:数据联动, formula：公式编辑
      defaultValue: undefined, // 如果默认值类型选择的是自定义，这个值存在则需要显示
      defaultList: [],
      rely: {
        sourceMenuId: '', // 表单id
        sourceAppId: '', // 应用id
        displayField: '', // 联动显示的字段
        targetField: '', // 联动字段需要等于的字段
        linkField: '', // 联动的字段
        subRelyFieldList: []
      }
    },

    patternType: '' // 类型 手机号码：1 电话号码：2 邮政编码：3 身份证号码：4 邮箱：5.
  }
  // 单|复选 + 下拉单|复选
  if ([3, 9, 10000, 10001].includes(item.fieldType)) {
    weightAttr.items = [
      { text: i18n.t('utils.optionValue1'), value: xbb.guid(), checked: false },
      { text: i18n.t('utils.optionValue2'), value: xbb.guid(), checked: false },
      { text: i18n.t('utils.optionValue3'), value: xbb.guid(), checked: false }
    ]
  }
  if (item.fieldType === 2) {
    weightAttr.numericalLimits = {
      max: 9007199254740991,
      min: -9007199254740991
    }
    weightAttr.accuracy = 4
    weightAttr.integerOnly = 0
    weightAttr.numericalLimitsFlag = 0
  }
  if (item.fieldType === 4) {
    weightAttr.dateType = 'yyyy-MM-dd'
  }
  if (item.fieldType === 10001) {
    // weightAttr.itemList = []
    // weightAttr.comboType = 0
  }
  if (item.fieldType === 9) {
    weightAttr.comboType = 0
  }
  if (item.fieldType === 10000) {
    weightAttr.comboType = 0
  }
  if (item.fieldType === 10002) {
    weightAttr.lineType = 'thin'
  }
  if (item.fieldType === 12) {
    weightAttr.showDetailAddress = 1
    weightAttr.defaultAttr.defaultType = ''
  }
  if (item.fieldType === 6) {
    weightAttr.maxFileCount = 9
    weightAttr.onlyOneFile = 0
    weightAttr.autoCompress = 1
    weightAttr.onlyCamera = 0
  }
  if (item.fieldType === 8) {
    weightAttr.maxFileCount = 9
    weightAttr.onlyOneFile = 0
  }
  if (item.fieldType === 10007 || item.fieldType === 10008) {
    weightAttr.linkInfo = {
      linkMenuId: '',
      linkFormId: '',
      linkedAttr: [],
      condition: [],
      linkKey: ''
    }
    // weightAttr.linkedAttr = []
    // weightAttr.condition = []
  }
  // if (item.fieldType === 10009) {
  //   weightAttr.optionalRange = []
  //   weightAttr.limitChooseRange = {
  //     type: 'all',
  //     rely: {},
  //     optionalRange: []
  //   }
  // }
  // if (item.fieldType === 10010) {
  //   weightAttr.optionalRange = []
  //   weightAttr.limitChooseRange = {
  //     type: 'all',
  //     rely: {},
  //     optionalRange: []
  //   }
  // bxw注--由于请求报错
  // weightAttr.defaultAttr.defaultValue = []
  // }
  // if (item.fieldType === 10011) {
  //   weightAttr.optionalRange = []
  //   weightAttr.limitChooseRange = {
  //     type: 'all',
  //     rely: {},
  //     optionalRange: []
  //   }
  // }
  // if (item.fieldType === 10012) {
  //   weightAttr.optionalRange = []
  //   weightAttr.limitChooseRange = {
  //     type: 'all',
  //     rely: {},
  //     optionalRange: []
  //   }
  // bxw注--由于请求报错
  // weightAttr.defaultAttr.defaultValue = []
  // }
  if (item.fieldType === 10016 || item.fieldType === 10017 || item.fieldType === 10018) {
    // weightAttr.optionalRange = []
    // weightAttr.limitChooseRange = {
    //   type: 'all',
    //   optionalRange: []
    // }
    weightAttr.linkWeightList = []
    weightAttr.relatedMember = ''
  }
  if (item.fieldType === 10019) {
    // 不设置默认值直接在组件内增加新字段的话，组件内watch监听fieldInfo中的其他属性将会被触发
    weightAttr.strictController = 0 // 流水号严格模式
    weightAttr.serialNumber = {
      dateFormat: 'all',
      prefix: '',
      postfix: '',
      startNum: 1,
      numberDigit: 5,
      resetCycle: 1 // 重置周期
    }
  }
  if (item.fieldType === 10006) {
    weightAttr.subForm = {
      items: [],
      value: []
    }
  }
  if (item.fieldType === 10005) {
    // 超链接打开方式默认为 从外部应用打开 1:外部；0:内部
    weightAttr.blank = 0
  }
  if (item.fieldType === 10003) {
    weightAttr.showLatAndLng = 0
    weightAttr.locationCenterFlag = 0 // 是否开启定位中心 1：是 0：否
    weightAttr.locationCenter = []
    weightAttr.tuning = {
      enable: 0, // 是否允许微调 1:允许 0：不允许
      radius: 500
    }
  }
  if (item.fieldType === 3 || item.fieldType === 10001) {
    weightAttr.linkForm = {
      sourceAppId: '',
      sourceMenuId: '',
      displayField: ''
    }
    weightAttr.comboType = 0
  }
  // 哪些字段不允许控制编辑
  if (!/^(10002|10004|10013|10014|10015)$/.test(item.fieldType)) {
    weightAttr.showEditable = 1
  } else {
    weightAttr.showEditable = 0
  }
  if (/^(10013|10014|10015)$/.test(item.fieldType)) {
    weightAttr.unableEditMemo = i18n.t('utils.systemCreatedAttr')
  }
  // 负责人字段权限不一样
  if (item.fieldType === 10017) {
    weightAttr.showEditable = 0
    weightAttr.showAdd = 1
    weightAttr.showRemove = 1
    weightAttr.required = 1
  }
  return Object.assign({}, weightAttr)
}
// 子表单属性设置项
export function subFormItem() {
  return [
    {
      attrName: i18n.t('form.singleLineText'),
      type: 'text',
      attrType: 'text',
      icon: 'web-icon-field-text',
      fieldType: 1,
      disabled: false
    },
    {
      attrName: i18n.t('form.multilineText'),
      type: 'textArea',
      attrType: 'text',
      icon: 'web-icon-field-textarea',
      fieldType: 7,
      disabled: false
    },
    {
      attrName: i18n.t('nouns.number'),
      type: 'num',
      attrType: 'num',
      icon: 'web-icon-field-number',
      fieldType: 2,
      disabled: false
    },
    {
      attrName: i18n.t('label.date'),
      type: 'datetime',
      attrType: 'date',
      icon: 'web-icon-field-date',
      fieldType: 4,
      disabled: false
    },
    {
      attrName: i18n.t('form.radioButton'),
      type: 'radiogroup',
      attrType: 'text',
      icon: 'web-icon-field-radio',
      fieldType: 10000,
      disabled: false
    },
    {
      attrName: i18n.t('form.checkBoxGroup'),
      type: 'checkboxgroup',
      attrType: 'array',
      icon: 'web-icon-field-checkbox',
      fieldType: 9,
      disabled: false
    },
    {
      attrName: i18n.t('form.dropDownBox'),
      type: 'combo',
      attrType: 'text',
      icon: 'web-icon-field-select-radio',
      fieldType: 3,
      disabled: false
    },
    {
      attrName: i18n.t('form.dropDownCheckBox'),
      type: 'combocheck',
      attrType: 'array',
      icon: 'web-icon-field-select-checkbox',
      fieldType: 10001,
      disabled: false
    },
    {
      attrName: i18n.t('form.address'),
      type: 'address',
      attrType: 'address',
      icon: 'web-icon-field-address',
      fieldType: 12,
      disabled: false
    },
    {
      attrName: i18n.t('unit.location'),
      type: 'location',
      attrType: 'geo',
      icon: 'web-icon-field-location',
      fieldType: 10003,
      disabled: false
    },
    {
      attrName: i18n.t('label.picture'),
      type: 'image',
      attrType: 'file',
      icon: 'web-icon-field-image',
      fieldType: 6,
      disabled: false
    },
    {
      attrName: i18n.t('label.attachments'),
      type: 'upload',
      attrType: 'file',
      icon: 'web-icon-field-files',
      fieldType: 8,
      disabled: false
    },
    // { attrName: i18n.t('utils.relationalQuery'), type: 'linkquery', attrType: 'text', icon: 'web-icon-field-query', fieldType: 10007, disabled: false },
    {
      attrName: i18n.t('utils.associatedData'),
      type: 'linkdata',
      attrType: 'text',
      icon: 'web-icon-field-data',
      fieldType: 10008,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: i18n.t('utils.multiAssociatedData'),
      type: 'linkdata',
      attrType: 'array',
      icon: 'web-icon-gongzuoliu-xiaoxi1',
      fieldType: 10032,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: i18n.t('utils.memberRadio'),
      type: 'user',
      attrType: 'text',
      icon: 'web-icon-field-user',
      fieldType: 10009,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: i18n.t('utils.multiMemberRadio'),
      type: 'usergroup',
      attrType: 'array',
      icon: 'web-icon-field-users',
      fieldType: 10010,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: i18n.t('utils.departmentalSingleElection'),
      type: 'dept',
      attrType: 'text',
      icon: 'web-icon-field-dept',
      fieldType: 10011,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: i18n.t('utils.departmentSelection'),
      type: 'deptgroup',
      attrType: 'array',
      icon: 'web-icon-field-depts',
      fieldType: 10012,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: '引用字段',
      type: 'deptgroup',
      attrType: 'quote',
      icon: 'web-icon-field-depts',
      fieldType: 22,
      disabled: false,
      feeLimit: 1
    },
    {
      attrName: i18n.t('utils.manualSignField'),
      type: 'file',
      attrType: 'file',
      icon: 'web-icon-edit',
      fieldType: 10033,
      disabled: false,
      feeLimit: 1
    }
  ]
}

// 处理后端传来的字段的是否
export function formWeightBoolean(item) {
  !!item.visible === true ? (item.visible = true) : (item.visible = false)
  !!item.visibleScopeEnable === true
    ? (item.visibleScopeEnable = true)
    : (item.visibleScopeEnable = false)
  !!item.editable === true ? (item.editable = true) : (item.editable = false)
  !!item.required === true ? (item.required = true) : (item.required = false)
  !!item.noRepeat === true ? (item.noRepeat = true) : (item.noRepeat = false)
  !!item.integerOnly === true ? (item.integerOnly = true) : (item.integerOnly = false)
  !!item.showDetailAddress === true
    ? (item.showDetailAddress = true)
    : (item.showDetailAddress = false)
  !!item.onlyOneFile === true ? (item.onlyOneFile = true) : (item.onlyOneFile = false)
  !!item.autoCompress === true ? (item.autoCompress = true) : (item.autoCompress = false)
  !!item.onlyCamera === true ? (item.onlyCamera = true) : (item.onlyCamera = false)
  !!item.numericalLimitsFlag === true
    ? (item.numericalLimitsFlag = true)
    : (item.numericalLimitsFlag = false)
  if (item.hasOwnProperty('locationCenterFlag')) {
    !!item.locationCenterFlag === true
      ? (item.locationCenterFlag = true)
      : (item.locationCenterFlag = false)
  }
  if (item.hasOwnProperty('tuning')) {
    !!item.tuning.enable === true ? (item.tuning.enable = true) : (item.tuning.enable = false)
  }
  if (item.hasOwnProperty('showLatAndLng')) {
    !!item.showLatAndLng === true ? (item.showLatAndLng = true) : (item.showLatAndLng = false)
  }
}
// 处理传给后端的字段
export function formWeightNumber(item) {
  item.visible = item.visible ? 1 : 0
  item.visibleScopeEnable = item.visibleScopeEnable ? 1 : 0
  item.editable = item.editable ? 1 : 0
  item.required = item.required ? 1 : 0
  item.noRepeat = item.noRepeat ? 1 : 0
  item.integerOnly = item.integerOnly ? 1 : 0
  item.showDetailAddress = item.showDetailAddress ? 1 : 0
  item.onlyOneFile = item.onlyOneFile ? 1 : 0
  item.autoCompress = item.autoCompress ? 1 : 0
  item.onlyCamera = item.onlyCamera ? 1 : 0
  item.numericalLimitsFlag = item.numericalLimitsFlag ? 1 : 0
  if (item.hasOwnProperty('locationCenterFlag')) {
    item.locationCenterFlag = item.locationCenterFlag ? 1 : 0
  }
  if (item.hasOwnProperty('tuning')) {
    item.tuning.enable = item.tuning.enable ? 1 : 0
  }
  if (item.hasOwnProperty('showLatAndLng')) {
    item.showLatAndLng = item.showLatAndLng ? 1 : 0
  }
}

/**
 * 处理限制对象
 * @param {Object} data 返回的限制对象
 * @param {Number} type 1:自定义表单 2:系统表单 3:子表单
 */
const limitHandler = (data = {}, type = 1) => {
  const resultObj = {}

  switch (type) {
    case 1: //自定义表单
      for (const prop in data) {
        resultObj[prop] = data[prop].customize
      }
      break
    case 2: //系统表单
    case 3: //子表单
      for (const prop in data) {
        resultObj[prop] = data[prop].total
      }
      break
  }

  return resultObj
}

/**
 *
 * 生成唯一的key
 * @param {array} explainList 存储所有控件的列表
 * @param {string} [prefix='text'] 控件前缀类型
 * @param {string} type 当type为subForm时，控件限制数量不一样
 * @param {number} businessType 业务类型 用于处理特殊逻辑
 * @param {number} fieldType 字段类型 用于处理特殊逻辑
 * @returns key
 */
let __INDEX = 1
export function createUniqueKey({
  explainList,
  prefix = 'text',
  type,
  fieldType,
  businessType,
  saasMark
}) {
  const {
    fieldMap, //自定义表单字段数量
    subFieldMap //子表单字段数量
  } = store.getters.formInfo

  // 不同空间类型的数量限制
  let countLimit = {
    text: 80,
    num: 70,
    array: 30,
    file: 10,
    date: 20,
    geo: 2,
    other: 20,
    subForm: 8,
    address: 2,
    lowcode: 10,
    quote: 2 // 引用字段控件数量 后期要计算整个表单的引用字段数量 暂时先这样
  }

  //覆盖默认限制配置
  countLimit = Object.assign(countLimit, limitHandler(fieldMap, 1))

  // 针对系统表单修改数量限制
  if (saasMark === 1) {
    countLimit = {
      text: 100 - 20,
      num: 80 - 20, // 暂定不改
      array: 40 - 10,
      file: 10 - 2,
      date: 30 - 10,
      geo: 2,
      other: 20,
      subForm: 8,
      address: 2,
      lowcode: 10
    }

    //覆盖默认限制配置
    countLimit = Object.assign(countLimit, limitHandler(fieldMap, 2))
  }

  // 不同空间类型的数量限制-子表单
  let subFormCountLimit = {
    text: 19,
    num: 15,
    array: 20,
    file: 10,
    date: 10,
    geo: 2,
    address: 2
  }

  //覆盖默认限制配置
  subFormCountLimit = Object.assign(subFormCountLimit, limitHandler(subFieldMap, 3))

  // 过滤出同类型的数据集合
  const filterexplainList = explainList.filter(
    (item) => item && item.attr && item.attr.indexOf(prefix) > -1
  )

  // 筛选出集合中的attr的序号
  // 2022.8.26:问题：日期时段attr = date 经过正则分割出来是'date' 导致Number包装后直接变成NaN 经过map后变成[NaN]导致下面处理key有问题
  // const storedAttrs = filterexplainList.map((item) => Number(item.attr.match(/([^_]+)$/)[0]))
  const storedAttrs = filterexplainList.map((item, index) => {
    const attr = Number(item.attr.match(/([^_]+)$/)[0])
    if (!isNaN(attr)) return attr
    return index === 0 ? index + 1 : index
  })

  // 特殊逻辑 如果 businessType 是 1505 1506 1507 这几个场景，不允许用户主动生成 subForm_3 这个 attr
  if (
    ['1505', '1506', '1507'].includes(businessType) &&
    prefix === 'subForm' &&
    storedAttrs.indexOf(3) === -1
  ) {
    storedAttrs.push(3)
  }
  let key = ''
  const limitArray = type === 'subForm' ? subFormCountLimit : countLimit

  // console.log("storedAttrs", storedAttrs);

  /**
   * 给一个数组，返回当前可插入的最小值
   * 同时判断到customize时，跳过reserved，直到达到限制total
   * @param {Array} arr
   * @param {*} start
   * @param {*} customize 80
   * @param {*} reserved 20
   * @param {*} total 130
   */

  function addKeyHandler(arr, start, customize, reserved, total) {
    // console.log("arr", arr, "start", start, "customize", customize, "reserved", reserved, "total", total);

    // 为了解决某些字段刚进来数组为空的时候 || 字段并未从1开始，直接是[29, 30] 给个默认值1
    if (arr.length === 0 || !arr.includes(1)) return 1

    // 给一个arr，返回可以最小插入的值
    function handler(resultArr) {
      // if (resultArr.indexOf(start) == -1) return start;
      const newArr = [...resultArr.sort((a, b) => a - b)]

      const curMin = newArr[0]
      if (newArr.includes(curMin + 1)) {
        // 如果找到的话
        newArr.shift()
        return handler(newArr)
      } else {
        return curMin + 1
      }
    }

    let minKey = ''

    // 是否大于了系统预留数
    const hasCutNum = arr.filter((it) => it > customize + reserved)

    minKey = handler(arr)

    // console.log("minKeyminKey", minKey);

    if (hasCutNum.length > 0 && minKey > customize) {
      // console.log('跳过预留数', hasCutNum)
      if (arr.indexOf(start) === -1) return start
      minKey = handler(hasCutNum)
    } else if (minKey > customize && minKey < total) {
      // 正常赋值
      minKey = customize + reserved + __INDEX
      __INDEX++
    }

    // 插入最小值大于总数，返回空，弹报错弹窗
    if (minKey > total) return ''
    return minKey
  }
  if (saasMark === 1 && fieldMap[prefix]) {
    // 如果是系统表单，需要做预留处理
    const {
      customize = 0, //用户自定义限制
      reserved = 0 //系统表单限制
    } = fieldMap[prefix]

    // 如果是20004(类似关联产品这种子表单，他的 key 是算作 subForm 的空间在占用的)
    const arrList = explainList.filter((item) => item && item.fieldType && item.fieldType === 20004)
    // 筛选出集合中的array的序号
    // const arrayAttrs = arrList.map((item) => Number(item.attr.match(/([^_]+)$/)[0]))

    // console.log("fieldMap[prefix]", fieldMap[prefix], customize, reserved);

    key = addKeyHandler(
      storedAttrs,
      customize + reserved + 1,
      customize,
      reserved,
      limitArray[prefix]
    )

    console.log('88888888888777', key)

    // 如果包含array，子表单拖拽数应该加上array的数量
    if (prefix.includes('subForm') && arrList.length > 0 && key > customize - arrList.length) {
      // console.log("arrList", arrList, "key", key, "total", customize -  arrList.length);
      key = ''
    }
    // 这段代码等后端也改了在用
    // if (prefix.includes('array') && arrList.length > 0) {
    //   // countLimit.array += arrList.length
    //   key = addKeyHandler(
    //     storedAttrs,
    //     customize + reserved + 1 + arrList.length,
    //     customize + arrList.length,
    //     reserved,
    //     limitArray[prefix]
    //   )
    // }
    // console.log('minKey', key)
  } else {
    // 自定义表单，子表单，直接用原来的逻辑
    for (let i = 1; i <= limitArray[prefix]; i++) {
      if (storedAttrs.indexOf(i) === -1) {
        key = i
        break
      }
    }
  }

  // 特殊逻辑：阶段推进器字段(10030)有且只能有一个,且不能存在于子表单中
  // 后端需要text字段存入es，无法新增枚举字段(attrType)去限制数量，必须要attrType为text，既：text_xx
  if (type === 'subForm' && fieldType === 10030) return
  console.log('ba', fieldMap[prefix])
  // console.log('addKeyHandler',  fieldMap[prefix]) // 一个表单的最多数量
  if (!quotePopup(fieldMap[prefix])) return
  if (!key || storedAttrs.length > limitArray[prefix]) {
    Message({
      type: 'error',
      message: i18n.t('utils.widgetOverControl')
    })
    return
  }

  console.log('8888888888', prefix + '_' + key)
  return prefix + '_' + key
}
function quotePopup(fieldMap) {
  if (fieldMap?.alias === 'quote') {
    let toplimit = 1 // 引用字段的剩余个数
    const { total, used } = fieldMap
    console.log('total', total, used)
    toplimit = total - used
    if (toplimit >= 1) {
      store.commit('SET_FIELD_MAP_QUOTE_USED', used + 1)
    }
    if (toplimit < 1) {
      Message({
        type: 'error',
        message: `当前引用字段可用总量为${total}个，已用${used}个，引用字段用量已触达上限，若需要使用更多引用字段，可联系客服购买资源包。`
      })
      return false
    }
  }
  return true
}
/**
 * 生成系统字段的唯一值attr
 * @param {number} fieldType 系统字段的区分值
 */
export function createSystemKey(fieldType) {
  const systemAttr = [
    { fieldType: 10013, attr: 'creatorId' },
    { fieldType: 10014, attr: 'addTime' },
    { fieldType: 10015, attr: 'updateTime' },
    { fieldType: 10016, attr: 'departmentId' },
    { fieldType: 10017, attr: 'ownerId' },
    { fieldType: 10018, attr: 'coUserId' },
    { fieldType: 10019, attr: 'serialNo' }
  ]
  const currentAttr = systemAttr.filter((item) => {
    return item.fieldType === fieldType
  })
  return currentAttr[0].attr
}
/**
 * 根据主键去找到对应字段对象
 * @param {String} key
 * text_1 | subForm_1.text_1
 */
export function getField(key = '', fields = []) {
  const keyArr = key.split('.')
  const [baseAttr, subAttr] = keyArr
  // 找第一级
  let baseField
  let targetField
  fields.some((item) => {
    const { attr } = item
    if (attr === baseAttr) {
      baseField = item
      return true
    }
    return false
  })
  if (subAttr) {
    const { subform } = baseField
    const { items } = subform
    items.some((item) => {
      const { attr } = item
      if (attr === subAttr) {
        targetField = item
        return true
      }
      return false
    })
    return targetField
  } else {
    return baseField
  }
}

// 将普通字段的字段解释解析成低代码字段的字段解释
export function encodeLowCodeComponentExplain(explainList) {
  const lists = explainList.map((item) => {
    if (item.isLowCodeComponent && item.lowCodeComponentUrl) {
      const lowCodeItem = fieldAttrMap.baseList.find((field) => field.fieldType === 800008)
      const lowCodeComponentField = xbb.deepClone(addWeightAttr(lowCodeItem))
      lowCodeComponentField.attr = item.attr
      lowCodeComponentField.attrName = item.attrName
      lowCodeComponentField.isLowCodeComponent = item.isLowCodeComponent
      lowCodeComponentField.lowCodeComponentUrl = item.lowCodeComponentUrl
      lowCodeComponentField.mapFieldInfo = item
      lowCodeComponentField.defaultAttr = item.defaultAttr
      lowCodeComponentField.editable = item.editable
      lowCodeComponentField.editableAdvanceEnable = item.editableAdvanceEnable
      return lowCodeComponentField
    }
    return item
  })
  return lists
}
