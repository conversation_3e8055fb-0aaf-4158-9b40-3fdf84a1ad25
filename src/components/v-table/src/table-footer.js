/* eslint-disable */
import LayoutObserver from './layout-observer'

export default {
  name: 'ElTableFooter',

  mixins: [LayoutObserver],

  render(h) {
    let sums = []
    if (this.summaryMethod) {
      sums = this.summaryMethod({ columns: this.columns, data: this.store.states.data })
    } else {
      this.columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.sumText
          return
        }
        const values = this.store.states.data.map((item) => Number(item[column.property]))
        const precisions = []
        let notNumber = true
        values.forEach((value) => {
          if (!isNaN(value)) {
            notNumber = false
            let decimal = ('' + value).split('.')[1]
            precisions.push(decimal ? decimal.length : 0)
          }
        })
        const precision = Math.max.apply(null, precisions)
        if (!notNumber) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return parseFloat((prev + curr).toFixed(Math.min(precision, 20)))
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = ''
        }
      })
    }

    return (
      <table class="el-table__footer" cellspacing="0" cellpadding="0" border="0">
        <colgroup>
          {this._l(this.columns, (column) => (
            <col name={column.id} />
          ))}
          {this.hasGutter ? <col name="gutter" /> : ''}
        </colgroup>
        <tbody class={[{ 'has-gutter': this.hasGutter }]}>
          <tr style={this.customHeadStyle}>
            {this._l(this.columns, (column, cellIndex) => (
              <td
                colspan={column.colSpan}
                rowspan={column.rowSpan}
                class={[
                  column.id,
                  column.headerAlign,
                  column.className || '',
                  this.isCellHidden(cellIndex, this.columns) ? 'is-hidden' : '',
                  !column.children ? 'is-leaf' : '',
                  column.labelClassName
                ]}
                style={this.customHeadStyle}
              >
                {this.isSummaryTips && cellIndex === 0 ? (
                  <div
                    onMouseenter={this.mouseEnterHanlder}
                    class={['cell', column.labelClassName]}
                    style={this.dynamicCellStyleFirst}
                  >
                    <span>{sums[cellIndex]}</span>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content={this.summaryTips}
                      placement="top"
                    >
                      <i class="web-icon-question-circle web-iconfont"></i>
                    </el-tooltip>
                  </div>
                ) : (
                  <el-tooltip
                    disabled={this.summaryTooltipShow}
                    content={String(sums[cellIndex])}
                    placement="top"
                  >
                    <div
                      onMouseenter={this.mouseEnterHanlder}
                      class={['cell', column.labelClassName]}
                      style={this.dynamicCellStyle}
                    >
                      <span>{sums[cellIndex]}</span>
                    </div>
                  </el-tooltip>
                )}
              </td>
            ))}
            {this.hasGutter ? <th class="gutter" style={this.customHeadStyle}></th> : ''}
          </tr>
        </tbody>
      </table>
    )
  },

  props: {
    fixed: String,
    store: {
      required: true
    },
    summaryMethod: Function,
    summaryCenter: {
      type: Boolean,
      default: false
    },
    summaryTips: {
      type: String,
      default: ''
    },
    sumText: String,
    border: Boolean,
    defaultSort: {
      type: Object,
      default() {
        return {
          prop: '',
          order: ''
        }
      }
    },
    itemStyleOption: {}
  },

  computed: {
    table() {
      return this.$parent
    },

    isAllSelected() {
      return this.store.states.isAllSelected
    },

    columnsCount() {
      return this.store.states.columns.length
    },

    leftFixedCount() {
      return this.store.states.fixedColumns.length
    },

    rightFixedCount() {
      return this.store.states.rightFixedColumns.length
    },

    columns() {
      return this.store.states.columns
    },

    hasGutter() {
      return !this.fixed && this.tableLayout.gutterWidth
    },

    customContentStyle() {
      if (!this.itemStyleOption) {
        return
      }
      return {
        color: this.itemStyleOption.itemTableContentFont,
        background: this.itemStyleOption.itemTableContent
      }
    },
    customHeadStyle() {
      if (!this.itemStyleOption) {
        return
      }
      return {
        color: this.itemStyleOption.itemTableHeadFont,
        background: this.itemStyleOption.itemTableHead
      }
    },
    dynamicCellStyle() {
      if (!this.summaryCenter) {
        return
      }
      return {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    },
    dynamicCellStyleFirst() {
      if (!this.summaryCenter) {
        return
      }
      return {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '300px',
        position: 'absolute',
        zIndex: 1,
        backgroundColor: '#ffffff'
      }
    },
    isSummaryTips() {
      return this.summaryTips
    }
  },

  data() {
    return {
      summaryTooltipShow: true
    }
  },

  methods: {
    isCellHidden(index, columns) {
      if (this.fixed === true || this.fixed === 'left') {
        return index >= this.leftFixedCount
      } else if (this.fixed === 'right') {
        let before = 0
        for (let i = 0; i < index; i++) {
          before += columns[i].colSpan
        }
        return before < this.columnsCount - this.rightFixedCount
      } else {
        return index < this.leftFixedCount || index >= this.columnsCount - this.rightFixedCount
      }
    },
    mouseEnterHanlder(evt) {
      let e = evt || window.event
      if (e.target.querySelector('span')) {
        this.summaryTooltipShow =
          e.target.querySelector('span').offsetWidth <= e.target.offsetWidth - 20
      }
    }
  }
}
