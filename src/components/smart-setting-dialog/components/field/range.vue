<!--
 * @Description:
 -->
<template>
  <span class="range">
    <el-input v-model="valueStart" class="input-width"></el-input>
    <span class="input-diver">-</span>
    <el-input v-model="valueEnd" class="input-width"></el-input>
    <span class="input-left">{{ suffixText }}</span>
  </span>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    fieldType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      valueStart: '',
      valueEnd: ''
    }
  },

  computed: {
    suffixText() {
      if ([340102, 340104, 340106, 340114].includes(this.fieldType)) {
        return this.$t('unit.day')
      } else if ([340107, 340108, 340109, 340110].includes(this.fieldType)) {
        return this.$t('smartSettingDialog.unprocessed')
      }
      return ''
    }
  },
  watch: {
    valueStart() {
      this.uodateVal()
    },
    valueEnd() {
      this.uodateVal()
    }
  },

  created() {
    this.valueStart = this.value[0] || 1
    this.valueEnd = this.value[1] || 1
  },
  methods: {
    uodateVal() {
      this.$emit('input', [this.valueStart, this.valueEnd])
    }
  }
}
</script>

<style lang="scss" scoped>
.input-width {
  width: 100px;
}
.input-left {
  padding-left: 10px;
}
.input-diver {
  padding: 0 10px;
}
.range {
  white-space: nowrap;
}
</style>
