/*
 * @Description: 公式相关 工具函数
 */
import $ from 'static/formulaDemo-master/jquery.min.js'
import xbb from '@xbb/xbb-utils'

window.$ = $
window.jQuery = $
const a = $
// 计算两位数应该乘以的数，为加减乘数服务的函数
// function formatNumber (num1, num2) {
//   let r1, r2
//   try {
//     r1 = num1.toString().split('.')[1].length
//   } catch (e) {
//     r1 = 0
//   }
//   try {
//     r2 = num2.toString().split('.')[1].length
//   } catch (e) {
//     r2 = 0
//   }
//   let sum = r1 + r2
//   let sub = r2 - r1
//   return { 'max': Math.pow(10, Math.max(r1, r2)), 'sum': Math.pow(10, sum), 'sub': Math.pow(10, sub) }
// }

/* eslint-disable */
const Utils = {
  toFixed(a, n = 0) {
    return Math.round(a * Math.pow(10, n)) / Math.pow(10, n)
  },
  isString: function (a) {
    return typeof a === 'string'
  },
  isNumber: function (b) {
    return a.isNumeric(b)
  },
  isFunction: function (b) {
    return a.isFunction(b)
  },
  isDate: function (a) {
    return a instanceof Date
  },
  isArray: function (b) {
    return a.isArray(b)
  },
  isEmpty: function (a) {
    console.log(Utils.isNull(a))
    return a === '' || xbb.isNull(a) || a === undefined
  },
  isBlank: function (a) {
    return xbb.isNull(a) || a.trim() === ''
  },
  isNull: function (a) {
    return a == null
  },
  isObjectEmpty: function (a) {
    if (a == null) {
      return !0
    }
    if (a.length > 0) {
      return !1
    }
    if (a.length === 0) {
      return !0
    }
    for (var b in a) {
      if (hasOwnProperty.call(a, b)) {
        return !1
      }
    }
    return isNaN(a)
  },
  isValueWidget: function (a) {
    return !!fml.ValueWidgets[a]
  },
  address2Str: function (a, b, c) {
    if (c && a) {
      b = ''
      var d = !0
      ;/p/.test(c) && a.province && ((b += a.province), a.province === a.city && (d = !1)),
        /c/.test(c) && a.city && d && (b += a.city),
        /d/.test(c) && a.district && (b += a.district),
        /a/.test(c) && a.detail && (b += a.detail)
    }
    return b
  },
  num2Str: function (a, b) {
    if (Utils.isEmpty(a)) {
      return ''
    }
    var c = a + ''
    if (Utils.isEmpty(b)) {
      return c
    }
    var d = /\[Num0\]/
    if (d.test(b)) {
      return b.replace(d, c)
    }
    if (((d = /\[Num1\]/), d.test(b))) {
      return b.replace(d, Utils._num2CN(c, !1))
    }
    if (((d = /\[Num2\]/), d.test(b))) {
      return b.replace(d, Utils._num2CN(c, !0))
    }
    d = /[#0]+,?[#0]*\.?[#0]*%?/
    var e = b.match(d)
    if (e && e.length > 0) {
      var f = e[0]
      return (c = Utils._numberFormat(a, f)), b.replace(d, c)
    }
    return b
  },
  _numberFormat: function (a, b) {
    var c = '',
      d = a + '',
      bb = b
    if (/%$/.test(b)) {
      ;(c = '%'), (a = 100 * a), (b = b.replace('%', ''))
      var e = d.indexOf('.')
      if (e > -1) {
        var f = d.length - 3 - e
        ;(f = f < 0 ? 0 : f > 8 ? 8 : f), (a = parseFloat(a.toFixed(f)))
      }
      d = a + ''
    }
    var g = b.split('.'),
      h = g[0],
      i = g[1]
    if (i !== '') {
      var j = i ? i.length : 0
      // d = parseFloat(a).toFixed(j)
      d = this.toFixed(a, j).toFixed(j)
      if (!/%$/.test(bb)) {
        for (var k = d.split(''), l = j; l > 0 && i.charAt(l - 1) === '#'; l--) {
          var m = k.pop()
          if (m !== '0') {
            k.push(m)
            break
          }
        }
        var n = k.pop()
        n === '.' && (n = ''), (d = k.join('') + n)
      }
    }
    var o = d.split('.'),
      p = o[0]
    if (/,/.test(h)) {
      o[0] = p.replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g, '$1,')
    } else {
      var q = h.match(/[0]+[0#]*$/)
      q && q.length > 0 && (o[0] = Utils.leftPad(p, q[0].length, '0'))
    }
    return o.join('.') + c
  },
  _num2CN: function (a, b) {
    if (b) {
      return this.changeNumMoneyToChinese(a)
    }
    var c = '〇一二三四五六七八九',
      d = '个十百千万亿'
    var e = Math.floor(Math.abs(a)),
      f = Math.abs(a).toString(),
      g = f.replace(/\..*$/, ''),
      h = f.split('.'),
      i = c,
      j = d,
      k = '-.',
      l = i[0],
      m = new RegExp(i[0] + '*$'),
      n = new RegExp(i[0] + '+', 'g'),
      o = '',
      p = ''
    if (((o = a < 0 ? k[0] : ''), h.length >= 2)) {
      var q = h[1]
      if (q) {
        p = k[1]
        for (var r = 0; r < q.length; r++) {
          p += i[+q[r]]
        }
      }
    }
    if (g.length == 1) {
      return o + i[e] + p
    }
    if (g.length <= 5) {
      for (var s = '', t = 0, e = g.length; e--; ) {
        var u = +g[t]
        ;(s += this._num2CN(g[t], b) + (u && e ? j[e] : '')), t++
      }
      return (s = s.replace(n, l)), (s = s.replace(m, '')), o + s + p
    }
    for (var v = (g.length / 4) >> 0, w = g.length % 4, s = ''; w == 0 || !j[3 + v]; ) {
      ;(w += 4), v--
    }
    if (+g.substr(0, w)) {
      s = this._num2CN(g.substr(0, w), b) + j[3 + v]
      var x = g.substr(w)
      x[0] === '0' && (s += i[0]), (s += this._num2CN(x, b))
    } else {
      s = this._num2CN(g.substr(0, w), b) + this._num2CN(g.substr(w), b)
    }
    return (s = s.replace(n, l)), (s = s.replace(m, '')), o + s + p
  },
  changeNumMoneyToChinese(money) {
    var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖') //汉字的数字
    var cnIntRadice = new Array('', '拾', '佰', '仟') //基本单位
    var cnIntUnits = new Array('', '万', '亿', '兆') //对应整数部分扩展单位
    var cnDecUnits = new Array('角', '分', '厘', '毫') //对应小数部分单位
    var cnInteger = '整' //整数金额时后面跟的字符
    var cnIntLast = '元' //整型完以后的单位
    var maxNum = 999999999999999.9999 //最大处理的数字
    var IntegerNum //金额整数部分
    var DecimalNum //金额小数部分
    var ChineseStr = '' //输出的中文金额字符串
    var parts //分离金额后用的数组，预定义
    if (money == '') {
      return ''
    }
    money = parseFloat(money)
    if (money >= maxNum) {
      alert('超出最大处理数字')
      return ''
    }
    if (money == 0) {
      ChineseStr = cnNums[0] + cnIntLast + cnInteger
      return ChineseStr
    }
    if (money < 0) {
      ChineseStr += '负'
      money = Math.abs(money)
    }
    money = money.toString() //转换为字符串
    if (money.indexOf('.') == -1) {
      IntegerNum = money
      DecimalNum = ''
    } else {
      parts = money.split('.')
      IntegerNum = parts[0]
      DecimalNum = parts[1].substr(0, 4)
    }
    if (parseInt(IntegerNum, 10) > 0) {
      //获取整型部分转换
      var zeroCount = 0
      var IntLen = IntegerNum.length
      for (var i = 0; i < IntLen; i++) {
        var n = IntegerNum.substr(i, 1)
        var p = IntLen - i - 1
        var q = p / 4
        var m = p % 4
        if (n == '0') {
          zeroCount++
        } else {
          if (zeroCount > 0) {
            ChineseStr += cnNums[0]
          }
          zeroCount = 0 //归零
          ChineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
        }
        if (m == 0 && zeroCount < 4) {
          ChineseStr += cnIntUnits[q]
        }
      }
      ChineseStr += cnIntLast
      //整型部分处理完毕
    }
    if (DecimalNum != '') {
      //小数部分
      var decLen = DecimalNum.length
      for (var i = 0; i < decLen; i++) {
        var n = DecimalNum.substr(i, 1)
        if (n != '0') {
          ChineseStr += cnNums[Number(n)] + cnDecUnits[i]
        }
      }
    }
    if (ChineseStr == '') {
      ChineseStr += cnNums[0] + cnIntLast + cnInteger
    } else if (DecimalNum == '') {
      ChineseStr += cnInteger
    }
    return ChineseStr
  },
  date2Str: function (t, e) {
    if (!t) return ''
    var i = (e = e || 'yyyy-MM-dd').length,
      a = ''
    if (i > 0) {
      for (var s = e.charAt(0), n = 0, o = s, r = 1; r < i; r++) {
        var l = e.charAt(r)
        s !== l
          ? ((a += Utils._compileDateFormat(
              {
                char: s,
                str: o,
                len: r - n
              },
              t
            )),
            (n = r),
            (o = s = l))
          : (o += l)
      }
      a += Utils._compileDateFormat(
        {
          char: s,
          str: o,
          len: i - n
        },
        t
      )
    }
    return a
  },
  getDateDN: function () {
    return ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  },
  getDateSDN: function () {
    return ['日', '一', '二', '三', '四', '五', '六']
  },
  getDateMN: function () {
    return [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月'
    ]
  },
  _compileDateFormat: function (t, e) {
    var i = t.str,
      a = t.len
    switch (t.char) {
      case 'E':
        i =
          a > 2
            ? Utils.getDateDN()[e.getDay()]
            : a > 1
            ? Utils.getDateSDN()[e.getDay()]
            : e.getDay() + ''
        break
      case 'y':
        i = a <= 3 ? (e.getFullYear() + '').slice(2, 4) : e.getFullYear()
        break
      case 'M':
        i =
          a > 2
            ? Utils.getDateMN()[e.getMonth()]
            : a < 2
            ? e.getMonth() + 1
            : Utils.leftPad(e.getMonth() + 1 + '', 2, '0')
        break
      case 'd':
        i = a > 1 ? Utils.leftPad(e.getDate() + '', 2, '0') : e.getDate()
        break
      case 'h':
        var s = e.getHours() % 12
        0 === s && (s = 12), (i = a > 1 ? Utils.leftPad(s + '', 2, '0') : s)
        break
      case 'H':
        i = a > 1 ? Utils.leftPad(e.getHours() + '', 2, '0') : e.getHours()
        break
      case 'm':
        i = a > 1 ? Utils.leftPad(e.getMinutes() + '', 2, '0') : e.getMinutes()
        break
      case 's':
        i = a > 1 ? Utils.leftPad(e.getSeconds() + '', 2, '0') : e.getSeconds()
        break
      case 'a':
        i = e.getHours() < 12 ? 'am' : 'pm'
        break
      default:
        i = t.str
    }
    return i
  },
  pick: function (a, b) {
    var c = {}
    return (
      Utils.forEach(b, function (b, d) {
        d in a && (c[d] = a[d])
      }),
      c
    )
  },
  applyFunc: function (a, b, c, d) {
    return Utils.isFunction(b) ? b.apply(a, c || []) : d
  },
  forEach: function (a, b) {
    if (Array.isArray(a) || a instanceof jQuery) {
      for (var c = 0, d = a.length; c < d && b.apply(a[c], [c, a[c]]) !== !1; c++);
    } else if (a && typeof a === 'object') {
      for (var e in a) {
        if (a.hasOwnProperty(e) && b.apply(a[e], [e, a[e]]) === !1) {
          break
        }
      }
    }
  },
  flatten: function (a, b, c) {
    if ((c || (c = []), a)) {
      for (var d = 0, e = a.length; d < e; d++) {
        var f = a[d]
        Array.isArray(f) ? Utils.flatten(f, b, c) : (b && !b(f)) || c.push(f)
      }
    }
    return c
  },
  applyCss: function (a, b) {
    Utils.isEmpty(b) || (Utils.isString(b) ? a.addClass(b) : a.css(b))
  },
  getServerDate: function (a) {
    if (a && a.getResponseHeader) {
      var b = a.getResponseHeader('date')
      b && ((fml.STATIC._st = new Date(b).getTime()), (fml.STATIC._ct = new Date().getTime()))
    }
  },
  ajax: function (b, c, d, e) {
    return a
      .ajax({
        type: 'POST',
        beforeSend: function (a) {
          a.setRequestHeader('X-CSRF-Token', fml.STATIC.CSRF)
        },
        url: b.url,
        async: b.async !== !1,
        data: JSON.stringify(b.data),
        contentType: 'application/json;charset=UTF-8',
        timeout: b.timeout
      })
      .done(function (a, b, d) {
        Utils.getServerDate(d), c && c(a, b), e && e(a, b)
      })
      .fail(function (a, b, c) {
        switch (a.status) {
          case 400:
            if (Utils.applyFunc(this, d, [a, b], !1) === !1) {
              var f = a.responseJSON || {}
              f.msg
                ? fml.Msg.toast({
                    type: 'warning',
                    msg: f.msg
                  })
                : fml.Msg.toast({
                    type: 'error',
                    msg: 'é”™è¯¯çš„è¯·æ±‚'
                  })
            }
            break
          case 401:
            fml.Msg.toast({
              type: 'warning',
              msg: 'ç”¨æˆ·æœªç™»å½•'
            })
            break
          case 402:
            fml.Msg.toast({
              type: 'warning',
              msg: 'å½“å‰ä¼šè¯å·²è¿‡æœŸ'
            })
            break
          case 403:
            fml.Msg.toast({
              type: 'warning',
              msg: 'æ²¡æœ‰æ•°æ®è¯·æ±‚æƒé™'
            })
            break
          case 404:
            fml.Msg.toast({
              type: 'warning',
              msg: 'æ‰¾ä¸åˆ°æ•°æ®èµ„æº'
            })
            break
          case 0:
            break
          default:
            fml.Msg.toast({
              type: 'warning',
              msg: 'ä¸ŽæœåŠ¡å™¨é€šä¿¡å¤±è´¥'
            }),
              console && console.log('é€šä¿¡å¤±è´¥')
        }
        e && e(a, b)
      })
  },
  ajaxUpload: function (b, c, d, e) {
    a.ajax({
      type: 'POST',
      url: b.url,
      data: b.data,
      cache: !1,
      contentType: !1,
      processData: !1,
      beforeSend: function (a) {
        a.setRequestHeader('X-CSRF-Token', fml.STATIC.CSRF)
      },
      xhr: function () {
        var c = a.ajaxSettings.xhr()
        return (
          c.upload &&
            b.onUpload &&
            c.upload.addEventListener(
              'progress',
              function (a) {
                a.lengthComputable ? b.onUpload(a.loaded, a.total) : b.onUpload(3, 10)
              },
              !1
            ),
          c
        )
      }
    })
      .done(function () {
        c && c.apply(this, arguments), e && e.apply(this, arguments)
      })
      .fail(function () {
        d && d.apply(this, arguments), e && e.apply(this, arguments)
      })
  },
  dataAjax: function (a, b, c, d) {
    a.data = a.data || {}
    var e = a.data
    return (
      Utils.isEmpty(fml.STATIC.APPID) || (e.appId = fml.STATIC.APPID),
      Utils.isEmpty(fml.STATIC.ENTRYID) || (e.entryId = fml.STATIC.ENTRYID),
      Utils.isEmpty(fml.STATIC.DATAID) || (e.dataId = fml.STATIC.DATAID),
      fml.STATIC.BACKUP && (e.isBackup = !0),
      Utils.isEmpty(fml.STATIC.FTOKEN)
        ? Utils.isEmpty(fml.STATIC.QTOKEN)
          ? Utils.isEmpty(fml.STATIC.RTOKEN)
            ? Utils.isEmpty(fml.STATIC.ATOKEN) ||
              ((e.fx_access_token = fml.STATIC.ATOKEN), (e.fx_access_type = 'app_public'))
            : ((e.fx_access_token = fml.STATIC.RTOKEN), (e.fx_access_type = 'report_public'))
          : ((e.fx_access_token = fml.STATIC.QTOKEN), (e.fx_access_type = 'form_query'))
        : ((e.fx_access_token = fml.STATIC.FTOKEN), (e.fx_access_type = 'form_public')),
      Utils.ajax(
        a,
        function (a, c) {
          b(a, c)
        },
        c,
        d
      )
    )
  },
  getUrlParameter: function (a) {
    for (var b = window.location.search.substring(1), c = b.split('&'), d = 0; d < c.length; d++) {
      var e = c[d].split('=')
      if (e[0] == a) {
        return e[1]
      }
    }
    return null
  },
  validateEmail: function (a) {
    return /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(a)
  },
  redirectTo: function (a) {
    window.location.href = a
  },
  isCanvasSupported: function () {
    var a = document.createElement('canvas')
    return !(!a.getContext || !a.getContext('2d'))
  },
  isFormDataSupported: function () {
    return void 0 !== window.FormData
  },
  getFileDownloadURL: function (a, b, c) {
    switch (a.bucket) {
      case fml.CONST.QN_BUCKET.PUBLIC_IMAGE:
        var d = '',
          e = a.thumb
        return (
          e && (d = '?imageView2/' + e.mode + '/w/' + e.width + '/h/' + e.height),
          b(fml.CONFIG.HOST.IMAGE_HOST + '/' + a.qnKey + d)
        )
      case fml.CONST.OSS_BUCKET.PUBLIC_IMAGE:
        return b(fml.CONFIG.HOST.OSS_IMAGE_HOST + '/' + a.ossKey)
      case fml.CONST.QN_BUCKET.PRIVATE_FILE:
      default:
        if (!fml.STATIC.APPID) {
          return c()
        }
        Utils.dataAjax(
          {
            url: '/dashboard/app/' + fml.STATIC.APPID + '/file_url',
            data: a
          },
          function (a) {
            b(a.url)
          },
          function (a) {
            Utils.applyFunc(this, c, [a], !1) === !1 &&
              fml.Msg.toast({
                type: 'warning',
                msg: 'æ–‡ä»¶èŽ·å–å¤±è´¥'
              })
          }
        )
    }
  },
  evalFormula: function (a) {
    var b = []
    Utils.forEach(Object.keys(fml.Formula), function (a, c) {
      b.push('var ' + c + '=fml.Formula.' + c)
    })
    var it = b.join(';') + ';'
    var c = new Function(it + 'return ' + a)()
    return c
  },
  createEntryAttributeField: function (b, c) {
    var d = {
      id: c.entryId
    }
    switch (b) {
      case 'ext':
        a.extend(d, {
          name: 'ext',
          type: 'text',
          text: '扩展字段',
          items: c.extParams
        })
        break
      case 'createTime':
        a.extend(d, {
          name: 'createTime',
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          text: '提交时间'
        })
        break
      case 'updateTime':
        a.extend(d, {
          name: 'updateTime',
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          text: '更新时间'
        })
        break
      case 'creator':
        a.extend(d, {
          name: 'creator',
          type: 'text',
          text: '提交人'
        })
        break
      case 'flowState':
        a.extend(d, {
          name: 'flowState',
          type: 'flowState',
          text: '流程状态'
        })
        break
      case 'chargers':
        a.extend(d, {
          name: 'chargers',
          type: 'chargers',
          text: '当前节点/负责人'
        })
        break
      case 'deleter':
        a.extend(d, {
          name: 'deleter',
          type: 'text',
          text: '删除人'
        })
        break
      case 'deleteTime':
        a.extend(d, {
          name: 'deleteTime',
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm:ss',
          text: '删除时间'
        })
        break
      default:
        return null
    }
    return d
  },
  createWidgetName: function () {
    return '_widget_' + fml.STATIC.IDBase++
  },
  formatFileSize: function (a) {
    return Utils.isNumber(a)
      ? a >= 1e9
        ? (a / 1e9).toFixed(2) + ' GB'
        : a >= 1e6
        ? (a / 1e6).toFixed(2) + ' MB'
        : (a / 1e3).toFixed(2) + ' KB'
      : 'æœªçŸ¥'
  },
  chunkArray: function (a, b) {
    var c = []
    if (!b || !a.length) {
      return c
    }
    for (var d = 0, e = a.length; d < e; d += b) {
      var f = a.slice(d, d + b)
      c.push(f)
    }
    return c
  },
  UUID: function (a) {
    return a
      ? (a ^ ((16 * Math.random()) >> (a / 4))).toString(16)
      : ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, this.UUID)
  },
  GCD: function (a, b) {
    return b ? this.GCD(b, a % b) : a
  },
  LCM: function (a, b) {
    return (a * b) / this.GCD(a, b)
  },
  fixDecimalPrecision: function (a, b) {
    var c = ''
    if ((b || (b = 8), !this.isEmpty(a))) {
      var d = parseFloat(a)
      if (!isNaN(d)) {
        var e = (d + '').split('.')[1]
        ;(c = e && e.length > b ? parseFloat(d.toFixed(b)) : d),
          b > 6 && Math.abs(c) < 1 && /e-/.test(c + '') && (c = parseFloat(d.toFixed(6)))
      }
    }
    return c
  },
  getSelectionText: function () {
    return window.getSelection
      ? window.getSelection().toString()
      : document.selection && document.selection.createRange
      ? document.selection.createRange().text
      : ''
  },
  getCorpType: function (a) {
    return a
      ? ((a = a.toLowerCase()), /^ding/.test(a) ? 'dingtalk' : /^w/.test(a) ? 'wechat' : 'internal')
      : null
  },
  isCorpSuiteAdmin: function (a) {
    return a && (a === 'dingtalk' || a === 'wechat')
  },
  getWeekStartDate: function (a) {
    var b = a.getDay()
    return b === 0 && (b = 7), new Date(a.getFullYear(), a.getMonth(), a.getDate() - (b - 1))
  },
  getWeekEndDate: function (a) {
    var b = a.getDay()
    return b === 0 && (b = 7), new Date(a.getFullYear(), a.getMonth(), a.getDate() + (7 - b))
  },
  getMonthStartDate: function (a) {
    return new Date(a.getFullYear(), a.getMonth(), 1)
  },
  getMonthEndDate: function (a) {
    return new Date(a.getFullYear(), a.getMonth() + 1, 0)
  },
  setPageTitle: function (a) {
    Utils.isEmpty(a) || (document.title = a)
  },
  createMask: function (b, c) {
    var d = a('<div class="x-window-mask"/>'),
      e = c || {}
    if (
      (e.isModal && d.addClass('modal'),
      e.isLight ? d.addClass('light') : e.isDark && d.addClass('dark'),
      e.hasLoader)
    ) {
      var f = !e.isDark
      this.createLoadIcon(d, f)
    }
    return (
      b &&
        d
          .css({
            'z-index': fml.STATIC.zIndex++
          })
          .appendTo(b),
      d
    )
  },
  createLoadIcon: function (b, c) {
    var d = a('<div class="x-loader-icon"/>').appendTo(b)
    return c && d.addClass('colorful'), a('<div/>').appendTo(d), d
  },
  doPrint: function (b, c) {
    a('body').children('div').addClass('x-ui-notprint')
    var d = a('#x-printer').removeClass().empty()
    d.length === 0 && (d = a('<div id="x-printer"/>').appendTo('body')),
      b && d.append(b),
      (c = xbb.isNull(c) ? 0 : c),
      setTimeout(function () {
        window.print()
      }, c)
  },
  cancelPrint: function () {
    a('body').children('.x-ui-notprint').removeClass('x-ui-notprint'), a('#x-printer').remove()
  },
  copyToClipboard: function (a, b) {
    if (a && a.length) {
      var c = document.createElement('textarea')
      ;(c.style.background = 'transparent'),
        (c.style.color = 'transparent'),
        (c.value = a),
        document.body.appendChild(c),
        c.select()
      var d
      try {
        d = document.execCommand('copy')
      } catch (a) {
        d = !1
      }
      document.body.removeChild(c), d && Utils.applyFunc(this, b, [], !1)
    }
  },
  getColorNumber: function (a) {
    return Utils.isEmpty(a) ? 1 : (parseInt(a.toString().substring(0, 8), 16) % 6) + 1
  },
  getFieldAttr: function (b, c) {
    if (!Utils.isValueWidget(b.widget.type)) {
      return null
    }
    if (c && c.indexOf(b.widget.type) < 0) {
      return null
    }
    var d = {
      id: b.formId,
      text: b.label,
      name: b.widget.widgetName,
      type: b.widget.type
    }
    switch (b.widget.type) {
      case 'subform':
        var e = []
        Utils.forEach(b.widget.items, function (d, f) {
          a.extend(f, {
            formId: b.formId
          })
          var g = Utils.getFieldAttr(f, c)
          g &&
            (a.extend(g, {
              subform: b.widget.widgetName
            }),
            e.push(g))
        }),
          a.extend(d, {
            items: e
          })
        break
      case 'linkdata':
        a.extend(d, {
          linkForm: b.widget.linkForm,
          linkFields: b.widget.linkFields,
          refAppId: b.widget.refAppId
        })
        break
      case 'combo':
      case 'combocheck':
      case 'radiogroup':
      case 'checkboxgroup':
        a.extend(d, {
          async: b.widget.async,
          items: b.widget.items
        })
        break
      case 'datetime':
        a.extend(d, {
          format: b.widget.format
        })
    }
    return d
  },
  leftPad: function (a, b, c) {
    var d = String(a)
    for (c || (c = ' '); d.length < b; ) {
      d = c + d
    }
    return d.toString()
  },
  startWith: function (a, b) {
    var c = a.length
    return !(b == null || b == '' || c === 0 || b.length > c) && a.substr(0, b.length) == b
  },
  getFieldInfoByFormula: function (a) {
    var b = {},
      c = a + '',
      d = c.match(/(\$[0-9a-zA-Z\._]+)(#[0-9a-f]+)?(@[0-9a-f]+)?/),
      e = ['', 'field', 'entryId', 'appId']
    return (
      Utils.forEach(d, function (a, c) {
        a !== 0 && c && (b[e[a]] = c.substr(1))
      }),
      b
    )
  },
  getFieldInfoById: function (a) {
    var b = {}
    a = '#' + a
    var c = a.match(/(#[0-9a-f]+)(@[0-9a-f]+)?/),
      d = ['', 'entryId', 'appId']
    return (
      Utils.forEach(c, function (a, c) {
        a !== 0 && c && (b[d[a]] = c.substr(1))
      }),
      b
    )
  },
  isWpsWebView: function () {
    return /wpscloudform/.test(navigator.userAgent)
  },
  callWPSAPI: function (a) {
    var b = 'jsAsynCall(' + JSON.stringify(a) + ')'
    window.cefQuery &&
      window.cefQuery({
        request: b
      })
  },
  onWPSPageUnload: function (a, b) {
    a
      ? fml.Msg.alert({
          type: 'warning',
          msg: 'å½“å‰é¡µé¢æœªä¿å­˜ï¼Œæ˜¯å¦ç¡®å®šç¦»å¼€ï¼Ÿ',
          text4Ok: 'ç¦»å¼€',
          text4Cancel: 'å–æ¶ˆ',
          onOk: function () {
            Utils.applyFunc(this, b, [], !1)
          }
        })
      : Utils.applyFunc(this, b, [], !1)
  },
  fileDownload: function (a, b) {
    if (Utils.isWpsWebView()) {
      var c = {
          method: 'downloadUrl',
          url: a,
          filename: b
        },
        d = b && b.split('.').pop()
      ;(c.filter = '(*.' + (d || '*') + ')'), Utils.callWPSAPI(c)
    } else {
      Utils.redirectTo(a)
    }
  },
  isSupportPdf: function () {
    return typeof navigator.mimeTypes['application/pdf'] !== 'undefined'
  },
  dt: function (a, b) {
    if (a) {
      var c = ['e=' + a, 't=' + new Date().getTime()]
      Utils.isEmpty(b) || c.push('ext=' + b),
        fml.STATIC.user && fml.STATIC.user.username && c.push('u=' + fml.STATIC.user.username)
      var d = new Image()
      d.src = fml.CONFIG.HOST.TRACK_HOST + '/log?' + c.join('&')
    }
  }
}
export default Utils
