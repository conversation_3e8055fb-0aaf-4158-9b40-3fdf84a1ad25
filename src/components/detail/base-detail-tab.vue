<!--
 * @Description:  详情-基本信息组件
 -->
<template>
  <div class="base-detail-tab">
    <div class="content">
      <form-data ref="formDataForm" :field-list="headList" :form-data="formData"> </form-data>
    </div>
  </div>
</template>

<script>
import { detailTabBaseInfo } from '@/api/detailTab'
import formData from '../form-data-edit/form-data-form'

export default {
  name: 'BaseDetailTab',

  components: {
    FormData: formData
  },

  data() {
    return {
      headList: [],
      formData: {}
    }
  },

  mounted() {
    this.getBaseInfo()
  },

  methods: {
    // 获取详情
    getBaseInfo() {
      const request = {}
      detailTabBaseInfo(request).then((res) => {
        this.headList = res.result.headList
        this.formData = res.result.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.base-detail-tab {
  .content {
    height: 90%;
    overflow: auto;
    clear: both;
    .item {
      display: table;
      padding-top: 6px;
      .label,
      .text {
        font-size: 13px;
        line-height: 20px;
      }
      .label {
        display: inline-block;
        width: 84px;
        overflow: hidden;
        color: $text-plain;
        text-align: right;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.rate {
          text-align: left;
        }
      }
      .text {
        display: table-cell;
        padding-left: 10px;
        word-break: break-all;
        vertical-align: top;
      }
      .tag {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-left: 20px;
        font-size: 12px;
        line-height: 20px;
        color: $base-white;
        text-align: center;
        border-radius: 2px;
        transform: scale(0.8);
        &.high {
          background-color: $error-base-color-6;
        }
        &.mid {
          background-color: $brand-color-5;
        }
        &.low {
          background-color: #78c06e;
        }
      }
      .el-rate {
        display: inline-block;
        padding-top: 2px;
        vertical-align: top;
        .el-rate__icon {
          font-size: 16px;
        }
      }
      .img-box {
        display: table-cell;
        padding-left: 10px;
        word-break: break-all;
        vertical-align: top;
        .img {
          margin-right: 6px;
        }
        .file {
          line-height: 20px;
        }
      }
      .phone-arr {
        display: table-cell;
        word-break: break-all;
        vertical-align: top;
        .text {
          display: block;
        }
      }
      :deep(.el-icon-question) {
        position: absolute;
        right: 6px;
      }
      .costRed {
        color: $error-color-5;
      }
      .file-info {
        padding: 0;
        margin: 0;
        border-top: 0;
        .label {
          display: none;
        }
      }
    }
  }
}
</style>
