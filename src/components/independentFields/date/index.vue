<!--
 * @Description: 时间筛选的控件
 -->
<!--
组件关联页面：日期
-->
<template>
  <div class="date-range">
    <el-date-picker
      v-model="modelVal"
      align="right"
      clearable
      :end-placeholder="$t('label.endTime')"
      :picker-options="pickerOptions"
      size="small"
      :start-placeholder="$t('label.startTime')"
      style="padding: 0 10px"
      type="daterange"
      unlink-panels
      value-format="yyyy-MM-dd"
      @change="changValue"
    >
    </el-date-picker>
  </div>
</template>

<script>
import fieldMixin from '../field-mixin.js'

export default {
  name: 'DateRange',

  mixins: [fieldMixin],

  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: this.$t('label.latestWeek'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('label.latestMonth'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('label.latestQuarter'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.date-range {
  width: 250px;
  .el-date-editor {
    width: 100%;
  }
}
</style>
