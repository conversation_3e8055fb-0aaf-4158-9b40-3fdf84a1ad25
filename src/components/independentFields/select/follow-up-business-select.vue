<!--
 * @Description: 单独用来渲染跟进记录表单-跟进业务 筛选
 -->
<template>
  <div class="follow-up-business-select range-select" :class="{ 'is-fixed': isFixed }">
    <el-autocomplete
      v-model="modelVal.text"
      clearable
      :disabled="!modelVal.attr"
      :fetch-suggestions="querySearchAsync"
      :placeholder="$t('placeholder.selectOrSearch')"
      size="small"
      value-key="text"
      @clear="clearSelect(0)"
      @select="handleSelect"
    >
      <el-select
        slot="prepend"
        v-model="modelVal.attr"
        class="range-select__type range-select_select"
        clearable
        placeholder="跟进类型"
        @change="selectChange"
        @clear="clearSelect(1)"
      >
        <el-option
          v-for="item in businessTypeList"
          :key="item.text"
          :label="item.text"
          :value="item.attr"
        >
        </el-option>
      </el-select>
    </el-autocomplete>
  </div>
</template>

<script>
import fieldMixin from '../field-mixin.js'
import xbb from '@xbb/xbb-utils'
import { mapGetters } from 'vuex'
import businessEnum from '@/constants/common/business-type.js'

export default {
  name: 'FollowUpBusinessSelect',

  mixins: [fieldMixin],

  props: {
    specialClearFlag: {
      type: Boolean,
      default: false
    },
    isFixed: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      modelVal: {
        attr: '',
        type: '',
        value: '',
        text: ''
      }
    }
  },

  methods: {
    @xbb.debounceWrap()
    querySearchAsync(queryString, cb) {
      const communicateBusiness = this.businessTypeList.find(
        (item) => item.attr === this.modelVal.attr
      )?.value
      this.getFilterConditions(
        queryString,
        {
          attr: 'text_5',
          fieldType: 20001, //后端固定要求的20001
          communicateBusiness // 本次功能新加的参数
        },
        true
      ).then(({ result: { items = [] } }) => {
        cb(items)
      })
    },
    handleSelect(item) {
      this.modelVal.value = item.value
      const params = Object.assign({}, this.modelVal)
      this.$emit('valueChange', params)
    },
    selectChange(val) {
      this.modelVal.text = ''
      if (!val) {
        this.modelVal.value = ''
      }
      this.changValue(this.modelVal)
    },
    clearSelect(type) {
      // type-- 1左侧清空   0右侧清空
      if (type) {
        this.resetFilterValues()
      } else {
        this.modelVal = {
          ...this.modelVal,
          value: '',
          text: ''
        }
      }
      const params = Object.assign({}, this.modelVal)
      this.$emit('valueChange', params)
    },
    resetFilterValues() {
      this.modelVal = {
        attr: '',
        type: '',
        value: '',
        text: ''
      }
    }
  },
  computed: {
    ...mapGetters({
      appInfo: 'proList/getAppInfo' // 获取appInfo
    }),
    businessTypeList() {
      return this.appInfo.businessType === businessEnum.SUPPLIER_CUSTOMER_COMMUNICATE
        ? [
            {
              text: this.$t('business.procurementContract'),
              attr: 'purchase',
              value: businessEnum.PURCHASE
            }
          ]
        : [
            {
              text: this.$t('marketManage.salesLeads'),
              attr: 'clue',
              value: businessEnum.SALES_LEADS
            },
            {
              text: this.$t('business.salesOpportunities'),
              attr: 'salesOpportunity',
              value: businessEnum.SALES_OPPORTUNITY
            },
            {
              text: this.$t('business.contractOrder'),
              attr: 'contract',
              value: businessEnum.CONTRACT
            },
            {
              text: this.$t('constants.common.quotation'),
              attr: 'quotation',
              value: businessEnum.QUOTATION
            }
          ]
    }
  },

  watch: {
    specialClearFlag(val) {
      this.resetFilterValues()
    }
  }
}
</script>

<style lang="scss" scoped>
.range-select {
  width: 285px;
  &_select {
    width: 100px !important;
    :deep(.el-input > input) {
      cursor: pointer !important;
    }
    :deep(.el-input-group__prepend) {
      .el-select {
        margin-left: 10px;
      }
    }
  }
}
.is-fixed {
  width: 100%;
  :deep(.el-input--suffix) {
    .el-input-group__prepend + input {
      width: 228px !important;
      margin-left: 10px !important;
      border-radius: 3px !important;
    }
  }
}
</style>
