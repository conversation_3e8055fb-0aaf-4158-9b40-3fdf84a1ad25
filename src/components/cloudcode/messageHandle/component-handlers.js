/*
 * @Description:
 */
// 使云叩调用pro组件
import store from '@/store'
import PersonSelectPromise from '@/components/person-select/index.js'
import CustomDrawerPromise from '@/components/cloudcode/components/drawer/custom-drawer-promise.js'
import { getDetailLinkAdd } from '@/api/form-detail-tabs'
import { getLinkAddVal, setFormatData } from '@/components/form-data-edit/utils'
import dialog from '../dialog'

const componentHandler = {
  openDetailPage(data) {
    // 打开详情页
    store.commit('SET_DETAIL_DIALOG_SHOW', true)
    store.commit('SET_DETAIL_QUERY', {
      appId: data.appId,
      dataId: +data.dataId, // 存在父产品id的时候，要和父产品比较
      saasMark: data.saasMark,
      businessType: +data.businessType,
      subBusinessType: +data.subBusinessType
    })
    return { data: {} }
  },
  addNewForm(data) {
    // 打开新建表单页面
    store.dispatch('formDataAdd', {
      appId: data.appId,
      menuId: data.menuId,
      formId: data.formId,
      saasMark: data.saasMark,
      businessType: +data.businessType,
      subBusinessType: +data.subBusinessType,
      cloudcodeData: data.formData
    })
    return { data: {} }
  },
  async personSelect(options) {
    const data = await PersonSelectPromise(options)
    return data
  },
  showMessage(options) {
    this['show-toast'](options)
    return { data: {} }
  },
  async openDrawer(options) {
    await CustomDrawerPromise(options)
    return {}
  },
  // 新建跟进提醒
  setFlowUpDialog(options) {
    getDetailLinkAdd(options).then(
      ({
        result,
        result: {
          explainList,
          serialNo,
          groupNumber,
          paasFormEntity: {
            appId,
            menuId,
            id,
            saasMark,
            businessType,
            name,
            fieldPosition = 'left'
          },
          saasObj,
          writeOffAmount,
          lowCodeConfig
        }
      }) => {
        store.commit('SET_MODE', 'add') // 编辑跟进提醒时，设置为编辑模式
        store.commit('SET_USE_DRAFT', false)
        store.commit('SET_GROUP_NO', groupNumber)
        store.commit('SET_ADD_TYPE', 'batchPushNotify') // 设置关联新建的类型  新建回款单的时候 走不同的保存接口
        store.commit('SET_LINK_ADD_DATA', options.dataIdList) // 设置关联新建的数据
        store.commit('SET_FORM_SAASOBJ', saasObj)
        store.commit('SET_FORM_DATA_ID', null)
        store.commit('SET_FIELD_POSITION', fieldPosition)
        const linkAddData = getLinkAddVal(explainList)
        const formatData = setFormatData(linkAddData, explainList)
        store.commit('SET_FORM_DATA', formatData)
        store.commit('SET_MATE_DATA', result.data)
        store.commit('SET_DIALOG_FORM_NAME', name)
        store.commit('SET_IS_MERGE_INVOICE', saasObj ? saasObj.isMergeInvoice : 0)
        const params = {
          appId,
          menuId,
          formId: id, // 工作报告是formId为0的，需要那get接口拿到的id
          saasMark,
          businessType,
          dataIdList: options.dataIdList
        }
        store.commit('SET_FORM_QUERY', params)
        store.commit('SET_SAAS_SPECIA_PARAM_POJO', {})
        store.commit('SET_SERIAL_NO', serialNo)
        store.commit('SET_EXPKAIN_LIST', explainList)
        store.commit('SET_DIALOG_SHOW', true)
        store.commit('SET_WRITE_OFF_AMOUNT', writeOffAmount || 0)
      }
    )
    console.log(options)
  },
  // 打开云叩弹框容器
  async openCloudCodeDialog(options) {
    const data = await dialog(options)
    return { data }
  }
}

export default componentHandler
