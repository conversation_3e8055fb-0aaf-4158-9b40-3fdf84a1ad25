<template>
  <div class="card-body">
    <div class="card-content">
      <div class="card-head">
        <Avatar class="avatar" radius :size="40" :src="content.avatar" />
        <div class="card-head__item userName">{{ content.userName }}</div>
        <v-tag :color="content.color" :content="showStatus(content.status)" />
      </div>
      <div class="card-info">
        <div>
          <span class="card-info__title">{{ $t('workOrderV2.workOrderNode') }}：</span
          ><span class="card-info__text">{{ content.nodeName }}</span>
        </div>
        <div>
          <span class="card-info__title">{{ $t('workOrderV2.serviceTime') }}：</span
          ><span class="card-info__text">{{ content.serviceTime || '--' }}</span>
        </div>
        <div class="card-info__address">
          <i class="icon-map-pin-2-fill t-iconfont"></i>
          {{ getAddress }}
        </div>
      </div>
    </div>
    <div class="card-detail">
      <span class="card-detail__text" @click="openSignInDetail">
        {{ $t('operation.detail') }}<i class="icon-arrow-right-s-line t-iconfont"></i
      ></span>
    </div>
  </div>
</template>

<script>
import Avatar from '@/components/base/avatar-img.vue'
import VTag from '@/components/base/v-tag.vue'

export default {
  name: 'WordOrderSignInCard',

  components: {
    Avatar,
    VTag
  },
  inject: {
    parentDetail: {
      default: () => null
    }
  },

  props: {
    content: {
      type: Object,
      default: () => ({})
    },
    query: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {}
  },

  computed: {
    getAddress() {
      const province = this.getDistrict('province')
      const city = this.getDistrict('city')
      const district = this.getDistrict('district')
      const address = this.getDistrict('address')
      const addressName = `${province}${city}${district}${address}`
      return addressName
    }
  },

  methods: {
    showStatus(val) {
      // 1 服务中 , 2 已完成 ，3 已中止
      let statusName
      switch (val) {
        case 1:
          statusName = '服务中'
          break
        case 2:
          statusName = '已完成'
          break
        case 3:
          statusName = '已中止'
          break
      }
      return statusName
    },
    openSignInDetail() {
      this.handleGoToDetail()
    },
    handleGoToDetail() {
      if (this.parentDetail) {
        const data = {
          businessType: 20300,
          dataId: this.query.dataId,
          formId: this.query.dataId,
          signInId: this.content.id,
          saasMark: 1,
          isWorkOrderSignInDetail: true
        }
        this.parentDetail.showChildrenDetail(data)
      }
    },
    getDistrict(val) {
      return this.content[val]
    }
  }
}
</script>

<style lang="scss" scoped>
.card-body {
  border: 1px solid $neutral-color-2;
  border-radius: 8px;
  .card-content {
    padding: 16px;
    .card-head {
      display: flex;
      align-items: center;
      .card-head__item {
        margin-left: 12px;
      }
      .userName {
        font-size: 14px;
        font-weight: 600;
        color: $text-plain;
      }
    }
    .card-info {
      margin-top: 12px;
      font-size: 13px;
      & div:not(:first-child) {
        margin-top: 8px;
      }
      .card-info__title {
        color: $text-auxiliary;
      }
      .card-info__address {
        .icon-map-pin-2-fill {
          color: $link-color-5;
        }
      }
    }
  }
  .card-detail {
    padding: 7px 16px;
    font-size: 13px;
    color: $text-auxiliary;
    border-top: 1px solid $neutral-color-2;
    .card-detail__text {
      cursor: pointer;
    }
  }
}
</style>
