<!--
 * @Description: 筛选条件列表封装入口
 -->
<template>
  <div class="filter-condition-list">
    <VueDraggable
      v-model="conditionListCopy"
      v-draggable
      handle=".drag-handle-sub"
      v-bind="{ ghostClass: 'ghost-class' }"
      @change="conditionChange"
    >
      <template v-for="(condition, index) in conditionListCopy">
        <div
          :key="condition.sepcialKey"
          class="filter-condition-list__content"
          :class="{ 'filter-condition-list__content--restrict': restrictWidth }"
        >
          <filter-condition
            v-if="!useSymbol || (condition.whereList && condition.whereList.length > 0)"
            :chart-explain="chartExplain"
            :condition="condition"
            :condition-index="index"
            :condition-length="conditionListCopy.length"
            :default-show="defaultShow"
            :high-search="highSearch"
            :hover-bg="hoverBg"
            :is-edit="isEdit"
            :is-not-panel="isNotPanel"
            :is-show-dashed="showDashedLine"
            :label-border="labelBorder"
            :layout="layout"
            :position="'special'"
            :restrict-width="restrictWidth"
            :show="show"
            :show-date-type="showDateType"
            :show-default-label="showDefaultLabel"
            :show-type="showType"
            :specification-keys="specificationKeys"
            :table-type="tableType"
            :use-delete="useDelete"
            :use-symbol="useSymbol"
            @conditionChange="conditionChange"
            @deleteCondition="deleteCondition(index)"
            @mousedown.native="formatItems(condition, index)"
            @set-condition="
              (condition) => {
                setCondition(index, condition)
              }
            "
          ></filter-condition>

          <p v-if="condition.errorText" class="error-tip">{{ condition.errorText }}</p>
        </div>
      </template>
    </VueDraggable>
  </div>
</template>

<script>
import { getConditionList } from '@/api/statistics'
import FilterCondition from './filter-condition.vue'
import {
  getBoardConditionList,
  getConnectConditionList,
  getDataSetConditionList
} from '@/api/workflow-design'
// import { getFilterConditions } from '@/api/list-filter'
import { multipleCondition, vagueSearchFields } from './filter.js'
import { fieldsMap } from '@/constants/common/fieldsMap.js'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'FilterConditionList',
  components: {
    FilterCondition
  },

  props: {
    condition: {
      type: Object,
      default: () => ({})
    },
    conditionList: {
      type: Array
    },
    // 是否显示symbol条件，不显示时，选择框默认有类型
    useSymbol: {
      type: Boolean,
      default: true
    },
    // 是否显示删除图标
    useDelete: {
      type: Boolean,
      default: true
    },
    // 排版布局，transverse横向；vertical竖向的；
    layout: {
      type: String,
      default: 'transverse'
    },
    // symbol是否带边框
    symbolBorder: {
      type: Boolean,
      default: true
    },
    // labelBorder是否带边框
    labelBorder: {
      type: Boolean,
      default: false
    },
    // 每一项hover时的背景色
    hoverBg: {
      type: Boolean,
      default: false
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: true
    },
    // 默认显示的字段属性名称
    defaultShow: {
      type: Object,
      default: () => ({
        label: 'label'
      })
    },
    // 外部包裹是否限制了宽度
    restrictWidth: {
      type: Boolean,
      default: true
    },
    highSearch: {
      type: Boolean,
      default: false
    },
    tableType: {
      type: Boolean,
      default: false
    },
    isNotPanel: {
      type: Boolean,
      default: true
    },
    // 是否在删除icon的右侧显示虚线样式
    showDashedLine: {
      type: Boolean,
      default: false
    },
    // 是否是图表中心,只有BI那边的时间筛选才有自定义动态和常用两个选项
    forChart: {
      type: Number,
      default: 1
    },
    showDateType: {
      type: Boolean
    },
    showType: {
      type: Boolean
    },
    show: {
      type: Boolean
    },
    showDefaultLabel: {
      type: Boolean,
      default: true
    },
    chartExplain: {},
    // 工单版本id
    workOrderVersionId: {
      type: [Number, String],
      default: ''
    },
    // 是否是资产管理
    isAsset: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: 'left'
    },
    // 产品规格字段的分组模式， 0 = 单规格， 1=多规格
    spType: {
      type: Number,
      default: 0
    },
    useCustomConditionListObj: {
      type: Boolean,
      default: false
    },
    customConditionListObj: {
      type: Object,
      default: () => ({})
    },
    specificationKeys: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      conditionListObject: [],
      vagueSearchFields: vagueSearchFields,
      multipleCondition: multipleCondition,
      fieldsMap: fieldsMap,
      conditionListCopy: [],
      flag: true
    }
  },

  watch: {
    conditionList: {
      handler(val, oldVal) {
        console.log('conditionList3', this.conditionList)
        this.conditionListCopy = xbb.deepClone(this.conditionList)
        console.log('conditionList2', this.conditionListCopy)
        this.conditionListCopy = this.conditionListCopy.map((item) => {
          return {
            sepcialKey: Symbol(),
            ...item
          }
        })
        console.log('conditionList', this.conditionListCopy)
        // 新增一个条件时进行处理，删除一个条件时不进行处理
        // if (oldVal === undefined || val > oldVal) {
        if (this.useSymbol) {
          if (JSON.stringify(this.conditionListObject).length > 2) {
            // 判断this.conditionListArray里有没有值
            this.formatCondition()
          } else if (this.useCustomConditionListObj) {
            this.getConditionListByCustom('format')
          } else {
            this.getConditionList('format')
          }
        } else {
          this.conditionListCopy.forEach((item) => {
            this.$set(
              item,
              'multiple',
              this.fieldsMap[item.fieldType].filterSelectType === 'multiple'
            )
            this.$set(item, 'symbolEmpty', false)
          })
        }
        // this.formatItems()
        this.conditionAddValue()
        // }
      },
      immediate: true
    }
  },
  mounted() {
    console.log('conditionList created', this.conditionList)
  },
  methods: {
    // 不可预料conditionList的watcher改成deep后的影响
    // 只能写一个辅助函数来兼容想显示错误信息的需求
    setErrorText(index, errorText) {
      this.$set(this.conditionListCopy[index], 'errorText', errorText)
    },
    setCondition(index, condition) {
      this.conditionListCopy[index] = condition
      this.$set(this.conditionList, index, condition)
    },
    conditionChange() {
      console.log('conditionChange', this.conditionList)
      this.$emit('update:conditionList', xbb.deepClone(this.conditionListCopy))
      this.$emit('conditionListChange', xbb.deepClone(this.conditionListCopy))
    },

    // 使用自定义的symbol 下拉列表
    getConditionListByCustom(type) {
      this.$nextTick(() => {
        this.conditionListObject = this.customConditionListObj
        if (type === 'format') {
          this.formatCondition()
        }
      })
    },

    // 获取symbol下拉列表
    getConditionList(type) {
      getConditionList({
        forChart: Number(this.forChart),
        spType: this.spType
      })
        .then((res) => {
          this.conditionListObject = res.result.conditionList
          if (type === 'format') {
            this.formatCondition()
          }
        })
        .catch(() => {})
    },
    // 为选中的筛选条件添加symbol对应的选项
    formatCondition() {
      this.conditionListCopy.forEach((item) => {
        if (item.whereList === undefined) {
          if (item.fieldType === 20) {
            this.$set(
              item,
              'whereList',
              this.conditionListObject[item.formulaInfo.computeReturnType || 2]
            )
          } else {
            this.$set(item, 'whereList', this.conditionListObject[item.fieldType])
          }
        }

        if (item.symbol === undefined) {
          if (item.fieldType === 20) {
            this.$set(
              item,
              'symbol',
              this.conditionListObject[item.formulaInfo.computeReturnType || 2][0].symbol
            )
          } else {
            this.$set(item, 'symbol', this.conditionListObject[item.fieldType][0].symbol)
          }
        }
        if (multipleCondition.includes(item.symbol)) {
          this.$set(item, 'multiple', true)
        } else {
          this.$set(item, 'multiple', false)
        }
      })
    },
    // 为选中的筛选条件（可模糊搜索的）添加下拉选项
    formatItems(item, num) {
      if (this.vagueSearchFields.includes(item.fieldType)) {
        this.getConnectConditionFieldVal(item)
          .then((data) => {
            if (item.valueItems && item.valueItems.length !== 0) {
              const itemData = data.map((dataSub) => {
                return dataSub.value
              })
              if (
                item.valueItems.some((sub) => {
                  return !itemData.includes(sub.value)
                })
              ) {
                this.$set(item, 'items', item.valueItems)
              } else {
                this.$set(item, 'items', data)
              }
            } else {
              this.$set(item, 'items', data)
            }
            this.$set(item, 'valueItems', data)
          })
          .catch(() => {})
      }
      // this.conditionListCopy.forEach((item, index) => {
      // })
    },
    // 为condition添加value
    conditionAddValue() {
      this.conditionListCopy.forEach((item) => {
        if (item.value === undefined) {
          this.$set(item, 'value', [])
        }
      })
    },
    // 删除一项condition
    deleteCondition(index) {
      this.conditionListCopy.splice(index, 1)
      this.$emit('update:conditionList', xbb.deepClone(this.conditionListCopy))
      // this.$emit('deleteCondition', index)
    },
    // 获取一些字段的搜索列表
    getConnectConditionFieldVal(fields) {
      const params = xbb.deepClone(fields)
      this.$set(params, 'subBusinessType', fields.businessType)
      this.$set(params, 'value', '')
      // 如果存在originalAttr，则attr也使用originalAttr来请求
      if (this.chartExplain && Number(this.chartExplain.single) === 0 && params.originalAttr) {
        this.$set(params, 'attr', fields.originalAttr)
      }
      return new Promise((resolve, reject) => {
        let getConditionListFn = getConnectConditionList
        // ??如果是数据集作为数据源，则接口的调用使用getDataSetConditionList
        if (this.chartExplain && Number(this.chartExplain.single) === 2) {
          getConditionListFn = getDataSetConditionList
          // 如果是数据集作为数据源 要在入参数添加dataSetId
          params.dataSetId =
            this.chartExplain.dataSetId !== undefined
              ? this.chartExplain.dataSetId
              : this.chartExplain.driverSources.dataSetId || this.chartExplain.driverSources.id
        }
        // ?? 如果当前处于数据集的画布编辑中（正在进行过滤条件的配置时），接口的调用使用getBoardConditionList
        if (this.chartExplain && this.$route.name === 'dataSetEdit') {
          if (!this.chartExplain.beforeNodesPass) {
            reject()
            return
          }
          getConditionListFn = getBoardConditionList
          params.inputId = this.chartExplain.belongNodeId
          params.jsonStr = this.chartExplain.beforeNodes
        }
        if (this.workOrderVersionId) {
          Object.assign(params, {
            specialField: this.workOrderVersionId
          })
        }
        // !! getConditionListFn 是动态判断，变化的
        getConditionListFn(params)
          .then((res) => {
            resolve(res.result.items)
          })
          .catch(() => {
            reject()
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-condition-list {
  &__content {
    display: inline-block;
    margin-bottom: 15px;
    // padding: 0 10px;
    &--restrict {
      width: 100%;
      padding: 0px;
    }
  }
}

.ghost-class {
  padding: 5px 0px;
  border: 1px dashed $line-filter;
  opacity: 1;
}

.error-tip {
  margin-top: 4px;
  font-size: 14px;
  line-height: 22px;
  color: $error-base-color-6;
}
</style>
