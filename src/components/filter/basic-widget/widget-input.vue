<!--
 * @Description: 输入框小部件
 -->
<template>
  <div class="widget-input">
    <!-- 单选 -->
    <template v-if="!multiple">
      <el-input
        v-if="!numFields.includes(condition.fieldType)"
        v-model="inputValue"
        class="widget-input--single"
        clearable
        :disabled="!isEdit"
        :placeholder="$t('placeholder.contentPls')"
      ></el-input>
      <el-input
        v-else
        v-model="inputValue"
        class="widget-input--single"
        clearable
        :controls="false"
        :disabled="!isEdit"
        :max="
          condition.numericalLimitsFlag &&
          condition.numericalLimits &&
          condition.numericalLimits.max
            ? condition.numericalLimits.max
            : 2147483647
        "
        :min="
          condition.numericalLimitsFlag &&
          condition.numericalLimits &&
          condition.numericalLimits.min
            ? condition.numericalLimits.min
            : -2147483647
        "
        :placeholder="$t('placeholder.inputPls', { attr: $t('nouns.number') })"
        :precision="condition.accuracy >= 0 ? condition.accuracy : undefined"
        type="number"
        @change.native="modelValChange()"
      ></el-input>

      <!-- <el-input :disabled="!isEdit"
        class="widget-input--single"
        v-model="inputValue"
        :placeholder="$t('placeholder.contentPls')" maxlength="8"
        @change.native="inputChange"></el-input> -->
    </template>
    <!-- 多选 -->
    <template v-else>
      <!-- 非数字 -->
      <div v-if="!numFields.includes(condition.fieldType)" class="widget-input--multiple">
        <div
          v-if="
            condition.fieldType === 1 ||
            condition.fieldType === 900030 ||
            condition.fieldType === 900031 ||
            condition.fieldType === 20031 ||
            condition.fieldType === 60
          "
          class="input-multiple-box"
        >
          <el-tag
            v-for="(tag, index) in inputTagList"
            :key="index"
            closable
            type="info"
            @close="handleClose(index)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-model="inputContent"
            class="widget-input__text"
            placeholder="请输入筛选条件"
            @blur="handleInputConfim"
            @keyup.enter.native="handleInputConfim"
          >
          </el-input>
        </div>
        <div v-else>
          <el-input
            v-model="inputValue[0]"
            class="widget-input__text"
            :disabled="!isEdit"
            :placeholder="$t('nouns.minNum')"
            @change.native="modelValChange()"
          ></el-input>
          <div class="widget-input__to">～</div>
          <el-input
            v-model="inputValue[1]"
            class="widget-input__text"
            :disabled="!isEdit"
            :placeholder="$t('nouns.maxNum')"
            @change.native="modelValChange()"
          ></el-input>
        </div>
      </div>
      <!-- 只允许数字 -->
      <div v-else class="widget-input--multiple">
        <el-input
          v-model="inputValue[0]"
          class="widget-input__text"
          :controls="false"
          :disabled="!isEdit"
          :placeholder="$t('nouns.minNum')"
          :precision="condition.accuracy >= 0 ? condition.accuracy : undefined"
          type="number"
          @change.native="modelValChange('min')"
        ></el-input>
        <div class="widget-input__to">～</div>
        <el-input
          v-model="inputValue[1]"
          class="widget-input__text"
          :controls="false"
          :disabled="!isEdit"
          :placeholder="$t('nouns.maxNum')"
          :precision="condition.accuracy >= 0 ? condition.accuracy : undefined"
          type="number"
          @change.native="modelValChange('max')"
        ></el-input>
      </div>
    </template>
  </div>
</template>

<script>
import { numFields } from './../filter.js'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'WidgetInput',

  props: {
    // 是否为两个输入框
    multiple: {
      type: Boolean,
      default: false
    },
    condition: {
      type: Object,
      default: () => ({})
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: true
    },
    // 是否显示symbol条件，不显示时，选择框默认有类型
    useSymbol: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      numFields: numFields,
      inputTagList: [], // 单行文本多选tag列表
      inputContent: '' // 单行文本输入
    }
  },
  mounted() {
    this.inputTagList = xbb.deepClone(this.inputValue)
  },

  computed: {
    inputValue: {
      get() {
        if (!this.multiple) {
          if (Array.isArray(this.condition.value) && this.condition.value.length) {
            return this.condition.value[0]
          } else if (Array.isArray(this.condition.value)) {
            return this.numFields.includes(this.condition.fieldType) ? undefined : ''
          } else {
            return this.condition.value
          }
        } else {
          if (Array.isArray(this.condition.value)) {
            return this.condition.value
          } else {
            this.$emit('valueChange', [])
            return []
          }
        }
      },
      set(val) {
        if (
          !Array.isArray(val) &&
          !['', undefined].includes(val) &&
          !['empty', 'noempty'].includes(this.condition.symbol)
        ) {
          this.$emit('valueChange', [val])
        } else if (!Array.isArray(val)) {
          this.$emit('valueChange', [])
        } else {
          if (val.length === 2) {
            this.$emit('valueChange', val)
          }
        }
      }
    }
  },

  methods: {
    modelValChange(val) {
      let minNumber = -100000000000
      let maxNumber = 100000000000
      if (this.condition.numericalLimitsFlag && this.condition.numericalLimits) {
        minNumber =
          this.condition.numericalLimits.min !== undefined
            ? this.condition.numericalLimits.min
            : -100000000000
        maxNumber =
          this.condition.numericalLimits.max !== undefined
            ? this.condition.numericalLimits.max
            : -100000000000
      }
      if (this.multiple) {
        // symbol可切换时，启动自动填充，范围必须输入两个值；symbol不可切换时，默认是范围选择，则允许输入一个值；
        if (this.useSymbol) {
          // 自动填充
          if (val === 'min' && this.inputValue[0] === undefined) {
            this.inputValue[0] = minNumber
          }
          if (val === 'max' && this.inputValue[1] === undefined) {
            this.inputValue[1] = maxNumber
          }
          if (
            val === 'min' &&
            this.inputValue[0] !== undefined &&
            this.inputValue[1] === undefined
          ) {
            this.$set(this.inputValue, 1, this.inputValue[0])
          }
          if (
            val === 'max' &&
            this.inputValue[1] !== undefined &&
            this.inputValue[0] === undefined
          ) {
            this.$set(this.inputValue, 0, this.inputValue[1])
          }
        }
        const [min, max] = this.inputValue.map((item) => +item)
        // 范围限定
        if (this.inputValue[0] !== undefined && (min < minNumber || max > maxNumber)) {
          this.inputValue[0] = minNumber
        }
        if (this.inputValue[1] !== undefined && (min < minNumber || max > maxNumber)) {
          this.inputValue[1] = maxNumber
        }
        // 最小值小于最大值
        if (this.inputValue[0] !== undefined && this.inputValue[1] !== undefined) {
          if (val === 'min' && min > max) {
            this.$set(this.inputValue, 0, this.inputValue[1])
          } else if (val === 'max' && max < min) {
            this.$set(this.inputValue, 1, this.inputValue[0])
          }
        }
        this.inputValue = xbb.deepClone(this.inputValue)
      } else {
        if (String(this.inputValue).length > 100) {
          this.inputValue = Number(String(this.inputValue).slice(0, 100))
        }
      }
    },
    // 单行文本字段 选择等于任意一个 可以筛选多个文字
    handleInputConfim() {
      if (this.inputContent) {
        this.inputTagList.push(this.inputContent)
        this.inputContent = ''
        this.$emit('valueChange', this.inputTagList)
      }
    },
    // 删除添加的多个筛选条件
    handleClose(index) {
      this.inputTagList.splice(index, 1)
      this.$emit('valueChange', this.inputTagList)
    }
    // inputChange () {
    //   if (!this.multiple) {
    //     var str = this.inputValue
    //     var len1 = str.substr(0, 1)
    //     var len2 = str.substr(1, 1)
    //     // 如果第一位是0，第二位不是点，就用数字把点替换掉
    //     if (str.length > 1 && len1 === 0 && len2 !== '.') {
    //       str = str.substr(1, 1)
    //     }
    //     // 第一位不能是.
    //     if (len1 === '.') {
    //       str = '0' + str
    //     }
    //     // 限制只能输入一个小数点
    //     if (str.indexOf('.') !== str.lastIndexOf('.')) {
    //       str = ''
    //     }
    //     // 正则替换，保留数字和小数点
    //     str = str.replace(/[^-\d.]+/g, '')
    //     this.inputValue = str
    //   }
    // }
  }
}
</script>

<style lang="scss" scoped>
.widget-input {
  &--single {
    width: 100%;
  }
  &--multiple {
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
  }
  &__text {
    width: auto;
  }
  &__to {
    width: 20px;
    height: 30px;
    padding: 0 3px;
    margin: 0 auto;
    line-height: 30px;
    text-align: center;
  }
  .input-multiple-box {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    width: 400px;
    // height: 40px;
    // padding: 5px;
    border: 1px solid $neutral-color-3;
    .el-tag {
      margin: 3px 5px;
    }
    :deep(.el-input__inner) {
      border: none;
    }
  }
}
</style>

<style lang="scss">
.widget-input {
  .el-input-number .el-input__inner {
    text-align: inherit;
  }
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none;
  }
}
</style>
