<!--
 * @Description: 人员选择小部件
 -->
<template>
  <div class="widget-person">
    <multi-tag
      class="select-form__multi-select"
      :closable="isEdit"
      :prop-format="mulitFormat"
      :selected-tag="personValue"
      @click.native="openPersonSelect"
      @tagDelete="tagDelete"
    ></multi-tag>
  </div>
</template>

<script>
import MultiTag from '@/components/select-tree/multi-tag.vue'
import PersonSelect from '@/components/person-select/index'

export default {
  name: 'WidgetPerson',

  components: {
    MultiTag
  },

  props: {
    // 是否为多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 单个筛选条件对象
    condition: {
      type: Object,
      default: () => ({})
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      personSelectVisit: false,
      // 多选回显值
      mulitFormat: {
        label: 'name'
      }
    }
  },

  computed: {
    personValue: {
      get() {
        if (!this.multiple) {
          if (Array.isArray(this.condition.value) && this.condition.value.length) {
            return this.condition.value.slice(0, 1)
          } else {
            return []
          }
        } else {
          if (Array.isArray(this.condition.value)) {
            return this.condition.value
          } else {
            return []
          }
        }
      },
      set(val) {
        if (!Array.isArray(val) && val !== '') {
          this.$emit('valueChange', [val])
        } else if (!Array.isArray(val)) {
          this.$emit('valueChange', [])
        } else {
          this.$emit('valueChange', val)
        }
      }
    }
  },

  methods: {
    // 删除选中的范围
    tagDelete(tag) {
      if (this.multiple) {
        const deleteTagIndex = this.personValue.findIndex((item) => {
          return item.id === tag.id
        })
        if (deleteTagIndex !== -1) {
          this.personValue.splice(deleteTagIndex, 1)
          // this.personValue = JSON.parse(JSON.stringify(this.personValue.splice(deleteTagIndex, 1)))
        }
      } else {
        this.personValue = []
      }
      this.$emit('valueChange', this.personValue)
    },
    openPersonSelect() {
      if (this.isEdit) {
        // this.personSelectVisit = true
        PersonSelect({
          activeTabName: 'user',
          showTabsName: ['user'],
          defaultVal: this.personValue,
          multiple: {
            user: this.multiple
          },
          otherParams: {
            // 是否控制组织架构权限范围
            deptVisibleRule: true
          }
        }).then((res) => {
          if (res.data) {
            this.personValue = res.data
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
