<!-- 表单树选择框 -->
<!-- 把原工作流里用的表单树提出来公用 -->
<!-- 系统里类似的组件有好几个里边都混了其他的业务逻辑，工作流的相对简洁且基本与业务无关 -->
<template>
  <div class="form-select-tree">
    <el-popover
      v-model="popoverShow"
      :disabled="disabled"
      placement="bottom"
      popper-class="form-select-tree__popover"
      title=""
      trigger="click"
      width="230"
    >
      <el-input
        slot="reference"
        class="form-select-tree__input"
        :disabled="disabled"
        :placeholder="$t('placeholder.choosePls', { attr: $t('form.form') })"
        :readonly="true"
        :value="inputName"
        @mouseenter.native="inputHovering = true"
        @mouseleave.native="inputHovering = false"
      >
        <template slot="suffix">
          <i v-show="!showClose" class="input__caret" :class="arrowClass"></i>
          <i v-if="showClose" class="el-icon-circle-close input__caret" @click="clearHandler"></i>
        </template>
      </el-input>
      <!-- 下拉树 -->
      <div class="select-item__block">
        <div class="search-block">
          <el-input
            v-model="treeSearch"
            :placeholder="$t('placeholder.searchPls', { attr: '' })"
            prefix-icon="el-icon-search"
          ></el-input>
        </div>
        <el-scrollbar v-if="filterTreeData.length" wrap-class="el-select-dropdown__wrap">
          <el-tree
            ref="tree"
            :data="filterTreeData"
            :filter-node-method="filterNode"
            node-key="formId"
            :props="defaultProps"
            @node-click="nodeClickHandler"
          >
            <template slot-scope="{ data }">
              <div>{{ data['name'] }}</div>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { getFormAllList, getFormAllListNew } from '@/api/form'

export default {
  name: 'FormSelectTree',

  props: {
    // 当前formId
    currentFormId: {
      type: Number
    },
    appId: {
      type: Number
    },
    menuId: {
      type: Number
    },
    formId: {
      type: Number
    },
    saasMark: {
      type: Number
    },
    businessType: {
      type: Number
    },
    name: {
      type: String
    },
    businessRuleFlag: {
      type: Number
    },
    filterPaasForm: {
      type: Boolean,
      default: false
    },
    // 筛选指定app
    filterApps: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    // 拓展接口，方便自定义下拉值的获取
    customListApi: {
      type: Function
    },
    // 自定义接口参数
    customListParams: {
      type: Object
    },
    isNewNode: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      inputHovering: false, // 鼠标是否在输入框里
      popoverShow: false,
      treeData: [], // form tree 数据
      defaultProps: {
        label: 'name',
        children: 'formList'
      },
      treeSearch: ''
    }
  },

  computed: {
    arrowClass() {
      return this.popoverShow ? 'el-icon-arrow-up is-reverse' : 'el-icon-arrow-up'
    },
    // 控制clear图标
    showClose() {
      // 可清除、鼠标移入、有值才显示 TODO:
      return this.clearable && !this.disabled && this.inputHovering && !!this.inputName
    },
    inputName: {
      get() {
        if (!this.formId) return ''

        const app = this.filterTreeData.find((item) => +item.appId === +this.appId)
        if (!app) return ''

        const appName = app.appName
        const form = app.formList.find((item) => +item.formId === +this.formId)
        if (!form) return appName

        const formName = form.name
        return `${appName}.${formName}`
      }
    },
    filterTreeData() {
      return (this.treeData || []).filter((item) => {
        return !this.filterApps.length || this.filterApps.includes(item.appId)
      })
    }
  },

  watch: {
    treeSearch(val) {
      this.$refs['tree'].filter(val)
    }
  },

  created() {
    this.getFormAllList()
  },

  destroyed() {
    this.treeData = null
  },

  methods: {
    updateProps(
      formInfo = {
        appId: null,
        formId: null,
        saasMark: null,
        businessType: null,
        menuId: null,
        name: null
      }
    ) {
      this.$emit('update:appId', formInfo.appId)
      this.$emit('update:formId', formInfo.formId)
      this.$emit('update:saasMark', formInfo.saasMark)
      this.$emit('update:businessType', formInfo.businessType)
      this.$emit('update:menuId', formInfo.menuId)
      this.$emit('update:name', formInfo.name)

      if (this.formId !== formInfo.formId) {
        this.$emit('change', {
          appId: formInfo.appId,
          menuId: formInfo.menuId,
          formId: formInfo.formId,
          saasMark: formInfo.saasMark,
          businessType: formInfo.businessType
        })
      }
    },
    clearHandler() {
      this.updateProps()
    },
    nodeClickHandler(data) {
      if (data.parentId === 0) return

      this.updateProps(data)
      this.popoverShow = false
    },
    filterNode(value, data) {
      return !value || data.name.indexOf(value) !== -1
    },
    getFormApi() {
      if (this.customListApi && typeof this.customListApi === 'function') {
        return this.customListApi(this.customListParams || {})
      }

      const params = this.businessRuleFlag
        ? { businessRuleFlag: this.businessRuleFlag }
        : { formId: this.currentFormId }
      const api = this.isNewNode ? getFormAllListNew : getFormAllList // 吴峰让换 新建数据节点 换接口
      return api(params)
    },
    getFormAllList() {
      return this.getFormApi()
        .then(({ result, result: { appList } }) => {
          this.treeData = appList.map((item) => {
            return {
              ...item,
              parentId: 0,
              name: item.appName,
              formList: item.formList
            }
          })

          return result
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.form-select-tree {
  & &__input {
    .input__caret {
      width: 25px;
      height: 100%;
      font-size: 14px;
      line-height: 32px;
      color: $text-grey;
      cursor: pointer;
      transition: transform 0.3s;
      transform: rotateZ(180deg);
      &.is-reverse {
        transform: rotateZ(0deg);
      }
    }
    &.is-disabled .input__caret {
      cursor: not-allowed;
    }
  }
}
</style>

<style lang="scss">
.form-select-tree__popover {
  .select-item__block {
    .search-block {
      margin-bottom: 5px;
    }
    .el-tree {
      .el-tree-node__content {
        .node-disabled {
          color: $text-grey;
        }
      }
    }
  }
}
.form-select-tree {
  & &__input {
    .el-input__inner {
      cursor: pointer;
      @include singleline-ellipsis;
    }
    &.is-disabled .el-input__inner {
      cursor: not-allowed;
    }
  }
}
</style>
