<!--
 * @Description: 环形进度条入口
 -->
<template>
  <div class="v-progress">
    <progress-loop :circle-angle="circleAngle"></progress-loop>
  </div>
</template>

<script>
import Progress<PERSON>oop from './progress-loop.vue'

export default {
  name: 'VProgress',

  components: {
    ProgressLoop
  },

  filters: {},

  mixins: [],

  props: {
    circleAngle: {
      type: Number,
      required: true
    }
  },

  data() {
    return {}
  },

  computed: {},

  watch: {},

  mounted() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.v-progress {
  position: relative;
}
</style>
