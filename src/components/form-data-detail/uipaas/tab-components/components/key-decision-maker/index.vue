<template>
  <div
    class="key-decision-maker"
    :class="isInDetail ? 'base-tab-content-inner' : 'base-tab-content'"
  >
    <div class="condition-block" :class="{ 'is-be-use': isInDetail }">
      <div class="tag" :class="{ active: active === 0 }" @click="handleChangeCondition(0)">
        主要人物
      </div>
      <div class="tag" :class="{ active: active === 1 }" @click="handleChangeCondition(1)">
        推荐联系方式
      </div>
    </div>
    <slot name="tips"></slot>
    <div class="key-decision-maker__content">
      <template v-if="active === 0">
        <div
          v-for="(item, index) in tabData.list"
          :key="index"
          class="key-decision-maker__content-item"
        >
          <div class="key-decision-maker__content-item__left">{{ item.position }}</div>
          <div class="key-decision-maker__content-item__right">{{ item.name }}</div>
        </div>
      </template>
      <template v-else>
        <!-- <el-button @click="handleAddContactInxbb">测试新建联系人销帮帮弹窗</el-button> -->
        <div v-for="(item, index) in tabData.contactList" :key="index" class="contact-list">
          <div class="contact-list__top">
            <div class="contact-list__top-phone">
              <div>{{ item.contactDetail }}</div>
              <i
                class="icon-file-copy-line t-iconfont"
                style="margin-left: 8px; color: #248df5"
                @click="handleCopyPhone(item.contactDetail)"
              ></i>
            </div>
            <div v-if="item.recommend === 1" class="contact-list__top-tag">推荐</div>
          </div>
          <div class="contact-list__desc">
            <div class="label">姓名</div>
            <div class="value">
              <span>{{ item.name }}</span>
              <span v-if="item.maybePerson" class="maybe">({{ item.maybePerson }})</span>
            </div>
          </div>
          <div class="contact-list__desc">
            <div class="label">职位</div>
            <div class="value">{{ item.position }}</div>
          </div>
          <div class="contact-list__desc">
            <div class="label">来源</div>
            <div class="value">{{ item.comeFrom }}</div>
          </div>
          <div class="contact-list__btn">
            <el-button
              :disabled="hasAdd.includes(index)"
              size="mini"
              @click="beforHandleAddContact(index, item)"
            >
              <i class="icon-user-add-line t-iconfont" style="margin-right: 8px; color: #248df5"></i
              >{{ hasAdd.includes(index) ? '已添加' : '添加联系人' }}</el-button
            >
          </div>
        </div>
        <el-button v-if="fetching" disabled link :loading="fetching">加载中</el-button>
      </template>
    </div>
  </div>
</template>

<script setup>
import xbb from '@xbb/xbb-utils'
import { ref, onBeforeUnmount } from 'vue'
// import { useStore } from '@/composables/useStore'
import { profileInsight, getId, saveCrmContactData } from '@/api/ai-assistant.js'

import axios from 'axios'
// const router = useRouter()
// const store = useStore()
// const emits = defineEmits(['handle-add-contact'])
const emits = defineEmits('instock-warring', 'link-add')

const props = defineProps({
  tabData: {
    type: Object,
    default: () => {}
  },
  customerName: {
    type: String,
    default: ''
  },
  isInDetail: {
    // 是否作为组件被其他页面调用
    type: Boolean,
    default: false
  },
  formQuery: {
    type: Object,
    default: () => ({})
  }
})

const active = ref(0)
const fetching = ref(false)

const editData = ref({}) // 添加联系人 表单数据

const hasAdd = ref([])

const axiossource = ref({})

onBeforeUnmount(() => {
  axiossource.value.cancel && axiossource.value.cancel()
})

const addForm = async (key) => {
  const res = await getId({ type: 'contact' })
  const { appId, menuId, formId, businessType } = res.result
  emits('link-add', { appId, menuId, formId, businessType, aiAddKey: key })
}

const handleChangeCondition = async (val) => {
  if (val !== active.value) {
    active.value = val
    if (val === 1 && !props.tabData.contactRequest) {
      try {
        const CancelToken = axios.CancelToken
        const source = CancelToken.source()
        axiossource.value = source
        fetching.value = true
        const res = await profileInsight(
          {
            subTab: 'recommendedContact',
            tab: 'keyDecisionMaker',
            customerName: props.customerName
          },
          { cancelToken: axiossource.value.token }
        )
        const data = res.result
        // eslint-disable-next-line vue/no-mutating-props
        props.tabData.contactRequest = true
        // eslint-disable-next-line vue/no-mutating-props
        props.tabData.contactList = data.recommendedContactList
      } catch (error) {
        console.log(error)
      }
      fetching.value = false
    } else if (val === 0) {
      axiossource.value.cancel && axiossource.value.cancel()
    }
  }
}

const beforHandleAddContact = async (index, item) => {
  editData.value = xbb.deepClone(item)
  editData.value.index = index
  // addContact.value = true
  const res = await saveCrmContactData({
    name: item.name,
    comeFrom: item.comeFrom,
    position: item.position,
    contactDetail: item.contactDetail,
    email: item.email,
    crmCustomerInfo: {
      dataId: props.formQuery.dataId,
      appId: props.formQuery.appId,
      menuId: props.formQuery.menuId,
      formId: props.formQuery.formId,
      name: ''
    }
  })
  addForm(res.result.key)
}

// const handleSave = async (type) => {
//   const item = editData.value
//   const res = await saveContact({
//     customerId: props.tabData.id,
//     name: item.name,
//     position: item.position,
//     comeFrom: item.comeFrom,
//     contactDetail: item.contactDetail,
//     recommend: item.recommend, //1-推荐，0-不推荐
//     maybePerson: item.maybePerson,
//     email: item.email
//   })
//   console.log(res)
// }

const handleCopyPhone = async (val) => {
  try {
    const copyText = async () => {
      try {
        await navigator.clipboard.writeText(val)
        emits('instock-warring', 'success', '复制成功')
      } catch (err) {
        console.error('复制文本出错:', err)
        emits('instock-warring', 'error', '复制错误')
      }
    }
    copyText()
  } catch (error) {
    console.log('error', error)
  }
}
</script>

<style lang="scss" scoped>
.key-decision-maker {
  .condition-block {
    display: flex;
    flex-direction: row;
    margin-bottom: 16px;
  }
  .tag {
    padding: 4px 10px;
    margin-right: 12px;
    /* 六级文字/常规 */
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    color: #323233;
    text-align: center;
    letter-spacing: 0em;
    cursor: pointer;
    background: #f2f3f5;
    border-radius: 4px;
  }
  .is-be-use {
    .tag {
      border-radius: 12px;
    }
  }
  .active {
    color: #ff6a00;
    background: #fff5e8;
  }
  .key-decision-maker__content {
    display: flex;
    flex-direction: column;
    .key-decision-maker__content-item {
      display: flex;
      flex-direction: row;
      margin-bottom: 16px;
      /* 四级文字/常规 */
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0em;
      .key-decision-maker__content-item__left {
        flex: 0 0 110px;
        margin-right: 12px;
        color: #969799;
      }
      .key-decision-maker__content-item__right {
        flex: 1;
        color: #323233;
      }
    }
    .contact-list {
      box-sizing: border-box;
      padding: 12px;
      margin-bottom: 12px;
      border: 1px solid #f2f3f5;
      border-radius: 8px;
      .contact-list__top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        .contact-list__top-phone {
          display: flex;
          font-family: PingFang SC;
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          color: #323233;
        }
        .contact-list__top-tag {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 20px;
          font-family: PingFang SC;
          font-size: 12px;
          line-height: 16px;
          color: #00c241;
          background: #e8ffec;
          border-radius: 4px;
        }
      }
      .contact-list__desc {
        display: flex;
        flex-direction: row;
        margin-bottom: 8px;
        font-family: PingFang SC;
        font-size: 14px;
        .label {
          flex: 0 0 32px;
          margin-right: 8px;
          color: #969799;
        }
        .value {
          color: #323233;
          .maybe {
            margin-left: 8px;
            color: #969799;
          }
        }
      }
      .contact-list__btn {
        display: flex;
        :deep(.el-button) {
          flex: 1;
        }
      }
    }
  }
}
</style>
