<template>
  <TabTitle :component-info="componentInfo">
    <Competitor
      :active-name-model="activeNameModel"
      :form-query="formQuery"
      :query="formQuery"
      :tab-info="componentInfo"
      :ui-paas="true"
    ></Competitor>
  </TabTitle>
</template>

<script>
import Competitor from '@/components/form-data-detail/components/tabs/sale-opportunity-detail.vue'
import TabTitle from '../tab-title.vue'

export default {
  components: {
    TabTitle,
    Competitor: Competitor
  },
  props: {
    componentInfo: {
      type: Object,
      default: () => {}
    },
    formQuery: {
      type: Object,
      default: () => {}
    },
    tabInfo: {
      type: Object,
      default: () => {}
    },
    tabList: {
      type: Array,
      default: () => []
    },
    activeNameModel: {
      type: String,
      default: ''
    }
  }
}
</script>
