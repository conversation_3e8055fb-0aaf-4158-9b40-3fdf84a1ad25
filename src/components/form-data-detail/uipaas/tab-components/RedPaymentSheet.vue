<template>
  <TabTitle :component-info="componentInfo">
    <RedPaymentSheet
      :active-name-model="activeNameModel"
      :form-query="formQuery"
      :query="formQuery"
      :tab-info="componentInfo"
      :ui-paas="true"
      :ui-paas-component-config="componentInfo.componentConfig"
    ></RedPaymentSheet>
  </TabTitle>
</template>

<script>
import RedPaymentSheet from '@/components/form-data-detail/components/tabs/red-rush-tab.vue'
import TabTitle from '../tab-title.vue'

export default {
  components: {
    TabTitle,
    RedPaymentSheet
  },
  props: {
    componentInfo: {
      type: Object,
      default: () => {}
    },
    formQuery: {
      type: Object,
      default: () => {}
    },
    tabInfo: {
      type: Object,
      default: () => {}
    },
    tabList: {
      type: Array,
      default: () => []
    },
    activeNameModel: {
      type: String,
      default: ''
    },
    hasRightContainer: {
      type: Boolean,
      default: false
    }
  }
}
</script>
