<template>
  <TabTitle :component-info="componentInfo">
    <OldCustomerStageTab
      :active-name-model="activeNameModel"
      :form-query="formQuery"
      :query="formQuery"
      :tab-info="componentInfo"
    ></OldCustomerStageTab>
  </TabTitle>
</template>

<script>
import customerStageTab from '@/components/form-data-detail/components/stage-process/historyStage/index.vue'
import TabTitle from '../tab-title.vue'

export default {
  components: {
    TabTitle,
    OldCustomerStageTab: customerStageTab
  },
  props: {
    componentInfo: {
      type: Object,
      default: () => {}
    },
    formQuery: {
      type: Object,
      default: () => {}
    },
    tabInfo: {
      type: Object,
      default: () => {}
    },
    tabList: {
      type: Array,
      default: () => []
    },
    activeNameModel: {
      type: String,
      default: ''
    }
  }
}
</script>
