<template>
  <TabTitle :component-info="componentInfo">
    <ContactTab
      :active-name-model="activeNameModel"
      :form-query="formQuery"
      :has-right-container="hasRightContainer"
      :is-load-more="isLoadMore"
      :query="formQuery"
      :tab-info="componentInfo"
      :ui-paas="true"
    ></ContactTab>
  </TabTitle>
</template>

<script>
import ContactTab from '@/components/form-data-detail/components/tabs/customer-contacts-detail.vue'
import TabTitle from '../tab-title.vue'

export default {
  components: {
    TabTitle,
    ContactTab: ContactTab
  },
  props: {
    componentInfo: {
      type: Object,
      default: () => {}
    },
    formQuery: {
      type: Object,
      default: () => {}
    },
    tabInfo: {
      type: Object,
      default: () => {}
    },
    tabList: {
      type: Array,
      default: () => []
    },
    activeNameModel: {
      type: String,
      default: ''
    },
    hasRightContainer: {
      type: Boolean,
      default: false
    },
    isLoadMore: {
      // 部分标签页加载下一页
      type: Boolean,
      default: () => {
        return false
      }
    }
  }
}
</script>
