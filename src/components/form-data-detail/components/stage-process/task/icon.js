/*
 * @Author: xuwang.bao
 * @LastEditors: xuwang.bao
 * @Description: 一些工具属性
 * @$emit:
 * @$slot:
 * @Date: 2019-05-10 16:09:03
 * @LastEditTime: 2019-05-15 15:00:43
 */
const FielTypeMap = {
  xls: 'ic_file_excel',
  xlsx: 'ic_file_excel',
  html: 'ic_file_html',
  htm: 'ic_file_html',
  jpg: 'ic_file_image',
  jpeg: 'ic_file_image',
  git: 'ic_file_image',
  png: 'ic_file_image',
  pdf: 'ic_file_pdf',
  doc: 'ic_file_word',
  docx: 'ic_file_word',
  txt: 'ic_file_text',
  zip: 'ic_file_zip',
  '*': 'ic_file_other'
}
export default {
  methods: {
    getFileType(name = '') {
      const strArr = name.split('.')
      const suffix = strArr[strArr.length - 1]
      return FielTypeMap[suffix] || FielTypeMap['*']
    }
  }
}
