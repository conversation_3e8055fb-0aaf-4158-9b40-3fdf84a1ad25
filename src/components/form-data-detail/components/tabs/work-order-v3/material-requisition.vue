<!--
 * @Description: 领料单
 -->
<template>
  <div class="contract-tab" style="height: 100%">
    <el-scrollbar style="height: 100%">
      <div v-if="headList.length" class="page__head">
        <div class="page__button">
          <el-button type="primary" @click="add">新建</el-button>
        </div>
      </div>
      <list-table
        ref="table"
        :choose-check-box="false"
        class="tabTableHeight"
        height="100%"
        :is-show-filter="false"
        :is-show-status="false"
        :loading="loading"
        :summary="false"
        :table-data="tableData"
        :table-head="headList"
        @editForm="editForm"
      ></list-table>
      <v-pagination
        :current-page="currentPage"
        layout="slot,sizes, prev, pager, next, jumper"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </v-pagination>
    </el-scrollbar>
  </div>
</template>

<script>
import { getReceiveMaterial, getWorkOrderReceiveMaterial } from '@/api/work-order-v2/spare-parts.js'
import { mapActions } from 'vuex'
import tempVue from '@/utils/temp-vue'

const listTable = () => import(/* webpackChunkName: "list-form" */ '@/components/list/list-table')
export default {
  components: { ListTable: listTable },

  inject: ['parentDetail'],
  props: {
    query: {
      type: Object,
      default: () => ({})
    },
    formQuery: {
      type: Object,
      default: () => ({})
    },
    tabInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {},
  data() {
    return {
      timeTableShowType: 'showlist',
      loading: false,
      headList: [],
      total: 0,
      pageSize: 10,
      currentPage: 1,
      tableData: [], // 表格数据数组
      linkFormInfo: {}
    }
  },
  created() {
    this.init()
  },
  mounted() {
    const detail = document.querySelector('.form-data-detail__dialog')?.getBoundingClientRect()
    if (detail) {
      const height = Math.floor(detail.height - 196)
      this.$el.style.height = height + 'px'
    }
  },
  destroyed() {
    tempVue.$off('refreshTableList')
  },
  methods: {
    ...mapActions([
      'formDetailLinkAdd' // 列表页关联新建
    ]),
    init() {
      this.getTableList()
      tempVue.$on('refreshTableList', this.getTableList)
    },
    // 获取接口
    // 请求表格数据
    getTableList() {
      this.loading = true
      // const {
      //   linkAppId,
      //   linkBusinessType,
      //   linkFormId,
      //   linkMenuId,
      //   linkSaasMark,
      //   linkSubBusinessType
      // } = this.tabInfo.linkList
      // const params = {
      //   dataId: this.query.dataId,
      //   appId: linkAppId,
      //   businessType: linkBusinessType,
      //   formId: linkFormId,
      //   menuId: linkMenuId,
      //   saasMark: linkSaasMark,
      //   subBusinessType: linkSubBusinessType,
      //   pageSize: this.pageSize,
      //   page: this.currentPage
      // }
      const params = {
        dataId: this.query.dataId,
        appId: this.query.appId,
        businessType: this.query.businessType,
        formId: this.query.formId,
        menuId: this.query.menuId,
        saasMark: this.query.saasMark,
        subBusinessType: this.query.subBusinessType,
        pageSize: this.pageSize,
        page: this.currentPage
      }
      const listApi =
        this.query.businessType === 20300 ? getWorkOrderReceiveMaterial : getReceiveMaterial
      listApi(params)
        .then((data) => {
          console.log(data)
          this.headList = data.result.headList
          this.tableData = data.result.paasFormDataESList.map((i) => {
            return {
              ...i,
              ...i.data,
              id: i.dataId,
              dataId: i.dataId,
              businessType: data.result.businessType,
              saasMark: 1
            }
          })
          this.linkFormInfo = data.result.form
          this.total = data.result.pageHelper.rowsCount // 总条数
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.loading = false
        })
    },

    editForm(data) {
      // 操作的类型
      const type = data.type
      if (type === 'detail') {
        this.openDetail({ data })
      }
    },

    /**
     * @description: 打开详情
     * @param {type}
     * @return:
     */
    openDetail({ data }) {
      const { dataId, businessType, dataid, businesstype } = data
      const params = {
        dataId: +dataid || +dataId,
        saasMark: +this.$route.query.saasMark || this.tabInfo.linkList.linkSaasMark,
        businessType: +businesstype || +businessType
      }
      this.parentDetail && this.parentDetail.showChildrenDetail(params)
    },
    // 每页所显示的条数变更
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableList()
    },

    // 当前分页变更
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableList()
    },
    add() {
      // this.addNew()
      const params = {
        ...this.query,
        appId: this.linkFormInfo.appId,
        businessType: this.linkFormInfo.businessType,
        linkBusinessType: this.query.businessType,
        formId: this.linkFormInfo.formId,
        menuId: this.linkFormInfo.menuId,
        saasMark: this.linkFormInfo.saasMark,
        subBusinessType: this.linkFormInfo.subBusinessType,
        dataIdList: [this.query.dataId]
      }
      this.formDetailLinkAdd(params)
    }
  }
}
</script>

<style lang="scss">
.contract-tab {
  .el-table {
    // margin: 15px auto;
  }
}
</style>

<style lang="scss" scoped>
.contract-tab {
  // padding: 0 15px;
  .theme {
    font-size: 13px;
    line-height: 30px;
    background: $bg-blue;
  }
  .theme i {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 0 10px;
    background: $brand-color-5;
  }
  .productCon {
    display: flex;
    flex-wrap: wrap;
    margin: 15px 0 0;
    margin-left: 28px;
    &.listCPGG li {
      padding: 10px 10px;
      margin-right: 15px;
      margin-bottom: 10px;
      cursor: pointer;
      border: 1px solid $text-tip;
      border-radius: 2px;
    }
    &.listCPGG li.active {
      color: $base-white;
      background: $brand-color-5;
      border: 1px solid $brand-color-5;
    }
  }
  .pagination-wrapper {
    width: 100%;
    padding-top: 20px;
  }
  .pagination-wrapper .v-pagination {
    float: right;
    width: auto;
  }
  .product-unit {
    line-height: 15px;
    &__item {
      font-size: 12px;
      color: $text-tip;
    }
  }
}
.page__head {
  display: flex;
  justify-content: flex-end;
  .page__button {
  }
}
:deep(.el-scrollbar__view) {
  height: 100%;
}
.tabTableHeight {
  height: calc(100% - 85px);
}

.table-box {
  padding: 10px 0 !important;
}
</style>
