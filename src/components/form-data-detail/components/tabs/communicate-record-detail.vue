<!--
 * @Description:跟进记录Tab
  curLoading: 滚动状态
  formQuery： 表单所需参数
 }
  updateFormList： 刷新列表
 -->

<template>
  <div class="communicate-recorders" style="height: 100%">
    <el-scrollbar ref="scrollbar" style="height: 100%">
      <!-- <communicate-reccord :form-query="formQuery" :query="query" @dynamicsSubmit="dynamicsSubmit">
      </communicate-reccord> -->
      <FollowRecordFast
        v-if="!uiPaas"
        :form-query="formQuery"
        :query="query"
        @submitSuccess="dynamicsSubmit"
      ></FollowRecordFast>
      <div v-for="(item, index) in refVisit" :key="item.opportunityId" class="list-item">
        <div class="item-content">
          <div class="user-infos">
            <div>
              <div class="title">
                <span class="name" v-html="addNodeByWX(item.userName)"></span>
                <span class="tag">{{ item.commuicateTimeStr }}</span>
              </div>

              <div class="avatar">
                <avatar-img :alt="item.userName" :size="44" :src="item.userAvatar" />
              </div>
              <div class="others">
                <span class="record-type text">{{ item.type }}</span>
                <span class="text">
                  <a v-if="item.hideSupplier" href="javascript:;" @click="unOpen">{{
                    item.customerName
                  }}</a>
                  <a
                    v-else
                    href="javascript:;"
                    @click="seeChildrenDetail(item.mainBusiness.entity)"
                    >{{ item.mainBusiness.name }}</a
                  >
                </span>
              </div>
            </div>

            <div class="options">
              <span
                v-if="item.permissionSet && item.permissionSet.indexOf('edit') > -1"
                class="text"
                @click="editItem(item)"
                >{{ $t('operation.edit') }}</span
              >
              <span
                v-if="item.permissionSet && item.permissionSet.indexOf('delete') > -1"
                class="red text"
                @click="handleDelete(item.id)"
                >{{ $t('operation.delete') }}</span
              >
            </div>
          </div>

          <div class="article">
            <!-- <a href="javascript:;" @click="openDetail(item.opportunityId, 'opportunityApi')" class="talk">ttt</a> -->
            <p v-for="(p, pIndex) in paragraph(item.memo)" :key="p + pIndex" class="text">
              <span v-html="p"></span>
            </p>
          </div>

          <div v-if="item.images && item.images.length" class="gallery">
            <ul class="gallery-wrap">
              <li
                v-for="(img, imgIndex) in strToArr(item.images)"
                :key="img + imgIndex"
                class="img"
              >
                <img
                  v-lazy="thumbnail(img, 80)"
                  height="80"
                  width="80"
                  @click="openLightbox(item.images, imgIndex)"
                />
              </li>
            </ul>
          </div>

          <div class="default">
            <p v-if="item.contactArray.length">
              {{ $t('formDataDetail.tabs.contacts') }}
              <!-- <template v-if="[pageBusinessType.SUPPLIER, pageBusinessType.PURCHASE].indexOf(query.businessType) > -1">
                  <span v-for="(contact, index) in item.contactArray" :key="contact.entity.dataId">{{contact.name}}{{index !== item.contactArray.length-1?'、':''}}</span>
                </template>
                <template v-else>
                  <a v-for="(contact, index) in item.contactArray" :key="contact.entity.dataId" @click="seeChildrenDetail(contact.entity)">{{contact.name}}{{index !== item.contactArray.length-1?'、':''}}</a>
                </template> -->
              <template>
                <a
                  v-for="(contact, index) in item.contactArray"
                  :key="contact.entity.dataId"
                  @click="seeChildrenDetail(contact.entity)"
                  >{{ contact.name }}{{ index !== item.contactArray.length - 1 ? '、' : '' }}</a
                >
              </template>
            </p>
            <p v-if="item.communicateBusiness.entity">
              {{ $t('formDataDetail.tabs.followUpBusiness')
              }}<a @click="seeChildrenDetail(item.communicateBusiness.entity)">{{
                item.communicateBusiness.name
              }}</a>
            </p>
            <!-- <p>完成访客计划：<a ></a></p> -->
            <p v-if="item.address">{{ $t('formDataDetail.tabs.position') }}：{{ item.address }}</p>
          </div>

          <!-- 展开其他信息 -->
          <ul v-if="currentOpenIndex === index" class="default no-padding">
            <FormDataForm
              :app-id="query.appId"
              :base-form-data="communicateFormData"
              :business-type="communicateBusinessType"
              :data-id="query.dataId"
              :field-list="communicateFieldList"
              :form-data="communicateFormData"
              :form-id="query.formId"
              :is-edit="true"
              :is-see="true"
              label-position="left"
              :menu-id="query.menuId"
              :readonly="true"
              :saas-mark="query.saasMark"
              :serial-no="formData['serialNo']"
              @linkItemClick="linkItemClick"
            />
          </ul>
        </div>
        <div class="item-options">
          <!-- <like :refId="item.id" v-if="item.like"></like> -->
          <commentAndLike
            :app-info="appInfo"
            :comment-count.sync="item.commentCount"
            :detail-info="item"
          >
            <a
              v-if="!item.autoGen"
              slot="notice"
              class="more"
              @click="redundantToggle(item, index)"
              >{{
                currentOpenIndex === index ? $t('operation.retract') : $t('message.showMore')
              }}</a
            >
          </commentAndLike>
        </div>
        <!-- <report :refId="item.id" v-if="item.report"></report> -->
      </div>
      <div class="load-more">
        <template v-if="showLoadmore">
          <i class="el-icon-loading"></i>
          {{ $t('operation.loadMore') }}
        </template>
        <template v-if="!showLoadmore && page.rowsCount > page.pageSize">{{
          $t('message.noMore')
        }}</template>
      </div>
      <Empty v-if="refVisit.length === 0"></Empty>

      <!-- 设置完成访客计划 -->
      <!-- <customer-plan
      v-if="planShow"
      :listData="planData"
      @close="planShow=false">
      </customer-plan> -->
      <lg-preview
        v-if="isPreviewShow"
        :index="previewIndex"
        :list="imageList"
        @close="isPreviewShow = false"
      ></lg-preview>
    </el-scrollbar>
  </div>
</template>

<script>
import {
  detailTabGetCommunicateAdd,
  detailTabCommunicateList,
  detailTabCommunicateSave,
  detailTabCommunicateOther,
  finishCommunicatePlan,
  hasCommunicatePlanFinish
} from '@/api/form-detail-tabs'
import { getTemplateEnableList } from '@/api/formData'

import { setCommunicate } from '@/api/ui-paas/detail-design'

import { mapGetters, mapActions, mapMutations } from 'vuex'
import { Message } from 'element-ui'
// 业务枚举
import commonBusinessType from '@/constants/common/business-type'
// 点赞评论
import commentAndLike from '@/components/common/comment-and-like/'
// import communicateBusiness from './../tabs/components/communicateBusiness'
// 表单渲染
import FormDataForm from '@/components/form-data-edit/form-data-form'
import uploadMix from '@/mixin/uploadMixin'
import detailMixin from './detail-mixin'
import tempVue from '@/utils/temp-vue'
import avatarImg from '@/components/base/avatar-img'
import LinkTableTable from '@/components/all-fields/form-data-edit/sys-saas/link-table/components/link-table-table'
import { setFormatData } from '@/components/form-data-edit/utils.js'
import AtWho from '@/components/at-who'
import communicateReccord from '@/components/form-data-detail/components/dialog/communicate-reccord'
import xbb from '@xbb/xbb-utils'
import Empty from '@/components/base/empty.vue'

export default {
  mixins: [uploadMix, detailMixin],
  provide: function (params) {
    return {
      ...this.formQuery,
      formDataFormInfo: 'baseDetailTab'
    }
  },
  props: {
    curLoading: {
      type: Number,
      default: 1
    },
    formQuery: {
      type: Object,
      default: () => {}
    },
    uiPaas: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageBusinessType: commonBusinessType,
      formDataFormShow: true, // 组件是否销毁
      isPreviewShow: false, // 查看图片弹窗
      imageList: [], //  图片
      previewIndex: 0, // 显示第几个图片文件
      serialNoData: undefined,
      // 跟进记录绑定值
      textareaValue: '',
      form: {},
      formData: {},
      explainList: [],
      itemData: [],
      rules: {},
      // 跟进记录
      refVisit: [],
      // 图片上传
      images: [],
      // apiUrl: api.path,
      // 当前获取评论的id
      currentId: '',
      // 分页信息
      // page: {
      //   size: 10,
      //   total: '',
      //   page: 1,
      //   rows: 1
      // },
      load: false,
      // 完成访客计划相关
      planData: [],
      planShow: false,
      // 是否禁止提交
      disSubmit: false,
      opUUID: '',
      currentOpenIndex: '', // 当前展开的扩展字段序号
      addnewProp: {},
      addnewShow: false,
      disableBefore: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      bottomResult: [],
      customerId: '',
      appInfo: {},
      communicateFieldList: [],
      communicateFormData: {},
      // 新建跟进记录同步完成访客计划弹窗
      isVisible: false,
      selectRows: [],
      finishPlanData: {},
      communicatePlanId: 0,
      fileAndImg: [],
      fileAndImgVal: {},
      elementTemplate: {
        front: '<span class="at-person">',
        end: '</span>'
      },
      communicateFormQuery: {}
    }
  },
  created() {
    this.init()
  },
  beforeDestroy() {
    // 保存草稿
    utils.LS.set(`VUE-${this.query.businessType}-communicate-draft`, this.textareaValue)
  },
  computed: {
    ...mapGetters(['getAttr', 'saasObj']),
    // 是否显示加载更多
    showLoadmore() {
      return (
        this.page.rowsCount > this.page.pageSize && this.page.pageTotal > this.page.currentPageNum
      )
    },
    // 跟进记录业务
    communicateBusinessType() {
      if (
        [this.pageBusinessType.SUPPLIER, this.pageBusinessType.PURCHASE].indexOf(
          this.query.businessType
        ) > -1
      ) {
        // 进销存跟进记录502
        return 502
      } else {
        // CRM跟进记录501
        return 501
      }
    }
  },
  methods: {
    ...mapActions(['setAttr', 'formDataEdit']),
    ...mapMutations({
      setsSaasObj: 'SET_FORM_SAASOBJ',
      setCommunicateEntity: 'COMMUNICATE_ENTITY'
    }),

    thumbnail(img, size) {
      return xbb.thumbnail(img, size)
    },

    // created
    init() {
      this.formData = {}
      this.page.currentPageNum = 1
      this.refVisit = []
      this.getRefVisit()
      this.getCommunicateList()
      // // 获取必填选项
      // this.$nextTick(() => {
      //   this.getCheckRequire()
      // })
      // this.makeopUUID()

      // // 读取草稿
      // this.textareaValue = utils.LS.get(`VUE-${this.query.businessType}-communicate-draft`) || ''
    },
    // 供应商无权限
    unOpen() {
      Message({
        type: 'error',
        message: this.$t('formDataDetail.tabs.noPermissionForSupplierField')
      })
    },

    // 跟进记录提交成功
    dynamicsSubmit() {
      setTimeout(() => {
        // 加一个延时解决添加第二条跟进记录不能及时同步的问题
        this.getCommunicateList()
      }, 500)
    },

    // 跟进记录列表
    getCommunicateList() {
      const request = {
        ...this.formQuery,
        parentBusinessType: this.query.businessType,
        parentId: this.query.dataId,
        page: this.page.currentPageNum,
        pageSize: this.page.pageSize
      }
      // 跟进记录列表
      detailTabCommunicateList(request)
        .then((data) => {
          console.log('我提交了')
          // 注释全部放开
          this.page.rowsCount = data.result.pageHelper.rowsCount
          this.page.pageTotal = data.result.pageHelper.pageTotal
          data.result.list = data.result.list.map((item) => {
            item.report = item.like = false
            return item
          })

          // 通过page.currentPageNum来判断当前是刷新还是下拉加载更多
          if (this.page.currentPageNum === 1) {
            this.refVisit = data.result.list
          } else {
            this.refVisit = this.refVisit.concat(data.result.list)
          }
        })
        .catch(() => {})
    },

    // 跟进记录页面初始
    getRefVisit() {
      this.formDataFormShow = true
      this.appInfo = {
        businessType: this.communicateBusinessType, // 跟进记录业务
        appId: this.query.appId
      }
      const copyQuery = JSON.parse(JSON.stringify(this.formQuery))
      delete copyQuery.businessType // 去掉formQuery的businessType
      const request = {
        ...copyQuery, // 传入对应的表单菜单应用信息
        parentBusinessType: this.query.businessType,
        parentId: this.query.dataId,
        businessType: this.query.businessType
      }
      if (this.isDesign) {
        setCommunicate(request)
          .then((res) => {
            this.explainList = res.result.explainList.filter((item) => {
              if ((item.fieldType === 8 || item.fieldType === 6) && !item.isRedundant) {
                this.fileAndImg.push(item)
                this.fileAndImgVal[item.attr] = []
              }
              return item
            })
          })
          .catch(() => {})
      } else {
        detailTabGetCommunicateAdd(request)
          .then((data) => {
            this.fileAndImg = []
            this.explainList = data.result.explainList.filter((item) => {
              if ((item.fieldType === 8 || item.fieldType === 6) && !item.isRedundant) {
                this.fileAndImg.push(item)
                this.fileAndImgVal[item.attr] = []
              }
              return item
            })
            this.recodeFormId = data.result.paasFormEntity.id
            this.formData = setFormatData({}, this.explainList)
            this.communicateFormQuery = data.result.paasFormEntity
            this.setsSaasObj(data.result.saasObj) // 详情页跟进记录联系人选择要传关联的ID
          })
          .catch(() => {})
      }
    },
    // 加载更多
    loadmore() {
      this.page.currentPageNum++
      this.getRefVisit()
    },
    // 解析JSON数组
    strToArr(str) {
      if (str instanceof Array) {
        return str
      }
      return xbb.json(str)
    },
    fileData(arr) {
      let arrList
      arr.forEach((element) => {
        if (element.fieldType === 8) {
          arrList = element.value4Show
        }
      })
      return arrList || ''
    },
    strToArrItem(str) {
      if (str) {
        return str.split('|')
      }
      return []
    },

    // 过滤为空数组的数据
    formDataFilter(formData) {
      const newFormData = JSON.parse(JSON.stringify(formData))
      const dataList = newFormData.dataList
      const newDataList = JSON.parse(JSON.stringify(newFormData.dataList))
      newFormData.dataList = Object.assign({}, newFormData.dataList, this.fileAndImgVal)
      Object.keys(newDataList).forEach((item) => {
        if (Array.isArray(newDataList[item]) && newDataList[item].length === 0) {
          delete dataList[item]
        }
      })
      console.log(newFormData)
      return newFormData
    },

    // 提交跟进记录
    handleSubmit() {
      // 校验
      this.$refs['formDataForm'].validate((formData) => {
        // 校验跟进记录备注
        if (!this.textareaValue) {
          Message({
            type: 'error',
            message: this.$t('formDataDetail.pleaseInputRecord')
          })
          return
        } else if (this.fileAndImg.length) {
          let isReturn = false
          for (const index in this.fileAndImg) {
            const item = this.fileAndImg[index]
            if (item.required && !this.fileAndImgVal[item.attr].length) {
              Message({
                type: 'error',
                message: this.$t('formDataDetail.pleaseAddSystem') + item.defaultName
              })
              isReturn = true
              break
            }
          }
          if (isReturn) return
        }
        formData = this.formDataFilter(formData)
        this.sendData(formData)
      })
    },
    sendData(formData) {
      const atUserIds = this.$refs.at.getInfo().templateArr
      const content = this.$refs.at.getInfo().template
      const request = {
        ...this.formQuery,
        menuId: this.communicateFormQuery.menuId,
        ...formData,
        saasNeedRedundantAttrPoJo: {
          memo: content,
          // memo: this.textareaValue,
          parentBusinessType: this.query.businessType,
          parentId: this.query.dataId,
          atUserIds: atUserIds
        }
      }
      this.$refs.at.closePanel()
      detailTabCommunicateSave(request)
        .then((res) => {
          Message({
            type: 'success',
            message: res.msg || this.$t('message.operateSuccessSymbol')
          })

          // 当前所建跟进记录的id
          this.communicatePlanId = res.result.formDataId
          // 判断是否
          this.hasCommunicatePlanFinish(formData)

          this.textareaValue = ''
          this.$refs['formDataForm'].resetFields()
          console.log(this.$refs)
          // this.fileAndImgVal = {}
          this.$refs.uploadFile[0] &&
            this.$refs.uploadFile[0].clearFiles() &&
            this.$refs.uploadFile[0].abort()
          this.$refs.uploadImg[0] &&
            this.$refs.uploadImg[0].clearFiles() &&
            this.$refs.uploadImg[0].abort()
          this.getCommunicateList()
          setTimeout(() => {
            this.init() // 更新跟进记录
            tempVue.$emit('updateFormList') // 跟新列表页
          }, globalConst.DELAY_TIME)
          this.textareaValue = ''
          this.formDataFormShow = false
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          // 一秒钟之内，禁止多次重复提交
          this.disSubmit = true
          setTimeout(() => {
            this.disSubmit = false
          }, 1000)
        })
    },

    // 是否存在可以同步完成的访客计划
    hasCommunicatePlanFinish(formData) {
      const formDataTmp = JSON.parse(JSON.stringify(formData))

      // 取当前所建的跟进记录的拜访时间当天的时间值
      let attr = ''
      this.explainList.forEach((item) => {
        if (item.saasAttr === 'communicateTime') {
          attr = item.attr
        }
      })
      const communicateTime = formDataTmp.dataList[attr]
      const timeObj = xbb.timestampToTime(communicateTime)
      const date = timeObj.year + '-' + timeObj.month + '-' + timeObj.day
      const conditions =
        this.saasObj.conditions &&
        this.saasObj.conditions.map((item) => {
          if (item.attr === attr) {
            this.$set(item, 'value', [
              new Date(date + ' 00:00:00').getTime() / 1000,
              new Date(date + ' 23:59:59').getTime() / 1000
            ])
          }
          return item
        })

      // 访客计划弹框请求需要的参数
      this.finishPlanData = {
        parentBusinessType: this.saasObj.parentBusinessType || 100,
        parentId: this.saasObj.linkParentId,
        conditions: conditions
      }

      // 获取访客计划模版id
      this.getTemplateEnableList().then((list) => {
        const params = {
          ...this.finishPlanData,
          businessType: 601,
          conditions: conditions,
          saasMark: 1,
          sourceBusinessType: this.formQuery.businessType,
          formId: list[0].formId
        }
        hasCommunicatePlanFinish(params)
          .then(
            ({
              result: {
                saasObj: { finishPlan }
              }
            }) => {
              if (finishPlan) {
                this.isVisible = true
              }
            }
          )
          .catch((err) => {
            console.log(err)
          })
      })
    },

    // 获取访客计划模版列表
    getTemplateEnableList() {
      return new Promise((resolve, reject) => {
        getTemplateEnableList({
          businessType: 601,
          saasMark: this.$route.query.saasMark,
          menuId: this.$route.query.menuId
        })
          .then(({ result: { formList } }) => {
            resolve(formList)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },

    // 访客计划完成保存
    communicatePlanSave() {
      if (!this.selectRows.length) {
        Message({
          type: 'warning',
          message: this.$t('message.selectVisitorPlan')
        })
        return
      }

      const params = {
        communicateDataId: this.communicatePlanId,
        linkPlanArr: this.selectRows
      }
      finishCommunicatePlan(params)
        .then((res) => {
          Message({
            type: 'success',
            message: res.msg || this.$t('message.operateSuccessSymbol')
          })
          this.selectRows = []
          this.isVisible = false
        })
        .catch(() => {})
    },

    tagClose(index, item) {
      this.selectRows.splice(index, 1)
    },
    // 回复
    handleReport(index) {
      this.refVisit[index].report = !this.refVisit[index].report
    },
    // 展示点赞者头像
    showLike(num, index) {
      if (num) {
        this.refVisit[index].like = true
      }
    },
    // 隐藏点赞者头像
    hideLike(num, index) {
      if (num) {
        this.refVisit[index].like = false
      }
    },
    // 将换行内容分割为数组
    paragraph(str) {
      if (str) {
        // 分割后使用map遍历，避免节点内容为undefined
        return str.split('\n').map((item) => item || '&nbsp')
      } else {
        return []
      }
    },
    openLightbox(urlList, previewIndex) {
      this.imageList = urlList
      this.previewIndex = previewIndex
      this.isPreviewShow = true
    },
    // 移除图片
    imgRemove(file, attr) {
      if (file) {
        const arr = this.fileAndImgVal[attr]
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].indexOf(file.url) > -1 || arr[i].indexOf(file.uid) > -1) {
            arr.splice(i, 1)
          }
        }
      }
    },

    // 根据时间戳生成hash
    makeopUUID() {
      const d = new Date()
      this.opUUID = d.getTime()
    },
    // 编辑跟进记录
    editItem(item) {
      const copyQuery = JSON.parse(JSON.stringify(this.formQuery))
      delete copyQuery.businessType // 去掉businessType使用跟进记录businessType
      delete copyQuery.dataId // 去掉dataId使用跟进记录id
      const params = {
        ...copyQuery,
        formId: this.recodeFormId, // 跟进记录自己的formId
        businessType: this.communicateBusinessType, // 跟进记录
        dataId: item.id,
        subBusinessType: this.communicateBusinessType, // 跟进记录
        menuId: this.communicateFormQuery.menuId
      }

      //记录跟进记录编辑的实体
      this.setCommunicateEntity(item.communicateBusiness.entity)

      this.formDataEdit(params)
    },
    // 删除跟进记录
    handleDelete(dataId) {
      // let copyQuery = JSON.parse(JSON.stringify(this.formQuery))
      // delete copyQuery.businessType // 去掉businessType使用跟进记录businessType
      // delete copyQuery.dataId // 去掉dataId使用跟进记录id
      const params = {
        appId: this.communicateFormQuery.appId,
        formId: this.communicateFormQuery.id,
        saasMark: this.communicateFormQuery.saasMark,
        menuId: this.communicateFormQuery.menuId,
        businessType: this.communicateBusinessType, // 跟进记录
        dataIdList: [dataId]
      }
      this.deleteSingle(params)
        .then(() => {
          // 延时刷新，因为ES
          setTimeout(() => {
            this.init()
          }, globalConst.DELAY_TIME)
        })
        .catch(() => {})
    },

    saveObj(obj) {
      this.form.contactArray = obj
    },

    // 判断文件名或uid，有任意一个则视为相等
    fileRemove(file, attr) {
      if (file) {
        const arr = this.fileAndImgVal[attr]
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].attachIndex === file.url || arr[i].uid === file.uid) {
            arr.splice(i, 1)
          }
        }
      }
    },

    fileAdd(response, file, attr) {
      const newFile = {
        filename: file.name,
        attachIndex: this.fileUrl[file.uid],
        ext: file.name.replace(/.+\./, '').toLowerCase(),
        size: file.size,
        uid: file.uid
      }
      this.fileAndImgVal[attr].push(newFile)
    },

    // 上传图片
    imgAdd(response, file, attr) {
      const newImg = this.fileUrl[file.uid] + '?' + file.uid
      this.fileAndImgVal[attr] = this.fileAndImgVal[attr].concat(newImg)
    },

    // 展开or收起扩展字段信息
    redundantToggle(item, index) {
      if (this.currentOpenIndex === index) {
        this.currentOpenIndex = ''
      } else {
        this.currentOpenIndex = index
        const copyQuery = JSON.parse(JSON.stringify(this.query))
        delete copyQuery.businessType // 去掉businessType使用跟进记录businessType
        delete copyQuery.dataId // 去掉dataId使用跟进记录id
        delete copyQuery.formId // 去掉dataId使用跟进记录formId
        const request = {
          ...copyQuery,
          // formId: item.formId,
          // businessType: this.communicateBusinessType,
          // dataId: item.id
          ...item.entity // 后端跟进记录的list里的entity给了formId,dataId,businessType
        }
        detailTabCommunicateOther(request)
          .then((res) => {
            this.communicateFormData = res.result.data
            this.communicateFieldList = res.result.headList
          })
          .catch(() => {})
      }
    },
    linkItemClick(query) {
      this.seeChildrenDetail({
        saasMark: query.saasMark,
        businessType: query.businessType,
        entity: { dataId: query.dataId }
      })
    }
  },
  watch: {
    curLoading() {
      if (this.page.rows > this.page.page) {
        this.loadmore()
      }
    },
    // 是否重新获取跟进记录
    // refresh () {
    //   this.getRefVisit()
    // },

    // 监听异步验证字段
    getAttr(attr) {
      if (this.rules[attr]) {
        this.$refs.ruleForm.validateField(attr)
        this.setAttr('')
      }
    },
    // 监听是否需要加载下一页，如果需要加载下一页的话执行函数
    isLoadMore: function (newVal, oldVal) {
      console.log('isLoadMore', newVal)
      if (newVal) {
        this.loadNextPage(this.getCommunicateList)
      }
    }
  },
  components: {
    // Report,
    // Like,
    // customerPlan,
    // addNew,
    // communicateBusiness,
    FormDataForm,
    CommentAndLike: commentAndLike,
    AvatarImg: avatarImg,
    LinkTableTable,
    AtWho,
    CommunicateReccord: communicateReccord,
    // associatedField
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      ),
    Empty
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/object/popover-upload-img.scss';
.item-content .file-content .render .file .operate {
  margin-left: 120px;
}
.el-dropdown-menu {
  .el-checkbox-group {
    p {
      padding: 4px 10px;
    }
  }
}
.communicate-recorders {
  .load-more {
    padding: 10px;
    margin-bottom: 30px;
    text-align: center;
  }
  .menu {
    .el-checkbox__inner {
      z-index: 0;
    }
  }
  .opt-item {
    position: relative;
    .el-upload-list {
      position: absolute;
      top: 28px;
      left: 0;
      z-index: 50;
      width: 230px;
      background-color: $base-white;
      border-radius: 2px;
      box-shadow: 0 0 4px 0 rgba(114, 114, 114, 0.5);
    }
  }
}
</style>

<style lang="scss" scoped>
.communicate-recorders {
  .default {
    p {
      color: $text-auxiliary;
      word-break: break-all;
    }
    .render {
      display: flex;
      .label {
        flex: 0 0 auto;
      }
    }
  }
  .communicate {
    position: relative;
    padding: 15px;
    background: $base-white;
    background-color: $neutral-color-1;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
    :deep(.el-date-editor) {
      width: 100%;
    }
    .item-select {
      white-space: nowrap;
    }
    .is-error {
      .item-select {
        border: 1px solid #ff4949;
      }
    }
    .selecte-top {
      box-sizing: border-box;
      width: 100%;
      padding: 10px 12px 6px 12px;
      background: $neutral-color-1;
      .menu {
        display: flex;
        flex-wrap: wrap;
        .opt-item {
          box-sizing: border-box;
          width: 47%;
          margin-right: 12px;
          margin-bottom: 20px;
        }
        .el-form-item {
          margin-bottom: 20px;
        }
        .contactArray {
          &.el-form-item {
            overflow: hidden;
          }
        }
      }
    }
    .form-data-form {
      overflow: hidden;
    }
    .enter {
      box-sizing: border-box;
      width: 100%;
      height: 126px;
      padding: 8px;
      font-size: 13px;
      line-height: 22px;
      color: $text-main;
      resize: none;
      border: none;
    }
    .options {
      position: relative;
      height: 44px;
      .menu {
        padding: 13px 0 0 18px;
        :deep(.el-badge__content) {
          height: 14px;
          padding: 0px 4px;
          font-size: 12px;
          line-height: 14px;
          background-color: $neutral-color-4;
        }
        .opt-item {
          display: inline-block;
          padding-right: 48px;
          font-size: 13px;
          &:first-child {
            padding-top: 1px;
            padding-bottom: 1px;
          }
          &:last-child {
            border-right-width: 0;
          }
          &.required::after {
            position: absolute;
            top: 2px;
            left: 10px;
            color: red;
            content: '*';
          }
          .text {
            font-size: 18px;
            color: #c4d1eb;
            word-break: break-all;
            cursor: pointer;
          }
        }
      }
      .submit {
        position: absolute;
        top: 14px;
        right: 35px;
        padding: 6px 15px;
      }
      .cancel {
        position: absolute;
        top: 7px;
        right: 10px;
        padding: 6px 15px;
      }
    }
    .button-out {
      text-align: right;
      .el-button {
        display: inline-block;
        margin-top: 15px;
      }
    }
  }
  .list-item {
    position: relative;
    margin-top: 16px;
    background: $base-white;
    border: 1px solid $neutral-color-3;
    border-radius: 8px;
    .item-content {
      .default {
        padding: 15px 15px 0;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        &.no-padding {
          padding: 15px 0;
        }
      }
      .user-infos {
        position: relative;
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        padding: 15px 15px 0 70px;
        .title {
          font-size: 0;
          line-height: 1.4;
          .name {
            font-size: 14px;
          }
          .tag {
            margin-left: 10px;
            font-size: 13px;
            color: $text-plain;
          }
        }
        .avatar {
          position: absolute;
          top: 15px;
          left: 15px;
          width: 44px;
          height: 44px;
          img {
            display: block;
          }
        }
        .others {
          display: flex;
          padding: 10px 0 2px;
          .text {
            display: inline-block;
            padding-right: 12px;
            font-size: 13px;
            line-height: 1;
            color: $text-plain;
            &:last-child {
              max-width: 680px;
              padding-right: 0;
              padding-left: 11px;
              margin-right: 0;
              border-left: 1px solid $text-plain;
            }
          }
          .record-type {
            max-width: 91px;
          }
        }
        .options {
          background-color: inherit !important;
          .text {
            position: relative;
            display: inline-block;
            padding-right: 14px;
            margin-right: 10px;
            font-size: 13px;
            line-height: 1;
            color: $link-base-color-6;
            cursor: pointer;
            border-right: 1px solid $neutral-color-4;
            &:last-child {
              padding-right: 0;
              margin-right: 0;
              border-right-width: 0;
            }
            &.red {
              color: $error-base-color-6;
            }
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
      .gallery {
        margin: 20px 15px 0;
        .gallery-wrap {
          font-size: 0;
          .img {
            display: inline-block;
            width: 80px;
            height: 80px;
            margin-right: 19px;
            overflow: hidden;
          }
        }
      }
      .article {
        padding: 20px 15px 0;
        font-size: 14px;
        line-height: 1.6;
        text-align: justify;
        word-break: break-all;
        .talk {
          margin-right: 4px;
          color: $link-base-color-6;
        }
      }
      .desc {
        padding: 9px;
        margin: 20px 15px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        background: $base-white;
        border: 1px solid $neutral-color-3;
        .text {
          display: inline-block;
          padding-right: 12px;
          margin-right: 12px;
          font-size: 13px;
          line-height: 1;
          color: $text-plain;
          border-right: 1px solid $neutral-color-4;
          &:last-child {
            padding-right: 0;
            margin-right: 0;
            border-right-width: 0;
          }
        }
      }
      .other-desc {
        padding: 5px 10px;
        margin: 15px 15px 0;
        background-color: $neutral-color-2;
        .o-d {
          display: flex;
          padding: 5px 0;
          font-size: 13px;

          .label {
            flex: 0 0 100px;
            width: 100px;
            color: $text-auxiliary;
          }
          .img {
            img {
              display: inline-block;
              height: 40px;
            }
          }
        }
      }
    }
    .flex {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
    }
    .item-options {
      position: relative;
      text-align: right;
      background-color: $neutral-color-1;
      .option {
        position: relative;
        display: inline-block;
        padding-right: 12px;
        margin-right: 12px;
        font-size: 13px;
        line-height: 1;
        color: $text-plain;
        border-right: 1px solid $neutral-color-4;
        &:last-child {
          padding-right: 0;
          margin-right: 0;
          border-right-width: 0;
        }
        &:before {
          position: absolute;
          top: 23px;
          left: 28px;
          z-index: 2;
          display: none;
          width: 10px;
          height: 10px;
          content: '';
          background-color: $base-white;
          box-shadow: 0 0 1px 1px #cdd9eb inset;
          transform: rotate(45deg);
        }
        &:after {
          position: absolute;
          top: 28px;
          left: 27px;
          z-index: 3;
          display: none;
          width: 12px;
          height: 7px;
          content: '';
          background-color: $base-white;
        }
        &.active {
          &:before {
            display: block;
          }
          &:after {
            display: block;
          }
        }
        .icon {
          font-size: 12px;
          vertical-align: middle;
        }
        .text {
          display: inline-block;
          margin-left: 2px;
          vertical-align: middle;
          .num {
            font-style: normal;
          }
        }
      }
    }
  }
}
.no-data {
  color: $text-plain;
  text-align: center;
}
.communicate-plan-dialog {
  .dialog-header {
    height: 76px;
    padding: 8px;
    margin-bottom: 16px;
    border: 1px solid $neutral-color-3;
  }
  .dialog-table {
    border: 1px solid $neutral-color-3;
  }
}
</style>
