/*
 * @Description: 详情页的公告mixin
 */
import { mapActions } from 'vuex'
import FormDataForm from '@/components/form-data-edit/form-data-form'
import UserListSelectDialog from '@/components/system/user-list-select-dialog'
// 业务枚举
import commonBusinessType from '@/constants/common/business-type'
import BadSeal from './components/bad-seal'
import PartBadSeal from './components/part-bad-seal'
import { deleteSingle } from '@/api/batch-handle'
import tempVue from '@/utils/temp-vue'
import FollowRecordFast from '@/components/common/create-follow-record-fast'
import specialJudge from '@/utils/form-data-special-judge'
import { workOrderV2DeleteSingle } from '@/api/work-order-v2/common.js'

export default {
  components: {
    FormDataForm,
    UserListSelectDialog,
    BadSeal,
    PartBadSeal,
    FollowRecordFast
  },
  inject: {
    parentTab: {
      default: () => ({})
    },
    parentDetail: {
      default: () => null
    }
  },
  computed: {
    isActive() {
      return this.activeNameModel === this.tabInfo.attr
    }
  },
  watch: {
    isActive: {
      handler(val) {
        // 激活刷新
        if (val) {
          this.init && this.init()
        }
      }
    }
  },
  props: {
    formQuery: {},
    activeNameModel: {
      // 当前激活的tab
    },
    tabInfo: {
      // 当前tab对象
      required: true,
      default: () => {
        return {}
      }
    },
    query: {
      required: true,
      default: () => {
        return {}
      }
    },
    isLoadMore: {
      // 部分标签页加载下一页
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 点击跳转标识,竞争对手全景跳转至销售机会
    jumpSign: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      pageBusinessType: commonBusinessType,
      statusVal: '',
      dataRange: [],
      loading: false,
      page: {
        rowsCount: 0,
        pageList: [], // 数据对象
        currentPageNum: 1,
        pageSize: 20,
        pageTotal: 1
      },
      scrollBarViewStyle: {
        'padding-right': '10px'
      },
      pickerOptions2: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  methods: {
    ...mapActions(['formDataAdd', 'formDataEdit']),
    /**
     * 查看子详情
     */
    seeChildrenDetail(item) {
      let params
      if (item.entity) {
        const {
          saasMark,
          businessType,
          distributorMark,
          entity: { dataId, appId, menuId, formId }
        } = item
        params = {
          dataId,
          appId,
          menuId,
          formId,
          saasMark,
          distributorMark,
          businessType
        }
      } else {
        const { saasMark, businessType, dataId, appId, menuId, formId } = item
        params = {
          dataId,
          appId,
          menuId,
          formId,
          saasMark,
          businessType
        }
      }
      this.parentDetail && this.parentDetail.showChildrenDetail(params)
    },
    /**
     * 批量删除
     */
    deleteSingle(data) {
      return new Promise((resolve, reject) => {
        this.$confirm(
          this.$t('formDataDetail.sureDeleteThisData'),
          this.$t('message.confirmTitle'),
          {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          }
        )
          .then(() => {
            const isWorkOrderV2 = specialJudge.isWorkOrderV2(data.businessType)
            let deleteSingleApi = deleteSingle
            let deleteParams = {
              ...data
              // 'appId': '134', // 应用id
              // 'menuId': 4, // 菜单id
              // 'formId': 4, // 表单id
              // 'saasMark': 1, // saas标识
              // 'businessType': '' // 业务类型
            }
            if (isWorkOrderV2) {
              deleteSingleApi = workOrderV2DeleteSingle
              deleteParams = {
                ...deleteParams,
                singleFlag: 0
              }
            }

            deleteSingleApi(deleteParams)
              .then(({ result, msg }) => {
                this.$message({
                  type: 'success',
                  message: msg
                })
                resolve(result)
                tempVue.$emit('updateFormList') // 跟新列表页
              })
              .catch(() => {})
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: this.$t('message.cancelDel')
            })
            reject()
          })
      })
    },
    // 滚动到底时进行加载下一页
    loadNextPage(cb) {
      setTimeout(() => {
        if (this.page.currentPageNum >= this.page.pageTotal) {
          return false
        } else {
          this.page.currentPageNum++
          console.log('onScroll!!!')
          cb && cb()
        }
      }, 300)
    }
  }
}
