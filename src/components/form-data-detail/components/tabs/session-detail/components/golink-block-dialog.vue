<!--
 * @Description: 会话记录详情-关联客户弹窗
 -->
<template>
  <div>
    <!-- 弹窗部分 -->
    <el-dialog
      v-if="dialogVisible"
      append-to-body
      title="客户管理"
      top="8vh"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <div class="field-input-block__dialog">
        <div class="field-input-block__dialog--header">
          <div class="slot__header" style="height: 100%">
            <el-scrollbar class="slot__header--scrollbar" style="height: 100%">
              <el-tag
                v-for="(item, index) in selectRows"
                :key="(item && item.id) || index"
                class="slot__header--tag"
                closable
                disable-transitions
                @close="tagClose(index, item)"
              >
                {{ (item && item.name) || (item && item.id) }}
              </el-tag>
            </el-scrollbar>
          </div>
        </div>
        <div class="field-input-block__dialog--body">
          <LinkTableTable
            :app-id-params="{ appId: formQuery.appId }"
            business-name="客户管理"
            :field-info="{ saasAttr: 'customerName' }"
            :link-business-type="100"
            :multiple="false"
            :select-rows.sync="selectRows"
          />
        </div>
      </div>
      <div slot="footer">
        <slot name="footer">
          <el-button @click="cancel">{{ $t('operation.cancel') }}</el-button>
          <el-button type="primary" @click="submit">{{ $t('operation.confirm') }}</el-button>
        </slot>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LinkTableTable from '@/components/all-fields/form-data-edit/sys-saas/link-table/components/link-table-table'

export default {
  name: 'GoLinkBlockDialog',
  components: {
    LinkTableTable
  },
  provide: {
    saasMark: 1
  },
  props: {
    formQuery: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectRows: []
    }
  },
  methods: {
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
      Object.assign(this.$data, this.$options.data()) // 所有参数init
    },
    submit() {
      this.dialogVisible = false
      this.$emit('submit', this.selectRows[0])
      Object.assign(this.$data, this.$options.data()) // 所有参数init
    },
    tagClose(index, item) {
      this.selectRows.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
body {
  .slot__header {
    height: 100%;
    // padding-bottom: 2px;
    // padding-top: 8px;
    // padding-bottom: 3px;
    overflow: scroll;
    line-height: 30px;
    & > .slot__header--scrollbar {
      & > .el-scrollbar__wrap {
        overflow-x: hidden;
        & > .el-scrollbar__view {
          display: flex;
          flex-wrap: wrap;
          padding-top: 8px;
          padding-right: 8px;
          padding-bottom: 3px;
          // justify-content: space-around;
          .slot__header--tag {
            margin-bottom: 5px;
            margin-left: 8px;
            // margin: {
            //   top:1px;
            //   left: 8px;
            //   right: 1px;
            //   bottom: 8px;
            // }
          }
        }
      }
    }
  }
  .field-input-block__dialog {
    min-height: 50px;
    & > .field-input-block__dialog--header {
      height: 76px;
      margin-bottom: 16px;
      border: 1px solid $neutral-color-3;
    }
    & > .field-input-block__dialog--body {
      border: 1px solid $neutral-color-3;
    }
  }
}
.field-input-block {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-left: 0px;
  & > .field-input-block__content {
    @include singleline-ellipsis;
    position: relative;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    align-items: center;
    height: 100%;
    padding-right: 0px;
    padding-left: 6px;
    &--readonly::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      cursor: not-allowed;
      content: '';
    }
  }
  & > .field-input-block__suffix {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    padding: 0px 10px;
    font-size: 14px;
    color: $link-base-color-6;
    cursor: pointer;
    user-select: none;
  }
  & > .field-input-block__suffix--readonly {
    color: $text-grey;
    cursor: not-allowed;
  }
}
.fund-table {
  overflow: scroll;
}
.styleFix {
  width: 100%;
}
</style>
