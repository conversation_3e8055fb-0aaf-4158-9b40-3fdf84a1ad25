<template>
  <div class="tab-view" @touchmove.prevent>
    <!-- left -->
    <!-- v-show 只有在tree视图下才有编辑 -->
    <div v-show="valueCompt === 'tree' && tabViewList.includes('edit')" class="tab-view__left">
      <!-- edit-tree 树编辑 -->
      <el-tooltip content="编辑" effect="dark" placement="top">
        <span><i class="web-icon-bianji web-iconfont" @click="toEdit"></i></span>
      </el-tooltip>
    </div>
    <!-- right -->
    <div class="tab-view__right">
      <!-- tree树状图 -->
      <template v-if="tabViewList.includes('tree')">
        <el-tooltip content="树状图" effect="dark" placement="top">
          <i
            class="icon-cluster iconfont"
            :class="{ active: valueCompt === 'tree' }"
            @click="changeTab('tree')"
          ></i>
        </el-tooltip>
        <span class="gap"></span>
      </template>
      <!-- list列表视图 -->
      <el-tooltip
        v-if="tabViewList.includes('table')"
        content="列表视图"
        effect="dark"
        placement="top"
      >
        <i
          class="icon-table iconfont"
          :class="{ active: valueCompt === 'table' }"
          @click="changeTab('table')"
        ></i>
      </el-tooltip>
      <span class="gap"></span>
      <!-- 卡片视图（待补充） -->
      <el-tooltip
        v-if="tabViewList.includes('card')"
        content="卡片视图"
        effect="dark"
        placement="top"
      >
        <i
          class="web-icon-appstore web-iconfont"
          :class="{ active: valueCompt === 'card' }"
          @click="changeTab('card')"
        ></i>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import utils from '@/utils'

export default {
  name: 'TabView',

  model: {
    prop: 'tabVal',
    event: 'tabChange'
  },

  components: {},

  filters: {},

  mixins: [],

  props: {
    valueStorageKey: {
      type: String,
      default: 'customerContactsDetailTabView',
      required: true
    },
    tabVal: {
      type: String,
      default: '',
      required: true
    },
    tabViewList: {
      type: Array,
      default: () => ['edit', 'tree', 'table', 'card']
    }
  },

  data() {
    return {}
  },

  computed: {
    valueCompt: {
      set(val) {
        utils.SS.set(this.valueStorageKey, val)
        this.$emit('tabChange', val)
      },
      get() {
        return this.tabVal
      }
    }
  },

  watch: {},

  methods: {
    changeTab(val) {
      this.valueCompt = val
      this.$emit('callback', {
        type: 'button',
        value: { value: val }
      })
    },
    // 跳转到编辑
    toEdit() {
      this.$emit('callback', {
        type: 'button',
        value: { label: '编辑', value: 'edit' }
      })
    }
  },

  created() {}
}
</script>

<style lang="scss" scoped>
.tab-view {
  display: flex;
  align-items: center;
  justify-content: center;
  &__left {
    margin-right: 12px;
  }
  &__right {
    .gap {
      display: inline-block;
      width: 1px;
      height: 20px;
      margin: 0 12px;
      background-color: rgba(216, 216, 216, 1);
    }
  }
  &__left,
  &__right {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color: $base-white;
    border: 1px solid rgba(229, 229, 229, 1);
    border-radius: 4px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
    // icon操作
    i {
      font-size: 16px;
      cursor: pointer;
      &:hover {
        color: $brand-color-5;
      }
      &.active {
        color: $brand-color-5;
      }
    }
  }
}
</style>
