<template>
  <div>
    <FlowLog
      :task-info="flowLogInfo"
      @deleteComment="deleteComment"
      @editDialogFormData="editDialogFormData"
    />
    <TalkDialog
      v-if="talkDialogShow"
      :dialog-form-data="dialogFormData"
      :show.sync="talkDialogShow"
      @talk-submit="talkSubmit"
    />
  </div>
</template>

<script>
import FlowLog from '@/views/app/work-flow/flowApprove/flowLog/index.vue'
import TalkDialog from '@/views/app/flow/flowApprove/talkDialog/index.vue'
import { getTabApproveRecords } from '@/api/form-detail-tabs'
import { delComment, addComment, editComment } from '@/api/new-approve'
// import { gioTabApprovalRecords } from '@/utils/buriedPoint'

export default {
  name: 'ApprovalRecords',
  components: {
    FlowLog,
    TalkDialog
  },
  props: {
    query: {
      type: Object
    }
  },
  data() {
    return {
      talkDialogShow: false,
      dialogFormData: [],
      flowLogInfo: {},
      processTaskId: ''
    }
  },
  mounted() {
    // gioTabApprovalRecords()
    this.getTabApproveRecords()
  },
  methods: {
    deleteComment(id) {
      delComment({ commentId: id, refId: this.processTaskId })
        .then((res) => {
          if (res.success === true) {
            this.$message({
              message: res.msg || '操作成功',
              type: 'success'
            })
            this.getTabApproveRecords()
          }
        })
        .catch(() => {})
    },
    editDialogFormData(item) {
      this.dialogFormData = item
      this.talkDialogShow = true
    },
    talkSubmit(form, data, editId) {
      if (editId) {
        this.editTalk(form, data, editId)
      } else {
        this.addTalk(form, data)
      }
    },
    addTalk({ images, attachmentList }, data) {
      const { content, atUserIds } = data
      addComment({
        refId: this.processTaskId, //  引用id 对应流程的processTaskId
        refUserId: '', // 被回复人user_sid
        content: content, // 评论内容 必传
        images: images, // 图片
        attachmentList: attachmentList,
        atUserIds
      })
        .then((res) => {
          this.getTabApproveRecords()
        })
        .catch(() => {})
    },
    editTalk({ images, attachmentList }, data, editId) {
      const { content, atUserIds } = data
      editComment({
        appId: this.query.appId,
        refId: this.processTaskId, //  引用id 对应流程的processTaskId
        refUserId: '', // 被回复人user_sid
        content: content, // 评论内容 必传
        images: images, // 图片
        attachmentList: attachmentList,
        atUserIds,
        commentId: editId
      })
        .then((res) => {
          this.getTabApproveRecords()
        })
        .catch(() => {})
    },
    handleComment() {
      this.talkDialogShow = true
      this.dialogFormData = {}
    },
    getTabApproveRecords() {
      const { appId, businessType, formId, saasMark, menuId, dataId } = this.query
      getTabApproveRecords({
        menuId,
        businessType,
        saasMark,
        formId,
        appId,
        dataId
      }).then(({ result }) => {
        this.flowLogInfo = {
          currentLog: result.currentLog,
          historyLog: result.approvalHistoryLog,
          ccUserList: result.ccUserList
        }
        this.processTaskId = result.processTaskId
      })
    }
  }
}
</script>
