<!--
 * @Description: 合同、机会、采购合同、入库、出库、调拨、盘点、装配、生产
 -->
<template>
  <div class="product-list v-tabs" style="height: 100%">
    <el-scrollbar style="height: 100%">
      <div class="head">
        <div v-if="query.businessType !== pageBusinessType.INVENTORY" class="total">
          <!-- {{tableData.length}}种产品<span v-if="isShowTotal">{{totalProducts}}件</span> -->
          {{ summaryInfo }}
        </div>
        <div class="head-right">
          <el-button
            v-for="btn in topRightButton"
            :key="btn.attr"
            plain
            type="text"
            @click="addLinkClick(btn)"
            >{{ btn.value }}</el-button
          >
        </div>
      </div>
      <div class="content">
        <el-table border :data="tableData" highlight-current-row style="width: 100%">
          <el-table-column fixed label="序号" type="index" width="50"> </el-table-column>
          <el-table-column
            v-if="showProductImgs"
            fixed
            :label="$t('formDataDetail.tabs.productPictures')"
            width="75"
          >
            <template slot-scope="scope">
              <img
                :key="scope.row.id"
                v-lazy="thumbnail(scope.row.thumbnail, 40)"
                data-type="img"
                :data-url="scope.row.thumbnail"
                height="30"
                width="30"
              />
            </template>
          </el-table-column>
          <template v-for="(h, index) in explains">
            <el-table-column
              v-if="getReturn(h)"
              :key="h.attr"
              :fixed="['name', 'productNo', 'specification'].indexOf(h.saasAttr) > -1"
              :label="h.attrName"
              min-width="100"
              :prop="h.attr"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <template v-if="h.attr === 'warehouse'">
                  <span>{{ scope.row.result[index].attrValue.value }}</span>
                </template>
                <template v-else-if="h.attr === 'lackNum'">
                  <span :class="{ 'red-text': scope.row.result[index].attrValue > 0 }">{{
                    scope.row.result[index].attrValue
                  }}</span>
                </template>
                <template v-else-if="h.attr === 'productCost' && scope.row.result[index].hideCost">
                  <span>*****</span>
                </template>
                <template
                  v-else-if="
                    h.attr === 'productPrice' &&
                    scope.row.result[index].hidePrice &&
                    query.businessType !== pageBusinessType.PURCHASE
                  "
                >
                  <span>*****</span>
                </template>
                <!-- 销售出库 售价相关字段 -->
                <template
                  v-else-if="query.businessType === pageBusinessType.OUTSTOCK && h.showSalePrice"
                >
                  {{ scope.row.result[index].attrValue }}
                </template>
                <template v-else-if="[5, 6, 8, 10].indexOf(h.fieldType) === -1">
                  <span v-if="typeof scope.row.result[index].attrValue === 'number'">{{
                    scope.row.result[index].attrValue
                  }}</span>
                  <span v-else>{{ scope.row.result[index].attrValue || '--' }}</span>
                </template>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <!-- 要做分页，但是后台还没做，等后台做好再打开 -->
        <v-pagination
          :current-page.sync="pageHelper.page"
          :page-size="pageHelper.pageSize"
          :page-sizes="[5, 10, 20]"
          :total="pageHelper.rowsCount"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></v-pagination>
        <!-- 合同订单 -->
        <div v-if="query.businessType === pageBusinessType.CONTRACT" class="table-foot">
          <span class="item">产品售价合计：{{ others.totalProdcutMoney }}{{ others.unit }};</span>
          <span
            v-for="(item, index) in others.surchargeList"
            :key="index"
            class="item"
            :class="{ discount: item.surchargeType === 2 }"
            >{{ item.surchargeName }}: {{ item.amount | amount }}{{ others.unit }};</span
          >
        </div>
        <!-- 采购退货\ 退货退款 -->
        <div
          v-if="
            [pageBusinessType.RETURNED_PURCHASE, pageBusinessType.REFUND].indexOf(
              query.businessType
            ) > -1
          "
          class="table-foot"
        >
          <span class="item">产品合计：{{ others.totalProdcutMoney }}元;</span>
        </div>
        <!-- 工单备件 -->
        <div v-if="query.businessType === pageBusinessType.WORK_ORDER && others" class="table-foot">
          <span class="item">产品合计：{{ others.totalProdcutMoney }}元;</span>
        </div>
        <!-- 采购合同 -->
        <div v-if="[pageBusinessType.PURCHASE].indexOf(query.businessType) > -1" class="table-foot">
          <span class="item">产品合计：{{ others.totalProdcutMoney }}元;</span>
          <!-- <span class="item">折扣：{{others.discount}}%;</span>
          <span class="item">其他费用：{{others.otherCharge}}元</span> -->
        </div>
        <!-- 报价单 -->
        <div
          v-if="[pageBusinessType.QUOTATION].indexOf(query.businessType) > -1"
          class="table-foot"
        >
          <span class="item">产品报价合计：{{ others.totalProdcutMoney }}元;</span>
        </div>
        <!-- 出库单 -->
        <div v-if="query.businessType === pageBusinessType.OUTSTOCK" class="table-foot">
          <span class="item">出库数量总计：{{ others.outNumTotal }};</span>
          <span v-if="hideTotalCost" class="item"
            >出库成本总计：
            <span v-if="others.hideCost">*****</span>
            <span v-else>{{ others.outProductCostTotal }};</span>
          </span>
          <!-- 兼容老数据，会传来字符串'--' -->
          <span v-if="others.priceTotal || others.priceTotal === 0" class="item"
            >产品售价合计：{{ others.priceTotal }}</span
          >
        </div>
      </div>
    </el-scrollbar>
    <lg-preview
      v-if="isPreviewShow"
      :index="previewIndex"
      :list="imageList"
      @close="isPreviewShow = false"
    ></lg-preview>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import {
  getDetailBusinessProductList,
  getDetailBusinessProductListSummary
} from '@/api/form-detail-tabs'
// mixin
import detailMixin from './detail-mixin'
// 业务枚举
import commonBusinessType from '@/constants/common/business-type'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'ProductList',

  components: {
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },

  filters: {
    amount(value) {
      return value > 0 ? value : -value
    }
  },

  mixins: [detailMixin],

  props: {
    id: {
      type: Number
    }
  },
  data() {
    return {
      pageHelper: {
        pageSize: 10,
        rowsCount: 10,
        page: 1
      }, // 页面分页信息
      summaryInfo: '',
      isShowTotal: true, // 产品数量显示
      pageBusinessType: commonBusinessType, // 业务枚举
      isPreviewShow: false, // 查看图片弹窗
      imageList: [], //  图片
      previewIndex: 0, // 显示第几个图片文件
      explains: [],
      tableData: [],
      others: '',
      showProductImgs: false, // 图片
      hideTotalCost: false,
      topRightButton: []
    }
  },

  computed: {
    // totalProducts () {
    //   let i = 0
    //   this.tableData.forEach(item => {
    //     item.result.forEach(obj => {
    //       if (obj.attr === 'num') {
    //         i = xbb.plus(i, obj.attrValue)
    //       }
    //     })
    //   })
    //   return i
    // }
  },

  created() {
    this.init()
  },

  methods: {
    ...mapActions(['formDetailLinkAdd']),

    thumbnail(img, size) {
      return xbb.thumbnail(img, size)
    },

    init() {
      this.getList()
    },
    // explains列展示条件判断(销售出库4个特殊字段)
    getReturn(h) {
      if (
        ['saleProductPrice', 'saleSubtotal', 'salePrice', 'saleDiscount'].indexOf(h.attr) === -1
      ) {
        return true
      } else {
        return !!h.showSalePrice
      }
    },
    getList() {
      const params = {
        ...this.query,
        ...this.pageHelper
      }
      if (this.query.businessType === this.pageBusinessType.SUPPLIER) {
        this.isShowTotal = false
      }
      if (this.activeNameModel === 'outAssembleProductList') {
        params.fromOutstock = 1
      }
      getDetailBusinessProductListSummary(params).then((data) => {
        this.summaryInfo = data.result.summaryInfo
      })
      getDetailBusinessProductList(params)
        .then((data) => {
          this.pageHelperExchange(data.result.pageHelper)
          this.explains = data.result.product.explains
          this.tableData = data.result.product.productList
          this.others = data.result.product.others
          this.topRightButton = data.result.topRightButton
          if (
            data.result.product.others &&
            typeof data.result.product.others.outProductCostTotal === 'number'
          ) {
            this.hideTotalCost = true
          }
          // 图片
          this.tableData.forEach((item) => {
            if (item.thumbnail) {
              this.showProductImgs = true
            }
          })
        })
        .catch(() => {})
    },
    /**
     * @description: 分页大小改变
     * @param {type}
     * @return:
     */
    handleSizeChange(val) {
      this.pageHelper.pageSize = val
      this.getList()
    },
    /**
     * @description: 当前页改变
     * @param {type}
     * @return:
     */
    handleCurrentChange(val) {
      this.pageHelper.page = val
      this.getList()
    },
    /**
     * @description: 分页处理
     * @param {type}
     * @return:
     */
    pageHelperExchange({ currentPageNum, pageSize, rowsCount }) {
      this.pageHelper.pageSize = pageSize
      this.pageHelper.page = currentPageNum
      this.pageHelper.rowsCount = rowsCount
    },
    openLightbox(urlList, previewIndex) {
      this.imageList = urlList
      this.previewIndex = previewIndex
      this.isPreviewShow = true
    },
    addLinkClick(item) {
      const params = {
        dataIdList: [this.query.dataId],
        saasMark: 1,
        businessType: item.linkBusinessType,
        subBusinessType: this.query.subBusinessType,
        linkBusinessType: this.query.businessType
      }
      this.formDetailLinkAdd(params)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/object/tab-concent.scss';
.product-list {
  .content {
    .red-text {
      color: $error-base-color-6;
    }
    .v-pagination {
      float: right;
      width: auto;
      border: 1px solid $line-filter;
      border-top-width: 0;
      border-bottom-width: 0;
      // .el-pagination {
      //   float: right;
      // }
    }
  }
}
</style>
