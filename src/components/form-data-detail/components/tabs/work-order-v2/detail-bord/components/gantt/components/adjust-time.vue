<template>
  <div class="adjust-time">
    <el-dialog
      append-to-body
      :before-close="cancelAdjust"
      title="调整时间"
      :visible.sync="dialogVisible"
      width="510px"
    >
      <div class="left-card__adjust-time">
        <div class="left-card__adjust-time_desc">
          {{ adjustTimeMoreContent.topContent }}
        </div>
        <div class="left-card__adjust-time_content">
          <el-select v-model="type" class="input-block">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model="num"
            class="input-block"
            placeholder="请输入"
            @input="checkNumber"
          ></el-input>
          <el-select v-model="unit" class="input-block">
            <el-option
              v-for="item in unitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="left-card__adjust-time_more">
          <el-checkbox v-model="setAfterStage"></el-checkbox>
          <span class="text">{{ adjustTimeMoreContent.bottomContent }}</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelAdjust">{{ $t('operation.cancel') }}</el-button>
        <el-button :loading="loading" type="primary" @click="confirmAdjust">{{
          $t('operation.confirm')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AdjustTime',

  components: {},

  filters: {},

  mixins: [],
  props: {
    adjustVisible: {
      type: Boolean,
      default: false
    },
    query: {
      type: Object,
      default: () => ({})
    },
    // 是否需要传项目id
    setProject: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    dialogVisible: {
      get() {
        return this.adjustVisible
      },
      set(val) {
        this.$emit('update:adjustVisible', val)
      }
    },
    adjustTimeMoreContent() {
      if (this.query && [20900, 21000].includes(this.query.businessType)) {
        return {
          topContent: this.$t('workOrderV2.adjustTimeTip.taskAdjustTimeTopContent'),
          bottomContent: this.$t('workOrderV2.adjustTimeTip.taskAdjustTimeBottomContent')
        }
      } else if (this.query && [20800].includes(this.query.businessType)) {
        return {
          topContent: this.$t('workOrderV2.adjustTimeTip.projectAdjustTimeTopContent'),
          bottomContent: this.$t('workOrderV2.adjustTimeTip.taskAdjustTimeBottomContent')
        }
      } else {
        return {
          topContent: this.$t('workOrderV2.adjustTimeTip.kanbanAdjustTimeTopContent'),
          bottomContent: this.$t('workOrderV2.adjustTimeTip.kanbanAdjustTimeBottomContent')
        }
      }
    }
  },

  data() {
    return {
      loading: false,
      type: 1, // 1：提前；2：推迟
      typeOptions: [
        { value: 1, label: '提前' },
        { value: 2, label: '推迟' }
      ],
      num: '',
      unit: 1,
      unitOptions: [
        { value: 1, label: '小时' },
        { value: 2, label: '天' }
      ],
      setAfterStage: false,
      oldNum: ''
    }
  },

  watch: {},

  mounted() {},

  methods: {
    promiseCallBack(success = false, data = {}) {
      if (this.callBack) {
        this.callBack({
          success,
          data
        })
      }
    },
    closeAdjust() {
      this.dialogVisible = false
      this.loading = false
      Object.assign(this.$data, this.$options.data()) // 所有参数init
    },
    cancelAdjust() {
      this.closeAdjust()

      // 用于以promise形式调用
      this.promiseCallBack(false)
    },
    checkNumber() {
      const regex = /^(?:\d+(\.\d?)?|)$/ // 一位小数或空字符串
      if (regex.test(this.num)) {
        this.oldNum = this.num
      } else {
        // 输入非法，恢复为原来的值
        this.num = this.oldNum
      }
    },
    confirmAdjust() {
      if (this.num === '') {
        this.$message.error('请输入时间')
        return
      }
      this.loading = true
      const data = {
        type: +this.type,
        num: +this.num,
        unit: +this.unit,
        setAfterStage: this.setAfterStage ? 1 : 0,
        unFinish: this.setAfterStage ? 1 : 0
      }
      if (this.setProject) {
        data.projectId = this.query.dataId
      }

      this.$emit('handler-adjust-time', data, () => {
        this.closeAdjust()
      })

      // 用于以promise形式调用
      this.promiseCallBack(true, {
        data,
        callback: () => {
          this.closeAdjust()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.left-card__adjust-time {
  display: flex;
  flex-direction: column;
  &_desc {
    flex-direction: column;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 24px;
    color: $text-auxiliary;
    text-align: left;
  }
  &_content {
    display: flex;
    margin-bottom: 10px;
    .input-block {
      width: 100px;
      margin-right: 10px;
    }
  }
  &_more {
    margin-bottom: 10px;
    .text {
      font-size: 12px;
      color: $text-plain;
      text-align: left;
    }
  }
}
</style>
