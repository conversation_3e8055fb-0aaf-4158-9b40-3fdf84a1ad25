<template>
  <div class="left-card-desc">
    <template v-for="(item, index) in list">
      <div :key="item.id" class="card-block">
        <div class="stage-desc"></div>

        <template v-if="item.children && item.children.length">
          <div v-show="item.expand" class="left-card-desc__body">
            <template v-for="child in item.children">
              <div v-show="child.isShow" :key="child.id" class="body-desc">
                <div
                  v-for="(head, index) in ganttShowField"
                  :key="index"
                  class="column-box"
                  :style="{ flex: `0 0 ${columnWidth}px`, width: `${columnWidth}px` }"
                >
                  <template
                    v-if="+child.businessType === 20900 && head.taskFieldInfo && child.data"
                  >
                    <edit-cell
                      v-model="child.data[head.taskFieldInfo.attr]"
                      :data-id="child.id"
                      :field-info="head.taskFieldInfo"
                      :form-data="child.data"
                      :form-info="child.formInfo"
                      :source-data="child.sourceData"
                    />
                  </template>
                  <template
                    v-else-if="+child.businessType === 21000 && head.riskFieldInfo && child.data"
                  >
                    <edit-cell
                      v-model="child.data[head.riskFieldInfo.attr]"
                      :data-id="child.id"
                      :field-info="head.riskFieldInfo"
                      :form-data="child.data"
                      :form-info="child.formInfo"
                      :source-data="child.sourceData"
                  /></template>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </template>
    <div
      v-if="views !== 'detail-person'"
      :class="{
        'new-stage-desc': !isScroll,
        'card-block': !isScroll,
        'new-stage-scroll': isScroll
      }"
    ></div>
  </div>
</template>

<script>
import EditCell from './components/edit-cell.vue'

export default {
  name: 'LeftCardDesc',
  components: {
    EditCell
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    // 甘特图视图类型 list-person 列表人员视图、detail-person 详情人员视图、normal 通用视图
    views: {
      type: String,
      default: 'normal'
    },
    ganttShowField: {
      type: Array,
      default: () => []
    },
    isScroll: {
      type: Boolean,
      default: false
    },
    columnWidth: {
      type: Number
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    handlerOpenDetail(params) {
      this.$emit('handler-open-detail', params)
    }
  }
}
</script>

<style lang="scss" scoped>
.left-card-desc::-webkit-scrollbar {
  display: none;
}
.left-card-desc {
  position: relative;
  flex: 1;
  width: 100%;
  overflow-y: scroll;
  background-color: $neutral-color-1;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  .card-block {
    margin-bottom: 5px;
    background-color: $base-white;
    border-radius: 5px;
    .stage-desc {
      height: 40px;
    }
    .body-desc {
      display: flex;
      height: 40px;
      .column-box {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid $neutral-color-2;
        border-left: 1px solid $neutral-color-2;
      }
    }
  }
  .new-stage-desc {
    height: 40px;
  }
  .new-stage-scroll {
    height: 35px;
  }
}
</style>
