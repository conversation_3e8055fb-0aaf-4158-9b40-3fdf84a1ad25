<template>
  <JourneyTimelineWrapper :journey-list="journeyList" />
</template>

<script>
import JourneyTimelineWrapper from '@/components/journey-timeline/journey-timeline-wrapper.vue'
import { getMailRecordList } from '@/api/mail.js'

const originalPageData = {
  hasMore: true,
  pageSize: 20,
  page: 1
}
export default {
  name: 'MailRecord',

  components: { JourneyTimelineWrapper },

  mixins: [],

  props: {
    formQuery: {
      type: Object,
      default: () => {
        return {}
      }
    },
    query: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isLoadMore: {
      type: Boolean
    }
  },

  data() {
    return {
      journeyList: [],
      loading: false,
      pageData: { ...originalPageData }
    }
  },

  computed: {},

  watch: {
    isLoadMore(val) {
      if (val) {
        this.loadNextPage()
      }
    }
  },

  created() {
    this.getListData()
  },

  beforeDestroy() {},

  methods: {
    getListData(concat = false) {
      const params = {
        businessType: this.query.businessType,
        formId: this.query.formId,
        businessId: this.query.dataId,
        page: this.pageData.page,
        pageSize: this.pageData.pageSize
      }

      this.loading = true
      getMailRecordList(params)
        .then(({ result }) => {
          const journeyData = result.mailDataList.map((mailData) => {
            return this.transformToJourneyData(mailData)
          })
          if (concat) {
            this.journeyList = this.journeyList.concat(journeyData)
          } else {
            this.journeyList = journeyData
          }

          this.pageData.hasMore = result.pageHelper.hasRight
        })
        .finally(() => {
          this.loading = false
        })
    },
    loadNextPage() {
      if (this.pageData.hasMore) {
        this.pageData.page++

        this.getListData(true)
      }
    },
    transformToJourneyData(mailData) {
      const obj = {
        addTime: mailData.addTime,
        showType: 'mailRecordCard',
        content: {
          sender: mailData.sender,
          subject: mailData.subject,
          mailSummary: mailData.summary,
          toRecipient: mailData.toRecipient,
          ccRecipient: mailData.ccRecipient,
          folderId: mailData.folderId,
          mailId: mailData.id
        }
      }

      if (mailData.roleType === 1) {
        obj.title = '收邮件'
        obj.imageIcon = 'mailIn'
      } else {
        obj.title = '发邮件'
        obj.imageIcon = 'mailOut'
      }

      return obj
    }
  }
}
</script>

<style lang="scss" scoped></style>
