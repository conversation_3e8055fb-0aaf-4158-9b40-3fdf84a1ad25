<!--
 * @Description: 详情页 - 产品详情
   formDataId: 产品id
 }
 -->
<template>
  <div class="product-tab" style="height: 100%">
    <div style="height: 100%">
      <h1 class="theme">
        <i></i><span>{{ $t('formDataDetail.tabs.essentialInformation') }}</span>
      </h1>
      <ul class="base-info">
        <!-- 标签的特殊处理 -->
        <template v-for="Info in baseInfo">
          <el-tooltip
            v-if="Info.fieldType === 800000"
            :key="Info.attr"
            :content="showTags(Info.value4Show)"
            :disabled="!Info.value4Show || Info.value4Show.length === 0"
            effect="dark"
            placement="top"
          >
            <li class="base-info-item">
              {{ Info.attrName }} ：
              <v-tag
                v-for="tag in Info.value4Show"
                :key="tag.id"
                :color="tag.color"
                :content="tag.name"
              />
            </li>
          </el-tooltip>
          <!-- 在线编辑字段的特殊处理 -->
          <li v-else-if="Info.fieldType === 10035" :key="Info.attr" class="base-info-item">
            {{ Info.attrName }} ：
            <template v-if="Info.value4Show">
              <span>{{ Info.value4Show.base_contract_name }}</span>
              <div class="file-list-item">
                <div class="file-list-item__block">
                  <div
                    class="file-list-item__img-bg file-type-icon"
                    :class="getFileType(Info.value4Show.file_ext)"
                  ></div>
                  <div class="file-list-item__img-right">
                    <span>{{ Info.value4Show.file_name }}</span>
                    <div class="file-list-item__right-button">
                      <a @click="changeImg(Info.value4Show.file_url)">{{
                        $t('operation.preview')
                      }}</a>
                      <a
                        @click="getDownloadUrl(Info.value4Show.file_url, Info.value4Show.file_name)"
                        >{{ $t('operation.download') }}</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <span v-else>--</span>
          </li>

          <li v-else :key="Info.attr" class="base-info-item">
            <!-- 关联数据支持穿透 -->
            <template v-if="Info.fieldType === 10008"
              >{{ Info.attrName }} ：
              <a v-if="Info.value4Show" @click="seeChildrenDetail(Info.entity)">{{
                Info.value4Show
              }}</a>
              <em v-else>--</em>
            </template>
            <!-- 关联数据多选支持穿透 -->
            <template v-else-if="Info.fieldType === 10032">
              {{ Info.attrName }} ：
              <a
                v-for="(item, index) in Info.value4Show"
                :key="index"
                @click="seeChildrenDetail(item.entity)"
                >{{ index === 0 ? '' : '、' }}{{ item.value4Show }}</a
              >
            </template>
            <!-- 数字类型的值 -->
            <template v-else-if="typeof Info.value4Show === 'number'">
              {{ Info.attrName }} ： {{ Info.value4Show }}
            </template>
            <template v-else>
              {{ Info.attrName }} ：<el-tooltip
                :content="Info.value4Show"
                :disabled="getTagLength(Info.value4Show) < 30"
                effect="dark"
                placement="top"
                :popper-class="getTagLength(Info.value4Show) > 100 ? 'moreWidth' : ''"
              >
                <span
                  v-if="Info.fieldType === 1 || Info.fieldType === 7"
                  v-html="htmlModelVal(Info.value4Show)"
                ></span>
                <span v-else>{{ maxSlice(Info.value4Show, 30) || '--' }}</span>
              </el-tooltip>
            </template>
          </li>
        </template>
      </ul>
      <template v-if="detailInfo.specificationInfo">
        <h1 class="theme">
          <i></i><span>{{ $t('business.specificationRelated') }}</span>
          <div class="specification">
            <el-switch
              v-model="isEnableSpecification"
              @change="enableSpecificationChange"
            ></el-switch>
            <span>{{ $t('formDataDetail.tabs.onlyEnableSpec') }}</span>
          </div>
        </h1>
        <div class="stock-table-out">
          <div class="stock-table">
            <table cellpadding="0" cellspacing="0">
              <thead>
                <tr>
                  <!-- 主图 -->
                  <th>{{ $t('formDataDetail.tabs.basicPicture') }}</th>
                  <th v-for="(item, index) in advancedSpecification" :key="index + 'a'">
                    <!--key值冲突zpp-->
                    <el-tooltip :content="item.name" effect="dark" placement="top">
                      <em class="spec-text">{{ item.name }}</em>
                    </el-tooltip>
                  </th>
                  <th>{{ $t('formDataDetail.tabs.specificationEncoding') }}</th>
                  <th v-if="attrShowObj.childProductBarcode">
                    {{ $t('business.productBarCode') }}
                  </th>
                  <th v-if="attrShowObj.childProductCost">{{ cost.attrName }}</th>
                  <th v-if="attrShowObj.childProductStock">
                    {{ $t('formDataDetail.tabs.stock') }}
                  </th>
                  <template v-if="attrShowObj.childProductPrice">
                    <template v-if="attrShowObj.childProductMultiUnit">
                      <th
                        v-for="item of childProductArray[0].childProductPrice"
                        :key="item.rate + '-' + item.value"
                      >
                        {{ price.attrName }}({{ item.text }})
                      </th>
                    </template>
                    <th v-else>{{ price.attrName }}</th>
                  </template>
                  <th>{{ $t('formDataDetail.tabs.isItEnabled') }}</th>
                </tr>
              </thead>
              <tbody v-if="advancedSpecification[0] && advancedSpecification[0].value.length">
                <template v-for="(item, index) in countSum(0)">
                  <tr
                    v-show="
                      !isEnableSpecification ||
                      (isEnableSpecification && childProductArray[index].isUse)
                    "
                    :key="index"
                  >
                    <!-- 主图 -->
                    <td>
                      <el-image
                        v-if="
                          childProductArray &&
                          childProductArray[index] &&
                          childProductArray[index].childProductImage &&
                          childProductArray[index].childProductImage.length
                        "
                        fit="fill"
                        :src="childProductArray[index].childProductImage[0]"
                        style="width: 30px; height: 30px"
                        @click="changeImg(childProductArray[index].childProductImage[0])"
                      ></el-image>
                    </td>
                    <template v-for="(n, specIndex) in advancedSpecification.length">
                      <td
                        v-if="showTd(specIndex, index)"
                        :key="n"
                        :rowspan="countSumSpec(n, index)"
                      >
                        <el-tooltip
                          :content="getSpecAttr(specIndex, index)"
                          effect="dark"
                          placement="top"
                        >
                          <em class="spec-text">{{ getSpecAttr(specIndex, index) }}</em>
                        </el-tooltip>
                      </td>
                    </template>
                    <td>
                      {{ childProductArray[index].childProductNo || '--' }}
                    </td>
                    <td v-if="attrShowObj.childProductBarcode">
                      {{ childProductArray[index].childProductBarcode || '--' }}
                    </td>
                    <td
                      v-if="attrShowObj.childProductCost"
                      :class="{ 'is-right': attrShowObj.childProductCost }"
                    >
                      {{ childProductArray[index].childProductCost }}
                    </td>
                    <td
                      v-if="attrShowObj.childProductStock"
                      :class="{ 'is-right': attrShowObj.childProductStock }"
                    >
                      {{ childProductArray[index].childProductStock }}
                    </td>
                    <template v-if="attrShowObj.childProductPrice">
                      <template v-if="attrShowObj.childProductMultiUnit">
                        <td
                          v-for="item of childProductArray[index].childProductPrice"
                          :key="item.rate + '-' + item.value"
                          :class="{ 'is-right': attrShowObj.childProductPrice }"
                        >
                          {{ item.rate }}
                        </td>
                      </template>
                      <td v-else :class="{ 'is-right': attrShowObj.childProductPrice }">
                        {{ childProductArray[index].childProductPrice }}
                      </td>
                    </template>
                    <td>
                      <span v-if="childProductArray[index].isUse">{{ $t('nouns.yes') }}</span>
                      <span v-else>{{ $t('nouns.no') }}</span>
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>
      </template>
      <h1 class="theme">
        <i></i><span>{{ $t('formDataDetail.tabs.productPictures') }}</span>
      </h1>
      <ul class="img-list">
        <li v-if="!imageInfo.length" class="empty">
          {{ $t('formDataDetail.tabs.noProductPictures') }}
        </li>
        <li v-for="(img, index) in imageInfo" :key="img" class="img">
          <img v-lazy="thumbnail(img, 80)" @click="imgBig(imageInfo, index)" />
        </li>
      </ul>

      <h1 class="theme">
        <i></i><span>{{ $t('formDataDetail.tabs.productDocumentation') }}</span>
      </h1>
      <ul class="file-list">
        <li v-for="(item, index) in fileList" :key="index" class="file">
          <div class="file-item">
            <div class="icon" :class="fileIcoPath(item.ext)"></div>
            <div class="file-con">
              <div class="file-name">
                <p class="title">{{ item.filename }}</p>
                <p class="size">{{ item.size | formatUnit }}</p>
              </div>
              <div class="operate">
                <a
                  v-if="isShowFile(item.ext)"
                  class="preview-btn"
                  @click="previewBox(fileList, index)"
                  >{{ $t('operation.preview') }}</a
                >
                <a class="download-btn" @click="getDownloadUrl(item.attachIndex, item.filename)">{{
                  $t('operation.download')
                }}</a>
              </div>
            </div>
          </div>
        </li>
        <li v-if="!fileList.length" class="empty">
          {{ $t('formDataDetail.tabs.noRelevantDocuments') }}
        </li>
      </ul>
      <h1 class="theme">
        <i></i><span>{{ $t('formDataDetail.tabs.otherInformation') }}</span>
      </h1>
      <ul class="other-infos">
        <li class="other-item">
          <span>{{ $t('nouns.founder') }}：</span>
          <el-popover
            :open-delay="500"
            placement="bottom-start"
            trigger="hover"
            :visible-arrow="false"
            width="300"
          >
            <user-card
              :avatar="creator.avatar"
              :department="creator.depSimpleTree"
              :job-number="creator.jobnumber"
              :name="creator.name || '--'"
            ></user-card>
            <span
              slot="reference"
              class="show-name"
              v-html="addNodeByWX(creator.name || '--')"
            ></span>
          </el-popover>
          <!-- <span>{{ creator || '--' }}</span> -->
        </li>
        <li class="other-item">
          <span>{{ $t('label.addTime') }}：</span>
          <span>{{ addTime || '--' }}</span>
        </li>
        <!-- <li class="other-item">
          <span>{{ $t('formDataDetail.tabs.lastUpdatedBy') }}</span>
          <span>{{updater || '--'}}</span>
        </li> -->
        <li class="other-item">
          <span>{{ $t('formDataDetail.tabs.lastUpdated') }}</span>
          <span>{{ updateTime || '--' }}</span>
        </li>
      </ul>
    </div>
    <!-- 图片预览 -->
    <lg-preview
      v-if="isPreviewShow"
      :index="previewIndex"
      :list="previewFiles"
      @close="isPreviewShow = false"
    ></lg-preview>
  </div>
</template>

<script>
import { detailTabProductDetail } from '@/api/form-detail-tabs'
import xbb from '@xbb/xbb-utils'
import bus from '@/utils/temp-vue.js'
// mixin
import detailMixin from './../detail-mixin'
import UserCard from '@/components/common/user-card.vue'
import VTag from '@/components/base/v-tag.vue'

// 为Object添加一个原型方法，判断两个对象是否相等
function objEquals(object1, object2) {
  // console.log('判断两个对象是否相等', object1)
  // For the first loop, we only check for types
  for (const propName in object1) {
    // Check for inherited methods and properties - like .equals itself
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/hasOwnProperty
    // Return false if the return value is different
    if (
      object1.hasOwnProperty(propName) !== object2.hasOwnProperty(propName) ||
      typeof object1[propName] !== typeof object2[propName]
    ) {
      return false
      // Check instance type
    }
  }
  // Now a deeper check using other objects property names
  for (const propName in object2) {
    // We must check instances anyway, there may be a property that only exists in object2
    // I wonder, if remembering the checked values from the first loop would be faster or not
    if (
      object1.hasOwnProperty(propName) !== object2.hasOwnProperty(propName) ||
      typeof object1[propName] !== typeof object2[propName]
    ) {
      return false
    }
    // If the property is inherited, do not check any more (it must be equa if both objects inherit it)
    if (!object1.hasOwnProperty(propName)) {
      continue
    }
    // Now the detail check and recursion
    // This returns the script back to the array comparing
    /** REQUIRES Array.equals**/
    if (object1[propName] instanceof Array && object2[propName] instanceof Array) {
      // recurse into the nested arrays
      if (objEquals(!object1[propName], object2[propName])) {
        return false
      }
    } else if (
      object1[propName] instanceof Object &&
      object2[propName] instanceof Object &&
      objEquals(!object1[propName], object2[propName])
    ) {
      return false
    } else if (object1[propName] !== object2[propName]) {
      return false
    }
  }
  // If everything passed, let's say YES
  return true
}

export default {
  filters: {
    toUpperCase(str) {
      return str.toUpperCase()
    },

    // 对文件大小单位进行格式化
    formatUnit(size) {
      if (size > 999999) {
        return (size / 1024 / 1024).toFixed(2) + 'MB'
      } else if (size > 999) {
        return (size / 1024).toFixed(2) + 'KB'
      } else {
        return size + 'B'
      }
    }
  },
  components: {
    UserCard,
    VTag,
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },
  mixins: [detailMixin],
  props: {
    id: {
      type: Number
    }
  },
  data() {
    return {
      detailInfo: {}, // 产品详情信息
      advancedSpecification: [], // 规格属性
      childProductArray: [], // 多规格产品属性值
      attrShowObj: {}, // 产品属性是否展示（成本库存单价条码）
      price: {}, // 单价
      cost: {}, // 成本
      baseInfo: [], // 基本信息
      imageInfo: [], // 产品图片
      addTime: '', // 创建时间
      creator: '', // 创建人对象
      updateTime: '', // 最后更新时间
      updater: '', // 最后更新人
      fileList: [], // 文件列表
      imageList: [], //  传入预览框的图片
      previewFiles: [], // 待预览文件数组，赋值 imageList 或者fileList
      isPreviewShow: false, // 查看图片弹窗
      previewIndex: 0, // 显示第几个图片文件
      isEnableSpecification: false // 展示启用规格
    }
  },
  created() {
    this.init()
    // 订阅更新
    const fn = () => {
      // 节约性能 -- 只对当前激活状态的tab 进行刷新
      if (this.isActive) {
        this.init()
      }
    }
    this.parentDetail && this.parentDetail.$on('detailUpdate', fn)
    this.$once('hook:beforeDestroy', function () {
      this.parentDetail && this.parentDetail.$off('detailUpdate')
    })
  },
  beforeDestroy() {},
  methods: {
    changeImg(item) {
      this.previewFiles = [item]
      this.previewIndex = 0
      this.isPreviewShow = true
    },
    thumbnail(img, size) {
      return xbb.thumbnail(img, size)
    },

    init() {
      this.getTabCon()
    },
    // 标签展示
    showTags(valueArr) {
      if (Array.isArray(valueArr)) {
        return valueArr.map((item) => item.name).join(';')
      }
      return ''
    },
    // 获取产品接口
    getTabCon() {
      // let url = `/productApi/productDetail.html`
      const request = {
        ...this.query
      }
      detailTabProductDetail(request)
        .then((data) => {
          this.baseInfo = data.result.baseInfo
          // this.imageInfo = imageInfoArray
          this.addTime = data.result.otherInfo.addTime
          this.creator = data.result.otherInfo.creator
          this.updateTime = data.result.otherInfo.updateTime
          this.updater = data.result.otherInfo.updater
          this.detailInfo = data.result
          // 图表中心 链接详情页 产品管理系列需要更新dataId的值
          bus.$emit('productParentDataId', data.result.productParentDataId)
          if (data.result.specificationInfo) {
            // 属性是否展示
            this.attrShowObj = data.result.specificationInfo.value4Show.attrShowObj
            // 成本、单价字段解释
            data.result.specificationInfo.explains.forEach((it) => {
              if (it.saasAttr === 'cost') {
                this.cost = it
              } else if (it.saasAttr === 'price') {
                this.price = it
              }
            })

            // 规格属性
            this.advancedSpecification =
              data.result.specificationInfo.value4Show.advancedSpecification
            console.log(6564, this.childProductArray)
            // 多规格产品属性值
            this.childProductArray = this.formatChildProduct(
              data.result.specificationInfo.value4Show.advancedSpecification,
              data.result.specificationInfo.value4Show.childProductArray
            )
            // this.childProductArray = data.result.specificationInfo.value4Show.childProductArray
          }
          // 文件列表格式化
          if (data.result.fileInfoArray && data.result.fileInfoArray.length) {
            let arr = []
            data.result.fileInfoArray.forEach((item) => {
              if (!item.value4Show) {
                arr = []
              } else {
                arr = arr.concat(item.value4Show)
              }
            })
            this.fileList = arr
          }

          // 图片列表格式化
          if (data.result.imageInfoArray && data.result.imageInfoArray.length) {
            let arr = []
            data.result.imageInfoArray.forEach((item) => {
              if (!item.value4Show) {
                arr = []
              } else {
                arr = arr.concat(item.value4Show)
              }
            })
            this.imageInfo = arr
          }
          // 是否展示启用规则
          this.isEnableSpecification = localStorage.getItem('enableSpecification') === 'true'
          this.enableSpecificationChange(this.isEnableSpecification)
        })
        .catch((error) => {
          console.log('detailTabProductDetail', error)
          if (error.code) {
            // 不存在产品就关闭产品详情
            this['parentDetail'] && (this['parentDetail']['dialogShow'] = false)
          }
        })
    },
    // 获取文件下载链接
    getDownloadUrl(url, name) {
      utils.fileDownload(url, name)
    },
    // 预览
    previewBox(fileList, previewIndex = 0) {
      this.previewFileList = fileList
      this.previewFiles = JSON.parse(JSON.stringify(this.previewFileList))
      this.previewIndex = previewIndex
      this.isPreviewShow = true
    },

    // 处理文件后缀为图片，txt,excel,word,pdf
    isShowFile(ext) {
      return utils.getFileViewType(ext)
    },
    // 通过后缀名获取关联图标
    fileIcoPath(ext) {
      return utils.getFileType(ext)
    },
    // 图片放大
    imgBig(urlList, previewIndex) {
      // lightBox(item)
      this.imageList = urlList
      this.previewFiles = JSON.parse(JSON.stringify(this.imageList))
      this.previewIndex = previewIndex
      this.isPreviewShow = true
    },
    /**
     * [formatChildProduct 服务端只返回了已启用的子产品列表，该方法遍历高级规格，生成一个完整的子产品列表（包含不启用的子产品）]
     * @param  {[type]} specification     [高级规格]
     * @param  {[type]} childProductArray [所有一起用的子产品列表]
     * @return {[type]}                   [返回完整的子产品列表]
     */
    formatChildProduct(specification, childProductArray) {
      const specArray = [] // 根据高级规格，生成一个包含所有规格的数组
      const arr = []
      console.log('this.countSum(0)', this.countSum(0))
      for (let i = 0; i < this.countSum(0); i++) {
        const spec = this.getChildProductSpec(i)
        console.log('规格', spec)
        specArray.push(spec)
      }
      console.log(777, specArray)
      let flag
      let priceArr
      // 用生成的规格去匹配服务端返回的子产品列表，如果能匹配上，返回一条已启用的子产品，反之则返回一条未启用的子产品
      specArray.forEach((spec, index) => {
        let isUse
        childProductArray.forEach((child) => {
          if (objEquals(spec, child.childProductSpec)) {
            child.isUse = true
            arr.push(child)
            isUse = true
          }
          if (
            child.childProductPrice &&
            Object.prototype.toString.call(child.childProductPrice) === '[object Array]' &&
            !flag
          ) {
            flag = true
            priceArr = xbb.deepClone(child.childProductPrice)
          }
        })
        priceArr &&
          priceArr.forEach((val) => {
            val.rate = 0
          })
        if (!isUse) {
          const childProduct = {
            childProductId: 0,
            childProductSpec: this.getChildProductSpec(index),
            childProductNo: '',
            childProductStock: 0,
            childProductPrice: priceArr || 0,
            childProductCost: 0,
            costState: true,
            isUse: false
          }
          arr.push(childProduct)
        }
      })
      return arr
    },
    // 获取childProductArray的childProductSpec属性
    getChildProductSpec(index) {
      const obj = {}
      this.advancedSpecification.forEach((item, specIndex) => {
        obj[item.name] = this.getSpecAttr(specIndex, index)
      })
      return obj
    },
    /*
      计算属性的乘积
      @params
        specIndex 规格项目在 advancedSpecification 中的序号
    */
    countSum(specIndex) {
      let num = 1
      this.advancedSpecification.forEach((item, index) => {
        if (index >= specIndex && item.value && item.value.length) {
          num *= item.value.length
        }
      })
      return num
    },
    /**
     * @description 规格合并行处理
     */
    countSumSpec(specIndex, allindex) {
      let unshow = 0
      let num = this.countSum(specIndex) // 获取该单元格所跨行数
      if (this.isEnableSpecification) {
        const startIndex = allindex // 区间数据上标
        const endIndex = allindex + num - (allindex % num) // 区间数据下标
        const lineData = this.childProductArray.slice(startIndex, endIndex) // 获取该区间下的数据
        lineData.forEach((item) => {
          // 获取该区间中未启用规格次数
          if (!item.isUse) {
            unshow++
          }
        })
        // 启用时单元格所跨行数
        num = lineData.length - unshow
      }
      return num
    },
    // 根据传入的条件，来判断是否显示该td
    showTd(specIndex, index) {
      const rowspan = this.countSum(specIndex + 1) // 获取合并的行数
      const num = index % rowspan // 获取在当前区间内 当前行之前的行数
      // 如果当前项目下没有属性，则不显示
      if (!this.advancedSpecification[specIndex]) {
        return false

        // 自己悟一下吧
      } else if (index % this.countSum(specIndex + 1) === 0) {
        return true
      } else if (
        this.isEnableSpecification &&
        this.childProductArray[index].isUse &&
        specIndex !== this.advancedSpecification.length - 1
      ) {
        // 启用规则 && 启用数据 && 不是最小sku
        // 有可能第一行未启用 会导致上一层规格显示不出来 需要在当前行显示
        const lineData = this.childProductArray.slice(index - num, index) // 获取在当前区间内 当前行之前的数据
        return lineData.every((item) => {
          return !item.isUse
        }) // 若存在为启用的数据 则说明前面已启用的数据已经回显出上一层规格
      } else {
        return false
      }
    },
    /*
      根据传入的属性值，拿到相应规格的属性
      @params
        specIndex 规格项目在 advancedSpecification 中的序号
        index 所有属性在遍历时的序号
    */
    getSpecAttr(specIndex, index) {
      // 获取当前规格项目下的属性值
      const currentValues = this.advancedSpecification[specIndex].value
      let indexCopy

      // 判断是否是最后一个规格项目
      if (
        this.advancedSpecification[specIndex + 1] &&
        this.advancedSpecification[specIndex + 1].value.length
      ) {
        indexCopy = index / this.countSum(specIndex + 1)
      } else {
        indexCopy = index
      }

      const i = Math.floor(indexCopy % currentValues.length)

      if (i.toString() !== 'NaN') {
        return currentValues[i]
      } else {
        return ''
      }
    },
    getTagLength(val) {
      // return xbb.getByteLen(val)
      return val && val.length
    },
    // 标签超出省略
    maxSlice(val, len) {
      return xbb.getByteLen(val) > len ? val.slice(0, len) + '...' : val
    },
    htmlEncodeByRegExp(str) {
      let s = ''
      if (str.length === 0) return ''
      s = str.replace(/&/g, '&amp;')
      s = s.replace(/</g, '&lt;')
      s = s.replace(/>/g, '&gt;')
      s = s.replace(/'/g, '&#39;')
      s = s.replace(/"/g, '&quot;')
      return s
    },
    htmlModelVal(val) {
      if (val !== '' && val !== undefined) {
        return this.htmlEncodeByRegExp(val).replace(
          /https?:\/\/\S+/g,
          `<a target='_blank' href='$&'>$&</a>`
        )
      } else {
        return ''
      }
    },

    // 展示启用规格
    enableSpecificationChange() {
      // 缓存启用规格状态
      localStorage.setItem('enableSpecification', this.isEnableSpecification)
    },
    getFileType(ext) {
      return utils.getFileType(ext)
    }
  }
}
</script>

<style lang="scss">
body .moreWidth {
  max-width: 700px;
}
</style>

<style lang="scss" scoped>
.product-tab {
  .noInfo {
    color: #9a9a9a;
  }
  padding: 0 15px;
  .theme {
    font-size: 13px;
    font-weight: bold;
    line-height: 30px;
    color: $text-main;
    background: $bg-blue;
  }
  .theme i {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 0 10px;
    background: $brand-color-5;
  }
  .theme .specification {
    float: right;
    margin: 0 10px;
  }
  .base-info {
    box-sizing: border-box;
    padding: 10px 20px 20px;
    font-size: 0;
    .base-info-item {
      display: inline-block;
      width: 45%;
      margin-top: 15px;
      margin-right: 5%;
      overflow: hidden;
      font-size: 13px;
      line-height: 18px;
      text-overflow: ellipsis;
      white-space: nowrap;
      .info-box {
        display: inline-block;
        width: calc(100% - 40px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .img-list {
    padding: 0 10px 18px 0;
    font-size: 0;
    .img {
      display: inline-block;
      margin: 18px 0 0 18px;
      img {
        width: 80px;
        height: 80px;
        cursor: pointer;
      }
    }
  }
  .instruction {
    padding: 20px;
    font-size: 13px;
    line-height: 1.8;
    text-align: justify;
    text-indent: 2em;
  }
  .other-infos {
    padding: 5px 20px 20px;
    .other-item {
      margin-top: 15px;
      font-size: 13px;
    }
  }
  .file-list {
    padding-bottom: 15px;
    font-size: 0;
    .file {
      display: inline-block;
      width: 50%;
      .file-item {
        padding: 15px 0 0 20px;
        .icon {
          display: inline-block;
          width: 46px;
          height: 46px;
          vertical-align: top;
          background-image: url('../../../../../assets/file-cabinet/other.svg');
          background-repeat: no-repeat;
          background-position: center center;
          background-size: 100% 100%;
          &.image {
            background-image: url('../../../../../assets/file-cabinet/image.svg');
          }
          &.txt {
            background-image: url('../../../../../assets/file-cabinet/text.svg');
          }
          &.zip {
            background-image: url('../../../../../assets/file-cabinet/zip.svg');
          }
          &.sounds {
            background-image: url('../../../../../assets/file-cabinet/sounds.svg');
          }
          &.excel {
            background-image: url('../../../../../assets/file-cabinet/excel.svg');
          }
          &.pdf {
            background-image: url('../../../../../assets/file-cabinet/pdf.svg');
          }
          &.word {
            background-image: url('../../../../../assets/file-cabinet/word.svg');
          }
          &.ppt {
            background-image: url('../../../../../assets/file-cabinet/ppt.svg');
          }
          &.video {
            background-image: url('../../../../../assets/file-cabinet/video.svg');
          }
          &.html {
            background-image: url('../../../../../assets/file-cabinet/html.svg');
          }
        }
        .file-con {
          position: relative;
          box-sizing: border-box;
          display: inline-block;
          width: 300px;
          height: 46px;
          padding: 7px 10px 0 8px;
          font-size: 13px;
          vertical-align: top;
          border: #dce1e8 solid 1px;
          border-left: 0;
          .file-name {
            float: left;
          }
          .title {
            width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .size {
            margin-top: 6px;
            color: $text-plain;
          }
          .operate {
            float: right;
          }
          .preview-btn {
            padding-right: 10px;
          }
          .download-btn {
            padding-left: 10px;
            line-height: 28px;
            color: $link-base-color-6;
            text-align: center;
            cursor: pointer;
            border-left: 1px solid $neutral-color-3;
            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
  .empty {
    font-size: 14px;
    line-height: 3;
    text-align: center;
    text-indent: 0;
  }
  .ql-snow {
    overflow: auto;
  }
  .ql-snow,
  .ql-snow * {
    box-sizing: border-box;
  }
  .ql-editor {
    box-sizing: border-box;
    height: 100%;
    padding: 12px 15px;
    overflow-y: auto;
    line-height: 1.42;
    text-align: left;
    word-wrap: break-word;
    tab-size: 4;
    white-space: pre-wrap;
    outline: none;
    & :deep(p) {
      strong {
        font-weight: bold !important;
      }
      strong {
        * {
          font-weight: bold;
        }
      }
    }
  }
  // 规格相关
  .stock-table-out {
    .stock-table {
      overflow-x: auto;
      overflow-y: hidden;
      table {
        width: 100%;
        min-width: 1100px;
      }
    }
    padding: 0;
    margin: 15px 0;
    border-collapse: separate;
    background-color: $base-white;
    border: 1px solid $neutral-color-3;
    td,
    th {
      position: relative;
      padding: 4px 10px;
      text-align: center;
      border-right: 1px solid $neutral-color-3;
      border-bottom: 1px solid $neutral-color-3;
      &:last-of-type {
        border-right: none;
      }
      &:first-of-type {
        border-left: none;
      }
    }
    th {
      line-height: 30px;
      background-color: $bg-blue;
    }
    td {
      .spec-text {
        line-height: 20px;
      }
    }
    .is-right {
      text-align: right;
    }
  }
}

.file-list-item {
  height: 48px;
  margin-top: 10px;
  & > .file-list-item__block {
    display: flex;
    align-items: center;
    height: 100%;
    & > .file-list-item__img-bg {
      flex-shrink: 0;
      width: 48px;
      height: 100%;
      margin-right: 15px;
      background-size: 100% 100%;
    }
    & > .file-list-item__img-right {
      display: flex;
      flex-direction: column;
      line-height: 20px;
      @include singleline-ellipsis;
      & > span {
        @include singleline-ellipsis;
      }
      .file-list-item__right-button {
        a {
          display: inline-block;
        }
      }
    }
  }
}
</style>
