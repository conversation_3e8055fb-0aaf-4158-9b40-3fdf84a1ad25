<template>
  <div class="container session-archive">
    <div v-show="!showDetail">
      <div class="header">
        <el-row align="middle" :gutter="20" type="flex">
          <el-col :span="12">
            <span>会话时间：</span>
            <el-date-picker
              v-model="dateRange"
              :clearable="false"
              :editable="false"
              :end-placeholder="$t('label.endTime')"
              format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              :range-separator="$t('unit.to')"
              :start-placeholder="$t('label.startTime')"
              type="daterange"
              unlink-panels
              value-format="timestamp"
              @change="handleDateRangeChange"
            >
            </el-date-picker>
          </el-col>
          <!-- <el-col :span="12">
            <el-input v-model="searchParams.keyWord" placeholder="搜索会话内容"></el-input>
          </el-col> -->
        </el-row>
      </div>

      <div v-if="hasData" class="list">
        <session-archive-item
          v-for="item in dataList"
          :key="item.weChatMsgId"
          :can-edit-main-person="canEditMainPerson"
          :original-content="item"
          @toDetail="handleCardClick(item)"
        />
      </div>

      <div v-else class="no-data">暂无会话内容</div>
    </div>

    <session-archive-detail
      v-if="showDetail"
      :session-archive-params="sessionArchiveParams"
      :style="{
        height: `${height}px`
      }"
      @back="handleBackToList"
    ></session-archive-detail>
  </div>
</template>

<script>
import SessionArchiveItem from './components/session-archive-item-old'
import SessionArchiveDetail from './components/session-archive-detail-old'

import { getSessionList } from '@/api/scrm/sales-manage/session-archive.js'

export default {
  name: 'SessionArchive',

  components: {
    SessionArchiveItem,
    SessionArchiveDetail
  },

  inheritAttrs: false,

  props: {
    query: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      height: 450,
      searchParams: {
        // keyWord: '',
        startTime: undefined,
        endTime: undefined
      },
      dateRange: [],
      showDetail: false,
      selectedSessionArchive: {},

      canEditMainPerson: false,
      dataList: []
    }
  },

  computed: {
    hasData() {
      return this.dataList.length > 0
    },
    pickerOptions() {
      const _this = this
      const pickerShortCutFn = (picker, month) => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * month)
        picker.$emit('pick', [start, end])
      }
      return {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              pickerShortCutFn(picker, 7)
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              pickerShortCutFn(picker, 30)
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              pickerShortCutFn(picker, 90)
            }
          }
        ],
        disabledDate(time) {
          const currentTime = Date.now() // 当前时间
          if (_this.dateRange[0]) {
            const times = 86400000 * 30 * 3 // 三个月的毫秒数
            const curSelectTime = _this.dateRange[0] // 选择的起始时间
            const before = curSelectTime - times
            const after = curSelectTime + times
            // 选择的时间
            // 1.必须小于当前
            // 2.前后三个月
            return time.getTime() > currentTime || time.getTime() > after || time.getTime() < before
          }
          return time.getTime() > currentTime
        }
      }
    },
    sessionArchiveParams() {
      return {
        sessionType: 'externalSession',
        weChatMsgId: this.selectedSessionArchive.weChatMsgId || '',
        sessionUserId: this.selectedSessionArchive.userId
      }
    }
  },

  watch: {
    searchParams: {
      deep: true,
      handler() {
        this.getDataList()
      }
    }
  },

  created() {
    this.initDateRange()
    this.$nextTick(() => {
      // 兼容其它模块（父容器没有 class = "scroll-body"的容器）
      const scrollBox = document.querySelector('.scroll-body')
      scrollBox && (this.height = scrollBox.clientHeight - 100)
    })
  },

  methods: {
    initDateRange() {
      const currentTime = new Date().getTime()
      const startTime = currentTime - 3600 * 1000 * 24 * 90
      const endTime = currentTime
      this.dateRange = [startTime, endTime]

      this.handleDateRangeChange(this.dateRange)
    },
    getDataList(concat = false) {
      const params = {
        id: this.query.dataId,
        sessionUserId: utils.LS.get('userId') || '1',
        sessionType: '2',
        ...this.searchParams,
        businessType: this.query.businessType
      }

      getSessionList(params).then(({ result }) => {
        this.canEditMainPerson = result.editFlag
        this.dataList = concat ? [...this.dataList, ...result.pojoList] : result.pojoList
      })
    },
    handleDateRangeChange(val) {
      if (val) {
        this.searchParams.startTime = Math.ceil(val[0] / 1000)
        this.searchParams.endTime = Math.ceil(val[1] / 1000)
      } else {
        this.searchParams.startTime = this.searchParams.endTime = undefined
      }
    },
    handleCardClick(card) {
      this.showDetail = true
      this.$nextTick(() => {
        this.selectedSessionArchive = card
      })
    },
    handleBackToList() {
      this.selectedSessionArchive = {}
      this.showDetail = false
    }
  }
}
</script>

<style lang="scss" scoped>
.session-archive {
  .header {
    margin-bottom: 20px;
    .back-button {
      font-size: 14px;
      line-height: 22px;
      color: $text-plain;
      cursor: pointer;
    }
  }
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 450px;
  font-size: 14px;
  color: #999999;
}
</style>
