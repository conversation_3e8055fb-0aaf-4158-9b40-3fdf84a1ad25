<template>
  <div ref="formReview" class="br-8 form-review">
    <div v-loading="loading" class="br-8 flex form-review_main">
      <!-- 头部 -->
      <div class="base-padding bc-white form-review_header">
        <div class="flex header-title justify-space">
          <div class="flex header-title_left">
            <span
              class="number"
              :class="{
                'cursor-pointer': dialogType === 'batchAdd',
                'number-color': dialogType === 'batchAdd'
              }"
              @click="jumpToDetail"
              >{{ headData?.title?.name }}</span
            >
            <div v-if="headData?.tags?.length" class="tag">
              <el-tag v-for="(tag, index) in headData?.tags" :key="index" type="warning">{{
                tag.value
              }}</el-tag>
            </div>
          </div>
          <div class="header-title_right">
            <el-tooltip :content="$t('CRM.addNewFollow')" placement="top">
              <i class="icon-notification-3-line operate-icon t-iconfont" @click="addReminder"></i>
            </el-tooltip>
            <el-tooltip
              v-if="workTaskObj?.linkList?.linkFormId"
              :content="$t('CRM.addWork')"
              placement="top"
            >
              <i
                class="icon-checkbox-multiple-line operate-icon t-iconfont"
                @click="workTaskDialog = true"
              ></i>
            </el-tooltip>
            <i class="operate-icon web-icon-close-line web-iconfont" @click="closeDialog"></i>
          </div>
        </div>
        <div class="br-8 header-second">
          <ul class="flex flex-1 justify-space">
            <li
              v-for="(item, index) in headData?.secondTitle"
              :key="index"
              class="header-second_item"
            >
              <div class="s-key">{{ item.name }}</div>
              <div class="s-value" :title="item.value">{{ item.value || '-' }}</div>
            </li>
            <!-- <li
              v-show="!reviewStatus.showReviewRecord"
              class="cursor-pointer expand size-12"
              @click="expandReview('showReviewRecord')"
            >
              <i class="el-icon-caret-right"></i>
              {{ $t('operation.spread') }}{{ $t('review.reviewLog') }}
            </li> -->
          </ul>
        </div>
      </div>
      <!-- 销售阶段 -->
      <div
        v-if="stageDataInfo && showStage"
        ref="stageContent"
        class="base-margin bc-white br-8 form-review_stage"
        :class="{ 'not-allowed': dialogType === 'edit' }"
      >
        <!--阶段推进器-->
        <div
          ref="stageThrusterRef"
          class="stage-container"
          :class="{ 'pointer-none': dialogType === 'edit' }"
        >
          <StageThruster
            :allow-back-stage="stageDataInfo.allowBackStage"
            :allow-trans-customer="stageDataInfo.allowTransCustomer"
            :detail-loading="stageDataInfo.detailLoading"
            :form-query="formQuery"
            :is-ass="stageDataInfo.childPropQuery?.isAss"
            :is-review="true"
            :lose-explain-list="stageDataInfo.loseExplainList"
            :query="query"
            :stage-list="stageDataInfo.customerStage"
            :stage-process-id="stageDataInfo.stageProcessId"
            :start-stage="stageDataInfo.startStage"
            :version="stageDataInfo.version"
            @createContract="createContract"
            @refreshReview="refreshReview"
          />
        </div>
      </div>
      <!-- 内容部分 -->
      <div ref="formReviewContent" class="base-margin flex form-review_content">
        <!-- 左侧表单 -->
        <div class="bc-white br-8 content_left flex flex-1 flex-column justify-space">
          <div class="left-form overflow-y-scroll p-20">
            <!-- review指标 -->
            <div v-if="showIndicatorsForm">
              <div
                class="cursor-pointer expand-title size-12"
                :style="titleStyle(reviewIndicatorsForm.lineType)"
                @click="expandReview(reviewIndicatorsForm.showFlag)"
              >
                <i
                  :class="[
                    reviewStatus[reviewIndicatorsForm.showFlag]
                      ? 'el-icon-caret-bottom'
                      : 'el-icon-caret-right'
                  ]"
                ></i>
                {{ reviewIndicatorsForm.title }}
              </div>
              <div v-show="reviewStatus[reviewIndicatorsForm.showFlag]">
                <div v-if="reviewIndicatorsForm.nuwaForm && reviewIndicatorsForm.nuwaForm.schema">
                  <FormEngine
                    :ref="reviewIndicatorsForm.ref"
                    :form="reviewIndicatorsForm.nuwaForm"
                  />
                </div>
              </div>
            </div>
            <!-- review反馈 -->
            <div>
              <div
                class="cursor-pointer expand-title size-12"
                :style="titleStyle(reviewFeedbackForm.lineType)"
                @click="expandReview(reviewFeedbackForm.showFlag)"
              >
                <i
                  :class="[
                    reviewStatus[reviewFeedbackForm.showFlag]
                      ? 'el-icon-caret-bottom'
                      : 'el-icon-caret-right'
                  ]"
                ></i>
                {{ reviewFeedbackForm.title }}
              </div>
              <div v-show="reviewStatus[reviewFeedbackForm.showFlag]">
                <div v-if="reviewFeedbackForm.nuwaForm && reviewFeedbackForm.nuwaForm.schema">
                  <FormEngine :ref="reviewFeedbackForm.ref" :form="reviewFeedbackForm.nuwaForm" />
                </div>
              </div>
            </div>
          </div>

          <div class="content-end flex left-footer">
            <el-button size="small" @click="submitReviewForm">
              {{ dialogType === 'batchAdd' ? $t('review.submitAndClose') : $t('operation.submit') }}
            </el-button>
            <el-button
              v-if="currentIndex < dataIdList.length - 1 && dialogType === 'batchAdd'"
              size="small"
              @click="submitAndNextItem"
            >
              {{ $t('review.submitAndNext') }}
            </el-button>
          </div>
        </div>
        <!-- 右侧历史记录 -->
        <div
          v-show="reviewStatus.showReviewRecord"
          ref="reviewLog"
          class="bc-white br-8 content_right overflow-y-scroll p-20"
        >
          <div class="journey-title">
            <!-- @click="expandReview('showReviewRecord')" -->
            <!-- <i class="el-icon-caret-bottom"></i> -->
            {{ $t('journey.journey') }}
          </div>
          <div>
            <Journey
              :key="journeyKey"
              class="review-journey"
              :form-query="query"
              :hide-edit="true"
              :is-load-more.sync="isLoadMore"
              :query="query"
              :review-default-journey.sync="reviewDefaultJourney"
              :show-quick-add-follow="false"
            ></Journey>
          </div>
        </div>
      </div>
    </div>

    <linkAddWorkTask
      v-if="workTaskDialog"
      :append-to-body="true"
      :query="query"
      :show="workTaskDialog"
      :task-info="cloneTaskInfo || {}"
      @update:show="(val) => (workTaskDialog = val)"
    ></linkAddWorkTask>

    <!-- 设置跟进提醒 -->
    <el-dialog
      :append-to-body="true"
      class="br-8"
      :title="$t('CRM.addNewFollow')"
      :visible.sync="showNotifyDialog"
      width="600px"
    >
      <el-form ref="form" :model="form">
        <el-form-item
          :label="$t('CRM.nextFollowUpTime')"
          :rules="[
            {
              required: true,
              message: $t('formDesign.pleaseChoose') + $t('CRM.nextFollowUpTime'),
              trigger: 'change'
            }
          ]"
        >
          <el-select v-model="setReminder" class="notify-time-select" @change="changeReminder">
            <el-option
              v-for="item in remindsArr"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="content-end flex">
        <el-button @click="showNotifyDialog = false">{{ $t('recommend.cancel') }}</el-button>
        <el-button type="primary" @click="submitNotify">{{ $t('recommend.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex'
// import FormDataDetail from '@/components/form-data-detail'

import StageThruster from './stage-thruster.vue'
import journey from '@/components/form-data-detail/components/tabs/journey/index.vue'
// nuwa
import { Form } from '@nuwa/core'
import { generateFormData } from '@/components/nuwa/utils/generateFormData'
import { generateEffects } from '@/components/nuwa/effects'
import { generateFieldStateList } from '@/components/nuwa/utils/generateBaseFieldState.js'
import FormEngine from '@/components/nuwa/components/form-engine.vue'
import { setFormatData } from '@/components/form-data-edit/utils.js'
import businessEnum from '@/constants/common/business-type'
import tempVue from '@/utils/temp-vue'
import { removeSubId } from '@/components/form-data-edit/utils.js'

import xbb from '@xbb/xbb-utils'
import {
  getReviewHeadData,
  getReviewFormData,
  addOpportunityReview,
  getReviewUpDate
} from '@/api/opportunity-review'
import { formUpdate } from '@/api/formData.js'

import { batchPushNotify } from '@/api/batch-handle'

import moment from 'moment'
import { getDetailLinkAdd } from '@/api/form-detail-tabs'
import { getDetailStage } from '@/api/form-detail-tabs'
import utils from '@/utils'

export default {
  name: 'AddOpportunityReview',
  components: {
    StageThruster,
    FormEngine,
    Journey: journey,
    // FormDataDetail: () => import('@/components/form-data-detail'),
    LinkAddWorkTask: () =>
      import('@/components/form-data-detail/components/dialog/link-add-work-task.vue')
  },
  props: {
    dataIdList: [Array],
    dialogType: [String],
    reviewId: [Number],
    taskInfo: [Object],
    dialogVisible: [Boolean],
    // 详情页的query
    query: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      parentDetail: {
        showChildrenDetail: this.showChildrenDetail
      }
    }
  },
  data() {
    return {
      reviewDefaultJourney: 'reviewLog',
      journeyKey: Math.random(),
      headData: {},
      stageDataInfo: {}, // 销售阶段信息
      // 控制review表单展示状态
      reviewStatus: {
        showReviewRecord: true, //展示右侧review历史记录内容
        showIndicators: true, // 展示商机指标
        showFeedback: true // 展示商机反馈
      },
      // 指标表单
      reviewIndicatorsForm: {
        ref: 'formDataForm_rm',
        title: this.$t('review.opportunityIndicators'),
        nuwaForm: {}, // 用于渲染的nuwa格式表单
        sourceForm: {}, // 接口返回的源数据
        showFlag: 'showIndicators'
      },
      // review反馈
      reviewFeedbackForm: {
        ref: 'formDataForm_ro',
        title: this.$t('review.reviewFeedback'),
        nuwaForm: {}, // 用于渲染的nuwa格式表单
        sourceForm: {}, // 接口返回的源数据
        showFlag: 'showFeedback'
      },
      // 是否展示指标表单
      showIndicatorsForm: true,

      workTaskDialog: false, // 控制工作任务弹框
      // 跟进提醒相关
      remindsArr: [
        //设置跟进提醒的选项
        { value: 2, text: this.$t('followUpEntry.afterhalfAnHour'), time: 30, unit: 'minutes' },
        { value: 3, text: this.$t('followUpEntry.afterHour', { num: 1 }), time: 1, unit: 'hours' },
        { value: 4, text: this.$t('followUpEntry.afterHour', { num: 2 }), time: 2, unit: 'hours' },
        { value: 5, text: this.$t('followUpEntry.afterDay', { num: 1 }), time: 1, unit: 'days' },
        { value: 6, text: this.$t('followUpEntry.afterDay', { num: 2 }), time: 2, unit: 'days' },
        { value: 7, text: this.$t('followUpEntry.afterDay', { num: 3 }), time: 3, unit: 'days' },
        { value: 8, text: this.$t('followUpEntry.afterWeek'), time: 7, unit: 'days' },
        { value: 1, text: this.$t('nouns.custom') }
      ],
      showNotifyDialog: false,
      setReminder: null, // 跟进提醒选中项
      form: {}, // 跟进提醒时间表单
      linkData: {}, // 存设置跟进提醒的默认值
      linkForm: {}, // 存请求跟进提醒解释的参数
      loading: false,
      reviewFormQuery: {}, // 这里存的是review表单的query数据，与机会表单不同
      showStage: false,
      isLoadMore: false
    }
  },
  computed: {
    ...mapGetters({
      useNuwa: 'useNuwa',
      currentIndex: 'proList/getCurrentIndex'
    }),

    // 阶段推进器需要的，多余的参数会存在报错，所以单独存一份特殊的
    formQuery() {
      const { appId, menuId, formId, saasMark, distributorMark, businessType } = this.query
      return {
        appId,
        menuId,
        formId, // saas业务里没有formId，但一些通用接口要求有formId且formId必须为正数
        saasMark,
        distributorMark,
        businessType
      }
    },
    workTaskObj() {
      return this.headData.workFlow?.find((el) => el.attr === 'workTask')
    },
    cloneTaskInfo() {
      return {
        ...this.workTaskObj.linkList,
        defaultLinkInfo: {
          dataId: this.query.dataId,
          linkList: this.workTaskObj.linkList,
          formId: this.query.formId,
          saasMark: this.query.saasMark,
          businessType: this.query.businessType
        },
        linkAdd: true
      }
    }
  },
  mounted() {
    if (!utils.LS.get('reviewJourney')) {
      console.log(this.reviewDefaultJourney, '---------------------------------')
      utils.LS.set('reviewJourney', this.reviewDefaultJourney)
    } else {
      this.reviewDefaultJourney = utils.LS.get('reviewJourney')
    }
    tempVue.$on('formDataEditSuccess', this.refreshJourney)
    this.init()
    this.$nextTick(() => {
      setTimeout(() => {
        this.addLoadMore()
      })
    })
    this.$once('hook:beforeDestroy', function () {
      tempVue.$off('formDataEditSuccess', this.refreshJourney)
    })
  },
  methods: {
    showChildrenDetail(query) {
      const { dataId, businessType, saasMark } = query
      const childrenDetailQuery = { dataId, businessType, saasMark, isReviewToDetail: true }
      this.$emit('jumpToDetail', { childrenDetailQuery, childrenShow: true })
    },
    refreshJourney() {
      this.journeyKey = Math.random()
    },
    init() {
      this.getHeadData()
      this.getFormData()
    },
    refreshReview() {
      // this.init()
      this.getStageData()
    },
    titleStyle(lineType) {
      const lineTypeMap = {
        thin: 'solid',
        thick: 'solid',
        nothing: '',
        dashed: 'dashed'
      }
      const bWidth = lineType === 'thick' ? '2px' : '1px'
      return {
        'border-bottom': `${bWidth} ${lineTypeMap[lineType]} #E5E6EB`
      }
    },
    addReminder() {
      this.showNotifyDialog = true
      this.setReminder = null
      this.getRelyRemind()
    },
    ...mapActions({
      formDetailLinkAdd: 'formDetailLinkAdd',
      setCurrentIndex: 'proList/setCurrentIndex'
    }),
    ...mapMutations(['proList/SET_CURRENT_INDEX']),

    // 面包屑中客户成生成合同、机会赢单生成合同
    createContract(params) {
      const obj = {
        ...params,
        dataIdList: [this.dataIdList[this.currentIndex]],
        getFormDataUrl: 'createContract'
      }
      this.formDetailLinkAdd(obj)
    },

    closeDialog() {
      this.$emit('changeDialog', false)
    },

    // 控制展开商机指标、商机反馈
    expandReview(value) {
      this.reviewStatus[value] = !this.reviewStatus[value]
    },
    // 获取商机review页-头部摘要
    getHeadData() {
      const apiParams = {
        ...this.query,
        subBusinessType: this.$route.subBusinessType,
        formRef: 1, // 商机review标识
        dataId: this.dataIdList[this.currentIndex]
      }
      delete apiParams.distributorMark
      getReviewHeadData(apiParams)
        .then(({ result }) => {
          this.headData = result.head
        })
        .catch((err) => this.closeDialog())
    },
    // 处理一下nuwa表单需要的配置
    handleNuwaConfigData(form, formType) {
      const fieldExplainList = xbb.deepClone(form.explainList)
      let formQuery, effects

      const otherConfig = {
        assignmentRules: form.assignmentRules, // 控制隐藏字段，但值和相关数据保留
        // mode: 'add', // 表单模式：preview(预览)、edit(编辑)、add(新建)、newVersion、design(设计)、flow(审批)
        isSee: false, // 是否是查看模式
        isChild: false, // 是否是内部新建弹窗
        isCopy: false, // 是否是复制表单
        isUsedDraft: false, // 是否使用草稿
        isFromTransfer: true
      }
      if (formType === 'Feedback') {
        //review反馈表单
        formQuery = {
          ...this.reviewFormQuery,
          subBusinessType: businessEnum.SALES_OPPORTUNITY_REVIEW,
          dataId: this.dataIdList[this.currentIndex]
        }
        otherConfig['mode'] = 'add' // 对于反馈表单，review时实际执行的是新建操作
        effects = generateEffects(fieldExplainList, generateFormData(form.explainList))
      } else {
        // 指标-用的是销售机会的formQuery
        formQuery = {
          ...this.query,
          subBusinessType: businessEnum.SALES_OPPORTUNITY,
          dataId: this.query.dataId
        }
        otherConfig['mode'] = 'edit' // 对于指标表单，review时实际执行的是编辑操作，(编辑机会)
      }
      const subFormAttrArr = form.explainList
        .filter((el) => el.fieldType === 10006)
        .map((k) => k.attr)
      subFormAttrArr.forEach((item) => {
        if (Array.isArray(form.data.data[item])) {
          form.data.data[item] = form.data.data[item].map((el) => {
            return {
              ...el,
              subId: xbb.guid()
            }
          })
        }
      })
      const values = form?.data ? form.data?.data : generateFormData(form.explainList)
      return { fieldExplainList, otherConfig, values, formQuery, effects }
    },
    // 获取review表单信息（指标+反馈）
    getFormData() {
      this.loading = true
      let params, promise, apiFeedBackFormName
      if (this.dialogType === 'add' || this.dialogType === 'batchAdd') {
        ;(params = {
          formRef: 1, // 商机指标
          relyFormDataUpdateGetDTO: {
            ...this.query,
            dataId: this.dataIdList[this.currentIndex]
          }
        }),
          delete params.relyFormDataUpdateGetDTO.subBusinessType
        delete params.relyFormDataUpdateGetDTO.del
        apiFeedBackFormName = 'formDataAddGetVO' // review反馈的编辑与新建，接口返回的名称不一致加个处理
        promise = getReviewFormData
      } else {
        ;(params = {
          formRef: 1,
          dataId: this.reviewId || this.query.dataId // 商机review数据id
        }),
          (promise = getReviewUpDate),
          (apiFeedBackFormName = 'formDataUpdateGetVO') // 由于接口返回的名称不一致加个处理
      }
      promise(params)
        .then(({ result }) => {
          const { id, appId, businessType, menuId, sassMark } =
            result[apiFeedBackFormName].paasFormEntity
          this.reviewFormQuery = {
            formId: id,
            appId,
            businessType,
            subBusinessType: businessType,
            menuId,
            sassMark
          }
          // 处理 nuwa 表单配置数据
          const handleConfig = (form, formType) => {
            const config = this.handleNuwaConfigData(form, formType)
            return new Form({
              schema: config.fieldExplainList,
              values: config.values,
              // generateFieldStateList 三个参数：fieldList, otherConfig, formQuery
              state: generateFieldStateList(
                config.fieldExplainList,
                config.otherConfig,
                config.formQuery
              ),
              //
              formParams: {
                ...config.otherConfig,
                ...config.formQuery
              },
              effects: config.effects
              // debug: true
            })
          }
          // this.handleLine(result.formDataAddGetVO)
          // 指标表单
          if (result.relyFormDataUpdateGteVO) {
            // const
            this.$set(this, 'reviewIndicatorsForm', {
              ...this.reviewIndicatorsForm,
              nuwaForm: result.relyFormDataUpdateGteVO
                ? handleConfig(result.relyFormDataUpdateGteVO, 'Indicators')
                : {},
              sourceForm: result.relyFormDataUpdateGteVO ?? {}
            })
            this.showStage = Boolean(
              this.reviewIndicatorsForm.sourceForm.explainList.some(
                (item) => item.attr === 'text_17'
              )
            )

            if (this.showStage) {
              // 指标存在销售阶段，才请求接口
              this.getStageData()
            }
          } else {
            this.showIndicatorsForm = false
          }
          this.reviewIndicatorsForm['lineType'] = result[apiFeedBackFormName].explainList.find(
            (item) => item.attr === 'other_1'
          ).lineType
          this.reviewIndicatorsForm['title'] = result[apiFeedBackFormName].explainList.find(
            (item) => item.attr === 'other_1'
          ).attrName
          // review反馈
          this.$set(this, 'reviewFeedbackForm', {
            ...this.reviewFeedbackForm,
            nuwaForm: result[apiFeedBackFormName]
              ? handleConfig(result[apiFeedBackFormName], 'Feedback')
              : {},
            sourceForm: result[apiFeedBackFormName] ?? {},
            lineType: result[apiFeedBackFormName].explainList.find(
              (item) => item.attr === 'other_2'
            ).lineType,
            title: result[apiFeedBackFormName].explainList.find((item) => item.attr === 'other_2')
              .attrName
          })
        })
        .catch(() => {
          // 接口存在报错，不展示弹窗
          this.closeDialog()
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 监听滚动来判断review历史是否需要加载下一页
    addLoadMore() {
      const scrollAdd = this.$refs['reviewLog']
      if (scrollAdd) {
        scrollAdd.addEventListener('scroll', () => {
          const scrollTop = scrollAdd.scrollTop
          const blockHeight = scrollAdd.clientHeight
          const scrollHeight = scrollAdd.scrollHeight
          if (
            scrollHeight - (scrollTop + blockHeight) <= 50 &&
            scrollHeight - (scrollTop + blockHeight) >= 0
          ) {
            this.isLoadMore = true
          } else {
            this.isLoadMore = false
          }
        })
      }
    },

    // 格式化提交接口中的dataLis
    formatSubmitFormData(form) {
      if (!form) return
      let formData
      if (form.nuwaForm && form.nuwaForm?.getValue) {
        formData = form.nuwaForm.getValue() || {}
      }
      let dataList
      if (formData) {
        dataList = setFormatData(
          JSON.parse(JSON.stringify(formData)) || {},
          form.sourceForm.explainList
        )
      }
      const attrList = form?.sourceForm?.explainList?.map((item) => item.attr) || []
      return {
        dataList,
        attrList,
        coUserId: formData?.coUserId
      }
    },

    getFormValidate(form) {
      return new Promise((resolve, reject) => {
        if (form?.validate) {
          resolve(form.validate())
        } else {
          resolve(true)
        }
      })
    },
    // 提交review表
    submitReviewForm(submitType) {
      this.loading = true
      // indicatorsForm存储指标表单原始data数据，indicatorsFormValue存储指标表单nuwa数据
      let indicatorsForm, indicatorsFormValue
      if (this.reviewIndicatorsForm && this.showIndicatorsForm) {
        indicatorsForm = this.reviewIndicatorsForm?.sourceForm?.data || {}
        if (this.reviewIndicatorsForm?.nuwaForm?.getValue) {
          indicatorsFormValue = this.reviewIndicatorsForm.nuwaForm.getValue() || {}
        }
      }
      const feedbackForm = this.reviewFeedbackForm?.sourceForm
      // 关闭review弹窗标识，提交并跳转至下一个时，不需要关闭
      const closeDialogFlag =
        submitType === 'submitAndNext' && this.dialogType === 'batchAdd' ? true : false
      const { attrList = [], dataList = [] } = this.formatSubmitFormData(this.reviewIndicatorsForm)
      let relyFormUpdateDTO
      if (!this.reviewIndicatorsForm && !indicatorsForm) {
        // 没有指标字段传false
        relyFormUpdateDTO = false
      } else {
        const newDataList = removeSubId(dataList)
        relyFormUpdateDTO = {
          // 商机指标
          formRef: 1,
          appId: indicatorsForm.appId,
          menuId: indicatorsForm.menuId,
          formId: indicatorsForm.formId,
          saasMark: indicatorsForm.saasMark,
          businessType: indicatorsForm.businessType,
          dataId: indicatorsForm.dataId,
          distributorMark: 0,
          ownerId: indicatorsFormValue?.ownerId || [],
          attrList,
          dataList: {
            ...newDataList,
            ownerId: indicatorsFormValue?.ownerId || undefined // 后端要求和attrList统一
          },
          serialNo: dataList.serialNo,
          coUserId: indicatorsFormValue?.coUserId || []
        }
      }
      const feedBackDataList = this.reviewFeedbackForm.nuwaForm.getValue()
      const formRefAddDTOData = {
        // 商机review反馈
        appId: feedbackForm.paasFormEntity.appId,
        menuId: feedbackForm.paasFormEntity.menuId,
        formId: feedbackForm.paasFormEntity.id,
        saasMark: feedbackForm.paasFormEntity.saasMark,
        businessType: feedbackForm.paasFormEntity.businessType,
        subBusinessType: feedbackForm.paasFormEntity.businessType,
        distributorMark: 0,
        dataList: feedBackDataList
      }
      let params, submitApi
      if (this.dialogType === 'add' || this.dialogType === 'batchAdd') {
        // 如果商机指标全部被删除时，传relyFormUpdateDTO为false
        params = {
          formRef: 1, // 可以写死
          hasRelyData: this.showIndicatorsForm, // 是否有商机指标数据。
          refFormId: this.query.formId, // 商机表单id
          refDataId: this.dataIdList[this.currentIndex], // 商机数据id
          formRefAddDTO: formRefAddDTOData
        }
        if (this.showIndicatorsForm) {
          params['relyFormUpdateDTO'] = relyFormUpdateDTO
        }
        submitApi = addOpportunityReview
      } else {
        // 因为编辑时需求只可编辑review反馈部分，所以编辑接口只要求传review反馈
        params = {
          ...formRefAddDTOData,
          dataId: this.reviewId, // 商机review数据id
          serialNo: feedbackForm?.data?.data?.serialNo
        }
        submitApi = formUpdate
      }
      Promise.all([
        this.getFormValidate(this.reviewIndicatorsForm.nuwaForm),
        this.getFormValidate(this.reviewFeedbackForm.nuwaForm)
      ])
        .then(() => {
          submitApi(params)
            .then((res) => {
              this.$message({
                type: 'success',
                message: res.msg || this.$t('message.operateSuccessSymbol')
              })
              this.$emit('changeDialog', closeDialogFlag)
              this.$emit('refreshDetail')
              if (closeDialogFlag && this.dialogType === 'batchAdd') {
                if (this.currentIndex === this.dataIdList.length) return
                this.setCurrentIndex(this.currentIndex + 1)
                this.init()
              }
            })
            .catch((err) => {})
            .finally(() => {
              this.loading = false
            })
        })
        .catch((err) => {
          if (err.errorFields.length) {
            this.$message({
              message: err.errorFields[0].err,
              type: 'error'
            })
          }
          this.loading = false
        })
      // .finally((err) => {
      //   console.log('🚀 ~ submitReviewForm ~ err:', err)
      //   this.loading = false
      // })
    },
    // 提交并跳转下一个 （批量）
    submitAndNextItem() {
      this.submitReviewForm('submitAndNext')
    },

    // 选择跟进时间
    async changeReminder() {
      if (this.setReminder === 1) {
        // 自定义打开提醒弹窗
        const params = {
          businessType: businessEnum.FOLLOW_UP_REMINDER, // 跟进提醒businessType,
          dataIdList: [this.dataIdList[this.currentIndex]],
          linkBusinessType: this.query.businessType || -1,
          saveUrl: 'batchPushNotify'
        }
        await this.formDetailLinkAdd(params)
        this.showNotifyDialog = false
      } else {
        // 有具体时间，手动调接口
        if (this.linkData) {
          const currentRemind = this.remindsArr.find((item) => item.value === this.setReminder)
          this.linkData['date_1'] = Number(
            moment().add(currentRemind.time, currentRemind.unit).format('X')
          )
          this.linkData['date_2'] = Number(
            moment().add(currentRemind.time, currentRemind.unit).format('X')
          )
          this.linkData['text_5'] =
            this.linkData['text_2'][0].name + this.$t('message.timelyRemind')
          this.linkData['num_2'] = 0
          this.linkData['amountDetail'] = null
        }
      }
    },
    // 获取依赖的跟进提醒数据  复用原跟进提醒逻辑
    getRelyRemind() {
      // 如果展示跟进提醒，则先请求跟进提醒的解释，设置默认值
      const params = {
        businessType: businessEnum.FOLLOW_UP_REMINDER,
        dataIdList: [this.dataIdList[this.currentIndex]],
        linkBusinessType: this.query.businessType || -1,
        saasMark: 1
      }
      getDetailLinkAdd(params).then((res) => {
        this.linkForm = {
          appId: res.result.paasFormEntity.appId,
          menuId: res.result.paasFormEntity.menuId,
          businessType: res.result.paasFormEntity.businessType,
          saasMark: res.result.paasFormEntity.saasMark
        }
        const dataList = res.result.explainList.map((item) => {
          return {
            key: item.attr,
            value: null
          }
        })
        dataList.forEach((item) => {
          this.linkData[item.key] = item.value
        })
        this.linkData.text_1 = res.result.explainList.find(
          (item) => item.attr === 'text_1'
        ).defaultAttr.defaultValue
        this.linkData.text_2 = res.result.explainList.find(
          (item) => item.attr === 'text_2'
        ).defaultAttr.linkFormValue
        const textThreeArr = res.result.explainList.find((item) => item.attr === 'text_3').items
        this.linkData.text_3 = {
          text: textThreeArr[textThreeArr.length - 1].text,
          value: textThreeArr[textThreeArr.length - 1].value
        }
        const textFourArr = res.result.explainList.find((item) => item.attr === 'text_4').items[0]
        this.linkData.text_4 = {
          text: textFourArr.text,
          value: textFourArr.value
        }
        this.linkData.array_1 = [
          res.result.explainList.find((item) => item.attr === 'array_1').items[0]
        ]
      })
    },
    submitNotify() {
      batchPushNotify({
        ...this.linkForm,
        ...this.query,
        isBatch: 0,
        dataIdList: [this.dataIdList[this.currentIndex]],
        dataList: this.linkData
      }).then((res) => {
        this.$message.success(this.$t('message.followUpReminderSetSuccess'))
        this.showNotifyDialog = false
      })
    },

    jumpToDetail() {
      // 批量新建review才支持点击编号跳转至详情
      if (this.dialogType !== 'batchAdd') {
        return
      }
      const { appId, saasMark, formId, businessType, menuId } = this.query
      const childrenDetailQuery = {
        formId,
        saasMark,
        businessType,
        subBusinessType: this.$route.subBusinessType,
        appId,
        menuId,
        dataId: this.dataIdList[this.currentIndex],
        isReviewToDetail: true
      }
      const childrenShow = true
      this.$emit('jumpToDetail', { childrenDetailQuery, childrenShow })
    },
    // 获取销售阶段信息
    getStageData() {
      getDetailStage(this.query)
        .then(
          ({
            result: {
              allowBackStage,
              allowTransCustomer,
              explainList,
              processVersionName,
              stageProcessId,
              stageShowType,
              startStage,
              stageList
            },
            success
          }) => {
            if (success) {
              this.stageDataInfo = {
                stageShowType: stageShowType,
                startStage: startStage,
                customerStage: stageList,
                loseExplainList: explainList,
                stageProcessId: stageProcessId,
                allowBackStage: allowBackStage,
                version: processVersionName,
                allowTransCustomer: allowTransCustomer
              }
            }
          }
        )
        .catch((err) => this.closeDialog())
    }
  }
}
</script>

<style lang="scss" scoped>
.base-padding {
  padding: 20px 32px;
}
.base-margin {
  margin: 12px;
}
.notify-time-select {
  width: calc(100% - 108px);
}
.pointer-none {
  pointer-events: none;
}
.not-allowed {
  cursor: not-allowed;
}
.journey-title {
  padding-bottom: 14px;
  font-size: 14px;
  color: $text-main;
  // border-bottom: 1px solid $neutral-color-3;
}
.review-journey {
  :deep(.el-tabs) {
    .el-tabs__item {
      padding: 0;
      padding-right: 32px;
    }
    .el-tabs__item:last-child {
      padding: 0;
    }
  }
}
</style>

<style lang="scss">
.form-review {
  height: 100%;
  &_main {
    flex-direction: column;
    height: 100%;
  }
  &_header {
    padding: 20px 32px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    .header-title {
      margin-bottom: 26px;
      &_left {
        .number {
          max-width: 400px;
          font-size: 20px;
          // 文本溢出隐藏
          @include singleline-ellipsis();
        }
        .number-color {
          color: $link-base-color-6;
        }
        .tag {
          // max-width: 300px;
          // 文本溢出隐藏
          @include singleline-ellipsis();
        }
        .el-tag {
          margin-left: 8px;
        }
      }
      &_right {
        align-items: center;
      }
    }
    .header-second {
      &_item {
        max-width: 252px;
        margin-right: 12px;
      }
      .s-key {
        margin-bottom: 14px;
        font-size: 14px;
        color: $text-auxiliary;
      }
      .s-value {
        // 文本溢出隐藏
        @include singleline-ellipsis();
      }
      .expand {
        display: block;
        width: 135px;
        color: $text-auxiliary;
      }
    }
  }
  &_stage {
    margin-bottom: 0 !important;
    overflow: hidden;
    .stage-container {
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
    }
  }
  &_content {
    flex: 1;
    height: 0;
    padding-bottom: 14px;
    margin-bottom: 0 !important;
    overflow: hidden;
    .content_left {
      width: 70%;
      margin-right: 12px;
      .left-footer {
        padding: 12px 20px;
        border-top: 1px solid $neutral-color-3;
      }
    }
    .content_right {
      width: 30%;
    }
  }
  .expand-title {
    padding-bottom: 8px;
    margin-bottom: 20px;
    color: $text-auxiliary;
  }
  .br-t {
    border-bottom: 1px dashed $neutral-color-3;
  }
  .operate-icon {
    margin-left: 20px;
    font-size: 20px;
    cursor: pointer;
    &:hover {
      color: $brand-color-5;
    }
  }
}
</style>
