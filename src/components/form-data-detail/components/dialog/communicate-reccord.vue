<!--
 * @Description: 跟进记录组件（不存在非访客计划与全景图跟进记录了！！！都是同一个跟进记录！！！）
 -->

<template>
  <div
    v-loading="loading"
    class="communicate-reccord"
    element-loading-spinner="el-icon-loading"
    :element-loading-text="$t('followUpEntry.formSubmit')"
  >
    <div class="communicate">
      <!-- 录入 -->
      <PicturesRemarksAttachments
        ref="fusionEntryFiles"
        v-model="textareaValue"
        active-name-model="communicateRecord"
        :explain-list="explainList"
        :field-info="followContentFieldInfo"
        :file-and-img="fileAndImg"
        :file-and-img-val="fileAndImgVal"
        :form-data="formData"
        :form-query="formQuery"
        :is-content-requied="followContentFieldInfo && followContentFieldInfo.required"
        :query="query"
        @fileChange="fileChange"
      ></PicturesRemarksAttachments>
      <div class="communicate-form">
        <FormDataForm
          v-if="formDataFormShow && formDataFormIsNull"
          ref="formDataForm"
          :app-id="formQuery.appId"
          :base-form-data="formData"
          :business-type="communicateBusinessType"
          :data-id="query.dataId"
          :double-column="true"
          :field-list="explainList"
          :field-position="fieldPosition"
          :form-data="formData"
          :form-id="recordFormId || formQuery.formId"
          :hide-link-style="true"
          :is-design="isDesign"
          :is-edit="isEdit"
          :is-not-apply-multiseriate="true"
          :menu-id="formQuery.menuId"
          mode="base"
          :run-low-code="!isDesign"
          :saas-mark="formQuery.saasMark"
          :serial-no="serialNoData"
        />
      </div>

      <div class="communicate-options">
        <div class="botton_slot">
          <div v-if="isCommunicateNotify" class="set-reminder">
            {{ $t('form.remindLast') }}
            <el-select v-model="setReminder">
              <el-option
                v-for="item in remindsArr"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <el-button :disabled="disSubmit" size="small" type="primary" @click="handleSubmit">{{
            $t('operation.submit')
          }}</el-button>
          <!-- <el-button size="small" @click="saveDraft">{{ $t('followUpEntry.saveDraft') }}</el-button> -->
          <!-- <slot :disabled="disSubmit" name="cancel"></slot> -->
          <el-button size="small" @click="handleCancel">{{ $t('operation.cancel') }}</el-button>
        </div>
      </div>
    </div>
    <el-dialog
      append-to-body
      custom-class="communicate-plan-dialog"
      :title="$t('business.visitorPlan')"
      :visible.sync="isVisible"
      width="50%"
      @closed="handleCommunicatePlanDialogClosed"
    >
      <div class="dialog-header">
        <el-scrollbar class="header-scrollbar">
          <el-tag
            v-for="(item, index) in selectRows"
            :key="item.id"
            class="slot__header--tag"
            closable
            disable-transitions
            @close="tagClose(index, item)"
          >
            {{ item.name || item.id }}
          </el-tag>
        </el-scrollbar>
      </div>
      <div class="dialog-table">
        <!-- 弹窗身体 - 关联列表 -->
        <LinkTableTable
          :app-id-params="{ appId: formQuery.appId }"
          businessName.sync="访客计划"
          :field-info="{ saasAttr: 'finishCommunicatePlanId' }"
          :finish-plan-data="finishPlanData"
          :is-finish-plan="true"
          :link-business-type="601"
          :multiple="false"
          :select-rows.sync="selectRows"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isVisible = false">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="communicatePlanSave">{{
          $t('formDataDetail.completingVisitors')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// API
import {
  detailTabGetCommunicateAdd,
  detailTabCommunicateSave,
  finishCommunicatePlan,
  hasCommunicatePlanFinish
} from '@/api/form-detail-tabs'
import { getTemplateEnableList } from '@/api/formData'
import { mapGetters, mapMutations, mapActions, mapState } from 'vuex'
import tempVue from '@/utils/temp-vue'
// 业务枚举
import commonBusinessType from '@/constants/common/business-type'
// 表单渲染
import FormDataForm from '@/components/form-data-edit/form-data-form'
import LinkTableTable from '@/components/all-fields/form-data-edit/sys-saas/link-table/components/link-table-table'
import uploadMix from '@/mixin/uploadMixin'
import { setFormatData } from '@/components/form-data-edit/utils.js'
import xbb from '@xbb/xbb-utils'
import PicturesRemarksAttachments from '@/components/all-fields/form-data-edit/pictures-remarks-attachments'
import { getDetailLinkAdd } from '@/api/form-detail-tabs'
import moment from 'moment'
import { batchPushNotify } from '@/api/batch-handle'
import { deepEqual } from '@/utils/unit'
import { setCommunicate } from '@/api/ui-paas/detail-design'
import { watch } from 'vue'

export default {
  name: 'CommunicateReccord',

  components: {
    LinkTableTable,
    FormDataForm,
    PicturesRemarksAttachments
  },

  filters: {},

  mixins: [uploadMix],
  provide: function (params) {
    return {
      ...this.formQuery
    }
  },

  props: {
    isBaseConfig: {
      type: Boolean,
      default: false
    },
    formQuery: {
      default: () => {
        return {}
      }
    },
    query: {
      default: () => {
        return {}
      }
    },
    isDesign: {
      type: Boolean,
      default: false
    },
    tabInfo: {
      default: () => {
        return {}
      }
    }
  },

  data() {
    return {
      remindsArr: [
        //设置跟进提醒的选项
        { value: 0, text: this.$t('form.noRemind') },
        { value: 1, text: this.$t('nouns.custom') },
        { value: 2, text: this.$t('followUpEntry.afterhalfAnHour'), time: 30, unit: 'minutes' },
        { value: 3, text: this.$t('followUpEntry.afterHour', { num: 1 }), time: 1, unit: 'hours' },
        { value: 4, text: this.$t('followUpEntry.afterHour', { num: 2 }), time: 2, unit: 'hours' },
        { value: 5, text: this.$t('followUpEntry.afterDay', { num: 1 }), time: 1, unit: 'days' },
        { value: 6, text: this.$t('followUpEntry.afterDay', { num: 2 }), time: 2, unit: 'days' },
        { value: 7, text: this.$t('followUpEntry.afterDay', { num: 3 }), time: 3, unit: 'days' },
        { value: 8, text: this.$t('followUpEntry.afterWeek'), time: 7, unit: 'days' }
      ],
      linkData: {},
      linkForm: {},
      pageBusinessType: commonBusinessType,
      formDataFormShow: true, // 组件是否销毁
      formDataFormIsNull: false,
      // 是否禁止提交
      disSubmit: false,
      serialNoData: undefined,
      // 跟进记录绑定值
      textareaValue: '',
      formData: {},
      explainList: [],
      originExplainList: [],
      // 新建跟进记录同步完成访客计划弹窗
      isVisible: false,
      selectRows: [],
      finishPlanData: {},
      communicatePlanId: 0,
      recordFormId: 0,
      fileAndImg: [],
      fileAndImgVal: {},
      setReminder: 0,
      loading: false,
      fieldPosition: 'top', //布局,
      isEdit: false, //如果是使用草稿的话要改为编辑模式
      initialValue: {}, //记录一下初始值
      followContentFieldInfo: {}, //跟进内容字段相关解释
      communicateFormQuery: {} // 跟进记录相关表单id 信息
    }
  },
  inject: ['parentDetail'],
  computed: {
    ...mapGetters(['saasObj', 'batchEditButtons', 'bottomPermissions', 'feeType']),
    ...mapState({
      lowCode: function (state) {
        return state.lowcode.instant[this.recordFormId]
      }
    }),
    // 跟进记录业务
    communicateBusinessType() {
      if (
        [this.pageBusinessType.SUPPLIER, this.pageBusinessType.PURCHASE].indexOf(
          this.query.businessType
        ) > -1
      ) {
        // 进销存跟进记录502
        return 502
      } else {
        // CRM跟进记录501
        return 501
      }
    },
    isCommunicateNotify() {
      if (this.tabInfo?.componentConfig?.followUpRecordEntry?.enableRemind === 0) return false
      if (this.isDesign) return true
      // 客户 访客计划 联系人
      const typeKey = [
        commonBusinessType.CUSTOMER_MANAGEMENT,
        commonBusinessType.COMMUNICATE_PLAN,
        commonBusinessType.CONTACT
      ]
      if (typeKey.includes(this.formQuery.businessType)) {
        return this.bottomPermissions.length > 0
      } else {
        return this.batchEditButtons.some((e) => {
          return e.attr === 'communicateNotify'
        })
      }
    }
  },

  watch: {
    isVisible: {
      handler(value, old) {
        // 访客计划弹窗关闭 && 访客计划弹窗之前是打开的 && 设置跟进提醒
        if (value === false && old === true && this.setReminder) {
          this.jumpToRemind()
        }
      }
    },
    textareaValue(value) {
      // 跟进记录内容支持低代码
      // 更新formData的值
      this.formData.text_6 = value
      // 如果有低代码配置的话执行低代码
      const configs =
        this.lowCode &&
        this.lowCode.config &&
        this.lowCode.config.actionList &&
        this.lowCode.config.actionList['text_6']
      if (!configs) return
      for (const config of configs) {
        this.$store.commit('EXECUTE_LOW_CODE', {
          handle: config.code,
          info: {
            formId: this.recordFormId,
            businessType: this.formQuery.businessType,
            subBusinessType: this.formQuery.subBusinessType,
            saasMark: this.formQuery.saasMark,
            appId: this.formQuery.appId
          },
          action: config.list,
          formId: this.recordFormId,
          dataJSON: this.formData
        })
      }
    }
  },

  created() {
    this.init()
    if (this.isCommunicateNotify && !this.isDesign) {
      this.getRelyRemind()
    }

    if (this.tabInfo?.componentConfig?.followUpRecordEntry) {
      watch(
        () => this.tabInfo,
        (val) => {
          if (val.componentConfig.followUpRecordEntry.fieldList?.length > 0) {
            const arr = []
            val.componentConfig.followUpRecordEntry.fieldList.forEach((item) => {
              const target = this.originExplainList.find((n) => n.attr === item)
              if (target && target.attr !== 'text_6' && target.attrType !== 'geo') arr.push(target)
            })
            this.explainList = arr
            this.fileAndImg = []
            this.explainList.forEach((item) => {
              if ((item.attr === 'file_1' || item.attr === 'file_3') && !item.isRedundant) {
                this.fileAndImg.push(item)
                this.$set(this.fileAndImgVal, item.attr, [])
              }
            })
          }
          this.remindsArr = this.tabInfo.componentConfig.followUpRecordEntry.itemList.filter(
            (n) => n.enable !== 0
          )
          if (!this.remindsArr.find((n) => n.value === this.setReminder)) {
            this.setReminder = undefined
          }
        },
        {
          immediate: true,
          deep: true
        }
      )
    }
  },

  mounted() {},

  methods: {
    ...mapMutations({
      setsSaasObj: 'SET_FORM_SAASOBJ'
    }),
    ...mapActions(['formDetailLinkAdd']),
    init() {
      this.explainList = []
      this.getRefVisit()
    },
    execLowCode(data) {
      return new Promise((resolve, reject) => {
        this.$store.commit('EXECUTE_LOW_CODE', {
          ...data,
          callback: (res) => {
            resolve(res)
          }
        })
      })
    },
    getRelyRemind() {
      // 如果展示跟进提醒，则先请求跟进提醒的解释，设置默认值
      const params = {
        businessType: 4100,
        dataIdList: [this.query.dataId],
        linkBusinessType: this.query.businessType || -1,
        saasMark: 1
      }
      getDetailLinkAdd(params).then((res) => {
        this.linkForm = {
          appId: res.result.paasFormEntity.appId,
          menuId: res.result.paasFormEntity.menuId,
          businessType: res.result.paasFormEntity.businessType,
          saasMark: res.result.paasFormEntity.saasMark
        }
        const dataList = res.result.explainList.map((item) => {
          return {
            key: item.attr,
            value: null
          }
        })
        dataList.forEach((item) => {
          this.linkData[item.key] = item.value
        })
        this.linkData.text_1 = res.result.explainList.find(
          (item) => item.attr === 'text_1'
        ).defaultAttr.defaultValue
        this.linkData.text_2 = res.result.explainList.find(
          (item) => item.attr === 'text_2'
        ).defaultAttr.linkFormValue
        const textThreeArr = res.result.explainList.find((item) => item.attr === 'text_3').items
        this.linkData.text_3 = {
          text: textThreeArr[textThreeArr.length - 1].text,
          value: textThreeArr[textThreeArr.length - 1].value
        }
        const textFourArr = res.result.explainList.find((item) => item.attr === 'text_4').items[0]
        this.linkData.text_4 = {
          text: textFourArr.text,
          value: textFourArr.value
        }
        this.linkData.array_1 = [
          res.result.explainList.find((item) => item.attr === 'array_1').items[0]
        ]
      })
    },

    // 跟进记录页面初始
    getRefVisit() {
      this.appInfo = {
        businessType: this.communicateBusinessType, // 跟进记录业务
        appId: this.query.appId
      }
      const copyQuery = JSON.parse(JSON.stringify(this.formQuery))
      delete copyQuery.businessType // 去掉formQuery的businessType
      const request = {
        ...copyQuery, // 传入对应的表单菜单应用信息
        parentBusinessType: this.query.businessType,
        parentId: this.query.dataId,
        isDesign: this.isDesign ? 1 : 0
      }
      if (this.tabInfo?.componentConfig?.followUpRecordEntry) {
        request.componentConfig = this.tabInfo?.componentConfig
      }
      if (this.isDesign) {
        this.handleDesignMode(request)
      } else {
        this.handleNonDesignMode(request)
      }
    },
    handleDesignMode(request) {
      setCommunicate(request)
        .then((data) => {
          this.originExplainList = xbb.deepClone(data.result.explainList)

          this.explainList = data.result.explainList.map((item) => {
            if ((item.attr === 'file_1' || item.attr === 'file_3') && !item.isRedundant) {
              this.fileAndImg.push(item)
              this.$set(this.fileAndImgVal, item.attr, [])
              // 将图片和附件字段的值给formData供低代码调用
              if (!this.isDesign) {
                this.$watch(
                  `fileAndImgVal.${item.attr}`,
                  (value) => {
                    this.formData[item.attr] = value
                  },
                  {
                    deep: true
                  }
                )
              }

              item.visible = 0
            }
            return item
          })
          this.explainList = this.explainList.filter(
            (i) => i.attr !== 'text_6' && i.attrType !== 'geo'
          )
          // 开了 uipaas 化 但是是默认布局 后端不会默认刷进来 所以得前端单独兼容下
          if (this.tabInfo?.componentConfig?.followUpRecordEntry?.fieldList?.length) {
            this.explainList = this.explainList.filter((i) =>
              this.tabInfo.componentConfig.followUpRecordEntry.fieldList.includes(i.attr)
            )
          }
          // 强行补全改跟进记录的所属客户名称key value
          const formData = setFormatData(
            {
              text_1: {}
            },
            this.explainList
          )
          if (data.result.serialNo && 'serialNo' in formData) {
            this.serialNoData = data.result.serialNo
          }
          formData.text_6 = this.textareaValue
          this.formData = formData
          this.formDataFormShow = true
          this.formDataFormIsNull = true
        })
        .catch((err) => {
          console.error(err)
        })
    },
    handleNonDesignMode(request) {
      detailTabGetCommunicateAdd(request)
        .then((data) => {
          let timeout = null
          this.fileAndImg = []
          this.recordFormId = xbb._get(data, 'result.paasFormEntity.id', 0)

          this.followContentFieldInfo = xbb.deepClone(
            data.result.explainList.find((i) => i.attr === 'text_6')
          )
          //如果跟进记录有默认值则需要附上默认值
          if (
            this.followContentFieldInfo &&
            this.followContentFieldInfo.defaultAttr &&
            this.followContentFieldInfo.defaultAttr.defaultValue
          ) {
            this.textareaValue = this.followContentFieldInfo.defaultAttr.defaultValue
          }
          this.originExplainList = xbb.deepClone(data.result.explainList)

          this.explainList = data.result.explainList.map((item) => {
            if ((item.attr === 'file_1' || item.attr === 'file_3') && !item.isRedundant) {
              this.fileAndImg.push(item)
              this.$set(this.fileAndImgVal, item.attr, [])
              // 将图片和附件字段的值给formData供低代码调用
              this.$watch(
                `fileAndImgVal.${item.attr}`,
                (value) => {
                  this.formData[item.attr] = value
                },
                {
                  deep: true
                }
              )
              item.visible = 0
            }
            return item
          })
          this.explainList = this.explainList.filter(
            (i) => i.attr !== 'text_6' && i.attrType !== 'geo'
          )
          // 开了 uipaas 化 但是是默认布局 后端不会默认刷进来 所以得前端单独兼容下
          if (this.tabInfo?.componentConfig?.followUpRecordEntry?.fieldList?.length) {
            this.explainList = this.explainList.filter((i) =>
              this.tabInfo.componentConfig.followUpRecordEntry.fieldList.includes(i.attr)
            )
          }
          // 强行补全改跟进记录的所属客户名称key value
          const formData = setFormatData(
            {
              text_1: {
                id: data.result.saasObj.linkParentId,
                dataId: data.result.saasObj.linkParentId
              }
            },
            this.explainList
          )
          if (data.result.serialNo && 'serialNo' in formData) {
            this.serialNoData = data.result.serialNo
          }
          formData.text_6 = this.textareaValue
          this.formData = formData

          //读取表单草稿
          const draftFormData = utils.LS.get(
            'VUE-follow-record-form-data' + this.query.appId + this.query.dataId
          )
          if (draftFormData) {
            this.$nextTick(() => {
              Object.assign(this.formData, xbb.deepClone(draftFormData))
              // this.formData = { ...this.formData, ...xbb.deepClone(draftFormData) }
              this.setReminder = this.formData.setReminder
            })
            //图片和附件赋值
            for (const index in this.fileAndImgVal) {
              this.fileAndImgVal[index] = xbb.deepClone(draftFormData[index]) || []
            }

            this.isEdit = true
          }

          if (timeout) clearTimeout(timeout)
          //把初始值先存一下，等下做比教
          timeout = setTimeout(() => {
            this.initialValue = xbb.deepClone(this.formData)
          }, 200)

          this.formDataFormShow = true
          this.formDataFormIsNull = true
          this.communicateFormQuery = data.result.paasFormEntity
          this.setsSaasObj(data.result.saasObj) // 详情页跟进记录联系人选择要传关联的ID
        })
        .catch(() => {})
    },
    // 过滤为空数组的数据
    formDataFilter(formData) {
      const newFormData = xbb.deepClone(formData)
      newFormData.dataList = Object.assign({}, newFormData.dataList, this.fileAndImgVal)
      const dataList = xbb.deepClone(newFormData.dataList)

      Object.keys(dataList).forEach((item) => {
        if (Array.isArray(dataList[item]) && dataList[item].length === 0) {
          delete dataList[item]
        } else if (item === 'file_1' || item === 'file_3') {
          //过滤关联数据没有选中的数据
          const arr = this.fileAndImg.filter((obj) => obj['attr'] === item)
          if (arr[0].fieldMapHidden === 1) {
            delete dataList[item]
          }
        }
      })
      newFormData.dataList = xbb.deepClone(dataList)
      return newFormData
    },
    beforeSubmitForLowCode(formData) {
      const info = {
        formInfo: this.formQuery,
        formData
      }
      const arr = xbb._get(this.lowCode, 'config.actionList.form')
      console.log({ arr })
      if (!this.$feeType.checkFeatureEnable('lowcode') || !arr)
        return { flag: true, exection: false }
      return new Promise(async (resolve, reject) => {
        if (!document.querySelector('iframe')) resolve({ flag: true, exection: false })
        let flag = true
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].code === 3) {
            const factoryRes = await this.execLowCode({
              handle: arr[i].code,
              info,
              action: arr[i].list,
              formId: this.recordFormId,
              dataJSON: formData
            })
            if (!factoryRes.success) {
              flag = false
              break
            }
          }
        }
        resolve({ flag, exection: true })
      })
    },
    afterSubmitForLowCode(requestResult) {
      const info = {
        formInfo: this.formQuery,
        requestResult
      }
      const arr = xbb._get(this.lowCode, 'config.actionList.form', [])
      console.log('%c [ arr ] 🐱-523', 'font-size:13px; background:pink; color:#bf2c9f;', arr)
      if (!this.$feeType.checkFeatureEnable('lowcode') || !arr)
        return { flag: true, exection: false }
      return new Promise(async (resolve, reject) => {
        if (!this.$feeType.checkFeatureEnable('lowcode') || !arr) resolve()
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].code === 4) {
            const factoryRes = await this.execLowCode({
              handle: arr[i].code,
              info,
              action: arr[i].list,
              formId: this.recordFormId,
              dataJSON: this.dataJSON
            })
            if (!factoryRes.success) {
              return resolve(factoryRes)
            }
          }
        }
        resolve()
      })
    },
    // 提交跟进记录
    @xbb.debounceWrap()
    handleSubmit() {
      //提交之后把编辑标识取消
      this.isEdit = false
      let val = false //用来标识校验，一次提示一个
      if (!(this.formDataFormShow && this.formDataFormIsNull)) return
      // 校验
      this.$refs['formDataForm'].validate(async (formData) => {
        const res = await this.beforeSubmitForLowCode(formData)
        if (!res.flag) {
          this.btnLoading = false
          return false
        }
        console.log('校验通过')

        const charLimits = this.followContentFieldInfo.charLimits || { max: 2000, min: 0 }
        // 校验跟进记录备注
        if (this.followContentFieldInfo.required) {
          if (!this.textareaValue) {
            this.$message({
              type: 'error',
              message: this.$t('formDataDetail.pleaseInputRecord')
            })
            val = true
            this.$refs.fusionEntryFiles.innerTextCheck()
            return
          } else if (this.textareaValue.length < charLimits.min) {
            this.$message({
              type: 'error',
              message:
                this.followContentFieldInfo.attrName +
                '字符数请控制在' +
                charLimits.min +
                '到' +
                charLimits.max +
                '之间'
            })
            val = true
            this.$refs.fusionEntryFiles.innerTextCheck()
            return
          }
        }
        //图片和附件校验
        if (this.fileAndImg.length) {
          let isReturn = false
          for (const index in this.fileAndImg) {
            const item = this.fileAndImg[index]
            // 设置必填且显示时，才进行校验
            if (
              item.required &&
              item.fieldMapHidden !== 1 &&
              !this.fileAndImgVal[item.attr].length
            ) {
              if (item.attr === 'file_1') {
                //图片校验
                this.$refs.fusionEntryFiles.imageCheck(true)
              } else if (item.attr === 'file_3') {
                //附件校验
                this.$refs.fusionEntryFiles.fileCheck(true)
              }
              if (!val) {
                this.$message({
                  type: 'error',
                  message:
                    this.$t('formDataDetail.pleaseAddSystem') + (item.defaultName || item.attrName)
                })
                val = true
              }
              isReturn = true
            }
          }
          if (isReturn) return
        }
        formData = this.formDataFilter(formData)
        this.sendData(formData)
      })
    },
    sendData(formData) {
      const atUserIds = this.$refs.fusionEntryFiles.getTemplateArr()
      const content = this.$refs.fusionEntryFiles.getTemplate()
      const request = {
        ...this.formQuery,
        menuId: this.communicateFormQuery.menuId,
        ...formData,
        saasNeedRedundantAttrPoJo: {
          memo: content,
          parentBusinessType: this.query.businessType,
          parentId: this.query.dataId,
          atUserIds: atUserIds
        }
      }

      detailTabCommunicateSave(request)
        .then(async (res) => {
          this.$message({
            type: 'success',
            message: res.msg || this.$t('message.operateSuccessSymbol')
          })
          await this.afterSubmitForLowCode(res.result)
          // 当前所建跟进记录的id
          this.communicatePlanId = res.result.formDataId
          let isPlanFinish = false
          // 除经销商业务的跟进记录没有访客计划功能
          if (this.communicateBusinessType === 501 && this.query.distributorMark !== 1) {
            // 判断是否有访客计划
            try {
              isPlanFinish = true
              const res = await this.hasCommunicatePlanFinish(formData)
              if (!res) {
                this.parentDetail && this.parentDetail.detailRefresh()
              }
            } catch (error) {
              console.error('🙆‍♂️🙆🙆‍♀️ ~ .then ~ error:', error)
            }
          }
          this.textareaValue = '' //清空记录内容
          this.$refs['formDataForm'].resetFields()
          this.$refs.fusionEntryFiles.clearOrFiles() //清空上传文件列表
          // 清除当前草稿
          utils.LS.remove('VUE-follow-record-draft-arr' + this.formQuery.appId + this.query.dataId)
          utils.LS.remove('VUE-follow-record-draft' + this.formQuery.appId + this.query.dataId)
          utils.LS.remove('VUE-follow-record-form-data' + this.formQuery.appId + this.query.dataId)

          this.formDataFormShow = false
          this.$nextTick(() => {
            setTimeout(() => {
              // 跟进提醒
              if (this.setReminder && !this.isVisible) {
                this.jumpToRemind()
              }
              this.init() // 更新跟进记录

              tempVue.$emit('updateFormList') // 跟新列表页
              if (!isPlanFinish) {
                this.parentDetail && this.parentDetail.detailRefresh()
                this.$emit('dynamicsSubmit')
              }
            }, globalConst.DELAY_TIME)
          })
        })
        .catch((error) => {
          console.log(error)
        })
        .finally(() => {
          this.loading = true
          // 一秒钟之内，禁止多次重复提交
          this.disSubmit = true
          //初始记录值清空
          this.initialValue = {}
          setTimeout(() => {
            this.loading = false
            this.disSubmit = false
          }, 1000)
        })
    },
    // 跳转至跟进提醒
    jumpToRemind() {
      if (this.setReminder === 1) {
        // 自定义打开提醒弹窗
        const params = {
          businessType: '4100',
          dataIdList: [this.query.dataId],
          linkBusinessType: this.query.businessType || -1,
          saveUrl: 'batchPushNotify'
        }
        this.formDetailLinkAdd(params)
      } else {
        // 有具体时间，手动调接口
        const currentRemind = this.remindsArr.find((item) => item.value === this.setReminder)
        this.linkData['date_1'] = Number(
          moment().add(currentRemind.time, currentRemind.unit).format('X')
        )
        this.linkData['date_2'] = Number(
          moment().add(currentRemind.time, currentRemind.unit).format('X')
        )
        this.linkData['text_5'] = this.linkData['text_2'][0].name + this.$t('message.timelyRemind')
        this.linkData['num_2'] = 0
        this.linkData['amountDetail'] = null

        batchPushNotify({
          ...this.linkForm,
          dataId: null,
          formId: 0,
          isBatch: 0,
          dataIdList: [this.query.dataId],
          dataList: this.linkData
        }).then((res) => {
          this.$message.success('跟进提醒已设置成功')
        })
      }
    },
    handleCommunicatePlanDialogClosed() {
      this.$emit('dynamicsSubmit')
    },
    // 是否存在可以同步完成的访客计划
    async hasCommunicatePlanFinish(formData) {
      // 线索的跟进记录不需要访客计划
      if (this.query.businessType === 8000) return
      const formDataTmp = JSON.parse(JSON.stringify(formData))

      // 取当前所建的跟进记录的拜访时间当天的时间值
      let attr = ''
      this.explainList.forEach((item) => {
        if (item.saasAttr === 'communicateTime') {
          attr = item.attr
        }
      })
      const communicateTime = formDataTmp.dataList[attr]
      const timeObj = xbb.timestampToTime(communicateTime)
      const date = timeObj.year + '-' + timeObj.month + '-' + timeObj.day
      const conditions =
        this.saasObj.conditions &&
        this.saasObj.conditions.map((item) => {
          if (item.attr === attr) {
            this.$set(item, 'value', [
              new Date(date + ' 00:00:00').getTime() / 1000,
              new Date(date + ' 23:59:59').getTime() / 1000
            ])
          }
          return item
        })

      // 访客计划弹框请求需要的参数
      this.finishPlanData = {
        parentBusinessType: this.saasObj.parentBusinessType || 100,
        parentId: this.saasObj.linkParentId,
        conditions: conditions
      }

      // 获取访客计划模版id
      return new Promise((resolve, reject) => {
        this.getTemplateEnableList()
          .then((list) => {
            const params = {
              ...this.finishPlanData,
              businessType: 601,
              conditions: conditions,
              saasMark: 1,
              sourceBusinessType: this.formQuery.businessType,
              formId: list[0].formId
            }
            hasCommunicatePlanFinish(params)
              .then(
                ({
                  result: {
                    saasObj: { finishPlan }
                  }
                }) => {
                  if (finishPlan) {
                    this.isVisible = true
                  }
                  resolve(finishPlan)
                }
              )
              .catch((err) => {
                console.log(err)
                reject()
              })
          })
          .catch(() => {
            reject()
          })
      })
    },
    // 获取访客计划模版列表
    getTemplateEnableList() {
      return new Promise((resolve, reject) => {
        getTemplateEnableList({ businessType: 601 })
          .then(({ result: { formList } }) => {
            resolve(formList)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 访客计划完成保存
    communicatePlanSave() {
      if (!this.selectRows.length) {
        this.$message({
          type: 'warning',
          message: this.$t('formDataDetail.selectVisitorPlan')
        })
        return
      }

      const params = {
        communicateDataId: this.communicatePlanId,
        linkPlanArr: this.selectRows
      }
      finishCommunicatePlan(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg || '操作成功'
          })
          this.selectRows = []
          this.isVisible = false
          this.parentDetail && this.parentDetail.detailRefresh()
        })
        .catch(() => {})
    },
    tagClose(index, item) {
      this.selectRows.splice(index, 1)
    },
    //图片或附件文件发生改变
    fileChange(val) {
      this.fileAndImgVal = { ...val }
    },
    //保存草稿
    @xbb.debounceWrap()
    saveDraft() {
      this.$message.success(this.$t('message.saveSuccess'))
      //备注
      this.$refs.fusionEntryFiles.saveDraft()
      //formData
      utils.LS.set(
        'VUE-follow-record-form-data' + this.query.appId + this.query.dataId,
        { ...this.formData, ...{ setReminder: this.setReminder } },
        720
      )
    },

    handleCancel() {
      this.initialValue = {}
      this.$emit('update:followRecordOpen', false)
    }
  },

  beforeDestroy() {
    //判断是否需要存草稿
    console.log('5555555555', this.initialValue, this.formData)
    if (
      Object.keys(this.initialValue).length !== 0 &&
      !deepEqual(this.initialValue, this.formData)
    ) {
      //备注
      this.$refs.fusionEntryFiles.saveDraft()
      //formData
      utils.LS.set(
        'VUE-follow-record-form-data' + this.query.appId + this.query.dataId,
        { ...this.formData, ...{ setReminder: this.setReminder } },
        720
      )
    }
  }
}
</script>

<style lang="scss">
.communicate-reccord {
  &__imgBox {
    & li.el-upload-list__item,
    & .el-upload--picture-card {
      width: 80px !important;
      height: 80px !important;
    }
    :deep(.el-upload-list__item-actions) {
      font-size: 12px;
      text-align: left;
      .el-upload-list__item-preview {
        position: relative;
        left: 2px;
      }
      .el-upload-list__item-delete {
        position: relative;
        right: 6px;
      }
    }
    :deep(.el-upload-list__item) {
      width: 42px !important;
      height: 42px !important;
      line-height: 42px !important;
    }
  }
}
:deep(.form-link-data) {
  & > .el-form-item__content {
    margin-left: 12px !important;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
}
.communicate-reccord {
  .communicate {
    position: relative;
    padding: 15px;
    background-color: $base-white;
    .communicate-form {
      margin-top: 8px;
    }
    .communicate-enter {
      height: 163px;
      border: 1px solid $neutral-color-3;
      border-radius: 3px;
      .record-content {
        box-sizing: border-box;
        width: 100%;
        height: 126px;
        padding: 8px;
        font-size: 13px;
        line-height: 22px;
        color: $text-main;
        resize: none;
        border: none;
      }
      textarea:focus {
        border: none;
        outline: none !important;
      }
      .menu {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 33px;
        padding-left: 10px;
        background-color: #8ca3cf;
        border-radius: 0px 0px 2px 2px;
        :deep(.el-badge__content) {
          height: 14px;
          padding: 0px 4px;
          font-size: 12px;
          line-height: 14px;
          background-color: $neutral-color-4;
        }
        .opt-item {
          display: inline-block;
          padding-right: 18px;
          font-size: 15px;
          &:first-child {
            padding-top: 1px;
            padding-bottom: 1px;
          }
          &:last-child {
            border-right-width: 0;
          }
          &.required::after {
            position: absolute;
            top: 2px;
            left: 10px;
            color: $error-base-color-6;
            content: '*';
          }
          .text {
            font-size: 15px;
            color: #c4d1eb;
            word-break: break-all;
            cursor: pointer;
          }
        }
      }
    }
    .communicate-enter:focus-within {
      border: 1px solid $brand-color-5;
    }
    .form-data-form {
      overflow: hidden;
    }
    .communicate-options {
      text-align: right;
      .botton_slot {
        top: 14px;
        .set-reminder {
          display: inline-block;

          margin-right: 16px;
          margin-bottom: 5px;
          .el-select {
            width: 120px;
          }
        }
      }
    }
  }
}
.communicate-plan-dialog {
  .dialog-header {
    height: 76px;
    padding: 8px;
    margin-bottom: 16px;
    border: 1px solid $neutral-color-3;
  }
  .dialog-table {
    border: 1px solid $neutral-color-3;
  }
}
</style>
