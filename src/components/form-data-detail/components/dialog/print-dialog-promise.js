/**
 * @description: 详情打印弹窗的promise形式，方便在非详情页调用
 */
import Vue from 'vue'
import PrintDialog from './print-dialog.vue'
import i18n from '@/lang'
import { PopupManager } from 'element-ui/lib/utils/popup'

let instance, currentMsg, vm

function callBack(result) {
  if (!result || !result.success) {
    currentMsg.reject('cancel')
  } else {
    currentMsg.resolve(result)
  }
  document.body.removeChild(vm.$el)
  vm.$destroy()
  instance = null
}

const PrintDialogPromise = ({ formQuery, extraParams = {} }) => {
  let show = true
  const propsData = {
    formQuery,
    extraParams,
    show
  }
  const Constructor = Vue.extend(PrintDialog)
  instance = new Constructor({
    i18n,
    propsData
  })
  instance.callBack = callBack
  instance.id = 'print-dialog-promise'
  vm = instance.$mount()
  vm.$el.style.zIndex = PopupManager.nextZIndex()

  document.body.appendChild(vm.$el)

  return new Promise((resolve, reject) => {
    currentMsg = {
      resolve(e) {
        show = false
        resolve(e)
      },
      reject(e) {
        show = false
        reject(e)
      }
    }
  })
}

export default PrintDialogPromise
