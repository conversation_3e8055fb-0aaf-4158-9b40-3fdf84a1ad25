import bus from '@/utils/temp-vue'

export default {
  name: '报告日期',
  register: (fieldInfo) => {
    return {
      formatValSet: (fieldInstant, value) => {
        let param = fieldInstant.form.formParams
        // 切换报告时间，请求接口
        if (value) {
          const dateVal = new Date(value * 1000)
          const dateMonth =
            dateVal.getMonth() + 1 < 10 ? '0' + (dateVal.getMonth() + 1) : dateVal.getMonth() + 1
          const dateDay = dateVal.getDate() < 10 ? '0' + dateVal.getDate() : dateVal.getDate()
          const dateStr = `${dateVal.getFullYear()}-${dateMonth}-${dateDay}`
          param = Object.assign({}, param, {
            saasSpecialParamPojo: {
              reportStartDate: dateStr
            }
          })
        }
        // 切换的同时将日报切换到新建状态
        bus.$emit('changeWorkOrder', param)
        return value
      }
    }
  }
}
