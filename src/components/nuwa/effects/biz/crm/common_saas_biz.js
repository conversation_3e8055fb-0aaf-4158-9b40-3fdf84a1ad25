// 公用的SaaS业务逻辑
import * as unit from '@/utils/unit.js'
import xbb from '@xbb/xbb-utils'
import { getRelyProductList } from '@/api/formData'
import repeatPromise from '../../../components/base-fields/InputText/repeat_promise'
import { onFieldValidateEnd } from '@nuwa/core'

/**
 * 根据关联单据查询目标产品
 */
export const warpGetRelyProductList = async (params, form) => {
  if (!params || !form) return new Error("function warpGetRelyProductList's params illegal")
  const { appId, menuId, formId, businessType, saasMark } = form.formParams

  return new Promise((resolve, reject) => {
    getRelyProductList({
      appId,
      menuId,
      formId,
      businessType,
      saasMark,
      ...params
    })
      .then(({ result: { productArray = {} } }) => {
        resolve(productArray)
      })
      .catch((e) => reject(e))
  })
}
/**
 * 当开启【价目表】功能时，需先选择客户/经销商，才能选则产品
 */
export function priceEffects(form, context, { productAttr, saasParticularAttributePoJo = {} }) {
  const linkProductField = form.getFieldInstant(productAttr)
  // 判断价目表是否开启
  if (!linkProductField?.fieldInfo?.saasParticularAttributePoJo?.isOpenPrice) return

  const val = context.value
  const oldVal = context.oldValue
  if (val && val.length) {
    linkProductField.changeState('pattern', 'editable')
    if (oldVal && JSON.stringify(val) !== JSON.stringify(oldVal)) {
      linkProductField.setValue([])
    }
  } else {
    linkProductField.setValue([])
    linkProductField.changeState('pattern', 'disable')
  }
}

/** 监听多单位处理价格
 * 合同需处理，单价、售价
 * 关联机会、关联报价单带的产品后端模拟的源单数据，数据里面会有个标识
 * 其余业务拷贝
 */
export const productUnitEffects = (form, context, { parentAttr }) => {
  const { value, row, index, init, oldValue } = context
  if (!value || init || !row || xbb._isEqual(value, oldValue) || !oldValue) return
  const { enableMultiUnit, sellingPriceMap } = row
  if (!enableMultiUnit || !sellingPriceMap) return
  // 售价
  let salePrice
  if (value.value === sellingPriceMap['text_8'].value) {
    salePrice = sellingPriceMap['num_6']
  } else {
    // 不是最初的单位时
    const unitObj = row['price'].find((o) => o.value === value.value) || {}
    if (unitObj['sellingPrice'] >= 0) {
      salePrice = unitObj['sellingPrice']
    } else {
      salePrice = unit.getUnitPrice(value, row['unitRate'], oldValue, row['num_6'])
    }
  }
  // 单价
  // 下面这行代码原先在上面的 因为会重复触发别的字段的 watch 给他放到这里，让他跟 num_6一起改
  // 我也不知道会不会有什么别的影响
  console.log(
    '%c [ salePrice ] 🐱-79',
    'font-size:13px; background:pink; color:#bf2c9f;',
    salePrice
  )
  const setData = {}
  setData[`${parentAttr}.${index}.num_1`] = unit.getUnitPrice(value, row['price'])
  setData[`${parentAttr}.${index}.num_6`] = salePrice
  // form.setValue(`${parentAttr}.${index}.num_1`, unit.getUnitPrice(value, row['price']))
  // form.setValue(`${parentAttr}.${index}.num_6`, salePrice)

  // 折扣比例
  // if (!row['num_4']) {
  // form.setValue(`${parentAttr}.${index}.num_4`, 100)
  setData[`${parentAttr}.${index}.num_4`] = 100
  // }
  form.setValue(setData)
}

/**
 * 关联产品联动单价和成本
 * 选择产品后联动改变自身这行的
 * 单价，成本，单位
 */
export const productEffects = (form, context, { attr, parentAttr }) => {
  if (context.init) return
  const { value, row, index } = context
  if (!row) return
  // 值为空时，清空关联字段的值
  if (!value) {
    form.setValue({
      [`${parentAttr}.${index}.num_1`]: undefined, // 单价
      [`${parentAttr}.${index}.text_8`]: undefined // 销售单位
    })
    return
  }

  const { sourceData } = value
  if (!sourceData) return
  const {
    enableMultiUnit,
    unitRate,
    transformUnitRate,
    price,
    sellingPriceMap,
    refProductId,
    num_1
  } = sourceData
  console.log(
    '%c [ sourceData ] 🐱-115',
    'font-size:13px; background:pink; color:#bf2c9f;',
    sourceData
  )
  const parentInstant = form.getFieldInstant(parentAttr)
  parentInstant.setBusinessDataForFormData(index, 'enableMultiUnit', enableMultiUnit)
  parentInstant.setBusinessDataForFormData(index, 'unitRate', unitRate)
  parentInstant.setBusinessDataForFormData(index, 'transformUnitRate', transformUnitRate)
  parentInstant.setBusinessDataForFormData(index, 'price', price)
  parentInstant.setBusinessDataForFormData(index, 'sellingPriceMap', sellingPriceMap)
  parentInstant.setBusinessDataForFormData(index, 'refProductId', refProductId)
  console.log(`${parentAttr}.${index}.num_1`)
  // 如果原先这行关联产品产品名称没值 但是这个时候单价有值，证明是从产品的数据联动那边来的，阻断掉下面的逻辑
  if (context.oldValue === undefined && row.num_1 !== undefined) return
  form.setValue(
    {
      [`${parentAttr}.${index}.num_1`]: isNaN(+num_1) ? 0 : +num_1
    },
    ['init']
  )

  // if (!row.text_8) {
  // 不存在就取产品的单位
  form.setValue(`${parentAttr}.${index}.text_8`, sourceData.text_8) // 单位
  // }
  // 折扣不动他 还是 100%
  form.setValue(`${parentAttr}.${index}.num_4`, 100, ['init']) // 折扣

  parentInstant.setBusinessDataForFormData(index, 'productSubId', null)

  parentInstant.setBusinessDataForFormData(index, 'productSubId', null)

  if (sourceData.sellingPrice >= 0) {
    form.setValue(`${parentAttr}.${index}.num_6`, sourceData.sellingPrice) // 售价
  }
}

// 多单位标识以及多单位选项
export const getUnitEditSomething = (form, context, { linkAttr }) => {
  const { value, index } = context
  const { sourceData } = value
  const { enableMultiUnit, unitRate, transformUnitRate, price, sellingPriceMap } = sourceData
  const linkFieldInstant = form.getFieldInstant(linkAttr)
  linkFieldInstant.setBusinessDataForFormData(index, 'enableMultiUnit', enableMultiUnit)
  linkFieldInstant.setBusinessDataForFormData(index, 'unitRate', unitRate)
  linkFieldInstant.setBusinessDataForFormData(index, 'transformUnitRate', transformUnitRate)
  linkFieldInstant.setBusinessDataForFormData(index, 'price', price)
  linkFieldInstant.setBusinessDataForFormData(index, 'sellingPriceMap', sellingPriceMap)
  // form.setValue({
  //   [`${linkAttr}.${index}.enableMultiUnit`]: enableMultiUnit, // 控制多单位是否可编辑
  //   [`${linkAttr}.${index}.unitRate`]: unitRate, // 多单位选项
  //   [`${linkAttr}.${index}.transformUnitRate`]: transformUnitRate, // 辅助单位选项
  //   [`${linkAttr}.${index}.price`]: price, // 多单位价格组
  //   [`${linkAttr}.${index}.sellingPriceMap`]: sellingPriceMap
  // })
}

// 批次序列号标识
export const getbatchShelfLife = (form, context, { linkAttr }) => {
  const { value, index } = context
  const { sourceData } = value
  const { enableSerialNumber, shelfLifeDays, enableBatchShelfLife, num_37 } = sourceData
  const linkFieldInstant = form.getFieldInstant(linkAttr)
  linkFieldInstant.setBusinessDataForFormData(index, 'enableSerialNumber', enableSerialNumber)
  linkFieldInstant.setBusinessDataForFormData(index, 'shelfLifeDays', shelfLifeDays)
  linkFieldInstant.setBusinessDataForFormData(index, 'enableBatchShelfLife', enableBatchShelfLife)
  // form.setValue({
  //   [`${linkAttr}.${index}.enableSerialNumber`]: enableSerialNumber,
  //   [`${linkAttr}.${index}.shelfLifeDays`]: shelfLifeDays,
  //   [`${linkAttr}.${index}.enableBatchShelfLife`]: enableBatchShelfLife
  // })

  // 开启保质期管理生产日期num_8默认当天,保质期num_9默认取产品模板
  if (shelfLifeDays) {
    const fmt = xbb.timestampToTimeString(Date.now(), 'yyyy-MM-dd 00:00:00')
    const date = parseInt(new Date(fmt) / 1000)
    if (!form.getValue(`${linkAttr}.${index}.text_5`)) {
      form.setValue({
        [`${linkAttr}.${index}.num_8`]: date,
        [`${linkAttr}.${index}.num_9`]: num_37
      })
    }
  }
}
// 监听阶段推进器字段，带入阶段比例字段
export const relyStage = (form, context) => {
  const val = context.value

  if (!form.checkSchema('long_3')) return

  if (val) {
    form.setValue('long_3', val.stageRatio)
  } else {
    form.setValue('long_3', undefined)
  }
}
// 关联机会获得formId
export const setOpportunityIdEffects = (form, context) => {
  const val = context.value
  // 这里原逻辑是给“关联产品”字段和“关联产品”下的“产品名称”字段设置sourceFormId
  if (val && val.length) {
    form.formParams.sourceFormId = val[0].formId
  } else {
    form.formParams.sourceFormId = undefined
  }
}

export const getSum = (arr) => {
  let sumVal = 0
  arr.forEach((item) => {
    sumVal += +item
  })
  return sumVal
}

/**
 * @description 关联账期赋值
 * @param {Object} [val] 选中的选项
 * @param {Array} [items] 选项
 * @param {String} [attr] 关联字段的attr
 */
export const setAcountPeriod = (form, val, items, attr) => {
  if (!val || val.value === '') {
    form.setValue(attr, undefined)
    return
  }
  const { value } = val
  let targetItem = items.find((v) => v.value === value)?.receivablePeriod
  if (!targetItem) {
    // 其他选项
    targetItem = items.find((v) => v.value === '' && v.isOther === 1 && value)?.receivablePeriod
  }
  if (targetItem?.type) {
    // 选中的选项，存在账期
    const { type, month, day } = targetItem
    const isHaveShowField = form.schema.find((v) => v.attr === attr).visible === 1 // 字段是否可见，可见字段才有赋值操作
    isHaveShowField && form.setValue(attr, { type, month, day })
  } else {
    form.setValue(attr, undefined)
  }
}

/**
 * 客户、合同、线索、联系人的特殊查重逻辑
 */
const repeatEffect = (field, context) => {
  // 子表单返回的是对象，所以这个处理一下
  const curContext = context?.stateInfo || context
  let noRepeat

  if (Object.prototype.toString.call(curContext) === '[object Array]') {
    // 成功返回的是数组
    noRepeat = curContext.find(
      (item) => item && (item.key === 'NoRepeat' || item.key === 'noDuplicate')
    )
  } else if (curContext?.key === 'NoRepeat' || curContext?.key === 'noDuplicate') {
    // 失败返回的对象
    noRepeat = curContext
  }

  if (!noRepeat) return

  let currentField = field
  const isSub = Boolean(context?.stateInfo)
  if (isSub) {
    // 这里是为了拿到正确的字段实例
    const attrArr = field.refKey.split('.')
    const currentAttr = `${attrArr[0]}.${context.index}.${attrArr[1]}`
    currentField = field.form.getFieldInstant(currentAttr)
  }
  currentField.setErrorMessage({
    ...noRepeat,
    callback: (type) => {
      console.log('🙆‍♂️🙆🙆‍♀️ ~ onFieldValidateSuccess ~ noRepeat:', type)
      repeatPromise(field)
        .then((res) => {
          console.log('🙆‍♂️🙆🙆‍♀️ ~ field.setErrorMessage ~ res:', res)
        })
        .catch((err) => {})
    }
  })
}

export const addRepeatValidate = (form) => {
  form.schema.forEach((explain) => {
    if (explain.noRepeat) {
      form.addEffects(`${explain.attr}_onFieldValidateEnd`, (form) => {
        onFieldValidateEnd(explain.attr, repeatEffect)
      })
    } else if (explain.duplicate) {
      form.addEffects(`${explain.attr}_onFieldDuplicateEnd`, (form) => {
        onFieldValidateEnd(explain.attr, repeatEffect)
      })
    }
    explain?.subForm?.items.forEach((sub) => {
      if (sub.noRepeat) {
        form.addEffects(`${explain.attr}.${sub.attr}_onFieldValidateSubEnd`, (form) => {
          onFieldValidateEnd(`${explain.attr}.${sub.attr}`, repeatEffect)
        })
      } else if (sub.duplicate) {
        form.addEffects(`${explain.attr}.${sub.attr}_onFieldDuplicateSubEnd`, (form) => {
          onFieldValidateEnd(`${explain.attr}.${sub.attr}`, repeatEffect)
        })
      }
    })
  })
}
