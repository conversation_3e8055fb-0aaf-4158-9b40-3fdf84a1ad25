import { onFieldValueChange } from '@nuwa/core'
import {
  specialDefaultInheritBusinessList,
  businessEnum
} from '@/components/all-fields/form-data-edit/all-mixin-new/business-type-map.js'
import { useStore } from '@/composables/useStore'
import { getInheritRule } from '@/api/team-member.js'
import { specialBusinessTypeMap } from '@/constants/common/business-type'
import { defaultOwnerAndCoUser } from '@/api/formData'
// import xbb from '@xbb/xbb-utils'

const store = useStore()
let form = null

const hasFromFormAttr = (curFormMap, fromFormInfoList) => {
  let matchObj
  for (const fromFormInfo of fromFormInfoList) {
    matchObj =
      curFormMap.get(fromFormInfo.formId) ||
      [...curFormMap.values()].find((item) =>
        item.dataId.has(fromFormInfo.dataId || fromFormInfo.id)
      ) ||
      {}
    if (matchObj) return matchObj.attr
  }
}
// 前端设置关联字段里，走可继承逻辑的字段 // todo
const defaultInheritableFieldList = () => {
  const saasAttrEnum = businessEnum[form.formParams.businessType]
  return saasAttrEnum
    .map((saasAttr) => {
      return form.schema.find((fieldInfo) => fieldInfo.saasAttr === saasAttr)
    })
    .filter((field) => field)
}
const getOriginAttr = () => {
  const currentInheritableFormMap =
    store.getters.inheritableFormMap[form.formParams.businessType] || new Map()
  console.log('currentInheritableFormMap', currentInheritableFormMap)

  if (currentInheritableFormMap.size <= 0) {
    if (specialDefaultInheritBusinessList.includes(form.formParams.businessType)) {
      return (defaultInheritableFieldList()[0] || {}).attr
    }
    console.log(11)
  } else if (currentInheritableFormMap.size === 1) {
    // 单继承
    console.log(22)
    return (
      [...currentInheritableFormMap.values()][0] && [...currentInheritableFormMap.values()][0].attr
    )
  } else {
    const multiInheritableAttr = hasFromFormAttr(currentInheritableFormMap, [
      store.getters.currentForm
    ])
    if (multiInheritableAttr) {
      console.log(33, '多继承')
      return multiInheritableAttr
    }
    console.log(44, '默认继承')
    const defaultObj = defaultInheritableFieldList().find(({ attr }) => {
      if (!form.getValue(attr)) return false

      const value = [].concat(form.getValue(attr))
      console.log('444 formData[attr]', value, attr, currentInheritableFormMap)
      return hasFromFormAttr(currentInheritableFormMap, value)
    })
    return (defaultObj || {}).attr
  }
}
// 需要继承的字段解释
const getOriginField = () => {
  const originAttr = getOriginAttr()
  console.log('originAttr', originAttr)
  return defaultInheritableFieldList().find(({ attr }) => {
    return originAttr === attr
  })
}

const linkAddInitCount = () => {
  // linkAddSaasAttrList: 记录表单初始化时，linkValue赋值的字段，用于保证linkValue默认值场景下，多继承的执行必须在所有字段赋值之后
  return (store.getters.linkAddSaasAttrList || []).filter((saasAttr) => {
    const saasBusinessEnum = businessEnum[form.formParams.businessType]
    return saasBusinessEnum && saasBusinessEnum.includes(saasAttr)
  }).length
}
const isAfterLinkValueInit = () => {
  // linkValueChangeCount: 记录关联字段的值变更次数，用于与linkAddSaasAttrList的长度比较来处理初始化多linkValue默认值问题
  if (store.getters.linkValueChangeCount + 1 < linkAddInitCount()) {
    store.dispatch('setLinkValueChangeCount', store.getters.linkValueChangeCount + 1)
    // watch触发的次数若小于初始化时执行linkAdd的次数，不执行后续赋值，确保继承状态的计算正确
    return false
  } else {
    // 当默认初始化完成，直继将所有计数置为0，确保关联新建表单不会受影响
    store.dispatch('resetLinkValueChangeCount')
    store.dispatch('resetLinkAddSaasAttrList')
    return true
  }
}
// 保证人员不重复
const handleDuplicate = (userList) => {
  const tempSet = new Set()
  return userList.filter((owner) => {
    if (tempSet.has(owner.id)) {
      return false
    } else {
      tempSet.add(owner.id)
      return true
    }
  })
}
// 设置负责人协同人的值
const setFormUser = (attr, originUserList) => {
  if (!Array.isArray(originUserList)) return
  const userList = handleDuplicate(originUserList)
  form.setValue(attr, userList)
}
const setOwnerAndCoUserOrigin = ({ coUserList, ownerList }) => {
  if (form.mode === 'flow') return
  let ownerToCoUser = []
  // 负责人
  const ownerField = form.getFieldInstant('ownerId')?.fieldInfo
  if (ownerField && !!ownerField.editable && !form.getValue('ownerId')?.length) {
    // 仅当负责人无值时进行带入
    if (ownerField.multiple === 1) {
      setFormUser('ownerId', ownerList)
    } else {
      setFormUser('ownerId', ownerList?.length ? [ownerList.shift()] : [])
      ownerToCoUser = ownerList || []
    }
  }
  // 协同人
  const coUserField = form.getFieldInstant('coUserId')?.fieldInfo
  if (coUserField && !!coUserField.editable && !form.getValue('coUserId')?.length) {
    // 仅当协同人无值时进行带入
    coUserList = coUserList.concat(ownerToCoUser)
    setFormUser('coUserId', coUserList)
  }
}
/**
 * 设置关联依赖字段信息
 */
const setRelyOwner = ({ coUserList = [], ownerList = [], signUserList = [], defaultAddress }) => {
  const signUsers = signUserList[0]
  const address = defaultAddress
  setOwnerAndCoUserOrigin({ coUserList, ownerList })
  // this.setOwnerAndCoUser()

  // 签订人
  if (signUsers && !form.getValue(signUsers['attr'])?.length) {
    form.setValue(signUsers['attr'], signUsers)
  }
  // 收件人默认值
  if (address?.data) {
    form.setValue(address.data['attr'], [address.data])
  }
}

// const compOriginField = (val, oldVal) => {
//   if (!oldVal || oldVal.attr === (val || {}).attr) return

//   store.dispatch('setLinkOwner', {
//     attr: oldVal.attr,
//     ownerList: [],
//     businessType: form.formParams.businessType
//   })
//   store.dispatch('setLinkCoUser', {
//     attr: oldVal.attr,
//     coUserList: [],
//     businessType: form.formParams.businessType
//   })
// }

export const destroyOwnerRely = (fieldInstant) => {
  if (fieldInstant.state.pattern === 'view' || fieldInstant.state.display !== 'visible') return

  store.dispatch('clearInheritableFormMap', {
    businessType: fieldInstant.form.formParams.businessType
  })
  store.dispatch('resetLinkAddSaasAttrList')
  store.dispatch('resetLinkValueChangeCount')
}

// 对枚举内的业务字段增加监听
export function initOwnerRely(fieldInstant) {
  if (fieldInstant.state.pattern === 'view' || fieldInstant.state.display !== 'visible') return

  const fieldInfo = fieldInstant.fieldInfo
  form = fieldInstant.form
  let originField = null
  // 当前字段对应的表单信息 [ {formId, ...}, ...]
  const getInheritableFormInfo = (value) => {
    if (!value) return

    const fieldValue = (Array.isArray(value) ? value : [value]).filter((val) => val)

    return fieldValue.map((val) => {
      return {
        businessType: fieldInfo.linkedType,
        distributorMark: 0,
        saasMark: 1, // 前端取不到，因为只有saas存在继承，写死
        formId: val.formId,
        dataId: val.id
      }
    })
  }
  // 获取继承信息
  const getInheritRuleMap = (formInfoList) => {
    console.log(fieldInfo.attrName, '请求rule接口', formInfoList)
    if (!Array.isArray(formInfoList)) return Promise.resolve()
    return Promise.all(
      formInfoList.map((formInfo) => {
        return getInheritRule(formInfo).then(({ result }) => {
          console.log(fieldInfo.attrName, '顺利请求', formInfo, result)
          const isIncludesBusinessType = result.linkList.some((businessType) => {
            const businessTypeList = specialBusinessTypeMap[businessType]
            if (businessTypeList) {
              return businessTypeList.includes(form.formParams.businessType)
            } else {
              return businessType === form.formParams.businessType
            }
          })
          console.log(
            '🚀 ~ file: owner-rely.js ~ line 178 ~ isIncludesBusinessType ~ isIncludesBusinessType',
            isIncludesBusinessType
          )
          if (result.inheritFlag && isIncludesBusinessType) {
            console.log(fieldInfo.attrName, '进入add前')
            store.dispatch('addInheritableFormMap', {
              businessType: form.formParams.businessType,
              formId: result.formId,
              dataId: formInfo.dataId,
              attr: fieldInfo.attr
            })
            console.log(fieldInfo.attrName, '设置完')
          }
        })
      })
    )
  }
  // 继承的字段参数
  const originParams = () => {
    if (!originField) return
    const selectDataIdList = (form.getValue(originField.attr) || []).map((item) => item.id)
    return {
      businessType: form.formParams.businessType,
      selectDataIdList: selectDataIdList,
      distributorMark: originField.distributorMark || 0,
      selectBusinessType: originField.linkedType
    }
  }
  // 获取业务的负责团队
  const dealDefaultOwnerAndCoUser = () => {
    defaultOwnerAndCoUser(originParams())
      .then((data) => {
        const { ownerList = [], coUserList = [] } = data.result
        console.log('从接口拿值', 'ownerList', ownerList)
        console.log('从接口拿值', 'coUserList', coUserList)

        store.dispatch('setLinkOwner', {
          attr: originField.attr,
          ownerList,
          businessType: form.formParams.businessType
        })
        store.dispatch('setLinkCoUser', {
          attr: originField.attr,
          coUserList,
          businessType: form.formParams.businessType
        })
        setRelyOwner(data.result)
      })
      .catch((err) => {
        console.log('eee', err)
      })
  }

  const { businessType, mode } = form.formParams
  const { saasAttr, attr } = fieldInfo
  fieldInstant.form.addEffects(`${saasAttr}_ownerRely`, () => {
    onFieldValueChange(`${fieldInstant.refKey}`, (field, context) => {
      const { value, oldValue, init } = context
      if (init && mode === 'edit') {
        return
      }
      console.log('进入watch', fieldInfo.attrName, value, oldValue)
      if (
        value?.length === oldValue?.length &&
        value?.length > 0 &&
        value[0]?.id === oldValue[0]?.id
      ) {
        console.log('被栏', value, oldValue)
        return
      }
      // 拿到前后数据的差集
      const divValueList = [].concat(oldValue).filter((oldValue) => {
        return (
          oldValue && [].concat(value).every((newValue) => !newValue || newValue.id !== oldValue.id)
        )
      })
      console.log('divValueList', fieldInfo.attrName, divValueList)
      divValueList.forEach((divValue) => {
        console.log('执行删除', fieldInfo.attrName)
        store.dispatch('delInheritableFormMap', {
          businessType,
          formId: divValue.formId,
          dataId: divValue.id,
          attr
        })
      })

      if (value?.length > 0) {
        const inheritableFormInfo = getInheritableFormInfo(value)
        console.log('获取继承表前inheritableFormInfo', inheritableFormInfo)

        getInheritRuleMap(inheritableFormInfo)
          .then(() => {
            // const oldOriginField = xbb.deepClone(originField)
            originField = getOriginField()
            // compOriginField(originField, oldOriginField)
            console.log(
              '获取继承表后originParams',
              (originField || {}).attrName,
              fieldInfo.attrName
            )

            if (
              !isAfterLinkValueInit() || // 必须在前，确保不会被originParams没有值过滤
              !originParams()
            ) {
              return
            }
            // 设置负责人、协同人
            dealDefaultOwnerAndCoUser()
          })
          .catch((err) => {
            console.log('eeeeee', err)
          })
      }
    })
  })
}

// nvwa在挂继承逻辑时，是针对字段的attr去增加和清除继承缓存的
// 但是有一段继承相关的缓存逻辑，来自于getLinkAddVal，这段公共逻辑的缓存是不跟attr走的 —— 会导致出现额外的缓存没有被销毁
// 这里补一个销毁逻辑，确保不会有多余的缓存相互影响
export const destroyOwnerRelyForForm = (businessType) => {
  store.dispatch('clearInheritableFormMap', {
    businessType
  })
  store.dispatch('resetLinkAddSaasAttrList')
  store.dispatch('resetLinkValueChangeCount')
}
