// 生产入库

import { onFieldValueChange } from '@nuwa/core'

export default {
  effects(form) {
    // 入库仓库
    onFieldValueChange('text_2', (field, context) => {
      const { value, init } = context
      if (init) return

      // 生产入库单（成品入库）切换仓库清空单据
      const refField = form.getValue('text_5') // 此时单据的数据
      const refValue = refField?.[0]?.text_5 // 此时单据中关联的入库仓库的数据
      // 如果此时仓库的值 等于单据中 对应的仓库值，说明此时就是悖论
      const bool = refValue?.[0]?.id === value?.[0]?.id
      if (!bool && refField) {
        form.setValue('text_5', [])
      }
    })
  }
}
