// 806 红冲付款单
import MoneyDetailTable from '../common-fields/MoneyDetailTable.js'
import { onFieldInit, onFieldUnMounted } from '@nuwa/core'
import { initOwnerRely, destroyOwnerRely } from '../crm/common-onwer-rely'

export default {
  effects(form) {
    // 关联供应商
    onFieldInit('text_4', (field) => {
      initOwnerRely(field)
    })
    onFieldUnMounted('text_4', (field) => {
      destroyOwnerRely(field)
    })
    // 关联采购合同
    onFieldInit('text_5', (field) => {
      initOwnerRely(field)
    })
    onFieldUnMounted('text_5', (field) => {
      destroyOwnerRely(field)
    })
    // 关联付款计划
    onFieldInit('text_6', (field) => {
      initOwnerRely(field)
    })
    onFieldUnMounted('text_6', (field) => {
      destroyOwnerRely(field)
    })
  },
  validator: {},
  customerField: {
    num_1: MoneyDetailTable
  }
}
