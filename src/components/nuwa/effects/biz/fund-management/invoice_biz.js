import { useStore } from '@/composables/useStore'
import { doInitInvoice } from './fund-common.js'

const store = useStore()

export const backPlanChange = (form, context) => {
  const { value, oldValue } = context
  doInitInvoice(value, oldValue)
  if ((!oldValue || oldValue.length === 0) && (!value || value.length === 0)) {
    return false
  }
  const contractData = form.getValue('text_4')
  // 清空回款单\回款计划时,如果合同有数据
  if (contractData && value && value.length === 0) {
    // 并没有触发计算合同 需要手动改变数据触发watch
    form.setValue('text_4', JSON.parse(JSON.stringify(contractData)))
  }
}
// 切换回款类型，将应收款和回款单字段的值清空
export const payTypeChange = (form, context) => {
  const { value, oldValue } = context

  if (value && oldValue && value.value !== oldValue.value) {
    form.setValue('text_5', undefined)
    form.setValue('text_62', undefined)
    // 这个typechange好像没用
    // store.commit('SET_TYPE_CHANGE', true)
    store.commit('SET_DETAIL_CHANGE', true) // 触发请求明细状态
  }
}
