// 711 红冲预收款

import { onFieldValueChange, onFieldInit, onFieldUnMounted } from '@nuwa/core'
import { initOwnerRely, destroyOwnerRely } from '../crm/common-onwer-rely'
import { paymentBalance } from './fund-common.js'
import MoneyTips from '../common-fields/MoneyTips.js'

export default {
  effects(form) {
    // 关联客户
    onFieldValueChange('text_6', (field, context) => {
      paymentBalance(field, context.value)
    })
    onFieldInit('text_6', (field) => {
      initOwnerRely(field)
    })
    onFieldUnMounted('text_6', (field) => {
      destroyOwnerRely(field)
    })
  },
  validator: {},
  customerField: {
    num_1: MoneyTips
  }
}
