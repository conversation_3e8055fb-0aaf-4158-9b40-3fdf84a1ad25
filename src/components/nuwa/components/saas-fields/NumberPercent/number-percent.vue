<template>
  <input-number v-bind="prop">
    <div slot="append" class="num-type_block">%</div>
  </input-number>
</template>

<script setup>
import { useCommonCtxOpts, useCommonCtx } from '@/components/nuwa/composables/useCommonCtx'
import InputNumber from '../../base-fields/InputNumber'

defineOptions({
  name: 'NumberPercent'
})

const props = defineProps({ ...useCommonCtxOpts })
const prop = useCommonCtx(props)
</script>

<style lang="scss" scoped>
.num-type_block {
  position: relative;
  box-sizing: border-box;
  flex: 1;
  flex-shrink: 0;
  width: 75px;
  height: 32px;
  padding-right: 8px;
  // 数字输入框的类型块内边距要 >= 8px
  padding-left: 8px;
  color: $text-auxiliary;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  background-color: $neutral-color-1;
  border: 1px solid $neutral-color-3;
  border-left: 0;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
