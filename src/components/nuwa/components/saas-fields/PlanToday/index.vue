<template>
  <!-- <div class="form-item" v-if="!!visible && fieldInfo.fieldMapHidden !==1"> -->
  <FormItemLayout
    v-if="!!visible"
    :field-info="fieldInfo"
    :field-instant="fieldInstant"
    :field-position="fieldPosition"
    :is-see="isSee"
    :mode="mode"
    :show-label="showLabel"
  >
    <template #label>
      <slot name="label"></slot>
    </template>
    <template #error>
      <slot name="error"></slot>
    </template>
    <div class="field-item">
      <!-- 基础组件 -->
      <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
        {{ fieldInfo.unableEditMemo }}
      </div>
      <template v-else>
        <slot :field-instant="fieldInstant">
          <template v-if="planList.length === 0">
            {{ $t('unit.no') }}
          </template>
          <template v-else>
            <div v-for="(item, index) of planList" :key="item.num_1" class="plan__item">
              <el-checkbox
                v-model="item.isChecked"
                :disabled="disabled || isSee"
                @change="switchChange"
                ><span class="plan__text_status">{{ showText(item.text_2) }}</span></el-checkbox
              >
              {{ index + 1 }}.<span class="plan__text_content">{{ item.text_1 }}</span>
            </div>
          </template>
        </slot>
      </template>
      <slot name="suffix"></slot>
    </div>
  </FormItemLayout>
</template>

<script setup>
import { onMounted, watch, ref } from 'vue'
import { useCommonCtxOpts, useCommonCtx } from '@/components/nuwa/composables/useCommonCtx'
import FormItemLayout from '../../layout/form-item-layout.vue'
import i18n from '@/lang/index'
import { onFieldValueChange } from '@nuwa/core'

defineOptions({
  name: 'PlanToday'
})
const props = defineProps({
  ...useCommonCtxOpts
})

const {
  showLabel,
  fieldPosition,
  mode,
  fieldInfo,
  isSee,
  fieldInstant,
  currentValue,
  visible,
  disabled
} = useCommonCtx(props)

const planList = ref([])
// switch改变时触发
function switchChange() {
  currentValue.value = planList.value.map((item) => {
    return {
      ...item,
      text_2: item.isChecked ? 1 : 0
    }
  })
}
// 设置默认值
function setDefaultValue() {
  const saasParticularAttributePoJo = fieldInfo.saasParticularAttributePoJo
  if (saasParticularAttributePoJo?.initialValue) {
    const list = saasParticularAttributePoJo.initialValue
    if (list.length) {
      currentValue.value = JSON.parse(JSON.stringify(list))
      planList.value = list.map((item) => {
        return {
          ...item,
          isChecked: Boolean(item.text_2)
        }
      })
    } else {
      planList.value = []
    }
  } else {
    planList.value = []
  }
}
function showText(status) {
  return status ? i18n.t('operation.finish') : i18n.t('nouns.unfinish')
}
function initPlanList() {
  if (currentValue.value?.length) {
    planList.value = currentValue.value.map((item) => {
      return {
        ...item,
        isChecked: Boolean(item.text_2)
      }
    })
  } else {
    setDefaultValue()
  }
}
watch(currentValue, () => {
  initPlanList()
})
onMounted(() => {
  initPlanList()
  fieldInstant.form.addEffects(`${fieldInstant.refKey}initPlanList`, () => {
    onFieldValueChange('date_1', (field, context) => {
      initPlanList()
    })
  })
})
</script>

<style lang="scss">
.plan {
  &__item {
    display: flex;
  }
  &__text {
    margin-right: 10px;
    &_content {
      flex: 1;
      margin-left: 10px;
      //@include singleline-ellipsis;
    }
    &_status {
      margin-right: 10px;
    }
  }
}
</style>
