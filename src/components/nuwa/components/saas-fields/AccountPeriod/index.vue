<template>
  <FormItemLayout
    v-if="!!visible"
    :field-info="fieldInfo"
    :field-instant="fieldInstant"
    :field-position="fieldPosition"
    :is-see="isSee"
    :mode="mode"
    :show-label="showLabel"
  >
    <template #label>
      <slot name="label"></slot>
    </template>
    <template #error>
      <slot name="error"></slot>
    </template>
    <div class="field-item">
      <!-- 基础组件 -->
      <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
        {{ fieldInfo.unableEditMemo }}
      </div>
      <template v-else-if="isSee">
        <slot name="view">
          {{ formateValue(currentValue) }}
        </slot>
      </template>
      <template v-else>
        <slot :field-instant="fieldInstant">
          <el-row>
            <el-col class="account-item">
              <el-select v-model="type" clearable :disabled="disabled" @change="changeType">
                <el-option v-for="t in typeItem" :key="t.key" :label="t.value" :value="t.key">
                </el-option>
              </el-select>
            </el-col>

            <el-col v-if="type === 3" class="account-item">
              <el-select v-model="month" :disabled="disabled" @change="changeMonth">
                <el-option v-for="m in monthList" :key="m.key" :label="m.value" :value="m.key">
                </el-option>
              </el-select>
            </el-col>

            <el-col class="account-item">
              <el-select
                v-if="type !== 4"
                v-model="day"
                :disabled="disabled || isSetType"
                @change="changeData"
              >
                <el-option v-for="d in daysList" :key="d.key" :label="d.value" :value="d.key">
                </el-option>
              </el-select>
              <template v-else-if="type === 4">
                <el-input-number
                  v-model="day"
                  class="input-number"
                  :controls="false"
                  :disabled="disabled"
                  :max="360"
                  :min="1"
                  @change="changeData"
                ></el-input-number>
                <div class="unit">天</div>
              </template>
            </el-col>
          </el-row>
        </slot>
      </template>
      <slot name="suffix"></slot>
    </div>
  </FormItemLayout>
</template>

<script setup>
import { computed, onMounted, ref, watch } from 'vue'
import { useCommonCtxOpts, useCommonCtx } from '@/components/nuwa/composables/useCommonCtx'
import FormItemLayout from '../../layout/form-item-layout.vue'
import i18n from '@/lang/index'

defineOptions({
  name: 'AccountPeriod'
})

const props = defineProps({
  ...useCommonCtxOpts
})

const {
  showLabel,
  mode,
  fieldInfo,
  isSee,
  fieldInstant,
  currentValue,
  visible,
  disabled,
  fieldPosition
} = useCommonCtx(props)

const type = ref('')
const month = ref('')
const day = ref('')
const typeItem = ref([
  { key: 1, value: i18n.t('fundSet.everyWeek') },
  { key: 2, value: i18n.t('fundSet.everyMonth') },
  { key: 3, value: i18n.t('fundSet.everyYear') },
  { key: 4, value: i18n.t('fundSet.createReceivable') }
])
const monthList = ref([])
const week = ref([])
const daysList = computed(() => {
  return type.value === 1
    ? week.value
    : type.value === 3
    ? getMonthDay(1) // 选择每年模式
    : getMonthDay(2) // 选择每月模式
})
const isSetType = computed(() => {
  return !type.value
})
watch(currentValue, (val) => {
  setValue(val) // 数据视图更新
})
/**
 * @description: 格式化查看模式下需要回显的数据
 * @param {JSONstring || string} [val] 需要格式化的值
 */
function formateValue(val) {
  function getValue(array = [], key) {
    const res = array.find((item) => item.key === key)
    return res?.value
  }
  if (mode.value === 'flow') {
    if (val) {
      let str = getValue(typeItem.value, val.type)
      switch (val.type) {
        case 1:
          str += getValue(week.value, val.day)
          break
        case 2:
          str += getValue(daysList.value, val.day)
          break
        case 3:
          str += getValue(monthList.value, val.month) + getValue(daysList.value, val.day)
          break
        case 4:
          str += val.day + i18n.t('unit.day')
          break
        default:
      }
      return str
    } else {
      return val
    }
  } else {
    return val
  }
}
onMounted(() => {
  setValue(currentValue.value) // 渲染初始化
  const weekArr = [
    i18n.t('fundSet.monday'),
    i18n.t('fundSet.tuesday'),
    i18n.t('fundSet.wednesday'),
    i18n.t('fundSet.thursday'),
    i18n.t('fundSet.friday'),
    i18n.t('fundSet.saturday'),
    i18n.t('fundSet.sunday')
  ]
  for (let index = 1; index <= 12; index++) {
    if (index <= 7) {
      week.value.push({
        key: index,
        value: weekArr[index - 1]
      })
    }
    monthList.value.push({
      key: index,
      value: index + '月'
    })
  }
})

/**
 * @description: 根据大小月获取对应月数的天数
 * @param {Number} [mode] 1需要区分大小月、2无需区分大小月
 */
function getMonthDay(mode) {
  const currentYear = new Date().getFullYear()
  const allDay = mode === 1 ? new Date(currentYear, month.value, 0).getDate() : 31
  const days = []
  for (let index = 1; index <= allDay; index++) {
    days.push({
      key: index,
      value: index + '号'
    })
  }
  return days
}
/**
 * @description: 改变类型：每年、每月、每周、自定义
 * @param {Number} [value] 选中类型
 */
function changeType(value) {
  if (value) {
    day.value = 1
    month.value = 1
    currentValue.value = getData()
  } else {
    day.value = ''
    month.value = ''
    currentValue.value = undefined
  }
}
/**
 * @description: 每年模式下，改变月份时，选中日期错位复位
 * @param {Number} [value] 选中日期
 */
function changeMonth(value) {
  const currentYear = new Date().getFullYear()
  const currentDays = new Date(currentYear, value, 0).getDate()
  if (day.value === 31 || (day.value === 30 && value === 2)) {
    day.value = currentDays
  }
  changeData(day.value)
}
/**
 * @description: 选中日期处理
 * @param {Number} [value] 选中日期
 */
function changeData(value) {
  const reg = /^[0-9]+$/
  if (!reg.test(value)) {
    day.value = parseInt(value)
  }
  currentValue.value = getData()
}
/**
 * @description: 应收账期数据处理
 * @return {Object}
 */
function getData() {
  return {
    type: type.value,
    month: month.value,
    day: day.value
  }
}
/**
 * @description 数据视图更新
 * @param {Object} [val] 组件数据集合
 */
function setValue(val) {
  if (!isSee.value && typeof val !== 'object' && val) {
    // 这里是兼容在审批可编辑时后端传的json字符串不是一个对象
    currentValue.value = JSON.parse(val)
  }
  if (val) {
    type.value = currentValue.value.type
    month.value = currentValue.value.month
    day.value = currentValue.value.day
  } else {
    type.value = ''
    month.value = ''
    day.value = ''
  }
}
</script>

<style lang="scss" scoped>
.account-item {
  display: flex;
  align-items: center;
  max-width: 115px;
  margin-right: 7px;
  .input-number {
    text-align: left;
  }
  .unit {
    margin-left: 2px;
  }
}
</style>
