<template>
  <FormItemLayout
    v-if="!!visible"
    :field-info="fieldInfo"
    :field-instant="fieldInstant"
    :field-position="fieldPosition"
    :is-see="isSee"
    :mode="mode"
    :show-label="showLabel"
  >
    <template #label>
      <slot name="label"></slot>
    </template>
    <template #error>
      <slot name="error"></slot>
    </template>
    <div class="field-item">
      <!-- 基础组件 -->
      <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
        {{ fieldInfo.unableEditMemo }}
      </div>
      <template v-else-if="isSee">
        <slot name="view">
          <div class="field-see__left field-see__left--detail">
            {{ seeValue }}
          </div>
        </slot>
      </template>
      <template v-else>
        <slot :field-instant="fieldInstant">
          <el-switch
            v-model="modelVal"
            active-color="#FF8C2E"
            :active-value="1"
            :disabled="disabled"
            inactive-color="#cccccc"
            :inactive-value="0"
            @click.native="switchClick"
          >
          </el-switch>
        </slot>
      </template>
      <slot name="suffix"></slot>
    </div>
  </FormItemLayout>
</template>

<script setup>
import { computed } from 'vue'
import { useCommonCtxOpts, useCommonCtx } from '@/components/nuwa/composables/useCommonCtx'
import FormItemLayout from '../../layout/form-item-layout.vue'
import i18n from '@/lang'

defineOptions({
  name: 'SwitchField'
})
const props = defineProps({ ...useCommonCtxOpts })

const {
  showLabel,
  mode,
  fieldInfo,
  isSee,
  fieldInstant,
  currentValue,
  visible,
  disabled,
  fieldPosition
} = useCommonCtx(props)

const seeValue = computed(() => {
  return currentValue.value ? i18n.t('nouns.yes') : i18n.t('nouns.no')
})
const modelVal = computed({
  get() {
    return currentValue.value ? currentValue.value : 0
  },
  set(val) {
    currentValue.value = val
  }
})
// 按钮点击
function switchClick() {
  if (disabled.value) {
    return
  }
}
</script>

<style lang="scss" scoped>
.enable-batch-popover {
  color: $text-tip;
}
</style>
