<template>
  <InputNumber
    :field-info="fieldInfo"
    :field-instant="fieldInstant"
    :field-position="fieldPosition"
    :show-label="showLabel"
  >
    <slot v-if="showDefault"></slot>
    <div v-if="formData.transformUnitRate?.length" slot="nextLevel">
      <el-tooltip :content="getformDataUnitStr(formData, fieldInfo)">
        <p class="unit_text_css">{{ getformDataUnitStr(formData, fieldInfo) }}</p>
      </el-tooltip>
    </div>
  </InputNumber>
</template>

<script setup>
import InputNumber from '../../base-fields/InputNumber/index.vue'
import { useCommonCtxOpts, useCommonCtx } from '@/components/nuwa/composables/useCommonCtx'
import { computed } from 'vue'
import { getUnitNumStr } from '@/utils/unit.js'

defineOptions({
  name: 'MulUnitNum'
})
const props = defineProps({
  ...useCommonCtxOpts,
  showDefault: {
    type: Boolean,
    default: false
  }
})

const { showLabel, fieldInfo, fieldInstant, fieldPosition } = useCommonCtx(props)

const formData = computed(() => {
  return fieldInstant.getSameLevelData()
})
// 辅助单位展示
function getformDataUnitStr(formData, fieldInfo) {
  const items = formData['transformUnitRate'] || []
  let str = ''
  const num = formData['num_3']
  // 选择的业务单位
  const curUnit = formData['text_8']
  // 值为0不处理
  if (typeof num === 'number' && num !== 0) {
    str = getUnitNumStr(curUnit, items, num)
  }
  return str
}
</script>

<style lang="scss" scoped>
.unit_text_css {
  display: block;
  overflow: hidden;
  font-size: 12px;
  line-height: 18px;
  color: $text-auxiliary;
  text-align: right;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
