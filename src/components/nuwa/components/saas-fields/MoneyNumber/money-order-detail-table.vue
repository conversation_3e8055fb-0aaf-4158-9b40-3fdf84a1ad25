<!--
 * @Description: 回款单详情金额明细
 -->
<template>
  <div v-if="tableData && tableData.length > 1" class="money-order-detail-table">
    <!-- 702 回款单 合同字段key text_4 计划字段key text_5-->
    <el-table v-if="businessType === 702" border :data="tableData" max-height="560">
      <el-table-column
        :label="
          $t('business.related', {
            attr: $t('business.contract')
          })
        "
        prop="text_4"
      >
        <template slot-scope="scope">
          <span>{{ formateName(scope.row.text_4) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('formDataEdit.relatedCollectionPlan')" prop="text_5">
        <template slot-scope="scope">
          <span>{{ formateName(scope.row.text_5) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" header-align="left" :label="moneyLabel">
        <template slot-scope="scope">
          {{ scope.row.num_1 || scope.row.num_7 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('nouns.belonger')" prop="array_1">
        <template slot-scope="scope">
          <span>{{ formateName(scope.row.array_1) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 802 付款单 合同字段key text_5 计划字段key text_6-->
    <el-table v-else-if="businessType === 802" :data="tableData" max-height="560">
      <el-table-column
        :label="$t('business.related', { attr: $t('business.procurementContract') })"
        prop="text_5"
      >
        <template slot-scope="scope">
          <span>{{ formateName(scope.row.text_5) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('formDataEdit.relatedPaymentPlan')" prop="text_6">
        <template slot-scope="scope">
          <span>{{ formateName(scope.row.text_6) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="right" header-align="left" :label="moneyLabel">
        <template slot-scope="scope">
          {{ scope.row.num_1 || scope.row.num_2 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('nouns.belonger')" prop="array_1">
        <template slot-scope="scope">
          <span>{{ formateName(scope.row.array_1) }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'MoneyOrderDetailTable',
  props: {
    form: {
      type: Object,
      required: true
    },
    fieldInfo: {
      type: Object,
      required: true
    }
  },

  computed: {
    moneyLabel() {
      return this.fieldInfo.attrName
    },
    moneyProp() {
      return this.fieldInfo.attr
    },
    tableData() {
      return this.form.formParams.amountDetail
    },
    businessType() {
      return this.form.formParams.businessType
    }
  },

  methods: {
    formateName(data) {
      if (data && Array.isArray(data) && data.length > 0) {
        try {
          return data[0].name
        } catch (err) {
          return '--'
        }
      } else {
        return '--'
      }
    }
  }
}
</script>

<style lang="scss">
.money-order-detail-table {
  .el-table::before {
    height: 0px;
  }
}
</style>
