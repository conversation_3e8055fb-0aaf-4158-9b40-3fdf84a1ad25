<template>
  <FormItemLayout
    v-if="!!visible"
    :field-info="fieldInfo"
    :field-instant="fieldInstant"
    :field-position="fieldPosition"
    :is-see="isSee"
    :mode="mode"
    :show-label="showLabel"
  >
    <div class="subform-container" :style="fullScreenStyles">
      <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
        {{ fieldInfo.unableEditMemo }}
      </div>
      <template v-else-if="isSee">
        <SubFormSee
          :field-info="fieldInfo"
          :form-params="form.formParams"
          :head-list="subFields"
          :query="{
            dataId: undefined
          }"
          :rely-field="fieldInstant.fieldInfo"
          :sub-form-data="currentValue"
        >
        </SubFormSee>
      </template>

      <template v-else>
        <slot name="subFormHead"></slot>
        <!-- 子表单内容 -->
        <div
          ref="tableInstant"
          class="subform-content"
          :style="{
            maxHeight: `${maxHeight}`
          }"
        >
          <div class="subform-content__left">
            <div class="left-cell">
              <div class="index-header subform-header">
                <el-checkbox
                  v-model="isAllSelect"
                  class="subform-header-selectall"
                  :disabled="isFromDesign || currentValueLength === 0 || !editable"
                  @change="handlerSelectPageData"
                ></el-checkbox>
                <i
                  v-if="!isFromDesign"
                  class="subform-table-fullscreen"
                  :class="
                    isFullScreen
                      ? 'web-iconfont web-icon-tuichuquanping'
                      : 'web-iconfont web-icon-quanping1'
                  "
                  @click="fullScreen"
                >
                </i>
              </div>
              <template v-for="(item, index) in filterTableData">
                <div
                  v-if="visualScrollShow(index)"
                  :key="index"
                  class="subform-item"
                  :class="{
                    'index-cell': !isSee && !single,
                    'subform-item_nunitText': item.transformUnitRate,
                    'highlight-row': isHoverRowEqual(index)
                  }"
                  @mouseenter="!isFromDesign && handlerMouseEnterDebounce($event, index)"
                  @mouseleave="!isFromDesign && handlerMouseEnterDebounce($event, index)"
                >
                  <el-checkbox
                    v-if="isHoverRowEqual(index) || selectArr.includes(item.subId)"
                    class="index-cell--checkbox-normal"
                    :class="{
                      'index-cell--checkbox-single-box':
                        selectArr.includes(item.subId) &&
                        !isHoverRowEqual(index) &&
                        !getGiftShow(index)
                    }"
                    :value="selectArr.includes(item.subId)"
                    @change="changeSingleCheckbox(item, index)"
                  ></el-checkbox>
                  <div
                    v-if="!selectArr.includes(item.subId) && !isHoverRowEqual(index)"
                    class="index-cell--index"
                  >
                    {{ getRealIndex(index + 1) }}
                  </div>
                  <div
                    v-if="getGiftShow(index) && !isHoverRowEqual(index)"
                    class="product-Gift__block"
                  >
                    {{ $t('formDataEdit.gift') }}
                  </div>
                  <div
                    v-if="isHoverRowEqual(index)"
                    class="index-cell--fullscreen"
                    @click="subFormFullScreen(index)"
                  >
                    <i class="web-icon-arrawsalt web-iconfont"></i>
                  </div>
                </div>
              </template>
              <div
                v-if="subSummary"
                class="subform-item summary"
                style="height: 24px; background: #fbfbfb"
              >
                <div class="index-cell--index" style="height: 24px; line-height: 24px">
                  {{ $t('form.summary') }}
                </div>
              </div>
            </div>
          </div>
          <ul
            class="subform-content__right"
            :class="{
              'subform-content__right--undesign': !isFromDesign,
              'subform-content__right--design': isFromDesign,
              'subform-content--see': isSee
            }"
          >
            <!-- 渲染字段列 -->
            <!-- 有字段时 -->
            <template v-for="(subField, index) in subFields">
              <li
                v-show="showList(subField)"
                :key="subField.attr + '_' + index"
                class="subform-cell"
                :style="{
                  width: !/^(10007|10008|10032)$/.test(subField.fieldType)
                    ? (subField.theWidth || 220) + 'px'
                    : 'auto',
                  ...liFreezeLeft(subField, index)
                }"
              >
                <!-- 头部 -->
                <div class="subfield-name__block subform-header">
                  <div class="title__block" :style="{ width: (subField.theWidth || 220) + 'px' }">
                    <span class="required-icon" style="color: #f7716c">{{
                      subField.required ? '*' : ''
                    }}</span>
                    <span class="field-name" :title="subField.attrName">
                      {{ subField.attrName }}
                      <!-- 子表单字段提示文字 -->
                      <el-popover
                        v-if="subField.toolTip"
                        :content="subField.toolTip"
                        placement="top"
                        trigger="hover"
                      >
                        <i slot="reference" class="el-icon-question"></i>
                      </el-popover>
                    </span>
                    <el-button
                      v-if="isShowBatchBtn(subField.attr)"
                      class="batch-edit"
                      :disabled="isDisabledBatchBtn(subField.attr)"
                      type="text"
                      @click="batchEdit(subField.attr)"
                      >{{ $t('form.batch') }}</el-button
                    >
                  </div>
                  <!-- linkedShowAttr有值，则要展示 -->
                  <slot name="linkFieldLabel" :sub-field="subField"></slot>
                </div>
                <!-- 渲染数据行 -->
                <template v-for="(item, valIndex) in filterTableData">
                  <div
                    v-if="visualScrollShow(valIndex)"
                    :key="item['subId']"
                    class="field-component__block subform-item"
                    :class="{
                      'subform-item_nunitText': item.transformUnitRate,
                      'highlight-row': isHoverRowEqual(valIndex)
                    }"
                    :style="subFormItemStyle"
                    @mouseenter="!isFromDesign && handlerMouseEnterDebounce($event, valIndex)"
                    @mouseleave="!isFromDesign && handlerMouseLeaveDebounce($event, valIndex)"
                  >
                    <component
                      :is="componentsName(subField, valIndex)"
                      :key="
                        subField.attr + item.subId + getFieldInstant(subField.attr, valIndex).refKey
                      "
                      :ref="subField.attr"
                      class="current__component"
                      :distributor-id="distributorId"
                      :field-info="subField"
                      :field-instant="getFieldInstant(subField.attr, valIndex)"
                      :form="form"
                      :show-label="false"
                      :style="minusWidth(subField)"
                      @multipleAdd="multipleAdd"
                    ></component>
                    <!-- linkedShowAttr有值，则要展示 -->
                    <slot
                      :field-instant="getFieldInstant(subField.attr, valIndex)"
                      name="linkFieldComponent"
                      :sub-field="subField"
                    ></slot>
                  </div>
                </template>
                <!-- 数据汇总行 -->
                <div
                  v-if="subSummary"
                  class="field-component__block subform-item summary"
                  style="height: 24px; background: #fbfbfb"
                  :style="subFormItemStyle"
                >
                  <div
                    v-if="subField.summaryFlag"
                    :id="`summary-data-${fieldInstant.fieldInfo.attr}-${subField.attr}`"
                    class="summary-data"
                    style="height: 24px; line-height: 24px"
                  >
                    <span>{{ summaryData(subField) }}</span>
                  </div>
                </div>
              </li>
            </template>
            <!-- 无字段时 -->
            <template v-if="subFields.length === 0">
              <li ref="noFeidld" class="no-field__block subform-cell">
                <div class="no-field__block--text">
                  {{ $t('formDataEdit.pleaseAddField') }}
                </div>
              </li>
            </template>
            <div v-if="subFields.length > 0 && isFromDesign" class="padding-cell"></div>
          </ul>
          <!-- 操作栏 -->
          <div v-if="!isFromDesign" id="operate-subForm" class="subform-cell">
            <div class="subfield-name__block subform-header">
              <p style="width: 100%; text-align: center">
                {{ $t('operation.operate') }}
              </p>
            </div>
            <template v-for="(item, index) in filterTableData">
              <div
                v-if="visualScrollShow(index)"
                :key="item.subId || item.subDataId + index"
                class="field-component__block subform-item subform-operate"
                :class="{
                  'subform-item_nunitText': item.transformUnitRate,
                  'highlight-row': isHoverRowEqual(index)
                }"
                @mouseenter="handlerMouseEnterDebounce($event, index, 'op')"
                @mouseleave="handlerMouseLeaveDebounce($event, index, 'op')"
              >
                <i
                  class="web-icon-ellipsis web-iconfont"
                  :class="deleteBtnDisabled ? 'isDisabled' : ''"
                >
                </i>
              </div>
            </template>
            <!-- 数据汇总行 -->
            <div
              v-if="subSummary"
              class="field-component__block subform-item summary"
              style="height: 24px; background: #fbfbfb"
              :style="subFormItemStyle"
            ></div>
          </div>
        </div>
        <div class="page-container">
          <template v-if="selectArr.length === 0">
            <div v-if="!isSee && !single" class="subform-bottom">
              <el-button
                v-if="!noAdd"
                class="page-container-add"
                :disabled="addButtonDisable"
                plain
                size="mini"
                @click="addOneSubFormValue"
                >{{ $t('operation.append') }}</el-button
              >
              <el-button
                v-if="!noAdd && !subProductField"
                class="page-container-add"
                :disabled="addButtonDisable"
                plain
                size="mini"
                @click="pasteAddDialogVisible = true"
                >{{ $t('operation.pasteAdd') }}</el-button
              >
            </div>
          </template>
          <!-- 有勾选数据后展示的操作 -->
          <div v-else>
            <span v-html="$t('listTable.selectNumbers', { attr: selectArr.length })"></span>
            <span>
              <el-button :disabled="copyBtnDisabled" size="mini" @click="batchOperate('copy')">{{
                $t('operation.copy')
              }}</el-button>
              <el-button
                :disabled="deleteBtnDisabled"
                size="mini"
                @click="batchOperate('delete')"
                >{{ $t('operation.delete') }}</el-button
              >
              <el-button size="mini" @click="batchOperate('cancel')">{{
                $t('operation.cancelBatchOperate')
              }}</el-button>
            </span>
          </div>
          <!-- 翻页器 -->
          <el-pagination
            v-if="needPaginationShow"
            :current-page.sync="virtualConfig.current"
            layout="total, prev, pager, next, jumper"
            :page-size="10"
            :total="currentValueLength"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </template>
      <!-- 操作浮窗 -->
      <OperatePopover
        ref="operatePopover"
        :copy-btn-disabled="copyBtnDisabled"
        :delete-btn-disabled="deleteBtnDisabled"
        :is-forbid-add-field="isForbidAddField"
        @operate="handlerSubformOperate"
      ></OperatePopover>
    </div>
    <slot name="dialog"></slot>
    <BatchEditDialog
      v-if="batchEditDialogShow"
      :item-field="itemBatchField"
      :show.sync="batchEditDialogShow"
      @batchSubmit="batchSubmit"
    />

    <!-- 子表单单条数据详情浮窗 -->
    <SubFormEditDetail
      ref="subFormEditDetail"
      :copy-btn-disabled="copyBtnDisabled"
      :delete-btn-disabled="deleteBtnDisabled"
      :field-instant="fieldInstant"
      :form="form"
      :is-forbid-add-field="isForbidAddField"
      :show-detail.sync="showDetail"
      :state-index="stateIndex"
      :sub-form-name="fieldInfo.attrName"
      :total-page="currentValue && currentValue.length"
      @hideDrawer="hideDrawer"
      @operate="handlerSubformDetailOperate"
      @updatePage="subFormFullScreen"
    ></SubFormEditDetail>
    <!-- 粘贴新增 -->
    <PasteAddDialog
      v-if="pasteAddDialogVisible"
      :current-row-count="currentValue && currentValue.length"
      :max-row-count="maxRowCount"
      :original-field-list="pastableSubFields"
      :visible.sync="pasteAddDialogVisible"
      @paste-confirm="handlePasteConfirm"
    />
  </FormItemLayout>
</template>

<script setup>
import { computed, onMounted, ref, nextTick, watch } from 'vue'
import { useCommonCtxOpts, useCommonCtx } from '../../../composables/useCommonCtx'
import FormItemLayout from '../../layout/form-item-layout.vue'
import { getBaseComponentsByFieldType } from '@/components/nuwa/utils/registerBaseComponents'
import xbb from '@xbb/xbb-utils'
import BatchEditDialog from '@/components/all-fields/form-data-edit/SubForm/batch-edit-dialog.vue'
import { generateFormData } from '@/components/nuwa/utils/generateFormData'
import OperatePopover from './operate-popover.vue'
import PasteAddDialog from './paste-add-dialog'
import SubFormEditDetail from './subform-edit-detail'
import { useStore } from '@/composables/useStore'
import i18n from '@/lang'
import { MessageBox, Message } from 'element-ui'
import { useSubDataRely } from '@/components/nuwa/composables/default-val/useDataRely'
import useDefaultVal from '@/components/nuwa/composables/default-val/useDefaultVal'
import { onFieldValueChange, LifeCycleTypes } from '@nuwa/core'
import {
  getDataRelyMap,
  handleSubFieldChange,
  handleFieldChange
} from '@/components/nuwa/composables/default-val/useDataRely'
import SubFormSee from './sub-form-see.vue'
import { useRoute } from 'vue-router/composables'
import { execLowCode } from '@/components/nuwa/composables/useLowCode'
import {
  defaultValBatch,
  initToday,
  initDynamic,
  initSelectionComboType
} from '@/utils/subform-default-batch/default-val-batch.js'
import { getSpecialDisplayFieldMap } from '@/utils/data-rely-batch'
import { PopupManager } from 'element-ui/lib/utils/popup'

defineOptions({
  name: 'Subform'
})
const props = defineProps({
  single: {
    // 是否是单条模式
    type: Boolean,
    default: false
  },
  // 不可新增
  noAdd: {
    type: Boolean,
    default: false
  },
  subProductField: {
    type: [Object, null],
    default: null
  },
  showGift: {
    type: Boolean,
    default: false
  },
  distributorId: {
    type: Number,
    default: null
  },
  ...useCommonCtxOpts
})

const {
  showLabel,
  mode,
  fieldInfo,
  form,
  fieldInstant,
  visible,
  isSee,
  currentValue,
  fieldPosition,
  disabled
} = useCommonCtx(props)

const store = useStore()

const tableInstant = ref(null)
// 低代码用 跟后端里的低代码 hook 枚举一一对应
const ON_ADDROW = 12
const ON_DELETEROW = 13
const ON_COPYROW = 14

const subFields = computed({
  get: () => {
    const subFields = fieldInstant.fieldInfo.subForm.items.filter((item) => {
      if (item.isOpen !== 1) return false
      const subFieldHeadInstant = props.fieldInstant.form.getFieldInstant(
        `${fieldInstant.refKey}.${item.attr}`
      )
      return (
        subFieldHeadInstant.state.display === 'visible' ||
        subFieldHeadInstant.state.display === 'hidden'
      )
    })
    const subFieldsWithId = subFields.map((item) => {
      return {
        ...item,
        id: xbb.guid()
      }
    })
    return subFieldsWithId
  },
  set: (val) => {
    fieldInstant.changeFieldInfo('subForm', { items: val })
  }
})

// 计算子表单内组件的宽度
// 关联类型会多个30px
const minusWidth = (subField) => {
  const theWidth = subField.theWidth || 220
  if (!/^(10007|10008|10032)$/.test(subField.fieldType)) {
    return { width: theWidth - 30 + 'px' }
  } else {
    return {
      'min-width': theWidth + 'px',
      'max-width': theWidth + 'px'
    }
  }
}
const currentValueLength = computed(() => {
  return currentValue.value ? currentValue.value.length : 0
})

const pastableSubFields = computed(() => {
  return subFields.value.filter((item) => {
    return item.visible && [1, 7, 2, 4, 12].includes(Number(item.fieldType))
  })
})

const maxRowCount = computed(() => {
  const subFormConfig = store.state.user.subFormConfig
  const countMap = {
    // 用fieldInfo索引
    10006: subFormConfig.subFormRowNum,
    20003: 10,
    20004: subFormConfig.selectProductRowNum,
    // 用saasAttr索引
    nextPlan: 20
  }
  return countMap[fieldInfo.fieldType] || countMap[fieldInfo.saasAttr] || 200
})
/**
 * 限制子表单数量
 */
function countLimitValidate() {
  if (currentValue.value && currentValue.value.length >= maxRowCount.value) {
    Message.error(`超过该子表单能新建的最大数量${maxRowCount.value}`)
    return false
  }
  return true
}
const componentsName = (fieldInfo) => {
  const fieldType = fieldInfo.fieldType

  if (!fieldType) {
    return false
  }
  const head = props.form.getFieldInstant(`${fieldInfo.parentAttr}.${fieldInfo.attr}`)
  if (head.customerRender) {
    return head.customerRenderComponent
  }
  return getBaseComponentsByFieldType(fieldInfo)
}

// 批量修改列数据操作
const isFromDesign = computed(() => {
  return form.formParams.mode === 'design'
})
const isFromFlow = computed(() => {
  return form.formParams.mode === 'flow'
})
function batchEditFields(fieldAttr) {
  const subField = form.getFieldInstant(`${fieldInstant.refKey}.${fieldAttr}`)
  // 目前仅支持单行、多行、数字类型的默认有批量按钮，业务中不需要批量的按钮以field.saasAttr判断
  return (
    [1, 2, 7].includes(subField.fieldInfo.fieldType) &&
    !['phone', 'batch', 'guaranteePeriod', 'serial'].includes(subField.fieldInfo.saasAttr)
  )
}
function isDisabledBatchBtn(fieldAttr) {
  const subField = form.getFieldInstant(`${fieldInstant.refKey}.${fieldAttr}`)
  return (
    isFromDesign.value ||
    !subField.fieldInfo.editable ||
    !(currentValue.value && currentValue.value.length)
  )
}
function isShowBatchBtn(fieldAttr) {
  return (isFromFlow.value || !isSee.value) && batchEditFields(fieldAttr)
}

const batchEditDialogShow = ref(false)
const itemBatchField = ref({})
function batchEdit(fieldAttr) {
  const subField = fieldInstant.form.getFieldInstant(`${fieldInstant.refKey}.${fieldAttr}`)
  itemBatchField.value = JSON.parse(JSON.stringify(subField.fieldInfo))
  batchEditDialogShow.value = true
}
function batchSubmit(val) {
  fieldInstant.setValueByCol(itemBatchField.value.attr, val)
}

// 子表单分页逻辑
// 是否需要开启分页
const needPaginationShow = computed(() => {
  return currentValue.value && currentValue.value.length > 50
})
const virtualConfig = ref({
  current: 0,
  start: 0,
  end: 0
})
function getRealIndex(index) {
  const currentIndex = index + virtualConfig.value.current * 10 - 10
  return needPaginationShow.value ? currentIndex : index
}
const getFieldInstant = (attr, index) => {
  const currentIndex = getRealIndex(index)
  return form.getFieldInstant(`${fieldInstant.refKey}.${currentIndex}.${attr}`)
}

// 计算元素是否在虚拟滚动展示范围内
const visualScrollShow = computed(() => {
  return (index) => {
    const currentIndex = getRealIndex(index)
    return needPaginationShow.value
      ? currentIndex >= virtualConfig.value.start && currentIndex <= virtualConfig.value.end
      : true
  }
})
const filterTableData = computed(() => {
  return (
    currentValue.value &&
    currentValue.value.filter((item, index) => {
      return needPaginationShow.value
        ? index >= virtualConfig.value.start && index <= virtualConfig.value.end
        : true
    })
  )
})

// ----------------- 子表单操作按钮逻辑 --------------------------
const hoverRow = ref(-1)

const addDisabled = computed(() => {
  return currentValue.value && currentValue.value.length >= maxRowCount.value
})
const editable = computed(() => {
  return !!fieldInfo.editable && fieldInfo.noEditable !== 1
})
// 添加按钮的是否可操作
const addButtonDisable = computed(() => {
  return addDisabled.value || !editable.value || disabled.value
})
// 添加是否可操作
const isForbidAddField = computed(() => {
  return addButtonDisable.value || props.noAdd || props.single
})

const isForbidCopyField = computed(() => {
  return isForbidAddField.value
})
// 编辑模式 序列号编辑不可复制
const isCannotCoySerial = computed(() => {
  return (type) => {
    return mode.value === 'edit' && type
  }
})

// 复制按钮是否可操作
const copyBtnDisabled = computed(() => {
  const targetRow = currentValue.value && currentValue.value[hoverRow.value]
  return Boolean(
    isForbidCopyField.value ||
      (targetRow && isCannotCoySerial.value(targetRow['enableSerialNumber']))
  )
})

// 删除按钮是否可操作
const deleteBtnDisabled = computed(() => {
  return !editable.value || props.single || disabled.value
})
const operatePopover = ref(null)
// 显示操作 popover
function showOperate(target, index) {
  operatePopover.value && operatePopover.value.showPopover(target, index)
}
function handlerSubformMouseEnter(e, index, type) {
  const currentIndex = getRealIndex(index)
  hoverRow.value = currentIndex
  if (
    type === 'op' &&
    !(copyBtnDisabled.value && isForbidAddField.value && deleteBtnDisabled.value)
  ) {
    showOperate(e.target, currentIndex)
  }
}
const handlerMouseEnterDebounce = xbb.debounce(handlerSubformMouseEnter, 30)

// 隐藏操作 popover
function hideOperate() {
  operatePopover.value && operatePopover.value.hidePopover()
}
function handlerSubformMouseLeave(e, index, type) {
  hoverRow.value = null
  if (type === 'op') {
    hideOperate()
  }
}
const handlerMouseLeaveDebounce = xbb.debounce(handlerSubformMouseLeave, 30)

function getDefaultRely() {
  // 收集统一依赖和标记信息减少性能消耗
  const customValueFields = []
  const dataRelyFields = {}
  const todayDefaultFields = []
  const dynamicDefaultFields = []
  const linkFormDefaultFields = []
  const selectionComboFields = []
  // const linkDataMultiValueFields = []
  // const linkDataValueFields = []

  subFields.value.forEach((res) => {
    if (res.defaultAttr && res.defaultAttr.defaultType) {
      if (
        (res.defaultAttr.defaultType === 'custom' &&
          (res.defaultAttr.defaultValue ||
            res.defaultAttr.defaultValue === 0 ||
            res.defaultAttr.defaultList)) ||
        res.defaultAttr.defaultType === 'linkDataMulti' ||
        res.defaultAttr.defaultType === 'linkData'
      ) {
        customValueFields.push(res)
      } else if (res.defaultAttr.defaultType === 'dataRely') {
        const relyConditions = res.defaultAttr.rely.condition || [res.defaultAttr.rely]
        const specialDisplayFieldMap = getSpecialDisplayFieldMap({
          fatherFormInfo: {
            businessType: fieldInstant.form.formParams.fatherBusinessType,
            templateId: (fieldInstant.form.formParams.fatherData || {}).template,
            fatherData: fieldInstant.form.formParams.fatherData
          },
          sourceFormInfo: {
            businessType: res.defaultAttr.rely.sourceBusinessType,
            formId: res.defaultAttr.rely.sourceFormId
          },
          businessType: fieldInstant.form.formParams.businessType,
          relyConditions
        })
        // 过滤特殊场景(关联新建相关)，特殊场景交给default-val处理
        if (xbb._isEmpty(specialDisplayFieldMap)) {
          dataRelyFields[res.attr] = {
            rely: res.defaultAttr.rely,
            fieldType: res.fieldType
          }
        }
      } else if (res.defaultAttr.defaultType === 'today') {
        todayDefaultFields.push(res)
      } else if (res.defaultAttr.defaultType === 'dynamic') {
        dynamicDefaultFields.push(res)
      } else if (res.defaultAttr.defaultType === 'linkForm') {
        linkFormDefaultFields.push(res)
      }
    }
    if (res.comboType === 0 && res.items && res.items.findIndex((n) => n.checked) !== -1) {
      selectionComboFields.push(res)
    }
  })
  return {
    customValueFields,
    dataRelyFields,
    todayDefaultFields,
    dynamicDefaultFields,
    linkFormDefaultFields,
    selectionComboFields
  }
}

// ----------------- 子表单选中列，批量编辑逻辑 --------------------------
const selectArr = ref([])
const pasteAddDialogVisible = ref(false)
watch(pasteAddDialogVisible, (val) => {
  if (val && currentValue.value === undefined) {
    currentValue.value = []
  }
})
const isAllSelect = ref(false)
const copyFlag = ref(false)

// 增加、删除、复制操作逻辑
async function addOneSubFormValue() {
  if (!countLimitValidate()) {
    return false
  }
  const res = await execSubformOperateLowCode(ON_ADDROW)
  if (!res.flag) return false
  const defaultValue = generateFormData(fieldInstant.fieldInfo.subForm.items)
  const {
    customValueFields,
    // formulaValueFields,
    // dataRelyFields,
    todayDefaultFields,
    linkFormDefaultFields,
    dynamicDefaultFields,
    selectionComboFields
  } = getDefaultRely()
  const defaultValueFields = [].concat(customValueFields, linkFormDefaultFields)
  defaultValBatch(defaultValue, defaultValueFields, false)
  initToday(defaultValue, todayDefaultFields)
  initDynamic({
    rowData: defaultValue,
    dynamicDefaultFields,
    userInfo: store.state.user.userInfo
  })
  initSelectionComboType(defaultValue, selectionComboFields)
  // console.log(defaultValue, 'defaultValue')
  fieldInstant.push(defaultValue)
  nextTick(() => {
    scrollToBottom(tableInstant.value)
    useSubDataRely(fieldInfo.subForm.items, fieldInstant, currentValue.value.length - 1)
  })
  // 触发子表单hook
  fieldInstant.notify(
    LifeCycleTypes.ON_ARRAY_FIELD_CREATE,
    fieldInstant,
    currentValue.value.length - 1
  )
}
function insertMultipleSubFormValue({ index, val, attr }) {
  if (!countLimitValidate()) {
    return false
  }

  if (val && val.length) {
    val.forEach((item, i) => {
      // 第一条数据，在本行中渲染，不必自动新增
      if (i !== 0) {
        const defaultValue = generateFormData(fieldInstant.fieldInfo.subForm.items)
        defaultValue[attr] = item
        const itemIndex = Number(index) + i
        fieldInstant.insert(itemIndex, defaultValue)
        // 子表单操作
        useSubDataRely(fieldInfo.subForm.items, fieldInstant, itemIndex)
        fieldInstant.notify(LifeCycleTypes.ON_ARRAY_FIELD_CREATE, fieldInstant, itemIndex)
      }
    })
  }
}
function multipleAdd(relationDataObj) {
  insertMultipleSubFormValue(relationDataObj)
}
function insertOneSubFormValue(index) {
  if (!countLimitValidate()) {
    return false
  }

  const defaultValue = generateFormData(fieldInstant.fieldInfo.subForm.items)
  const {
    customValueFields,
    // formulaValueFields,
    // dataRelyFields,
    todayDefaultFields,
    linkFormDefaultFields,
    dynamicDefaultFields,
    selectionComboFields
  } = getDefaultRely()
  const defaultValueFields = [].concat(customValueFields, linkFormDefaultFields)
  defaultValBatch(defaultValue, defaultValueFields, false)
  initToday(defaultValue, todayDefaultFields)
  initDynamic({
    rowData: defaultValue,
    dynamicDefaultFields,
    userInfo: store.state.user.userInfo
  })
  initSelectionComboType(defaultValue, selectionComboFields)
  fieldInstant.insert(index + 1, defaultValue)
  // 触发子表单hook
  nextTick(() => {
    useSubDataRely(fieldInfo.subForm.items, fieldInstant, index + 1)
  })
  fieldInstant.notify(LifeCycleTypes.ON_ARRAY_FIELD_CREATE, fieldInstant, index + 1)
}
// 复制子表单数据
async function copyOneSubFormValue(index, addToLast) {
  if (!countLimitValidate()) {
    return false
  }
  const res = await execSubformOperateLowCode(ON_COPYROW, index)
  if (!res.flag) return false
  const newVal = xbb.deepClone(currentValue.value[index])
  if (newVal.productSubId) delete newVal.productSubId
  if (newVal.subDataId) delete newVal.subDataId
  copyFlag.value = true
  if (addToLast) {
    fieldInstant.push(newVal)
    fieldInstant.notify(
      LifeCycleTypes.ON_ARRAY_FIELD_COPY,
      fieldInstant,
      currentValue.value.length - 1
    )
  } else {
    fieldInstant.insert(index + 1, newVal)
    fieldInstant.notify(LifeCycleTypes.ON_ARRAY_FIELD_COPY, fieldInstant, index + 1)
  }
  // 用于处理公式默认值的情况 如果已经复制了一行就不需要再存留默认值了
  // sessionStorage.removeItem('defaultVal')
  // copyFlag.value = true
}
const route = useRoute()
const { formId, businessType, subBusinessType, saasMark, appId } = fieldInstant.form.formParams
const lowCode = store.state.lowcode.instant[formId] || {}

function execSubformOperateLowCode(code, index) {
  const lowCodeAttr = fieldInfo.attr
  const arr = lowCode.config && lowCode.config.actionList[lowCodeAttr]
  const info = {
    formId: formId || route.query.formId,
    businessType: businessType === undefined ? route.query.businessType : businessType,
    subBusinessType: subBusinessType === undefined ? route.query.subBusinessType : subBusinessType,
    saasMark: saasMark === undefined ? route.query.saasMark : saasMark,
    appId: appId || route.query.appId
  }
  const dataJSON = xbb.deepClone(fieldInstant.form.getValue())
  dataJSON.index = index
  return new Promise(async (resolve, reject) => {
    if (!document.querySelector('iframe') || !arr) {
      resolve({ flag: true, exection: false })
      return
    }
    let flag = true
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].code === code) {
        const factoryRes = await execLowCode({
          handle: arr[i].code,
          info,
          action: arr[i].list,
          formId: formId || route.query.formId,
          dataJSON
        })
        if (!factoryRes.success) {
          flag = false
          break
        }
      }
    }
    resolve({ flag, exection: true })
  })
}
async function deleteOneSubForm(item) {
  // ! 注意：批量删除的时候，因为调用这个方法是一个 forEach 循环，并没有异步的能力，但是因为下面这段异步代码，会导致假设当前10 条数据
  // 这个方法到这里为止会执行 10 遍，index 从 0-9
  // 但是真的到了删除的时候，value 已经变了，所以需要针对这个情况对 index 做修复
  let index = currentValue.value.findIndex((n) => n.subId === item)
  const res = await execSubformOperateLowCode(ON_DELETEROW, index)
  index = currentValue.value.findIndex((n) => n.subId === item)
  if (!res.flag) return false
  if (index === -1) return false
  if (
    currentValue.value[index] &&
    ('productSubId' in currentValue.value[index] || 'refProductId' in currentValue.value[index])
  ) {
    sessionStorage.removeItem('defaultVal')
  }
  fieldInstant.remove(index)
  // 触发子表单hook
  fieldInstant.notify(LifeCycleTypes.ON_ARRAY_FIELD_REMOVE, fieldInstant, index)
}
function handleCurrentChange(e) {
  if (e === 0) return
  virtualConfig.value = {
    current: e,
    start: (e - 1) * 10,
    end: e * 10 - 1
  }
  // 计算目标页是否需要回显全选状态
  const subIdArr = currentValue.value.slice(virtualConfig.value.start, virtualConfig.value.end + 1)
  let isSelectAll = true
  subIdArr.forEach((item) => {
    if (!selectArr.value.includes(item.subId)) {
      isSelectAll = false
    }
  })
  isAllSelect.value = isSelectAll
}
// 批量操作
function batchOperate(type) {
  switch (type) {
    case 'copy':
      if (
        currentValue.value &&
        currentValue.value.length + selectArr.value.length > maxRowCount.value
      ) {
        Message.error(`超过该子表单能新建的最大数量${maxRowCount.value}`)
        return false
      }
      selectArr.value.forEach((item) => {
        const index = currentValue.value.findIndex((n) => n.subId === item)
        copyOneSubFormValue(index, true)
      })
      setTimeout(() => {
        scrollToBottom(tableInstant.value)
      })
      isAllSelect.value = false
      break
    case 'delete':
      MessageBox.confirm(`确认删除选中的${selectArr.value.length}条数据吗？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          for (let i = 0; i < selectArr.value.length; i++) {
            deleteOneSubForm(selectArr.value[i])
          }
          isAllSelect.value = false
          selectArr.value = []
          // 如果删完还大于 50 条，要变一下页码
          if (currentValue.value.length > 50) {
            handleCurrentChange(virtualConfig.value.current - 1)
          }
          Message({
            type: 'success',
            message: i18n.t('message.delSuccess')
          })
        })
        .catch((err) => {
          Message({
            type: 'info',
            message: i18n.t('message.cancelDel')
          })
        })
      break
    case 'cancel':
      selectArr.value = []
      isAllSelect.value = false
      break
  }
}
// 汇总字段值变化时，重新计算汇总数据
const summaryFields = subFields.value
  .filter((subField) => {
    return subField.summaryFlag
  })
  .map((subField) => {
    return subField.attr
  })

function updateSummaryData(summaryField) {
  const el = document.getElementById(`summary-data-${fieldInstant.fieldInfo.attr}-${summaryField}`)
  const fieldInfo = subFields.value.find((subField) => {
    return subField.attr === summaryField
  })
  const res = summaryData(fieldInfo)
  if (el) {
    el.innerHTML = `<span>${res}</span>`
  }
}
function updateAllSummaryData() {
  for (const summaryField of summaryFields) {
    updateSummaryData(summaryField)
  }
}
// 子表单单行浮层操作
function handlerSubformOperate(type, index) {
  switch (type) {
    case 'copy':
      isAllSelect.value = false
      copyOneSubFormValue(index)
      updateAllSummaryData()
      nextTick(() => {
        scrollToBottom(tableInstant.value)
      })
      break
    case 'addToNext':
      isAllSelect.value = false
      insertOneSubFormValue(index)
      break
    case 'delete':
      const subId = currentValue.value[index].subId
      const idx = selectArr.value.findIndex((n) => n === subId)
      if (idx > -1) {
        selectArr.value.splice(idx, 1)
      }
      deleteOneSubForm(subId)
      updateAllSummaryData()
      break
  }
}

// ----------------- 子表单粘贴新增逻辑 --------------------------

function addOneSubFormRowWithValue(rowValue) {
  if (!countLimitValidate()) {
    return false
  }

  const defaultValue = generateFormData(fieldInstant.fieldInfo.subForm.items)
  const rowKeys = Object.keys(defaultValue)
  Object.keys(rowValue).forEach((key) => {
    if (rowKeys.includes(key)) {
      defaultValue[key] = rowValue[key]
    }
  })
  copyFlag.value = true
  fieldInstant.push(defaultValue)
  // useSubDataRely(fieldInfo.subForm.items, fieldInstant, currentValue.value.length - 1)
  // 触发子表单hook
  fieldInstant.notify(
    LifeCycleTypes.ON_ARRAY_FIELD_CREATE,
    fieldInstant,
    currentValue.value.length - 1
  )
  return true
}
function scrollToBottom(dom) {
  dom.scrollTop = 99999 // scrollTop设置一个超出这个容器可滚动的值, scrollTop 会被设为最大值。具体查看https://developer.mozilla.org/zh-CN/docs/Web/API/Element/scrollTop
}
function handlePasteConfirm(tableData, close) {
  for (let i = 0; i < tableData.length; i++) {
    const success = addOneSubFormRowWithValue(tableData[i])
    if (!success) break
  }
  nextTick(() => {
    scrollToBottom(tableInstant.value)
  })
  close()
}

// ----------------- 子表单单条数据详情浮窗逻辑 --------------------------
// const freezeSubFields = computed(() => {
//   if (isFromDesign.value) {
//     return subFields.value
//   }
//   return subFields.value.filter((item) => {
//     // 如果字段不可见 但配置了公式 且配置了赋值规则为不可见也运行公式 则也需要渲染 上方再通过v-show控制具体渲不渲染
//     const isAssignMentRules = item.defaultAttr && item.defaultAttr.defaultType === 'formula'
//     return item.visible || isAssignMentRules
//   })
// })

const subFormEditDetail = ref(null)
const stateIndex = ref(0)
const showDetail = ref(false)
// 全屏某一条指定的数据
function subFormFullScreen(index) {
  // subFormEditDetail.value.showDrawer(index)
  stateIndex.value = getRealIndex(index)
  if (!showDetail.value) showDetail.value = true
}
function hideDrawer(value) {
  showDetail.value = value
}
function handlerSubformDetailOperate(type, index) {
  handlerSubformOperate(type, index)
  subFormFullScreen(index + 1)
}

// ----------------- 子表单单全选逻辑 --------------------------
// 当页全选
function handlerSelectPageData() {
  if (currentValue.value.length <= 50) {
    if (isAllSelect.value) {
      selectArr.value = currentValue.value.map((n) => n.subId)
    } else {
      selectArr.value = []
    }
  } else {
    const arr = currentValue.value.slice(virtualConfig.value.start, virtualConfig.value.end + 1)
    arr.forEach((item) => {
      if (isAllSelect.value && !selectArr.value.includes(item.subId)) {
        selectArr.value.push(item.subId)
      } else {
        const idx = selectArr.value.findIndex((n) => n === item.subId)
        if (idx !== -1 && !isAllSelect.value) selectArr.value.splice(idx, 1)
      }
    })
  }
}
// 一行选中
function changeSingleCheckbox(item) {
  if (selectArr.value.includes(item.subId)) {
    const index = selectArr.value.findIndex((n) => n === item.subId)
    // console.log(index)
    selectArr.value.splice(index, 1)
  } else {
    selectArr.value.push(item.subId)
  }
  if (currentValue.value.length >= 50) {
    const arr = currentValue.value.slice(virtualConfig.value.start, virtualConfig.value.end + 1)
    let isAllSelectScope = true
    arr.forEach((item) => {
      if (!selectArr.value.includes(item.subId)) {
        isAllSelectScope = false
      }
    })
    isAllSelect.value = isAllSelectScope
  } else {
    isAllSelect.value = selectArr.value.length === currentValue.value.length
  }
}
// ----------------- 子表单全屏逻辑 --------------------------
const isFullScreen = ref(false)
const headerRowHeight = 34
const dataRowHeight = 44
const maxHeight = computed(() => {
  return isFullScreen.value ? '85%' : `${headerRowHeight + dataRowHeight * 11}px` // 内容行超过10行显示滚动条
})
// 子表单展示全屏
function fullScreen() {
  isFullScreen.value = !isFullScreen.value
}

const fullScreenStyles = computed(() => {
  return isFullScreen.value
    ? {
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        zIndex: PopupManager.nextZIndex()
      }
    : ''
})

// const isFromEdit = computed(() => {
//   return fieldInstant.form.formParams.mode === 'edit'
// })
const isFromCopy = computed(() => {
  return fieldInstant.form.formParams.isCopy
})
const isFromTransfer = computed(() => {
  return fieldInstant.form.formParams.isFromTransfer
})
const isUsedDraft = computed(() => {
  return fieldInstant.form.formParams.isUsedDraft
})
const defaultRet = useDefaultVal(fieldInstant)

const disableInitValue = computed(() => {
  return (
    !isFromFlow.value &&
    !isFromDesign.value &&
    !isFromCopy.value &&
    !isUsedDraft.value &&
    !isFromTransfer.value
  )
})

if (defaultRet && disableInitValue.value) {
  const { defaultVal } = defaultRet
  defaultVal.value && fieldInstant.setValue(defaultVal.value)
}

function isHoverRowEqual(index) {
  const currentIndex = getRealIndex(index)
  return hoverRow.value === currentIndex
}
// 单价为0 时 展示赠品
function getGiftShow(index) {
  if (!props.showGift) return
  const currentIndex = getRealIndex(index)
  // 如果是产品字段，且业务是 【机会，报价单，合同】的时候
  const product = currentValue.value && currentValue.value[currentIndex]['text_1']
  if (!product) return false
  const price = currentValue.value && currentValue.value[currentIndex]['num_6']
  return price === 0
}
// li的冻结定位
function liFreezeLeft(subField, index) {
  const freezeCount = fieldInfo.freezeCount
  if (!isFromDesign.value && freezeCount < subFields.value.length) {
    let leftWidth = 68 // 第一个冻结列左侧有序号列和操作列
    // 第二个li开始，第一个固定为69px定位
    if (index <= freezeCount - 1 && index >= 1) {
      if (/^(10007|10008|10032)$/.test(subFields.value[index - 1].fieldType)) {
        // 前一个li是关联字段
        subFields.value[index - 1].linkInfo.linkedShowAttr &&
          subFields.value[index - 1].linkInfo.linkedShowAttr.forEach((item) => {
            leftWidth += item.theWidth || 220
          })
        leftWidth += (subFields.value[index - 1].theWidth || 220) + 22
        leftWidth += subFields.value[index - 2] ? subFields.value[index - 2].theWidth || 220 : 0
      } else if (
        subFields.value[index - 2] &&
        /^(10007|10008|10032)$/.test(subFields.value[index - 2].fieldType)
      ) {
        // 前第二个li是关联字段
        subFields.value[index - 2].linkInfo.linkedShowAttr &&
          subFields.value[index - 2].linkInfo.linkedShowAttr.forEach((item) => {
            leftWidth += item.theWidth || 220
          })
        leftWidth += subFields.value[index - 2].theWidth || 220
        leftWidth += (subFields.value[index - 1].theWidth || 220) + 22
      } else if (
        subFields.value[index - 2] &&
        !/^(10007|10008|10032)$/.test(subFields.value[index - 2].fieldType)
      ) {
        // 第三个li的定位
        leftWidth += (subFields.value[0].theWidth || 220) + (subFields.value[1].theWidth || 220)
      } else {
        // 一般情况
        leftWidth += subFields.value[index - 1] ? subFields.value[index - 1].theWidth || 220 : 0
      }
    }
    return index < freezeCount
      ? {
          position: 'sticky',
          left: leftWidth + 'px',
          'z-index': 94 // 最多冻结3列，设置为 3+1 避免非冻结列遮挡冻结列
        } // 冻结列设置样式
      : {} // 非冻结列不设置样式
  }
  // 编辑模式不进行样式更改，否则会与拖拽组件相冲突
  return {}
}
// 子表单数据是否存在汇总字段
const subSummary = computed(() => {
  return subFields.value.some((subField) => {
    return subField.summaryFlag
  })
})
const subFormItemStyle = computed(() => {
  return { 'text-align': isSee.value ? 'left' : '' }
})

const showList = (subField) => {
  const fieldInstant = props.form.getFieldInstant(`${fieldInfo.attr}.${subField.attr}`)
  return fieldInstant.state.display === 'visible'
}

// 汇总数据
function summaryData(subField) {
  if (subField.summaryFlag) {
    let data = 0
    const n = subField.accuracy || 4
    currentValue.value &&
      currentValue.value.forEach((item) => {
        data = +xbb.plus(Number(item[subField.attr] || 0), data)
      })
    const res = +xbb.plus(0, data, n)
    return res
  }
}

onMounted(() => {
  virtualConfig.value = {
    current: 1,
    start: 0,
    end: 9
  }
  if (subSummary.value) {
    form.addEffects(`${fieldInstant.refKey}`, () => {
      for (const summaryField of summaryFields) {
        onFieldValueChange(`${fieldInstant.refKey}.${summaryField}`, (field, context) => {
          // eslint-disable-next-line max-nested-callbacks
          nextTick(() => {
            updateSummaryData(summaryField)
          })
        })
      }
    })
  }
  fieldInstant.form.addEffects(`${fieldInstant.refKey}dataRely`, () => {
    // 子字段数据联动处理
    const dataRelyMap = getDataRelyMap(form.schema)
    Object.keys(dataRelyMap).forEach((displayField) => {
      // console.log(displayField, 'displayField')
      const subFormAttr = displayField.split('.')[0]
      if (displayField.split('.').length > 1 && subFormAttr === fieldInstant.refKey) {
        const subFormField = displayField.split('.')[1]
        // 这时候的 currentValue 是执行过默认值逻辑的值，先进行一次初始化的计算
        if (disableInitValue.value && fieldInstant.form.formParams.mode === 'add') {
          handleSubFieldChange({
            field: fieldInstant,
            subFormValue: currentValue.value,
            dataRelyMapEntry: dataRelyMap[displayField],
            subFormAttr,
            subFormField,
            isInit: true
          })
        }
        // 再监听子字段
        // 1. 监听子表单单列，onFieldValueChange 返回具体更改行数
        // eslint-disable-next-line max-nested-callbacks
        onFieldValueChange(displayField, (field, context) => {
          if (context.operate === 'remove') {
            return
          }
          if (copyFlag.value) {
            copyFlag.value = false
            return
          }
          const value = context.value
          const isSingleRowChange = !isNaN(Number(context.index))
          // 1. 监听对象是子字段，那么只可能联动当前子表单内子字段
          handleFieldChange({
            field,
            value,
            dataRelyMapEntry: dataRelyMap[displayField],
            isSubForm: true,
            subFormAttr: displayField.split('.')[0],
            subFormIndex: context.index,
            isSingleRowChange
          })
        })
      }
    })
  })
})

watch(currentValue, () => {
  updateAllSummaryData()
})
</script>

<style lang="scss" scoped>
$row-height: 34px;
$indexCol-width: 68px;
$operateCol-width: 40px;
$item-width: 220px;
$hover-border-color: $brand-color-5;
$border-color: $neutral-color-3;

#operate-subForm .subform-item .isDisabled {
  color: $text-grey !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
}

.page-container {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  .page-container-add {
    color: $brand-color-5;
    border-color: $brand-color-5;
    &:hover {
      color: $brand-base-color-6;
      border-color: $brand-base-color-6;
    }
    &.is-disabled {
      color: $text-grey;
      border-color: $line-table;
    }
  }
}

.subform-operate {
  cursor: pointer;
}

.highlight-row {
  background-color: $brand-color-1;
}

.subform-header-selectall {
  margin-right: 10px;
  .el-checkbox__input {
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
    }
  }
  .el-checkbox__inner::after {
    top: 2px;
    left: 5px;
  }
}
.subform-table-fullscreen {
  margin-top: 2px;
  font-size: 14px;
  cursor: pointer;
}
.subform-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 50px;
  overflow: hidden;
  background: white;
  & > .subform-bottom {
    width: 100%;
    height: $row-height;
    text-align: left;
    cursor: pointer;
    &:hover {
      color: $brand-color-5;
    }
  }
  .subform-content--see {
    & > .subform-cell {
      flex: 1 0;
    }
  }
  & > .subform-content {
    position: relative;
    display: flex;
    width: 100%;
    overflow: auto;
    .last-row {
      text-align: center;
      border-bottom: 1px solid transparent !important;
    }
    & > #operate-subForm {
      position: sticky;
      right: 0;
      z-index: 2;
      align-self: flex-start;
      width: $operateCol-width;
      .subform-header,
      .subform-item {
        border-left: 1px solid $border-color;
      }
    }
    .subform-content__left {
      position: sticky;
      left: 0;
      z-index: 3;
      box-sizing: border-box;
      flex-shrink: 0;
      align-self: flex-start;
      width: $indexCol-width;
      & > .left-cell {
        position: relative;
        z-index: 90;
        width: 100%;
        background: $base-white;
        .index-header {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .index-cell {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          > * {
            flex: 1;
          }
          :first-child {
            margin-left: 1px;
          }
          .index-cell--index {
            text-align: center;
          }
          .index-cell--fullscreen {
            flex: 0;
            margin-right: 7px;
            cursor: pointer;
          }
          .index-cell--checkbox-normal {
            margin-right: 3px;
          }
          .index-cell--checkbox-single-box {
            margin-right: 28px;
          }
          .el-checkbox__input {
            .el-checkbox__inner {
              width: 16px;
              height: 16px;
            }
            .el-checkbox__inner::after {
              top: 2px;
              left: 5px;
            }
          }
        }
        .index-cell--delete {
          display: none;
        }
        .index-cell--see {
          display: none;
        }
        > .subform-item,
        > .subform-header {
          border-left: 1px solid $border-color;
          > .subform-header-selectall {
            margin-right: 10px;
            .el-checkbox__input {
              .el-checkbox__inner {
                width: 16px;
                height: 16px;
              }
            }
            .el-checkbox__inner::after {
              top: 2px;
              left: 5px;
            }
          }
        }
      }
    }
    // 非设计模式时。关闭hove border 样式
    .subform-content__right--undesign {
      & > .subform-cell {
        background: $base-white;
        & > .subform-header {
          box-sizing: border-box;
          padding: 0 10px;
        }
      }
      & > .subform-cell:last-child {
        .subform-header,
        .subform-item {
          border-right: 0;
        }
      }
    }
    .subform-content__right--design {
      & > .subform-cell {
        background: $base-white;
        & > .subform-header {
          box-sizing: border-box;
          padding: 0 10px;
        }
        &:hover {
          > .subform-header {
            border-top: 1px dashed $brand-color-5;
            border-right: 1px dashed $brand-color-5;
            border-left: 1px dashed $brand-color-5;
          }
          > .subform-item {
            border-right: 1px dashed $brand-color-5;
            border-bottom: 1px dashed $brand-color-5;
            border-left: 1px dashed $brand-color-5;
          }
        }
      }
    }
    // 拖动元素在页面上drag容器中的虚影添加样式
    .dragging-subform-content__ghost {
      max-width: 220px;
      background: $base-white;
      border: 1px dashed $brand-color-5 !important;
      * {
        visibility: hidden;
      }
    }
    .subform-content__right {
      z-index: 1;
      box-sizing: border-box;
      display: flex;
      align-self: flex-start;
      // max-width: 220px;
      padding-bottom: 5px;
      & > .subform-cell--select {
        > .subform-header {
          border-top: 1px dashed $brand-color-5;
          border-right: 1px dashed $brand-color-5;
          border-left: 1px dashed $brand-color-5;
        }
        > .subform-item {
          border-right: 1px dashed $brand-color-5;
          border-bottom: 1px dashed $brand-color-5;
          border-left: 1px dashed $brand-color-5;
        }
      }
      // 空中透明一点
      & > .draggable-index__field-item--fallback {
        z-index: 10;
        cursor: move;
      }
      // 拖动时的虚影
      & > .draggable-index__field-item--ghost {
        z-index: 91;
        min-width: 220px;
        background: $base-white !important;
        border: 1px dashed $brand-color-5 !important;
        * {
          visibility: hidden;
        }
      }
      & > .padding-cell {
        flex-shrink: 0;
        order: 2;
        width: 75px;
        background: $base-white;
        border-top: 1px solid $border-color;
        border-right: 1px solid $border-color;
        border-bottom: 1px solid $border-color;
      }
      .no-field__block {
        position: relative;
        width: 220px;
        height: 78px;
        background: $base-white;
        border-top: 1px solid $border-color;
        border-right: 1px solid $border-color;
        border-bottom: 1px solid $border-color;
        & > .no-field__block--text {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
        &:not(:only-child) {
          > .no-field__block--text {
            display: none;
          }
        }
      }
    }
    .subform-header {
      position: sticky;
      top: 0;
      z-index: 2;
      box-sizing: border-box;
      height: $row-height;
      background: $base-white;
      border-top: 1px solid $border-color;
      border-right: 1px solid $border-color;
      border-bottom: 1px solid $border-color;
      .batch-edit {
        font-size: 12px;
        cursor: pointer;
      }
    }
    .subform-cell {
      position: relative;
      box-sizing: border-box;
      flex-shrink: 0;
      background: $base-white;
      & > .sub-field__toolbar {
        position: absolute;
        right: 10px;
        bottom: -11px;
        z-index: 93;
        padding: 0 5px;
        .sub-field__btns-item {
          padding: 8px;
          font-size: 13px;
        }
        .delete-btn {
          color: $error-base-color-6;
        }
      }
    }
    .subform-item {
      position: relative;
      box-sizing: content-box;
      height: $row-height;
      padding: 5px;
      padding-bottom: 4px;
      line-height: $row-height;
      text-align: center;
      border-right: 1px solid $border-color;
      border-bottom: 1px solid $border-color;
      .summary-data {
        width: 100%;
        padding: 0px 15px;
        overflow: hidden;
        text-align: right;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .current__component {
        flex-shrink: 0;
        margin-right: 5px;
      }
      & > .product-Gift__block {
        width: 20px;
        height: 20px;
        font-size: 12px;
        line-height: 20px;
        color: $error-base-color-6;
        color: $text-auxiliary;
        text-align: center;
        background-color: $bg-primary;
        border-radius: 2px;
      }
    }
    .subform-item_nunitText {
      height: 50px;
    }
    .field-component__block {
      display: flex;
      justify-content: center;
    }
    .subfield-name__block {
      display: flex;
      @include singleline-ellipsis;
      .title__block {
        display: flex;
        align-items: center;
        @include singleline-ellipsis;
        & > .required-icon {
          width: 12px;
        }
        & > .batch-edit {
          padding-left: 5px;
        }
        & > .field-name {
          flex: 1;
          @include singleline-ellipsis;
        }
      }
    }
    .linkField-header__block {
      display: flex;
      & > span {
        flex: 1;
        padding-left: 10px;
      }
    }
  }
}
</style>
