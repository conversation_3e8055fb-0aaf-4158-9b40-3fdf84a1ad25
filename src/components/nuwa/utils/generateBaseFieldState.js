import { reactive } from 'vue'
import xbb from '@xbb/xbb-utils'
import { addWeightAttr } from '@/utils/form-design.js'
import { FieldTypeEnum } from '@/constants/enum/fieldType'
import { generateValidator } from './generateValidator'
import { bizField } from '@/components/nuwa/effects/biz/biz_map'

export const generateCustomerField = function (fieldInfo, formQuery) {
  return bizField(
    formQuery.subBusinessType,
    fieldInfo.parentAttr ? `${fieldInfo.parentAttr}.${fieldInfo.attr}` : fieldInfo.attr,
    formQuery.businessType,
    fieldInfo.fieldType
  )
}

export const generateBaseFieldState = function (fieldInfo, otherConfig, formQuery) {
  // text_1: {
  //   display: 'visible', visible、hidden、none
  //     pattern: 'editable', view、editable、disable
  // }

  //  showType 和 visible 共同决定是否显示
  const isVisible =
    fieldInfo.visible && fieldInfo.fieldMapHidden !== 1 && fieldInfo.switchMapHidden !== 1
  const showTypeCondition = componentShowType(fieldInfo.showType, otherConfig)
  let display = 'visible'
  if (!isVisible || !showTypeCondition) display = 'hidden' // 注意：nvwa选项关联里有跟这块同样的显隐处理逻辑，若修改这边逻辑，需要去useLinkMap.js里getDisplayValue同步修改

  // 表单来源维度、字段解释 editable 共同控制字段是否可编辑
  let pattern = 'editable'
  const isSubField = fieldInfo.parentAttr || [FieldTypeEnum.subForm].includes(fieldInfo.fieldType)
  const isLinkField = [
    FieldTypeEnum.singleChoiceLink,
    FieldTypeEnum.multipleChoiceLink,
    FieldTypeEnum.relyData,
    FieldTypeEnum.multiRelyData
  ].includes(fieldInfo.fieldType)
  const isProductField = ['product'].includes(fieldInfo.saasAttr)
  if (
    ['design'].includes(otherConfig.mode) || // 设计页
    !(!!fieldInfo.editable && fieldInfo.noEditable !== 1) || // 字段解释控制
    fieldInfo.packageLimit === 1 ||
    fieldInfo.packageLimit === 2
  ) {
    if (otherConfig.mode === 'flow') {
      // 工作流下，子表单内的关联字段需要跳转。关联产品字段不允许跳转
      pattern = isSubField && (!isLinkField || isProductField) ? 'disable' : 'view'
      if (['dataRely', 'formula'].includes(fieldInfo.defaultAttr.defaultType)) {
        pattern = 'disable'
      }
    } else {
      pattern = 'disable'
    }
  }

  if (otherConfig.isSee) pattern = 'view'

  return {
    display,
    pattern,
    validator: generateValidator(fieldInfo),
    customerField: generateCustomerField(fieldInfo, formQuery || {})
  }
}

export const generateFieldStateList = function (fieldList, otherConfig, formQuery) {
  const state = {}

  fieldList.forEach((fieldInfo) => {
    if (fieldInfo.isOpen !== 1) return

    state[fieldInfo.attr] = generateBaseFieldState(fieldInfo, otherConfig, formQuery)
    // TODO：子表单的判断逻辑需优化，不应该通过fieldType来判断
    if (
      [10006, 20004, 20003, 20014, 20034, 20007].includes(fieldInfo.fieldType) &&
      fieldInfo.isOpen === 1
    ) {
      fieldInfo.subForm.items.forEach((subFieldInfo) => {
        subFieldInfo.parentAttr = fieldInfo.attr
        state[`${subFieldInfo.parentAttr}.${subFieldInfo.attr}`] = generateBaseFieldState(
          subFieldInfo,
          otherConfig,
          formQuery
        )
      })
    }
  })
  return state
}

// * showType
// * 0 全部显示
// * 1 新建编辑显示
// * 2 列表显示
// * 3 详情显示
// * 4 新建编辑、列表显示
// * 5 新建编辑、详情显示
// * 6 列表、详情显示
// * 7 都不显示
// * 8 仅新建不展示
// * 9 导出可见导入不可见
function componentShowType(showType, otherConfig) {
  if (showType !== undefined) {
    // 设计
    if (otherConfig.mode === 'design') {
      return /^(0)$/.test(showType)
    }
    // 详情查看
    if (otherConfig.isSee && otherConfig.mode !== 'flow') {
      return /^(0|3|5|6|8)$/.test(showType)
    }
    // 新建
    return /^(0|1|4|5)$/.test(showType)
  } else {
    return true
  }
}
function generateFieldSuffix(schema, fieldInfo) {
  const FieldAttrList = schema
    .filter((item) => item.attrType === fieldInfo.attrType)
    .map((item) => item.attr)
  let name
  for (let i = 1; ; i++) {
    name = `${fieldInfo.attrType}_${i}`
    if (!FieldAttrList.includes(name)) {
      return i
    }
  }
}

export function generateComponentFieldData({ baseComponent, form, subForm }) {
  const otherConfig = reactive({
    assignmentRules: 0,
    mode: subForm ? 'sub' : 'base', // sub(子表单)、base(默认值)、preview(预览)、edit(编辑页)、add(新建)、newVersion、design(设计页)、approval(审批页)
    isSee: false
  })
  const fieldInfo = xbb.deepClone(addWeightAttr(baseComponent))
  const fieldSuffix = subForm
    ? generateFieldSuffix(subForm.fieldInfo.subForm.items, fieldInfo)
    : generateFieldSuffix(form.schema, fieldInfo)
  fieldInfo['attr'] = `${fieldInfo.attrType}_${fieldSuffix}`
  fieldInfo['attrName'] = `${fieldInfo.attrName}_${fieldSuffix}`
  if (subForm) {
    fieldInfo['parentAttr'] = subForm.refKey
  }
  const state = generateBaseFieldState(fieldInfo, otherConfig)
  const config = {
    ...state
  }
  // TODO：下面的代码需要被移除，不要在通用方法中做特殊字段类型的判断，这会增加字段添加和维护的复杂度
  if ([9, 10001].includes(baseComponent.fieldType)) {
    config.value = []
  }
  if ([12].includes(baseComponent.fieldType)) {
    config.value = { city: '', address: '', district: '', province: '' }
  }
  if ([10006].includes(baseComponent.fieldType)) {
    config.value = [{}]
  }
  return { fieldInfo, config }
}
