<template>
  <div class="manager-tab">
    <el-checkbox-group
      v-if="multiple"
      v-model="selectManagerListId"
      class="manager-list__box"
      @change="checkboxChange"
    >
      <template v-for="(item, index) in managerList">
        <el-checkbox
          v-if="item.name.includes(managerName)"
          :key="index"
          class="manager-list__item"
          :label="item.id"
        >
          {{ item.name }}
        </el-checkbox>
      </template>
    </el-checkbox-group>
    <el-radio-group
      v-else
      v-model="selectManagerId"
      class="manager-list__box"
      @change="radioChange"
    >
      <template v-for="(item, index) in managerList">
        <el-radio
          v-if="item.name.includes(managerName)"
          :key="index"
          class="manager-list__item"
          :label="item.id"
        >
          {{ item.name }}
        </el-radio>
      </template>
    </el-radio-group>
  </div>
</template>

<script>
export default {
  name: 'ManagerTab',

  props: {
    // 选中的所有节点数组
    selectValue: {
      type: Array,
      default: () => {}
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    }
  },
  inject: {
    otherParams: {
      default: () => {}
    }
  },

  data() {
    return {
      managerNum: 20,
      topManager: {
        property: 'manager', // 类型 用于区分部门：dept、角色：role、用户：user ,主管 manager
        editable: 1, // 是否可编辑
        id: '-1', // 所选的id
        name: this.$t('person.topExecutives') // 用于名称
      },
      managerList: [],
      selectManagerId: {},
      selectManagerListId: [],
      // 负责人的模糊搜索
      managerName: ''
    }
  },

  computed: {
    // 单选选中的负责人对象
    selectManager() {
      return this.managerList.filter((item) => {
        return String(item.id) === String(this.selectManagerId)
      })[0]
    },
    // 多选选中的负责人数组对象
    selectManagerList() {
      return this.managerList.filter((item) => {
        return this.selectManagerListId.includes(String(item.id))
      })
    }
  },

  watch: {
    selectValue(val) {
      const arr = []
      for (let i = 0; i < val.length; i++) {
        arr.push(String(val[i].id))
      }
      this.selectManagerListId = arr
    }
  },
  created() {
    this.initManagerList()
  },
  mounted() {
    // 负责人回显
    if (this.multiple) {
      this.selectManagerListId = this.selectValue.map((item) => {
        return String(item.id)
      })
    } else {
      this.selectManagerId = this.selectValue.map((item) => {
        return String(item.id)
      })[0]
    }
  },

  methods: {
    initManagerList() {
      this.managerList = []
      for (let i = 1, len = this.managerNum; i <= len; i++) {
        this.managerList.push({
          property: 'manager', // 类型 用于区分部门：dept、角色：role、用户：user ,主管 manager
          editable: 1, // 是否可编辑
          id: '' + i, // 所选的id
          name: `${this.$t('person.levelSupervisor', { attr: i })}` // 用于名称
        })
      }
      this.managerList.push({
        property: 'manager', // 类型 用于区分部门：dept、角色：role、用户：user ,主管 manager
        editable: 1, // 是否可编辑
        id: '-1', // 所选的id
        name: this.$t('person.topExecutives') // 用于名称
      })
    },
    // 成员多选点击
    checkboxChange() {
      // 选中的所有节点
      const arr = this.selectValue
      // 之前所展示的选中的负责人
      const managerArrId = arr.map((item) => {
        return String(item.id)
      })

      // 目前被操作的框
      const differentArrId = managerArrId.concat(this.selectManagerListId).filter((val, i, arr) => {
        return arr.indexOf(val) === arr.lastIndexOf(val)
      })

      // 之前展示的负责人是否包含该节点
      const index = arr.findIndex((item) => {
        return String(item.id) === String(differentArrId[0])
      })
      // 取消选中一个负责人
      if (index !== -1) {
        arr.splice(index, 1)
      } else {
        // 勾选一个负责人
        const differentArrObj = this.selectManagerList.filter((item) => {
          return String(item.id) === String(differentArrId[0])
        })
        arr.push(differentArrObj[0])
      }
      this.$emit('setValueList', 'manager', arr)
    },
    // 成员单选点击
    radioChange() {
      const arr = this.selectValue
      if (this.selectManagerId) {
        arr.push(this.selectManager)
      }
      this.$emit('setValueList', 'manager', arr)
    },
    // 选中的节点删除，手动取消勾选
    cancelCheck(node) {
      if (this.multiple) {
        this.selectManagerListId.splice(this.selectManagerListId.indexOf(String(node.id)), 1)
      } else {
        this.selectManagerId = {}
      }
    },
    // 搜索框变化时执行
    filterList(val) {
      this.managerName = val
    }
  }
}
</script>

<style lang="scss" scoped>
.manager-tab {
  height: 100%;
  overflow-y: scroll;
  .manager-list {
    &__box {
      display: flex;
      flex-direction: column;
    }
    &__item {
      padding: 5px 20px;
      overflow-x: hidden;
      white-space: nowrap;
    }
  }
}
</style>

<style lang="scss">
.manager-tab {
  .manager-list__box {
    .el-radio + .el-radio {
      margin-left: 0px;
    }
    .el-checkbox + .el-checkbox {
      margin-left: 0px;
    }
  }
}
</style>
