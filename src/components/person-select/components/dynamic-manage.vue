<!--
 * @Description: 动态负责人选择
 -->

<template>
  <div v-loading="loading" class="manager-tab">
    <div v-if="!hasDynamicManager" class="no-manager">
      <Empty />
    </div>
    <template v-else>
      <div class="manager-tab-left">
        <div
          v-for="item of formExplainList"
          :key="item.attr"
          class="tab-left-form"
          :class="{ active: activeAttr === item.attr }"
          @click="changeLeftExplain(item.attr)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="manager-tab-right">
        <el-checkbox-group
          v-if="multiple"
          v-model="selectManagerListId"
          class="manager-list__box"
          @change="checkboxChange"
        >
          <template v-for="(item, index) in managerList">
            <el-checkbox
              v-if="item.name.includes(managerName)"
              :key="index"
              class="manager-list__item"
              :label="item.id"
            >
              {{ item.name }}
            </el-checkbox>
          </template>
        </el-checkbox-group>
        <el-radio-group
          v-else
          v-model="selectManagerId"
          class="manager-list__box"
          @change="radioChange"
        >
          <template v-for="(item, index) in managerList">
            <el-radio
              v-if="item.name.includes(managerName)"
              :key="index"
              class="manager-list__item"
              :label="item.id"
            >
              {{ item.name }}
            </el-radio>
          </template>
        </el-radio-group>
      </div>
    </template>
  </div>
</template>

<script>
import { dynamicManager } from '@/api/system'
import { processDynamicManager } from '@/api/work-order-v2'
import Empty from '@/components/base/empty.vue'

export default {
  name: 'ManagerTab',

  components: {
    Empty
  },

  props: {
    // 选中的所有节点数组
    selectValue: {
      type: Array,
      default: () => {}
    },
    // 是否多选
    multiple: {
      type: Boolean
    }
  },
  inject: {
    otherParams: {
      default: () => {}
    }
  },

  data() {
    return {
      managerNum: 20,
      // 负责人的模糊搜索
      managerName: '',
      hasDynamicManager: false,
      formExplainList: [],
      activeAttr: '',
      explainManagers: {},
      explainSelectIds: {},
      explainSelectId: {},
      loading: true
    }
  },

  computed: {
    // 单选选中的负责人对象
    selectManager() {
      return this.managerList.filter((item) => {
        return String(item.id) === String(this.selectManagerId)
      })[0]
    },

    managerList() {
      if (!this.activeAttr) {
        return []
      }
      return this.explainManagers[this.activeAttr] || []
    },

    selectManagerListId: {
      get() {
        if (!this.activeAttr) {
          return []
        }
        return this.explainSelectIds[this.activeAttr] || []
      },
      set(val) {
        if (typeof val !== 'undefined') {
          const attr = this.activeAttr
          this.explainSelectIds[attr] = val
          this.explainSelectIds = JSON.parse(JSON.stringify(this.explainSelectIds))
        }
      }
    },
    selectManagerId: {
      get() {
        if (!this.activeAttr) {
          return ''
        }
        return this.explainSelectId[this.activeAttr] || ''
      },
      set(val) {
        if (typeof val !== 'undefined') {
          const attr = this.activeAttr
          this.explainSelectId = {}
          this.explainSelectId[attr] = val
        }
      }
    }
  },

  watch: {
    selectValue(val) {
      if (this.multiple) {
        const temp = {}
        val.forEach((item) => {
          temp[item.attr] ? temp[item.attr].push(item.id) : (temp[item.attr] = [item.id])
        })
        this.explainSelectIds = temp
      } else {
        const list = val
        if (list.length) {
          this.explainSelectId[list[0].attr] = list[0].id
        } else {
          this.explainSelectId = {}
        }
      }
    }
  },
  created() {
    if (this.otherParams?.dynamicList?.length) {
      this.hasDynamicManager = true
      this.formExplainList = this.otherParams.dynamicList
      this.activeAttr = this.otherParams.dynamicList[0].attr
      this.initManagerList(this.formExplainList, 5)
      this.loading = false
      return
    }
    const requestFn = this.otherParams?.isWorkOrder ? processDynamicManager : dynamicManager
    requestFn(this.otherParams.formQuery).then(({ result: { formExplainList } }) => {
      if (formExplainList && formExplainList.length > 0) {
        this.hasDynamicManager = true
        this.formExplainList = formExplainList
        this.activeAttr = formExplainList[0].attr
        if (this.otherParams.isWorkOrder) {
          this.initWorkOrderManagerList(formExplainList)
        } else {
          this.initManagerList(formExplainList)
        }
      }
      this.loading = false
    })
  },
  mounted() {
    // 负责人回显
    if (this.multiple) {
      this.selectValue.forEach((item) => {
        this.explainSelectIds[item.attr]
          ? this.explainSelectIds[item.attr].push(item.id)
          : (this.explainSelectIds[item.attr] = [item.id])
      })
      this.explainSelectIds = JSON.parse(JSON.stringify(this.explainSelectIds))
    } else {
      if (this.selectValue.length) {
        this.explainSelectId[this.selectValue[0].attr] = this.selectValue[0].id
      }
    }
  },

  methods: {
    initManagerList(formExplainList, length) {
      formExplainList.forEach((item) => {
        const arr = []
        const obj = {
          property: 'dynamicManager',
          editable: 1,
          attr: item.attr,
          attrname: item.name
        }
        let newObj = JSON.parse(JSON.stringify(obj))
        Object.assign(newObj, { id: '0', name: item.name })
        arr.push(newObj)
        for (let i = 1, len = length || this.managerNum; i <= len; i++) {
          newObj = JSON.parse(JSON.stringify(obj))
          Object.assign(newObj, {
            id: `${i}`,
            name: `${this.$t('person.levelSupervisor', { attr: i })}`
          })
          arr.push(newObj)
        }
        newObj = JSON.parse(JSON.stringify(obj))
        Object.assign(newObj, { id: '-1', name: this.$t('person.topExecutives') })
        arr.push(newObj)
        this.explainManagers[item.attr] = arr
      })
    },
    initWorkOrderManagerList(formExplainList) {
      formExplainList.forEach((item) => {
        const arr = []
        const obj = {
          property: 'dynamicManager',
          editable: 1,
          attr: item.attr,
          attrname: item.name
        }
        const newObj = {
          ...obj,
          id: '0',
          name: item.name
        }
        arr.push(newObj)
        this.explainManagers[item.attr] = arr
      })
    },
    changeLeftExplain(attr) {
      this.activeAttr = attr
    },

    // 成员多选点击
    checkboxChange() {
      let managerList = []
      // const notDynamicList = this.selectedTag.filter((item) => {
      //   return item.property !== 'dynamicManager'
      // })
      // managerList = managerList.concat(notDynamicList)
      Object.keys(this.explainSelectIds).forEach((key) => {
        const list = this.explainManagers[key].filter((item) => {
          return this.explainSelectIds[key].includes(String(item.id))
        })
        managerList = managerList.concat(JSON.parse(JSON.stringify(list)))
      })
      managerList.forEach((item) => {
        if (item.id !== '0') {
          item.name = item.attrname + item.name
        }
      })
      this.$emit('setValueList', 'dynamicManager', managerList)
    },

    // 成员单选点击
    radioChange() {
      let arr = []
      if (this.tabMultiple) {
        arr = this.selectedTag.filter((item) => {
          return item.property !== 'dynamicManager'
        })
      }
      if (this.selectManagerId) {
        const item = JSON.parse(JSON.stringify(this.selectManager))
        if (item.id !== '0') {
          item.name = item.attrname + item.name
        }
        arr.push(item)
      }
      this.$emit('setValueList', 'dynamicManager', arr)
    },

    // 搜索框变化时执行
    filterList(val) {
      this.managerName = val
    }
  }
}
</script>

<style lang="scss" scoped>
.manager-tab {
  display: flex;
  height: 100%;
  overflow-x: hidden;
  .no-manager {
    flex: 1;
    padding-top: 10px;
    text-align: center;
  }
  .manager-tab-left {
    box-sizing: border-box;
    flex: 0 0 238px;
    padding: 5px 0;
    .tab-left-form {
      padding: 0 10px;
      overflow: hidden;
      font-size: 14px;
      line-height: 30px;
      color: $text-main;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &.active {
        color: $brand-color-5;
      }
      &:hover {
        background: $brand-color-1;
      }
    }
  }
  .manager-tab-left,
  .manager-tab-right {
    height: 100%;
    overflow-y: auto;
    border-right: 1px solid $neutral-color-3;
  }
  .manager-tab-right {
    flex: 1;
  }
  .manager-list {
    &__box {
      display: flex;
      flex-direction: column;
    }
    &__item {
      padding: 5px 20px;
      overflow-x: hidden;
      white-space: nowrap;
    }
  }
}
</style>

<style lang="scss">
.manager-tab {
  .manager-list__box {
    .el-radio + .el-radio {
      margin-left: 0px;
    }
    .el-checkbox + .el-checkbox {
      margin-left: 0px;
    }
  }
}
</style>
