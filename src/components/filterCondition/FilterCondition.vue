<!-- eslint vue/no-mutating-props: 1 -->

<template>
  <div class="filter-condition">
    <!-- 条件循环 -->
    <div
      v-for="(condition, index) in conditions"
      :key="condition.attr"
      class="condition-block-item"
      :class="{ 'fliter-model-condition-block': filterModel }"
    >
      <div
        v-if="!filterModel"
        class="condition-block-item-btn"
        @click="deleteConditionClick(index)"
      >
        <i class="el-icon-delete"></i>
      </div>
      <div class="condition-block-item-value">
        <FilterConditionFieldVal
          :condition="condition"
          :filter-model="filterModel"
          :query="{
            appId: query.appId, // 应用id
            menuId: query.menuId, // 菜单id
            formId: query.formId,
            saasMark: query.saasMark,
            businessType: query.businessType,
            subAttr: condition.subAttr, // 子表单字段标识
            attr: condition.attr, // 字段标识
            fieldType: condition.fieldType // 字段类型
          }"
          @conditionChange="conditionChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import FilterConditionFieldVal from './FilterConditionFieldVal'

export default {
  components: {
    FilterConditionFieldVal
  },
  // props: ['query', 'conditions'],
  props: {
    filterModel: {
      type: Boolean,
      default: false
    },
    query: {},
    conditions: {}
  },
  data() {
    return {}
  },
  methods: {
    deleteConditionClick(index) {
      this.conditions.splice(index, 1)
    },
    conditionChange(condition, operate) {
      this.$emit('conditionChange', condition, operate)
    }
  }
}
</script>

<style lang="scss">
.filter-condition {
  /* max-height: calc(100% - 240px); */
  .condition-block-item {
    position: relative;
    &:hover {
      background: #f3f6fc;
    }
    padding: 20px 10px;
    .condition-block-item-btn {
      position: absolute;
      top: 20px;
      right: 5px;
      width: 30px;
      height: 30px;
      margin-left: 10px;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      &:hover {
        color: #ff0000;
      }
    }
    .condition-block-item-top {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      width: 100%;
      height: 30px;
      margin-bottom: 5px;
      line-height: 30px;
      .condition-block-item-name {
        flex: 1;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .condition-block-item-where {
        /* flex: 1; */
        width: 145px;
        white-space: nowrap;
      }
    }
    .filter-model-top {
      display: inline-block;
      width: auto;
      .filter-model-name {
        display: inline-block;
      }
      .filter-model-item {
        display: inline-block;
        width: auto;
      }
    }
  }
  .filter-model-top {
    display: inline-block;
    width: auto;
    .filter-model-name {
      display: inline-block;
    }
    .filter-model-item {
      display: inline-block;
      .dropdown-hover {
        height: 30px;
        // border-right: 0;
        padding: 0px 6px;
        line-height: 30px;
        border: 1px solid $neutral-color-3;
      }
    }
  }
  .condition-block-item-value {
    padding-right: 30px;
  }
  .fliter-model-condition-block {
    display: inline-block;
    padding: 10px;
    .condition-block-item-value {
      padding-right: 0px;
    }
  }
}
</style>
