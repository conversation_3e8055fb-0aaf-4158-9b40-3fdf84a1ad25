<!--
 * @Description: 授权登录
 -->

<template>
  <el-dialog
    :before-close="handleClose"
    class="authorized-login"
    :title="$t('loginAuthorization.title')"
    :visible.sync="dialogVisible"
    width="620px"
  >
    <div class="authorized-login__notice">
      <span>{{ $t('message.confirmTitle') + ':' }}</span>
      <p>{{ $t('loginAuthorization.tip1') }}</p>
      <p>{{ $t('loginAuthorization.tip2') }}</p>
      <p>{{ $t('loginAuthorization.tip3') }}</p>
    </div>
    <el-form ref="form" label-position="right" label-width="150px" :model="formData" :rules="rules">
      <el-form-item :label="$t('loginAuthorization.isAllowNewData') + ':'" prop="addData">
        <el-radio-group v-model="formData.addData">
          <el-radio :label="1">{{ $t('loginAuthorization.allow') }}</el-radio>
          <el-radio :label="0">{{ $t('loginAuthorization.forbid') }}</el-radio>
        </el-radio-group>
        <p class="authorized-login__tip">{{ $t('loginAuthorization.createTip') }}</p>
      </el-form-item>
      <el-form-item :label="$t('loginAuthorization.overview') + ':'" prop="problemDescription">
        <el-input
          v-model="formData.problemDescription"
          :placeholder="$t('loginAuthorization.overviewPlaceholder')"
          type="textarea"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item :label="$t('label.attachments') + ':'">
        <FileUpload v-model="formData.attachmentList" :field-info="fileUploadFieldInfo" />
      </el-form-item> -->
    </el-form>
    <span slot="footer">
      <el-button @click="handleClose">{{ $t('operation.cancel') }} </el-button>
      <el-button :loading="loading" type="primary" @click="submit">{{
        $t('operation.confirm')
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import store from '@/store'
import { formatParams } from '@/axios'
import { saveAuthorizationLogin } from '@/api/help'
import FileUpload from '@/components/all-fields/form-data-edit/FileUpload/FileUpload'

export default {
  name: 'FeedbackBusiness',

  components: {
    FileUpload
  },
  data() {
    return {
      dialogVisible: true,
      formData: {
        addData: undefined, //
        problemDescription: '' // 问题概述
        // attachmentList: [] // 附件
      },
      rules: {
        addData: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', {
              attr: this.$t('loginAuthorization.isAllowNewData')
            })
          }
        ]
      },
      loading: false,
      fileUploadFieldInfo: {
        editable: 1
      }
    }
  },

  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          this.saveAuthorizationLogin()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveAuthorizationLogin() {
      const param = {
        userId: localStorage.userId,
        corpid: localStorage.corpid,
        token: localStorage.xbbAccessToken,
        clientInfo: navigator.userAgent,
        corpName: xbb._get(store, 'state.user.companyName.corpName', ''),
        userName: xbb._get(store, 'state.user.userInfo.name', ''),
        ...this.formData
      }
      // btoa不支持中文转码, 需要编码
      const data = window.btoa(unescape(encodeURIComponent(formatParams(param).trim())))
      saveAuthorizationLogin({
        data
      })
        .then((data) => {
          this.$message.success(this.$t('message.saveSuccess'))
          this.handleClose()
        })
        .catch(() => {})
        .then((data) => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.authorized-login {
  &__notice {
    padding: 20px;
    margin-bottom: 10px;
    font-size: 12px;
    line-height: 20px;
    background: #f0f4fc;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 35px;
  }

  &__notice > span {
    font-size: 14px;
    font-weight: 600;
  }
  &__tip {
    font-size: 12px;
    line-height: 20px;
    color: #9ea0a3;
  }
}
</style>
