<!--
 * @Description: 首页页面顶部菜单
 -->

<template>
  <div class="header-view">
    <div class="header-view-left">
      <!-- 公司信息 -->
      <div v-if="corpInfoShow" class="corp-info">
        <!-- 公司logo -->
        <!-- 有公司icon -->
        <div v-if="companyIcon" class="company-icon">
          <img :src="companyIcon" />
        </div>
        <!-- 没有公司icon -->
        <div v-else class="corp-abbreviation" :class="{ 'is-stand-alone': isStandAlone }">
          {{ companyName.charAt(0) }}
        </div>
        <!-- 公司名称 -->
        <el-tooltip class="item" :content="companyName" effect="dark" placement="bottom">
          <router-link class="corp-name" to="/">
            {{ companyName }}
          </router-link>
        </el-tooltip>
        <!-- 公司套餐图标 -->
        <div class="fee-type">
          <!-- 旗舰版 -->
          <div v-if="feeType === 3 && !isGray">
            <el-tooltip
              class="item"
              :content="$t('nouns.ultimate')"
              effect="dark"
              placement="bottom"
            >
              <i class="icon-qijianban iconfont version"></i>
            </el-tooltip>
          </div>
          <!-- 高级版 -->
          <div v-else-if="feeType === 1">
            <el-tooltip
              class="item"
              :content="$t('homeHeaderView.advancedVersion')"
              effect="dark"
              placement="bottom"
            >
              <i class="icon-gaojiban iconfont version"></i>
            </el-tooltip>
          </div>
          <!-- 灰度版 -->
          <div v-else-if="isGray">
            <el-tooltip
              class="item"
              :content="$t('homeHeaderView.garyVersion')"
              effect="dark"
              placement="bottom"
            >
              <i class="icon-yunying1 iconfont version"></i>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div v-if="isShowOpen">
        <el-tooltip
          class="item"
          :content="$t('appLayout.openBrowser')"
          effect="dark"
          placement="bottom"
        >
          <span class="header-view-open-browser" @click="openBrowser">
            <i class="icon-global-line t-iconfont"></i>
            <!-- {{ $t('appLayout.openBrowser') }} -->
          </span>
        </el-tooltip>
      </div>
    </div>
    <div class="header-view-center" :class="headerRightWidth" :style="{ right: headerRight }">
      <div class="header-view-menu">
        <router-link
          v-for="item in topCenterList"
          :id="item.alias === 'chartCenter' ? 'step-2' : ''"
          :key="item.alias"
          class="header-view-menu-item"
          :class="{ active: item.alias === activeAlias }"
          :to="item.url"
          @click.native="menuClick(item)"
        >
          <span class="icon-block"><i class="center-icon t-iconfont" :class="item.icon"></i></span>
          <span>{{ item.name }}</span>
          <span
            v-if="item.count"
            class="is-small"
            :class="isEnglish ? 'menu-count-us' : 'menu-count'"
          >
            {{ item.count | handleCount }}
          </span>
          <span v-if="getIsNew && item.alias === 'templateCenter'" class="new-icon-wrapper">
            <i class="icon-new-left iconfont new-module newbiaoqian"></i>
            <b>NEW</b>
          </span>
        </router-link>
      </div>
    </div>
    <div class="header-view-right">
      <span
        v-if="aiInsideWebUrl"
        v-tooltip.bottom.delay="{ content: 'AI销售助手', delayTime: 500 }"
        class="header-ai-menu header-right-menu"
        @click="openAITab"
      >
        <img alt="" height="20px" :src="aiLogo" width="20px" />
        <span
          v-if="messageCount"
          class="is-small"
          :class="isEnglish ? 'menu-count-us' : 'menu-count'"
        >
          {{ messageCount | handleCount }}
        </span>
      </span>

      <template v-for="item in topRightList">
        <span
          v-if="item.alias === 'help'"
          :key="item.alias"
          v-tooltip.bottom.delay="{ content: item.name, delayTime: 500 }"
          class="header-right-menu"
          :class="{
            'mr-40': liveStreamingInfo.liveStatus === 'living',
            'mr-45': liveStreamingInfo.liveStatus === 'futureLive'
          }"
        >
          <el-popover
            id="step-4"
            :key="item.alias"
            ref="helpCenter"
            placement="bottom"
            popper-class="header-popover"
            trigger="click"
            @show="helpPopoverShow"
          >
            <div class="header-help-icon">
              <ul class="header-help-formlist">
                <template v-for="(item, index) in helpListFilter">
                  <li
                    v-if="item.isShow"
                    :key="index"
                    class="header-help-formlist-item"
                    @click="openHelpCenter(item)"
                  >
                    <span class="header-help-img">
                      <img v-lazy="item.img" alt="" />
                    </span>
                    <p class="item-name">{{ item.name }}</p>
                  </li>
                </template>
                <!-- 精选直播 -->
                <!-- <li class="header-help-formlist-item" @click="openLiveStreamingDialog">
                  <span class="header-help-img">
                    <img v-lazy="require('../../assets/live-streaming.png')" alt="" />
                  </span>
                  <p class="item-name">{{ liveStatusText }}</p>
                  <span v-if="liveTopic" class="menu-count">{{ liveTopic }}</span>
                </li> -->
                <li class="header-help-formlist-item" @click="goWorthStrategy">
                  <span class="header-help-img header-help-img--circle">
                    <el-progress
                      :color="'#FF8C2E'"
                      :percentage="scorePercentage"
                      :show-text="false"
                      type="circle"
                      :width="32"
                    ></el-progress>
                    <el-tooltip
                      class="item"
                      :content="String(companyScore.score)"
                      effect="dark"
                      placement="top-start"
                    >
                      <div class="header-help-img__text">{{ companyScore.score }}</div>
                    </el-tooltip>
                  </span>
                  <p class="item-name">{{ $t('business.digitalStrategy') }}</p>
                </li>
                <!-- 授权登录 -->
                <li class="header-help-formlist-item" @click="isShowAuthorizationLogin = true">
                  <span class="header-help-img">
                    <img v-lazy="require('../../assets/authorization.png')" alt="" />
                  </span>
                  <p class="item-name">{{ $t('loginAuthorization.title') }}</p>
                </li>
              </ul>
              <div class="header-help-foot">
                <el-button
                  v-if="isAddPerson"
                  class="user"
                  icon="web-iconfont web-icon-field-user"
                  type="text"
                  @click="showUser"
                  >{{ $t('homeHeaderView.inviteStaffTry') }}</el-button
                >
                <el-button
                  icon="el-icon-edit-outline"
                  type="text"
                  @click="isShowFeedbackBusiness = true"
                  >{{ $t('nouns.feedback') }}</el-button
                >
              </div>
            </div>
            <div slot="reference">
              <i class="t-iconfont" :class="item.icon"></i>
              <!-- 顶部角标文字- 直播中/直播预告展示 -->
              <span
                v-if="liveStreamingInfo.liveStatus !== 'historyLive'"
                class="is-small menu-count"
                >{{ liveStatusText }}</span
              >
              <!-- <span v-if="liveStreamingInfo.liveStatus === 'futureLive'" class="is-small menu-count">{{ liveStatusText }}</span> -->
            </div>
          </el-popover>
        </span>

        <span
          v-else-if="item.alias === 'message'"
          :key="item.alias"
          v-tooltip.bottom.delay="{ content: item.name, delayTime: 500 }"
          class="header-right-menu"
          :class="{ active: isNotification }"
          @click="menuClick(item)"
        >
          <i class="t-iconfont" :class="item.icon"></i>
          <span
            v-if="item.count && String(item.count) !== '0'"
            class="is-small"
            :class="isEnglish ? 'menu-count-us' : 'menu-count'"
          >
            {{ item.count | handleCount }}
          </span>
        </span>
        <template v-else>
          <span
            v-if="item.alias === 'todo'"
            :key="item.alias"
            v-tooltip.bottom.delay="{ content: item.name, delayTime: 500 }"
            class="header-right-menu"
            :class="{ active: item.alias === activeAlias }"
            @click="menuClick(item)"
          >
            <span>{{ item.name }}</span>
            <span
              v-if="item.count"
              class="is-small"
              :class="isEnglish ? 'menu-count-us' : 'menu-count'"
            >
              {{ item.count | handleCount }}
            </span>
          </span>
          <router-link
            v-else-if="item.alias !== 'templateCenter' || getCompanyLicenseType !== 5"
            :id="item.alias === 'workFlow' ? 'step-3' : ''"
            :key="item.alias"
            class="header-right-menu"
            :class="{ active: item.alias === activeAlias }"
            :to="item.url"
            @click.native="menuClick(item)"
          >
            <i
              v-if="item.icon"
              v-tooltip.bottom.delay="{ content: item.name, delayTime: 500 }"
              class="t-iconfont"
              :class="item.icon"
            ></i>
            <span v-else v-tooltip.bottom.delay="{ content: item.name, delayTime: 500 }">{{
              item.name
            }}</span>
            <span v-if="Number(item.count)" :class="isEnglish ? 'menu-count-us' : 'menu-count'">
              {{ item.count | handleCount }}
            </span>
          </router-link>
        </template>
      </template>

      <div class="header-personalCenter">
        <el-popover
          ref=""
          placement="bottom"
          popper-class="personalCenter-popover"
          trigger="click"
          width="250"
          @show.once="getPackageInfo"
        >
          <div
            v-loading="informationLoading"
            class="personalCenter-information"
            :class="{ 'personalCenter-information-en': localLang === 'en_US' }"
          >
            <!-- 企业微信-账号身份(基础账号/互通账号) -->
            <div v-if="qwxAccountLicense.length" class="system-qwx-identity">
              <el-tag v-for="license in qwxAccountLicense" :key="license.name">{{
                license.name
              }}</el-tag>
            </div>
            <!--  -->
            <div
              v-if="isAdminOrBoss"
              class="systemIndex"
              @click="jumpPersonalCenter('personalIndex')"
            >
              <h2 class="name">
                {{ $t('homeHeaderView.systemUsage') }} <span>{{ health }}</span>
              </h2>
              <p>
                {{ $t('homeHeaderView.compareWith')
                }}<span
                  class="web-iconfont"
                  :class="[isRise ? 'web-icon-xiangshang' : 'web-icon-xiangxia']"
                ></span
                ><span :class="[isRise ? 'rise' : 'decline']">{{ difference }}</span>
              </p>
            </div>
            <div
              v-if="isAdminOrBoss"
              class="systemCapacity"
              @click="jumpPersonalCenter('capacity')"
            >
              <h2 class="title">{{ feeName }}</h2>
              <div class="personalCenter-functions">
                <div
                  v-for="(item, index) in personalCenterData.systemCapacityList"
                  :key="index"
                  class="personalCenter-functions-item"
                >
                  <span>{{ item.name }}</span>
                  <el-progress
                    :color="item.color"
                    :percentage="item.percentage"
                    :show-text="false"
                    :stroke-width="8"
                  ></el-progress>
                  <span class="functions-item-value">{{ item.usedValueStr }}</span>
                  <!-- 只在数据量后出现提示tips -->
                  <span v-if="item.type == 11" class="functions-item-tips">
                    <el-tooltip
                      class="item"
                      :content="$t('homeHeaderView.includeData')"
                      effect="dark"
                      placement="top"
                    >
                      <span v-popover:popover class="el-icon-question icon"></span>
                    </el-tooltip>
                  </span>
                </div>
              </div>
            </div>
            <!-- 搜客套餐信息 -->
            <div v-if="souke.total" class="system-souke">
              <h2 class="title">{{ souke.title }}</h2>
              <span class="souke-use"
                ><span>{{ souke.remain }}</span
                >/{{ souke.total }}</span
              >
            </div>
            <div v-if="isAppEnv && signOutHide" class="crtCompany" @click="crtCompany">
              {{ $t('standLogin.switchCompany') }}
            </div>
            <div v-if="isMainAdmin && signOutHide" class="crtCompany" @click="dissolutionCompany">
              {{ $t('homeHeaderView.dissolutionCompany') }}
            </div>
            <div
              v-if="signOutHide"
              class="account-setting"
              @click="jumpPersonalCenter('accountSetting')"
            >
              {{ $t('homeHeaderView.accountSetting') }}
            </div>
            <template v-if="multiLang">
              <div class="switch-language" @click="dialogLanguage = true">
                {{ $t('system.changeLanguage') }}
              </div>
              <div v-if="isProd" class="switch-language">
                {{ $t('system.changeServer') }}
                <el-switch
                  v-model="customGateway"
                  class="switch-language__button"
                  @change="handleGatewayChange"
                ></el-switch>
              </div>
            </template>
            <div v-if="isDeveloper" class="switch-language" @click="handleChangeDeveloperEnv">
              {{ isDeveloperStatus ? $t('lowCode.useReleaseJsFile') : $t('lowCode.useDevJsFile') }}
            </div>
            <div class="switch-language" @click="showPreferences = true">偏好设置</div>
            <div
              v-if="!(isStandAlone && xbb.isThirdPC(['wx'], false) && wxIsUnbindApp) && signOutHide"
              class="signOut"
              @click="signOutClick"
            >
              {{ $t('homeHeaderView.singOut') }}
            </div>
          </div>
          <div id="list-guide-step-2" slot="reference" class="header-personalCenter-right">
            <avatar-img :alt="userInfo.name" :size="28" :src="userInfo.avatar" type="square" />
            <span class="username" v-html="addNodeByWX(userInfo.name)"></span>
            <span class="icon-arrow-down-s-line t-iconfont"></span>
          </div>
        </el-popover>
      </div>
    </div>
    <invite-staff v-if="isShowInviteStaff" @close="isShowInviteStaff = false"></invite-staff>
    <feedback-business
      v-if="isShowFeedbackBusiness"
      @click="isShowFeedbackBusiness = false"
    ></feedback-business>
    <authorized-login v-if="isShowAuthorizationLogin" @close="isShowAuthorizationLogin = false" />
    <!-- 是否消息弹框 -->
    <!-- <notification v-if="isNotification" :show.sync="isNotification"></notification> -->
    <MessageDelivery v-if="isNotification"></MessageDelivery>
    <el-dialog :title="$t('system.changeLanguage')" :visible.sync="dialogLanguage" width="30%">
      <el-select
        v-model="language"
        :placeholder="$t('placeholder.choosePls', { attr: '' })"
        style="width: 100%"
      >
        <el-option
          v-for="item in languageOption"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogLanguage = false">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="changeLanguage(language)">{{
          $t('operation.confirm')
        }}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="$t('homeHeaderView.contactCustomerservice')"
      :visible.sync="showPhone"
      width="30%"
    >
      <div class="title__tel">
        <p>{{ $t('personalCenter.index.customerServiceHotline') }}</p>
        <h2>0571-28834699</h2>
      </div>
    </el-dialog>
    <el-dialog
      class="new-style-dialog person-dialog-mode"
      title="个人偏好设置"
      :visible.sync="showPreferences"
      width="480px"
    >
      <div class="person-preferences">
        <div class="main">{{ $t('system.detailOpenMethod') }}</div>
        <div class="desc">{{ $t('system.detailOpenMethodTips') }}</div>
        <div class="detail-dialog-type">
          <div class="detail-dialog-type-item" @click.stop="detailOpenMethod = '1'">
            <div class="dialog-type-drawer" />
            <div
              class="detail-dialog-type-item-radio"
              :class="{ active: detailOpenMethod === '1' }"
            >
              <el-radio v-model="detailOpenMethod" label="1">{{
                $t('system.detailOpenMethodDrawer')
              }}</el-radio>
            </div>
          </div>
          <div class="detail-dialog-type-item" @click.stop="detailOpenMethod = '2'">
            <div class="dialog-type-dialog" />
            <div
              class="detail-dialog-type-item-radio"
              :class="{ active: detailOpenMethod === '2' }"
            >
              <el-radio v-model="detailOpenMethod" label="2">{{
                $t('system.detailOpenMethodDialog')
              }}</el-radio>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelChangeDialogType">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="changeDialogType">{{
          $t('operation.confirm')
        }}</el-button>
      </span>
    </el-dialog>

    <dissolutionCompany :dialog-show.sync="dtCompanyDialogShow"></dissolutionCompany>
    <AIDrawer
      v-if="aiInsideWebUrl"
      :params="{
        width: '400px',
        appLink: aiInsideWebUrl
      }"
    ></AIDrawer>
    <span class="header-view__flag" :class="{ hover: flag }" :title="flag"></span>
  </div>
</template>

<script>
import { getMenuTopInfo } from '@/api/menu'
import { userLogOut } from '@/api/stand-alone'
import { guideLabelIsShow, getHealthByDate, getPackageSystem } from '@/api/help'
import { getCompanyScore, getStrategyState } from '@/api/worth-strategy.js'
import { soukeUseInfo } from '@/api/souke'
import { sendWXMessage } from '@/api/wx'
import { setLangPack, getLiveStreamingInfo, liveEventTracking } from '@/api/system'
import { userConfigSave } from '@/api/user'
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import inviteStaff from './components/help/invite-staff'
import feedbackBusiness from './components/help/feedback-business'
import AuthorizedLogin from './components/help/authorized-login'
// import notification from '@/views/notification'
import avatarImg from '@/components/base/avatar-img'
import dissolutionCompany from '@/components/layout/dissolutionCompany.vue'

import xbb from '@xbb/xbb-utils'
import aiLogo from '@/assets/ai-logo.svg'
// import { getTodoList } from '@/api/work-order-v2/common.js'
// import { gioSystemMenu, gioClearUserId } from '@/utils/buriedPoint.js'
// enum
import {
  ENUM_LOGIN_COMPANY_TYPE,
  ENUM_WX_ACCOUNT_TYPE
} from '@/views/scrm/constants/enum.license.js'
import AIDrawer from '@/components/cloudcode/components/drawer/ai-drawer.vue'

const menuAliasMap = {
  '/app/home': 'home',
  '/appModule/index': 'templateCenter',
  '/chartCenter': 'chartCenter',
  '/app/flowApprove': 'workFlow',
  '/Setting': 'controlCenter',
  'todo/list': 'todo'
}

export default {
  name: 'HeaderView',

  components: {
    AuthorizedLogin,
    InviteStaff: inviteStaff,
    FeedbackBusiness: feedbackBusiness,
    DissolutionCompany: dissolutionCompany,
    // Notification: notification,
    MessageDelivery: () => import('@/views/message-delivery/index.vue'),
    AvatarImg: avatarImg,
    AIDrawer
  },

  filters: {
    handleCount(val) {
      if (val <= 99) {
        return val
      } else {
        return '99+'
      }
    }
  },
  data() {
    return {
      xbb,
      isWxPC: xbb.isThirdPC(['wx'], false),
      isLarkPC: xbb.isThirdPC(['lark'], false),
      isStandAlone: false, // 是否独立上架
      isDingTalk: xbb.isDingTalk(),
      topCenterList: [],
      topRightList: [],
      showClose: false, // 是否显示关闭上手攻略的按钮
      isShowInviteStaff: false, // 是否显示邀请员工
      isShowFeedbackBusiness: false, // 是否显示反馈意见
      isShowAuthorizationLogin: false, // 是否展示授权登录
      health: 0, // 指数
      // 搜客
      souke: {
        total: 0,
        remain: 0,
        title: ''
      },
      difference: '', // 指数的比较值
      personalCenterData: {}, // 个人中心展示数据
      isRise: '',
      // corpName: '', // 公司名
      userInfo: {},
      activeAlias: '',
      isAdminOrBoss: '', // 权限判断
      companyScore: {}, // 公司积分
      scorePercentage: 0, // 公司当前积分比例
      strategyShow: 0, // 价值攻略是否展示
      language: utils.LS.get('VUE_LANG'), // 中英文选择
      dialogLanguage: false,
      languageOption: [
        {
          label: this.$t('system.Chinese'),
          value: 'zh_CN'
        },
        {
          label: this.$t('system.English'),
          value: 'en_US'
        }
      ],
      headerRight: '50%',
      // 当前环境是否支持多语言切换
      multiLang: false,
      dtCompanyDialogShow: false,
      isDeveloperStatus: false, // 是否是开发环境状态
      customGateway: !!utils.LS.get('customGateway'),
      isProd: window.location.hostname === 'pfweb.xbongbong.com',
      // liveThemeName: '精选直播',
      isLiveStreaming: false, // 是否正在直播
      liveTopic: '', // 直播主题
      liveLink: '',
      informationLoading: false,
      showPreferences: false, // 偏好设置
      detailOpenMethod: '1', // 详情页弹窗类型
      aiMessagesCount: 9,
      aiIntervalId: null,
      aiInsideWebUrl: null,
      signOutHide: false,
      showPhone: false
    }
  },

  computed: {
    // 企微是否未绑定自建应用
    wxIsUnbindApp() {
      return utils.SS.get('wxIsBindApp') === 'false'
    },
    // 企微开启邀请员工
    isAddPerson() {
      return +utils.SS.get('authMode') && this.isWxPC
    },
    isAppEnv() {
      return xbb.isThirdPC(['h5'], true) && this.userInfo.mobile !== '13000000000'
    },
    isShowOpen() {
      return this.isDingTalk || this.isWxPC || this.isLarkPC
    },
    aiLogo() {
      return aiLogo
    },
    liveStatusText() {
      switch (this.liveStreamingInfo.liveStatus) {
        case 'living':
          return '直播中'
        case 'futureLive':
          return '直播预告'
        case 'historyLive':
          return '精选直播'
        default:
          return '精选直播'
      }
    },
    // header距离右侧的距离
    // 图表中心和模板中心是上下布局 管理中心和首页是左右布局 且管理中心和首页会根据左侧菜单是否固定改变header的宽度
    headerRightWidth() {
      // debugger
      if (this.corpInfoShow) {
        // 是否是模版中心 图表中心
        return 'chart-right'
      } else {
        console.log('this.$store.state.sidemenuStatus', this.$store.state.sidemenuStatus)
        // return utils.LS.get('VUE-sidemenu-status') === 'false' ? 'home-right' : 'home-fix-right' // 菜单是否固定
        return this.$store.state.sidemenuStatus ? 'home-fix-right' : 'home-right'
      }
    },
    // 是否是模版中心 图表中心
    corpInfoShow() {
      return this.$route.path === '/appModule/index' || this.$route.path === '/chartCenter'
    },
    // 是否显示公测标识
    betaInfo() {
      if (this.getBetaInfo === 'beta') {
        return 'BETA'
      }
      return ''
    },
    ...mapGetters([
      'getUserInfo',
      'getIndexTopList',
      'refreshUnreadCount',
      'feeType',
      'feeTypeName',
      'feeName',
      'isProfessionalEdition',
      'companyName',
      'adminOrBoss',
      'getIsNew',
      'getBetaInfo',
      'getGuideStep',
      'isDeveloper',
      'getCompanyLicenseType',
      'isGray',
      'companyIcon',
      'messageCount'
    ]),
    ...mapState({
      liveStreamingInfo: (state) => state.user.liveStreamingInfo
    }),
    detailDialogShow() {
      return this.$store.state.formDetailDialog.detailDialogShow
    },
    isNotification() {
      return this.$store.state.isNotification
    },

    // 当前语种
    localLang() {
      return utils.LS.get('VUE_LANG')
    },

    isMainAdmin() {
      return this.$store.state.user.mainAdmin
    },

    isEnglish() {
      return utils.LS.get('VUE_LANG') === 'en_US'
    },
    helpList() {
      return [
        {
          img: require('../../assets/help-center.png'),
          name: this.$t('homeHeaderView.helpDocument'),
          url: this.isStandAlone
            ? 'https://help.xbongbong.com/?p=19479'
            : 'https://qwhelp.xbongbong.com/',
          isShow: true
        },
        {
          img: require('../../assets/operation-manual.png'),
          name: this.$t('homeHeaderView.commonProblem'),
          url: 'https://help.antcloud.com.cn/help.htm?tntInstId=UQMROWCN&helpCode=SCE_00016057',
          isShow: true
        },
        {
          img: require('../../assets/video-tutorial.png'),
          name: this.$t('homeHeaderView.videoTutorial'),
          url: this.isStandAlone
            ? 'https://help.xbongbong.com/?p=20823'
            : 'https://qwhelp.xbongbong.com/?p=311',
          isShow: true
        },
        {
          img: require('../../assets/industry-case.png'),
          name: this.$t('homeHeaderView.excellentCourse'),
          url: 'https://qwhelp.xbongbong.com/?p=8803',
          isShow: true
        },
        {
          img: require('../../assets/update-log.png'),
          name: this.$t('homeHeaderView.updateLog'),
          url: 'https://help.xbongbong.com/?p=9386',
          isShow: true
        },
        {
          img: require('../../assets/referrer.png'),
          name: this.$t('homeHeaderView.referrer'),
          url: 'https://appwebfront.xbongbong.com/outer-form.html#/25a3456c9f8dadd6a11c1281db556996s1',
          ignore: ['wx'],
          isShow: false
        },
        {
          img: require('../../assets/smart-service.png'),
          name: this.$t('homeHeaderView.smartService'),
          url: 'https://work.weixin.qq.com/kfid/kfcc935cc27b73cbb47',
          isShow: !this.$store.state.user.feeCompany.isFree && !this.isStandAlone
        }
      ]
    },
    // 部分平台不显示某些菜单
    helpListFilter() {
      const currentEnv = xbb.isThirdPC(['wx', 'dd', 'h5', 'lark'], true)
      return this.helpList.filter((item) => {
        if (!item.ignore) return true
        return item.ignore.indexOf(currentEnv) === -1
      })
    },

    // 个人信息---企业微信-账号身份(基础账号/互通账号)
    qwxAccountLicense() {
      // 非企微公司length = 0
      if (this.getCompanyLicenseType !== ENUM_LOGIN_COMPANY_TYPE.THIRD_WEXIN_SOURCE.code) return []
      // 企微公司、当前登录人license
      const enumTag = {
        [ENUM_WX_ACCOUNT_TYPE.BASE.code]: { type: 'primary', name: ENUM_WX_ACCOUNT_TYPE.BASE.name },
        [ENUM_WX_ACCOUNT_TYPE.EACH.code]: { type: 'success', name: ENUM_WX_ACCOUNT_TYPE.EACH.name }
      }
      return Array.isArray(this.getUserInfo.licenseCodeType)
        ? this.getUserInfo.licenseCodeType.map((item) => enumTag[item])
        : []
    },
    // 用于展示特殊表示
    flag() {
      if (this.$store.state.user.useNuwa) {
        return 'nuwa'
      }
      return ''
    }
  },

  watch: {
    '$route.path': {
      immediate: true,
      handler(value) {
        // this.SET_NOTIFICATION(false)
        this.activeAlias = this.getMenuAlias(value)
      }
    },

    refreshUnreadCount: {
      handler(val) {
        if (val) {
          this.getMenuTopInfo(true)
          this.SET_REFRESH_UNREAD_COUNT(false)
        }
      },
      immediate: true
    },

    // 新手指导到第四步的时候打开帮助的popover
    getGuideStep: {
      handler(val) {
        if (val === 4) {
          console.log(this.$refs.helpCenter[0])
          setTimeout(() => {
            this.$refs.helpCenter[0].doShow()
          }, 0)
        }
      }
    }
  },

  mounted() {
    // 用户信息
    this.userInfo = this.getUserInfo

    this.activeUrl = this.$route.path
    this.aiInsideWebUrl = localStorage.getItem('aiInsideWebUrl')

    this.getMenuTopInfo()
    // 公司名
    // this.corpName = this.companyName

    // 是否管理员
    this.isAdminOrBoss = this.adminOrBoss

    // 是否是开发者状态
    this.isDeveloperStatus = localStorage.getItem('js-file-status') === 'dev'

    if (this.getIndexTopList) {
      this.topCenterList = this.getIndexTopList.topCenterList
      this.topRightList = this.getIndexTopList.topRightList
    }
    this.$nextTick(() => {
      this.headResize()
    })
    // 服务云独立上架
    if (+localStorage.getItem('systemKind') === 2) {
      this.isStandAlone = true
    }
    window.addEventListener('resize', this.headResize)

    this.initLiveStreamingTip()
    console.log(
      '%c [ this.$store.state ] 🐱-709',
      'font-size:13px; background:pink; color:#bf2c9f;',
      this.$store.state
    )

    this.detailOpenMethod = this.$store.state.user.detailOpenMethod
    if (this.aiInsideWebUrl && this.aiInsideWebUrl.startsWith('http')) {
      this.getMessageCount()
      this.aiIntervalId = window.setInterval(() => {
        // 每隔一分钟拉取消息
        this.getMessageCount()
      }, 1000 * 60)
      document.addEventListener('visibilitychange', this.getMessageCount)
    }
  },
  beforeDestroy() {
    utils.LS.remove('crtCompany')
    window.removeEventListener('resize', this.headResize)
    document.removeEventListener('visibilitychange', this.getMessageCount)
    if (this.aiIntervalId) {
      clearInterval(this.aiIntervalId)
    }
  },

  methods: {
    ...mapActions([
      'setTakeLead',
      'setIsNew',
      'signOut',
      'setBackStrategy',
      'showAIDrawer',
      'getMessageCount'
    ]),
    ...mapMutations([
      'SET_DETAIL_DIALOG_SHOW',
      'SET_NOTIFICATION',
      'SET_INDEX_TOPLIST',
      'SET_REFRESH_UNREAD_COUNT',
      'SET_DETAIL_DIALOG_TYPE'
    ]),
    // 邀请员工试用
    async showUser() {
      // 企微选择
      if (this.isWxPC) {
        try {
          const select = await utils.selectPrivilegedContact()
          const res = await sendWXMessage({ selectedTicket: select.selectedTicket })
          res.success &&
            this.$message({ message: this.$t('message.operateSuccessSymbol'), type: 'success' })
        } catch (e) {}
        return
      }
      this.isShowInviteStaff = true
    },
    initLiveStreamingTip() {
      if (this.liveStreamingInfo.liveStatus === 'living' && this.liveStreamingInfo.liveStartTime) {
        const now = Math.floor(Date.now() / 1000)
        const timeDelay = this.liveStreamingInfo.liveStartTime - now
        if (timeDelay < 0) {
          this.isLiveStreaming = true
        } else {
          console.log(`设置显示直播提示的定时器，间隔${timeDelay}s`)
          const timer = setTimeout(() => {
            this.isLiveStreaming = true
          }, timeDelay * 1000)

          this.$once('hook:beforeDestroy', () => {
            console.log(`清除显示直播提示的定时器`)
            clearTimeout(timer)
          })
        }
      }
    },

    headResize() {
      const headViewCenter = document.querySelector('.header-view-center')
      const divRightLeft = document.querySelector('.header-right-menu')
      if (this.$route.name === 'chartManagement' && headViewCenter && divRightLeft && this.$el) {
        const divCenter = headViewCenter.getBoundingClientRect()
        const divRight = divRightLeft.getBoundingClientRect().left
        const headViewWidth = this.$el.getBoundingClientRect().width
        const divRightWidth = headViewWidth + 20 - divRight
        if (divCenter.width / 2 + divRightWidth > headViewWidth / 2) {
          this.headerRight = divRightWidth + divCenter.width / 2 + 'px'
        } else {
          this.headerRight = '50%'
        }
        return
      }
      if (this.$el && this.$el.getBoundingClientRect().width <= 1150) {
        this.headerRight = '57%'
      } else {
        this.headerRight = '50%'
      }
    },

    getMenuAlias(url) {
      const menuAliasMapKey = Object.keys(menuAliasMap).find((key) => {
        return new RegExp(`.*${key}.*`, 'i').test(url)
      })

      return menuAliasMap[menuAliasMapKey] || ''
    },

    openHelpCenter(item) {
      const url = typeof item === 'string' ? item : item.url

      if (url) {
        window.open(url)
      }
    },

    openAITab() {
      this.showAIDrawer()
    },

    returnAlibaba() {
      window.location.href = 'https://fuwu.1688.com/qn/index.html'
    },
    // 切换语言方式
    changeLanguage(val) {
      setLangPack({
        lang: val
      }).then((res) => {
        this.$message({
          message: res.msg,
          type: 'success'
        })
        utils.LS.set('VUE_LANG', val)
        window.location.reload()
      })
    },
    changeDialogType() {
      userConfigSave({
        configAlias: 'detailOpenMethod',
        configValue: this.detailOpenMethod
      }).then((res) => {
        if (res.success) {
          this.$message({
            message: res.msg,
            type: 'success'
          })
          this.showPreferences = false
          this.SET_DETAIL_DIALOG_TYPE(this.detailOpenMethod)
        }
      })
    },
    signOutClick() {
      utils.LS.remove('crtCompany')
      utils.LS.remove('passportId')
      utils.LS.remove('loginVerify')
      utils.LS.remove('account')
      utils.LS.remove('mobile')
      userLogOut({})
        .then((res) => {})
        .catch(() => {})
        .finally((_) => {
          this.signOut()
          // gioClearUserId()
        })
    },
    // 跳转切换公司页面
    crtCompany() {
      utils.LS.set('crtCompany', true)
      // 清除企业微信秘钥弹窗的显隐标识
      utils.SS.set('setSecretDialogShow', 'false')
      window.location.replace('/stand-alone-login.html#/?chooseCompany=1')
      // this.signOut()
      // gioClearUserId()
    },
    menuClick(item) {
      const type = item.alias
      switch (type) {
        case 'message':
          this.detailDialogShow && this.SET_DETAIL_DIALOG_SHOW(false)
          this.SET_NOTIFICATION(true)
          break
        case 'workFlow':
        case 'controlCenter':
        case 'chartCenter':
        case 'templateCenter':
          this.SET_NOTIFICATION(false)
          this.activeUrl = item.url
          this.$router.push(item.url)
          break
        // this.$router.push({path: '/appModule/index'})
        // break
        case 'home':
          if (this.$store.state.user.noApp) {
            this.$router.push({ name: 'noApp' })
          }
          this.SET_NOTIFICATION(false)
          this.activeUrl = item.url
          this.$router.push(item.url)
          break
        case 'customerService':
          this.showPhone = true
          break
        case 'returnMarket':
          this.returnAlibaba()
          break
      }

      // 顶部埋点，按照产品需求，和左侧菜单公用一个埋点
      // gioSystemMenu({ name: item.name })
    },
    // 获取套餐信息
    getPackageInfo() {
      // 获取搜客套餐信息
      this.getSoukeUseInfo()
      this.getPackageSystem()
      this.getHealthByDate()
    },
    // 个人中心展示数据
    getPackageSystem() {
      if (!this.personalCenterData.userName) {
        this.informationLoading = true
        getPackageSystem()
          .then((res) => {
            this.multiLang = res.result.multiLang
            res.result.systemCapacityList.forEach((item) => {
              if (item.percentage <= 0) {
                item.percentage = 0
              }
              if (item.percentage >= 100) {
                item.percentage = 100
              }
            })
            sessionStorage.setItem('mobile', res.result.mobile)
            sessionStorage.setItem('email', res.result.email)
            sessionStorage.setItem('realMobile', res.result.realMobile)
            this.personalCenterData = res.result
            this.signOutHide = res.result.companySource !== '5'
          })
          .catch(() => {})
          .finally(() => {
            this.informationLoading = false
          })
      }
    },
    // 获取搜客套餐使用信息
    getSoukeUseInfo() {
      soukeUseInfo({
        uid: String(utils.LS.get('corpid')) + String(utils.LS.get('userId'))
      }).then(({ result }) => {
        // 未开通搜客应用
        if (result.error_code) return
        this.souke.total = result.data.view_contact_num
        this.souke.remain = result.data.view_contact_num_left
        this.souke.title =
          +result.data.type === 1
            ? this.$t('homeHeaderView.dailySearchTimes')
            : this.$t('homeHeaderView.monthSearchTimes')
      })
    },

    // 跳转到个人中心
    jumpPersonalCenter(sign) {
      this.$router.push({ path: '/personalCenter/personalCenterIndex', query: { sign: sign } })
    },

    // 获取昨日系统指数
    getHealthByDate() {
      const date = xbb.getDay(-1)
      getHealthByDate({ date: date })
        .then((data) => {
          this.health = data.result.health
          if (data.result.difference >= 0) {
            this.isRise = true
            this.difference = data.result.difference
          } else if (data.result.difference < 0) {
            this.isRise = false
            this.difference = Math.abs(data.result.difference)
          }
        })
        .catch(() => {})
    },

    // 判断是否显示上手攻略
    guideLabelIsShow() {
      // 如果第五步的页面跳转应该保存参数
      if (this.getGuideStep === 5) {
        this.setTakeLead(5)
      } else {
        guideLabelIsShow({})
          .then((data) => {
            const show = data.result.show
            if (xbb.isThirdPC(['wx'], true)) {
              utils.SS.set('guideLabelIsShow', show)
            } else {
              show && this.setTakeLead(1)
            }
            this.setIsNew(data.result.isNew)
            // this.setTakeLead(1)
          })
          .catch(() => {})
      }
    },
    // 获取顶部菜单
    getMenuTopInfo(bool) {
      if (!bool) {
        this.topRightList = [
          {
            alias: 'help'
          }
        ]
      }
      getMenuTopInfo({})
        .then((data) => {
          this.guideLabelIsShow()
          this.topCenterList = data.result.topCenterList
          this.topRightList = data.result.topRightList
          data.result.topCenterList.forEach((item) => {
            if (item.alias === 'home') {
              item.icon = 'icon-logo1'
            }
          })
          console.log('data.result:', data.result)
          this.SET_INDEX_TOPLIST(data.result)
        })
        .catch(() => {})
    },

    // 在浏览器中打开
    openBrowser() {
      const corpid = utils.LS.get('corpid')
      const url = window.location.pathname + window.location.hash
      let goUrl = utils.openBlankUrl(url)
      goUrl += `&corpid=${corpid}`
      this.isLarkPC && (goUrl += '&env=lark')
      this.isDingTalk && (goUrl += '&env=dd')
      // 微信pc使用微信的特殊方法打开
      this.isWxPC
        ? utils.openWXLink(window.location.origin + goUrl + '&env=wx')
        : window.open(goUrl)
    },

    // 邀请员工
    inviteStaff() {
      this.$emit('inviteStaff')
    },

    // 获取公司积分
    getCompanyScore() {
      getCompanyScore({})
        .then((res) => {
          this.companyScore = res.result
          this.scorePercentage =
            ((this.companyScore.score - this.companyScore.levelMin) * 100) /
            (this.companyScore.levelMax - this.companyScore.levelMin)
          if (this.companyScore.level === 6) {
            this.scorePercentage = 100
          }
        })
        .catch((err) => console.log(err))
    },

    getLiveStreamingInfo() {
      getLiveStreamingInfo().then((res) => {
        if (this.liveStreamingInfo.liveStatus !== 'historyLive') {
          this.liveTopic = res.result.liveTopic
        }
        this.liveLink = res.result.activityLink || res.result.livePlaybackLink
      })
    },

    helpPopoverShow() {
      this.getCompanyScore()
      this.getLiveStreamingInfo()
    },

    getStrategyState() {
      return new Promise((resolve, reject) => {
        getStrategyState({})
          .then((res) => {
            this.strategyShow = res.result.status
            resolve(this.strategyShow)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },

    // 跳转到价值攻略
    goWorthStrategy() {
      this.setBackStrategy(false)
      this.$router.push({ path: '/worthStrategy/home' })
    },

    // 解散公司
    dissolutionCompany() {
      this.dtCompanyDialogShow = true
    },

    handleChangeDeveloperEnv() {
      console.log(this.isDeveloperStatus)
      const msg = this.isDeveloperStatus
        ? '您的所有表单都将切换回已发布的JS代码'
        : '您的所有表单都将加载未发布版本的JS代码，以供您调试'
      const jsFileStatus = this.isDeveloperStatus ? 'release' : 'dev'
      this.$alert(msg, '提示', {
        confirmButtonText: '确定',
        callback: () => {
          localStorage.setItem('js-file-status', jsFileStatus)
          this.isDeveloperStatus = !this.isDeveloperStatus
        }
      })
    },
    // 改变gateawy地址
    handleGatewayChange(val) {
      this.$confirm(this.$t('system.changeServerMessage'), this.$t('message.confirmTitle'), {
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(() => {
          this.changeGateway(val)
        })
        .catch(() => {
          this.customGateway = !val
        })
    },

    changeGateway(val) {
      if (val) {
        utils.LS.set('customGateway', 1)
      } else {
        utils.LS.remove('customGateway')
      }
      window.location.reload()
    },
    openLiveStreamingDialog() {
      if (this.liveLink) {
        const h = this.$createElement
        this.$alert(
          h(
            'div',
            {
              staticClass: 'img-wrapper'
            },
            [
              h(
                'img',
                {
                  style: {
                    width: '100%'
                  },
                  attrs: {
                    src: this.liveLink
                  }
                },
                null
              )
            ]
          ),
          '',
          {
            customClass: 'live-streaming-dialog',
            showClose: true,
            showConfirmButton: false,
            center: true
          }
        )
        // 触发直播埋点
        liveEventTracking()
      }
    },
    cancelChangeDialogType() {
      this.showPreferences = false
      this.detailOpenMethod = this.$store.state.user.detailOpenMethod
    }
  }
}
</script>

<style lang="scss" scoped>
$fontColor: $base-white;
$bg-orange: $brand-color-5;
$headerHeight: 52px;
@keyframes changeImg {
  from {
    left: -100%;
  }
  100% {
    left: 150%;
  }
}
.shan {
  position: absolute;
  top: 0;
  width: 40%;
  height: 100%;
  content: '';
  background: -webkit-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background: -o-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background: -moz-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(45deg);
  -webkit-animation: changeImg 3s ease 0s;
  -o-animation: changeImg 3s ease 0s;
  animation: changeImg 3s ease 0s;
  -moz-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.title__tel {
  display: flex;
  p {
    font-size: 14px;
    color: $text-auxiliary;
  }
  h2 {
    font-size: 14px;
    font-style: italic;
    color: $text-main;
  }
}
.header-view {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  color: $fontColor;
  a {
    color: inherit;
  }
  & > .header-view-center {
    position: absolute;
    z-index: 3;
    display: flex;
    // flex: 1 1 auto;
    align-items: center;
    justify-content: center;
    width: 400px;
    margin-right: 10px;
    overflow: hidden;
    transition: all 0.5s;
    .header-view-menu {
      display: flex;
      align-items: center;
      .header-view-menu-item {
        position: relative;
        min-width: 65px;
        margin-right: 48px;
        font-size: 14px;
        color: $text-plain;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        .icon-block {
          display: inline-block;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          background: $neutral-color-1;
          border-radius: 4px;
          .center-icon {
            font-size: 16px;
            color: $neutral-color-4;
          }
        }
        &:hover {
          color: $brand-color-5;
          outline: none;
          .center-icon {
            color: $brand-color-5;
          }
        }
        &.active {
          color: $brand-color-5;
          border-bottom-width: 0px;
          .center-icon {
            color: $brand-color-5;
          }
        }
        .new-icon-wrapper {
          position: absolute;
          top: 13px;
          display: inline-block;
          width: 30px;
          line-height: 18px;
          .new-module {
            margin-right: 0px;
            font-size: 19px;
            color: $error-color-5;
          }
          b {
            position: absolute;
            top: -2px;
            left: 0px;
            font-weight: normal;
            color: $base-white;
            white-space: nowrap;
            transform: scale(0.6);
          }
        }
        .menu-count {
          position: absolute;
          top: 11px;
          left: 54px;
          display: block;
          padding: 0 5px;
          font-size: 12px;
          line-height: 16px;
          color: $base-white;
          word-break: keep-all;
          background-color: $error-base-color-6;
          border-radius: 7px;
          transform: scale(0.83333);
        }
        .menu-count-us {
          position: absolute;
          top: 11px;
          left: 70px;
          display: block;
          padding: 0 5px;
          font-size: 12px;
          line-height: 16px;
          color: $base-white;
          word-break: keep-all;
          background-color: $error-base-color-6;
          border-radius: 7px;
          transform: scale(0.83333);
        }
      }
    }
    .web-iconfont {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
      background-color: $neutral-color-1;
      border-radius: 4px;
    }
    .header-view-menu-item:hover,
    .header-view-menu-item.active {
      .web-iconfont {
        background-color: $brand-color-1;
      }
    }
  }
  .chart-right {
    margin-right: -200px;
  }
  .home-right {
    margin-right: -170px;
  }
  .home-fix-right {
    margin-right: -120px;
  }
  & > .header-view-left {
    display: flex;
    flex: 1;
    white-space: nowrap;
    .corp-info {
      display: flex;
      align-items: center;
      width: 160px;
      height: $headerHeight;
      line-height: $headerHeight;
      .corp-abbreviation {
        flex: 0 0 28px;
        width: 28px;
        height: 28px;
        line-height: 28px;
        color: $base-white;
        text-align: center;
        background-color: $brand-color-5;
        border-radius: 6px;
      }
      .is-stand-alone {
        background-color: #35db9d;
      }
      .company-icon {
        // display: inline-block;
        flex: 0 0 28px;
        width: 28px;
        height: 28px;
        overflow: hidden;
        // margin-right: 16px;
        line-height: 28px;
        text-align: center;
        border-radius: 6px;
        img {
          width: 28px;
        }
      }
      .corp-name {
        max-width: 90px;
        margin-left: 12px;
        overflow: hidden;
        color: $text-plain;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .fee-type {
        margin-left: 10px;
        font-size: 12px;
        line-height: $headerHeight;
        .version {
          color: $brand-color-2;
        }
        .feeTypeNameForGray {
          z-index: 0;
          padding: 2px 3px;
          font-size: 12px;
          color: $bg-menu;
          background-image: linear-gradient(to right, #fff4db, #ffe4b6);
          border-radius: 4px;
        }
      }
    }
    .header-view-open-browser {
      position: relative;
      display: inline-block;
      width: 28px;
      height: 28px;
      margin-left: 32px;
      line-height: 28px;
      color: $text-plain;
      text-align: center;
      cursor: pointer;
      background-color: $neutral-color-1;
      border-radius: 10px;
    }
  }
  & > .header-view-right {
    position: relative;
    display: flex;
    flex: 1;
    justify-content: flex-end;
    .header-ai-menu {
      display: flex;
      align-items: center;
    }
    .header-right-menu {
      position: relative;
      flex: 0 0 auto;
      margin-right: 26px;
      font-size: 14px;
      color: $text-plain;
      cursor: pointer;
      .t-iconfont {
        font-size: 20px;
      }
      &.active {
        color: $brand-color-5;
      }
      &:hover {
        color: $brand-color-5;
        outline: none;
      }
      .menu-count {
        @include singleline-ellipsis();
        position: absolute;
        top: 16px;
        right: 8px;
        z-index: 1;
        display: block;
        max-width: 80px;
        padding: 0 5px;
        font-size: 12px;
        line-height: 16px;
        color: $base-white;
        word-break: keep-all;
        background-color: $error-base-color-6;
        border-radius: 100px;
        transform: translateY(-50%) translateX(100%);
        &.is-small {
          transform: translateY(-50%) translateX(100%) scale(0.83333);
        }
      }
      .menu-count-us {
        @include singleline-ellipsis();
        position: absolute;
        top: 12px;
        right: 14px;
        z-index: 1;
        display: block;
        max-width: 80px;
        padding: 0 5px;
        font-size: 12px;
        line-height: 16px;
        color: $base-white;
        word-break: keep-all;
        background-color: $error-base-color-6;
        border-radius: 100px;
        transform: translateY(-50%) translateX(100%);
        &.is-small {
          transform: translateY(-50%) translateX(100%) scale(0.83333);
        }
      }
    }
    .header-help-lead {
      position: absolute;
      top: 60px;
      right: 250px;
      z-index: 10000;
      cursor: pointer;
      .header-help-lead-img {
        display: inline-block;
        width: 57px;
        height: 82px;
        font-size: 14px;
        background: url('../../assets/take-lead.png');
        .header-help-lead-text {
          position: absolute;
          top: 36px;
          right: 8px;
          width: 35px;
          height: 35px;
          line-height: 16px;
        }
      }
      .header-help-close {
        position: absolute;
        top: 10px;
        right: -14px;
        width: 16px;
        height: 16px;
        line-height: 10px;
        text-align: center;
        background: $brand-color-5;
        border-radius: 50%;
        .el-icon-close {
          font-size: 12px;
        }
      }
    }
    .header-personalCenter {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      cursor: pointer;
      .el-dropdown {
        height: 100%;
      }
      .avatar-wrap {
        margin-right: 10px;
      }
      .header-personalCenter-right {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 40px;
        margin-right: 10px;
        color: $text-plain;
        .username {
          display: inline-block;
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  &__flag {
    position: fixed;
    top: -5px;
    right: -11px;
    width: 32px;
    height: 20px;
    line-height: 22px;
    text-align: center;
    transform: rotate(45deg);
    &.hover {
      cursor: pointer;
      &:hover {
        background-color: $brand-color-5;
      }
    }
  }
}
.personalCenter-information {
  overflow: hidden;
  // 企业微信-账号身份(基础账号/互通账号)
  .system-qwx-identity {
    padding: 16px 28px;
    cursor: pointer;
    border-bottom: 1px solid $neutral-color-2;
    .el-tag {
      margin-right: 8px;
    }
    // 基础账号
    .el-tag--primary {
      color: $link-base-color-6;
      background: #e6f7ff;
      border-color: #91d5ff;
    }
    // 互通账号
    .el-tag--success {
      color: #52c41a;
      background: #f6ffed;
      border-color: #b7eb8f;
    }
  }
  .systemIndex {
    padding: 16px 28px;
    cursor: pointer;
    border-bottom: 1px solid $bg-primary;
    &:hover {
      background-color: #ff8c2e1a;
    }
    .name {
      color: $text-plain;
      span {
        font-size: 16px;
        font-weight: 500;
        color: $brand-color-5;
      }
    }
    p {
      font-size: 12px;
      color: $text-auxiliary;
    }
  }
  .systemCapacity {
    padding: 18px 28px;
    cursor: pointer;
    border-bottom: 1px solid $bg-primary;
    &:hover {
      background-color: #ff8c2e1a;
    }
    .personalCenter-functions-item {
      position: relative;
      display: flex;
      align-items: center;
      margin-top: 10px;
      font-size: 12px;
      color: $text-auxiliary;
      & > span:nth-of-type(1) {
        flex-shrink: 0;
        width: 60px;
        max-width: 70px;
      }
      .el-progress {
        width: 70px;
      }
      .functions-item-value {
        margin-left: 18px;
      }
      .functions-item-tips {
        position: absolute;
        right: -5px;
      }
    }
  }
  .system-souke {
    padding: 18px 28px;
    border-bottom: 1px solid $bg-primary;
    &:hover {
      background-color: #ff8c2e1a;
    }
    .title {
      margin-bottom: 6px;
    }
    .souke-use {
      font-size: 12px;
      color: $text-auxiliary;
      span {
        color: $brand-color-5;
      }
    }
  }
  .signOut {
    padding: 16px 28px;
    color: $error-base-color-6;
    cursor: pointer;
    &:hover {
      background-color: #ff8c2e1a;
    }
  }
  .crtCompany {
    padding: 16px 28px;
    color: #000000;
    cursor: pointer;
    &:hover {
      background-color: #ff8e3d1a;
    }
  }
  .switch-language {
    position: relative;
    padding: 16px 28px;
    cursor: pointer;
    border-bottom: 1px solid $bg-primary;
    &:hover {
      background-color: #ff8c2e1a;
    }
    &__button {
      position: absolute;
      top: 16px;
      right: 28px;
    }
  }
}
.personalCenter-information-en {
  .systemIndex {
    padding: 16px 10px;
  }
  .systemCapacity {
    padding: 18px 10px;
    .personalCenter-functions-item {
      & > span:nth-of-type(1) {
        width: 90px;
        max-width: 90px;
      }
      .functions-item-value {
        margin-left: 8px;
      }
    }
  }
  .system-souke {
    padding: 18px 10px;
  }
  .signOut {
    padding: 16px 10px;
  }
  .switch-language {
    padding: 16px 10px;
  }
}
.web-icon-xiangshang,
.rise {
  color: #ff7171;
}
.web-icon-xiangxia,
.decline {
  color: #78c86d;
}
.account-setting {
  padding: 16px 28px;
  color: #000000;
  cursor: pointer;
  &:hover {
    background-color: #ff8e3d1a;
  }
}
</style>

<style lang="scss">
.header-popover {
  width: 350px;
  padding: 0;
  .header-help-icon {
    .header-help-formlist {
      display: flex;
      flex-wrap: wrap;
      margin-top: 16px;
      .header-help-formlist-item {
        position: relative;
        flex: 0 0 auto;
        width: 33%;
        margin-bottom: 16px;
        text-align: center;
        cursor: pointer;
        .header-help-img {
          display: inline-block;
          width: 32px;
          height: 32px;
          &--circle {
            position: relative;
          }
          &__text {
            position: absolute;
            top: 8px;
            left: 10px;
            width: 14px;
            height: 16px;
            overflow: hidden;
            font-size: 12px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          img {
            width: 100%;
            height: 100%;
          }
        }
        .menu-count {
          @include singleline-ellipsis();
          position: absolute;
          top: 0px;
          right: 55px;
          display: block;
          max-width: 80px;
          padding: 0 5px;
          font-size: 12px;
          line-height: 16px;
          color: $base-white;
          word-break: keep-all;
          background-color: $error-base-color-6;
          border-radius: 100px;
          transform: translateY(-50%) translateX(100%);
        }
      }
    }
    .header-help-foot {
      display: flex;
      justify-content: flex-start;
      height: 38px;
      background: #f7f9ff;
      .el-button {
        position: relative;
        flex: 1;
        font-size: 12px;
        color: $text-auxiliary;
        .web-icon-field-user,
        .el-icon-edit-outline {
          margin-right: 3px;
          font-size: 14px;
        }
      }
      .user::after {
        position: absolute;
        top: 0;
        right: -14px;
        width: 1px;
        height: 100%;
        content: '';
        background: $neutral-color-3;
      }
    }
  }
}
.personalCenter-popover {
  padding: 0;
}
.mr-40 {
  margin-right: 40px !important;
}
.mr-45 {
  margin-right: 45px !important;
}

.live-streaming-dialog {
  position: relative;
  box-sizing: border-box;
  width: auto;
  padding: 52px 20px 20px 20px !important;
  .img-wrapper {
    box-sizing: border-box;
    width: 396px;
    overflow-y: auto;
    border: 1px solid $neutral-color-3;
    border-radius: 8px;
  }
  .el-message-box__header {
    position: static !important;
    width: 0 !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 0 !important;
    border: none !important;
  }
  .el-message-box__headerbtn {
    top: 20px;
    right: 20px;
  }
  .el-message-box__content {
    padding: 0 !important;
  }
  .el-message-box__btns {
    display: none;
  }
}

@media screen and (max-height: 780px) {
  .live-streaming-dialog {
    .img-wrapper {
      height: 600px;
    }
  }
}
@media screen and (max-height: 680px) {
  .live-streaming-dialog {
    .img-wrapper {
      height: 500px;
    }
  }
}
@media screen and (max-height: 580px) {
  .live-streaming-dialog {
    .img-wrapper {
      height: 400px;
    }
  }
}
.person-preferences {
  .main {
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    color: $text-main;
  }
  .desc {
    margin-top: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 400;
    line-height: 13px;
    color: $text-auxiliary;
  }
  .detail-dialog-type {
    display: flex;
    flex-direction: row;
    .detail-dialog-type-item {
      overflow: hidden;
      cursor: pointer;
      border: 1px solid $neutral-color-3;
      border-radius: 6px;
      img {
        display: block;
      }
      .detail-dialog-type-item-radio {
        display: flex;
        align-items: center;
        height: 36px;
        padding-left: 12px;
      }
      .active {
        background: $brand-color-1;
      }
    }
    .detail-dialog-type-item:first-child {
      margin-right: 16px;
    }
  }
}

.dialog-type-drawer {
  width: 208px;
  height: 120px;
  background-image: url('@/assets/dialog-type-1.svg');
  &:hover {
    background-image: url('https://cdn.xbongbong.com/cdnfile/ui-paas-drawer.gif');
    background-repeat: no-repeat;
    background-position: center; /* 控制图片的位置 */
    background-size: cover;
  }
}

.dialog-type-dialog {
  width: 208px;
  height: 120px;
  background-image: url('@/assets/dialog-type-2.svg');
  &:hover {
    background-image: url('https://cdn.xbongbong.com/cdnfile/ui-paas-dialog.gif');
    background-repeat: no-repeat;
    background-position: center; /* 控制图片的位置 */
    background-size: cover;
  }
}

.person-dialog-mode {
  .el-dialog {
    .el-dialog__body {
      padding: 16px 24px;
    }
  }
}
</style>
