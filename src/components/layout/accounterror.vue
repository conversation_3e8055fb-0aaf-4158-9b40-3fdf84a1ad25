<template>
  <div class="accounterror">
    <div class="content">
      <img src="@/assets/Reception.png" />
      <div class="tips-text">{{ memo }}</div>
      <el-button v-if="type === 'admin'" type="primary" @click="accountSet">授杈账户管理</el-button>
    </div>
    <!-- <el-dialog
      class="accounterror-dialog new-style-dialog"
      title="超出授权账户可用数量"
      :visible.sync="dialogVisible"
      width="800px"
    >
      <div class="dialog-content">asd</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>

<script>
export default {
  name: 'Accounterror',

  components: {},

  filters: {},

  mixins: [],
  props: {},

  data() {
    return {
      dialogVisible: false,
      type: 'normal',
      memo: ''
    }
  },

  computed: {},

  watch: {},

  mounted() {
    this.type = localStorage.getItem('userType')
    this.memo = localStorage.getItem('userMemo')
  },

  methods: {
    accountSet() {
      this.$router.push({ name: 'organization' })
    }
  }
}
</script>

<style lang="scss" scoped>
.accounterror {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: $base-white;
  .content {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;
    width: 100%;
    img {
      width: 160px;
      height: 160px;
    }
    .tips-text {
      width: 600px;
      font-size: 14px;
      line-height: 20px;
      color: $text-auxiliary;
      text-align: center;
    }
  }
}
.accounterror :deep(.el-dialog .el-dialog__body) {
  padding: 24px;
}
.accounterror-dialog .dialog-content {
  height: 508px;
}
</style>
