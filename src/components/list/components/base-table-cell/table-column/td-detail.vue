<!--
 * @Description:
-->
<template>
  <div class="td-detail">
    <el-table-column
      :align="head.fieldType === 2 ? 'right' : 'left'"
      header-align="left"
      :label="head.attrName"
      :prop="head.attr"
      :sortable="head.sortable"
    >
      <template slot-scope="{ row }">
        <div class="product-unit">
          <div :class="{ 'td-detail__title': row.enableSerialNumber }">
            {{ row[head.attr] }}
            <el-popover
              v-if="row.enableSerialNumber"
              placement="bottom"
              trigger="click"
              width="400"
            >
              <el-table
                ref="batch-table"
                v-loading="loading"
                border
                class="batch-table"
                :data="tableData"
                max-height="400"
                style="width: 100%"
              >
                <template v-for="(head, index) in tableHead">
                  <el-table-column :key="index" :label="head.attrName" :prop="head.attr">
                    <template slot-scope="{ row }">
                      <BaseTableTd :field-info="head" :row="row" />
                    </template>
                  </el-table-column>
                </template>
              </el-table>
              <div class="batch-dialog-pagination">
                <v-pagination
                  :current-page.sync="page.page"
                  layout="slot,prev, pager, next, jumper"
                  :page-size="page.pageSize"
                  :total="page.total"
                  @current-change="handleCurrentChange"
                >
                </v-pagination>
              </div>
              <div slot="reference" class="td-detail__tips" @click="showTable(row)">
                {{ $t('formDataEdit.operaProductSerial') }}
              </div>
            </el-popover>
          </div>
          <el-tooltip v-if="row[head.attr] && row.transformUnitRate" :content="fetchUnitStr(row)">
            <p class="product-unit__item">{{ fetchUnitStr(row) }}</p>
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </div>
</template>

<script>
import { selectSerial } from '@/api/form'
import commonMixin from './common-mixin.js'
import BaseTableTd from '@/components/base/base-table-td.vue'
import { getUnitNumStr } from '@/utils/unit'

export default {
  name: 'TdDetail',

  components: {
    BaseTableTd
  },

  mixins: [commonMixin],

  props: {
    formQuery: {
      // 表单参数
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      loading: true,
      page: {
        page: 1,
        pageSize: 50,
        total: 0
      },
      tableData: [],
      tableHead: []
    }
  },

  methods: {
    showTable(items) {
      this.loading = true
      this.items = items
      this.fetchSerialList()
    },
    fetchSerialList() {
      const params = {
        businessType: this.formQuery.businessType,
        enableSerialNumber: this.items.enableSerialNumber,
        refId: this.formQuery.dataId,
        productSubId: this.items.productSubId,
        warehouseId: this.formQuery.warehouseId,
        attr: this.formQuery.attr || null,
        ...this.page
      }
      selectSerial(params).then(({ result }) => {
        this.tableData = result.seq || []
        this.tableHead = result.explainList[0].subForm.items || []
        if (result.pageHelper) {
          this.page.total = result.pageHelper.rowsCount
        }
        this.loading = false
      })
    },
    // 翻页
    handleCurrentChange(val) {
      this.page.page = val
      this.fetchSerialList()
    },
    fetchUnitStr(row) {
      // 库存数量显示基本单位
      let unit
      if (this.head.saasAttr === 'stock') {
        unit = row['transformUnitRate'] && row['transformUnitRate'].filter((item) => item.isBase)[0]
      } else {
        unit = row['text_8']
      }
      return getUnitNumStr(unit, row['transformUnitRate'], row[this.head.attr])
    }
  }
}
</script>

<style lang="scss" scoped>
.td-detail {
  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__tips {
    color: $link-base-color-6;
    cursor: pointer;
  }
}
.product-unit {
  line-height: 15px;
  &__item {
    width: 100%;
    overflow: hidden;
    font-size: 12px;
    color: $text-tip;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
