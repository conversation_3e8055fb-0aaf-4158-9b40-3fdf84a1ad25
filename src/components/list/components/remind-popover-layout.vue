<template>
  <div class="remind-popover-layout">
    <el-popover
      ref="remindModalRef"
      v-model="remindModalShow"
      :placement="placement"
      :popper-class="computedPopperClass"
      trigger="click"
      :visible-arrow="visibleArrow"
      @hide="onHide"
      @show="onShow"
      ><slot></slot
    ></el-popover>
    <p v-popover:remindModalRef></p>
  </div>
</template>

<script>
export default {
  name: 'RemindPopoverLayout',

  props: {
    popperClass: {
      type: String,
      default: ''
    },
    placement: {
      type: String,
      default: 'right'
    },
    visibleArrow: {
      type: Boolean,
      default: true
    }
    // popperWidth: {
    //   type: Number,
    //   default: 376
    // }
  },

  data() {
    return {
      remindModalShow: false
    }
  },

  computed: {
    computedPopperClass() {
      return `remind-popover-layout__popper ${this.popperClass}`
    }
  },

  methods: {
    onShow() {
      this.$emit('show')
    },
    onHide() {
      this.$emit('hide')
    },
    // 需要动态指定的dom
    show(target) {
      this.remindModalShow = true
      this.$nextTick(() => {
        const ref = this.$refs.remindModalRef
        ref.popperJS._reference = target
        ref.popperJS.state.position = ref.popperJS._getPosition(
          ref.popperJS._popper,
          ref.popperJS._reference
        )
        ref.popperJS.update()
      })
    },
    close() {
      this.remindModalShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
.remind-popover-layout {
  width: 0;
  height: 0;
  & > p {
    width: 0;
    height: 0;
  }
}
</style>

<style lang="scss">
.remind-popover-layout__popper {
  padding: 0;

  &.el-popper[x-placement^='left'] .popper__arrow {
    border-left-color: inherit;
  }
  &.el-popper[x-placement^='left'] .popper__arrow::after {
    border-left-color: inherit;
  }

  &.el-popper[x-placement^='bottom'] .popper__arrow {
    border-bottom-color: inherit;
  }
  &.el-popper[x-placement^='bottom'] .popper__arrow::after {
    border-bottom-color: inherit;
  }

  &.el-popper[x-placement^='top'] .popper__arrow {
    border-top-color: inherit;
  }
  &.el-popper[x-placement^='top'] .popper__arrow::after {
    border-top-color: inherit;
  }

  &.el-popper[x-placement^='right'] .popper__arrow {
    border-right-color: inherit;
  }
  &.el-popper[x-placement^='right'] .popper__arrow::after {
    border-right-color: inherit;
  }
}
</style>
