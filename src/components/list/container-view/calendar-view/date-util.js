export const getDayCountOfMonth = function (year, month) {
  if (isNaN(+month)) return 31

  return new Date(year, +month + 1, 0).getDate()
}

export const modifyDate = function (date, y, m, d) {
  return new Date(
    y,
    m,
    d,
    date.getHours(),
    date.getMinutes(),
    date.getSeconds(),
    date.getMilliseconds()
  )
}

export const changeYearMonthAndClampDate = function (date, year, month) {
  // clamp date to the number of days in `year`, `month`
  // eg: (2010-1-31, 2010, 2) => 2010-2-28
  const monthDate = Math.min(date.getDate(), getDayCountOfMonth(year, month))
  return modifyDate(date, year, month, monthDate)
}

export const prevMonth = function (date) {
  const year = date.getFullYear()
  const month = date.getMonth()
  return month === 0
    ? changeYearMonthAndClampDate(date, year - 1, 11)
    : changeYearMonthAndClampDate(date, year, month - 1)
}

export const nextMonth = function (date) {
  const year = date.getFullYear()
  const month = date.getMonth()
  return month === 11
    ? changeYearMonthAndClampDate(date, year + 1, 0)
    : changeYearMonthAndClampDate(date, year, month + 1)
}

export const prevDate = function (date, amount = 1) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() - amount)
}

export const nextDate = function (date, amount = 1) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate() + amount)
}

// export function getOneDayTimeRange(date) {
//   if (!date) {
//     date = new Date()
//   }
//   const startTimeStamp = new Date(date).setHours(0, 0, 0, 0)
//   const endTimeStamp = new Date(date).setHours(23, 59, 59, 999)

//   return [startTimeStamp, endTimeStamp]
// }

export function convertMillisecondsToSeconds(milliseconds) {
  if (String(milliseconds).length === 13) {
    return parseInt(milliseconds / 1000)
  } else {
    return milliseconds
  }
}
