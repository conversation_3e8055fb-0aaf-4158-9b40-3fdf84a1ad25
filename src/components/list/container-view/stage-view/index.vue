<!--
  U<PERSON> Paas 化的泳道视图
-->
<template>
  <div v-loading="loadingStatus.initLoading" class="stage-view">
    <div
      v-if="!isEmpty"
      ref="groupWrapperRef"
      v-xbb-infinite-scroll="loadMoreGroupHandler"
      class="stage-view__wrapper"
      xbb-infinite-scroll-direction="horizontal"
      :xbb-infinite-scroll-disabled="rightScrollDisabled"
      :xbb-infinite-scroll-immediate="false"
    >
      <div
        v-for="groupItem in groupList"
        :key="`${groupItem.groupId}-group`"
        class="stage-view__item"
      >
        <lane
          :key="`${groupItem.groupId}-lane`"
          ref="laneRef"
          :app-info="appInfo"
          :card-list="groupDataMap[groupItem.groupId].dataList"
          :drag-options="getLaneDragOptions(groupItem)"
          :lane-info="groupItem"
          :loading="groupDataMap[groupItem.groupId].loading"
          :page-helper="groupDataMap[groupItem.groupId].pageHelper"
          :summary="groupDataMap[groupItem.groupId].summary"
          @buttomScroll="loadMoreGroupDataHandler"
          @cardBtnClick="cardBtnClickHandler"
          @cardBtnRefresh="cardBtnRefreshHandler"
          @cardDrag="cardDragHandler"
          @cardOpenDetail="onLaneDetail"
          @headerBtnClick="onLaneAdd"
        ></lane>
      </div>
      <!-- 页面右划加载 -->
      <div v-if="rightScrollLoadingShow" class="stage-view__loading">
        <span class="loading-text"
          ><i class="el-icon-loading"></i>{{ $t('operation.loadMore') }}</span
        >
      </div>
    </div>
    <div v-else-if="!groupLoading" class="no-data">
      <Empty :text="emptyTxt" />
    </div>
  </div>
</template>

<script>
// constant
import Empty from '@/components/base/empty.vue'
import { ViewTypeEnum } from '@/constants/enum/ui-paas/enum.view-type.js'
import commonBusinessType from '@/constants/common/business-type'

// api
import { batchFocus } from '@/api/batch-handle'
import { batchArchive } from '@/api/batch-handle'
import { getUiPaasApiMap } from '@/components/list/container-view/utils/ui-paas-api-map.js'
import { clueFollow } from '@/api/market-manage'
import { setPaymentBadDebtReturn, setPayPlanBadDebtReturn } from '@/api/form-detail-tabs'
import {
  updateStatus,
  updateTaskTime,
  updateChangeMilepost,
  updateProjectTime
} from '@/api/work-order-v2/common.js'

// components promise
import { initMailWriteFullscreenDialogInstance } from '@/views/mail/mail-write-fullscreen-dialog/index.js'
import PrintDialogPromise from '@/components/form-data-detail/components/dialog/print-dialog-promise.js'
import InvalidReasonPromise from '@/components/form-data-detail/components/dialog/invalid-reason-promise.js'
import AdjustTimePromise from '@/components/form-data-detail/components/tabs/work-order-v2/detail-bord/components/gantt/components/adjust-time-promise.js'
import DingGroupSetPromise from '@/components/form-data-detail/components/dialog/ding-group-set-promise.js'
// components
import Lane from '@/components/list/container-view/stage-view/lane.vue'
import ListHandler from '@/components/cloudcode/messageHandle/lowcode/list-handler'

// utils
import xbb from '@xbb/xbb-utils'
import {
  formatOriginGroupData,
  btnIconStatusChange
} from '@/components/list/container-view/stage-view/utils/group-data.js'
import {
  mockGetUiPaasLaneViewData, // mock getUiPaasListDisplayData 接口，模拟泳道数据获取接口
  mockGetUiPaasLaneViewGroup // mock getUiPaasListDisplayGroup 接口，模拟泳道分组获取接口
} from '@/components/list/container-view/stage-view/utils/mock-data.js'

import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'

export default {
  name: 'UiPaasStageView',

  components: {
    Empty,
    Lane
  },

  props: {
    // 预览模式
    isPreview: {
      type: Boolean,
      default: false
    },
    // 预览配置
    previewConfig: {
      type: Object,
      default: () => ({})
    },
    // 表单相关信息
    appInfo: {
      type: Object,
      default: () => ({}),
      required: true
    },
    stageProcessId: {
      type: [Number, String]
    },
    groupField: {
      type: Object,
      default: () => ({})
    },
    viewConfig: {
      type: Object,
      default: () => ({})
    },
    pageInfo: {
      // 把页面上的一些分组信息，环境信息，接口查询信息都往这里丢
      type: Object,
      default: () => ({
        layoutId: undefined,
        layoutSource: undefined,
        isUseUserConfig: undefined,
        defaultGroup: undefined,
        listGroupId: undefined,
        conditions: undefined
      })
    }
  },

  data() {
    return {
      groupList: [],
      groupDataMap: {}, // { [key]: { pageHelper, dataList, summary, loading }}, key 把groupId转为字符串，确保响应式更新
      groupPageHelper: {
        // 泳道分组分页
        currentPageNum: 1,
        pageSize: 20,
        hasRight: true,
        rowsCount: 0
      },
      groupLoading: false, // 刷新加载
      pageQueryParams: {}, // 缓存外部列表通用查询参数：筛选条件、查询条件等
      dragCache: {
        // 缓存拖拽信息，用于回退等操作
        fromLane: null, // 拖拽前的泳道
        toLane: null, // 拖拽后的泳道
        fromCardIndex: undefined, // 拖拽前卡片的位置
        card: null
      }
    }
  },

  computed: {
    emptyTxt() {
      return this.isPreview
        ? this.$t('layoutDesign.groupEmpty')
        : this.$t('layoutDesign.groupError')
    },
    groupListMap() {
      const mapList = this.groupList.map((group) => [group.groupId, group])
      return Object.fromEntries(mapList)
    },
    uiPaasApi() {
      return getUiPaasApiMap({ ...this.appInfo }) ?? {}
    },
    isEmpty() {
      return !this.groupList?.length
    },
    isStage() {
      return this.groupField?.fieldType === 10030
    },
    loadingStatus() {
      return {
        initLoading: !!(this.groupLoading && this.isEmpty),
        moreLoading: !!(this.groupLoading && !this.isEmpty)
      }
    },
    rightScrollDisabled() {
      return this.isPreview || this.groupLoading || !this.groupPageHelper.hasRight
    },
    rightScrollLoadingShow() {
      return !this.isPreview && this.groupPageHelper.hasRight
    },
    /** 低代码相关 start */
    // 泳道用精简后的低代码参数，不提供过多的信息，更详细的信息用户可以通过api获取。老阶段和详情那边未来也考虑往泳道的参数结构转换
    lowCodeParams() {
      if (!this.isStage) {
        return {}
      }

      const stageList = this.groupList.map((lane, index) => {
        return {
          id: lane.origin.stageId,
          stageTitle: lane.origin.text,
          stageType: lane.origin.stageType
        } // 只提供必要的阶段信息
      })

      const dragParams = {
        dataId: this.dragCache?.card?.id,
        stageProcessId: this.stageProcessId,
        toStageId: this.dragCache?.toLane?.groupId,
        fromStageId: this.dragCache?.fromLane?.groupId
      }

      const formInfo = {
        appId: this.appInfo.appId,
        saasMark: this.appInfo.saasMark,
        menuId: this.appInfo.menuId,
        formId: this.appInfo.formId,
        subBusinessType: this.appInfo.subBusinessType,
        distributorMark: this.appInfo.distributorMark,
        businessType: this.appInfo.businessType
      }
      return {
        formInfo,
        params: {
          stageList,
          ...dragParams
        }
      }
    },
    ...mapState({
      lowCode: function (state) {
        if (this.appInfo?.formId) return state.lowcode.instant[this.appInfo.formId]
      }
    }),
    // 审批中分组
    isFlowGroup() {
      return this.pageInfo.defaultGroup === 1 && this.pageInfo.listGroupId === 9999
    },
    /** 低代码相关 end */
    ...mapGetters(['feeType'])
  },

  watch: {
    isEmpty(val, oldVal) {
      this.addScrollListener(!val)
    }
  },

  mounted() {
    this.initLowCode()
  },

  destroyed() {
    this.unBindLowCode()
  },

  methods: {
    ...mapMutations(['INIT_LOW_CODE', 'UN_BIND_LOW_CODE', 'SET_DATA_ID_LIST']),
    ...mapActions([
      'formDataEdit',
      'formDetailCopy', // 数据复制
      'formDetailNewVersion', // 报价单新版本
      'formDetailLinkAdd', //
      'linkContract', // 资金-关联合同
      'payMentWriteOff' // 资金-核销应收
    ]),
    getLaneDragOptions(groupItem) {
      return {
        group: {
          name: 'lane',
          put: groupItem?.origin?.emptyGroup !== 1, // 空值分组不允许拖入
          pull: true
        }
      }
    },
    addScrollListener(show) {
      if (show) {
        this.$nextTick(() => {
          this.$refs.groupWrapperRef?.addEventListener('scroll', this.hideCardDropdown)
          this.$once('hook:beforeDestroy', () => {
            this.$refs.groupWrapperRef?.removeEventListener('scroll', this.hideCardDropdown)
          })
        })
      }
    },
    // 处理dropdown隐藏
    hideCardDropdown(eve) {
      this.$refs.laneRef?.forEach((ref) => ref.btnDropdownClose())
    },
    /** 低代码相关 start */
    initLowCode() {
      // 预览模式禁止交互
      if (this.isPreview || !this.isStage) return

      if (this.$feeType.checkFeatureEnable('lowcode')) {
        this['INIT_LOW_CODE']({
          formInfo: {
            formId: this.appInfo.formId,
            saasMark: this.appInfo.saasMark,
            businessType: this.appInfo.businessType
          },
          component: this,
          callback: (res) => {
            if (res && res.config) {
              this.lowcodeMessageHandler = new ListHandler(
                `iframe${res.config.lowCodeJsFile.fileId}`,
                this
              )
            }
          }
        })
      }
    },
    unBindLowCode() {
      // 预览模式禁止交互
      if (this.isPreview || !this.isStage) return

      if (this.$feeType.checkFeatureEnable('lowcode')) {
        // 低代码仅旗舰版可用
        this['UN_BIND_LOW_CODE']({
          formInfo: {
            formId: this.appInfo.formId,
            saasMark: this.appInfo.saasMark,
            businessType: this.appInfo.businessType
          },
          component: this
        })
      }
    },
    lowCodeMessage(type, messageData, app, requestId) {
      this.lowcodeMessageHandler?.exec(type, messageData, requestId)
    },
    async exeLowCode() {
      if (!this.lowCode?.config?.actionList || !this.isStage) {
        return Promise.resolve(true)
      }

      let actionList = []
      for (const key in this.lowCode.config.actionList) {
        const action = this.lowCode.config.actionList[key].find((item) => item.code === 8)
        if (action) {
          actionList = action.list
          break
        }
      }
      if (actionList.length) {
        try {
          const res = await this.$store.dispatch('EXECUTE_LOW_CODE_PROMISE', {
            handle: 1,
            info: this.lowCodeParams,
            action: actionList,
            formId: this.appInfo.formId
          })
          // 如果低代码返回false则打断后面的逻辑执行（指的是用户低代码里返回false来阻塞）
          if (!res.success) {
            window['console']['info'](this.$t('lowCode.systemLogicInterrupted'))
            return Promise.reject(false)
          }
        } catch (err) {
          // 对于低代码来说，用户的错误代码逻辑不应该阻塞正常业务逻辑
          return Promise.resolve(true)
        }
      }
      return Promise.resolve(true)
    },
    /** 低代码相关 end */
    /** 拖拽相关 start */
    resetDragCache() {
      this.dragCache = {
        // 缓存拖拽信息，用于回退等操作
        fromLane: null, // 拖拽前的泳道
        toLane: null, // 拖拽后的泳道
        fromCardIndex: undefined, // 拖拽前卡片的位置
        card: null
      }
    },
    // 为了解决后端es缓存问题，临时用前端拖拽更新塞值的方式处理拖拽结果，如果后续后端解决了es问题，这个方法可以替换成 refreshTargetGroupDataHandler 方法
    tempUpdateLaneDragInfo(fromLaneGroupId, toLaneGroupId) {
      // 卡片塞值在拖拽后就已经塞进去，这边只需要处理相关分页值（跟产品确认，其他信息更新暂时不管）
      const toCardData = this.groupDataMap[toLaneGroupId]
      const fromCardData = this.groupDataMap[fromLaneGroupId]
      this.$set(fromCardData, 'pageHelper', {
        ...fromCardData.pageHelper,
        rowsCount: fromCardData.pageHelper.rowsCount - 1
      })
      this.$set(toCardData, 'pageHelper', {
        ...toCardData.pageHelper,
        rowsCount: toCardData.pageHelper.rowsCount + 1
      })
    },
    revertDrag() {
      if (!this.dragCache.card) return

      const toCardList = this.groupDataMap[this.dragCache.toLane.groupId].dataList
      const fromCardList = this.groupDataMap[this.dragCache.fromLane.groupId].dataList
      // 通过查找的方式获取目标卡片位置（draggable的add的newIndex在插入空数组时值有问题）
      const toCardIndex = toCardList.findIndex((card) => card.id === this.dragCache.card.id)

      if (toCardIndex !== -1) {
        toCardList.splice(toCardIndex, 1)
      }
      fromCardList.splice(this.dragCache.fromCardIndex ?? 0, 0, this.dragCache.card)
    },
    // 阶段跳转
    async setUiPaasListDisplayFastSwitch({ dataId, stageInfo = {}, fieldInfo = [] }) {
      const fixParams = {
        // 表单参数
        appId: this.appInfo.appId,
        menuId: this.appInfo.menuId,
        formId: this.appInfo.formId,
        businessType: this.appInfo.businessType,
        subBusinessType: this.appInfo.subBusinessType,
        saasMark: this.appInfo.saasMark,
        // 分组参数
        fieldType: this.groupField.fieldType
      }
      const dataParams = {
        dataId,
        stageInfo,
        fieldInfo
      }

      return this.uiPaasApi.setUiPaasListDisplayFastSwitch({
        ...fixParams,
        ...dataParams
      })
    },
    cardMove() {
      let confirmConfig = {
        message: this.$t('formDataDetail.tabs.confirmJumpToGroup', {
          attr: this.dragCache.toLane?.origin?.text
        }),
        title: this.$t('formDataDetail.tabs.groupChange')
      }
      let dataParams = {
        dataId: this.dragCache.card?.id,
        fieldInfo: [
          {
            attr: this.groupField?.attr,
            value: {
              text: this.dragCache.toLane?.origin?.text,
              value: this.dragCache.toLane?.origin?.value,
              color: this.dragCache.toLane?.origin?.color,
              isVisible: this.dragCache.toLane?.origin?.isVisible
            }
          }
        ]
      }

      if (this.isStage) {
        confirmConfig = {
          message: this.$t('formDataDetail.tabs.confirmJumpTo', {
            attr: this.dragCache.toLane?.origin?.text
          }),
          title: this.$t('formDataDetail.tabs.stageChange')
        }

        dataParams = {
          dataId: this.dragCache.card?.id,
          stageInfo: {
            // 注意，对于阶段来说，stageId不等同于value
            fromStageId: this.dragCache.fromLane?.origin?.stageId,
            toStageId: this.dragCache.toLane?.origin?.stageId,
            reasonId: 0, // TODO: 这个在实际拖拽中是不会有的
            memo: '', // TODO: 这个在实际拖拽中是不会有的
            stageProcessId: this.stageProcessId
          }
        }
      }

      return new Promise((resolve, reject) => {
        this.$confirm(confirmConfig.message, confirmConfig.title, {
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning',
          beforeClose: async (action, instance, done) => {
            if (action !== 'confirm') {
              reject()
              done()
              return
            }

            instance.confirmButtonLoading = true
            instance.confirmButtonText = this.$t('mixin.inExecution')
            try {
              // 执行低代码的前置逻辑
              await this.exeLowCode()

              const { result } = await this.setUiPaasListDisplayFastSwitch(dataParams)
              this.$message({
                type: 'success',
                message: result.msg || this.$t('message.operateSuccessSymbol')
              })
              // 这里因为后端es同步数据很慢，前端需要等待
              setTimeout(() => {
                resolve()
                done()
                instance.confirmButtonLoading = false
              }, 0)
              // }, globalConst.DELAY_TIME) 先去掉延时，如果后续换成刷新逻辑，可能要加回来
            } catch (err) {
              console.log('errr', err)
              reject()
              done()
              instance.confirmButtonLoading = false
            }
          }
        }).catch((err) => {})
      })
    },
    dragChoose(event, laneInfo) {
      console.log('dragChoose', {
        event,
        laneInfo
      })
      const startCardList = this.groupDataMap[laneInfo.groupId].dataList
      const card = xbb.deepClone(startCardList?.[event.oldIndex])
      this.dragCache = {
        fromLane: laneInfo,
        toLane: null,
        fromCardIndex: event.oldIndex,
        card
      }
    },
    async dragAdd(event, laneInfo) {
      console.log('dragAdd', {
        event,
        laneInfo
      })
      this.dragCache.toLane = laneInfo
    },
    async dragEnd(event, laneInfo) {
      console.log('dragEnd', {
        event,
        laneInfo
      })
      if (!this.dragCache.toLane) {
        this.resetDragCache()
        return
      }

      try {
        await this.cardMove()
        // 成功后刷新对应列的泳道
        // await this.refreshTargetGroupDataHandler([
        //   this.dragCache.fromLane.groupId,
        //   this.dragCache.toLane.groupId
        // ]).catch((err) => console.log(err)) // 这里加catch是不希望刷新的请求被try catch到导致错误回滚

        this.tempUpdateLaneDragInfo(this.dragCache.fromLane.groupId, this.dragCache.toLane.groupId)
      } catch (err) {
        console.log('eee', err)
        this.revertDrag()
      } finally {
        this.resetDragCache()
      }
    },

    /**
     * @description: 拖拽事件分发
     * @param {string} type 拖拽事件类型，对应sortable的event名
     * @param {object} event sortable拖拽事件返回的event
     * @param {object} laneInfo 触发该事件的泳道信息
     * @return {*}
     */
    cardDragHandler({ type, event, laneInfo }) {
      switch (type) {
        case 'choose':
          this.dragChoose(event, laneInfo)
          break
        case 'add':
          this.dragAdd(event, laneInfo)
          break
        case 'end':
          this.dragEnd(event, laneInfo)
          break
        default:
          return
      }
    },
    /** 拖拽相关 end */

    /**
     * @description: 计算分组header形状，针对阶段推进器
     * @param {Number} curStageType 当前分组的阶段类型
     * @param {Number} preStageType 前置分组的阶段类型
     * @return {String} 'start-arrow', 'middle-arrow', 'end-arrow', 'square'
     */
    getHeaderShapeType(curStageType = undefined, preStageType = undefined) {
      // 阶段类型不存在，意味着是普通下拉框分组
      if (typeof curStageType === 'undefined') {
        return 'square' // 渲染为方块
      }

      // 当前类型存在，但前置类型不存在，意味着是首节点
      if (typeof preStageType === 'undefined') {
        return 'start-arrow'
      } else if (curStageType === 0) {
        return 'middle-arrow'
      } else if (preStageType === 0) {
        return 'end-arrow'
      } else {
        return 'square'
      }
    },
    /**
     * @description:
     * @param {Number} curStageType
     * @param {Boolean} isEmptyGroup 是否是空值分组
     * @return {String} 'start', 'success', 'fail', 'cancel', 'other', 'number', 'none'
     */
    getHeaderIconType(curStageType, isEmptyGroup = false) {
      if (isEmptyGroup) {
        return 'other'
      }

      if (typeof curStageType === 'undefined') {
        return 'none' // 非阶段，不需要图标
      }

      const iconMap = {
        1: 'success',
        2: 'fail',
        3: 'cancel',
        0: 'number',
        [-1]: 'other'
      }

      return iconMap[curStageType]
    },
    /**
     * @description: 对后端返回的原始分组列表进行处理，处理成前端容易渲染的结构
     * @param {Object} groupField 分组字段 { attr, fieldType, visible, editable} visible标识是否可以新建、editable标识是否可以拖动交互
     * @param {Array} group 原始分组信息 [{ text, value, isOther, color, stageType, stageId }]
     * @param {Number} preIndex 前置索引
     * @return {Array} [{ origin, groupId, index, title, shape, icon, op }]
     */
    formatOriginGroup(groupField, group, preIndex = -1) {
      const hasEmptyGroup = group[0]?.emptyGroup || this.groupList[0]?.origin?.emptyGroup
      return group?.map((groupItem, index) => {
        const curIndex = preIndex + index + 1

        const preStageType =
          index === 0 ? this.groupList[preIndex]?.origin?.stageType : group[index - 1]?.stageType
        const curStageType = groupItem.stageType
        return {
          origin: groupItem, // 记录原始数据
          groupId: groupItem.value, // 用选项值作为唯一标识，阶段则对应stageCode。无值分组，这里是-1
          index: hasEmptyGroup ? curIndex - 1 : curIndex, // 分组对应的会想的索引值（注意，是分组序号，不是实际列表里索引）
          title: {
            name: groupItem.text,
            style: {
              color: groupItem.color
            }
          },
          shape: this.getHeaderShapeType(curStageType, preStageType),
          icon: this.getHeaderIconType(curStageType, !!groupItem.emptyGroup),
          op: {
            // 一些泳道操作配置 1
            // 下拉单选/单选按钮中的不可见选项也需要支持显示，该选项作为单独一列泳道，不支持泳道快捷新增功能，新建按钮隐藏
            add: !!groupItem?.add,
            drag: !!(
              (
                groupItem?.isVisible === 1 && // 可见
                groupField.editable && // 可编辑
                !this.isFlowGroup
              ) // 非工作流页面分组
            ) // 是否允许拖拽
          }
        }
      })
    },
    /**
     * @description: 请求分组列表
     * @param {Number} page 目标页
     * @return {Array} 获取到的格式化后的对应page的泳道分组列表
     */
    async fetchGroup(page = 1) {
      if (this.isStage && !this.stageProcessId) {
        return []
      }

      this.groupLoading = true
      // 默认pageSize 20，后端目前设定的最大值 40。实际阶段可设置的是37
      const params = {
        ...this.appInfo, // { appId, businessType, formId, }
        viewType: ViewTypeEnum.PAAS_LANEVIEW,
        layoutId: this.pageInfo.layoutId,
        viewId: this.viewConfig.id,
        groupAttr: this.groupField.attr,
        stageProcessId: this.stageProcessId,
        page,
        pageSize: 20
      }

      try {
        const { result } = await this.uiPaasApi.getUiPaasListDisplayGroup(params)
        this.groupPageHelper = result.pageHelper
        const formatGroupList = this.formatOriginGroup(
          result.groupField,
          result.group,
          this.groupList.length - 1
        )

        return formatGroupList
      } catch (err) {
        console.log('/uipaas/list/display/laneView/group', err)
        this.$emit('onStageView', {
          action: 'laneError',
          params: {
            msg: err,
            type: 'group'
          }
        })
        return []
      } finally {
        this.groupLoading = false
      }
    },

    getGroupDataParams(groupId, pageQueryParams = {}, page = 1) {
      const { businessType, formId, appId } = this.appInfo
      const targetGroup = this.groupListMap[groupId]
      // 默认参数，可以被覆盖
      const defaultParams = {
        businessType,
        formId,
        appId
      }
      // 泳道相关固定参数，不该被外部覆盖
      const fixParams = {
        viewType: ViewTypeEnum.PAAS_LANEVIEW,
        layoutId: this.pageInfo.layoutId,
        layoutSource: this.pageInfo.layoutSource,
        viewId: this.viewConfig.id,
        page,
        pageSize: 20,
        isUseUserConfig: this.pageInfo.isUseUserConfig
      }
      // 需要特定拼接的参数，后端把分组信息作为查询参数塞到公共的conditons里，同时需要把conditions里带的推进器参数去掉(确保不会受外部查询影响)
      // const groupConditon = {
      //   attr: this.groupField.attr,
      //   attrName: this.groupField.attrName,
      //   fieldType: this.groupField.fieldType,
      //   symbol: 'equal',
      //   value: [groupId]
      // }
      // 后端改了接口，查询分组数据的条件来自group接口
      const groupConditon = targetGroup?.origin.conditions ?? []

      const { conditions, ...restPageQueryParams } = pageQueryParams ?? {}

      return {
        ...defaultParams,
        ...restPageQueryParams,
        conditions: [...conditions, ...groupConditon],
        ...fixParams,
        isSync: false // 前端参数，确保不会因为异步导致接口请求报错
      }
    },
    /**
     * @description: 获取单泳道数据
     * @param {String|Number} groupId 泳道id
     * @param {Object} pageQueryParams 外部查询参数
     * @param {Number} page 分页
     * @return {*}
     */
    async fetchGroupData(groupId, pageQueryParams = {}, page = 1) {
      try {
        const params = this.getGroupDataParams(groupId, pageQueryParams, page)
        let groupData = []

        if (page === 1) {
          // 数据初始化
          this.$set(this.groupDataMap, `${groupId}`, {
            loading: true, // 请求状态
            dataList: [], // 卡片数据列表
            pageHelper: {}, // 分页数据
            summary: {} // 统计信息
          })
        } else {
          this.groupDataMap[groupId].loading = true
          // 这里在请求之前作下浅拷贝，防止意料之外的重复请求导致数据重复（防一手外部mixin错误重复刷新问题 BUG.20241116046）
          groupData = [...(this.groupDataMap?.[`${groupId}`]?.dataList ?? [])]
        }

        const { result } = await this.uiPaasApi.getUiPaasListDisplayData(params)
        const { summary, ...restFormatGroupData } = formatOriginGroupData(result, groupData)
        // 后端出于性能优化考虑，希望汇总字段信息只在 Page === 1 时获取
        this.groupDataMap[groupId] =
          page === 1
            ? { summary, ...restFormatGroupData }
            : { ...this.groupDataMap[groupId], ...restFormatGroupData }
      } catch (err) {
        console.log('uipaas/list/display/laneView/data', {
          err,
          groupId
        })
      } finally {
        this.groupDataMap[groupId].loading = false
      }
    },

    /**
     * @description: 获取分组和数据
     * @param {Object} pageQueryParams 外部查询参数
     * @param {Boolean} isInit 是否初始化
     * @return {*}
     */
    async getGroupAndData(pageQueryParams = {}, isInit = true) {
      const page = isInit ? 1 : this.groupPageHelper.currentPageNum + 1
      const formatGroupList = await this.fetchGroup(page)

      if (page === 1) {
        // 初始化
        this.groupList = formatGroupList
      } else {
        // 翻页
        this.groupList = [...this.groupList, ...formatGroupList]
      }

      const formatGroupListPromise = formatGroupList.map((groupItem) =>
        this.fetchGroupData(groupItem.groupId, pageQueryParams)
      )
      Promise.all(formatGroupListPromise)
    },

    // 按钮刷新
    async cardBtnRefreshHandler({ data, index, groupId }) {
      // 预览模式禁止交互
      if (this.isPreview) return

      console.log('cardBtnRefreshHandler', { data, index, groupId })
      const targetCard = this.groupDataMap[groupId]?.dataList?.[index]

      if (!targetCard) return

      try {
        const { result } = await this.uiPaasApi.getUiPaasLaneCardButton({
          dataId: data.id,
          appId: this.appInfo.appId,
          saasMark: this.appInfo.saasMark,
          businessType: this.appInfo.businessType,
          subBusinessType: this.appInfo.subBusinessType,
          distributorMark: this.appInfo.distributorMark,
          formId: this.appInfo.formId,
          menuId: this.appInfo.menuId,
          defaultGroup: this.pageInfo.defaultGroup,
          listGroupId: this.pageInfo.listGroupId,
          layoutId: this.pageInfo.layoutId,
          viewId: this.viewConfig.id,
          viewType: this.viewConfig.type
        })

        const btns = result.cardButton ?? []
        this.$set(targetCard, 'btns', btns)
      } catch (err) {}
    },
    // 卡片穿透
    onLaneDetail({ isLinkOther = false, otherDataInfo = {}, otherDataField = {}, groupId, index }) {
      // 预览模式禁止交互
      if (this.isPreview) return

      console.log('cardOpenDetail', { isLinkOther, otherDataInfo, groupId, index })
      let params
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      if (isLinkOther) {
        params = {
          linkOther: true,
          id: otherDataInfo.id,
          dataId: otherDataInfo.id,
          type: 'detail',
          formId: otherDataInfo.formId,
          distributorMark: otherDataInfo.distributorMark || otherDataField.distributorMark,
          subBusinessType: otherDataInfo.subBusinessType,
          businessType: otherDataInfo.businessType,
          saasMark: otherDataInfo.saasMark,
          menuId: otherDataInfo.menuId,
          parentId: otherDataInfo.parentId,
          callinfoType: this.appInfo.businessType,
          appId: this.appInfo.appId
        }
        // 关联穿透不翻页
        this.setDetailPageDataList()
      } else {
        params = {
          id: data.id,
          dataId: data.id,
          appId: this.appInfo.appId,
          saasMark: this.appInfo.saasMark,
          formId: this.appInfo.formId,
          businessType: this.appInfo.businessType,
          menuId: this.appInfo.menuId,
          callinfoType: this.appInfo.businessType,
          type: 'detail',
          subBusinessType: this.appInfo.subBusinessType
        }
        this.setDetailPageDataList(groupId)
      }
      // 复用列表页公共穿透逻辑
      this.$emit('onStageView', {
        action: 'laneDetail',
        params
      })
    },
    /** cardButton start */
    /**
     * @description: 卡片按钮点击，云呼叫等，有些是来自列表页的操作，有些是复用详情页操作
     * 按钮事件：https://alidocs.dingtalk.com/i/nodes/93NwLYZXWyg4BrL6Tl30be4ZJkyEqBQm?utm_scene=team_space&sideCollapsed=true&iframeQuery=utm_source%253Dportal%2526utm_medium%253Dportal_new_tab_open&corpId=dinge3fa697f86d461d2
     *    option_1 - 关注
     *    option_2 - 提醒
     *    option_4 - 跟进记录
     *    archived - 归档
     *    cancelArchived - 取消归档
     *    sendMail - 发邮件
     *    copy - 复制
     *    edit - 编辑
     *    del - 删除
     *    newVersion - 新版本
     *    TODO：云呼等
     * @param {*} btn
     * @param {*} groupId
     * @param {*} index
     * @param {*} event 按钮点击事件
     * @return {*}
     */
    @xbb.debounceWrap()
    cardBtnClickHandler({ btn = {}, groupId, index = 0 }, event) {
      if (this.isPreview) return

      // onXX是需要外部事件处理，其他的内部处理即可
      switch (btn.attr) {
        case 'option_3': // 云呼 aliCall 呼叫中心 call
          this.onLaneCall({ btn, groupId, index }, event)
          break
        case 'option_2': // 提醒
          this.onLaneRemind({ btn, groupId, index }, event)
          break
        case 'option_4': // 跟进记录
          this.onLanePreview({ btn, groupId, index }, event)
          break
        case 'archived': // 归档
          this.btnClickForArchived({ btn, groupId, index }, 1)
          break
        case 'cancelArchived': // 取消归档
          this.btnClickForArchived({ btn, groupId, index }, 2)
          break
        case 'sendMail': // 发送邮件
          this.btnClickForSendMail({ btn })
          break
        case 'copy': // 复制
          this.btnClickForCopy({ btn, groupId, index })
          break
        case 'edit': // 编辑
        case 'del': // 删除
        case 'cancel': // 发票作废
          const customAttr = `option_${btn.attr}`
          this.onLaneButton({ btn, groupId, index }, customAttr)
          break
        case 'option_add_payplan': // 添加付款单
        case 'option_add_purchase_invoice': // 新建进项发票
        case 'option_add_invoice': // 开发票
        case 'option_add_payment': // 添加回款单
        case 'option_write_off_prepayment':
        case 'option_payment_write_off': // 应收款核销
        case 'option_write_off_prepay': // 预付款核销
        case 'option_red': // 红冲
        case 'option_invoice_invoice_cancel': // 进项发票作废
        case 'option_purchase_invoice_revert': // 进项发票还原
        case 'option_cancel': // 作废发票
        case 'option_invoice_revert': // 发票还原
          this.onLaneButton({ btn, groupId, index })
          break
        case 'revertCancel': // 还原
          this.onLaneButton({ btn, groupId, index }, 'option_invoice_revert')
          break
        case 'badDebt': // 坏帐
        case 'return': // 回退
          this.btnClickForBadOrReturnDebt({ btn, groupId, index })
          break
        case 'linkContract': // 资金相关-关联合同
          this.btnClickForLinkContract({ btn, groupId, index })
          break
        case 'red': // 红冲 后端按钮有的来自列表页有的来自详情，两方的key可能不同，逻辑还存在差异，这里都兼容下
        case 'redBalance': // 红冲，退到余额
        case 'redReceivables': // 红冲应收款
          this.btnClickForRed({ btn, groupId, index })
          break
        case 'paymentWriteOff': // 核销应收
          this.btnClickForPaymentWriteOff({ btn, groupId, index })
          break
        case 'newVersion': // 报价单新版本
          this.btnClickForNewVersion({ btn, groupId, index })
          break
        case 'invalid': // 线索无效
          this.btnClickForInvalidReason({ btn, groupId, index })
          break
        case 'follow': // 线索跟进中
          this.btnClickForSalesFollow({ btn, groupId, index })
          break
        case 'print': // 打印
          this.btnClickForPrint({ btn, groupId, index })
          break
        case 'option_1': // 关注
          this.btnClickForForce({ btn, groupId, index })
          break
        case 'contractCompare': // 合同比对
          this.onLaneContractCompare({ btn, groupId, index })
          break
        case 'coolAppUnbindGroup': // 机会-解绑群
          this.btnClickForCoolAppUnbindGroup({ btn, groupId, index })
          break
        // 工单、项目相关
        case 'addWorkTimeRecord': // 工时记录
        case 'addWorkOrder': // 新建工单
          this.btnClickForWorkOrderV2LinkAdd({ btn, groupId, index })
          break
        case 'changeMilepost': // 转换里程碑
        case 'changeTask':
        case 'changeRisk':
          this.btnClickForWorkOrderV2ChangeMilepost({ btn, groupId, index })
          break
        case 'cancelTask': // 取消任务
        case 'cancelRisk': // 取消风险
        case 'cancelMilepost': // 取消里程碑
          this.btnClickForWorkOrderV2CancelMilepost({ btn, groupId, index })
          break
        case 'adjustTime': // 任务调整时间
          this.btnClickForWorkOrderV2AdjustTime({ btn, groupId, index })
          break
        case 'dingGroupSetting': // 钉钉群设计
          this.btnClickForWorkOrderV2DingGroupSetting({ btn, groupId, index })
          break
        // 企微特殊按钮
        case 'option_17': // 企微会话
          this.onLaneWeChat({ btn, groupId, index }, event)
          break
        default:
          this.$message({
            type: 'warning',
            message: this.$t('message.notSupportOpt', {
              business: this.$t('layoutDesign.laneView'),
              opt: btn.value
            })
          })
          break
      }
    },
    // 云呼和呼叫中心
    onLaneCall({ btn = {}, groupId, index = 0 }, event) {
      const type = btn.buttonExtra?.isCall ? 'call' : 'aliCall'
      const params = {
        type,
        callParam: btn.buttonExtra?.callParam || []
      }

      this.$emit('onStageView', {
        action: 'laneCall',
        params,
        event
      })
    },
    // 后端给的attr，有的是列表页的attr，有的是详情页的attr。会导致相同操作attr不同而不好复用列表已有逻辑。这里加个自定义的 customAttr 去灵活转换attr
    onLaneButton({ btn = {}, groupId, index = 0 }, customAttr = '') {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const attr = customAttr || btn.attr
      // const attr = prefix ? `${prefix}_${btn.attr}` : btn.attr
      let baseParams = {
        type: 'button',
        attr,
        id: data.id
      }
      // 有些特殊业务需要额外的信息，比如“新建付款单”的businessType和表单本身不同
      if (btn.buttonExtra) {
        baseParams = {
          ...baseParams,
          ...btn.buttonExtra
        }
      }

      this.$emit('onStageView', {
        action: 'laneButton',
        params: baseParams
      })
    },
    onLaneWeChat({ btn = {}, groupId, index = 0 }, event) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      const baseParams = {
        type: 'wechat',
        attr: btn.attr,
        id: data.id
      }
      this.$emit('onStageView', {
        action: 'laneWechat',
        params: baseParams,
        event
      })
    },
    // 红冲、红冲退到余额、红冲应收款
    async btnClickForRed({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      if (
        [
          commonBusinessType.PAYMENTAPI,
          commonBusinessType.PAYMENT_SHEET,
          commonBusinessType.PAYPLAN,
          commonBusinessType.PAY_SHEET,
          commonBusinessType.OTHER_INCOME,
          commonBusinessType.OTHER_EXPENSES
        ].includes(this.appInfo.businessType)
      ) {
        // 新建红冲回款单
        this.formDetailLinkAdd({
          dataIdList: [data.id],
          saasMark: 1,
          businessType: btn.linkBusinessType,
          linkBusinessType: this.appInfo.businessType,
          saveUrl: 'linkAddCollectionSave'
        })
      } else if (
        [commonBusinessType.INVOICE, commonBusinessType.PAY_INVOICE].includes(
          this.appInfo.businessType
        )
      ) {
        const { businessType, saasMark, appId, menuId, formId, distributorMark } = this.appInfo
        this.formDataEdit({
          appId,
          menuId,
          formId,
          businessType,
          distributorMark,
          saasMark,
          dataId: data.id,
          saasSpecialParamPojo: { fromRed: 1 } // 红冲发票请求编辑页参数
        })
      }
    },
    // 核销应收
    async btnClickForPaymentWriteOff({ btn = {}, groupId, index = 0 }) {
      const { saasMark, distributorMark } = this.appInfo
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      const params = {
        linkBusinessType: 702, // 详情页这里是写死的
        businessType: btn.linkBusinessType,
        saasMark,
        distributorMark,
        getFormDataUrl: 'paymentWriteOff',
        saveUrl: 'paymentWriteOff',
        dataIdList: [Number(data.id)]
      }

      this.payMentWriteOff(params)
    },
    // 合同比对
    onLaneContractCompare({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const dataList = [data]
      this.$emit('onStageView', {
        action: 'laneContractCompare',
        params: dataList
      })
    },
    // 关注
    async btnClickForForce({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      // const { titleIcons } = data
      // const isForced = titleIcons.some((titleIcon) => titleIcon.attr === 'option_1')
      const targetEnable = xbb.reverseNum(btn.enable)
      const params = {
        businessType: this.appInfo.businessType,
        formId: this.appInfo.formId,
        dataIdList: [Number(data.id)],
        focus: targetEnable
      }
      try {
        const res = await batchFocus(params)
        this.$message({
          type: 'success',
          message: res.msg || this.$t('message.operateSuccessSymbol')
        })
        // 刷新列表
        // this.refreshTargetGroupDataHandler([groupId])
        // 直接前端更新按钮
        const targetBtnIndex = data.btns.findIndex((button) => button.attr === btn.attr)
        const targetBtn = data.btns[targetBtnIndex]
        const icon = btnIconStatusChange(targetBtn.icon, targetEnable)
        const newBtn = {
          ...targetBtn,
          enable: targetEnable,
          icon
        }
        // 这么写是为了让 btns 变化后能触发 dropdown 重绘
        data.btns.splice(targetBtnIndex, 1, newBtn)

        let titleIcons = data.titleIcons
        if (!targetEnable) {
          titleIcons = data.titleIcons.filter((icon) => icon.attr !== btn.attr)
          this.$set(data, 'titleIcons', titleIcons)
        } else {
          titleIcons = [
            ...data.titleIcons,
            {
              attr: btn.attr,
              action: 'titleIcon',
              value: {
                icon
              }
            }
          ]
        }
        this.$set(data, 'titleIcons', titleIcons)
      } catch (err) {}
    },
    // 提醒

    async onLaneRemind({ btn = {}, groupId, index = 0 }, event) {
      const cardData = this.groupDataMap?.[groupId]?.dataList?.[index]
      const params = {
        type: 'remind',
        attr: btn.attr,
        id: cardData.id,
        appId: this.appInfo.appId,
        status: btn.enable,
        businessType: btn.buttonExtra.businessType
      }
      // 复用外部公共提醒组件
      this.$emit('onStageView', {
        action: 'laneRemind',
        params,
        event
      })
    },
    async btnClickForArchived({ btn = {}, groupId, index = 0 }, archived = 1) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      // 判断当前是归档还是取消归档
      const cancel = archived === 1 ? '' : this.$t('operation.cancel')

      try {
        await this.$confirm(
          `${this.$t('formDataDetail.confirm') + cancel + this.$t('operation.pigeonhole')} “${
            data.title.value
          }”?`,
          `${cancel + this.$t('formDataDetail.archiveConfirmatioan')}`,
          {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          }
        )

        const params = {
          businessType: this.appInfo.businessType,
          dataIdList: [data.id],
          archived: archived,
          formId: this.appInfo.formId,
          saasMark: this.appInfo.saasMark,
          isBatch: 0 // 日志记录需要的批量判断
        }
        const res = await batchArchive(params)

        this.$message({
          type: 'success',
          message: res.msg || this.$t('message.operateSuccessSymbol')
        })
        // 刷新列表
        this.refreshTargetGroupDataHandler([groupId])
      } catch (err) {}
    },
    async btnClickForPrint({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      try {
        await PrintDialogPromise({
          formQuery: {
            ...this.appInfo,
            dataId: data.id
          },
          extraParams: btn.buttonExtra ?? {}
        })
      } catch (err) {
        console.log('btnClickForPrint cancel')
      }
    },
    // 跟进记录
    onLanePreview({ btn = {}, groupId, index = 0 }, event) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const params = {
        // 跟进记录
        attr: 'lastFollow',
        id: data.id,
        status: btn.buttonExtra?.status,
        type: 'preview',
        // 上边是给请求跟进记录列表的参数，下边是给跟进记录跳转目标表单详情的参数...这么拼是为了确保外部的老逻辑能正常走通
        dataId: data.id,
        appId: this.appInfo.appId,
        saasMark: this.appInfo.saasMark,
        formId: this.appInfo.formId,
        businessType: this.appInfo.businessType,
        menuId: this.appInfo.menuId,
        callinfoType: this.appInfo.businessType,
        subBusinessType: this.appInfo.subBusinessType
      }

      this.setDetailPageDataList(groupId)

      // 复用外部公共跟进记录组件
      this.$emit('onStageView', {
        action: 'lanePreview',
        params,
        event
      })
    },
    // 卡片复制
    btnClickForCopy({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      const params = {
        ...this.appInfo,
        dataId: data.id
      }

      this.formDetailCopy(params)
    },
    // 报价单新版本
    btnClickForNewVersion({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      const params = {
        ...this.appInfo,
        dataId: data.id,
        newVersionAttr: true
      }

      this.formDetailNewVersion(params)
    },
    // 资金-坏账、还原
    async btnClickForBadOrReturnDebt({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      if (btn.attr === 'badDebt') {
        this.formDetailLinkAdd({
          dataIdList: [data.id],
          saasMark: this.appInfo.saasMark,
          businessType: btn.linkBusinessType,
          linkBusinessType: this.appInfo.businessType,
          saveUrl: 'linkAddCollectionSave'
        })
      } else if (btn.attr === 'return') {
        const params = {
          appId: this.appInfo.appId,
          dataId: data.id,
          saasMark: this.appInfo.saasMark,
          businessType: this.appInfo.businessType,
          subBusinessType: this.appInfo.subBusinessType,
          menuId: this.appInfo.menuId,
          formId: this.appInfo.formId,
          distributorMark: this.appInfo.distributorMark
        }
        try {
          await this.$confirm(
            this.$t('formDataDetail.areYouSaureToRestoreData'),
            this.$t('message.confirmTitle'),
            {
              confirmButtonText: this.$t('operation.confirm'),
              cancelButtonText: this.$t('operation.cancel'),
              type: 'warning'
            }
          )

          let promise = ''
          if (this.appInfo.businessType === 701) {
            promise = setPaymentBadDebtReturn(params)
          } else {
            promise = setPayPlanBadDebtReturn(params)
          }

          const res = await promise
          this.$message({
            type: 'success',
            message: res.msg || this.$t('message.operateSuccessSymbol')
          })

          // 刷新泳道(因为状态可能被作为分组，这里需要全刷新)
          setTimeout(() => {
            this.refresh(this.pageQueryParams)
          }, globalConst.DELAY_TIME)
        } catch (err) {
          console.log('btnClickForHBadOrReturnDebt', err)
        }
      }
    },
    btnClickForWorkOrderV2LinkAdd({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      const params = {
        dataIdList: [data.id],
        saasMark: 1,
        distributorMark: 0, // 经销商业务的要有distributorMark
        businessType: btn.linkBusinessType,
        subBusinessType: this.appInfo.businessType,
        linkBusinessType: this.appInfo.businessType
      }

      this.formDetailLinkAdd(params)
    },
    async btnClickForWorkOrderV2ChangeMilepost({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const typeName =
        +this.appInfo.businessType === 20900
          ? this.$t('workOrderV2.task')
          : this.$t('workOrderV2.risk')
      const msgMap = {
        changeMilepost: {
          title: this.$t('workOrderV2.changeToTarget', { target: this.$t('workOrderV2.milepost') }),
          msg: this.$t('workOrderV2.changeSourceToTarget', {
            source: typeName,
            target: this.$t('workOrderV2.milepost')
          })
        },
        changeTask: {
          title: this.$t('workOrderV2.changeToTarget', { target: typeName }),
          msg: this.$t('workOrderV2.changeSourceToTarget', {
            source: this.$t('workOrderV2.milepost'),
            target: typeName
          })
        },
        changeRisk: {
          title: this.$t('workOrderV2.changeToTarget', { target: typeName }),
          msg: this.$t('workOrderV2.changeSourceToTarget', {
            source: this.$t('workOrderV2.milepost'),
            target: typeName
          })
        }
      }
      const msgInfo = msgMap[btn.attr]
      if (!msgInfo) return

      try {
        await this.$confirm(msgInfo.msg, msgInfo.title, {
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        })
        const params = {
          id: data.id,
          businessType: this.appInfo.businessType,
          menuId: this.appInfo.menuId,
          saasMark: this.appInfo.saasMark
        }
        const res = await updateChangeMilepost(params)
        this.$message({
          type: 'success',
          message: res.msg
        })
        // 刷新泳道
        setTimeout(() => {
          this.refresh(this.pageQueryParams)
        }, globalConst.DELAY_TIME)
      } catch (err) {
        console.log('btnClickForWorkOrderV2ChangeMilepost', err)
      }
    },
    async btnClickForWorkOrderV2CancelMilepost({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const typeNameMap = {
        cancelMilepost: this.$t('workOrderV2.milepost'),
        cancelTask: this.$t('workOrderV2.task'),
        cancelRisk: this.$t('workOrderV2.risk')
      }
      const typeName = typeNameMap[btn.attr]
      if (!typeName) return

      const title = this.$t('workOrderV2.cancelTargetHit', { target: typeName })
      const msg = this.$t('workOrderV2.cancelTarget', { target: typeName })
      try {
        await this.$confirm(msg, title, {
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        })
        const params = {
          dataId: data.id,
          status: '4',
          businessType: this.appInfo.businessType,
          saasMark: this.appInfo.saasMark,
          menuId: this.appInfo.menuId
        }
        const res = await updateStatus(params)
        this.$message({
          message: res.msg,
          type: 'success'
        })
        // 刷新泳道
        setTimeout(() => {
          this.refresh(this.pageQueryParams)
        }, globalConst.DELAY_TIME)
      } catch (err) {
        console.log('btnClickForWorkOrderV2CancelMilepost', err)
      }
    },
    async btnClickForWorkOrderV2AdjustTime({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const query = {
        ...this.appInfo,
        dataId: data.id
      }
      try {
        const promiseRes = await AdjustTimePromise({
          query
        })
        const adjustTimeParams = {
          type: promiseRes.data.type, // 1：提前；2：推迟
          num: promiseRes.data.num, // 推迟多长时间
          unit: promiseRes.data.unit, // 时间单位 1：小时；2：天
          unFinish: promiseRes.data.unFinish, // 不对状态=已完成 的数据进行调整
          businessType: +this.appInfo.businessType,
          saasMark: +this.appInfo.saasMark,
          menuId: +this.appInfo.menuId
        }
        const apiMap = {
          20800: {
            api: updateProjectTime,
            params: {
              projectId: data.id,
              ...adjustTimeParams
            }
          },
          default: {
            api: updateTaskTime,
            params: {
              taskId: data.id, // 任务id
              ...adjustTimeParams
            }
          }
        }

        const { api, params } = apiMap[this.appInfo.businessType] ?? apiMap.default

        const apiRes = await api(params)
        promiseRes?.callback?.()
        this.$message({
          message: apiRes.msg,
          type: 'success'
        })

        // 只需要刷新当前泳道
        this.refreshTargetGroupDataHandler([groupId])
      } catch (err) {
        console.log('btnClickForWorkOrderV2AdjustTime', err)
      }
    },
    async btnClickForWorkOrderV2DingGroupSetting({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      try {
        const result = await DingGroupSetPromise({
          query: { dataId: data.id }
        })
        if (result.refresh) {
          // 只需要刷新当前泳道
          this.refreshTargetGroupDataHandler([groupId])
        }
      } catch (err) {
        console.log('btnClickForWorkOrderV2DingGroupSetting', err)
      }
    },
    // 资金-关联合同
    btnClickForLinkContract({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      const { menuId, saasMark, appId, formId, businessType, distributorMark, subBusinessType } =
        this.appInfo
      const params = {
        appId,
        menuId,
        formId,
        saasMark,
        distributorMark,
        linkBusinessType: 201,
        businessType,
        subBusinessType,
        dataId: data.id
      }
      this.linkContract(params)
    },
    // 线索跟进中
    async btnClickForSalesFollow({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      try {
        const res = await clueFollow({
          dataId: data.id
        })
        this.$message({
          message: res.msg || this.$t('message.operateSuccessSymbol'),
          type: 'success'
        })
        // 刷新泳道(因为线索状态可能被作为分组，这里需要全刷新)
        setTimeout(() => {
          this.refresh(this.pageQueryParams)
        }, globalConst.DELAY_TIME)
      } catch (err) {
        console.log('btnClickForSalesFollow', err)
      }
    },
    // 线索无效
    async btnClickForInvalidReason({ btn = {}, groupId, index = 0 }) {
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]
      const { appId, saasMark, businessType, subBusinessType, menuId, formId } = this.appInfo

      try {
        await InvalidReasonPromise({
          query: {
            appId,
            dataId: data.id,
            saasMark,
            businessType,
            subBusinessType,
            menuId,
            formId
          }
        })
        // 刷新泳道(因为线索状态可能被作为分组，这里需要全刷新)
        setTimeout(() => {
          this.refresh(this.pageQueryParams)
        }, globalConst.DELAY_TIME)
      } catch (err) {
        console.log('btnClickForInvalidReason', err)
      }
    },
    // 发邮件
    btnClickForSendMail({ btn = {} }) {
      if (!btn.buttonExtra.mailAddress) {
        this.$message.error(this.$t('mail.mustBindMail'))
      } else {
        initMailWriteFullscreenDialogInstance({
          recipientList: [
            {
              name: btn.buttonExtra.bizName || btn.buttonExtra.mailAddress.split('@')[0],
              mailAddress: btn.buttonExtra.mailAddress
            }
          ]
        })
      }
    },
    async btnClickForCoolAppUnbindGroup({ btn = {}, groupId, index = 0 }) {
      const groupName = btn.buttonExtra?.title || btn.buttonExtra?.groupName || ''
      const data = this.groupDataMap?.[groupId]?.dataList?.[index]

      try {
        await this.$confirm(
          this.$t('coolApp.confirmUnbind', { groupName }),
          this.$t('coolApp.unbindGroup'),
          {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          }
        )

        const res = await this.uiPaasApi.unbindGroup({ dataId: data.id })
        this.$message({
          type: 'success',
          message: res.msg || this.$t('message.operateSuccessSymbol')
        })
        this.refreshTargetGroupDataHandler([groupId])
      } catch (err) {
        console.log('btnClickForCoolAppUnbindGroup', err)
      }
    },
    /** cardButton end */
    setDetailPageDataList(groupId) {
      // 不传值走清空
      if (!groupId) {
        this['SET_DATA_ID_LIST']([])
      } else {
        const dataIdList = this.groupDataMap?.[groupId]?.dataList?.map((card) => {
          return card.id
        })
        this['SET_DATA_ID_LIST'](dataIdList)
      }
    },
    // 泳道本身交互
    onLaneAdd({ btn, groupId }) {
      if (this.isPreview) return

      console.log('headerBtnClick', { btn, groupId })
      if (btn.attr === 'add') {
        const params = {
          ...this.appInfo,
          defaultAttrInfo: [
            {
              attr: this.groupField.attr,
              fieldType: this.groupField.fieldType,
              value: groupId,
              stageProcessId: this.stageProcessId
            }
          ]
        }
        this.$emit('onStageView', {
          action: 'laneAdd',
          params
        })
      }
    },

    /**
     * @description: 右拉加载
     * @return {*}
     */
    loadMoreGroupHandler() {
      if (this.rightScrollDisabled) return

      this.getGroupAndData(this.pageQueryParams, false)
    },
    /**
     * @description: 下拉加载更多
     * @param {String|Number} groupId 分组id
     * @return {*}
     */
    loadMoreGroupDataHandler({ groupId }) {
      if (this.isPreview) return

      const page = this.groupDataMap[groupId]?.pageHelper?.currentPageNum + 1 || 1
      this.fetchGroupData(groupId, this.pageQueryParams, page)
    },
    // 刷新指定分组数据
    refreshTargetGroupDataHandler(groupIds = []) {
      if (this.isPreview || !Array.isArray(groupIds)) return

      const page = 1
      const promiseList = groupIds.map((groupId) =>
        this.fetchGroupData(groupId, this.pageQueryParams, page)
      )

      return Promise.all(promiseList)
    },
    /**
     * @description: 初始化方法，供外部刷新调用和初始化数据调用
     * @param {Object} pageQueryParams 外部列表查询参数
     * @return {*}
     */
    refresh(pageQueryParams) {
      // 初始化时更新外部列表参数
      this.pageQueryParams = pageQueryParams
      // 刷新时清空列表
      this.groupList = []
      this.getGroupAndData(pageQueryParams)
    },

    /** 预览相关 start */
    mockFetchGroup(previewParams) {
      this.groupList = [] // 清空
      this.groupLoading = true
      const { pageHelper, groupField, group } = mockGetUiPaasLaneViewGroup(previewParams) // TODO: previewParams里的内容根据mock-data里的实际需求来，或者考虑通过previewConfig传，那么就可能不需要previewParams

      this.groupPageHelper = pageHelper

      const formatGroupList = group
        ? this.formatOriginGroup(groupField, group, this.groupList.length - 1)
        : []
      this.groupLoading = false
      return formatGroupList
    },

    mockFetchGroupData(groupItem, previewParams, formExplainList) {
      // 数据初始化

      const {
        explainList = [], // 解释
        viewConfig = {}, // 视图配置，包括aggregateField、columnSortField、columnVisibleField、titleField
        dataList = [], // 卡片数据列表，每个对象里，主要信息有data、id、otherInfo
        aggregateList = [], // 全量的统计信息
        pageHelper = {} // 分页
      } = mockGetUiPaasLaneViewData(previewParams, formExplainList)

      const groupData = formatOriginGroupData({
        explainList,
        viewConfig,
        dataList,
        aggregateList,
        pageHelper
      })
      this.groupDataMap[groupItem.groupId] = groupData
      this.groupDataMap[groupItem.groupId].loading = false
      // this.$set(this.groupDataMap, `${previewParams.groupId}`, {
      //   loading: true, // 请求状态
      //   dataList: dataList, // 卡片数据列表
      //   pageHelper: pageHelper, // 分页数据
      //   summary: {} // 统计信息
      // })
    },

    preview(previewParams, formExplainList) {
      const formatGroupList = this.mockFetchGroup(previewParams)

      formatGroupList?.forEach((groupItem) => {
        this.mockFetchGroupData(groupItem, previewParams, formExplainList)
      })
      this.groupList = formatGroupList?.filter((item) => {
        // 确保 item.origin 存在，并检查 isOther 属性
        return !(item.origin && item.origin.isOther === 1)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$__block-gap: 12px;
$__bottom-scroll-width: 10px;

.stage-view {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: $__block-gap;
  overflow: hidden;
  background: $base-white;

  &__wrapper {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: $__block-gap;
    width: 100%;
    height: calc(100% + #{$__bottom-scroll-width}); // 确保滚动条不会影响高度
    overflow-x: scroll; // 确保滚动条不会影响高度
    overflow-y: hidden;

    // 处理滚动样式
    // &::-webkit-scrollbar-thumb {
    //   background: transparent;
    //   transition: background-color 120ms ease-out;
    // }
    // &:hover::-webkit-scrollbar-thumb {
    //   background: $neutral-color-4;
    //   transition: background-color 340ms ease-out;
    // }
  }

  &__loading {
    box-sizing: border-box;
    width: 16px;
    height: 100%;
    margin-left: $__block-gap;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: center;
    writing-mode: tb-rl;
  }
}
.no-data {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  margin-bottom: $__bottom-scroll-width;
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
}
</style>
