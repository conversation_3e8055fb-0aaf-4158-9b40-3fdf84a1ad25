<!-- 新泳道卡片 -->
<template>
  <div class="stage-lane__lane-card" @click="openDetailHandler">
    <div class="lane-card__header">
      <field-content
        class="lane-card__header__title"
        :field-info="title.fieldInfo"
        :tags="title.tags"
        :text-class="title.class"
        :value="title.value"
        @openDetail="openDetailHandler"
        @openOtherDetail="(otherData) => openOtherDetailHandler(otherData, title.fieldInfo)"
      />
      <div v-if="filterTitleTexts.length" class="lane-card__header__text-tags">
        <span
          v-for="titleText in filterTitleTexts"
          :key="titleText.attr"
          :style="{ color: titleText.value.color ?? titleText.value.backgroundColor }"
          >{{ titleText.value.value ?? '--' }}</span
        >
      </div>
      <div v-if="filterTitleIcons.length" class="lane-card__header__icons">
        <i
          v-for="icon in filterTitleIcons"
          :key="icon.attr"
          class="t-iconfont"
          :class="icon.value?.icon"
        >
        </i>
      </div>
      <el-dropdown
        v-if="op.more"
        ref="btnCardRef"
        class="lane-card__header__btns"
        :hide-on-click="false"
        placement="bottom-start"
        trigger="click"
        @visible-change="dropdownChangeHandler"
      >
        <span class="btns-content" @click.stop>
          <i class="icon-more-line t-iconfont"></i>
        </span>
        <el-dropdown-menu
          ref="btnCardMenuRef"
          slot="dropdown"
          class="stage-lane__lane-card__btn-menu"
        >
          <el-scrollbar class="stage-lane__lane-card__btn-menu__scrollbar">
            <template v-if="btns && btns.length">
              <el-dropdown-item
                v-for="(cardBtn, cardBtnIndex) in btns"
                :key="cardBtnIndex + cardBtn.attr"
                class="stage-lane__lane-card__btn-menu__item"
                :class="{ active: cardBtn.enable }"
                :command="cardBtn"
                :disabled="!!cardBtn.disabled"
                @click.native="btnClickHandler(cardBtn, $event)"
              >
                <span class="btn-menu__item__icon"
                  ><i class="t-iconfont" :class="cardBtn.icon"></i
                ></span>
                <span class="btn-menu__item__text">{{ getCardBtnName(cardBtn) }}</span>
              </el-dropdown-item>
            </template>
            <el-dropdown-item
              v-else
              key="empty"
              class="stage-lane__lane-card__btn-menu__item"
              disabled
            >
              <span class="btn-menu__item__text">{{ $t('message.noOperate') }}</span>
            </el-dropdown-item>
          </el-scrollbar>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="lane-card__content">
      <div
        v-for="contentField in contents"
        :key="contentField.fieldInfo.attr"
        class="lane-card__content__field lane-card__content__line"
      >
        <div class="field-title">
          <span class="field-title__name">{{ contentField.fieldInfo.attrName }}</span>
          <span class="field-title__dot">:</span>
        </div>
        <field-content
          class="field-content"
          :field-info="contentField.fieldInfo"
          :tags="contentField.tags"
          :text-class="contentField.class"
          :value="contentField.value"
          @openDetail="openDetailHandler"
          @openOtherDetail="
            (otherData) => openOtherDetailHandler(otherData, contentField.fieldInfo)
          "
        />
      </div>
      <div
        v-for="tagField in filterTagFields"
        :key="tagField.fieldInfo.attr"
        class="lane-card__content__line lane-card__content__tag"
      >
        <v-tag v-for="tag in tagField.value" :key="tag.id" :color="tag.color" :content="tag.name" />
      </div>
    </div>
  </div>
</template>

<script>
import FieldContent from '@/components/list/container-view/stage-view/field-content.vue'
import xbb from '@xbb/xbb-utils'
import VTag from '@/components/base/v-tag.vue'

export default {
  name: 'UiPaasLaneCard',

  components: {
    FieldContent,
    VTag
  },

  props: {
    title: {
      type: Object,
      default: () => ({}) // { fieldInfo, value, tags }
    },
    titleTexts: {
      // 标题文本标签，如“超时提醒”
      type: Array,
      default: () => []
    },
    titleIcons: {
      // 标题图标列表，与按钮状态相关，目前只有“关注”回显
      type: Array,
      default: () => []
    },
    contents: {
      // 卡片摘要列表
      type: Array,
      default: () => [] // [{ fieldInfo, value, tags }]
    },
    tags: {
      // 标签字段列表
      type: Array,
      default: () => [] //  [{ fieldInfo, value }]
    },
    btns: {
      // 更多按钮列表
      type: Array,
      default: () => []
    },
    op: {
      // 卡片的一些交互权限控制
      type: Object,
      default: () => ({
        drag: true, // 拖拽
        more: true, // 更多按钮
        through: true // 穿透详情
      })
    }
  },

  computed: {
    filterTagFields() {
      // 标签字段只渲染有值的
      return this.tags.filter((tag) => tag.value?.length)
    },
    // 只回显存在激活状态且至多展示2个图标
    filterTitleIcons() {
      return this.titleIcons
        .filter((titleIcon) => {
          return titleIcon.value
        })
        .slice(0, 2)
    },
    // 文本标签至多回显一个
    filterTitleTexts() {
      return this.titleTexts.slice(0, 1)
    }
  },

  watch: {
    btns: {
      deep: true,
      handler(val) {
        // 操作内容变化时，强制重绘下popper
        this.reRenderDropdownPopper(this.$refs.btnCardMenuRef)
      }
    }
  },

  methods: {
    // 重绘dropdown的popper位置，确保位置正确不偏移
    reRenderDropdownPopper(popperRef) {
      if (!popperRef || !popperRef.popperJS) return
      this.$nextTick(() => {
        popperRef.popperJS.state.position = popperRef.popperJS._getPosition(
          popperRef.popperJS._popper,
          popperRef.popperJS._reference
        )
        popperRef.popperJS.update()
      })
    },
    getTagFieldStyle(tagValue) {
      return {
        color: `${tagValue.color}`
      }
    },
    getCardBtnName(cardBtn) {
      // 没有enable或者enable为0，直接走后端返回的回显
      // enable为1的，说明存在同按钮状态转换，后端目前不返回文案，前端处理下
      const nameMap = {
        option_2: {
          1: this.$t('CRM.viewRemind'),
          0: this.$t('appModule.remind')
        },
        option_1: {
          1: this.$t('operation.offFocus'),
          0: this.$t('operation.focus')
        }
      }
      const name =
        typeof cardBtn.enable !== 'undefined' && nameMap[cardBtn.attr]
          ? nameMap[cardBtn.attr][+cardBtn.enable]
          : cardBtn.value
      return name
    },
    /**
     * @description: 按钮点击
     * 按钮事件：
     *    option_1 - 关注
     *    option_2 - 提醒
     *    option_4 - 跟进记录
     *    archived - 归档
     *    cancelArchived - 取消归档
     *    sendMail - 发邮件
     *    copy - 复制
     *    edit - 编辑
     *    del - 删除
     *    TODO：云呼等
     * @param {*} btn
     * @return {*}
     */
    btnClickHandler(cardBtnItem, event) {
      this.$emit('btnClick', cardBtnItem, event)
    },
    dropdownChangeHandler(val) {
      if (val && !this.btns?.length) {
        // 展开时没有值则请求
        this.$emit('btnRefresh')
      }
    },
    //
    openOtherDetailHandler(otherData, contentField) {
      if (!this.op.through) {
        return
      }
      this.$emit('openDetail', {
        isLinkOther: true,
        otherDataInfo: otherData,
        otherDataField: contentField
      })
    },
    @xbb.debounceWrap(200)
    openDetailHandler() {
      if (!this.op.through) {
        return
      }
      this.$emit('openDetail', {})
    },
    // 外部调用，关闭dropdown
    btnDropdownClose() {
      this.$refs.btnCardRef?.hide()
    }
  }
}
</script>

<style lang="scss" scoped>
$__card-padding: 12px;
$__title-height: 20px;
$__field-height: 18px;
$__block-gap: 8px;
$__card-btn-height: 32px;

.stage-lane__lane-card {
  box-sizing: border-box;
  width: 100%;
  min-height: calc(#{$__card-padding} * 2 - #{$__title-height});
  padding: $__card-padding;
  background: $base-white;
  border: 1px solid $neutral-color-3;
  border-radius: 8px;
  &:hover {
    cursor: pointer;
    border-color: $brand-base-color-6;
  }

  .lane-card__header {
    position: relative;
    box-sizing: border-box;
    display: flex;
    gap: $__block-gap;
    width: 100%;
    height: $__title-height;
    padding-right: calc(#{$__title-height} + 4px);
    line-height: $__title-height;
    .lane-card__header__title {
      flex: 0 1 auto;
      font-size: 14px;
      font-weight: 600;
      line-height: $__title-height;
      @include singleline-ellipsis;
      :deep(.card-field-content__text) {
        line-height: $__title-height;
        span {
          font-size: 14px;
          font-weight: 600;
          color: $text-main;
        }
      }
    }
    .lane-card__header__text-tags {
      display: flex;
      flex: 0 0 auto;
      gap: 4px;
      font-size: 12px;
      line-height: $__title-height;
    }
    .lane-card__header__icons {
      display: flex;
      flex: 1 0 auto;
      gap: 10px;
      // text-align: right;
      justify-content: flex-end;
      line-height: $__title-height;
      color: $brand-base-color-6;
    }
    .lane-card__header__btns {
      position: absolute;
      right: 0;
      width: $__title-height;
      line-height: $__title-height;
      color: $text-auxiliary;
      text-align: center;
      border-radius: 4px;
      .btns-content {
        display: inline-block;
        width: 100%;
        height: 100%;
      }
      &:hover {
        background: $neutral-color-2;
      }
    }
  }
  .lane-card__content {
    width: 100%;
    margin-top: $__block-gap;
    .lane-card__content__line {
      box-sizing: border-box;
      display: flex;
      flex-wrap: nowrap;
      width: 100%;
      height: $__field-height;
      line-height: $__field-height;
    }
    .lane-card__content__line + .lane-card__content__line {
      margin-top: $__block-gap;
    }
    .lane-card__content__field {
      gap: 8px;
      font-size: 13px;
      .field-title {
        display: flex;
        flex: 0 0 auto;
        max-width: 92px;
        color: $text-auxiliary;
        @include singleline-ellipsis;
        .field-title__name {
          flex: 0 1 auto;
          @include singleline-ellipsis;
        }
        .field-title__dot {
          flex: 0 0 auto;
        }
      }
      .field-content {
        flex: 1;
        color: $text-main;
        @include singleline-ellipsis;
        :deep(.card-field-content__text) {
          span {
            font-size: 13px;
          }
        }
      }
    }
    .lane-card__content__tag {
      display: block;
      height: $__title-height;
      line-height: $__title-height;
      @include singleline-ellipsis;
    }
  }
}

.stage-lane__lane-card__btn-menu {
  display: flex;
  flex-direction: row; // 目的是让scrollbar与容器同高并能触发滚动
  max-height: 6 * $__card-btn-height; // 让可展示的高度内，展示完整的选项
  overflow: visible;
  .stage-lane__lane-card__btn-menu__scrollbar {
    :deep(.el-scrollbar__wrap) {
      margin-bottom: 0 !important;
      overflow-x: hidden;
    }
  }

  .stage-lane__lane-card__btn-menu__item {
    height: $__card-btn-height;
    font-size: 14px;
    white-space: nowrap;
    .btn-menu__item__text {
      white-space: nowrap;
      pointer-events: none; // 确保点击只传整个item
    }
    .btn-menu__item__icon {
      display: inline-block;
      width: $__title-height;
      pointer-events: none; // 确保点击只传整个item
    }
    &.active {
      .btn-menu__item__icon {
        color: $brand-base-color-6;
      }
    }
  }
  .stage-lane__lane-card__btn-menu__item:not(.is-disabled) {
    color: $text-plain;
  }

  .stage-lane__lane-card__btn-menu__item:focus,
  .stage-lane__lane-card__btn-menu__item:not(.is-disabled):hover {
    color: $text-plain;
    background-color: $neutral-color-1;
  }
}
</style>
