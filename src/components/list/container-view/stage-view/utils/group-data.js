/**
 * @description: 用于泳道卡片接口数据格式化处理相关
 */

/**
 * @description: 获取卡片内容特定的otherInfo信息
 * @param {String} action 特定info类型，目前有：style、titleText、titleIcon、fieldTag
 * @param {Object} targetFieldInfo 字段解释，如果目标信息和字段关联，则要提供字段解释
 * @param {object[]} otherInfo 来自接口的otherInfo列表
 * @return {object[]}
 */
const getDataFieldOtherInfo = ({ action = '', targetFieldInfo = {}, otherInfo = [] }) => {
  // targetFieldInfo若没有意味着不是针对特定字段的
  return otherInfo?.filter(
    (config) =>
      config.action === action &&
      (targetFieldInfo.attr ? ['all', targetFieldInfo.attr].includes(config.attr) : true)
  )
}

/**
 * @description: 格式化卡片上字段相关内容
 * @param {Object} fieldInfo 字段解释
 * @param {Object} formData 接口的全量卡片数据
 * @param {object[]} otherInfo 来自接口的otherInfo列表
 * @return {object[]} 格式化后，可用于卡片渲染的字段数据
 */
const getDataFieldInfo = ({ fieldInfo = {}, formData = {}, otherInfo = [] }) => {
  return {
    fieldInfo: fieldInfo,
    value: formData[fieldInfo?.attr],
    // 字段值标签信息，目前只有“归档”有用
    tags: getDataFieldOtherInfo({
      action: 'fieldTag',
      otherInfo,
      targetFieldInfo: fieldInfo
    }),
    class: getDataFieldOtherInfo({
      action: 'style', // isCancel、isRed
      otherInfo,
      targetFieldInfo: fieldInfo
    })
  }
}

/**
 * @description: 后端给的是全量数据，结构比较杂，前端根据需要进行格式化，方便后续渲染
 * @param {*} explainList
 * @param {*} viewConfig
 * @param {*} dataList
 * @return {*}
 */
const formatOriginDataList = ({ explainList = [], viewConfig = {}, dataList = [] }) => {
  const filterExplainList = explainList.filter((field) => field.visible && field.isOpen === 1)
  const {
    titleField = [], // 标题配置信息
    columnSortField = [], // 排序配置信息
    columnVisibleField = [] // 摘要配置信息
  } = viewConfig ?? {}
  const targetTitleField = Array.isArray(titleField) ? titleField[0] : titleField
  // 标题字段解释
  const titleFieldInfo = filterExplainList.find((field) => field.attr === targetTitleField?.attr)

  const sortVisibleFields = columnSortField
    .filter(({ attr }) => columnVisibleField.includes(attr))
    .map(({ attr }) => filterExplainList.find((field) => field.attr === attr))
    .filter((field) => field)
  // 标签字段单独抽出，后续置底
  const tagFieldList = sortVisibleFields.filter((field) => field.fieldType === 800000)
  // 非标签字段解释
  const contentFieldList = sortVisibleFields.filter((field) => field.fieldType !== 800000)

  return dataList.map((cardData) => {
    // cardButton：按钮，otherInfo：一些定制样式块，包括（字段标签（如归档）fieldTag、超时提醒标识titleText、按钮状态图标titleIcon，分类标识是和后端约定好的字符串）
    const {
      appId,
      menuId,
      formId,
      businessType,
      saasMark,
      id, // dataId
      cardButton,
      otherInfo,
      data
    } = cardData
    const appInfo = { appId, menuId, formId, businessType, saasMark }

    return {
      id, // 卡片唯一标识，dataId
      appInfo, // 卡片数据相关表单信息
      title: titleFieldInfo
        ? getDataFieldInfo({
            fieldInfo: titleFieldInfo,
            otherInfo,
            formData: data
          })
        : {},
      // 标题副标签，目前只有“超时提醒”用到；
      titleTexts: getDataFieldOtherInfo({
        action: 'titleText',
        otherInfo
      }),
      // 标题状态图标，与按钮相关。所有可回显选中状态的按钮，目前只有“关注”；
      titleIcons: getDataFieldOtherInfo({
        action: 'titleIcon',
        otherInfo
      }),
      contents: contentFieldList.map((field) => {
        return getDataFieldInfo({
          fieldInfo: field,
          otherInfo,
          formData: data
        })
      }),
      tags: tagFieldList.map((tagField) => {
        return getDataFieldInfo({
          fieldInfo: tagField,
          otherInfo,
          formData: data
        })
      }),
      btns: cardButton,
      originCard: cardData, // 原始接口的卡片信息
      op: {
        // 卡片的一些交互操作权限配置
        // 1 待审批 2 审批通过 0 未审批 4 审批中
        drag: [0, 2].includes(cardData.flowStatus) || !cardData.flowStatus, // 卡片是否允许拖拽
        more: [0, 2].includes(cardData.flowStatus) || !cardData.flowStatus, // 是否展示更多按钮
        through: true // 查看详情
      }
    }
  })
}

/**
 * @description: 从来自接口的aggregateList解析出汇总信息
 * @param {object[]} aggregateList 接口的全量汇总信息，不知道为何是数组
 * @param {Object[]} aggregateField 接口的汇总字段信息
 * @return {Object}
 */
const getGroupDataSummary = (aggregateList = [], aggregateField = []) => {
  if (!aggregateList?.[0] || !aggregateField?.[0]) return {}

  return {
    title: aggregateField[0].attrName,
    value: aggregateField.type !== 7 ? aggregateList[0].value : undefined, // TODO: 等于7的场景目前无法处理
    fieldInfo: aggregateField[0]
  }
}

/**
 * @description:
 * @param {Object} originResult 来自接口的result，内容包括explainList、viewConfig、dataList、aggregateList、pageHelper
 * @param {object[]} curDataList 目前已有的卡片数据
 * @return {Object} 格式化之后的卡片数据
 */
const formatOriginGroupData = (originResult = {}, curDataList = []) => {
  const {
    explainList = [], // 解释
    viewConfig = {}, // 视图配置，包括aggregateField、columnSortField、columnVisibleField、titleField
    dataList = [], // 卡片数据列表，每个对象里，主要信息有data、id、otherInfo
    aggregateList = [], // 全量的统计信息
    pageHelper = {} // 分页
  } = originResult
  const formatDataList = formatOriginDataList({
    explainList,
    viewConfig,
    dataList
  })
  return {
    pageHelper,
    dataList: [...curDataList, ...formatDataList],
    loading: false, // 加载状态
    summary: getGroupDataSummary(aggregateList, viewConfig.aggregateField)
  }
}

/**
 * @description: 用于按钮图标根据状态不同切换类型
 * @param {String} icon 原图标
 * @param {Number} enabel 目标状态
 * @return {String} 替换后的图标
 */
const btnIconStatusChange = (icon = '', enabel = 1) => {
  if (typeof icon !== 'string') {
    return icon
  }
  const keyList = ['line', 'fill']
  const sourceIndex = +enabel === 1 ? 0 : 1
  const targetIndex = +!sourceIndex
  const regex = new RegExp(`${keyList[sourceIndex]}$`)
  return icon.replace(regex, keyList[targetIndex])
}

export { formatOriginGroupData, btnIconStatusChange }
