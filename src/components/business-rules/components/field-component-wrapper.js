// 使用 HOC 包装 all-fields 里的组件（UserCheckbox/DeptCheckbox），专用于业务规则（包括阶段推进器里相关的如触发条件等）。
// 延迟 el.form.change 触发，解决业务规则里 ElFormItem 的校验提前触发的问题，同时不影响原组件逻辑
// 原业务规则部分字段直接使用 all-fields 组件，直接修改在里边影响范围太大，而且会导致各种业务逻辑杂糅
import FieldUserCheckbox from '@/components/all-fields/form-data-edit/UserCheckbox/UserCheckbox.vue'
import FieldDeptCheckbox from '@/components/all-fields/form-data-edit/DeptCheckbox/DeptCheckbox.vue'
import emitter from 'element-ui/src/mixins/emitter'

const componentWrapper = (WrappedComponent) => {
  return {
    mixins: [emitter],
    model: {
      prop: 'value',
      event: 'modelChange'
    },
    props: WrappedComponent.props,
    methods: {
      // 拦截 modelChange，做个延迟 change 触发
      modelChangeHandler(val) {
        this.$emit('modelChange', val)
        this.dispatch('ElFormItem', 'el.form.change', val)
      }
    },
    render(h) {
      const keys = Object.keys(this.$slots)
      const slotList = keys
        .reduce((arr, key) => arr.concat(this.$slots[key]), [])
        .map((vnode) => {
          vnode.context = this._self
          return vnode
        })
      const { modelChange, ...listeners } = this.$listeners
      return h(
        WrappedComponent,
        {
          // on: this.$listeners,
          on: { modelChange: this.modelChangeHandler, ...listeners }, // 透传 event
          attrs: this.$attrs, // 透传 attrs
          props: this.$props, // 透传 props
          scopedSlots: this.$scopedSlots // scopedSlots 和 slotList 是为了透传 slots
        },
        slotList
      )
    }
  }
}

const UserCheckbox = componentWrapper(FieldUserCheckbox)
const DeptCheckbox = componentWrapper(FieldDeptCheckbox)

export { UserCheckbox, DeptCheckbox }
