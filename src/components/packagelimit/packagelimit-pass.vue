<!--
 * @Description: 自定义表单 packageLimit
-->
<template>
  <div class="packagelimit-content">
    <div>{{ packageLimitInfo.message }}</div>
    <div>
      <el-button v-if="packageLimitInfo.delete" type="text" @click="handleDelete">{{
        $t('packageDowngrade.deleteForm')
      }}</el-button>
      <el-button v-if="packageLimitInfo.upgrade" type="text" @click="handleUpgrade">{{
        $t('packageDowngrade.updatePackage')
      }}</el-button>
    </div>
  </div>
</template>

<script>
import { getTemplateList } from '@/api/formData.js'
import { deleteByPackage, hasData } from '@/api/package-limit.js'

export default {
  props: {
    packageLimitInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      deleteInfo: {}
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    async getInfo() {
      const { menuId, saasMark, subBusinessType, appId } = this.$route.query
      const params = {
        menuId,
        saasMark,
        subBusinessType,
        appId,
        isSync: true
      }
      const { result } = await getTemplateList(params)
      this.deleteInfo = result.formList[0]
    },
    async handleDelete() {
      const { menuId, saasMark, appId } = this.$route.query
      this.getInfo()
      const { formId, formName, businessType } = this.deleteInfo
      const checkData = await hasData({ appId, saasMark, formId })
      const message =
        !checkData.result.hasData && !checkData.result.processHasData
          ? this.$t('packageDowngrade.deleteMessageNoData')
          : this.$t('packageDowngrade.deleteMessageHasData')
      this.$confirm(message, this.$t('packageDowngrade.deleteTitle', { attr: formName }), {
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(async () => {
          await deleteByPackage({ appId, formId, formName, businessType, menuId })
          this.$emit('deleteSuccess')
          window.location.href = window.location.origin
        })
        .catch(() => {
          // this.$emit('deleteSuccess')
        })
    },
    handleUpgrade() {
      const h = this.$createElement
      this.$msgbox({
        title: this.$t('packageDowngrade.updatePackage'),
        message: h(
          'div',
          {
            style:
              'display: flex;justify-content: center;flex-direction: column;margin: 0 auto;padding: 20px 100px'
          },
          [
            h('p', { style: 'color: #9e9e9e;font-size: 20px;' }, this.$t('appModule.serviceTel')),
            h(
              'p',
              { style: 'font-style: italic;font-size: 20px;margin-top: 10px;' },
              '0571-28834699'
            )
          ]
        ),
        showConfirmButton: false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.packagelimit-content {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  transform: translate(-50%, -100px);
  div {
    text-align: center;
  }
}

.el-message-box {
  width: 380px;
}
</style>
