<template>
  <div class="gantt-list-default">
    <div
      v-for="(item, index) in defaultList"
      :key="index"
      class="default-item"
      :class="{ isLineActive: item.dataId === activeData.id }"
      @mouseenter.stop="lineActiveMouseenter(item, 'data')"
      @mouseleave.stop="lineActiveMouseleave(item)"
    >
      <div class="default-item__data">
        <div class="default-item__content num">{{ index + 1 }}</div>
        <div
          v-for="(head, headIndex) in explainList"
          :key="headIndex"
          class="default-item__content"
          :class="{ 'first-fiexd': !headIndex }"
          :style="{ width: widthList[headIndex] + 'px' || '160px' }"
        >
          <div
            v-tooltip="{
              content: getContent(item.data[head.attr] || '', head.fieldType),
              disabled: 'str' + index + head.attr !== tooltipAttr
            }"
            class="default-item__view"
            @mouseenter="onMouseOver('str' + index + head.attr)"
          >
            <!-- 关联数据10008 -->
            <template v-if="[10008].includes(head.fieldType)">
              <span
                :ref="'str' + index + head.attr"
                class="glsj"
                @click="openDetail(item.data[head.attr], 'link')"
                >{{ item.data[head.attr] | getUser }}</span
              >
            </template>
            <!-- 标签类型 -->
            <template v-else-if="[800000].includes(head.fieldType)">
              <tag-group :tags="item.data[head.attr] || []" />
            </template>
            <template v-else>
              <span :ref="'str' + index + head.attr">{{
                getContent(item.data[head.attr] || '', head.fieldType) || '--'
              }}</span>
            </template>
          </div>

          <div v-if="!headIndex && item.dataId === activeData.id" @click="openDetail(item)">
            <el-tooltip content="查看详情" effect="dark" placement="top">
              <i class="icon-expand-diagonal-line t-iconfont"></i>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import tagGroup from '@/components/list/components/tag-group.vue'

export default {
  name: 'GanttListDefault',
  props: {
    defaultList: {
      type: Array,
      default: () => []
    },
    explainList: {
      type: Array,
      default: () => []
    },
    widthList: {
      type: Array,
      default: () => []
    },
    activeData: {
      type: Object,
      default: () => ({})
    },
    timeList: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    TagGroup: tagGroup
  },
  data() {
    return {
      tooltipAttr: ''
    }
  },
  filters: {
    // formatDate(val) {
    //   return xbb.formatTime(val * 1000, 'yyyy-MM-dd HH:mm:ss')
    // },
    getString(val) {
      if (Array.isArray(val)) {
        const list = []
        val.forEach((item) => {
          list.push(item.text)
        })
        return list.join('、')
      } else {
        return val
      }
    },
    getAddress(val) {
      return val.province ? val.province + val.city + val.district + val.address : val
    },
    getUser(val) {
      if (Array.isArray(val)) {
        const list = []
        val.forEach((item) => {
          list.push(item.name)
        })
        return list.join('、')
      } else {
        return val
      }
    }
  },
  methods: {
    // hover对齐右侧数据
    lineActiveMouseenter(item) {
      // console.log('-----------', item)
      // 抛出hover数据，用于对齐左边列表hover
      const obj = {
        isGroup: false,
        id: item.dataId,
        groupId: item.groupInfo?.value
      }
      this.$emit('update:activeData', obj)
    },
    lineActiveMouseleave() {
      this.$emit('update:activeData', { isGroup: '', id: '', groupId: '' })
    },
    isTime(data, attr) {
      // console.log('data:::::', data, attr, this.timeList.beginTimeField.attr)
      // 判断实际是不是需要渲染time,当前attr是开始时间的attr && 不需要开始时间
      const start = attr === this.timeList.beginTimeField.attr && data.noStartTime
      const end = attr === this.timeList.endTimeField.attr && data.noEndTime
      return !(start || end)
    },
    openDetail(item, linkType) {
      this.$emit('handler-open-detail', {
        businessType: item.businessType,
        dataId: item.id,
        saasMark: this.$route.query.saasMark,
        item,
        linkType
      })
    },
    onMouseOver(str) {
      const parentWidth = this.$refs[str] && this.$refs[str][0].parentNode.offsetWidth
      const contentWidth = this.$refs[str] && this.$refs[str][0].offsetWidth
      // 若子集比中间层更宽 开启tooltip功能
      if (contentWidth > parentWidth) {
        this.tooltipAttr = str
      }
    },
    formatDate(val, type) {
      return xbb.formatTime(val * 1000, type.dateType)
    },
    getContent(data, type) {
      if ([2].includes(type)) return data.toString() || '--'
      if (!data) return '--'
      switch (type) {
        case 3:
        case 10000:
        case 10030:
          return data.text || data
        case 9:
        case 10001:
          if (Array.isArray(data)) {
            const list = []
            data.forEach((item) => {
              list.push(item.text)
            })
            return list.join('、')
          } else {
            return data
          }
        case 2:
          return data.toString()
        case 10003:
          return data.province ? data.province + data.city + data.district + data.address : data
        case 10016:
          return data.name || data
        case 10008:
        case 10017:
        case 20001:
          if (Array.isArray(data)) {
            const list = []
            data.forEach((item) => {
              list.push(item.name)
            })
            return list.join('、')
          } else {
            return data
          }
        case 4:
        case 10014:
        case 10015:
        case 10026:
        case 10027:
        case 20019:
          const date = xbb.formatTime(data * 1000, 'yyyy-MM-dd HH:mm:ss')
          return date.toString()
        case 800000:
          return (data || []).map((item) => item.name).join('、')
        default:
          return data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.gantt-list-default {
  .default-item__data-fixed {
    position: fixed;
  }
  .default-item {
    display: flex;
    align-items: center;
    height: 36px;
    font-size: 12px;
    color: $text-main;
    &__data {
      display: flex;
    }
    &__content {
      box-sizing: border-box;
      flex-shrink: 0;
      align-items: center;
      width: 160px;
      height: 36px;
      padding: 0 8px;
      overflow: hidden;
      line-height: 36px;
      color: $text-main;
      background-color: $base-white;
      border-right: 1px solid $neutral-color-3;
      border-bottom: 1px solid $neutral-color-3;
      &.num {
        position: -webkit-sticky; /* Safari */
        position: sticky;
        left: 0;
        display: flex;
        justify-content: center;
        width: 48px;
        padding: 0 0;
        color: $text-auxiliary;
      }
      &.first-fiexd {
        position: -webkit-sticky; /* Safari */
        position: sticky;
        left: 48px;
        display: flex;
      }
      .icon-expand-diagonal-line:hover {
        color: $brand-base-color-6;
      }
    }
    &.isLineActive {
      .default-item__content {
        background-color: $neutral-color-2 !important;
      }
    }
    &__view {
      // display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-between;
      width: 90%;
      overflow: hidden;
      font-size: 14px;
      color: $text-main;
      text-overflow: ellipsis;
      white-space: nowrap;
      .glsj {
        color: $link-base-color-6;
        cursor: pointer;
      }
    }
  }
}
</style>
