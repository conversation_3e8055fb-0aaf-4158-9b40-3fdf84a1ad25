<!-- 甘特图首页 -->
<template>
  <div class="gantt-detail">
    <div v-if="noData" class="gantt-nodata">
      <img src="@/assets/gantt-nodata.png" />
    </div>
    <div v-else id="gantt" ref="ganttView" class="gantt">
      <div
        v-show="listView"
        id="gantt-left"
        class="gantt-left"
        :class="{ 'gantt-left-preview': isPreview }"
      >
        <div class="gantt-left__title">
          <el-tooltip content="收起表格" effect="dark" placement="top">
            <i class="icon-arrow-left-double-line t-iconfont" @click="closeList"></i>
          </el-tooltip>
        </div>
        <GanttList
          ref="list"
          :active-data.sync="activeData"
          :default-add="defaultAdd"
          :default-list="defaultList"
          :explain-list="explainList"
          :group-list="groupList"
          :is-group="isGroup"
          :is-preview="isPreview"
          :other-params="otherParams"
          :time-list="timeList"
          @addMoreGroupData="addMoreGroupData"
          @changeLeftWidth="changeLeftWidth"
          @defaultNew="defaultNew"
          @groupClick="groupClick"
          @handler-open-detail="handlerOpenDetail"
          @listScroll="listScroll"
          @updateActive="updateActive"
          @updateWidth="updateWidth"
          @widthListFunc="widthListFunc"
        ></GanttList>
      </div>
      <div
        v-show="listView"
        id="resize"
        class="gantt-resize"
        :class="{ 'gantt-resize__disabled': explainList.length === 1 }"
      ></div>
      <div class="gantt-icon">
        <el-tooltip content="展开表格" effect="dark" placement="top">
          <i
            v-show="!listView"
            class="icon-arrow-right-double-line t-iconfont"
            @click="openList"
          ></i>
        </el-tooltip>
      </div>
      <div id="gantt-right" ref="ganttright" class="gantt-right" :class="{ isDefault: !isGroup }">
        <div class="date-show" :class="[listView ? 'noPosition' : 'isPosition']">
          {{ topShowTime.text }}
        </div>
        <GanttHead
          ref="gantthead"
          :all-days="allDays"
          :current-day-size="currentDaySize"
          :current-line-day="currentLineDay"
          :is-hover="isHover"
        >
        </GanttHead>
        <GanttBody
          ref="ganttbody"
          :active-data.sync="activeData"
          :computed-time-width="computedTimeWidth"
          :current-day-size="currentDaySize"
          :current-line-day="currentLineDay"
          :day-length="dayLength"
          :ganttright-refs="$refs.ganttright"
          :is-hover.sync="isHover"
          :is-preview="isPreview"
          :left-width="leftWidth"
          :line="line"
          :line-left="lineLeft"
          :list="list"
          :old-position.sync="oldPosition"
          :scroll-left-width="scrollLeftWidth"
          :time-list="timeList"
          :title-data="explainList[0]?.attr"
          views="normal"
          :week-day="weekDay"
          @gantt-new-time="ganttNewTime"
          @get-left-silder="getLeftSilder"
          @gotoday="gotoday"
          @handle-time-change="handleTimeChange"
          @handler-open-detail="handlerOpenDetail"
        >
        </GanttBody>
      </div>
      <div class="time-filter">
        <div class="today" @click="gotoday">今天</div>
        <i class="icon-arrow-left-s-line t-iconfont" @click="preNextView(1)"></i>
        <i class="icon-arrow-right-s-line t-iconfont" @click="preNextView(2)"></i>
        <div class="filter">
          <div
            v-for="item in currentDaySizeOptions"
            :key="item.value"
            class="filter-item"
            :class="{ isGanttViewCheck: currentDaySize.value === item.value }"
            @click="filterTime(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GanttList from '@/components/gantt/components/gantt-list.vue'
import GanttHead from '@/components/gantt/components/gantt-head.vue'
import GanttBody from '@/components/gantt/components/gantt-body.vue'
import ganttMixin from '@/components/gantt/gantt-mixin'
import xbb from '@xbb/xbb-utils'
import { getProcess } from '@/api/stage-process-design'
import { getProcessVersionList } from '@/api/stage-process-design'
import moment from 'moment'

export default {
  name: 'Gantt',

  mixins: [ganttMixin],

  components: {
    GanttList, // 左侧列表
    GanttHead, // 头部
    GanttBody
  },
  props: {
    isPreview: Boolean
  },
  data() {
    return {
      noData: false,
      listView: true,
      defaultAdd: false,
      lineLeft: 0,
      scrollLeftWidth: {
        left: 0,
        width: 0
      }, // gantt-body距离左边的滚动宽度
      leftWidth: 0, // 左侧宽度
      allDays: [], // 近三年的所有年月日
      topShowTime: {}, // 顶部日期计算
      // 当前的时间大小
      currentDaySize: {
        label: '月',
        value: 60
      },
      currentDaySizeOptions: [
        {
          label: '周',
          value: 160
        },
        {
          label: '月',
          value: 60
        },
        {
          label: '季',
          value: 14
        },
        {
          label: '年',
          value: 4
        }
      ],
      dataList: [], // 接口拿到的甘特图数据（未处理）
      list: [],
      // 当前hover的项目起止时间
      currentLineDay: {
        start: 0,
        end: 0
      },
      isHover: false,
      dayLength: 0,
      line: [],
      timeList: {}, // 开始时间和结束时间的解释
      activeData: {}, // hover对齐
      previewFormExplainList: [], // 设置端数据
      groupDataMock: {}, // 设置端分组下data-mock数据
      explainList: [],
      groupList: [],
      defaultList: [],
      isGroup: false,
      oldPosition: undefined, // 数据移动的原位置 left
      stageNodeListInit: [], // 预览的阶段
      enableVerList: [], // 如果分组字段是阶段推进器的话，要可以选择这这个版本，所以还得请求接口获取这个数组对象
      baseParams: {}, // 预览-阶段的参数
      // ====== list公用数据 ======
      otherParams: {
        appId: 1832,
        formId: 573451,
        businessType: 100,
        saasMark: 1,
        layoutId: 0, //布局Id
        viewId: 0, //视图Id
        listGroupId: 0, // 分组Id
        viewType: 3, // 视图类型(1列表2泳道3甘特4日历)
        conditions: []
      },
      viewConfig: {},
      weekDay: true, // 是否开启计算工作日1是仅计算工作日
      pageInfo: {}
    }
  },
  computed: {
    // 当前年份
    currentYear() {
      return new Date().getFullYear()
    },
    // 当前月份
    currentMonth() {
      return new Date().getMonth() + 1
    },
    // 当前日
    currentDay() {
      return new Date().getDate()
    },
    todayWidth() {
      const dayNum = moment(`${this.currentYear}-${this.currentMonth}-${this.currentDay}`).diff(
        moment(`${this.currentYear - 1}-01-01`),
        'days'
      )

      const width = dayNum * this.currentDaySize.value - Math.round(this.rightWidth / 2)
      console.log('todayWidth:', width)

      return width
    },
    rightWidth() {
      const box = document.getElementById('gantt')
      return box.offsetWidth - this.leftWidth
    }
  },
  watch: {
    'currentDaySize.value'(newValue, oldValue) {
      const newVal = (item, left, widthMe) => {
        item[left] = (item[left] / oldValue) * newValue
        item[widthMe] = (item[widthMe] / oldValue) * newValue
      }
      const widthFormat = (list) => {
        list.forEach((item) => {
          newVal(item, 'left', 'widthMe')
          if (item.widthMeShow) {
            newVal(item, 'leftShow', 'widthMeShow')
          }
        })
      }
      widthFormat(this.list)
    }
  },
  mounted() {
    this.dragControllerDiv()
    this.getDay()
    this.$refs.ganttright.addEventListener('scroll', this.handleScroll)
    this.$refs.ganttright.addEventListener('mousemove', this.handlMousemove)
    window.addEventListener('resize', this.windowChange)
  },
  methods: {
    // 初始化：接收列表页传来的参数（筛选等）：this.$refs.ganttView.refresh(params)
    refresh(params) {
      console.log('refresh--筛选等参数', params)
      setTimeout(() => {
        this.initGet(params)
      }, 500)
    },
    // 初始化数据
    initGet(params) {
      this.isGroup = params.isGroup
      this.explainList = [...params.titleList]
      this.timeList = { ...params.timeList }
      this.viewConfig = { ...params.viewConfig }
      this.weekDay = !!params.viewConfig.isCalculateWeekday
      this.pageInfo = params.pageInfo
      this.otherParams = {
        ...this.otherParams,
        ...params, // 有是否是默认分组(1是0不是),有archive,有//阶段流程Id(没有可不传)
        viewId: params.viewConfig.id,
        groupAttr: params.groupAttr,
        conditions: params.conditions
      }

      this.listPage = 1 //当前页
      this.listPageSize = 20 //页大小
      this.groupList = []
      // ====== 无分组 ======
      this.dataPage = 1
      this.dataPageSize = 20
      this.defaultList = []

      // 新增后刷新也走这个，需要关一下分组
      this.isGroup && this.$refs.list.clearGroupActive()

      // 重置分组的加载
      this.isNextPage = true
      this.isGroup ? this.ganttListGetGanttGroup() : this.ganttListGetGanttData(true)

      this.$nextTick(() => {
        // 有了表头刷新一下宽度
        this.$refs.list.getWidth()
        // 顺便修复一下 左侧的宽度
        setTimeout(async () => {
          await this.initWidth()
          // this.leftWidth = document.getElementById('gantt-left').offsetWidth
          this.gotoday()
          this.setStoneHeight()
        }, 500)
      })
    },
    // ==================== 甘特图核心数据方法 =========================
    // 甘特图刷新：重新计算高度
    reComputed() {
      this.list = []
      this.$nextTick(() => {
        this.dataFormat()
      })
    },
    // 数据格式化，拿到每个甘特图节点的位置信息。第一次触发在父组件获取数据后，手动调一次
    dataFormat() {
      const getAllList = ({ dataList, isChildren = false, groupInfo = '' }) => {
        let arr = []
        dataList.forEach((item, index) => {
          item.isChildren = isChildren // 是否是子数据
          item.groupInfo = groupInfo // 子数据的 分组属性 & index值

          // （无 children && 不是子数据） || （是分组子数据 && 不是最后一位）
          if ((!item.children && !isChildren) || (isChildren && index + 1 !== dataList.length)) {
            arr.push(item)
          } else if (
            (item.children && !item.children.length) ||
            (isChildren && index + 1 === dataList.length)
          ) {
            // (有 children ，但 children无长度) || (isChildren && 是子数据的最后一位)
            arr.push(item)
            // 无子数据的分组 || 子数据分组的最后一位，增加底部样式
            arr.push({ isGroupStyle: true })
          } else if (item.children && item.children.length >= 1) {
            const _item = JSON.parse(JSON.stringify(item))
            delete _item.children
            arr.push(_item)
            arr = arr.concat(
              getAllList({
                dataList: item.children,
                isChildren: true,
                groupInfo: { ..._item, groupIndex: index }
              })
            ) // children数组，是子任务，最外层父节点index
          }
        })
        return arr
      }
      const listFormat = getAllList({ dataList: this.dataList })
      listFormat.forEach((data) => {
        this.pushData({ obj: data, isInit: true })
      })
    },
    /**
     * @description: 处理每一条数据
     * @param  {Object} obj 行数据
     * @param  {Boolean} isInit 是否初始化
     * @param  {Number} listIndex 当前操作的下标
     * @param  {Number} groupIndex 数据的分组下标
     * @param  {Boolean} isNoDataLine 暂无数据标识
     * @param  {Number} isNoDataLineIndex 暂无数据的插入位置
     * @param  {Number} id 新建操作返回当前数据的id
     * @param  {Number} linkParentId 新建选择了父任务 父任务id
     */
    pushData({
      obj,
      isInit = false,
      listIndex = '',
      id,
      linkParentId,
      groupIndex,
      isNoDataLine = false,
      isNoDataLineIndex
    }) {
      // 如果是暂无数据，插入空行，结束
      if (obj.isNoDataLine) {
        console.log('❌isNoDataLine-groupIndex:', isNoDataLineIndex)
        this.$set(this.list, isNoDataLineIndex, { isNoDataLine: true, isGroupStyle: true })
        return
      }

      // 如果是加载更多，插入空行，结束
      if (obj.isDataMore) {
        console.log('✅isDataMore-groupIndex:', isNoDataLineIndex)
        this.$set(this.list, isNoDataLineIndex, { isDataMore: true, isGroupStyle: true })
        return
      }

      const index = this.list.length

      // 如果item中children数组，说明该item是分组的，加入isGroup属性，判断增加间距
      if (obj.children) this.$set(obj, 'isGroup', true)

      // 处理开始时间和结束时间
      let startTime, endTime
      if (!obj.isGroup && !obj.isGroupStyle && obj.data) {
        if (
          !obj.data[this.timeList.beginTimeField?.attr] &&
          !obj.data[this.timeList.endTimeField?.attr]
        ) {
          this.$set(obj, 'isNoData', true)
        } else {
          console.log('YYYYYYYYYYYY', this.timeList.beginTimeField.attr)
          // 有开始时间 else 无开始时间
          if (obj.data[this.timeList.beginTimeField.attr]) {
            startTime = obj.data[this.timeList.beginTimeField.attr]
          } else {
            const date = moment.unix(obj.data[this.timeList.endTimeField.attr]).startOf('day')
            startTime = date.unix()
            obj.data[this.timeList.beginTimeField.attr] = startTime
            obj.data['noStartTime'] = true
            this.$set(obj, 'data', { ...obj.data })
          }
          // 有结束时间 else 无结束时间
          if (this.isPreview) {
            endTime = moment().add(7, 'days').unix()
            obj.data[this.timeList.endTimeField.attr] = endTime
            this.$set(obj, 'data', { ...obj.data })
          } else {
            if (obj.data[this.timeList.endTimeField.attr]) {
              endTime = obj.data[this.timeList.endTimeField.attr]
            } else {
              const startDate = moment.unix(obj.data[this.timeList.beginTimeField.attr])
              const endDate = startDate.clone().endOf('day')
              endTime = endDate.unix()
              obj.data[this.timeList.endTimeField.attr] = endTime
              // 不能把结束时间加在列表上
              obj.data['noEndTime'] = true
              this.$set(obj, 'data', { ...obj.data })
            }
          }
          this.$set(obj, 'isNoData', false)
        }
      }

      this.$set(obj, 'hasChildren', obj.hasChildren || false)
      this.$set(obj, 'left', this.computedTimeWidth(startTime * 1000))
      this.$set(obj, 'widthMe', this.computedTimeWidth(startTime * 1000, endTime * 1000))
      this.$set(obj, 'status', obj.status || '1')

      obj.id = obj.id || id

      this.$set(obj, 'isShow', true)

      if (listIndex !== '') {
        this.list.splice(listIndex, 0, obj)
        this.reComputed()
      } else if (groupIndex) {
        this.$set(this.list, groupIndex, obj)
      } else {
        this.$set(this.list, index, obj)
      }
    },
    // ==================== 操作类方法【新增，进入详情等】 =========================
    // 时间改变触发，data 任务数据，callback 回调，成功保存后调用
    handleTimeChange(data, callback) {
      const params = {
        data: data.data
      }
      if (data.flg === 'left') {
        params['startTime'] = data.startTime
      } else if (data.flg === 'right') {
        params['endTime'] = data.endTime
      } else {
        params['startTime'] = data.startTime
        params['endTime'] = data.endTime
      }
      this.ganttNewTime(params, callback)
    },
    // 列表区域滚动，调用接口
    listScroll() {
      if (this.isPreview) return
      this.isGroup ? this.ganttListGetGanttGroup() : this.ganttListGetGanttData(false)
    },
    // 点击分组，获取分组下数据
    groupClick(group) {
      if (this.isPreview) {
        console.log('点击分组，获取分组下数据---isPreview', group)
        // 增加数据到相应的分组下
        if (group.flg) {
          // data
          const getDataList = []
          this.groupDataMock.dataList.forEach((item, index) => {
            getDataList.push({
              data: item,
              dataId: index + 1 + group.value[0],
              id: index + 1 + group.value[0]
            })
          })
          // 加入分组中
          const newList = []
          this.groupList.forEach((item) => {
            let obj = { ...item }
            if (group.value[0] === item.value) {
              // 如果是分组，将数据加入分组的对应index的children中
              obj = {
                ...item,
                children: [...getDataList],
                // 还要把分页等信息加到children中
                dataPageTotal: 1,
                dataPage: 2
              }
            }
            newList.push(obj)
          })
          this.groupList = [...newList]

          this.dataList = [...this.groupList]
          this.$nextTick(() => {
            this.reComputed()
          })
        } else {
          // 关闭分组，清空数据，重绘甘特图
          this.dataList.forEach((item) => {
            if (item.children && item.value === group.value[0]) {
              item.children = []
            }
          })
          this.reComputed()
        }
      } else {
        console.log('点击分组，获取分组下数据', group)
        // 保存当前选择的分组，用于在获取数据时，加入conditions字段中
        this.activeGroup = group.flg ? { ...group } : {}
        if (group.flg) {
          this.ganttListGetGanttData(false, group) // 是打开分组，请求数据
          return
        }
        // 是关闭分组 ====
        this.list.forEach((item, index) => {
          if (item.value === group.value[0]) {
            if (item.children.length) {
              // 如果有数据，删除分组下所有数据
              this.list.splice(index + 1, item.children.length)
              if (this.list[index + 1].isDataMore) {
                this.list.splice(index + 1, 1)
              }
            } else {
              // 分组下无数据，删除当前组后面的一位暂无数据
              this.list.splice(index + 1, 1)
            }
            item.children = []
          }
        })
        // 关闭分组，清空数据，重绘甘特图
        this.dataList.forEach((item) => {
          if (item.value === group.value[0]) {
            item.children = []
            item.dataPage = 1
          }
        })
        // this.reComputed()
      }
    },
    // 分组下的加载更多
    addMoreGroupData(group) {
      this.ganttListGetGanttData(false, group)
    },
    // 打开详情
    handlerOpenDetail(data) {
      console.log('✅❌❌打开详情🔛', data)
      let dataList = []
      // 判断有无分组，有分组取分组下的childen，没有分组取全部
      // 如果有分组，有groupInfo属性，groupInfo.value
      if (data.item.groupInfo) {
        this.groupList.forEach((item) => {
          if (item.value === data.item.groupInfo.value) {
            // 说明是这个分组，拿下面的children
            dataList = [...item.children]
          }
        })
      } else {
        dataList = [...this.defaultList]
      }
      const params = {
        ...data,
        businessType: this.otherParams.businessType,
        appId: this.otherParams.appId,
        formId: data.item.formId,
        menuId: data.item.menuId,
        subBusinessType: this.$route.query.subBusinessType,
        dataList
      }
      this.$emit('onGanttView', { type: 'openDetail', params })
    },
    // 新建
    defaultNew(data, type) {
      console.log('onGanttViewonGanttView', data)
      // 有data：分组下新增，无data：是默认列表新增
      this.$emit('onGanttView', {
        type: 'addForm',
        isgroup: !!type,
        explainList: this.explainList,
        viewConfig: this.viewConfig,
        otherParams: this.otherParams,
        data
      })
    },
    // ==================== 甘特图相关方法 =========================
    // 甘特图：快捷新建
    ganttNewTime(info, callback) {
      console.log('快捷新建:', info)
      const fieldInfo = []

      if (this.timeList.beginTimeField && info.startTime) {
        // 有开始解释
        fieldInfo.push({
          attr: this.timeList.beginTimeField.attr,
          value: info.startTime / 1000
        })
      }
      if (this.timeList.endTimeField && info.endTime) {
        // 有结束解释
        fieldInfo.push({
          attr: this.timeList.endTimeField.attr,
          value: info.endTime / 1000
        })
      }

      const params = {
        appId: this.otherParams.appId,
        menuId: info.data.menuId,
        formId: this.otherParams.formId,
        businessType: this.otherParams.businessType,
        subBusinessType: 101,
        dataId: info.data.dataId,
        fieldType: 4, //字段类型
        fieldInfo,
        saasMark: this.otherParams.saasMark
      }
      console.log('快捷新建=========>>>>>>>:', params)
      if (this.isPreview) {
        this.changeTime(info)
        return
      }
      this.uiPaasApi
        .setUiPaasListDisplayFastSwitch(params)
        .then(({ result, msg }) => {
          if (msg === '操作成功') {
            this.changeTime(info)
          } else {
            this.changeOldPosition(info)
          }
          callback && callback()
        })
        .catch(() => {})
    },
    // 甘特移动：更新甘特图中的数据
    changeTime(info) {
      this.dataList.forEach((item) => {
        if (item.children && item.children.length) {
          // 有 children 说明是group，并且有子集，要去子集里面找数据
          item.children.forEach((data) => {
            if (data.dataId === info.data.dataId) {
              if (this.timeList.beginTimeField && info.startTime) {
                data.data[this.timeList.beginTimeField.attr] = info.startTime / 1000
              }
              if (this.timeList.endTimeField && info.endTime) {
                data.data[this.timeList.endTimeField.attr] = info.endTime / 1000
              }
            }
          })
        } else if (!item.children && item.dataId === info.data.dataId) {
          // 没有 children 说明是无分组的，直接遍历就可以了
          if (this.timeList.beginTimeField && info.startTime) {
            item.data[this.timeList.beginTimeField.attr] = info.startTime / 1000
          }
          if (this.timeList.endTimeField && info.endTime) {
            item.data[this.timeList.endTimeField.attr] = info.endTime / 1000
          }
        }
      })
      this.reComputed()
    },
    // 甘特移动：位置的计算
    changeOldPosition(info) {
      // this.oldPosition
      this.dataList.forEach((item) => {
        if (item.children && item.children.length) {
          // 有 children 说明是group，并且有子集，要去子集里面找数据
          item.children.forEach((data) => {
            if (data.dataId === info.data.dataId) {
              item.left = this.oldPosition
            }
          })
        } else if (!item.children && item.dataId === info.data.dataId) {
          item.left = this.oldPosition
        }
      })
      this.reComputed()
    },
    // 甘特图：点击跳转到滑块区域
    getLeftSilder(left) {
      this.$refs.ganttright.scrollTo({
        left: left,
        behavior: 'smooth'
      })
    },
    // 甘特图：监听gantt图的滚动状态/获取顶部的年月
    handleScroll() {
      const ganttRight = this.$refs.ganttright
      this.scrollLeftWidth.left = ganttRight.scrollLeft
      const ganttRightWidth = Math.floor(ganttRight.getBoundingClientRect().width)
      let type = 'YD'
      if ([4].includes(this.currentDaySize.value)) {
        // 年视图返回年
        type = 'Y'
      }
      const left = Math.floor(ganttRight.scrollLeft + ganttRightWidth / 2) // 滚动长度 加上1/2可视容器
      const time = this.$refs.ganttbody.computedWithTime(Math.floor(left), type)
      const todayMin = this.todayWidth - this.currentDaySize.value / 2
      const todayMax = this.todayWidth + ganttRightWidth - this.currentDaySize.value / 2

      let showToday = true
      if (left > todayMin && left < todayMax) {
        showToday = false
      }
      this.$emit('top-show-time', {
        text: time,
        showToday
      })
      this.topShowTime = {
        text: time,
        showToday
      }
      const fullTime = this.$refs.ganttbody.computedWithTime(Math.floor(left), true)
      this.$emit('get-active-time', fullTime)
    },
    // 甘特图：监听鼠标在 gantt图 中的移动状态
    handlMousemove(e) {
      const left = document.getElementById('gantt-right')
      const relativeX = Math.trunc(e.clientX - left.getBoundingClientRect().left)
      // Math.trunc（当前鼠标距离左边的位置 / 当前时间筛选下的单个宽度）* 当前时间筛选下的单个宽度 = 新增的起点坐标
      const leftPosition = (this.scrollLeftWidth.left + relativeX) / this.currentDaySize.value
      this.lineLeft = Math.trunc(leftPosition) * this.currentDaySize.value
    },
    // 甘特图-筛选：时间维度选择
    filterTime(val) {
      const value = this.currentDaySizeOptions.find((o) => o.value === val.value)
      this.currentDaySize = value
      this.handleSetDaySize(value)
    },
    // 甘特图-筛选：点击下一个月/上一个月
    preNextView(type) {
      let val = 0
      switch (this.currentDaySize.value) {
        case 160:
          val = this.currentDaySize.value * 7
          break
        case 60:
          val = this.currentDaySize.value * 30
          break
        case 14:
          val = this.currentDaySize.value * 90
          break
        case 4:
          val = this.currentDaySize.value * 365
          break
      }
      if (type === 1) {
        val = -val
      }
      const ganttRight = this.$refs.ganttright
      const scrollLeft = ganttRight.scrollLeft
      const z = val + scrollLeft
      ganttRight.scrollTo({
        top: 0,
        left: z,
        behavior: 'smooth'
      })
    },
    // 甘特图-筛选：回到今天
    gotoday() {
      this.$refs.ganttright.scrollTo({
        top: 0,
        left: this.todayWidth,
        behavior: 'smooth'
      })
    },
    // 甘特图-筛选：时间维度选择 --> 切换时间轴
    handleSetDaySize(value) {
      this.currentDaySize = value
      this.resetWidth()
      this.$nextTick(() => {
        this.gotoday()
      })
    },
    // 甘特图：计算甘特图的背景等高度
    setStoneHeight() {
      this.$refs.gantthead.setStoneLine()
      this.$refs.ganttbody.setStoneLine()
    },
    // 甘特图：获取全年数据
    getDay() {
      this.getAllDate()
    },

    // 工具：处理每月等天数数组
    handleMonthDay(year) {
      // 获取整月的数据
      const wholeMonthData = (year, month) => {
        const days = moment([year, month - 1]).daysInMonth()

        return Array.from({ length: days }, (v, k) => {
          return {
            date: k + 1,
            weekday: [0, 6].includes(moment([year, month - 1, k + 1]).day())
          }
        })
      }
      const obj = {}
      for (let i = 1; i <= 12; i++) {
        obj[i] = wholeMonthData(year, i)
      }
      return obj
    },

    // 甘特图：获取全年数据
    getAllDate() {
      const arr = [this.currentYear - 1, this.currentYear, this.currentYear + 1]
      this.allDays = arr.map((year) => ({
        year,
        days: moment([year]).isLeapYear() ? 366 : 365,
        month: this.handleMonthDay(year)
      }))

      this.initToday(this.allDays)

      this.resetWidth()
    },
    // 甘特图：根据时间计算距离【startTime：开始时间戳 毫秒,endTime：结束时间戳 毫秒】
    computedTimeWidth(startTime, endTime) {
      const timeStart = (timestamp) =>
        timestamp ? moment(timestamp).startOf('day').valueOf() : null
      const _timeStart = timeStart(startTime) // 开始时间的0点0分0秒0毫秒
      const _endTime = timeStart(endTime) // 结束时间的0点0分0秒0毫秒

      if (!_timeStart) return 0

      const left =
        moment(_timeStart).diff(moment(`${this.currentYear - 1}/01/01`), 'days') *
        this.currentDaySize.value
      if (!_endTime) return left

      const width =
        moment(_endTime).diff(moment(_timeStart), 'days') * this.currentDaySize.value +
        this.currentDaySize.value
      return width
    },
    // 甘特图：重置单元格宽度，以及天数距离左侧距离
    resetWidth() {
      const days = this.allDays.flatMap((year) => Object.values(year.month).flat())

      days.forEach((item, index) => {
        item.width = (index + 1) * this.currentDaySize.value
      })
      this.dayLength = days[days.length - 1].width
    },
    // 拖动宽度
    widthListFunc(index, diff) {
      this.widthList.splice(index, 1, diff)
      // 同步一下列表的宽度
      this.$nextTick(() => {
        const groupWdth = document.querySelector('.gantt-list-group')
        const headWidth = document.querySelector('.gantt-list__head .head-name-box')
        groupWdth.style.width = `${headWidth.offsetWidth}px`
      })
    },
    // 左侧列表：宽度初始化
    initWidth() {
      const resize = document.getElementById('resize')
      const left = document.getElementById('gantt-left')
      const right = document.getElementById('gantt-right')
      const box = document.getElementById('gantt')

      const maxWidth = box.clientWidth - resize.offsetWidth

      const { subBusinessType, formId } = this.otherParams
      const currentPageSign = `subBusinessType_${subBusinessType}-formId_${formId}`
      const widthObj = utils.LS.get('VUE-list-gantt-width')
        ? utils.LS.get('VUE-list-gantt-width')[currentPageSign]
        : {}

      // 初始宽度计算，获取一下头部宽度，如果小于360，使用这个宽度，如果>360，判断>70%用70%，不然用tou'bu'kuan'du
      const headWidth = document.querySelector('.gantt-list__head .head-name-box')
      let initWidth
      if (headWidth) {
        if (headWidth.offsetWidth < 360) {
          initWidth = headWidth.offsetWidth
        } else {
          const maxWidthData = maxWidth * 0.7
          initWidth = headWidth.offsetWidth > maxWidthData ? maxWidthData : headWidth.offsetWidth
        }
      } else {
        initWidth = 360
      }
      if (this.isPreview) {
        initWidth = 360
      }
      // 是否有缓存宽度 ？ （有缓存宽度且 > 当前初始化头部宽度 ？ 用头部宽度 ：缓存宽度） ： 使用初始化头部宽度
      const getLeftwidth =
        widthObj && widthObj.ganttWidth
          ? widthObj.ganttWidth > initWidth
            ? initWidth
            : widthObj.ganttWidth
          : initWidth

      left.style.width = getLeftwidth + 'px'
      this.leftWidth = getLeftwidth // 左侧宽度传入甘特图
      // 右边区域 = 总宽度 - 左边宽度 - 拖动条宽度
      right.style.width = maxWidth - this.leftWidth + 'px'

      this.scrollLeftWidth.width = maxWidth - getLeftwidth
    },
    // 左侧列表：列表拖动头部宽度时，左侧最小宽度计算
    changeLeftWidth(headWidth) {
      console.log('左侧列表拖动头部宽度时，左侧最小宽度计算 -- headWidth:', headWidth)
      const resize = document.getElementById('resize')
      const left = document.getElementById('gantt-left')
      const right = document.getElementById('gantt-right')
      const box = document.getElementById('gantt')
      const maxWidth = box.clientWidth - resize.offsetWidth

      left.style.width = headWidth + 'px'
      this.leftWidth = headWidth // 左侧宽度传入甘特图
      // 右边区域 = 总宽度 - 左边宽度 - 拖动条宽度
      right.style.width = maxWidth - this.leftWidth + 'px'

      this.scrollLeftWidth.width = maxWidth - headWidth

      // 计算完还需要走一下保存逻辑
      // 缓存中的原有数据
      const oldWidthObj = utils.LS.get('VUE-list-gantt-width') || {}
      const { subBusinessType, formId } = this.otherParams
      const currentPageSign = `subBusinessType_${subBusinessType}-formId_${formId}`

      const newTableColumnWidthGroup = {
        ...oldWidthObj,
        [currentPageSign]: { ...oldWidthObj[currentPageSign], ganttWidth: this.leftWidth }
      }

      utils.LS.set('VUE-list-gantt-width', newTableColumnWidthGroup)
    },
    // 左侧列表：列表区域拖动宽度计算
    dragControllerDiv() {
      const resize = document.getElementById('resize')
      const left = document.getElementById('gantt-left')
      const right = document.getElementById('gantt-right')
      const box = document.getElementById('gantt')
      const _that = this

      // 鼠标按下事件
      resize.onmousedown = function (e) {
        // 记录坐标起始位置
        const startX = e.clientX
        // 左边元素起始宽度
        resize.left = left.offsetWidth

        document.onmousemove = function (e) {
          const endX = e.clientX // 鼠标拖动的终止位置

          // 左边区域最后的宽度 = resize.left + (endx-startx:移动的距离）
          let moveLen = resize.left + (endX - startX)
          const maxWidth = box.clientWidth - resize.offsetWidth

          // 如果拖动的宽度 > 总宽度的70%，则最大宽度为：总宽度的70%
          if (moveLen > maxWidth * 0.7) moveLen = maxWidth * 0.7
          // 如果拖动的宽度 > 左边列表的最大宽度，则最大宽度为：表头宽度
          const leftHeadWidth = document.querySelector('.gantt-list__head .head-name-box')
          if (moveLen > leftHeadWidth.offsetWidth) {
            moveLen = leftHeadWidth.offsetWidth
          }
          const titleWidth = document.querySelector('.head-name-box__fixed')
          // 如果左侧宽度小于48+标题宽度，=标题宽度
          if (moveLen < Number(48 + titleWidth.offsetWidth)) {
            moveLen = 48 + titleWidth.offsetWidth
          }

          // 设置左边区域的宽度
          left.style.width = moveLen + 'px'
          _that.leftWidth = moveLen // 左侧宽度传入甘特图
          // 右边区域 = 总宽度 - 左边宽度 - 拖动条宽度
          right.style.width = maxWidth - moveLen + 'px'
          // 赋值给内部计算滑块是否在画面内
          _that.scrollLeftWidth.width = maxWidth - moveLen
        }
        // 鼠标松开
        document.onmouseup = function (evt) {
          // 缓存中的原有数据
          const oldWidthObj = utils.LS.get('VUE-list-gantt-width') || {}
          const { subBusinessType, formId } = _that.otherParams
          const currentPageSign = `subBusinessType_${subBusinessType}-formId_${formId}`

          const newTableColumnWidthGroup = {
            ...oldWidthObj,
            [currentPageSign]: { ...oldWidthObj[currentPageSign], ganttWidth: _that.leftWidth }
          }

          utils.LS.set('VUE-list-gantt-width', newTableColumnWidthGroup)

          document.onmousemove = null
          document.onmouseup = null
          resize.releaseCapture && resize.releaseCapture()
        }
        resize.setCapture && resize.setCapture()
        return false
      }
    },
    // 左侧列表：收起
    closeList() {
      this.listView = false
    },
    // 左侧列表：打开
    openList() {
      this.listView = true
    },
    // 更新列宽
    updateWidth(width, index) {
      this.explainList[index].width = width
    },
    // 行数据的hover处理
    updateActive(item) {
      this.activeData = item
    },
    // 窗口变化计算左侧的宽度，用于定位
    windowChange() {
      const ganttRight = this.$refs.ganttright
      const resize = document.getElementById('resize')
      const box = document.getElementById('gantt')
      if (!box.clientWidth) return
      const maxWidth = box.clientWidth - resize.offsetWidth

      this.scrollLeftWidth.width = maxWidth - this.leftWidth
      this.scrollLeftWidth.left = ganttRight && ganttRight.scrollLeft
    },
    // ==================== 设置端相关方法 =========================
    // 设置端：init
    previewInit(previewConfig) {
      console.log('🚀 => 甘特图设置端init：', previewConfig)
      this.getData(previewConfig)
    },
    @xbb.debounceWrap(300)
    // 设置端：init
    preview(viewConfig) {
      console.log(`🚀 => 甘特图预览更新:`, viewConfig)
      this.getData(viewConfig)
    },
    // 设置端：数据填充
    async getData(previewConfig) {
      this.explainList = [...previewConfig.explainList]
      this.timeList = { ...previewConfig.timeList }
      this.isGroup = previewConfig.isGroup
      this.weekDay = !!previewConfig.viewConfig.isCalculateWeekday
      this.list = []
      this.dataList = []
      this.changeListWidth() // 搞一下整体宽度
      // 如果有分组
      if (this.isGroup) {
        // 存一下data的mock数据
        this.groupDataMock = { ...previewConfig }
        if (previewConfig.viewConfig.groupField.fieldType === 10008) {
          // 关联数据（10008）需要mock数据
          this.groupList = [
            { text: '关联数据1', value: 0, add: true },
            { text: '关联数据2', value: 1, add: true },
            { text: '关联数据3', value: 2, add: true }
          ]
        } else if (previewConfig.viewConfig.groupField.fieldType === 10030) {
          if (previewConfig.stageNodeListInit) {
            // 阶段推进器，要调接口获取，接口从dialog页面处理过了，这边只接收即可
            this.groupList = [...previewConfig.stageNodeListInit].map((item) => {
              return {
                ...item,
                add: true
              }
            })
          } else {
            // 坑爹的预览。。。我服了
            console.log('阶段推进器预览')
            this.baseParams = { ...previewConfig.baseParams }
            getProcessVersionList({
              archive: 2,
              ...this.baseParams
            }).then(({ result }) => {
              console.log('result:', result)
              // 这里过滤出已启用的版本
              this.enableVerList = result.stageProcessVersionPojoList.filter(
                (item) => item.type !== 1
              )

              this.enableVerList = this.enableVerList.sort((a, b) => a.priority - b.priority)

              const curStageProcess = this.enableVerList.filter(
                (item) => item.id === previewConfig.viewConfig.defaultStageProcessId
              )
              if (curStageProcess.length) {
                this.getProcessData(curStageProcess[0].id, curStageProcess[0].type)
              }
            })
          }
        } else {
          // 阶段推进器没选默认版本/下拉框/单选都是直接读解释
          previewConfig.formExplainList.forEach((item) => {
            // 在解释中找到当前选中的分组
            if (
              item.fieldType === previewConfig.viewConfig.groupField.fieldType &&
              item.attr === previewConfig.viewConfig.groupField.attr
            ) {
              // 获取分组
              const list = []
              item.items.forEach((group) => {
                list.push({ ...group, children: [], isGroup: true, add: true })
              })
              this.groupList = [...list]
            }
          })
        }
        this.$refs.list.clearGroupActive()
        this.reComputed()
      } else {
        console.log('🚀🚀🚀 无分组', previewConfig)
        // 如果没有分组
        const getDataList = []
        previewConfig.dataList.forEach((item, index) => {
          getDataList.push({ data: item, dataId: index + 1, id: index + 1 })
        })
        this.dataList = [...getDataList] || []
        this.defaultList = [...getDataList] || []
        this.reComputed()
      }
    },
    // 设置端：预览相关
    getProcessData(id, type) {
      const params = { id, type, ...this.baseParams }
      return new Promise((resolve, reject) => {
        getProcess(params)
          .then(({ result }) => {
            const stageNodeListInit =
              result.stageListPojoList.filter((item) => item.enable === 1) || []
            this.stageNodeListInit = stageNodeListInit.map((item) => {
              return {
                allowSkipStage: item.allowSkipStage,
                color: item.stageWarningColor,
                enable: item.enable,
                stageId: item.id,
                stageProcessId: result.stageProcessGetPojo.id,
                stageType: item.type,
                text: item.name
              }
            })
            this.groupList = [...this.stageNodeListInit]
            resolve()
          })
          .finally(() => {})
      })
    },
    // ==================== 工具 =========================
    // 工具：根据年份天数创建月份及月份天数数组
    initToday(allDays) {
      const todayYear = allDays.find((year) => year.year === this.currentYear)
      if (todayYear) {
        const todayMonth = todayYear.month[this.currentMonth]
        if (todayMonth) {
          const today = todayMonth.find((day) => day.date === this.currentDay)
          if (today) {
            today.today = true
          }
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.windowChange)
    this.$refs.ganttright?.removeEventListener('scroll', this.handleScroll)
    this.$refs.ganttright?.removeEventListener('mousemove', this.handlMousemove)
  }
}
</script>

<style lang="scss" scoped>
.gantt-detail {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex: 1;
  padding: 12px 16px;
  overflow: hidden;
  img {
    width: 500px;
  }
  .gantt-nodata {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
  }
  .gantt {
    position: relative;
    display: flex;
    flex: 1;
    width: 100%;
    min-height: 400px;
    overflow: hidden;
    // margin: 12px 16px;
    border: 1px solid $neutral-color-3;
    border-radius: 8px;
    .gantt-left {
      display: flex;
      flex-direction: column;
      min-width: 198px;
      &__title {
        box-sizing: border-box;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        height: 40px;
        padding: 0 14px;
        border-bottom: 1px solid $neutral-color-3;
        .icon-arrow-left-double-line {
          width: 22px;
          height: 22px;
          line-height: 22px;
          color: $text-auxiliary;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;
          &:hover {
            background-color: $neutral-color-2;
          }
        }
      }
    }
    .gantt-left-preview {
      min-width: 360px !important;
    }
    .gantt-resize {
      flex-grow: 0;
      flex-shrink: 0;
      // 增加一点点宽度，方便拖动
      width: 5px;
      margin: 0 -2px;
      cursor: col-resize;
      flex-basic: 5px;
      &::after {
        display: block;
        width: 1px;
        height: 100%;
        margin: 0 auto;
        content: '';
        background-color: $neutral-color-3;
      }
      &__disabled {
        cursor: default !important;
      }
    }
    .gantt-icon {
      position: absolute;
      z-index: 999;
      box-sizing: border-box;
      width: 40px;
      height: 40px;
      line-height: 40px;
      color: $text-auxiliary;
      text-align: center;
      background-color: $base-white;
      border-bottom: 1px solid $neutral-color-3;
      .icon-arrow-right-double-line {
        width: 22px;
        height: 22px;
        line-height: 22px;
        color: $text-auxiliary;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: $neutral-color-2;
        }
      }
    }
    .isDefault {
      padding: 0 0 28px 0;
    }
    .gantt-right {
      display: flex;
      flex: 1 auto;
      flex-direction: column;
      max-width: 100%;
      overflow-x: scroll;
      overflow-y: hidden;
      background-color: $base-white;
      // border-left: 1px solid $neutral-color-2;
      .date-show {
        position: absolute;
        z-index: 999;
        // left: 40px;
        box-sizing: border-box;
        height: 40px;
        font-size: 14px;
        line-height: 40px;
        color: $text-main;
        background-color: $base-white;
        border-bottom: 1px solid $neutral-color-3;
      }
      .noPosition {
        padding: 0 0 0 20px;
      }
      .isPosition {
        left: 40px;
        padding: 0;
      }
    }
    .time-filter {
      position: absolute;
      right: 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      height: 39px;
      padding: 0 12px 0 0;
      color: $text-auxiliary;
      background-color: $base-white;
      .today {
        box-sizing: border-box;
        height: 24px;
        padding: 0 8px;
        margin-right: 10px;
        line-height: 24px;
        color: $text-plain;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: $neutral-color-2;
        }
      }
      .icon-arrow-left-s-line,
      .icon-arrow-right-s-line {
        width: 22px;
        height: 22px;
        margin-right: 10px;
        line-height: 22px;
        color: $text-plain;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: $neutral-color-2;
        }
      }
      .filter {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 24px;
        padding: 2px;
        cursor: default;
        background: $neutral-color-1;
        border-radius: 4px;
        .filter-item {
          width: 28px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          cursor: pointer;
        }
        .filter-item:hover {
          color: $text-main;
        }
        .isGanttViewCheck {
          color: $text-main;
          background-color: $base-white;
        }
      }
    }
  }
}
</style>
