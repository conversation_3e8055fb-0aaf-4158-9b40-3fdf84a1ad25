<!--
import { debug } from 'util';
 * @Description: 图渲染组件
 -->

<template>
  <div ref="graph" class="graph" @mouseenter="mouseenter" @mouseleave="mouseleave"></div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import { mapGetters } from 'vuex'
import { HashCompare } from '@/utils'

const hashCompare = new HashCompare()

export default {
  name: 'Graph',

  props: {
    option: {
      type: Object,
      default: () => ({})
    },
    // 目前地图选中的城市
    province: {
      type: String
    },
    // x轴的label是否倾斜显示
    XLabelLine: {
      type: Boolean,
      default: false
    },
    // 是否查看数据预警
    isShowWarn: {
      type: Boolean,
      default: false
    },
    graphWidth: {
      type: Number,
      default: 0
    },
    chartExplain: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      myChart: null,
      optionCache: [] // option的缓存，最多只缓存2次进来的option
    }
  },

  watch: {
    option: {
      handler(val, oldVal) {
        if (!oldVal) return
        hashCompare.init(oldVal)
        // 对option进行缓存和比较，防止无必要的执行watch中的图表初始化
        if (hashCompare.compare(val)) return
        console.log('触发了图表初始化option in watch')
        this.optionInitChartsHandler()
        if (!this.isShowWarn) {
          return this.setChartOption(this.option, 'resize')
        }
        if (this.myChart) {
          this.myChart.clear()
        }
        this.setChartOption(this.option)
      },
      deep: true
    }
  },
  computed: {
    ...mapGetters(['linkageState', 'linkageArgu', 'linkageParam'])
  },

  created() {
    // 接收事件
    this.$root.eventHub.$on('graphResize', this.handlerGraphResize)
    // 联动高亮
    this.$root.eventHub.$on('graphHighLight', this.graphHighLightHandle)
    // 联动取消高亮
    this.$root.eventHub.$on('cancelHighLight', this.graphHighLightCancel)
  },

  mounted() {
    // 没有延时的化，有一定概率图表无法渲染，提示获取不到容器宽度
    setTimeout(() => {
      this.initCharts()

      if (!this.myChart) return
      this.setChartOption(this.option)
      this.myChart?.on('click', (params) => {
        console.log('>?>?>?>?>?>?', params)
        // 发送点击事件
        if (params.componentType !== 'xAxis') {
          this.$root.eventHub.$emit('graph-click', params)
          this.$emit('graph-click', params)
          if (this.linkageState) {
            this.graphHighLightHandle(params)
          }
        }
      })
      // 为了监听x轴label的鼠标hover事件
      this.myChart?.on('mousemove', (params) => {
        // 柱形图、折线图、面积图等
        if (
          ['line', 'bar', 'area', 'percentStack', 'stacked'].includes(this.$parent.graphType) &&
          !this.isShowWarn &&
          this.option
        ) {
          // 兼容y轴为主轴的情况
          const data = this.option.xAxis.data || this.option.yAxis.data
          const dataIndex = data.findIndex((item) => {
            return item === params.value
          })
          if (this.XLabelLine) {
            this.myChart.dispatchAction({
              type: 'showTip', // 显示提示框
              seriesIndex: 0, // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
              dataIndex: dataIndex, // 数据的 index，如果不指定也可以通过 name 属性根据名称指定数据
              // 本次显示 tooltip 的位置。只在本次 action 中生效。
              // 缺省则使用 option 中定义的 tooltip 位置。
              position: params.event.target.position
            })
          }
        }
      })
    }, 100)
  },

  destroyed() {
    this.$root.eventHub.$off('graphResize', this.handlerGraphResize)
    this.$root.eventHub.$off('graphHighLight')
    this.$root.eventHub.$off('cancelHighLight')
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose()
    }
  },
  methods: {
    @xbb.debounceWrap(500)
    optionInitChartsHandler() {
      this.initCharts()
    },
    handlerGraphResize(target) {
      setTimeout(() => {
        this.resize()
      }, 0)
    },
    initCharts() {
      // 引入地图文件
      // require('echarts/map/js/china.js')
      const chartDom = this.$refs.graph
      if (!chartDom) {
        return
      }
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(chartDom)
    },
    setChartOption(option, type) {
      // 绘制图表
      if (!option || !this.myChart) {
        return false
      }
      if (type === 'resize') {
        this.myChart.setOption(option, true)
      } else {
        this.myChart.setOption(option)
      }
    },
    // 取消高亮处理
    graphHighLightCancel() {
      this.myChart?.dispatchAction({
        type: 'downplay',
        seriesId: this.linkageArgu.seriesId,
        dataIndex: this.linkageArgu.dataIndex
      })
    },
    // 图表高亮处理
    graphHighLightHandle(graphParams) {
      const params = graphParams.inOther ? graphParams.params : graphParams
      // console.log('graphHighLightHandle', graphParams, params, Date.now())
      if (!this.linkageState) return
      // 点击时立即生效，高亮所选item
      this.graphHighLight(params)
      // // 对失去高亮的event进行监听，一旦触发再次设置高亮
      // this.myChart.on('mouseout', (_) => {
      //   this.$nextTick(() => {
      //     this.graphHighLight(params)
      //   })
      // })
    },
    // 图表高亮设置
    graphHighLight(graphParams) {
      if (!this.linkageState) return

      if (
        this.chartExplain &&
        this.linkageParam.chartEntity &&
        this.chartExplain.id === this.linkageParam.chartEntity.id
      ) {
        // 如果是堆叠图或者百分比堆叠图 需要高亮一列
        if ([30, 31].includes(this.chartExplain.chartType)) {
          this.myChart.dispatchAction({
            type: 'highlight',
            dataIndex: graphParams.dataIndex
          })
        } else {
          this.myChart.dispatchAction({
            type: 'highlight',
            seriesId: graphParams.seriesId,
            dataIndex: graphParams.dataIndex
          })
        }
      }
    },
    // 图表重绘
    resize() {
      this.myChart?.resize()
    },
    // 鼠标移入canvas触发预警动画停止动画事件
    mouseenter() {
      if (this.isShowWarn) {
        this.$emit('mouseover')
      }
    },
    // 鼠标移出canvas触发预警动画开始动画事件
    mouseleave() {
      if (this.isShowWarn) {
        this.$emit('mouseout')
      }
      if (this.linkageState) {
        this.graphHighLightHandle(this.linkageArgu)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.graph {
  width: 100%;
  height: 100%;
  overflow: hidden;
  // width: 100px;
  // height: 100px;
}
</style>
