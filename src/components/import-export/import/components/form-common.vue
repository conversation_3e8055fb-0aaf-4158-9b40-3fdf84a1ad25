<!--
 * @Description: 导入的通用表单
 -->
<template>
  <el-form
    ref="form"
    class="common-form demo-form-inline"
    label-position="left"
    label-width="124px"
    :model="form"
    :rules="rules"
  >
    <el-form-item
      v-show="isTemplateShow"
      class="common-form__item"
      :label="$t('importExport.selectTemplate')"
      prop="formId"
    >
      <el-select
        v-model="form.formId"
        :placeholder="$t('placeholder.choosePls', { attr: $t('form.template') })"
        :popper-append-to-body="false"
        popper-class="form-select-item-popper"
        style="width: 100%"
        @change="formTypeChange"
      >
        <el-option
          v-for="(item, index) in templateList"
          :key="index"
          :label="item.formName"
          :value="item.formId"
        ></el-option>
      </el-select>
    </el-form-item>
    <slot name="file"></slot>
    <el-form-item class="common-form__item" :label="$t('importExport.importMethod')">
      <el-select
        v-model="importMode"
        :placeholder="$t('el.select.placeholder')"
        :popper-append-to-body="false"
        popper-class="form-select-item-popper"
        style="width: calc(100% - 28px)"
        @change="onModeChange"
      >
        <el-option
          v-for="item in modeOpt"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-popover
        v-if="importMode === 'update'"
        placement="bottom-start"
        popper-class="tips-index"
        trigger="hover"
        width="370"
      >
        <div class="update-import-tips">
          <p class="update-import-tips-title">{{ $t('importExport.importTipsByIdTitle') }}</p>
          <p>{{ $t('importExport.importTipsByIdDesc') }}</p>
          <img
            src="https://cloudcode-devstorage.oss-cn-hangzhou.aliyuncs.com/ding66041eb1c6df73f535c2f4657eb6378f/front/update-import-tips.png"
            width="365"
          />
        </div>
        <i slot="reference" class="tooltip-icon web-icon-question-circle web-iconfont"></i>
      </el-popover>
      <el-tooltip
        v-if="importMode === 'all'"
        :content="$t('importExport.importTipsByAllField')"
        placement="top"
        popper-class="tips-index"
        trigger="hover"
        ><i class="tooltip-icon web-icon-question-circle web-iconfont"></i
      ></el-tooltip>
    </el-form-item>
    <el-form-item
      v-if="importMode === 'all' && importInfo.saasMark !== 2"
      class="common-form__item"
      :label="$t('importExport.importField')"
    >
      <el-select
        v-model="importFieldType"
        :placeholder="$t('el.select.placeholder')"
        :popper-append-to-body="false"
        popper-class="form-select-item-popper"
        style="width: 100%"
      >
        <el-option label="全部字段导入" value="all"> </el-option>
        <el-option label="指定字段导入" value="appoint"> </el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      v-show="
        (importFieldType === 'appoint' && importInfo.saasMark === 1) || importMode === 'update'
      "
      class="common-form__item"
      :label="importMode === 'update' ? $t('importExport.updateField') : $t('operation.chooseFile')"
      prop="attrList"
    >
      <el-select
        ref="appointRef"
        v-model="form.attrList"
        filterable
        multiple
        :placeholder="$t('el.select.placeholder')"
        :popper-append-to-body="false"
        popper-class="form-select-item-popper appoint"
        style="width: 100%"
      >
        <template v-if="importMode === 'update'">
          <el-option
            v-for="item in attrPojosForUpdate"
            :key="item.attr"
            :disabled="item.noRepeat === 1 || item.required === 1"
            :label="item.attrName"
            :value="item.attr"
          >
          </el-option>
        </template>
        <template v-else>
          <el-option
            v-for="item in attrPojos"
            :key="item.attr"
            :disabled="item.noRepeat === 1 || item.required === 1"
            :label="item.attrName"
            :value="item.attr"
          >
          </el-option>
        </template>
      </el-select>
    </el-form-item>
    <el-form-item class="common-form__item" :label="$t('importExport.selectImportFile')">
      <input-file v-model="form.file" style="width: 100%" />
      <p v-if="initErrorTips" class="init-error-tips">{{ initErrorTips }}</p>
      <div class="download-template">
        <span class="download-template__txt" @click="downloadTemplate">{{
          $t('importExport.downloadImportTemplate')
        }}</span>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import BUSINESS_TYPE from '@/constants/common/business-type'
import { stsInstance, initOSSClient } from '@/utils/oss'

import formMixin from './form-mixin'
import { downloadTemplate, startFormImport, getAttrs, getAttrs4CoverById } from '@/api/import'

export default {
  name: 'CommonForm',

  mixins: [formMixin],

  props: {
    initErrorTips: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      form: {
        formId: '', // 选中的模板
        file: undefined, // 上传的文件
        attrList: [],
        initErrorTips: ''
      },
      rules: {
        formId: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', { attr: this.$t('form.form') }),
            trigger: 'blur'
          }
        ],
        file: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', { attr: this.$t('form.form') }),
            trigger: 'blur'
          }
        ]
      },
      importMode: 'all',
      modeOpt: [
        {
          value: 'all',
          label: this.$t('importExport.addAndUpdateData')
        },
        {
          value: 'update',
          label: this.$t('importExport.onlyUpdateData')
        }
      ],
      importFieldType: 'all',
      // 字段列表
      attrPojos: [],
      // 仅更新字段列表，仅更新的字段跟普通模式展示的不一样
      attrPojosForUpdate: []
    }
  },
  computed: {
    noRepeatAttr() {
      const attr = this.attrPojos.filter((e) => {
        return e.noRepeat === 1 || e.required === 1
      })

      return attr.map((e) => {
        return e.attr
      })
    },
    requiredAttrForUpdate() {
      const attr = this.attrPojosForUpdate.filter((e) => {
        return e.required === 1
      })

      return attr.map((e) => {
        return e.attr
      })
    }
  },

  mounted() {
    this.init()
  },
  methods: {
    init() {
      // 新工单不支持仅更新的导入方式
      if ([5510, 5520, 5530, 20300].includes(this.importInfo.businessType)) {
        this.modeOpt.pop()
      }
      // 根据isFileImport字段 判断是否添加 带附件/图片导入 选项
      if (this.isFileImport) {
        this.modeOpt.push({
          value: 'file',
          label: this.$t('importExport.isFileImport')
        })
      }
      // if (this.importInfo.saasMark === 2) {
      //   this.modeOpt.splice(1, 1)
      // }
      console.log(this.importInfo.saasMark, this.modeOpt)
      this.form.formId = this.importInfo.formId
      this.$refs.appointRef.deleteTag = (event, tag) => {
        if (this.noRepeatAttr.includes(tag.currentValue) && this.importMode !== 'update') {
          this.$message({
            type: 'warning',
            message: this.$t('importExport.noRepeat')
          })
          return
        }
        if (this.requiredAttrForUpdate.includes(tag.currentValue) && this.importMode === 'update') {
          this.$message({
            type: 'warning',
            message: this.$t('importExport.noRepeat')
          })
          return
        }
        const that = this.$refs.appointRef
        const index = that.selected.indexOf(tag)
        if (index > -1 && !that.selectDisabled) {
          const value = that.value.slice()
          value.splice(index, 1)
          that.$emit('input', value)
          that.emitChange(value)
          that.$emit('remove-tag', tag.value)
        }
        event.stopPropagation()
      }
      this.setAppoint()
      this.setUpdateFields()
    },
    setAppoint() {
      const { appId, menuId, businessType, saasMark, formId } = this.importInfo
      const params = {
        appId,
        menuId,
        formId,
        businessType,
        saasMark
      }
      if (this.importMode === 'file') params.isFileImport = this.isFileImport
      getAttrs(params).then(({ result: { attrPojos } }) => {
        this.attrPojos = attrPojos
        this.$nextTick(() => {
          if (this.importMode === 'all') {
            attrPojos.forEach((e) => {
              ;(e.noRepeat || e.required) && this.form.attrList.push(e.attr)
            })
          }
        })
      })
    },

    setUpdateFields() {
      const { appId, menuId, businessType, saasMark, formId } = this.importInfo
      const params = {
        appId,
        menuId,
        formId,
        businessType,
        saasMark
      }
      getAttrs4CoverById(params).then(({ result: { attrPojos } }) => {
        this.attrPojosForUpdate = attrPojos
      })
    },

    /**
     * @description: 模板下载
     * @param {type}
     * @return:
     */
    downloadTemplate() {
      const { appId, menuId, businessType, saasMark } = this.importInfo
      this.formValidate()
        .then(() => {
          const formId = this.form.formId
          const params = {
            appId,
            menuId,
            formId,
            businessType,
            saasMark,
            isSync: true
          }
          if (this.importMode === 'file') params.isFileImport = this.isFileImport
          // 表单可能有多模版
          // 先找到当前选择的表单模版的名字，如果有的话，文件名以它为准
          const formTemplate = this.templateList.find((item) => item.formId === formId)
          const formName = formTemplate ? formTemplate.formName : ''
          const fileName = formName
            ? `${formName + this.$t('importExport.importTemplate')}`
            : this.importInfo.title
            ? `${this.importInfo.title + this.$t('importExport.importTemplate')}`
            : this.$t('importExport.importTemplate')
          params.isCoverById = this.importMode === 'update' ? 1 : 0
          params.attrList = this.form.attrList
          if (utils.isMacWX()) {
            utils.WXLoadFile(fileName, 'downloadTemplate', params)
          } else {
            downloadTemplate(params)
              .then((data) => {
                utils.filePostDownload(fileName, data)
              })
              .catch(() => {})
          }
        })
        .catch(() => {})
    },

    /**
     * @description: 开始导入
     * @param {type}
     * @return:
     */
    startImport() {
      const { appId, menuId, businessType, saasMark, subBusinessType } = this.importInfo
      return new Promise((resolve, reject) => {
        this.formValidate()
          .then(async () => {
            const { formId, file } = this.form
            const params = {
              appId,
              menuId,
              businessType,
              subBusinessType,
              saasMark,
              formId
            }
            if (Number(businessType) === BUSINESS_TYPE.KNOWLEDGE_BASE) {
              params.knowledgeBaseId = this.importInfo.knowledgeBaseId
            }
            if (
              (this.importMode === 'all' && this.importFieldType === 'appoint') ||
              this.importMode === 'update'
            ) {
              params.attrList = this.form.attrList
            }
            if (subBusinessType === 102) {
              params.subBusinessType = subBusinessType
            }

            const loadingInstant = this.$loading({
              target: this.$parent.$el.children[0],
              text: this.$t('importExport.uploading')
            })
            try {
              const stsOssInfo = await stsInstance.getInfo(this)
              const newOssClient = await initOSSClient(stsOssInfo)

              const timestamp = new Date().getTime()
              // 文件一周后过期
              const Expires = new Date(timestamp + 604800000)
              const corpid = utils.cleanCorpIdPrefix() //移除corpId中的特定前缀
              const result = await newOssClient.put(
                `${stsOssInfo.dir}/${corpid}/${localStorage.getItem('userId')}/xlsx/${timestamp}-${
                  file.name
                }`,
                file,
                {
                  Expires
                }
              )
              params.fileUrl = result.url
              if (this.importMode === 'file') params.isFileImport = this.isFileImport
              params.isCoverById = this.importMode === 'update' ? 1 : 0
              startFormImport(params)
                .then((data) => {
                  resolve()
                })
                .catch(() => {
                  // 导入失败后，将文件置空，否则下次重新选择该文件时，不会触发change事件
                  this.form.file = undefined
                })
                .finally(() => {
                  loadingInstant.close()
                })
              console.log(result)
            } catch (error) {
              loadingInstant.close()
              console.error(error)
            }
          })
          .catch((err) => {
            console.log(err)
          })
      })
    },
    formTypeChange(val) {
      this.$emit('change-form-type', val)
    },
    // 导入方式change
    onModeChange(val) {
      utils.SS.set('import-mode', val)
      this.form.attrList = []
      if (this.importMode === 'all') {
        this.attrPojos.forEach((e) => {
          ;(e.noRepeat || e.required) && this.form.attrList.push(e.attr)
        })
      } else if (this.importMode === 'update') {
        this.attrPojosForUpdate.forEach((e) => {
          e.required && this.form.attrList.push(e.attr)
        })
      }
    }
  }
}
</script>

<style lang="scss">
.appoint .is-disabled span {
  color: $text-grey;
}
.tips-index {
  z-index: 99999 !important;
}
</style>

<style lang="scss" scoped>
.download-template {
  margin-top: 4px;
  font-size: 12px;
  line-height: 16px;
  color: $link-base-color-6;
  // text-decoration: underline;
  .download-template__txt {
    cursor: pointer;
  }
}

.init-error-tips {
  color: $error-base-color-6;
}
.update-import-tips {
  font-size: 14px;
  img {
    margin-top: 8px;
  }
}
.update-import-tips-title {
  margin-bottom: 8px;
  font-weight: bold;
}
.tooltip-icon {
  position: absolute;
  right: 0;
  width: 16px;
  line-height: 32px;
  color: $text-grey;
  &:hover {
    color: $brand-base-color-6;
  }
}
.common-form__item {
  :deep(.el-form-item__label) {
    color: $text-main;
  }
  margin-bottom: 0;
  & + & {
    margin-top: 16px;
  }
  :deep(.form-select-item-popper) {
    position: absolute !important;
    left: 0px !important;
  }
}
</style>
