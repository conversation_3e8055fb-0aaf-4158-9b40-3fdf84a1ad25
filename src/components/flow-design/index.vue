<!--
 * @Description: 仿钉钉流程设计 - 根组件
 -->
<template>
  <div class="flow-design">
    <div class="zoom-btns">
      <el-button
        :disabled="scale <= scaleRange.min"
        icon="el-icon-minus"
        @click="scaleMinus"
      ></el-button>
      <span class="scale-text">{{ parseInt(scale * 100) }}%</span>
      <el-button
        :disabled="scale >= scaleRange.max"
        icon="el-icon-plus"
        @click="scalePlus"
      ></el-button>
    </div>
    <div class="box-scale-box">
      <div class="box-scale" style="width: 100%" :style="scaleStyle">
        <!-- 开始节点 -->
        <NodeWrap
          :explain-list="explainList"
          :node-data="nodeData"
          :parent-node="null"
          :select-item="selectItem"
        ></NodeWrap>
        <div class="end-node">
          <div class="end-node-circle"></div>
          <div class="end-node-text">{{ $t('label.endProcess') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NodeWrap from './components/node-wrap'
// import BranchWrap from './components/branch-wrap'
// import store from '@/store'
export default {
  name: 'FlowDesign',
  components: {
    NodeWrap
    // BranchWrap
  },

  filters: {
    // 动态渲染warp
    warpName(type) {
      let res
      switch (type) {
        case 1:
          res = 'NodeWrap'
          break
        case 'condition':
          res = 'BranchWrap'
          break
        default:
          break
      }
      return res
    }
  },
  provide: function () {
    return {
      flowIndex: this
    }
  },

  props: {
    readonly: {
      type: Boolean,
      default: false
    },
    selectItem: {
      // 当前选中项
    },
    propNodeData: {
      // 表单树初始对象
      required: true
    },
    explainList: {
      default: () => []
    },
    summary: {
      default: () => []
    }
  },

  data() {
    return {
      nodeList: [],
      scaleRange: {
        min: 0.5,
        max: 1.5
      },
      scale: 1,
      endNode: {
        type: 2
      },
      nodeData: {},
      nodeDataInit: {}
    }
  },

  computed: {
    scaleStyle() {
      return {
        transform: `scale(${this.scale})`,
        'min-height': this.scale + 'px'
      }
    }
  },

  watch: {
    propNodeData: {
      deep: true,
      handler(val) {
        if (val) {
          this.nodeData = JSON.parse(JSON.stringify(val))
          this.nodeDataInit = JSON.parse(JSON.stringify(val))
        }
      }
    },
    nodeData: {
      immediate: true,
      deep: true,
      handler(val, oldVal) {
        const flag1 = JSON.stringify(val)
        const flag2 = JSON.stringify(this.nodeDataInit)
        if (flag1 !== flag2) {
          this.$store.commit('SET_EDIT_CHANGE_FLOW', true)
        } else {
          this.$store.commit('SET_EDIT_CHANGE_FLOW', false)
        }
      }
    }
  },

  created() {},

  methods: {
    init() {},
    /**
     * @function {cb} 回调函数 arg[校验是否通过,错误信息列表]
     */
    validate(cb) {
      let vali = true
      const errorList = []
      this.nodeList.forEach((item) => {
        if (item.erro) {
          vali = false
          errorList.push(item.erro)
        }
      })
      cb && cb(vali, errorList)
    },
    /**
     * 对数据格式进行处理
     * 解递归
     */
    formatData() {
      const list = []
      function fn(node, wrapperId) {
        list.push({
          ...node,
          wrapperId
        })
        if (node.childNode) {
          fn(node.childNode, wrapperId)
        }
        if (node.conditionNodes && node.conditionNodes.length > 0) {
          const { nodeId } = node
          node.conditionNodes.forEach((item) => {
            fn(item, nodeId)
          })
        }
        return false
      }

      fn(this.nodeData)
      return list
    },
    /**
     * 层级减一
     */
    scaleMinus() {
      if (this.scale <= this.scaleRange.min) {
        return false
      }
      this.scale = +this.scale - 0.1
      this.scale = this.scale.toFixed(1)
    },
    /**
     * 层级加一
     */
    scalePlus() {
      if (this.scale >= this.scaleRange.max) {
        return false
      }
      this.scale = +this.scale + 0.1
      this.scale = this.scale.toFixed(1)
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-design {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: $neutral-color-1;
  & > .zoom-btns {
    position: absolute;
    top: 20px;
    right: 50px;
    z-index: 10;
    & > .scale-text {
      display: inline-block;
      width: 66px;
      text-align: center;
    }
    & > button {
      width: 32px;
      padding-right: 0;
      padding-left: 0;
      text-align: center;
    }
  }
  & > .box-scale-box {
    width: 100%;
    height: 100%;
    overflow: scroll;
    & > .box-scale {
      position: relative;
      box-sizing: border-box;
      display: inline-block;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: center;
      min-width: min-content;
      min-height: 0;
      padding: 54.5px 0;
      background-color: $neutral-color-1;
      transform: scale(1);
      -webkit-transform-origin: 0 0 0;
      transform-origin: 0 0 0;
      -webkit-box-align: start;
      -ms-flex-align: start;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      & > .end-node {
        font-size: 14px;
        color: rgba(25, 31, 37, 0.4);
        text-align: left;
        border-radius: 50%;
        & > .end-node-circle {
          width: 10px;
          height: 10px;
          margin: auto;
          background: #dbdcdc;
          border-radius: 50%;
        }
        & > .end-node-text {
          margin-top: 5px;
          text-align: center;
        }
      }
    }
  }
}
</style>
