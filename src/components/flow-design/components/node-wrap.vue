<!--
 * @Description: 节点的布局组件（主要是为了控制连接线）
 -->
<template>
  <div class="node-wrap">
    <!-- 分支节点 -->
    <template v-if="nodeData.type === 6">
      <BranchWrap
        :key="`branch-wrap-${nodeData.nodeId}`"
        :node-data="nodeData"
        :parent-node="parentNode"
        :select-item="selectItem"
      />
    </template>
    <!-- 普通节点 -->
    <template v-else>
      <ApproverNode
        :key="`approver-node-${nodeData.nodeId}`"
        :node-data="nodeData"
        :parent-node="parentNode"
        :select-item="selectItem"
      />
    </template>

    <!-- 子节点 -->
    <template v-if="nodeData.childNode">
      <NodeWrap
        :key="`node-wrap-${nodeData.nodeId}`"
        :node-data="nodeData.childNode"
        :parent-node="nodeData"
        :select-item="selectItem"
      />
    </template>
  </div>
</template>

<script>
import utilMixin from './../mixin/util-mixin'

export default {
  name: 'NodeWrap',
  components: {
    ApproverNode: () => import('./approver-node'),
    BranchWrap: () => import('./branch-wrap')
  },

  filters: {
    // 动态渲染warp
    warpName(type) {
      let res
      switch (type) {
        case 1:
          res = 'NodeWrap'
          break
        case 5:
          res = 'BranchWrap'
          break
        default:
          break
      }
      return res
    }
  },

  mixins: [utilMixin],
  provide: function () {
    return {
      nodeWrap: this // 向下注入自己
    }
  },

  props: {
    nodeData: {
      /**
       * 节点数据对象
       */
      type: Object,
      default: () => ({}),
      required: true
    }
  },

  methods: {
    init() {}
  }
}
</script>

<style lang="scss">
.node-wrap {
  // padding: 0 50px;
  position: relative;
  display: inline-flex;
  flex-direction: column;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  .active {
    &::after {
      border: 1px solid $brand-color-5 !important;
      box-shadow: 0 0 6px 0 rgba(176, 213, 250, 0.3) !important;
    }
  }
}
</style>
