/*
 * @Description: 把子表单内的 location 组件包一层，保持单例
 * 历史的代码里存在子表嵌套，会导致 el-popover 存在多份实例
 */
import Vue from 'vue'
import merge from 'element-ui/src/utils/merge'
import i18n from '@/lang'
import LocationFiled from './Location.vue'
import { createPopper } from '@popperjs/core'
import { PopupManager } from 'element-ui/lib/utils/popup'

const defaults = {
  fieldInfo: {},
  mode: 'base'
} // Internationalization

// class LocationPopover {
//   constructor(options, event) {
//     options = merge({}, defaults, options)
//     const Constructor = Vue.extend(LocationFiled)
//     this.instance = new Constructor({ i18n, propsData: options })
//     this.vm = this.instance.$mount()
//     this.container = document.getElementById("full-screen-popover")
//     this.container.appendChild(this.vm.$el)
//     this.popper = createPopper(event.target, this.container)
//     window.addEventListener('mousedown', this.checkClickPlace.bind(this, event))
//   }

//   checkClickPlace(e) {
//     console.log(e)
//     // 判断一下点击像素不在 container 元素内
//     const range = this.container.getBoundingClientRect()
//     const { x, y } = e
//     const rangeX = range.width + range.left
//     const rangeY = range.height + range.top
//     if (x <= range.left || x >= rangeX || y <= range.top || y >= rangeY) {
//       this.destory()
//     }
//   }

//   destory() {
//     this.container.removeChild(this.vm.$el)
//     this.vm.$destroy()
//     this.popper.destroy()
//     window.removeEventListener('mousedown', this.checkClickPlace)
//   }
// }

function LocationPopover(options, event) {
  const zIndex = PopupManager.nextZIndex()
  options = merge({}, defaults, options)
  const Constructor = Vue.extend(LocationFiled)
  const instance = new Constructor({ i18n, propsData: options })
  const vm = instance.$mount()
  const container = document.getElementById('full-screen-popover')
  container.appendChild(vm.$el)
  container.style.display = 'inline-block'
  container.style.zIndex = zIndex
  const popper = createPopper(event.target, container)
  const mouseDown = (e) => {
    console.log(e)
    // 判断一下点击像素不在 container 元素内
    const range = container.getBoundingClientRect()
    const { x, y } = e
    const rangeX = range.width + range.left
    const rangeY = range.height + range.top
    if (x <= range.left || x >= rangeX || y <= range.top || y >= rangeY) {
      container.removeChild(vm.$el)
      container.style.display = 'none'
      vm.$destroy()
      popper.destroy()
      window.removeEventListener('mousedown', mouseDown)
      options.onDestory && options.onDestory()
    }
  }
  window.addEventListener('mousedown', mouseDown)
}

export default LocationPopover
