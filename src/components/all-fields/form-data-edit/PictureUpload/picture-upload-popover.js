/*
 * @Description: 把子表单内的 location 组件包一层，保持单例
 * 历史的代码里存在子表嵌套，会导致 el-popover 存在多份实例
 */
import Vue from 'vue'
import merge from 'element-ui/src/utils/merge'
import i18n from '@/lang'
import PictureUpload from './PictureUpload.vue'
import { createPopper } from '@popperjs/core'
import { PopupManager } from 'element-ui/lib/utils/popup'

const defaults = {
  fieldInfo: {},
  mode: 'base',
  formData: {},
  baseFormData: {}
} // Internationalization

function PictureUploadPopover(options, event) {
  const zIndex = PopupManager.nextZIndex()
  options = merge({}, defaults, options)
  const Constructor = Vue.extend(PictureUpload)
  const instance = new Constructor({ i18n, propsData: options })
  const vm = instance.$mount()
  const container = document.getElementById('full-screen-popover')
  container.appendChild(vm.$el)
  container.style.display = 'inline-block'
  container.style.zIndex = zIndex
  const popper = createPopper(event.target, container)
  const destoryEvent = (e) => {
    console.log(e)
    // 判断一下点击像素不在 event 元素内
    const range = container.getBoundingClientRect()
    const { x, y } = e
    const rangeX = range.width + range.left
    const rangeY = range.height + range.top
    console.log(x, y, range.left)
    console.log(x <= range.left, x >= rangeX, y <= range.top, y >= rangeY)
    if (x !== 0 && y !== 0 && (x <= range.left || x >= rangeX || y <= range.top || y >= rangeY)) {
      console.log(4444, options)
      container.removeChild(vm.$el)
      container.style.display = 'none'
      vm.$destroy()
      popper.destroy()
      document.removeEventListener('click', destoryEvent)
      options.onDestory && options.onDestory()
    }
  }
  document.addEventListener('click', destoryEvent)
}

export default PictureUploadPopover
