/*
 * @Description: 经销商 资金
 */
import { getApplyLimit } from '@/api/fund.js'

export default {
  // data () {
  //   return {
  //   }
  // },
  methods: {
    /**
     * @description 新建临时额度申请记录 监听关联客户 给固定额度赋相对应客户额度
     */
    initCreditTemporary() {
      const saasAttr = this.fieldInfo.saasAttr
      if (saasAttr === 'customerId') {
        const attr = this.fieldInfo.attr
        this.addWatch('baseFormData.' + attr, (val, oldVal) => {
          // console.log('val: ', val)
          // console.log('oldVal: ', oldVal)
          if (!val || val.length === 0 || (!oldVal && (this.isEdit || this.isFlow))) return false // 若当前选中 或者 (没有初始值 非新建状态)
          if (!oldVal || oldVal.length === 0 || val[0].id !== oldVal[0].id) {
            const customerId = val[0].id
            getApplyLimit({ customerId, distributorMark: this.distributorMark }).then((res) => {
              const {
                result: { fixedLimit }
              } = res
              const attr = this.fieldList.find((field) => field.saasAttr === 'fixedLimit').attr
              this.baseFormData[attr] = fixedLimit
            })
          }
        })
      }
    },
    /**
     * @description 新建固定额度修改记录 监听关联客户 给原固定额度赋相对应客户额度
     */
    creditFixedLimit() {
      const saasAttr = this.fieldInfo.saasAttr
      if (saasAttr === 'customerId') {
        const attr = this.fieldInfo.attr
        this.addWatch('baseFormData.' + attr, (val, oldVal) => {
          // console.log('val: ', val)
          // console.log('oldVal: ', oldVal)
          if (!val || val.length === 0 || (!oldVal && (this.isEdit || this.isFlow))) return false // 若当前选中 或者 (没有初始值 非新建状态)
          if (!oldVal || oldVal.length === 0 || val[0].id !== oldVal[0].id) {
            const customerId = val[0].id
            getApplyLimit({ customerId, distributorMark: this.distributorMark }).then((res) => {
              const {
                result: { fixedLimit }
              } = res
              const attr = this.fieldList.find((field) => field.saasAttr === 'oldLimit').attr
              this.baseFormData[attr] = fixedLimit
            })
          }
        })
      }
    }
  }
}
