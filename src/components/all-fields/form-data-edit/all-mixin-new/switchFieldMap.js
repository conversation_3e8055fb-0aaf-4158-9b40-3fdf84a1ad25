/*
 * @Description: switch 关联其他字段显示与否
 */

export default {
  created() {
    // 设计模式不启用
    if (this.isDesign) {
      return false
    }
    // 查看模式不启用
    if (this.isSee) {
      return false
    }
    // 关联模式不启用
    if (!this.isLink && this.mode === 'base') {
      const { switchMap } = this.fieldInfo
      if (switchMap && switchMap.length > 0) {
        this.setSwitchFieldShow(1, switchMap)
        this.addWatch(
          'formData.' + this.attr,
          (val, oldVal) => {
            this.setSwitchFieldShow(val, switchMap)
          },
          true
        )
      }
    }
  },

  methods: {
    setSwitchFieldShow(val, switchMap) {
      this.fieldList.forEach((field) => {
        if (switchMap.includes(field.attr)) {
          this.$set(field, 'switchMapHidden', val === 1 ? 0 : 1)
        }
      })
    }
  }
}
