<!--
 * @Description: 进项发票明细
 -->
<template>
  <div class="invoice-table">
    <!-- {{ detailData }} -->
    <el-table
      v-if="detailData && detailData.length > 1"
      :border="!isSee"
      :data="detailData"
      max-height="500"
    >
      <el-table-column
        v-if="!isRed && !isFlow && !isSee"
        :label="$t('operation.operate')"
        width="62"
      >
        <template slot-scope="scope">
          <el-button class="delete__button" type="text" @click="deleteData(scope.row)">{{
            $t('operation.delete')
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('business.related', { attr: $t('business.procurementContract') })"
        width="150"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.purchase | showVal }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="getLabelName" width="280">
        <template slot-scope="scope">
          <span>{{ (scope.row.paySheet || scope.row.payPlan) | showVal }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('formDataEdit.amountOfInvoice')" width="280">
        <template slot-scope="scope">
          <el-input-number
            v-if="!isSee"
            v-model="scope.row.invoiceAmount"
            :controls="false"
            :max="scope.row.maxInvoiceAmount"
            :min="scope.row.minInvoiceAmount"
            @change="dataChange(scope.row)"
          ></el-input-number>
          <span v-else>{{ scope.row.invoiceAmount }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { invoiceDetailGet } from '@/api/form-detail-tabs'
import fundParams from './fund-management-params'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'InvoiceTable',
  filters: {
    showVal(value) {
      if (value && value.length) {
        return `${value[0].name}`
      } else {
        return '--'
      }
    }
  },
  mixins: [fundParams],
  props: {
    dataId: {},
    saasSpecialParamPojo: {
      // saas 特殊逻辑的包装类
    },
    isSee: {
      // 是否是查看模式
      type: Boolean,
      default: false
    },
    fieldInfo: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      isRequest: false, // 是否请求明细接口 if （存在合同 || 存在（付款计划 || 付款单））= true
      detailData: []
    }
  },

  computed: {
    getLabelName() {
      const _formData = this.formData.text_4
      let text4
      if (this.isSee || _formData) {
        text4 = '付款'
      }
      return this.$t('business.related', { attr: text4 })
    },
    detailChange() {
      return this.$store.state.fundManagement.detailChange // 获取合同变化状态 true为变化
    },
    isRed() {
      return (
        this.saasSpecialParamPojo &&
        (this.saasSpecialParamPojo.fromRed === 1 || this.saasSpecialParamPojo.formRedEdit === 1)
      )
    }
  },

  watch: {
    formData: {
      handler(val, oldVal) {
        // 数据变化多请求 加一个防抖
        this.formDataWatchHandler(val, oldVal)
      },
      deep: true
    }
  },

  created() {
    if ((this.isEdit || this.isUesdDraft) && !this.isSee) {
      // 编辑、使用草稿
      this.detailData = []
      const val = this.formData
      this.detailData = val.amountDetail || []
      this.$store.commit('SET_DETAIL_CHANGE', false) // 合同是否改变状态初始化
      if (this.detailData.length) {
        this.setFundDetail()
        this.dataChange() // 金额联动处理
      }
    } else if (this.isSee) {
      this.detailData = []
      const val = this.formData
      this.detailData = val.amountDetail || []
    } else {
      this.watchFunc(this.formData)
    }
  },

  methods: {
    formDataWatchHandler(val, oldVal) {
      this.watchFunc(val, oldVal)
    },
    /**
     * @description 原本在watch内的方法，就是监听后的操作。但是在初始化的时候需要主动触发一次。抽成方法
     * @param {Object} [val] formData数据
     */
    watchFunc(val, oldVal) {
      let val2
      const val1 = val.text_3 // 选择的合同
      const text4 = val.text_4 // 关联付款类型
      const purchaseIdList =
        (Array.isArray(val1) &&
          val1.map((item) => {
            return item.id
          })) ||
        [] // 采购合同id
      const supplierIdList =
        (Array.isArray(val.text_2) &&
          val.text_2.map((item) => {
            return item.id
          })) ||
        [] // 供应商id
      let paySheetIdList = [] // 付款单id
      let payPlanIdList = [] // 付款计划id
      if (text4 && text4.value === '1') {
        // 切换关联付款类型 数据情况
        val2 = val.text_5 // 付款计划
        payPlanIdList =
          (val2 &&
            val2.map((item) => {
              return item.id
            })) ||
          [] // 付款计划id
      } else if (text4 && text4.value === '2') {
        val2 = val.text_6 // 付款单
        paySheetIdList =
          (val2 &&
            val2.map((item) => {
              return item.id
            })) ||
          [] // 付款单id
      } else if (!text4) {
        val2 = undefined
      }
      this.isRequest = (val1 && val1.length >= 1) || (val2 && val2.length >= 1)
      this.getDetailData(
        this.businessType,
        purchaseIdList,
        payPlanIdList,
        paySheetIdList,
        supplierIdList
      )
    },
    /**
     * @description: 请求明细接口，获取金额明细
     * @param {String} [businessType] 业务类型
     * @param {Array} [purchaseIdList] 采购合同
     * @param {Array} [payPlanIdList] 付款计划
     * @param {Array} [paySheetIdList] 付款单
     * @param {Array} [supplierIdList] 供应商
     */
    getDetailData(businessType, purchaseIdList, payPlanIdList, paySheetIdList, supplierIdList) {
      const { accuracy = 2 } = this.fieldInfo
      let totalMoney = this.formData.num_1 || 0 // 页面显示的总开票金额
      let sumVal = 0 // 明细金额之和 用来与开票总金额对比
      this.detailData.forEach((item) => {
        sumVal = +xbb.plus(sumVal, item.invoiceAmount, accuracy)
      })
      if (this.detailChange && this.isRequest) {
        // 若合同或者 付款计划付款单发生变化
        this.$store.commit('SET_DETAIL_CHANGE', false) // 合同是否改变状态初始化
        this.detailData = [] // 初始化明细
        const purchaseInvoiceId = this.dataId // 当前发票id 有值的话 说明不是新建 后端用来筛选金额
        invoiceDetailGet({
          businessType,
          purchaseIdList,
          payPlanIdList,
          paySheetIdList,
          supplierIdList,
          purchaseInvoiceId
        }).then((res) => {
          this.detailData = res.result.amountDetail || [] // 获取明细相关内容
          if (this.detailData.length) {
            this.setFundDetail()
            this.dataChange() // 金额联动处理
          }
        })
      } else if (totalMoney !== sumVal) {
        // 合同不变 金额改变 进项金额分配
        this.detailData.forEach((item) => {
          if (totalMoney >= item.maxInvoiceAmount) {
            // 开票总额 大于 当前该条最大值
            item.invoiceAmount = item.maxInvoiceAmount // 本条开票金额
            totalMoney = +xbb.subtract(totalMoney, item.invoiceAmount) // 剩余分配开票金额
          } else {
            item.invoiceAmount = totalMoney // 本条开票金额 为 剩下开票总金额
            totalMoney = 0 // 分配完成 可分配为0
          }
        })
        if (this.detailData.length) {
          this.setFundDetail()
          this.dataChange() // 金额联动处理
        }
      }
    },
    // 金额变化
    dataChange(val) {
      console.log('checkrow', val)
      const sumAmount = this.detailData[0].sumAmount // 开票金额最大值
      let detailSumAmount = 0 // 明细内总金额
      const { accuracy = 2 } = this.fieldInfo // 从解释里拿精度
      this.detailData.forEach((item) => {
        detailSumAmount = +xbb.plus(detailSumAmount, item.invoiceAmount, accuracy)
      })
      detailSumAmount < 0 && (detailSumAmount = 0)
      // 当前明细金额之和小于总金额 马上重置外层总金额，反之 则使用nextTick修复数据过大时 视图没有及时更新问题
      if (detailSumAmount < sumAmount) {
        this.$set(this.formData, 'num_1', detailSumAmount)
      } else {
        this.$set(this.formData, 'num_1', sumAmount)
      }
    },
    // 点击删除
    deleteData(row) {
      const _this = row
      if (_this.paySheet && _this.paySheet.length) {
        // 付款单
        this.deleteFunc(this.detailData, _this.paySheet[0].id, 'paySheet') // 明细table内删除付款单
        this.deleteFuncData(this.formData.text_6, row.paySheet[0].id) // 外层formData删除付款单
      } else if (_this.payPlan && _this.payPlan.length) {
        // 付款计划
        this.deleteFunc(this.detailData, _this.payPlan[0].id, 'payPlan') // 明细table内删除付款计划
        this.deleteFuncData(this.formData.text_5, row.payPlan[0].id) // 外层formData删除付款计划
      } else if (_this.purchase) {
        // 采购合同
        this.deleteFunc(this.detailData, _this.purchase[0].id, 'purchase') // 明细table内删除采购合同
        this.deleteFuncData(this.formData.text_3, row.purchase[0].id) // 外层formData删除采购合同
      }
    },
    // 执行删除明细
    deleteFunc(data, id, type) {
      data.forEach((item, index) => {
        if (item[type][0].id === id) {
          data.splice(index, 1)
        }
      })
    },
    // 执行删除源数据
    deleteFuncData(data, id) {
      data.forEach((item, index) => {
        if (item.id === id) {
          data.splice(index, 1)
        }
      })
    }
  },
  beforeDestroy() {
    this.setFundDetailDestroy()
  }
}
</script>

<style lang="scss" scoped>
.invoice-table {
  width: max-content;
  .delete__button {
    color: $error-base-color-6;
  }
}
</style>
