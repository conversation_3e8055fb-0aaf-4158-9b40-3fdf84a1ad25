/*
 * @Description: 资金明细vuex
 */
export default {
  inject: {
    formDialog: { default: () => ({}) }
  },
  props: {
    formData: {
      // 数据对象
      type: Object,
      default() {
        return {}
      }
    },
    isFlow: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isRetuenEdit: {
      // 退货退款新建回款单 编辑数据 其实走的新建逻辑 回显编辑模式
      type: Boolean,
      default: false
    },
    businessType: {
      type: Number,
      default: 0
    },
    isUesdDraft: {
      type: Boolean,
      default: false
    },
    fatherBusinessType: {
      type: Number,
      default: 0
    }
  },
  computed: {
    // isChild
    isFormChild() {
      return this.formDialog && this.formDialog.isChild
    }
  },
  methods: {
    setFundDetail() {
      if (this.isFormChild) {
        this.$store.commit('formDataDialogChild/SET_FUND_MANAGEMENT_DETAIL', this.detailData)
      } else {
        this.$store.commit('SET_FUND_MANAGEMENT_DETAIL', this.detailData)
      }
    },
    setFundDetailDestroy() {
      if (this.isFormChild) {
        this.$store.commit('formDataDialogChild/SET_FUND_MANAGEMENT_DETAIL', null)
      } else {
        this.$store.commit('SET_FUND_MANAGEMENT_DETAIL', null)
      }
    }
  }
}
