<!--
 * @Description: 付款单明细/
 -->
<template>
  <div class="payment-detail-table">
    <el-table v-if="detailData && detailData.length > 1" border :data="detailData" max-height="500">
      <el-table-column
        :label="$t('business.related', { attr: $t('business.procurementContract') })"
        width="140"
      >
        <template slot-scope="scope">
          {{ (scope.row.text_5[0] && scope.row.text_5[0].name) || '--' }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('formDataEdit.relatedPaymentPlan')" width="150">
        <template slot-scope="scope">
          {{ scope.row.text_6 | paymentName }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('formDataEdit.paymentAmount')" width="200">
        <template slot-scope="scope">
          <el-input-number
            v-if="!editable"
            v-model="scope.row.num_1"
            :controls="false"
            :disabled="editable"
            :max="scope.row.amount"
            :min="0"
            @change="dataChange(scope.row)"
          ></el-input-number>
          <el-input-number
            v-if="editable"
            v-model="scope.row.num_1"
            :controls="false"
            :disabled="editable"
            :max="scope.row.amount"
            @change="dataChange(scope.row)"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column :label="$t('nouns.belonger')" width="250">
        <template slot-scope="scope">
          <div class="detail-cell">
            <UserRadio
              v-model="scope.row.array_1[0]"
              :field-info="fieldInfo"
              :readonly="readonly"
              :width-control="widthControl"
              @modelChange="userChange(scope.row)"
            ></UserRadio>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { payPlanSheetGet } from '@/api/form-detail-tabs'
import UserRadio from '../../UserRadio/UserRadio'
import fundParams from './fund-management-params'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'PaymentDetailTable',

  components: {
    UserRadio
  },

  filters: {
    paymentName(value) {
      if (!value || !value.length) {
        return '--'
      } else {
        return `${value[0].name}`
      }
    }
  },

  mixins: [fundParams],
  props: {
    fieldList: {
      // 全部字段
    },
    metaFieldList: {
      // 全部字段源数据
    },
    fieldInfo: {
      // 当前字段详细
      type: Object,
      required: true
    }
  },

  data() {
    return {
      flowEdit: false, // 是否流程 编辑 无明细情况 若无明细情况为合同快捷新建
      returnPlanData: [],
      detailData: [],
      numKeyStatus: false, // 判断金额字段的key 预付款核销为num_2 其他为 num_1
      widthControl: true // 调用组件 修改组件宽度判断
    }
  },

  computed: {
    detailChange() {
      return this.$store.state.fundManagement.detailChange
    },
    editable() {
      return !this.fieldInfo.editable
    },
    readonly() {
      const belongId = this.metaFieldList.find((item) => {
        return item.saasAttr === 'belongId'
      })
      let isEditAble
      belongId && (isEditAble = !belongId.editable)
      return (!this.isFlow && this.isEdit && !this.isUesdDraft) || (this.isFlow && isEditAble) // 4.6 https://xbb.yuque.com/lfwuxq/vsf9sv/tisg7y#qj7I8
    }
  },

  watch: {
    formData: {
      handler(val, oldVal) {
        this.watchFunc(val, oldVal)
      },
      deep: true
    }
  },

  created() {
    if ((this.isEdit && !this.isUesdDraft) || this.isRetuenEdit) {
      this.$store.commit('SET_DETAIL_CHANGE', false)
      const val = this.formData
      this.detailData = val.amountDetail || []
      if (this.isFlow && !val.amountDetail) {
        // 流程编辑 无明细 为合同快捷新建
        this.flowEdit = true
      }
      if (this.detailData.length) {
        // this.$store.commit('SET_FUND_MANAGEMENT_DETAIL', this.detailData)
        // this.numKeyStatus = (this.isFlow && ['2'].includes(this.formData.text_10.value)) || (!this.isFlow && [804].includes(this.businessType)) // 2代表预付款核销 804。由于审批回款单统一802 改判text_10
        this.setFundDetail()
        this.dataChange()
      }
    } else if (this.isUesdDraft) {
      const val = this.formData
      this.detailData = val.amountDetail || []
    } else {
      this.watchFunc(this.formData)
    }
  },
  mounted() {
    if (
      this.formData &&
      Array.isArray(this.formData.array_1) &&
      this.formData.array_1.length === 0 &&
      this.detailData.length > 0
    ) {
      // 容错 采购退货单关联新建红冲付款单出现无归属人
      this.userChange()
    }
  },
  beforeDestroy() {
    // this.$store.commit('SET_FUND_MANAGEMENT_DETAIL', undefined)
    this.setFundDetailDestroy()
  },
  methods: {
    /**
     * @description 原本在watch内的方法，就是监听后的操作。但是在初始化的时候需要主动触发一次。抽成方法
     * @param {Object} [val] formData数据
     */
    watchFunc(val, oldVal) {
      const totalMoney = 0
      this.returnPlanData = []
      const { accuracy = 2 } = this.fieldInfo
      const contractList = [] // 关联采购合同id
      const paymentIdList = [] // 关联付款计划id
      const contract = val.text_5 // text_5关联采购合同i
      const paymemtId = val.text_6 // text_6关联付款计划
      let linkPreId
      val.text_15 && (linkPreId = val.text_15[0].id) // 关联原始付款单
      if (!contract) return
      Array.isArray(contract) &&
        contract.forEach((item) => {
          contractList.push(item.id)
        })
      Array.isArray(paymemtId) &&
        paymemtId.forEach((item) => {
          paymentIdList.push(item.id)
        })
      this.numKeyStatus =
        (this.isFlow && ['2'].includes(this.formData.text_10.value)) ||
        (!this.isFlow && [804].includes(this.businessType)) // 2代表预付款核销 804。由于审批回款单统一802 改判text_10
      if ((!this.detailData.length && !this.flowEdit) || this.detailChange) {
        // 首次进入 或合同变化 ps:(!this.detailData.length && !this.flowEdit)为非流程编辑时 即正常新建 无明细为首次进入
        this.paymentOrderDetail(val, totalMoney, linkPreId, contractList, paymentIdList)
      } else {
        // 金额分配
        console.log('unfirst come')
        let totalMoney = 0
        if (this.numKeyStatus) {
          totalMoney = this.formData.num_2
        } else {
          totalMoney = this.formData.num_1
        }
        let sumVal = 0 // 明细金额之和 用来与开票总金额对比
        this.detailData.forEach((item) => {
          sumVal = +xbb.plus(sumVal, item.num_1 || 0, accuracy)
        })
        if (totalMoney !== sumVal && !this.detailChange) {
          this.detailData = this.detailData.map((item) => {
            let thisMoney = 0
            if (totalMoney >= item.amount) {
              thisMoney = item.amount // 本条金额
              totalMoney = +xbb.subtract(totalMoney, thisMoney)
            } else {
              thisMoney = totalMoney
              totalMoney = 0 // 分配完成 可分配为0
            }
            item.num_1 = thisMoney
            return item
          })
        }
        if (this.detailData.length === 1 && val.array_1 && val.array_1.length === 1) {
          // 容错 回款单快捷新建发票出现无归属人
          this.detailData[0].array_1 = val.array_1
        }
      }
      if (this.detailData.length) {
        // this.$store.commit('SET_FUND_MANAGEMENT_DETAIL', this.detailData)
        this.setFundDetail()
        this.dataChange()
      }
    },

    /**
     * @description: 请求明细接口，获取金额明细
     */
    paymentOrderDetail(val, totalMoney, linkPreId, contractList, paymentIdList) {
      console.log('first come')
      if (contractList.length === 0) {
        // 采购合同清空 数据init
        this.detailData = []
        return false
      }
      this.$store.commit('SET_DETAIL_CHANGE', false)
      payPlanSheetGet({
        businessType: this.businessType,
        dataIdList: contractList,
        paymentIdList: paymentIdList,
        linkPreId
      }).then((res) => {
        this.detailData = res.result.list.map((item) => {
          totalMoney = +xbb.plus(totalMoney, item.num_1)
          if (item.text_6) {
            // 接口里付款计划
            this.returnPlanData.push({
              ...item.text_6[0],
              num_1: item.num_1,
              amount: item.amount
            })
          } else if (!item.text_6) {
            item.text_6 = []
          }
          return {
            // ...this.formData,
            ...item
          }
        })
        if (this.numKeyStatus) {
          val.num_2 = totalMoney
        } else {
          val.num_1 = totalMoney
        }
        val.text_6 = this.returnPlanData
        val.text_5 = val.text_5.map((item) => {
          item.num_1 = 0
          res.result.list.forEach((v) => {
            if (item.id === v.text_5[0].id) {
              // item.num_1 += v.amount
              item.num_1 = +xbb.plus(item.num_1, v.amount)
            }
          })
          return item
        })
        if (this.detailData.length) {
          // this.$store.commit('SET_FUND_MANAGEMENT_DETAIL', this.detailData)
          this.setFundDetail()
          this.dataChange('requiredata')
        }
        this.userChange()
      })
    },
    dataChange(type) {
      let sumVal = 0
      const { accuracy = 2 } = this.fieldInfo // 从解释里拿精度
      this.detailData.forEach((item) => {
        if (!item.num_1) {
          item.num_1 = 0
        }
        sumVal = +xbb.plus(sumVal, item.num_1 || 0, accuracy)
      })
      // 804预付款核销
      if (this.businessType === 804) {
        const limitVal = this.formData.text_4[0].advancePaymentBalance
        // 预付款余额 小于 核销总金额 ，核销总金额为付款预付款余额 两者取小
        sumVal = Math.min.call(null, limitVal, sumVal)
      }
      if (this.numKeyStatus) {
        if (!(type === 'requiredata' && this.isFlow && this.formData.num_2 < sumVal)) {
          // 审批情况下 请求接口后 当前金额若较小 取当前金额（新建合同直接建回款单 没有明细会请求接口 导致数据重置）
          this.$set(this.formData, 'num_2', sumVal)
        }
      } else {
        if (!(type === 'requiredata' && this.isFlow && this.formData.num_1 < sumVal)) {
          this.$set(this.formData, 'num_1', sumVal)
        }
      }
    },
    userChange() {
      const arrayBus = []
      const arrary = []
      this.detailData.forEach((item) => {
        if (item.array_1[0] && !arrayBus.includes(item.array_1[0].id)) {
          arrayBus.push(item.array_1[0].id)
          arrary.push({
            ...item.array_1[0]
          })
        }
      })
      this.$set(this.formData, 'array_1', arrary)
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-detail-table {
  width: max-content;
  .delete__button {
    color: $error-base-color-6;
  }
  .detail-cell {
    width: 100%;
    overflow: scroll;
  }
}
</style>
