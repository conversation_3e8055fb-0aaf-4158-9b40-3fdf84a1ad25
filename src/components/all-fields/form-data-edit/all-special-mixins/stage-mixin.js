// 阶段推进器特殊逻辑 100， 301， 8000
export default {
  methods: {
    init_stage_radio() {
      this.init_rely_stage()
    },
    // 监听阶段推进器字段，带入阶段比例字段
    init_rely_stage() {
      if (
        ['clueStage', 'customerStage', 'saleStage'].includes(this.fieldInfo.saasAttr)
        // this.fieldInfo.editable
      ) {
        this.addWatch(`formData.${this.attr}`, (val, oldVal) => {
          if (val) {
            this.formData['long_3'] = val.stageRatio
          } else {
            this.formData['long_3'] = null
          }
        })
      }
    }
  }
}
