/*
 * @Description: 出库
 */
import businessEnum from '@/constants/common/business-type.js'
import { makerFormData } from '@/components/form-data-edit/utils.js'
import { getRelyCustomerInfo } from '@/api/formData'
import xbb from '@xbb/xbb-utils'
import { getUnitPrice } from '@/utils/unit.js'

export default {
  data() {
    return {
      outstockExplains: []
    }
  },
  methods: {
    init_outstock() {
      this.init_outstock_type()
      this.init_getRelyProductList_outstock()
      this.init_setRefId_outstock()
      this.init_relyProductSelected_outstock()
      this.init_warehouse_outstock()
      this.init_batchEdit_warehouse_outstock()
      this.init_relyGuarantee_outstock()
      this.init_productNoEdit_outStock()
      this.init_relyCustomerInfo()
      this.init_relyCustomerInfo_In_WORK_ORDER_OUTSTOCK()
      this.init_multiUnit_outStock() // 多单位处理价格
      this.init_selectAddress_outStock() // 收货人、寄件人选择
      if (!this.distributorMark) return // 以下逻辑经销商出库发货单没有
      this.init_distributor_outStock()
    },
    /**
     * 在联系电话中订阅关联单据(此处节约性能，让地址、电话附属在联系人的订阅回调中处理)
     * 当关联单据发生变化的时候
     * 调用接口联动该关联单据的信息进行set value
     */
    init_relyCustomerInfo_In_WORK_ORDER_OUTSTOCK() {
      // 只有工单出库才进
      if (this.businessType !== businessEnum.WORK_ORDER_OUTSTOCK) return
      if (this.fieldInfo.saasAttr === 'customerPhone' && !this.isEdit) {
        this.addWatch(
          `formData.text_4`,
          (val, oldVal) => {
            const contractId = val && val[0] ? val[0]['id'] : ''
            if (contractId) {
              getRelyCustomerInfo({
                dataId: contractId,
                businessType: this.businessType
              }).then(({ result: { customerObject } }) => {
                const customerPhone = customerObject['subForm_3'] || []
                const addressDetail = customerObject['address_2']
                const relationPerson = customerObject['text_68']
                const customerName = customerObject['text_66']
                // 修复子表单中的行的空key
                const formatVal = customerPhone.map((item) => {
                  const defaultVal = makerFormData(this.fieldInfo.subForm.items)
                  return {
                    ...defaultVal,
                    ...item
                  }
                })
                // 在这个悖论下终止
                // if (!this.formData['text_66'] || !this.formData['text_66'].length) {
                //   this.formData['text_66'] = customerName
                // }
                this.formData['text_66'] = customerName
                this.formData['text_68'] = relationPerson
                this.formData['subForm_3'] = formatVal
                this.formData['address_2'] = addressDetail
              })
            } else {
              this.formData['subForm_3'] = undefined
              this.formData['address_2'] = undefined
              this.formData['text_68'] = undefined
              this.formData['text_66'] = undefined
            }
          },
          false
        )
      }
    },
    /**
     * 在联系电话中订阅客户(此处节约性能，让地址附属在电话的订阅回调中处理)
     * 当客户发生变化的时候
     * 调用接口联动该用户的信息进行set value
     */
    init_relyCustomerInfo() {
      // 只有销售出库才进噢
      if (this.businessType !== businessEnum.CONTRACT_OUTSTOCK) return
      if (this.fieldInfo.saasAttr === 'customerPhone') {
        this.addWatch(
          `formData.text_66`,
          (val, oldVal) => {
            const customerId = val && val[0] ? val[0]['id'] : ''
            if (customerId) {
              getRelyCustomerInfo({
                dataId: customerId,
                businessType: this.businessType
              }).then(({ result: { customerObject } }) => {
                const customerPhone = customerObject['subForm_3'] || []
                const addressDetail = customerObject['address_2']
                // 修复子表单中的行的空key
                const formatVal = customerPhone.map((item) => {
                  const defaultVal = makerFormData(this.fieldInfo.subForm.items)
                  return {
                    editFlag: 1,
                    subId: xbb.guid(),
                    ...defaultVal,
                    ...item
                  }
                })
                this.formData['subForm_3'] = formatVal
                this.formData['address_2'] = addressDetail
              })
            } else {
              this.formData['subForm_3'] = undefined
              this.formData['address_2'] = undefined
            }
          },
          false
        )
      }
    },
    /**
     *  初始化出库类型逻辑
     *  当自己是选择产品字段时
     *  当选择其他出库时，产品才可编辑
     *  当是其他出库类型时必须选择单据有值才能编辑
     *  当其他出库时候仓库可选，别的不可选
     */
    init_outstock_type() {
      // 入库类型字段逻辑
      if (this.fieldInfo.saasAttr === 'products') {
        // 获取初始化关联产品解释
        this.outstockExplains =
          this.fieldInfo.subForm && xbb.deepClone(this.fieldInfo.subForm.items)
        // 出库类型变化，改变可编辑性，默认触发
        this.addWatch(`formData.text_3`, (val) => {
          // this.formData[this.attr] = []
          if (
            val &&
            [businessEnum.OTHER_OUTSTOCK].includes(this.businessType) &&
            (this.formMode === 'add' || this.isFlow)
          ) {
            // 其他出库、盘亏出库、调拨出库
            this.$set(this.fieldInfo, 'noEditable', 0)
            /** 不同类型下不同字段是否可编辑
             */
            this.outstockExplains.forEach((item) => {
              // 其他类型均不可编辑，（拆分的单据由后端给到，如生产物料出库8可编辑成本）
              if (item.saasAttr === 'cost') {
                this.$set(item, 'editable', 0)
                // 盘盈出库调拨出库默认仓库不可编辑
              } else if (item.saasAttr === 'warehouse') {
                if (['3', '4'].includes(val['value'])) {
                  this.$set(item, 'editable', 0)
                } else {
                  this.$set(item, 'editable', 1)
                }
              }
            })
          }
        })

        // 每次单据变化后，切换出库产品字段是否可编辑 默认触发
        this.addWatch(`formData.text_4`, (val) => {
          // 单据有值时，出库产品才可以编辑
          if (val && val[0]) {
            this.$set(this.fieldInfo, 'noEditable', 0)
          } else if (this.formData['text_3'] && this.formData['text_3']['value'] === '5') {
            // 只有其他出库单业务下，单据无值时，出库产品也可以编辑
            this.$set(this.fieldInfo, 'noEditable', 0)
          } else {
            this.$set(this.fieldInfo, 'noEditable', 1)
          }
        })
      }
    },
    /**
     * 初始化关联单据查询关联产品
     */
    init_getRelyProductList_outstock() {
      if (this.fieldInfo.saasAttr !== 'products') return

      this.addWatch(
        `formData.text_4`,
        (val, oldVal) => {
          if (val === oldVal) return

          if (!(val && val.length)) {
            this.formData['array_1'] = []
            return
          }
          const dataId = val[0] && val[0].id
          const sourceFormId = val[0] && val[0].formId
          const type = (this.formData['text_3'] && this.formData['text_3'].value) || '' // 入库类型选项值
          if (!dataId) return
          // 生产领料单：默认取关联单据里的仓库（有就取，没有空）
          if ([businessEnum.PRODUCTION_MATERIEL_OUTSTOCK].includes(this.businessType)) {
            this.baseFormData['text_2'] = val[0]['text_7']
          }
          this.getRelyProductList({
            dataId,
            sourceFormId,
            type: this.getRefType(this.businessType, type)
          }).then((productArray = {}) => {
            const products = productArray['array_1']
            // let formatVal = products.map(item => {
            //   let defaultVal = makerFormData(this.fieldInfo.subForm.items)
            //   return {
            //     editFlag: 1,
            //     subId: guid(),
            //     ...defaultVal,
            //     ...item
            //   }
            // })
            // this.formData[this.attr] = formatVal
            const subVal = products.map((item) => {
              const row = makerFormData(this.fieldInfo.subForm.items)
              return {
                editFlag: 1,
                subId: xbb.guid(),
                ...item,
                ...row
              }
            })
            this.formData[this.attr] = subVal
            // 此处留下钩子主要是为了追踪定位数据变化-并没有接受此事件的组件
            this.$emit('changeFormData', this.attr)
            const wareHouse = () => {
              if (![businessEnum.PRODUCTION_MATERIEL_OUTSTOCK].includes(this.businessType)) {
                this.$nextTick(() => {
                  // 将产品仓库带入批量仓库作为回显，有值回显，没值不显示
                  const productWarehouse =
                    this.formData['array_1'].length && this.formData['array_1'][0]['text_6']
                  const warehouseId = this.formData['text_2']
                  if (productWarehouse && productWarehouse.length) {
                    this.formData['text_2'] = productWarehouse
                  } else if (warehouseId && warehouseId.length) {
                    // 如果上面选了仓库时，下面的批量修改为上面的总仓
                    const itemText = (item) => {
                      if (this.getHasProductWarehouse(item['text_1'], warehouseId[0]['id'])) {
                        item['text_6'] = warehouseId
                      }
                    }
                    this.formData['array_1'].forEach(itemText)
                  }
                })
              }
            }
            const dataChange = () => {
              const subVal = this.formData[this.attr] || []
              subVal.forEach((item, index) => {
                const relyRow = products[index] || {}
                Object.keys(relyRow).forEach((key) => {
                  item[key] = relyRow[key]
                })
              })
            }
            this.$nextTick(() => {
              dataChange()
              wareHouse()
              if (this.businessType === businessEnum.CONTRACT_OUTSTOCK) {
                // 销售出库单、出库发货单
                this.formData['num_62'] = productArray['num_62'] // 优惠金额
                this.formData['num_61'] = productArray['num_61'] // 整单折扣率
                this.formData['num_39'] = productArray['num_39'] // 其他费用
                //   if (this.fieldInfo.distributorMark === 0) { // 销售出库单
                //     this.formData['num_61'] = productArray['num_61'] // 整单折扣率
                //   }
              }
            })
            wareHouse()
          })
        },
        false
      )
    },
    /**
     * 选择产品关联字段
     * 记录关联单据id
     */
    init_setRefId_outstock() {
      if (
        this.fieldInfo.saasAttr === 'products' ||
        (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'product')
      ) {
        this.addWatch(`baseFormData.text_4`, (val) => {
          if (val && val.length) {
            // this.fieldInfo.fieldMapHidden = 1
            this.refId = val[0].id
            this.sourceFormId = val[0].formId
          } else {
            this.refId = undefined
            this.sourceFormId = undefined
          }
        })
        // 其他出库单需要业务类型
        if ([businessEnum.OTHER_OUTSTOCK].includes(this.businessType)) {
          this.addWatch(`baseFormData.text_3`, (val) => {
            if (val) {
              this.refType = this.baseFormData['text_3'] && this.baseFormData['text_3'].value
            } else {
              this.refType = undefined
            }
          })
        }
      }
    },
    /**
     * 关联产品
     * 选择产品后联动改变自身这行的相关字段
     */
    init_relyProductSelected_outstock() {
      if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'product') {
        this.addWatch(
          `formData.${this.attr}`,
          (val, oldVal) => {
            // 若相等不执行
            if (val && oldVal && val['id'] === oldVal['id']) return
            if (val && val['sourceData']) {
              // 多单位标识
              this.getUnitEditSomething(this.formData, val)
              // 序列号管理、保质期管理、批次号管理状态取值
              this.getbatchShelfLife(this.formData, val)
              // 选择产品后，需要回显的信息会放到 sourceInfo 中，前端直接遍历并覆盖即可
              const sourceInfo = val.sourceData.sourceInfo
              if (sourceInfo && !this.formData['text_5']) {
                for (const key in sourceInfo) {
                  this.$emit('updateFormdata_sourceInfo', this.formData)
                  this.formData[key] = sourceInfo[key]
                }
                console.log(447, sourceInfo)
                this.getOutstockStockCost(val)
              }
            }
          },
          false
        )
      }
    },
    // 仓库库存成本关联取值
    getOutstockStockCost(product) {
      // 已选出库仓库，产品在此仓库中，直接回显出库仓库
      if (product.sourceData && product.sourceData.warehouse) {
        const relyWarehouse = product.sourceData.warehouse // 产品关联的仓库集合
        const baseWarehouseId =
          this.baseFormData['text_2'] &&
          this.baseFormData['text_2'][0] &&
          this.baseFormData['text_2'][0].id // 已选表单出库仓库id
        const warehouseId =
          (this.formData['text_6'] &&
            this.formData['text_6'][0] &&
            this.formData['text_6'][0].id) ||
          baseWarehouseId // 已选仓库id

        // 调拨盘点出库
        const type =
          (this.baseFormData['text_3'] && this.baseFormData['text_3'].value) ||
          this.getRefType(this.businessType)
        if (warehouseId in relyWarehouse) {
          // 取到匹配到的仓库信息
          const warehouseData = product.sourceData.warehouse[warehouseId] || {}
          const { cost = 0, stock = 0 } = warehouseData
          // 这行产品已有仓库不进行赋值（1.兼容批次拆分需触发公式计算发现的问题; 2.使代码更严谨）
          const subWarehouseId =
            this.formData['text_6'] && this.formData['text_6'][0] && this.formData['text_6'][0].id
          if (subWarehouseId !== warehouseId) {
            this.formData['text_6'] = this.baseFormData['text_2'] // 仓库
          }
          if (['3', '4'].includes(type)) return false
          // 采购退货出库不取仓库成本
          if (
            ![businessEnum.RETURNED_PURCHASE_OUTSTOCK].includes(this.businessType) &&
            !this.formData['text_5']
          ) {
            // 必须保证没有批次信息的时候，才取仓库成本
            this.formData['num_2'] = cost
            this.multiUnitSellMap()
          }
          // 必须保证没有批次信息的时候，才取仓库库存
          if (!this.formData['text_5']) {
            this.formData['num_12'] = stock
          }
        } else {
          this.formData['text_6'] = [] // 触发init_warehouse_outstock
          if (product.sourceData.sourceInfo) {
            this.formData['num_2'] = product['sourceData']['sourceInfo']['num_2'] // 全仓成本
            this.formData['num_12'] = product['sourceData']['sourceInfo']['num_12'] // 全仓库存
            this.multiUnitSellMap()
          }
        }
      }
    },
    // 清空批次处理成本库存
    clearOutstockBatch() {
      console.log(566, this.formData['text_1'])
      const product = this.formData['text_1']
      if (product && product.sourceData && product.sourceData.warehouse) {
        const relyWarehouse = product.sourceData.warehouse // 产品关联的仓库集合
        const warehouseId =
          this.formData['text_6'] && this.formData['text_6'][0] && this.formData['text_6'][0].id // 已选仓库id
        if (warehouseId in relyWarehouse) {
          // 取到匹配到的仓库信息
          const warehouseData = product.sourceData.warehouse[warehouseId] || {}
          const { cost = 0, stock = 0 } = warehouseData
          // 采购退货出库不取批次成本
          if (
            ![businessEnum.RETURNED_PURCHASE_OUTSTOCK].includes(this.businessType) &&
            !this.formData['text_5']
          ) {
            // 必须保证没有批次信息的时候，才取仓库成本
            this.formData['num_2'] = cost
            this.multiUnitSellMap()
          }
          // 必须保证没有批次信息的时候，才取仓库库存
          if (!this.formData['text_5']) {
            this.formData['num_12'] = stock
          }
        } else {
          if (product.sourceData.sourceInfo) {
            this.formData['num_2'] = product['sourceData']['sourceInfo']['num_2'] // 源单成本
            this.formData['num_12'] = product['sourceData']['sourceInfo']['num_12'] // 源单库存
            this.multiUnitSellMap()
          }
        }
      }
    },
    /**
     * 选择产品中的分仓逻辑
     * 当仓库发生改变时，去前面的产品中找到自己对应的仓库 + 重置批次号
     * 当产品没有时不做操作
     * 当找不到自己这个仓库时不做操作
     * 找到则更新库存成本，除了采购退货出库
     * 更新仓库库存数量
     */
    init_warehouse_outstock() {
      if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'warehouse') {
        this.addWatch(
          `formData.${this.attr}`,
          (val, oldVal) => {
            const type =
              (this.baseFormData['text_3'] && this.baseFormData['text_3'].value) ||
              this.getRefType(this.businessType)
            // 采购退货出库特殊逻辑（上游有启用批次号的产品数据处理）
            if (['1'].includes(type) && this.formData['enableBatchShelfLife']) {
              const baseWareHouse = this.baseFormData['text_2']
              const baseWareHouseId =
                baseWareHouse && baseWareHouse.length && baseWareHouse[0] && baseWareHouse[0].id
              const warehouse = val && val.length && val[0].id
              if (warehouse && warehouse === baseWareHouseId && oldVal === undefined) return
            }
            // 盘亏出库、调拨出库特殊, 默认是盘点单、调拨单的批次，无需清空, 仓库也不能更改，所以以下逻辑不用执行
            if (['3', '4'].includes(type)) return false
            this.formData['text_5'] = ''
            const product = this.formData['text_1']
            if (!product) {
              return
            }
            // let type = (this.baseFormData['text_3'] && this.baseFormData['text_3'].value) || this.getRefType(this.businessType)
            if (val && val.length) {
              const warehouseId = val[0].id
              if (product.sourceData && product.sourceData.warehouse) {
                const warehouseData = product.sourceData.warehouse[warehouseId] || {}
                // 解构成本和库存, 采购退货出库成本不由仓库影响
                const { cost = 0, stock = 0 } = warehouseData
                if (!['1'].includes(type)) {
                  this.formData['num_2'] = cost
                  this.multiUnitSellMap()
                }
                this.formData['num_12'] = stock
              }
            } else {
              // 采购退货出库成本不由仓库影响
              if (!['1'].includes(type)) {
                this.formData['num_2'] = undefined
              }
              this.formData['num_12'] = undefined
            }
            // 清空序列号
            if (
              JSON.stringify(val) !== JSON.stringify(oldVal) &&
              this.formData.enableSerialNumber
            ) {
              this.formData.seq = []
              this.formData.num_3 = undefined
            }
          },
          false
        )
      }
    },
    /**
     * 判断该仓库是否可以设置为该产品的关联仓库
     * product  text_1
     * warehouse 'id'
     * return boolean
     */
    getHasProductWarehouse(product, warehouseId) {
      const relyWarehouse = product && product.sourceData && product.sourceData.warehouse
      return relyWarehouse && warehouseId in relyWarehouse
    },
    /**
     * 当上面的产品发生改变时
     * 选择产品的仓库发生批量变化
     * 当批量修改的仓库在该行的关联产品的关联仓库中时更新为该值
     * 不在时不做操作
     */
    init_batchEdit_warehouse_outstock() {
      if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'warehouse') {
        this.addWatch(
          `baseFormData.text_2`,
          (val, oldVal) => {
            // 生产领料单（物料出库）切换仓库清空单据
            if ([businessEnum.PRODUCTION_MATERIEL_OUTSTOCK].includes(this.businessType)) {
              ;(function () {
                const refField = this.baseFormData['text_4'] // 此时单据的数据
                const refValue = refField && refField[0] && refField[0]['text_7'] // 此时单据中关联的出库仓库的数据
                // 如果此时仓库的值 等于单据中 对应的仓库值，说明此时就是悖论
                const bool =
                  refValue && refValue[0] && refValue[0]['id'] === (val && val[0] && val[0]['id'])
                if (!bool && this.baseFormData['text_4']) {
                  this.baseFormData['text_4'] = []
                }
              }).call(this)
            }
            if (val && val.length) {
              const warehouseId = val[0].id
              const product = this.formData['text_1'] // 取出当前行的产品数据
              const relyWarehouse = product && product.sourceData && product.sourceData.warehouse // 取出产品中的关联仓库信息
              // 当需要批量改变的仓库在该产品的关联仓库中时t
              if (relyWarehouse && warehouseId in relyWarehouse) {
                // 如果批量选择的仓库与产品已选仓库相等不赋值
                const formWarehouseId =
                  this.formData[this.attr] &&
                  this.formData[this.attr][0] &&
                  this.formData[this.attr][0].id
                if (warehouseId === formWarehouseId) return
                this.formData[this.attr] = val
              }
              // else {
              //   this.formData[this.attr] = []
              // }
            } else {
              // this.formData[this.attr] = []
              // 调拨和盘点出库、物料出库修改仓库后把关联单据清空
              const type =
                (this.baseFormData['text_3'] && this.baseFormData['text_3'].value) ||
                this.getRefType(this.businessType)
              if (['3', '4', '8'].includes(type)) {
                this.baseFormData['text_4'] = []
              }
            }
          },
          false
        )
      }
    },
    /**
     * 初始化关联查询保质期
     * 批次选择订阅自己
     * 当批次发生变化时
     * 更新库存数量等于批次库存
     * 更新生产日期和保质期
     */
    init_relyGuarantee_outstock() {
      if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'batch') {
        // 调拨纯取后端数据，不走
        const type =
          (this.baseFormData['text_3'] && this.baseFormData['text_3'].value) ||
          this.getRefType(this.businessType)
        if (!['3', '4'].includes(type)) {
          this.addWatch(
            `formData.${this.attr}`,
            (val, oldVal) => {
              console.log(22, val, 55, oldVal)
              // if (val) {
              //   this.$nextTick(() => {
              //     let { productCost, guaranteePeriod, produceDate, productNum } = (this.getBatchData() || {})
              //     this.formData['num_8'] = produceDate // 生产日期
              //     this.formData['num_9'] = guaranteePeriod // 保质期
              //     this.formData['num_12'] = productNum // 批次库存
              //     // 采购退货出库不取批次成本
              //     if (![businessEnum.RETURNED_PURCHASE_OUTSTOCK].includes(this.businessType)) {
              //       this.formData['num_2'] = productCost // 批次成本
              //     }
              //   })
              // } else {
              //   this.formData['num_8'] = undefined
              //   this.formData['num_9'] = undefined
              //   // this.formData['num_12'] = undefined
              //   // this.formData['num_2'] = undefined
              //   // 清空批次
              //   this.clearOutstockBatch()
              // }
              if (!val) {
                this.formData['num_8'] = undefined
                this.formData['num_9'] = undefined
                this.clearOutstockBatch()
              }
            },
            false
          )
        }
      }
    },
    /**
     * 控制子表单中的产品不可编辑
     * 依赖上游单据
     */
    init_productNoEdit_outStock() {
      if (this.fieldInfo.saasAttr === 'products') {
        this.addWatch(`baseFormData.text_4`, (val) => {
          if (val && val.length) {
            // let type = this.baseFormData['text_3']
            // if (['1', '2'].includes(type['value'])) {
            //   this.noAdd = true
            // } else {
            //   this.noAdd = false
            // }
            this.noAdd = true
          } else {
            this.noAdd = false
          }
        })
      }
      // 选择产品不可以编辑
      // if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'product') {
      //   this.addWatch(`baseFormData.text_4`, (val) => {
      //     if (val && val.length) {
      //       // let type = this.baseFormData['text_3']
      //       // if (['1', '2'].includes(type['value'])) {
      //       //   this.fieldInfo.noEditable = 1
      //       // } else {
      //       //   this.fieldInfo.noEditable = 0
      //       // }
      //       this.fieldInfo.noEditable = 1
      //     } else {
      //       this.fieldInfo.noEditable = 0
      //     }
      //   })
      // }
      // if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'product') {
      //   this.addWatch(`baseFormData.text_4`, (val) => {
      //     if (val && val.length) {
      //       this.fieldInfo.noEditable = 1
      //     } else {
      //       this.fieldInfo.noEditable = 0
      //     }
      //   })
      // }
    },
    /** 监听多单位处理价格
     * 出库需要处理入库单价cost字段，销售出库还需要处理售价
     * 上游单据todo
     * 其余业务拷贝
     */
    init_multiUnit_outStock() {
      if (this.fieldInfo.parentAttr === 'array_1' && this.fieldInfo.saasAttr === 'unit') {
        this.addWatch(
          'formData.text_8',
          (val, oldVal) => {
            if (
              val &&
              oldVal &&
              JSON.stringify(val) !== JSON.stringify(oldVal) &&
              this.formData['enableMultiUnit']
            ) {
              // 单位比
              const itemsUnit = this.formData['unitRate'] || []
              // 销售出库需要算售价
              if ([businessEnum.CONTRACT_OUTSTOCK].includes(this.businessType)) {
                // 单价
                this.formData['num_1'] = getUnitPrice(val, this.formData['price'])
                // 售价
                const salePrice =
                  this.formData.sellingPriceMap['text_8'].value === val.value
                    ? this.formData.sellingPriceMap['num_6']
                    : getUnitPrice(val, itemsUnit, oldVal, this.formData['num_6'])
                this.formData['num_6'] = salePrice
                // 销售出库单计算折扣比例
                this.formData['num_4'] = xbb.divide(salePrice, this.formData['num_1'], 4) * 100
              }
              // 成本cost
              if (this.formData['num_2']) {
                const type = parseInt(
                  (this.baseFormData['text_3'] && this.baseFormData['text_3'].value) ||
                    this.getRefType(this.businessType)
                )
                /*
                 * 调拨出库单 OTHER_OUTSTOCK
                 * 采购退货出库单 RETURNED_PURCHASE_OUTSTOCK
                 * 取业务单位的值
                 */
                const flag =
                  (businessEnum.OTHER_OUTSTOCK === this.businessType && type === 4) ||
                  [businessEnum.RETURNED_PURCHASE_OUTSTOCK].includes(this.businessType)
                if (
                  (this.formData.sellingPriceMap['text_8'].value === val.value && flag) ||
                  (this.formData['unitRate'] &&
                    this.formData['unitRate'].find((item) => item.isBase === 1).value ===
                      val.value &&
                    !flag)
                ) {
                  this.formData['num_2'] = this.formData.sellingPriceMap['num_2']
                } else {
                  this.formData['num_2'] = getUnitPrice(
                    val,
                    itemsUnit,
                    oldVal,
                    this.formData['num_2']
                  )
                }
              }
            }
          },
          false
        )
      }
    },
    /**
     * 监听经销商字段、仓库字段
     * 切换经销商、仓库后清空对应数据
     */
    init_distributor_outStock() {
      if (this.fieldInfo.saasAttr === 'linkDistributor') {
        this.addWatch(
          'formData.text_66',
          (val, oldVal) => {
            if (val && val.length && JSON.stringify(val) !== JSON.stringify(oldVal)) {
              this.formData['address_1'] = void 0
              this.formData['text_5'] = void 0
              this.formData['text_9'] = void 0
            }
          },
          false
        )
      } else if (this.fieldInfo.saasAttr === 'warehouseId') {
        this.addWatch(
          'formData.text_2',
          (val, oldVal) => {
            if (val && val.length && JSON.stringify(val) !== JSON.stringify(oldVal)) {
              this.formData['address_2'] = void 0
              this.formData['text_69'] = void 0
              this.formData['text_11'] = void 0
            }
          },
          false
        )
      }
    },
    // 监听收货人、寄件人
    init_selectAddress_outStock() {
      if (this.fieldInfo.saasAttr === 'linkPartnerReceiver') {
        // 监听收货人字段
        this.addWatch(
          'formData.text_5',
          (val, oldVal) => {
            if (val && val.length) {
              this.formData['address_1'] = this.formData['text_5'][0]['address_1']
              this.formData['text_9'] = this.formData['text_5'][0]['text_2']
            } else {
              this.formData['address_1'] = void 0
              this.formData['text_9'] = void 0
            }
          },
          false
        )
      } else if (this.fieldInfo.saasAttr === 'sendName') {
        // 寄件人
        this.addWatch(
          'formData.text_69',
          (val, oldVal) => {
            if (val && val.length) {
              this.formData['address_2'] = this.formData['text_69'][0]['address_1']
              this.formData['text_11'] = this.formData['text_69'][0]['text_2']
            } else {
              this.formData['address_2'] = void 0
              this.formData['text_11'] = void 0
            }
          },
          false
        )
      }
    }
  }
}
