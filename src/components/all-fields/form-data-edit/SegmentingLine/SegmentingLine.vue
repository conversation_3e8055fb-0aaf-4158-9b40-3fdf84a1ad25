<template>
  <div class="segmenting-line" :class="[lineClass]" :style="lineStyle">
    <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
      {{ fieldInfo.unableEditMemo }}
    </div>
  </div>
</template>

<script>
import commonMixinChild from './../all-mixin-child/common'

export default {
  mixins: [commonMixinChild],
  props: ['isSee'],
  data() {
    return {
      lineStyle: {},
      lineClass: ''
    }
  },
  computed: {
    lineType() {
      return this.fieldInfo.lineType
    }
  },
  watch: {
    lineType: {
      immediate: true,
      handler(val) {
        switch (val) {
          case 'nothing':
            this.lineClass = 'line-type--nothing'
            // this.lineStyle = {
            //   'border-bottom': 'none'
            // }
            break
          case 'dashed':
            this.lineClass = 'line-type--dashed'
            // this.lineStyle = {
            //   borderBottom: '1px dashed block'
            // }
            break
          case 'thin':
            this.lineClass = 'line-type--thin'
            // this.lineStyle = {
            //   'border-bottom': '1px solid block'
            // }
            break
          case 'thick':
            this.lineClass = 'line-type--thinck'
            // this.lineStyle = {
            //   'border-bottom': '2px solid block'
            // }
            break

          default:
            break
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.segmenting-line-see {
  width: 100%;
}
.segmenting-line {
  //position: absolute;
  //top: 0px;
  width: 100%;
  height: 0px !important;
  //margin-left: -80px;
  // border-bottom: 1px solid $text-plain;
}
.line-type--nothing {
}
.line-type--dashed {
  border-bottom: 1px dashed $neutral-color-3;
}
.line-type--thin {
  border-bottom: 1px solid $neutral-color-3;
}
.line-type--thinck {
  border-bottom: 2px solid $neutral-color-3;
}
</style>
