<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 公农历生日组件（内层实体）
   formData： 放保存数据
   fieldInfo：当前字段信息
   readonly：是否只读（在表单设置页时候为true）
 }
 -->
<template>
  <div class="birthday">
    <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
      {{ fieldInfo.unableEditMemo }}
    </div>
    <template v-else-if="!isSee">
      <el-col class="birthday-col">
        <!-- <el-form-item> -->
        <el-select
          v-model="year"
          class="inline-input"
          :disabled="readonly"
          :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.years') })"
          @change="dateChange"
        >
          <el-option
            v-for="y in yearList"
            :key="y"
            :label="y + (localLang === 'en_US' ? '' : $t('unit.year'))"
            :value="y"
          >
          </el-option>
        </el-select>
        <!-- </el-form-item> -->
      </el-col>
      <el-col class="birthday-col">
        <!-- <el-form-item> -->
        <el-select
          v-model="month"
          class="inline-input"
          :disabled="readonly"
          :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.month') })"
          @change="dateChange"
        >
          <el-option
            v-for="m in 12"
            :key="m"
            :label="m + (localLang === 'en_US' ? '' : $t('unit.month'))"
            :value="m"
          >
          </el-option>
        </el-select>
        <!-- </el-form-item> -->
      </el-col>
      <el-col class="birthday-col">
        <!-- <el-form-item> -->
        <!-- 公历 -->
        <el-select
          v-if="isLunar"
          v-model="date"
          class="inline-input"
          :disabled="readonly"
          :placeholder="$t('placeholder.choosePls', { attr: $t('label.date') })"
          @change="dateChange"
        >
          <el-option
            v-for="d in lunarDays"
            :key="d"
            :label="d + (localLang === 'en_US' ? '' : $t('unit.days'))"
            :value="d"
          >
          </el-option>
        </el-select>
        <!-- 农历 -->
        <el-select
          v-else
          v-model="date"
          class="inline-input"
          :disabled="readonly"
          :placeholder="$t('placeholder.choosePls', { attr: $t('label.date') })"
          @change="dateChange"
        >
          <el-option
            v-for="d in days"
            :key="d"
            :label="d + (localLang === 'en_US' ? '' : $t('unit.days'))"
            :value="d"
          >
          </el-option>
        </el-select>
        <!-- </el-form-item> -->
      </el-col>

      <el-col class="birthday-col">
        <el-form-item>
          <!-- 开关 -->
          <el-radio-group v-model="isLunar" size="small" style="display: flex" @change="dateChange">
            <el-radio-button :disabled="readonly" :label="0">{{
              $t('formDataEdit.gregorianCalendar')
            }}</el-radio-button>
            <el-radio-button :disabled="readonly" :label="1">{{
              $t('formDataEdit.lunarCalendar')
            }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </template>
    <p v-else>{{ birthdayValue }}</p>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import calendar from './calendar'
import xbb from '@xbb/xbb-utils'

const yearList = []
const currentYear = new Date().getFullYear()
for (let i = 1910; i <= currentYear; i++) {
  yearList.push(i)
}

export default {
  props: {
    // 判断在详情页展示
    isSee: {
      type: Boolean,
      default: () => false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    fieldInfo: {
      type: Object,
      default: () => ({})
    },
    readonly: {
      // 字段是否只读
      type: Boolean,
      default: () => false
    }
  },

  data() {
    return {
      birthdayValue: '',
      yearList: yearList,
      year: 1980,
      month: '',
      date: '',
      isLunar: 0 // 是否开启农历
    }
  },

  computed: {
    // 公历每月天数
    days() {
      return calendar.days(this.year, this.month)
    },

    // 农历每月天数
    lunarDays() {
      return calendar.lunarDays(this.year, this.month)
    },

    // 是否开启验证
    isRequired() {
      return this.item.required
    },

    // 当前语种
    localLang() {
      return utils.LS.get('VUE_LANG')
    }
  },

  watch: {
    formData: {
      handler() {
        const value = this.formData[this.fieldInfo.attr]
        // 外链客户电话
        if (xbb.isOuterLinkEnv() && value && !value.value) {
          this.formData[this.fieldInfo.attr].value = this.formData[this.fieldInfo.attr].birthday
        }
        this.$nextTick(() => {
          if (this.formData[this.fieldInfo.attr] && this.formData[this.fieldInfo.attr].birthday) {
            this.isLunar = this.formData[this.fieldInfo.attr].birthdayFlag
            const dataArr = this.formData[this.fieldInfo.attr].birthday.split('-')
            this.year = Number(dataArr[0])
            this.month = Number(dataArr[1])
            this.date = Number(dataArr[2])
          }
        })
        // 详情页显示的值
        if (value && value.value) {
          this.birthdayValue = value.value
        }
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {
    const value = this.formData[this.fieldInfo.attr] // 获得编辑时的初始值

    // 确定值不为空
    if (value && value.birthday) {
      // 农历模式
      // if (value.birthdayFlag) {
      //   this.year = Number(value.lunarBirthday.slice(0, 4))
      //   this.month = Number(value.lunarBirthday.slice(5, 7))
      //   this.date = Number(value.lunarBirthday.slice(-2))
      // } else {
      //   // 公历模式
      //   let d = new Date(value.birthday * 1000)
      //   this.year = d.getFullYear()
      //   this.month = d.getMonth() + 1
      //   this.date = d.getDate()
      // }
      this.year = Number(value.birthday.slice(0, 4))
      this.month = Number(value.birthday.slice(5, 7))
      this.date = Number(value.birthday.slice(-2))
      this.isLunar = value.birthdayFlag
    }
  },

  methods: {
    // 日期变化
    dateChange() {
      // 当年月日均有值时，才继续向下执行

      if (this.year && this.month && this.date) {
        const data = {
          birthdayFlag: this.isLunar
        }
        let formatDate = ''
        const month = `0${this.month}`.slice(-2)
        const date = `0${this.date}`.slice(-2)
        formatDate = `${this.year}-${month}-${date}`

        // 公历需格式化为int值
        // if (!this.isLunar) {
        //   let d = new Date(this.year, month - 1, date)
        //   formatDate = d.getTime() / 1000
        //   data.birthday = formatDate
        // } else {
        //   data.lunarBirthday = formatDate
        // }
        data.birthday = formatDate

        this.$set(this.formData, this.fieldInfo.attr, data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.birthday {
  // width: 75%;
  .birthday-col {
    max-width: 130px;
    margin-bottom: 5px;
  }
  .inline-input {
    display: inline-block;
    margin: 0 12px 0 0;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
