<!--
 * @Description: 特殊字段显示“无”
 -->
<template>
  <div v-if="!!visible && fieldInfo.fieldMapHidden !== 1" class="form-item">
    <!-- label 根据当前模式决定是否显示 -->
    <FormItemLayout
      v-if="!!visible"
      class="form-item-base"
      :field-info="fieldInfo"
      :field-position="fieldPosition"
      :label="showLabel ? label : ''"
      :memo="memo"
      :no-need-mode="true"
      :prop="prop"
      :rules="roules"
    >
      <div>{{ description }}</div>
      <!-- 描述信息 -->
      <!-- <el-tooltip v-if="memo && fieldInfo.memo" class="item field-memo-tooltip" effect="dark" :content="fieldInfo.memo" placement="top">
        <em class="el-icon-question"></em>
      </el-tooltip> -->
    </FormItemLayout>
  </div>
</template>

<script>
import commonMixin from './../../all-mixin-new/common'
import defaultValMixin from './../../all-mixin-new/default-val'
import roulesMixin from './../../all-mixin-new/roules'
import { mapGetters } from 'vuex'
import specialJudge from '@/utils/form-data-special-judge'

export default {
  mixins: [commonMixin, defaultValMixin, roulesMixin],
  computed: {
    // 字段key
    attr() {
      return this.fieldInfo.attr
    },
    visible() {
      return this.fieldInfo.visible
    },
    // 注入当前表单
    formInfo() {
      return this.$store.state.form.formInfo
    },
    // 计算当前表单的属性
    formInfoAttr() {
      return this.formInfo && this.formInfo.formAttr
    },
    // 描述
    description() {
      const text = this.$t('unit.no')
      if (specialJudge.isBOM(this.formQuery.businessType)) {
        return this.$t('formDataEdit.bomChosenProduct')
      }
      if (specialJudge.isProductionOrder(this.formQuery.businessType)) {
        return this.$t('formDataEdit.productChosenBom')
      }
      if (specialJudge.isTransfer(this.formQuery.businessType)) {
        return this.$t('formDataEdit.warehouseTransfer')
      }
      if (specialJudge.isInventory(this.formQuery.businessType)) {
        return this.$t('formDataEdit.warehouseInventory')
      }
      if (specialJudge.isPurchase(this.formQuery.businessType)) {
        return this.$t('formDataEdit.supplierChoose')
      }
      if (specialJudge.isReturnPurchase(this.formQuery.businessType)) {
        return this.$t('formDataEdit.contractChoose')
      }
      return text
    },
    ...mapGetters(['formQuery'])
  },
  mounted() {
    setTimeout(() => {
      if (
        this.fieldInfo.fieldType === 20001 &&
        this.formData[this.prop] &&
        this.formData[this.prop].length === 0
      ) {
        this.$set(this.fieldInfo.defaultAttr, 'useLinkValue', 0)
      }
    }, 100)
  }
}
</script>
