<!--
 * @Description: 跟进业务
 -->
<template>
  <div class="field-item follow-up-business form-data__field-item">
    <div v-if="fieldInfo.unableEditMemo && !isSee" class="field-item__text">
      {{ fieldInfo.unableEditMemo }}
    </div>
    <template v-else-if="isSee">
      <div>
        <el-tag v-if="businessName" class="xbb-tag" @click.native="tagClick">{{
          businessName
        }}</el-tag>
      </div>
    </template>
    <template v-else-if="!isNull">
      <el-form-item style="width: 140px; margin-bottom: 0">
        <el-select
          v-model="selectVal"
          class="field-item__select"
          :disabled="readonly"
          :placeholder="$t('placeholder.choosePls', { attr: '' })"
          @change="selectChange"
        >
          <el-option
            v-for="item in selectItems"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <FieldInputBlock
        class="field-item__detail"
        :readonly="readonly"
        :style-fix="true"
        :title="title"
        @beforeDialogShow="beforeDialogShow"
        @submit="submit"
      >
        <!-- input插槽 -->
        <template slot="input">
          <template>
            <el-tag
              v-if="value && value.id && value.name"
              :key="value.id"
              class="el-select__tag"
              closable
              disable-transitions
              @close="valueDelete"
            >
              {{ value.name || value.id }}
            </el-tag>
          </template>
        </template>
        <!-- 弹窗头部value回显 -->
        <div slot="header" class="slot__header">
          <el-scrollbar class="slot__header--scrollbar">
            <el-tag
              v-for="(item, index) in selectRows"
              :key="item.id"
              class="slot__header--tag"
              closable
              disable-transitions
              @close="tagClose(index, item)"
            >
              {{ item.name || item.id }}
            </el-tag>
          </el-scrollbar>
        </div>
        <!-- 弹窗身体 - 关联列表 -->
        <LinkTableTable
          :app-id-params="appIdParams"
          :business-name.sync="title"
          :business-params="specialBusinessParams"
          :distributor-mark="fieldInfo.distributorMark"
          :field-info="fieldInfo"
          :link-business-type="selectVal"
          :multiple="false"
          :select-rows.sync="selectRows"
        />
      </FieldInputBlock>
    </template>
    <div v-else>{{ $t('unit.no') }}</div>
  </div>
</template>

<script>
import commonMixinChild from './../../all-mixin-child/common'
import LinkTableTable from '../link-table/components/link-table-table'
import FormRelyMixin from '../../all-mixin-child/form-rely'
import specialJudge from '@/utils/form-data-special-judge'
import emitter from 'element-ui/src/mixins/emitter'

export default {
  components: {
    LinkTableTable
  },
  mixins: [commonMixinChild, FormRelyMixin, emitter],
  model: {
    prop: 'value',
    event: 'modelChange'
  },
  props: {
    fieldList: {
      type: Array,
      default: () => []
    },
    isSee: {},
    formData: {
      // 当前绑定的表达对象
    },
    mode: {
      type: String,
      default: 'base'
    },
    value: {
      default: ''
    },
    formQuery: {
      type: Object
    }
  },
  data() {
    return {
      selectVal: '',
      title: '',
      selectRows: [],
      isNull: false
    }
  },
  computed: {
    businessName() {
      let res
      if (this.value && this.value.id) {
        res = this.value['name'] || this.value.id
      }
      return res
    },
    // selectItemText () {
    //   let businessType = this.value && this.value['businessType']
    //   return this.itemsMap[businessType] ? this.itemsMap[businessType] + ':' : ''
    // },
    itemsMap() {
      const res = {}
      const arr = this.selectItems || []
      arr.forEach((item) => {
        res[item['value']] = item['text']
      })
      return res
    },
    // 计算是否可以编辑(只读属性在内层实现)
    editable() {
      return this.fieldInfo.editable
    },
    selectItems() {
      return this.fieldInfo.items
    },
    /**
     * TODO：
     * 开发过程中过滤脏数据用，需限定appid,后续删除
     */
    appIdParams() {
      return {
        appId: this.appId,
        formId: this.formId
      }
    },
    specialBusinessParams() {
      const { businessType } = this
      const {
        saasParticularAttributePoJo: { relyBusiness, relyMode, relyType }
      } = this.fieldInfo
      if (
        relyType === 'relied' &&
        relyMode === 'customer' &&
        relyBusiness &&
        (specialJudge.isJXCCommunicate(businessType) || specialJudge.isCRMCommunicate(businessType))
      ) {
        return {
          parentBusinessType: relyBusiness,
          parentId: this.parentId
        }
      }
      return {}
    }
  },
  watch: {
    value(val) {
      // 临时处理下格式
      if (JSON.stringify(val) === '[]') {
        this.$emit('modelChange', {})
      }
    }
  },

  mounted() {
    const { value, selectItems } = this
    selectItems.forEach((item) => {
      this.$set(item, 'value', Number(item.value))
    })
    this.selectVal = selectItems[0].value
    // 编辑时,数据存在则回显
    if (value) {
      if (value.businessType) {
        this.selectVal = value.businessType
      }
      if (value.id && value.name) {
        this.selectRows = [
          {
            id: value.id,
            name: value.name
          }
        ]
      }
    }
  },
  methods: {
    tagClick() {
      this.$emit('linkItemClick', this.value)
    },
    // 清除当前选中的标签
    valueDelete() {
      this.selectRows = []
      this.$emit('modelChange', {})
    },

    // select切换时,清除之前的选项
    selectChange() {
      this.selectRows = []
      this.$emit('modelChange', {})
    },

    // 移除当前项
    tagClose(i, d) {
      this.selectRows.splice(i, 1)
    },

    // 全部清除
    tagRemove() {
      this.selectRows = []
    },

    // 弹窗确认时触发
    submit() {
      // 此处业务返回为一个list,但list中有且只有1项
      const { selectRows } = this
      let data
      if (selectRows && selectRows[0]) {
        data = {
          businessType: this.selectVal,
          id: this.selectRows[0].id,
          name: this.selectRows[0].name
        }
      }
      this.$emit('modelChange', data)
      this.dispatch('ElFormItem', 'el.form.change', data)
    },

    clearFieldData() {
      this.selectRows = []
      this.$emit('modelChange', [])
    },

    // 弹窗弹出前触发
    beforeDialogShow() {
      // 弹窗显示前注意回显
      const { value } = this
      if (value.id && value.name) {
        this.selectRows = [
          {
            id: value.id,
            name: value.name
          }
        ]
      } else {
        this.selectRows = []
      }
    }
  }
}
</script>

<style lang="scss">
.follow-up-business {
  display: flex;
  &__select {
    width: 120px;
    margin-right: 14px;
  }
  &__detail {
    flex: 1;
  }
}
.el-select {
  &__tags {
    width: 100%;
    line-height: normal;
    white-space: normal;
  }
  &__tag {
    margin: 2px 0 2px 6px;
  }
}
.slot__header {
  height: 100%;
  &--scrollbar {
    height: 100%;
  }
}
.xbb-tag {
  cursor: pointer;
}
.field-item__detail {
  width: 100%;
  margin-left: 8px;
  .styleFix {
    height: 100%;
  }
}
</style>
