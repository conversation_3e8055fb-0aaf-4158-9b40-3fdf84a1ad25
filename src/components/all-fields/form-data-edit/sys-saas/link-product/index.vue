<!--
 * @Description: 产品选择结果渲染 (外层)
 -->
<template>
  <div class="index">
    <FormItemLayout
      v-if="!!visible"
      class="form-item-base link-product-no-label"
      :field-position="fieldPosition"
      :label="showLabel ? label : ''"
      :prop="prop"
      :rules="roules"
    >
      <select-product
        :big-editable="fieldInfo.editable"
        :bill-id="BillId"
        :business-type="businessType"
        :explains="productExplains"
        :field-attr="fieldInfo.saasAttr"
        :form="form"
        :form-inout-stock-type="formInoutStockType"
        :instockproduct="getzpProduct(fieldInfo.saasAttr)"
        :is-design="isDesign"
        :item="fieldInfo"
        @getProductList="getProductList"
      ></select-product>
      <link-product
        :attr-value="attrValue"
        :big-editable="fieldInfo.editable"
        :business-type="businessType"
        :explains="productExplains"
        :field-info="fieldInfo"
        :field-list="fieldList"
        :form="productOther"
        :form-data="formData"
        :form-inout-stock-type="formInoutStockType"
        :instockproduct="getzpProduct(fieldInfo.saasAttr)"
        :saas-attr="fieldInfo.saasAttr"
        @parentDecimal="parentDecimal"
        @setMateriel="setMateriel"
      ></link-product>
    </FormItemLayout>
  </div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import linkProduct from './link-product' // 产品展示组件
import selectProduct from './select-product' // 产品选择组件
import commonBusinessType from '@/constants/common/business-type'

import commonMixin from './../../all-mixin-new/common'
import defaultValMixin from './../../all-mixin-new/default-val'
import roulesMixin from './../../all-mixin-new/roules'
import setP from '@/utils/temp-vue' // eimt产品方法
import specialJudge from '@/utils/form-data-special-judge'
import { selfLoopEach } from '@/utils/util-loop'

export default {
  components: {
    LinkProduct: linkProduct,
    SelectProduct: selectProduct
  },

  mixins: [commonMixin, defaultValMixin, roulesMixin],
  inject: {
    parentForm: { default: null }
  },

  data() {
    return {
      form: {}, // 临时
      // 产品选择后的其他要传给后台的信息（表格底部）
      productOther: {
        // 合同
        surchargeArr: [], // 自定义费用
        // 采购合同
        purchaseOther: {
          discount: 100, // 采购合同总折扣
          otherCharge: 0 // 其他费用
        },
        // 销售出库单
        outstockOther: {
          outNumTotal: 0, // 出库数量总计
          outProductCostTotal: 0, // 出库成本总计
          priceTotal: 0 // 产品售价合计
        }
      },
      attrValue: {
        productList: []
      },
      pageBusinessType: commonBusinessType,
      productExplains: [],
      // 进销存新建编辑产品
      instockproduct: [], // 关联的产品无论已选还是选中
      outProduct: [], // 装配出库产品
      inProduct: [], // 装配入库产品
      finishedProduct: [], // 成品产品
      materiel: [], // 物料产品
      BillId: [], // 关联单据的产品ID(不变)
      // saleExplains: [], // 销售出库产品解释
      stockNum: 0, // 库存数量
      cost: 0, // 成本
      wareCount: 0, // 出库仓库watch次数
      formInoutStockType: '' // 出入库类型
    }
  },

  computed: {
    productInfo() {
      return this.formData[this.fieldInfo.attr]
    },
    // productInfoNew () {
    //   return JSON.parse(JSON.stringify(this.productInfo))
    // },
    ...mapGetters([
      'formMode',
      'saasObj', // saas业务特殊属性
      // 'formQuery',
      'inWarehouseObj', // 入库仓库信息（生产单、装配出入库单涉及）
      'outWarehouseObj', // 出库仓库信息（生产单、装配出入库单涉及）
      'warehouseObj', //  出入库单、调拨单、盘点单saasAttr为warehouseId的仓库
      'businessId' // 关联ID
    ])
  },

  watch: {
    // 监听出库仓库字段
    outWarehouseObj() {
      // 编辑第一次不走
      if (!this.wareCount && this.formMode === 'edit') {
        this.wareCount++
        return
      }
      // 生产单物料出库、装配出库、调拨单
      // if ((this.businessType === this.pageBusinessType.PRODUCTIONORDER && this.fieldInfo.saasAttr === 'materiel') || (this.businessType === this.pageBusinessType.ASSEMBLE && this.fieldInfo.saasAttr === 'outProducts')) {
      //   this.getchangeHouse('outWarehouseId')
      // }
      this.getchangeHouse('outWarehouseId')
    },
    // 监听入库仓库字段
    inWarehouseObj() {
      // 装配入库
      if (
        this.businessType === this.pageBusinessType.ASSEMBLE &&
        this.fieldInfo.saasAttr === 'inProducts'
      ) {
        this.getchangeHouse('intoWarehouseId')
      }
    },
    // 监听关联单据是否选中
    businessId: {
      handler(val, oldVal) {
        if (
          ([
            this.pageBusinessType.INSTOCK,
            this.pageBusinessType.OUTSTOCK,
            this.pageBusinessType.RETURNED_PURCHASE,
            this.pageBusinessType.REFUND
          ].indexOf(this.businessType) > -1 &&
            !val) ||
          ([this.pageBusinessType.PURCHASE].indexOf(this.businessType) > -1 &&
            this.saasObj &&
            this.saasObj.purchaseProductBelongSupplier)
        ) {
          this.instockproduct = []
          this.getzpProduct(this.fieldInfo.saasAttr)
        }
      }
    },
    // 判断服务端给的数据格式是否满足渲染，满足再执行方法
    productInfo: {
      handler(val, oldVal) {
        if (
          this.productInfo &&
          this.productInfo.productList &&
          this.productInfo.productList[0] &&
          this.productInfo.productList[0].result
        ) {
          // 销售出库解释存VUEX
          if (this.businessType === this.pageBusinessType.OUTSTOCK) {
            let outType = ''
            this.fieldList.forEach((it) => {
              if (it.saasAttr === 'type') {
                outType = this.formData[it.attr].value
              }
            })
            if (outType === '2') {
              this.setSaleExplains(this.productInfo.explains)
              // 销售出库草稿产品解释
              this.setDraftSaleExplains(this.productInfo.explains)
            }
          }
          // 存储单据关联的产品ID
          this.BillId = this.productInfo.productList.map((item) => item.id)
          this.getProductData()
        } else if (
          [this.pageBusinessType.REFUND].indexOf(this.businessType) > -1 &&
          this.productInfo &&
          !this.productInfo.productList
        ) {
          // 退货退款
          this.instockproduct = []
        }
      },
      deep: true,
      immediate: true
    },
    finishedProduct() {
      // 当成品产品发生改变时，更改存在vuex中的值
      const {
        fieldInfo: { saasParticularAttributePoJo }
      } = this
      if (saasParticularAttributePoJo) {
        const { relyMode, relyType } = saasParticularAttributePoJo
        if (relyType === 'base' && relyMode === 'product') {
          if (this.finishedProduct.length > 0) {
            this.setProductChosen(this.finishedProduct[0].id)
          } else {
            this.setProductChosen(null)
          }
        }
      }
    },
    // 采购额合同产品其他信息监听
    'productOther.purchaseOther': {
      handler(value) {
        if (this.businessType === this.pageBusinessType.PURCHASE) {
          setP.$emit('productOther', this.productOther.purchaseOther)
        }
      },
      deep: true
    },
    // 监听仓库
    warehouseObj: {
      handler() {
        // 盘点单仓库清空清空产品
        if ([this.pageBusinessType.INVENTORY].indexOf(this.businessType) > -1) {
          this.instockproduct = []
        }
        // 出库单入库单改变产品仓库
        if (
          [this.pageBusinessType.INSTOCK, this.pageBusinessType.OUTSTOCK].indexOf(
            this.businessType
          ) > -1
        ) {
          this.getchangeHouse()
        }
      },
      deep: true
    },
    // 监听出入库类型
    formInoutStockType(val, oldVal) {
      if (!oldVal && val) {
        return
      }
      this.instockproduct = []
    },
    'formData.text_66': {
      handler(value) {
        // 新建合同时监听报价单字段，置空产品
        if (
          this.businessType === this.pageBusinessType.CONTRACT &&
          (value === undefined || value.length === 0)
        ) {
          this.instockproduct = []
          this.$set(this.formData, 'array_4', [])
        }
      }
    }
  },
  created() {
    this.getProudctExplains()
    this.getSurchargeList()
    if (specialJudge.isInOutStock(this.businessType)) {
      let typeAttr = this.fieldList.filter((field) => {
        return field.saasAttr === 'type'
      })
      if (typeAttr.length > 0) {
        typeAttr = typeAttr[0].attr
        this.addWatch(
          `formData.${typeAttr}`,
          (val, oldVal) => {
            if (Object.prototype.toString.call(val) === '[object Object]') {
              // 对象类型，则更新出入库类型值
              this.formInoutStockType = val.value
            }
          },
          true
        )
      }
    }
  },
  mounted() {
    // this.proformData(this.finishedProduct)
    // 使用src\components\form-data-edit\form-data-form.vue提供的方法
    this.parentForm && (this.parentForm.productListFn = this.proformData.bind(this))
  },

  methods: {
    // 获取编辑时候产品信息
    getProductData() {
      this.fieldList.forEach((item) => {
        if (
          ['products', 'inProducts', 'outProducts', 'finishedProduct', 'materiel'].includes(
            item.saasAttr
          )
        ) {
          let subArryPro = []
          if (this.businessType === this.pageBusinessType.PRODUCTIONORDER) {
            // 改变成品的时候需要影响物料
            if (this.formData[item.attr]) {
              subArryPro = this.initProduct(this.formData[item.attr].productList)
            }
          } else {
            subArryPro = JSON.parse(
              JSON.stringify(this.initProduct(this.formData[item.attr].productList))
            )
          }
          if (item.saasAttr === 'finishedProduct') {
            this.finishedProduct = subArryPro
            setP.$emit('finishedProduct', subArryPro)
          } else if (item.saasAttr === 'materiel') {
            this.materiel = subArryPro
            setP.$emit('materiel', subArryPro)
          } else if (item.saasAttr === 'outProducts') {
            this.outProduct = subArryPro
            setP.$emit('outProduct', subArryPro)
          } else if (item.saasAttr === 'inProducts') {
            this.inProduct = subArryPro
            setP.$emit('inProduct', subArryPro)
          } else {
            this.instockproduct = subArryPro
            // 合同、采购合同、出库单的产品其他信息
            this.getOthersInfo(item)
            // 出入库各别类型仓库影响库存和成本
            if (
              [this.pageBusinessType.INSTOCK, this.pageBusinessType.OUTSTOCK].includes(
                this.businessType
              ) &&
              this.warehouseObj &&
              this.formMode === 'add'
            ) {
              // 采购退货出库、 采购入库、 销售出库、采购合同订单入库、工单出库、其他出入库
              if (['1', '2', '5', '7'].includes(this.formInoutStockType)) {
                const fn = (obj, itempro) => {
                  // 仓库
                  if (itempro.attr === 'warehouse') {
                    // this.$set(item, 'attrValue', this.warehouseObj)
                    this.getStockNum(itempro) // 更新this.stockNum
                    obj.result.forEach((items) => {
                      if (items.attr === 'stock') {
                        items.attrValue = this.stockNum
                      }
                      // 采购退货出库关联单据和关联单据入库不用考虑仓库改变成本
                      if (
                        items.attr === 'productCost' &&
                        ((this.businessType === this.pageBusinessType.INSTOCK &&
                          this.formInoutStockType === '5') ||
                          (this.businessType === this.pageBusinessType.OUTSTOCK &&
                            this.formInoutStockType !== '1'))
                      ) {
                        items.attrValue = this.cost
                      }
                    })
                  }
                }
                selfLoopEach(this.instockproduct, ['result'], fn)
              }
              setP.$emit('instockproduct', this.instockproduct)
            } else {
              setP.$emit('instockproduct', subArryPro)
            }
          }
        }
      })
    },
    // 初始获得关联单据产品其他信息
    getOthersInfo(item) {
      // 采购合同产品总折扣其他费用
      if (this.businessType === this.pageBusinessType.PURCHASE) {
        this.productOther.purchaseOther.discount = this.formData[item.attr].others.discount
        this.productOther.purchaseOther.otherCharge = this.formData[item.attr].others.otherCharge
        setP.$emit('productOther', this.productOther.purchaseOther)
      }
      // 合同订单
      if (this.businessType === this.pageBusinessType.CONTRACT) {
        this.productOther.surchargeArr =
          (this.formData[item.attr].others && this.formData[item.attr].others.surchargeList) || []
        setP.$emit('productOther', this.productOther.surchargeArr)
      }
      // 出库单数量售价成本汇总字段
      if (this.businessType === this.pageBusinessType.OUTSTOCK) {
        this.productOther.outstockOther.outNumTotal =
          (this.formData[item.attr].others && this.formData[item.attr].others.outNumTotal) || 0
        this.productOther.outstockOther.outProductCostTotal =
          (this.formData[item.attr].others &&
            this.formData[item.attr].others.outProductCostTotal) ||
          0
        this.productOther.outstockOther.priceTotal =
          (this.formData[item.attr].others && this.formData[item.attr].others.priceTotal) || 0
        setP.$emit('productOther', this.productOther.outstockOther)
      }
    },
    // 初始化产品
    initProduct(arr) {
      return arr.map((e) => {
        e.result.map((f) => {
          if (f.fieldType === 9) {
            f.attrValue = f.attrValue.split('|') || []
          }
          return f
        })
        return e
      })
    },
    // 获取产品解释
    getProudctExplains() {
      if (
        this.fieldInfo.saasParticularAttributePoJo &&
        this.fieldInfo.saasParticularAttributePoJo.productExplains &&
        this.fieldInfo.saasParticularAttributePoJo.productExplains.length
      ) {
        this.productExplains = this.fieldInfo.saasParticularAttributePoJo.productExplains
      }
    },
    // 获取合同其他费用
    getSurchargeList() {
      if (
        this.fieldInfo.saasParticularAttributePoJo &&
        this.fieldInfo.saasParticularAttributePoJo.surchargeList &&
        this.fieldInfo.saasParticularAttributePoJo.surchargeList.length
      ) {
        // 判断是否存在surchargeList
        const surchargeList = this.fieldInfo.saasParticularAttributePoJo.surchargeList
        for (let i = 0; i < surchargeList.length; i++) {
          surchargeList[i].amount = surchargeList[i].amount
            ? surchargeList[i].amount > 0
              ? surchargeList[i].amount
              : -surchargeList[i].amount
            : 0
        }
        this.productOther.surchargeArr = surchargeList
      }
    },
    // 区分装配出入库产品\成品\物料
    getzpProduct(attr) {
      if (attr === 'outProducts') {
        setP.$emit('outProduct', this.outProduct)
        return this.outProduct
      } else if (attr === 'inProducts') {
        setP.$emit('inProduct', this.inProduct)
        return this.inProduct
      } else if (attr === 'finishedProduct') {
        setP.$emit('finishedProduct', this.finishedProduct)
        return this.finishedProduct
      } else if (attr === 'materiel') {
        setP.$emit('materiel', this.materiel)
        return this.materiel
      } else {
        setP.$emit('instockproduct', this.instockproduct)
        return this.instockproduct
      }
    },
    // 获取选择产品弹框选中的产品
    getProductList(handleSelectPro, type) {
      // type有outProduct\inProduct
      const subArryPro = JSON.parse(JSON.stringify(handleSelectPro))
      if (type === 'outProducts') {
        this.outProduct = subArryPro
        setP.$emit('outProduct', subArryPro)
      } else if (type === 'inProducts') {
        this.inProduct = subArryPro
        setP.$emit('inProduct', subArryPro)
      } else if (type === 'finishedProduct') {
        this.finishedProduct = subArryPro
        setP.$emit('finishedProduct', subArryPro)
      } else if (type === 'materiel') {
        this.materiel = subArryPro
        setP.$emit('materiel', subArryPro)
      } else {
        this.instockproduct = subArryPro
        setP.$emit('instockproduct', subArryPro)
      }

      // 采购合同
      if (this.businessType === this.pageBusinessType.PURCHASE) {
        this.productOther.purchaseOther.discount = 100
        this.productOther.purchaseOther.otherCharge = 0
        setP.$emit('productOther', this.productOther.purchaseOther)
      }
    },
    // 生产单，成品生产数量计算物料领料数量
    setMateriel(productionObj) {
      this.materiel.forEach((item) => {
        let unitNum = 1
        let attritionRate = 0
        item.result.forEach((obj) => {
          if (obj.attr === 'unitNum') {
            unitNum = obj.attrValue // 标准用量
          }
          if (obj.attr === 'attritionRate') {
            attritionRate = obj.attrValue // 损耗率
          }
        })
        // 计算领料数量
        item.result.forEach((numObj) => {
          if (numObj.attr === 'num') {
            const realtionRate = 1 - attritionRate / 100
            if (realtionRate > 0) {
              numObj.attrValue = Number(
                ((unitNum / realtionRate) * productionObj.productionNum).toFixed(numObj.accuracy)
              )
            }
          }
        })
      })
    },
    // 仓库改变
    getchangeHouse(itemAttr) {
      // 入库出库
      if (
        [this.pageBusinessType.INSTOCK, this.pageBusinessType.OUTSTOCK].indexOf(this.businessType) >
        -1
      ) {
        // 调拨和盘点出入库、退料入库成品入库物料出库修改仓库后把关联单据和产品清空
        if (
          this.formInoutStockType === '3' ||
          this.formInoutStockType === '4' ||
          this.formInoutStockType === '8' ||
          (this.formInoutStockType === '7' && this.businessType === this.pageBusinessType.INSTOCK)
        ) {
          this.instockproduct = []
          // this.form.refId = ''
        } else {
          this.instockproduct.forEach((obj) => {
            obj.result.forEach((item) => {
              // 仓库
              if (item.attr === 'warehouse') {
                // this.$set(item, 'attrValue', this.warehouseObj)
                this.getStockNum(item) // 更新this.stockNum
                obj.result.forEach((items) => {
                  if (items.attr === 'stock') {
                    items.attrValue = this.stockNum
                  } else if (
                    items.attr === 'productCost' &&
                    ((this.businessType === this.pageBusinessType.INSTOCK &&
                      this.formInoutStockType === '5') ||
                      (this.businessType === this.pageBusinessType.OUTSTOCK &&
                        this.formInoutStockType !== '1'))
                  ) {
                    // 采购退货出库关联单据和关联单据入库不用考虑仓库改变成本
                    items.attrValue = this.cost
                  }
                })
              } else if (item.attr === 'batch') {
                // 清空批次
                item.attrValue = ''
              }
            })
          })
        }
      }
      // 装配单
      if (this.businessType === this.pageBusinessType.ASSEMBLE) {
        if (this.outProduct.length && itemAttr === 'outWarehouseId') {
          // 装配出库
          this.outProduct.forEach((obj) => {
            obj.result.forEach((item) => {
              if (item.attr === 'warehouse') {
                // this.$set(item, 'attrValue', this.outWarehouseObj)
                this.getStockNum(item, itemAttr) // 更新this.stockNum
                obj.result = this.doChangeObj(obj.result)
              } else if (item.attr === 'batch') {
                // 清空批次
                item.attrValue = ''
              }
            })
          })
        } else if (this.inProduct.length && itemAttr === 'intoWarehouseId') {
          // 装配入库
          this.inProduct.forEach((obj) => {
            obj.result.forEach((item) => {
              if (item.attr === 'warehouse') {
                // this.$set(item, 'attrValue', this.inWarehouseObj)
                this.getStockNum(item, itemAttr) // 更新this.stockNum
                obj.result = this.doChangeObj(obj.result)
              }
            })
          })
        }
      }
      // 调拨单出库/盘点单
      if (
        [this.pageBusinessType.TRANSFER, this.pageBusinessType.INVENTORY].indexOf(
          this.businessType
        ) > -1
      ) {
        this.instockproduct = []
      }
      // 生产单
      if (
        this.businessType === this.pageBusinessType.PRODUCTIONORDER &&
        itemAttr === 'outWarehouseId' &&
        this.materiel.length
      ) {
        this.materiel.forEach((obj) => {
          obj.result.forEach((item) => {
            if (item.attr === 'warehouse') {
              // this.$set(item, 'attrValue', this.outWarehouseObj)
              this.getStockNum(item, itemAttr) // 更新this.stockNum
              // 原代码这里没有处理attr为‘productCost’的情况，所以价格判断，true时为不判断attr为‘productCost’的情况
              obj.result = this.doChangeObj(obj.result, true)
            }
          })
        })
      }
    },
    // 封装仓库改变时内部变量变化
    doChangeObj(obj, flag = false) {
      obj.forEach((items) => {
        if (items.attr === 'stock') {
          items.attrValue = this.stockNum
        } else if (items.attr === 'productCost' && !flag) {
          items.attrValue = this.cost
        }
      })
      return obj
    },
    // 匹配仓库数量
    getStockNum(obj, arrtType) {
      // arrtType表示装配出库or装配入库
      if (obj.warehouseArray && obj.warehouseArray.length) {
        // 表单出库仓库字段值
        let outWareId = ''
        if (this.outWarehouseObj) {
          outWareId = this.outWarehouseObj.id
        }
        // 表单入库仓库字段值
        let inWareId = ''
        if (this.inWarehouseObj) {
          inWareId = this.inWarehouseObj.id
        }
        // 只有一个（入库或者出库）仓库字段的值
        let wareId = ''
        if (this.warehouseObj) {
          wareId = this.warehouseObj.id
        }
        const wareFlag = obj.warehouseArray.some((it) => {
          const flag1 = arrtType === 'outWarehouseId' && it.id === outWareId
          const flag2 = arrtType === 'intoWarehouseId' && it.id === inWareId
          const flag3 = !arrtType && it.id === wareId
          if (flag1 || flag2 || flag3) {
            this.stockNum = it.num
            this.cost = it.productCost
            return true
          }
          return false
        })
        // 匹配到仓库要做的一些事情
        if (wareFlag) {
          if (arrtType === 'outWarehouseId') {
            this.$set(obj, 'attrValue', this.outWarehouseObj)
          } else if (arrtType === 'intoWarehouseId') {
            this.$set(obj, 'attrValue', this.inWarehouseObj)
          } else if (!arrtType) {
            this.$set(obj, 'attrValue', this.warehouseObj)
          }
        } else {
          this.stockNum = 0
          this.cost = 0
          this.$set(obj, 'attrValue', '')
        }
      }
    },

    /* 机会产品合计改变预计金额estimateAmount\采购合同产品合计改变采购金额totalMoney\合同订单产品合计费用影响合同金额totalMoney
     * 退货退款产品改变退货金额
     */
    parentDecimal(obj) {
      if (
        [
          this.pageBusinessType.SALES_OPPORTUNITY,
          this.pageBusinessType.CONTRACT,
          this.pageBusinessType.PURCHASE,
          this.pageBusinessType.RETURNED_PURCHASE,
          this.pageBusinessType.REFUND,
          this.pageBusinessType.QUOTATION
        ].indexOf(this.businessType) > -1
      ) {
        this.fieldList.forEach((item) => {
          if (
            ['estimateAmount', 'productTotal', 'returnAmount'].indexOf(item.saasAttr) > -1 ||
            (['totalMoney'].indexOf(item.saasAttr) > -1 &&
              [this.pageBusinessType.RETURNED_PURCHASE].indexOf(this.businessType) > -1)
          ) {
            let accuracy
            if (!item.integerOnly) {
              accuracy = item.accuracy
            } else {
              accuracy = 0
            }
            const totalMoney = Number(obj.money.toFixed(accuracy))
            // 除了编辑页面第一次不赋值,当改变其他费用或者改变产品价格数量之类的需要赋值
            if (
              ((this.formMode === 'edit' || this.isFlow) && obj.count > 1) ||
              (this.formMode === 'edit' && obj.flagType === 'watch') ||
              this.formMode === 'add' ||
              this.formMode === 'newVersion'
            ) {
              this.$set(this.formData, item.attr, totalMoney)
            }
          }
        })
      }
    },

    // 产品格式化(后续我优化，只传ID和可编辑的或者有改变的值)
    proformData(arrPro) {
      const temp = JSON.parse(JSON.stringify(arrPro))
      const productList = []
      temp.forEach((obj) => {
        const proObj = {
          id: obj.id,
          parentId: obj.parentId,
          redundantData: {}
        }
        obj.result.forEach((item) => {
          if (
            [
              this.pageBusinessType.PURCHASE,
              this.pageBusinessType.RETURNED_PURCHASE,
              this.pageBusinessType.REFUND
            ].indexOf(this.businessType) > -1
          ) {
            // 采购合同\采购退货、退货退款
            if (item.attr === 'num') {
              proObj.num = item.attrValue
            }
            if (item.attr === 'productPrice') {
              proObj.productPrice = item.attrValue
            }
            if (item.attr === 'memo') {
              proObj.memo = item.attrValue
            }
          }
          if (
            [
              this.pageBusinessType.INSTOCK,
              this.pageBusinessType.OUTSTOCK,
              this.pageBusinessType.ASSEMBLE,
              this.pageBusinessType.TRANSFER,
              this.pageBusinessType.INVENTORY,
              this.pageBusinessType.WORK_ORDER
            ].indexOf(this.businessType) > -1
          ) {
            // 入库单
            if (item.editable === 1 && item.isRedundant === 0) {
              proObj[item.attr] = item.attrValue
            }
            // 自定义字段
            if (item.isRedundant === 1) {
              if (item.fieldType === 4) {
                // 时间
                proObj.redundantData[item.attr] = item.attrValue
              } else if (item.fieldType === 9) {
                // 多选
                if (item.attrValue instanceof Array) {
                  // 数组转成字符串
                  const newArr = item.attrValue.filter((item) => item)
                  proObj.redundantData[item.attr] = newArr.join('|')
                } else {
                  proObj.redundantData[item.attr] = item.attrValue
                }
              } else {
                // 其他
                proObj.redundantData[item.attr] = item.attrValue
              }
            }

            // 销售出库（售价、销售单价、单价、折扣）
            if (
              this.formInoutStockType === '2' &&
              !item.editable &&
              ['saleProductPrice', 'saleSubtotal', 'salePrice', 'saleDiscount'].indexOf(item.attr) >
                -1
            ) {
              proObj[item.attr] = item.attrValue
            }
          }
          if (this.businessType === this.pageBusinessType.INVENTORY && item.attr === 'stock') {
            proObj.oriNum = item.attrValue
          }
          // BOM清单、生产单item.editable === 1替换true
          if (
            [this.pageBusinessType.BOMBILL, this.pageBusinessType.PRODUCTIONORDER].indexOf(
              this.businessType
            ) > -1 &&
            (item.isRedundant === 0 || item.attr === 'unitNum' || item.attr === 'attritionRate')
          ) {
            proObj[item.attr] = item.attrValue
          }
          // 合同、机会、报价单
          if (
            [
              this.pageBusinessType.CONTRACT,
              this.pageBusinessType.SALES_OPPORTUNITY,
              this.pageBusinessType.QUOTATION
            ].indexOf(this.businessType) > -1
          ) {
            proObj[item.attr] = item.attrValue
            // 预估成本
            proObj.estimateCost = obj.estimateCost
          }
        })
        // 采购合同编辑的时候采购产品的ID需要传给后端
        // if (this.businessType === this.pageBusinessType.PURCHASE) {
        //   proObj.purchaseProductId = obj.purchaseProductId || 0
        // }
        proObj.businessProductId = obj.businessProductId || 0
        productList.push(proObj)
      })
      return productList
    },
    ...mapMutations({
      setDraftSaleExplains: 'SET_DRAFT_SELEEXPLAINS',
      setSaleExplains: 'SET_SELEEXPLAINS',
      setProductChosen: 'SET_PRODUCT_CHOSEN'
    })
  }
}
</script>

<style lang="scss">
.index {
  .link-product-no-label {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
