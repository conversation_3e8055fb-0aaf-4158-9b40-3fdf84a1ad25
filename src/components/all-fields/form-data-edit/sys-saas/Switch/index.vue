<!--
 * @Description: 开关组件--带校验条件
 -->
<template>
  <div v-if="!!visible && fieldInfo.fieldMapHidden !== 1" class="form-item">
    <!-- label 根据当前模式决定是否显示 -->
    <FormItemLayout
      v-if="!!visible"
      class="form-item-base"
      :field-info="fieldInfo"
      :field-position="fieldPosition"
      :label="showLabel ? label : ''"
      :memo="memo"
      :no-need-mode="true"
      :prop="prop"
      :rules="roules"
    >
      <SwitchField
        v-model="formData[fieldInfo.attr]"
        :field-info="fieldInfo"
        :field-list="fieldList"
        :form-data="formData"
        :is-edit="isEdit"
        :is-see="isSee"
        :iss-copy="issCopy"
        :mode="mode"
        :readonly="readonly"
      />
      <!-- 描述信息 -->
      <!-- <el-tooltip v-if="memo && fieldInfo.memo" class="item field-memo-tooltip" effect="dark" :content="fieldInfo.memo" placement="top"> -->
      <!-- <svg-icon icon-class="describe"></svg-icon> -->
      <!-- <i class="el-icon-question"></i>
      </el-tooltip> -->
    </FormItemLayout>
  </div>
</template>

<script>
import commonMixin from './../../all-mixin-new/common'
import defaultValMixin from './../../all-mixin-new/default-val'
import roulesMixin from './../../all-mixin-new/roules'
import switchFieldMap from './../../all-mixin-new/switchFieldMap'

import SwitchField from './Switch'

export default {
  components: {
    SwitchField
  },

  mixins: [commonMixin, defaultValMixin, roulesMixin, switchFieldMap],
  computed: {
    issCopy() {
      return this.saasObj && this.saasObj.isCopy === 1
    }
  }
}
</script>
