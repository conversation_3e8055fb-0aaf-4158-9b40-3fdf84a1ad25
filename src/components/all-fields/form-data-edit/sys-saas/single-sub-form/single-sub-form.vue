<!--
 * @Description: 单条子表单的字段 --- 业务字段封装
 -->
<template>
  <div>
    <!-- 对发票业务的寄送地址字段特殊处理 businessType: 901 attr:subForm_2 -->
    <template
      v-if="
        businessType === 901 &&
        ['subForm_1', 'subForm_2'].includes(attr) &&
        selectRadio.items.length > 1 &&
        !isEdit &&
        !isFlow
      "
    >
      <SelectRadioFormData
        v-if="selectShow"
        ref="selectRadioFormData"
        :base-form-data="selectFormData"
        :field-info="selectRadio"
        :form-data="selectFormData"
        :is-edit="false"
        prop="val"
      >
      </SelectRadioFormData>
    </template>
    <SubFormFormData
      v-show="!vHidden"
      :base-form-data="baseFormData"
      :field-info="fieldInfo"
      :field-list="fieldList"
      :form-data="formData"
      :is-edit="isEdit"
      :is-see="isSee"
      :prop="prop"
      :serial-no="serialNo"
      :single="true"
    >
    </SubFormFormData>
  </div>
</template>

<script>
import commonMixin from './../../all-mixin-new/common'
import relyAttrMixin from './../../all-mixin-new/rely-attr'
import xbb from '@xbb/xbb-utils'
import { makerFormData } from '@/components/form-data-edit/utils.js'

export default {
  name: 'SingleSubForm',

  mixins: [commonMixin, relyAttrMixin],

  data() {
    return {
      selectShow: false,
      selectFormData: {
        val: ''
      },
      dropDownBoxList: [],
      selectRadio: {
        attr: 'val',
        attrName:
          this.fieldInfo.attr === 'subForm_1'
            ? this.$t('form.invoiceTitle')
            : this.$t('form.selectAddress'),
        attrType: 'text',
        comboType: 0,
        editHide: 0,
        editable: 1,
        fieldType: 3,
        items: [
          // {'checked': false, 'isOther': 0, 'text': this.$t('formDataEdit.VATGeneralInvoce'), 'value': '0'},
          // {'checked': false, 'isOther': 0, 'text': this.$t('formDataEdit.VATSpecialInvoice'), 'value': '1'},
          // {'checked': false, 'isOther': 0, 'text': this.$t('formDataEdit.nationalTaxUniversalMachine'), 'value': '2'},
          // {'checked': false, 'isOther': 0, 'text': this.$t('formDataEdit.localTaxGeneralMachineInvoice'), 'value': '3'},
          // {'checked': false, 'isOther': 0, 'text': this.$t('formDataEdit.receipt'), 'value': '4'}
        ],
        memo: '',
        noRepeat: 0,
        parentAttr: '',
        required: 0,
        summaryFlag: 0,
        visible: 1
      }
    }
  },

  watch: {
    // baseFormData: {
    //   immediate: !0,
    //   handler (val) {
    //     val[this.attr] = [{}]
    //   }
    // }
  },
  computed: {
    isInvoiceField() {
      return this.attr === 'subForm_1'
    }
  },
  mounted() {
    this.specialInit()
    this.specialInitSec()
  },

  methods: {
    /**
     * 特殊处理
     * businessType === 903 && attr === 'subForm_1'
     */
    specialInitSec() {
      const obj = {
        text_1: '',
        text_2: '',
        text_3: '',
        text_4: '',
        text_5: '',
        text_6: '',
        text_7: ''
      }
      if (this.businessType === 903 && ['subForm_1', 'subForm_2'].includes(this.attr)) {
        try {
          this.baseFormData[this.attr].length === 0 && (this.baseFormData[this.attr] = [obj])
        } catch (err) {
          this.baseFormData[this.attr] = [obj]
        }
      }
    },
    init() {
      this.initAddWatch()
    },
    /**
     * 特殊处理
     * businessType === 901 && attr === 'subForm_2'
     */
    specialInit() {
      if (this.businessType === 901 && ['subForm_1', 'subForm_2'].includes(this.attr)) {
        // this.addWatch('baseFormData.subForm_2')
        this.addWatch(
          'baseFormData',
          (val) => {
            const arr = val && val[this.attr]
            this.setSelectOption(arr)
          },
          true
        )

        this.addWatch(
          'selectFormData.val',
          (val) => {
            if (val && val.value !== undefined) {
              const value = val.value
              let subVal = [this.dropDownBoxList[value]]
              subVal = JSON.parse(JSON.stringify(subVal))
              if (subVal.length === 1) {
                for (const key in subVal) {
                  if (subVal[key]) {
                    this.baseFormData[this.attr] = subVal
                  } else {
                    this.baseFormData[this.attr] = [{}]
                  }
                }
              }

              // if (subVal != null) {
              //   this.baseFormData['subForm_2'] = subVal
              // } else {
              //   console.log('99999999999999999999')
              //   this.baseFormData['subForm_2'] = undefined
              // }
            }
          },
          true
        )
      }
    },
    setSelectOption(val = []) {
      this.selectShow = false
      if (val) {
        const items = val.map((item, index) => {
          return {
            checked: index === 0,
            text: this.fieldInfo.attr === 'subForm_1' ? item['text_2'] : item['text_1'],
            value: index + ''
          }
        })
        this.selectRadio.items = items
      }
      this.$nextTick(() => {
        this.selectShow = true
      })
    },
    relyAttrChange(res) {
      this.dropDownBoxList = res[this.attr] || [{}]
      // 只对开票信息、寄送地址处理
      if (['subForm_1', 'subForm_2'].includes(this.attr)) {
        if (res[this.attr] && res[this.attr].length > 0) {
          if (res[this.attr].length === 1) {
            this.formData[this.attr] = res[this.attr]
            this.setSelectOption(res[this.attr])
          } else {
            this.dropDownBoxList = res[this.attr]
            this.baseFormData[this.attr] = [this.dropDownBoxList[0]]
            this.setSelectOption(this.dropDownBoxList)
          }
        } else {
          const row = makerFormData(this.fieldInfo.subForm.items)
          this.formData[this.attr] = [
            {
              subId: xbb.guid(),
              ...row
            }
          ]
          this.selectShow = false
        }
        // this.setSelectOption(this.dropDownBoxList)
      } else {
        const row = makerFormData(this.fieldInfo.subForm.items)
        const val =
          res[this.attr].length > 0
            ? [res[this.attr][0]]
            : [
                {
                  subId: xbb.guid(),
                  ...row
                }
              ]
        this.baseFormData[this.attr] = val
      }
    }
  }
}
</script>
