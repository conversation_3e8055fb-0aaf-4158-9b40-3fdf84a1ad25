<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description:新建表单-table 回显
 -->
<template>
  <div class="link-form-table">
    <el-table
      border
      class="link-form-table__table"
      :data="mapFilterTableDate"
      header-row-class-name="link-form-table__table--header"
      size="mini"
      style="min-width: 555px"
    >
      <!-- 渲染字段列 -->
      <template v-for="head in showColumnList">
        <!-- 子表单列 -->
        <template v-if="/^(20003)$/.test(head.fieldType)">
          <el-table-column
            :key="head.attr"
            :label="head.attrName"
            :prop="head.attr"
            show-overflow-tooltip
          >
            <el-table-column
              v-for="sub in head.subForm.items"
              :key="head.attr + '.' + sub.attr"
              :label="sub.attrName"
              :prop="head.attr"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <BaseTableTd :field-info="sub" :row="row" :sub-form-info="head" />
              </template>
            </el-table-column>
          </el-table-column>
        </template>

        <!-- 不同列 -->
        <template v-else>
          <el-table-column
            :key="head.attr"
            :label="head.attrName"
            :prop="head.attr"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <BaseTableTd :field-info="head" :row="row" />
            </template>
          </el-table-column>
        </template>
      </template>
      <el-table-column align="center" :label="$t('operation.operate')" prop="desc" width="160">
        <template slot-scope="{ row }">
          <BaseTableRow>
            <el-button
              :disabled="!editable || row.editPermission === false"
              size="mini"
              @click="dataEdit(row)"
              >{{ $t('operation.edit') }}</el-button
            >
            <el-button
              :disabled="!editable || row.delPermission === false"
              size="mini"
              type="danger"
              @click="dataDelete(row)"
              >{{ $t('operation.delete') }}</el-button
            >
          </BaseTableRow>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import BaseTableRow from '@/components/base/base-table-row'
import BaseTableTd from '@/components/base/base-table-td.vue'

export default {
  name: 'LinkFormTable',

  components: {
    BaseTableRow,
    BaseTableTd
  },

  props: {
    fieldInfo: {},
    showColumnList: {
      // 显示的列
      type: Array,
      default: () => []
    },
    tableData: {
      // 表格数据
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      filterTableData: []
    }
  },

  computed: {
    mapFilterTableDate() {
      let res = []
      res = this.filterTableData.map((item) => {
        return {
          ...item,
          ...item.data
        }
      })
      return res
    },
    editable: {
      get() {
        return !!this.fieldInfo.editable
      },
      set(val) {
        this.fieldInfo.editable = +val
      }
    }
  },

  watch: {
    tableData: {
      immediate: !0,
      handler(val) {
        if (!val) {
          this.filterTableData = []
          return false
        }
        this.filterTableData = val.filter((item) => {
          return item.operationFlag !== 2
        })
      }
    }
  },

  methods: {
    init() {},
    dataEdit(index) {
      this.$emit('dataEdit', index)
    },
    dataDelete(index) {
      this.$emit('dataDelete', index)
    }
  }
}
</script>

<style lang="scss">
.link-form-table {
  & > .link-form-table__table {
    .link-form-table__table--header {
      // height: 36px;
    }
    & > .el-table__body-wrapper {
      & > .el-table__body {
        td {
          padding: 0;
          .cell {
            padding: 0;
          }
        }
      }
    }
  }
}
</style>
