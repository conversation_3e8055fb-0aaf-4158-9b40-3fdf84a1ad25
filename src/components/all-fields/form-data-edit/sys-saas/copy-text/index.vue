<template>
  <div class="input-batch-index">
    <FormItemLayout
      v-if="!!visible"
      class="form-item-base"
      :field-info="fieldInfo"
      :field-position="fieldPosition"
      :label="showLabel ? label : ''"
      :memo="memo"
      :mode="mode"
      :no-need-mode="true"
      :prop="prop"
      :show-message="showMessage"
    >
      <CopyText v-model="formData[fieldInfo.attr]" />
    </FormItemLayout>
  </div>
</template>

<script>
import commonMixin from './../../all-mixin-new/common'
import CopyText from './copy-text.vue'

export default {
  name: 'CopyTextIndex',

  components: {
    CopyText
  },

  mixins: [commonMixin]
}
</script>
