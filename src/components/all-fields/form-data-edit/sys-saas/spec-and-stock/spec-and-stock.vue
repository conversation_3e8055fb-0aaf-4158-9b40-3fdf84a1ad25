<!--
 * @Description: 规格和库存
    fieldList: 字段解释
    formData: 父表单提交数据
    originForm: 原始form对象
    isJxcUse: 是否启用了进销存
    scope: 当前组件的所有字段
    warehouseArray 所有仓库列表
 -->

<template>
  <div class="spec-and-stock">
    <el-form ref="form" label-width="120px" :model="form" :rules="rules">
      <el-form-item
        v-if="isSpecification"
        :label="$t('business.specifications')"
        prop="specification"
      >
        <template slot="label">
          <span class="required-icon">{{ specificationRequied ? '*' : '' }}</span>
          <span>{{ fieldInfo.attrName || $t('business.specifications') }}：</span>
        </template>
        <!-- 高级规格 -->
        <div v-if="form.specification.advancedMode" class="advanced-spec">
          <ul class="spec-list">
            <li
              v-for="(item, index) in form.specification.advancedSpecification"
              :key="index"
              class="item"
            >
              <div class="name">
                <el-input
                  v-model.trim="item.name"
                  :placeholder="
                    $t('placeholder.inputPls', { attr: $t('business.productSpecifications') })
                  "
                  size="small"
                  @blur="handleSpecNameBlur(item)"
                ></el-input>
                <i class="el-icon-circle-close icon" @click="delSpec(index, item.name)"></i>
              </div>
              <div class="values">
                <span v-for="(tag, num) in item.value" :key="tag" class="el-tag">
                  <i class="el-icon-close el-tag__close" @click="delSpecTag(index, num, tag)"></i>
                  <span class="el-select__tags-text">{{ tag }}</span>
                </span>
                <div class="add-attr">
                  <el-input
                    v-model="addValues[index]"
                    :placeholder="$t('formDataEdit.multipleProductUseSpaces')"
                    size="small"
                    @keyup.native.enter="handleAddSpecTag(index)"
                  ></el-input>
                  <el-button type="text" @click="handleAddSpecTag(index)"
                    ><i class="el-icon-plus icon"></i
                  ></el-button>
                </div>
              </div>
            </li>
          </ul>
          <div class="add-spec">
            <el-button
              :disabled="form.specification.advancedSpecification.length >= 3"
              size="small"
              type="primary"
              @click.native="addSpec"
              >{{ $t('formDataEdit.addSpecificationItem') }}</el-button
            >
          </div>
        </div>

        <!-- 简单规格 -->
        <el-input
          v-else
          v-model="form.specification.simpleSpecification"
          :placeholder="$t('placeholder.inputPls', { attr: $t('business.productSpecifications') })"
          size="small"
        ></el-input>
        <template v-if="isSpecification && ['add', 'preview'].indexOf(formMode) > -1">
          <el-switch
            v-model="form.specification.advancedMode"
            active-color="#FF943E"
            active-text=""
            class="spec-switch"
            inactive-text=""
            @change="handleAdvanceChange"
          >
          </el-switch>
          <p class="tips-inline">{{ $t('business.multiSpecification') }}</p>
        </template>
      </el-form-item>
      <!-- 未开启多规格, 开启了库存字段 -->
      <el-form-item
        v-if="isStock && !form.specification.advancedMode"
        :label="$t('business.stock')"
        prop="stock"
      >
        <template slot="label">
          <span class="required-icon">{{ stockRequied ? '*' : '' }}</span>
          <span>{{ stockLabel }}：</span>
        </template>
        <el-input
          v-model.number="form.stock.attrValue"
          :disabled="isJxcUse"
          :placeholder="$t('placeholder.setUpPls', { attr: $t('business.productInventory') })"
          size="small"
          @keyup.native="onInput(form.stock)"
        >
        </el-input>
        <el-button
          v-if="form.stock.editable && isJxcUse"
          class="set-warehouse-text"
          download=""
          size="small"
          type="text"
          @click="setWarehouse()"
          >{{ $t('formDataEdit.setWarehouseInventory') }}</el-button
        >
      </el-form-item>
      <!-- 开启了多规格, 是否开启库存字段 -->
      <el-form-item
        v-if="form.specification.advancedMode"
        :label="$t('business.stock')"
        prop="stock"
      >
        <template slot="label">
          <span class="required-icon">{{ stockRequied ? '*' : '' }}</span>
          <span>{{ stockLabel }}</span>
        </template>
        <div v-if="form.specification.advancedMode" class="stock-table-out">
          <div class="stock-table">
            <table cellpadding="0" cellspacing="0">
              <thead>
                <tr>
                  <th
                    v-for="(item, index) in form.specification.advancedSpecification"
                    :key="index + 'a'"
                  >
                    <!--key值冲突zpp-->
                    <el-tooltip :content="item.name" effect="dark" placement="top">
                      <em class="spec-text">{{ item.name }}</em>
                    </el-tooltip>
                  </th>
                  <th>{{ $t('formDataEdit.specificationEncoding') }}</th>
                  <th>{{ $t('business.productBarCode') }}</th>
                  <th v-if="isCost" width="140">
                    {{ $t('business.cost') }}（{{ $t('unit.rmb') }}）
                  </th>
                  <th v-if="isStock" width="140">{{ $t('business.stock') }}</th>
                  <th v-if="isPrice">{{ $t('unit.unitPrice') }}（{{ $t('unit.rmb') }}）</th>
                  <th>{{ $t('message.isOpen') }}</th>
                </tr>
              </thead>
              <tbody
                v-if="
                  form.specification.advancedSpecification[0] &&
                  form.specification.advancedSpecification[0].value.length
                "
              >
                <tr v-for="(item, index) in childProductArray" :key="index">
                  <template
                    v-for="(n, specIndex) in form.specification.advancedSpecification.length"
                  >
                    <td v-if="showTd(specIndex, index)" :key="n" :rowspan="countSum(n)">
                      <el-tooltip
                        :content="getSpecAttr(specIndex, index)"
                        effect="dark"
                        placement="top"
                      >
                        <em class="spec-text">{{ getSpecAttr(specIndex, index) }}</em>
                      </el-tooltip>
                    </td>
                  </template>
                  <td>
                    <!-- <span class="text">{{childProductArray[index].childProductNo}}</span> -->
                    <input
                      v-model="childProductArray[index].childProductNo"
                      class="el-input"
                      :disabled="!childProductArray[index].isUse"
                      :placeholder="
                        $t('placeholder.inputPls', {
                          attr: $t('business.productSpecificationsnum')
                        })
                      "
                      size="small"
                      type="text"
                      @blur="handleNoOrBarcode(index, 'childProductNo')"
                    />
                  </td>
                  <td>
                    <input
                      ref="codeInput"
                      v-model="childProductArray[index].childProductBarcode"
                      class="el-input"
                      :disabled="!childProductArray[index].isUse"
                      :placeholder="$t('formDataEdit.pleaseInputOrScan')"
                      size="small"
                      type="text"
                      @change="handleNoOrBarcode(index, 'childProductBarcode')"
                    />
                  </td>
                  <td v-if="isCost">
                    <input
                      v-model.number="childProductArray[index].childProductCost"
                      class="el-input"
                      :disabled="
                        !form.cost.editable ||
                        (isJxcUse && !childProductArray[index].isUse) ||
                        childProductArray[index].costState
                      "
                      :placeholder="$t('placeholder.inputPls', { attr: $t('business.cost') })"
                      size="small"
                      type="text"
                      @change="cantBeString(index, 'childProductCost')"
                    />
                    <!-- 确认、取消成本价修改 -->
                    <template>
                      <template v-if="formMode === 'edit' && !childProductArray[index].costState">
                        <i class="el-icon-check td-btn" @click="confirmEditCost(index)"> </i>
                        <i class="el-icon-close td-btn" @click="cancelEditCost(index)"> </i>
                      </template>
                      <template v-else-if="formMode === 'edit' && form.cost.editable">
                        <el-button
                          class="el-icon-edit"
                          :disabled="!childProductArray[index].isUse"
                          type="text"
                          @click="editCost(index)"
                        ></el-button>
                      </template>
                    </template>
                  </td>
                  <td v-if="isStock">
                    <input
                      v-model.number="childProductArray[index].childProductStock"
                      class="el-input w100"
                      :disabled="
                        !form.stock.editable || isJxcUse || !childProductArray[index].isUse
                      "
                      :placeholder="$t('placeholder.inputPls', { attr: $t('business.stock') })"
                      size="small"
                      type="text"
                      @change="cantBeString(index, 'childProductStock')"
                    />
                    <span
                      v-if="form.stock.editable && isJxcUse"
                      class="link"
                      :class="{ 'link-disabled': !childProductArray[index].isUse }"
                      @click="setWarehouse(index)"
                      >{{ $t('operation.set') }}</span
                    >
                  </td>
                  <td v-if="isPrice">
                    <input
                      v-model.number="childProductArray[index].childProductPrice"
                      class="el-input"
                      :disabled="!form.price.editable || !childProductArray[index].isUse"
                      :placeholder="
                        $t('placeholder.inputPls', { attr: $t('business.sellingPrice') })
                      "
                      size="small"
                      type="text"
                      @change="cantBeString(index, 'childProductPrice')"
                    />
                  </td>
                  <td>
                    <el-switch
                      v-model="childProductArray[index].isUse"
                      :disabled="formMode === 'edit'"
                      @change="
                        (val) => {
                          handleUserChange(index, val)
                        }
                      "
                    ></el-switch>
                  </td>
                </tr>
                <!-- <tr>
                  <td colspan="9" class="wh-foot">
                    <span class="label">批量设置：</span>
                    <ul class="set-list" v-if="isSetListShow">
                      <li class="set-item" @click="openBatch('childProductCost')" v-if="isCost">成本</li>
                      <li class="set-item" @click="batchStock(isJxcUse)">库存</li>
                      <li class="set-item" @click="openBatch('childProductPrice')" v-if="isPrice">单价</li>
                    </ul>
                    <div class="set-form" v-else>
                      <el-input size="mini" v-model.number="batchValue" :placeholder="$t('formDataEdit.enterQuantityToSet')"></el-input>
                      <i class="set-btn blue el-icon-check" @click="setBatch"></i>
                      <i class="set-btn red el-icon-close" @click="cancelBatch"></i>
                    </div>

                    <span class="right text">
                      总库存：{{form.stock.attrValue || 0}}
                    </span>
                  </td>
                </tr> -->
              </tbody>
            </table>
          </div>
          <!-- 表格底部信息 -->
          <div class="wh-foot">
            <span class="label">{{ $t('form.batchSet') }}：</span>
            <ul v-if="isSetListShow" class="set-list">
              <li
                v-if="isCost"
                class="set-item"
                :class="{ noedit: form.cost.editable === 0 }"
                @click="openBatch('childProductCost')"
              >
                {{ $t('business.cost') }}
              </li>
              <li
                v-if="isStock"
                class="set-item"
                :class="{ noedit: form.stock.editable === 0 }"
                @click="batchStock(isJxcUse)"
              >
                {{ $t('business.stock') }}
              </li>
              <li
                v-if="isPrice"
                class="set-item"
                :class="{ noedit: form.price.editable === 0 }"
                @click="openBatch('childProductPrice')"
              >
                {{ $t('unit.unitPrice') }}
              </li>
            </ul>
            <div v-else class="set-form">
              <el-input
                v-model.number="batchValue"
                :placeholder="$t('formDataEdit.enterQuantityToSet')"
                size="mini"
              ></el-input>
              <i class="blue el-icon-check set-btn" @click="setBatch"></i>
              <i class="el-icon-close red set-btn" @click="cancelBatch"></i>
            </div>

            <span v-if="isStock" class="right text">
              {{ $t('business.sumStock') }}：{{ form.stock.attrValue || 0 }}
            </span>
          </div>
        </div>
        <template v-else>
          <!-- 开启进销存后，库存不允许手动输入 -->
          <template v-if="isStock">
            <el-input
              v-model.number="form.stock.attrValue"
              :disabled="isJxcUse"
              :placeholder="$t('placeholder.setUpPls', { attr: $t('business.productInventory') })"
              size="small"
              @keyup.native="onInput(form.stock)"
            >
            </el-input>
            <el-button
              v-if="form.stock.editable && isJxcUse"
              class="set-warehouse-text"
              download=""
              size="small"
              type="text"
              @click="setWarehouse()"
              >{{ $t('formDataEdit.setWarehouseInventory') }}</el-button
            >
          </template>
        </template>
      </el-form-item>

      <!-- <el-form-item label="销售价(元)" prop="price" v-if="isPrice">
        <el-input
          size="small"
          :disabled="form.specification.advancedMode"
          v-model.number="form.price.attrValue"
          @keyup.native="onInput(form.price)"
          placeholder="">
        </el-input>
        <el-checkbox v-model="form.price.isShow">隐藏销售价</el-checkbox>
        <p class="tips">销售价格为规格的价格的平均值，不可更改</p>
      </el-form-item>

      <el-form-item label="成本价(元)" prop="cost" v-if="isCost">
        <el-input
          size="small"
          :disabled="form.specification.advancedMode"
          v-model.number="form.cost.attrValue"
          @keyup.native="onInput(form.cost)"
          placeholder="">
        </el-input>
        <el-checkbox v-model="form.cost.isShow">隐藏成本价</el-checkbox>
        <p class="tips">成本价格为规格的价格的平均值，不可更改</p>
      </el-form-item> -->
    </el-form>

    <warehouse-select
      v-if="showWarehoseModal"
      ref="warehouseSelect"
      :stock-decimal-precision="stockDecimalPrecision"
      :ware-value-array="currentSpecValue"
      @change="warehouseChange"
      @close="showWarehoseModal = false"
    >
    </warehouse-select>
  </div>
</template>

<script>
import warehouseSelect from './warehouseSelect.vue' // 仓库设置
import { mapGetters } from 'vuex'
import { Message } from 'element-ui'
import xbb from '@xbb/xbb-utils'

let lazy

// 为Object添加一个原型方法，判断两个对象是否相等
function objEquals(object1, object2) {
  // For the first loop, we only check for types
  for (const propName in object1) {
    // Check for inherited methods and properties - like .equals itself
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/hasOwnProperty
    // Return false if the return value is different
    if (
      object1.hasOwnProperty(propName) !== object2.hasOwnProperty(propName) ||
      typeof object1[propName] !== typeof object2[propName]
    ) {
      return false
      // Check instance type
    }
  }
  // Now a deeper check using other objects property names
  for (const propName in object2) {
    // We must check instances anyway, there may be a property that only exists in object2
    // I wonder, if remembering the checked values from the first loop would be faster or not
    if (
      object1.hasOwnProperty(propName) !== object2.hasOwnProperty(propName) ||
      typeof object1[propName] !== typeof object2[propName]
    ) {
      return false
    }
    // If the property is inherited, do not check any more (it must be equa if both objects inherit it)
    if (!object1.hasOwnProperty(propName)) {
      continue
    }
    // Now the detail check and recursion
    // This returns the script back to the array comparing
    /** REQUIRES Array.equals**/
    if (object1[propName] instanceof Array && object2[propName] instanceof Array) {
      // recurse into the nested arrays
      if (objEquals(!object1[propName], object2[propName])) {
        return false
      }
    } else if (
      object1[propName] instanceof Object &&
      object2[propName] instanceof Object &&
      objEquals(!object1[propName], object2[propName])
    ) {
      return false
    } else if (object1[propName] !== object2[propName]) {
      return false
    }
  }
  // If everything passed, let's say YES
  return true
}

// 校验规格
// TODO 缺少规格查重
function checkSpecification(rule, value, callback) {
  if (value.advancedMode) {
    if (!value.advancedSpecification.length) {
      if (rule.required) {
        callback(new Error(this.$t('formDataEdit.pleaseAddProductSpecification')))
      } else {
        callback()
      }
    } else {
      let valid
      value.advancedSpecification.forEach((item) => {
        if (!item.name || !item.value.length) {
          valid = true
        }
      })
      if (valid) {
        callback(new Error('产品规格填写不完整，请检查后重新提交'))
      } else {
        callback()
      }
    }
  } else {
    if (value.simpleSpecification.trim() || !rule.required) {
      callback()
    } else {
      callback(new Error(this.$t('formDataEdit.pleaseAddProductSpecification')))
    }
  }
}

// 校验成本和售价和库存
function checkMoney(rule, value, callback) {
  const money = value.attrValue
  // 小数位超出提示
  if (money.toString().indexOf('.') !== -1 && money.toString().split('.')[1].length > rule.num) {
    return callback(new Error(`该字段限制小数位数不能大于${rule.num}`))
  }

  if (rule.required) {
    if ((money && isNaN(money)) || money < 0) {
      callback(new Error(this.$t('rule.moreThan', { attr: this.$t('nouns.number'), num: '0' })))
    } else {
      callback()
    }
  } else {
    if ((!isNaN(money) && money > 0) || !money) {
      callback()
    } else {
      callback(new Error(this.$t('rule.moreThan', { attr: this.$t('nouns.number'), num: '0' })))
    }
  }
}

// Array.prototype.unique = function () {
//   var result = []
//   this.forEach(function (v) {
//     if (!result.includes(v)) {
//       result.push(v)
//     }
//   })
//   return result
// }

export default {
  name: 'SpecAndStock',
  // 注入 src\components\form-data-edit\form-data-form.vue提供的parentForm
  inject: {
    parentForm: {
      default: () => null
    }
  },
  props: {
    // 表单字段解释
    fieldList: {
      type: Array,
      default: () => []
    },
    // 规格字段解释
    fieldInfo: {
      type: Object,
      default: () => ({})
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    originForm: {
      type: Object
    },
    isJxcUse: {
      type: Boolean,
      default: false
    },
    scope: {
      type: Array,
      required: true,
      default: () => []
    },
    warehouseArray: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      barcodeRequired: 0, // 条码是否必填
      stockLabel: this.$t('business.stock'), // 库存字段前缀
      addValues: [], // 用来存储要添加的规格属性
      childProductArray: [], // 子产品列表
      childProductWarehouse: [], // 分仓库存信息
      form: {
        specification: {
          advancedMode: false, // 规格是否开启高级模式
          simpleSpecification: '',
          advancedSpecification: [
            {
              name: '',
              value: []
            }
          ]
        },
        // 库存
        stock: {
          attrValue: 0,
          accuracy: 2,
          editable: 1
        },
        // 售价
        price: {
          attrValue: 0,
          accuracy: 2,
          isShow: true,
          editable: 1
        },
        // 成本价
        cost: {
          attrValue: 0,
          accuracy: 2,
          isShow: true,
          editable: 1
        }
      },
      rules: {},
      currentSpecValue: [], // 仓库设置所绑定的值
      currentSpecIndex: '', //  仓库设置所绑定的序号
      showWarehoseModal: false,
      // 批量设置相关
      isSetListShow: true,
      batchValue: '', // 批量设置所绑定的值
      currentType: '', // 要批量设置的类型
      productNo: '', // 产品编号
      stockDecimalPrecision: 2 // 库存数量精度
    }
  },

  created() {
    // 使用src\components\form-data-edit\form-data-form.vue提供的方法
    if (this.parentForm) {
      this.parentForm.childValidateFn = this.formatForm.bind(this)
    }

    this.initFormData()

    // 获取产品编号
    this.productNo = this.originForm.productNo || ''
  },

  computed: {
    ...mapGetters(['getProductNo', 'formMode']),
    // 所有sku的id
    stockSpecArr() {
      return this.childProductArray.map((item) => item.childProductSpec)
    },

    isPrice() {
      let result = false
      this.scope.forEach((item) => {
        if (item.attr === 'price') {
          result = true
        }
      })
      return result
    },

    isCost() {
      let result = false
      this.scope.forEach((item) => {
        if (item.attr === 'cost') {
          result = true
        }
      })
      return result
    },

    isSpecification() {
      let result = false
      let secendResult = false
      this.scope.forEach((item) => {
        // 判断编辑多规格或单规格、以及新建时候规格字段是否展示
        if (
          this.formMode === 'edit' &&
          item.attrValue &&
          typeof item.attrValue.specValue === 'string'
        ) {
          secendResult =
            Object.prototype.toString.apply(item.attrValue.specValue) === '[object String]'
        } else if (['add', 'preview'].indexOf(this.formMode) > -1) {
          secendResult = true
        } else if (
          this.formMode === 'edit' &&
          item.attr === 'specification' &&
          !item.attrValue &&
          !this.form.specification.advancedMode
        ) {
          // 单规格编辑
          secendResult = true
        }
        if (item.attr === 'specification' && secendResult) {
          result = true
        }
      })
      return result
    },

    isStock() {
      let result = false
      this.scope.forEach((item) => {
        if (item.attr === 'stock') {
          result = true
        }
      })
      return result
    },
    /**
     * 更具校验条件判断是否必填
     */
    stockRequied() {
      let res = false
      this.scope.forEach((item) => {
        if (item === 'stock' && item.required) {
          res = true
        }
      })
      return res
    },
    specificationRequied() {
      let res = false
      this.scope.forEach((item) => {
        if (item.attr === 'specification' && item.required) {
          res = true
        }
      })
      return res
    },

    // sku总数
    SKUNums() {
      let num = 1
      this.form.specification.advancedSpecification.forEach((item, index) => {
        if (index >= 0 && item.value && item.value.length) {
          num *= item.value.length
        }
      })
      return num
    }
  },

  methods: {
    // 初始化form数据
    initFormData() {
      this.scope.forEach((item) => {
        if (item.attr === 'specification') {
          if (item.attrValue) {
            this.$set(this.form.specification, 'advancedMode', item.attrValue.advancedMode)

            // 高级模式
            if (item.attrValue.advancedMode) {
              // 改变库存字段前缀
              this.stockLabel = this.$t('business.specificationRelated')
              this.$set(this.form.specification, 'advancedSpecification', item.attrValue.specValue)

              this.childProductArray = this.formatChildProduct(
                item.attrValue.specValue,
                item.childProductArray
              )
            } else {
              this.$set(this.form.specification, 'simpleSpecification', item.attrValue.specValue)
            }
          }

          this.$set(this.rules, 'specification', {
            required: !!item.required,
            validator: checkSpecification,
            trigger: 'change'
          })
        } else if (item.attr === 'stock') {
          // 获取库存精度、编辑权限
          this.form.stock.accuracy = item.accuracy
          this.form.stock.editable = item.editable
          if (typeof item.accuracy === 'number') {
            this.stockDecimalPrecision = item.accuracy
          }
          //  初始库存设为0
          this.form.stock.attrValue = item.attrValue || 0

          // 开启进销存
          if (item.childProductWarehouse && this.isJxcUse) {
            this.childProductWarehouse = item.childProductWarehouse
          }

          // this.$set(this.rules, 'stock', [
          //   { required: !!item.required, message: '库存不能为空' },
          //   { type: 'number', message: this.$t('formDataEdit.inventoryMustBeANumber') },
          //   { type: 'number', range: { min: 0 }, message: this.$t('rule.moreThan', {attr: this.$t('formDataEdit.stockNumber'), num: '0'}) }
          // ])
          this.$set(this.rules, 'stock', {
            required: !!item.required,
            type: 'object',
            validator: checkMoney,
            num: item.accuracy,
            // message: this.$t('formDataEdit.pleaseEnterProductCost'),
            trigger: 'change'
          })
        } else if (item.attr === 'cost') {
          if (item.attrValue) {
            const formatCost = {
              attrValue: item.attrValue.attrValue,
              isShow: !item.attrValue.isShow
            }
            this.form.cost = formatCost
          }
          // 获取成本精度、编辑权限
          this.form.cost.accuracy = item.accuracy
          this.form.cost.editable = item.editable

          this.$set(this.rules, 'cost', {
            required: !!item.required,
            type: 'object',
            validator: checkMoney,
            num: item.accuracy,
            // message: this.$t('formDataEdit.pleaseEnterProductCost'),
            trigger: 'change'
          })
        } else if (item.attr === 'price') {
          if (item.attrValue) {
            const formatPrice = {
              attrValue: item.attrValue.attrValue,
              isShow: !item.attrValue.isShow
            }
            this.form.price = formatPrice
          }
          // 获取销售价精度、编辑权限
          this.form.price.accuracy = item.accuracy
          this.form.price.editable = item.editable

          this.$set(this.rules, 'price', {
            required: !!item.required,
            type: 'object',
            validator: checkMoney,
            num: item.accuracy,
            // message: this.$t('formDataEdit.pleaseEnterTheProductPrice'),
            trigger: 'change'
          })
        } else if (item.attr === 'barcode') {
          this.barcodeRequired = item.required
        }
      })
    },

    /**
     * [formatChildProduct 服务端只返回了已启用的子产品列表，该方法遍历高级规格，生成一个完整的子产品列表（包含不启用的子产品）]
     * @param  {[type]} specification     [高级规格]
     * @param  {[type]} childProductArray [所有一起用的子产品列表]
     * @return {[type]}                   [返回完整的子产品列表]
     */
    formatChildProduct(specification, childProductArray) {
      const specArray = [] // 根据高级规格，生成一个包含所有规格的数组
      const arr = []
      for (let i = 0; i < this.SKUNums; i++) {
        const spec = this.getChildProductSpec(i)
        specArray.push(spec)
      }

      // 用生成的规格去匹配服务端返回的子产品列表，如果能匹配上，返回一条已启用的子产品，反之则返回一条未启用的子产品
      specArray.forEach((spec, index) => {
        let isUse
        childProductArray.forEach((child) => {
          if (objEquals(spec, child.childProductSpec)) {
            child.isUse = true
            arr.push(child)
            isUse = true
            // 编辑多规格时候成本价默认不可编辑
            if (this.formMode === 'edit') {
              child.costState = true
              child.originCost = child.childProductCost
            }
          }
        })
        if (!isUse) {
          const childProduct = {
            childProductId: 0,
            childProductSpec: this.getChildProductSpec(index),
            childProductNo: '',
            childProductStock: 0,
            costState: true,
            isUse: false
          }
          if (this.isPrice) {
            childProduct.childProductPrice = 0
          }
          if (this.isCost) {
            childProduct.childProductCost = 0
          }
          if (this.isStock) {
            childProduct.childProductStock = 0
          }
          arr.push(childProduct)
        }
      })
      return arr
    },

    // 监听高级规格的开启或关闭
    handleAdvanceChange(bool) {
      // 重新计算总库存
      if (bool) {
        // 隐藏单规格的成本单价
        this.fieldList.forEach((item) => {
          if (['barcode', 'price', 'cost'].indexOf(item.saasAttr) > -1) {
            item.visible = 0
          }
        })
        // 改变库存字段前缀
        this.stockLabel = this.$t('business.specificationRelated')
        this.countTotalStock(this.form.specification.advancedMode, this.childProductArray)
        // 开启高级规格时候去掉库存校验
        if (this.isStock) {
          this.$set(this.rules.stock, 'validator', null)
        }
      } else {
        // 显示单规格的成本单价
        this.fieldList.forEach((item) => {
          if (['barcode', 'price', 'cost'].indexOf(item.saasAttr) > -1) {
            item.visible = 1
          }
        })
        // 改变库存字段前缀
        this.stockLabel = this.$t('business.stock')
        this.$set(this.form.stock, 'attrValue', '')
        // 关闭高级规格时候加入库存校验
        this.$set(this.rules.stock, 'validator', checkMoney)
      }
    },

    // 添加规格项目
    addSpec() {
      if (this.form.specification.advancedSpecification.length < 3) {
        this.form.specification.advancedSpecification.push({
          name: '',
          value: []
        })
      }
    },

    // 监听规格名的blur事件
    handleSpecNameBlur(item) {
      // 规格判重
      const singelArr = this.form.specification.advancedSpecification.map((item) => item.name)
      const repeat = this.repeatFn(singelArr, 'spec')
      // 判断是否超过200字符,超过就截掉，但不终止程序
      if (item.name.length > 200) {
        Message({
          type: 'warning',
          message: this.$t('rule.lessThan', {
            attr: this.$t('business.specificationsValue'),
            num: '200'
          })
        })
        item.name = item.name.substring(0, 200)
      }
      if (repeat) {
        item.name = ''
      }
      // 如果item.value有值，先判断是否超过200字符，无则重新生成所有的childProductSpec
      if (item.value && item.value.length) {
        this.childProductArray.forEach((item, index) => {
          item.childProductSpec = this.getChildProductSpec(index)
        })
      }
    },

    // 删除规格项目
    delSpec(index, name) {
      this.form.specification.advancedSpecification.splice(index, 1)
      this.childProductArray = []
      for (let i = 0; i < this.SKUNums; i++) {
        this.addStock(i)
      }
    },

    // 规格和规格值去重方法
    repeatFn(arr, type) {
      const newArr = []
      let repeat = false
      for (let i = 0; i < arr.length; i++) {
        if (newArr.indexOf(arr[i]) === -1) {
          newArr.push(arr[i])
        } else {
          repeat = true
        }
      }
      if (type === 'spec') {
        if (repeat) {
          Message({
            type: 'warning',
            message: this.$t('formDataEdit.doNotEnterDuplicateSpecifications')
          })
        }
        return repeat
      } else if (type === 'specval') {
        if (repeat) {
          Message({
            type: 'warning',
            message: this.$t('formDataEdit.doNotEnterDuplicateSpecificationsValue')
          })
        }
        return newArr
      }
    },
    // 添加规格属性
    handleAddSpecTag(index) {
      this.addSpecTag(index)
      this.clearAddValues(index)

      for (let i = 0; i < this.SKUNums; i++) {
        this.addStock(i)
      }
    },

    // 在规格添加之前做一些校验
    addSpecTag(index) {
      let str = this.addValues[index] || ''
      if (!str.trim()) return // 判空
      str = str.trim()
      // 规格属性值超过200字符，截掉多余并且终止程序
      if (str.length > 200) {
        Message({
          type: 'warning',
          message: this.$t('rule.lessThan', { attr: '规格属性值', num: '200' })
        })
        this.$set(this.addValues, index, this.addValues[index].substring(0, 200))
        return
      }
      let arr = str.split(/\s+/) // 使用空格分割成数组
      if (this.form.specification.advancedSpecification[index].value) {
        // 去重，先去除arr本身重复的值，再去除已有规格中重复的值
        arr = arr
          .filter((spec, i, array) => array.indexOf(spec) === i)
          .filter(
            (tag) => !this.form.specification.advancedSpecification[index].value.includes(tag)
          )

        // 如果去重复后新添加的规格为空，则给予提示；如不为空，则去重后继续执行后续逻辑
        if (!arr.length) {
          Message({
            type: 'warning',
            message: this.$t('formDataEdit.doNotEnterDuplicateSpecificationsValue')
          })
          return
        }
        if (this.SKUNums * arr.length > 600) {
          Message({
            type: 'error',
            message: this.$t('formDataEdit.skuNumberLimit')
          })
          return
        }
        arr = this.form.specification.advancedSpecification[index].value.concat(arr)
        this.$set(this.form.specification.advancedSpecification[index], 'value', arr)
      }
    },

    /**
     * 删除规格值
     * @param  {number} index 规格所在行
     * @param  {number} num 规格值的序号
     * @param  {string}} tag 规格值
     * @return {void}
     */
    delSpecTag(index, num, tag) {
      this.form.specification.advancedSpecification[index].value.splice(num, 1)
      const specName = this.form.specification.advancedSpecification[index].name

      // 重新生成this.childProductArray
      this.childProductArray = this.childProductArray.filter((item) => {
        return item.childProductSpec[specName] !== tag
      })
    },

    // 清空 addValues
    clearAddValues(index) {
      this.$set(this.addValues, index, '')
    },

    /**
     * [根据规格，动态改变库存相关信息]
     * @param  {[number]} index
     * @return {[type]}           [description]
     */
    addStock(index) {
      const spec = this.getChildProductSpec(index)
      // 如果此规格不存在，说明为新增属性，则向 childProductArray 中添加一条数据
      const len = this.stockSpecArr.length
      if (len) {
        for (let i = 0; i < len; i++) {
          if (!objEquals(spec, this.stockSpecArr[i])) {
            this.$set(this.childProductArray, index, this.creatChildProduct(index))
            return
          }
        }
      } else {
        this.$set(this.childProductArray, index, this.creatChildProduct(index))
      }
    },

    // 生成一个新的规格
    creatChildProduct(index) {
      const childProduct = {
        childProductId: 0,
        childProductSpec: this.getChildProductSpec(index),
        childProductNo: this.makeProductNo(index),
        childProductStock: 0,
        isUse: true
      }
      if (this.isPrice) {
        childProduct.childProductPrice = 0
      }
      if (this.isCost) {
        childProduct.childProductCost = 0
      }
      if (this.isStock) {
        childProduct.childProductStock = 0
      }
      return childProduct
    },

    /*
      计算属性的乘积
      @params
        specIndex 规格项目在 advancedSpecification 中的序号
    */
    countSum(specIndex) {
      let num = 1
      this.form.specification.advancedSpecification.forEach((item, index) => {
        if (index >= specIndex && item.value && item.value.length) {
          num *= item.value.length
        }
      })
      return num
    },

    /*
      根据传入的属性值，拿到相应规格的属性
      @params
        specIndex 规格项目在 advancedSpecification 中的序号
        index 所有属性在遍历时的序号
    */
    getSpecAttr(specIndex, index) {
      // 获取当前规格项目下的属性值
      const currentValues = this.form.specification.advancedSpecification[specIndex].value
      let indexCopy

      // 判断是否是最后一个规格项目
      if (
        this.form.specification.advancedSpecification[specIndex + 1] &&
        this.form.specification.advancedSpecification[specIndex + 1].value.length
      ) {
        indexCopy = index / this.countSum(specIndex + 1)
      } else {
        indexCopy = index
      }

      const i = Math.floor(indexCopy % currentValues.length)

      if (i.toString() !== 'NaN') {
        return currentValues[i]
      } else {
        return ''
      }
    },

    // 获取childProductArray的childProductSpec属性
    getChildProductSpec(index) {
      const obj = {}
      this.form.specification.advancedSpecification.forEach((item, specIndex) => {
        obj[item.name] = this.getSpecAttr(specIndex, index)
      })
      return obj
    },

    // 根据传入的条件，来判断是否显示该td
    showTd(specIndex, index) {
      // 如果当前项目下没有属性，则不显示
      if (!this.form.specification.advancedSpecification[specIndex]) {
        return false

        // 自己悟一下吧
      } else if (index % this.countSum(specIndex + 1) === 0) {
        return true
      } else {
        return false
      }
    },

    // 设置仓库库存
    setWarehouse(index) {
      // 如果该规格未启用，则不执行任何操作
      if (typeof index === 'number' && index > -1 && !this.childProductArray[index].isUse) {
        return
      }

      // 将规格序号暂存在 currentSpecIndex 中
      this.currentSpecIndex = index

      const defaultValue = this.warehouseArray.map((item) => {
        return {
          warehouseChecked: true,
          warehouseStockId: 0,
          childProductWarehouseId: item.warehouseId,
          childProductWarehouseName: item.warehouseName,
          childProductWarehouseStock: 0,
          childProductWarehouseOriStock: 0,
          stockLowerLimit: 0,
          stockUpperLimit: 0,
          isNotify: false
        }
      })

      // 批量库存设置
      if (index === -1) {
        this.currentSpecValue = defaultValue

        // 高级模式的库存设置
      } else if (this.form.specification.advancedMode) {
        const childStock = this.childProductArray[index].childProductWarehouse

        // 已存在数据
        if (childStock instanceof Array && childStock.length) {
          this.currentSpecValue = JSON.parse(JSON.stringify(childStock))
        } else {
          this.currentSpecValue = defaultValue
        }

        // 简单模式的库存设置
      } else {
        // 已存在库存数据
        if (this.childProductWarehouse.length) {
          this.currentSpecValue = JSON.parse(JSON.stringify(this.childProductWarehouse))
        } else {
          this.currentSpecValue = defaultValue
        }
      }
      // 库存修改的警告
      this.$confirm(
        this.$t('formDataEdit.directlySetInventoryTips'),
        this.$t('formDataEdit.setWarehouseInventory'),
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // 打开设置库存弹窗
          this.showWarehoseModal = true
          this.$nextTick(() => {
            this.$refs.warehouseSelect.dialogVisible = true
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: this.$t('operation.cancel')
          })
        })
    },

    // 编辑多规格设置成本
    editCost(index) {
      this.$confirm(
        this.$t('formDataEdit.directlyEditCostTips'),
        this.$t('formDataEdit.editorialCost'),
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.childProductArray = this.childProductArray.map((it, itIndex) => {
            if (index === itIndex) {
              it.costState = false
            }
            return it
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: this.$t('operation.cancel')
          })
          this.childProductArray = this.childProductArray.map((it, itIndex) => {
            if (index === itIndex) {
              it.costState = true
            }
            return it
          })
        })
    },
    // 确认成本价修改
    confirmEditCost(index) {
      this.childProductArray = this.childProductArray.map((it, itIndex) => {
        if (index === itIndex) {
          it.costState = true
        }
        return it
      })
    },
    // 取消成本价修改
    cancelEditCost(index) {
      this.childProductArray = this.childProductArray.map((it, itIndex) => {
        if (index === itIndex) {
          it.costState = true
          it.childProductCost = it.originCost
        }
        return it
      })
    },
    // 监听仓库设置的数据提交
    warehouseChange(arr) {
      // 批量库存设置
      if (this.currentSpecIndex === -1) {
        // alert(this.$t('formDataEdit.batchInventorySettings'))
        this.childProductArray.forEach((item, index) => {
          if (item.isUse) {
            // 编辑产品时，点击批量设置库存，warehouseStockId会被重置为0，提交后会被认为是新增仓库，因此这里会保留原有的warehouseStockId
            if (item.childProductWarehouse && item.childProductWarehouse.length) {
              const formatChildProductWarehouse = arr.map((arrItem, arrIndex) => {
                arrItem.warehouseStockId =
                  item.childProductWarehouse[arrIndex].warehouseStockId || 0
                return arrItem
              })
              item.childProductWarehouse = JSON.parse(JSON.stringify(formatChildProductWarehouse))
            } else {
              // 产品新建时，childProductWarehouse 不存在，因此直接赋值即可
              item.childProductWarehouse = arr
            }
            this.countSpecStock(index, arr)
          }
        })
        this.countTotalStock(this.form.specification.advancedMode, this.childProductArray)

        // 高级模式的库存设置
      } else if (this.form.specification.advancedMode) {
        // alert(this.$t('formDataEdit.advancedModeInventorySettings'))
        this.$set(this.childProductArray[this.currentSpecIndex], 'childProductWarehouse', arr)

        this.countSpecStock(this.currentSpecIndex, arr)
        this.countTotalStock(this.form.specification.advancedMode, this.childProductArray)

        // 简单模式的库存设置
      } else {
        // alert(this.$t('formDataEdit.simpleModeInventorySettings'))
        this.childProductWarehouse = arr

        this.countTotalStock(this.form.specification.advancedMode, arr)
      }
    },

    // 开启进销存时，计算每个规格的库存
    countSpecStock(index, arr) {
      let stock = 0
      // 总仓库存
      arr.forEach((child) => {
        if (child.childProductWarehouseId === -1) {
          stock = child.childProductWarehouseStock
        }
      })

      stock = Number(stock.toFixed(this.stockDecimalPrecision))
      this.$set(this.childProductArray[index], 'childProductStock', stock)
    },

    // 计算总库存
    countTotalStock(isAdvanced, arr) {
      let stock = 0
      if (isAdvanced) {
        arr.forEach((item) => {
          if (item.isUse) {
            stock = xbb.plus(stock, item.childProductStock)
          }
        })
      } else {
        // 总仓库存
        arr.forEach((child) => {
          if (child.childProductWarehouseId === -1) {
            stock = child.childProductWarehouseStock
          }
        })
      }
      this.$set(this.form.stock, 'attrValue', stock)
    },

    // 所填入的值不能为字符串 && 小数最多两位
    cantBeString(index, attr) {
      const value = this.childProductArray[index][attr]
      if (typeof value === 'string' || value < 0) {
        clearTimeout(lazy)
        lazy = setTimeout(() => {
          this.$set(this.childProductArray[index], attr, 0)
          Message({
            type: 'warning',
            message: this.$t('rule.moreThan', { attr: this.$t('nouns.number'), num: '0' })
          })
        }, 1000)
      } else {
        // 成本、库存、销售价精度
        let accuracy = 2
        let text = ''
        if (attr === 'childProductCost') {
          accuracy = this.form.cost.accuracy
          text = this.$t('business.cost')
        } else if (attr === 'childProductStock') {
          accuracy = this.stockDecimalPrecision
          text = this.$t('formDataEdit.stockNumber')
        } else if (attr === 'childProductPrice') {
          accuracy = this.form.price.accuracy
          text = this.$t('business.sellingPrice')
        }
        const floatNums = value.toString().split('.')
        if (floatNums.length === 2 && floatNums[1].length > accuracy) {
          clearTimeout(lazy)
          lazy = setTimeout(() => {
            const messageText = text + this.$t('rule.decimalLimit', { attr: accuracy })
            this.$set(this.childProductArray[index], attr, Number(value.toFixed(accuracy)))
            Message({
              type: 'warning',
              message: messageText
            })
          }, 1000)
        }
      }
    },

    // 监听产品编号的blur事件/监听产品条码change事件
    handleNoOrBarcode(index, attr) {
      // 1.当用户输入完产品编号时，判断是否重复，如有重复，则提示客户并自动修改为不重复的产品编号 2.当用户录入产品条码时，判断是否重复，如有重复，则提示并且不完成录入光标保持在当前输入框，如果没有重复，光标自动跳到下一个输入框
      const value = this.childProductArray[index][attr]
      let isRepet

      this.childProductArray.forEach((item, i) => {
        if (item[attr] === value && i !== index) {
          isRepet = true
        }
      })

      if (isRepet) {
        if (attr === 'childProductNo') {
          Message({
            type: 'warning',
            message: this.$t('formDataEdit.productNumbersNoSame')
          })
          this.$set(this.childProductArray[index], attr, this.makeProductNoNotRepet(index))
        } else if (attr === 'childProductBarcode') {
          Message({
            type: 'warning',
            message: this.$t('formDataEdit.duplicateProductBarcode')
          })
          this.$set(this.childProductArray[index], 'childProductBarcode', '')
        }
      } else {
        if (attr === 'childProductBarcode' && this.$refs.codeInput[index + 1]) {
          this.$refs.codeInput[index + 1].focus()
        }
      }
    },

    // 库存批量设置
    batchStock(isJxcUse) {
      // 库存不可编辑禁止点击
      if (!this.form.stock.editable) {
        return
      }
      if (isJxcUse) {
        this.setWarehouse(-1)
      } else {
        this.openBatch('childProductStock')
      }
    },

    // 打开设置框
    openBatch(type) {
      if (type === 'childProductCost') {
        // 成本不可编辑禁止点击
        if (!this.form.cost.editable) {
          return
        }
        this.$confirm(
          this.$t('formDataEdit.directlyEditCostTips'),
          this.$t('formDataEdit.editorialCost'),
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          }
        )
          .then(() => {
            this.currentType = type
            this.isSetListShow = false
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: this.$t('operation.cancel')
            })
          })
      } else if (type === 'childProductPrice') {
        // 单价不可编辑禁止点击
        if (!this.form.price.editable) {
          return
        }
        this.currentType = type
        this.isSetListShow = false
      }
    },

    // 批量设置
    setBatch() {
      if (typeof this.batchValue === 'string') {
        Message({
          type: 'warning',
          message: this.$t('formDataEdit.pleaseEnterTheCorrectValue')
        })
        return
      }
      this.childProductArray.forEach((item) => {
        if (item.isUse) {
          if (this.currentType === 'childProductCost') {
            item[this.currentType] = Number(this.batchValue.toFixed(this.form.cost.accuracy))
          } else if (this.currentType === 'childProductPrice') {
            item[this.currentType] = Number(this.batchValue.toFixed(this.form.price.accuracy))
          }
        }
      })

      if (this.currentType === 'childProductStock') {
        this.countTotalStock(this.form.specification.advancedMode, this.childProductArray)
      }

      this.cancelBatch()
    },

    // 取消批量设置
    cancelBatch() {
      this.batchValue = ''
      this.currentType = ''
      this.isSetListShow = true
    },

    // 监听规格启用操作
    handleUserChange(index, value) {
      // 重新计算总库存
      this.countTotalStock(this.form.specification.advancedMode, this.childProductArray)

      // 启用规格时，生成不重复的产品编号；关闭规格时，清空产品编号
      if (value) {
        const No = this.makeProductNoNotRepet(index)
        this.$set(this.childProductArray[index], 'childProductNo', No)
      } else {
        this.$set(this.childProductArray[index], 'childProductNo', '')
      }
    },

    // 默认生成产品编号规则
    makeProductNo(index) {
      return this.productNo + '.' + index
    },

    // 产品编号判重
    isProductNoRepet(No) {
      const result = this.childProductArray.findIndex((item) => {
        return item.childProductNo === No
      })
      return result > -1
    },

    // 生成不重复的产品编号
    makeProductNoNotRepet(index) {
      let No
      let i = index
      let isRepet = true
      while (isRepet) {
        No = this.makeProductNo(i)
        isRepet = this.isProductNoRepet(No)
        i++
      }
      return No
    },

    // 验证表单
    validateForm() {
      let result
      let res = true // 产品条码必填校验
      this.childProductArray.forEach((item) => {
        if (item.childProductBarcode === '' && this.barcodeRequired) {
          res = false
        }
      })
      if (!res) {
        Message({
          type: 'error',
          message: this.$t('formDataEdit.productNumbersRequire')
        })
      }
      this.$refs.form.validate((valid) => {
        if (valid && res) {
          result = true
        }
      })
      return result
    },

    // 超出小数位的处理
    onInput(item) {
      const value = item.attrValue.toString()
      if (value.indexOf('.') !== -1) {
        const precisionLen = value.split('.')[1].length // 获取input输入小数位的长度
        if (precisionLen > item.accuracy) {
          setTimeout(() => {
            // 如果小数位超出，则保留有效小数位
            item.attrValue = value.substr(0, value.indexOf('.') + item.accuracy + 1)
          }, 1500)
        }
      }
    },

    /**
     * 提交前对数据进行格式化
     * tips： 成本与售价的isShow属性，实际对应的逻辑是隐藏，所以这里要求反
     **/
    formatForm() {
      if (!this.validateForm()) return false

      this.scope.forEach((item) => {
        if (item.attr === 'specification') {
          const advanced = this.form.specification.advancedMode
          const specification = {
            advancedMode: advanced,
            specValue: advanced
              ? this.form.specification.advancedSpecification
              : this.form.specification.simpleSpecification
          }

          // 开启高级模式
          if (advanced) {
            // 只提交启用的子产品信息
            specification.childProductArray = this.childProductArray.filter((item) => {
              return item.isUse
            })
          }
          this.$set(this.originForm, 'specification', specification)
        } else if (item.attr === 'stock') {
          this.$set(this.originForm, 'stock', {
            allStock: this.form.stock.attrValue
          })

          // 未开启高级模式，但开启了进销存
          if (!this.form.specification.advancedMode && this.isJxcUse) {
            this.$set(this.originForm.stock, 'childProductWarehouse', this.childProductWarehouse)
          }
        } else if (item.attr === 'cost') {
          this.$set(this.originForm, 'cost', {
            attrValue: this.form.cost.attrValue,
            isShow: !this.form.cost.isShow
          })
        } else if (item.attr === 'price') {
          this.$set(this.originForm, 'price', {
            attrValue: this.form.price.attrValue,
            isShow: !this.form.price.isShow
          })
        }
      })
      this.$emit('originForm', this.originForm)
      return true
    }
  },

  watch: {
    // 监听库存的变化，实时计算平均成本价与平均售价
    childProductArray: {
      handler(arr) {
        if (this.form.specification.advancedMode) {
          let costSum = 0
          let priceSum = 0
          let isUseSum = 0
          arr.forEach((item) => {
            if (item.isUse) {
              costSum += item.childProductCost
              priceSum += item.childProductPrice
              isUseSum++
            }
          })

          // 当启用的规格属性大于0时，计算平均价，否则平均价为0
          if (isUseSum) {
            if (this.isCost) {
              this.$set(
                this.form.cost,
                'attrValue',
                Number((costSum / isUseSum).toFixed(this.form.cost.accuracy))
              )
            }
            if (this.isPrice) {
              this.$set(
                this.form.price,
                'attrValue',
                Number((priceSum / isUseSum).toFixed(this.form.price.accuracy))
              )
            }
          } else {
            this.$set(this.form.cost, 'attrValue', 0)
            this.$set(this.form.price, 'attrValue', 0)
          }

          // 当未开启进销存时，需要计算总库存
          if (!this.isJxcUse) {
            this.countTotalStock(true, this.childProductArray)
          }
        } else {
          // this.childProductArray = []
        }
      },
      deep: true
    },

    // 监听产品编号的改变
    getProductNo(newVal, oldVal) {
      this.productNo = newVal
      // 每当编号变更时，重新生成所有编号
      if (newVal !== oldVal && newVal) {
        this.childProductArray.forEach((child, index) => {
          if (child.isUse) {
            child.childProductNo = newVal + '.' + index
          }
        })
      }
    }
  },

  components: {
    WarehouseSelect: warehouseSelect
  }
}
</script>

<style lang="scss" scoped>
.spec-and-stock {
  .tips,
  .tips-inline {
    font-size: 13px;
    color: $text-plain;
  }
  .advanced-spec {
    display: inline-block;
    width: 480px;
    vertical-align: top;
    .spec-list {
      padding: 10px;
      background-color: $base-white;
      border: 1px solid $neutral-color-3;
      .item {
        margin-top: 5px;
        &:first-child {
          margin-top: 0;
        }
        .name {
          padding: 2px 8px;
          overflow: hidden;
          text-align: right;
          background: #f3f6fb;
          .el-input {
            float: left;
            width: 150px;
          }
          .icon {
            display: inline-block;
            color: $text-plain;
            cursor: pointer;
            &:hover {
              color: $link-base-color-6;
            }
          }
        }
        .values {
          .el-tag {
            max-width: 200px;
            margin: 8px 0 0 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;
            .el-tag__close.el-icon-close {
              right: 5px;
            }
          }
          .add-attr {
            display: inline-block;
            margin-top: 8px;
            overflow: hidden;
            vertical-align: top;
            .el-input {
              width: 200px;
              margin: 0 0 0 4px;
            }
          }
        }
      }
    }
    .add-spec {
      padding-top: 10px;
      font-size: 13px;
    }
  }
  .spec-switch {
    margin-left: 10px;
  }
  .stock-table-out {
    .stock-table {
      overflow-x: auto;
      overflow-y: hidden;
      table {
        min-width: 1100px;
      }
      .el-input {
        box-sizing: border-box;
        display: inline-block;
        height: 32px;
        padding: 0 15px;
        font-size: inherit;
        line-height: 32px;
        color: $text-plain;
        background-color: $base-white;
        border: 1px solid $neutral-color-3;
        border-radius: 4px;
        outline: none;
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        &:focus {
          border-color: $brand-color-5;
        }
        &.w100 {
          width: 100px;
        }
      }
    }
    padding: 0;
    border-collapse: separate;
    background-color: $base-white;
    // border-color: $neutral-color-3;
    // border-style: solid;
    // border-width: 1px 0 0 1px;
    border: 1px solid $neutral-color-3;
    td,
    th {
      position: relative;
      padding: 4px 10px;
      text-align: center;
      border-right: 1px solid $neutral-color-3;
      border-bottom: 1px solid $neutral-color-3;
      &:last-of-type {
        border-right: none;
      }
      &:first-of-type {
        border-left: none;
      }
    }
    th {
      line-height: 30px;
      background-color: $bg-blue;
    }
    .el-input {
      width: 100px;
    }
    .td-btn {
      position: absolute;
      right: 0;
      z-index: 2;
      width: 19px;
      height: 19px;
      font-size: 12px;
      line-height: 19px;
      text-align: center;
      cursor: pointer;
      background-color: $base-white;
      border-left: 1px solid $neutral-color-3;
      border-radius: 1px;
      opacity: 0.9;
      &:hover {
        opacity: 1;
      }
      &.el-icon-check {
        top: 0;
        color: $link-base-color-6;
        border-bottom: 1px solid $neutral-color-3;
      }
      &.el-icon-close {
        bottom: 0;
        color: $error-base-color-6;
      }
    }
    .el-icon-edit {
      color: $link-base-color-6;
      &:disabled {
        color: $text-grey;
      }
    }
    .link {
      margin-left: 6px;
      font-size: 13px;
      color: $link-base-color-6;
      cursor: pointer;
      &.link-disabled {
        color: $text-grey;
        cursor: not-allowed;
      }
    }
    .wh-foot {
      padding: 0 10px;
      line-height: 30px;
      .label {
        display: inline-block;
        vertical-align: top;
      }
      .set-list {
        display: inline-block;
        font-size: 0;
        vertical-align: top;
        .set-item {
          display: inline-block;
          margin-left: 15px;
          font-size: 13px;
          color: $link-base-color-6;
          vertical-align: top;
          cursor: pointer;
          &.noedit {
            cursor: not-allowed;
          }
        }
      }
      .set-form {
        display: inline-block;
        margin-left: 10px;
        .el-input {
          display: inline-block;
          width: 120px;
        }
        .set-btn {
          display: inline-block;
          padding: 0 2px;
          font-size: 15px;
          cursor: pointer;
          &.blue {
            color: $link-base-color-6;
          }
          &.red {
            color: $error-base-color-6;
          }
        }
      }
      .right {
        float: right;
      }
      .text {
        font-size: 13px;
      }
    }
  }
  // label样式
  .el-form-item__label {
    display: flex;
    padding-right: 38px;
    & > .required-icon {
      display: inline-block;
      width: 10px;
      color: $error-color-5;
    }
  }
}
</style>

<style lang="scss">
.spec-and-stock {
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    display: none;
  }
  .el-form-item__content {
    width: calc(100% - 160px);
    // overflow: auto;
    .el-input {
      max-width: 480px;
    }
    .tips-inline {
      display: inline-block;
      vertical-align: top;
    }
    .el-checkbox__label {
      font-size: 13px;
      color: $text-plain;
    }
    .set-warehouse-text {
      color: $link-base-color-6;
    }
    em.el-tooltip {
      display: inline-block;
      width: 80px;
      overflow: hidden;
      font-style: normal;
      text-overflow: ellipsis;
      white-space: nowrap;
      vertical-align: top;
    }
  }
}
</style>
