<!--
 * @Description: 任务描述字段
-->
<template>
  <FormItemLayout
    v-if="!!visible"
    :field-info="fieldInfo"
    :field-position="fieldPosition"
    :label="showLabel ? label : ''"
    :memo="memo"
    :mode="mode"
    :package-limit="fieldInfo.packageLimit"
    :prop="prop"
    :rules="roules"
    :show-message="showMessage"
  >
    <!-- 详情页展示 -->
    <div v-if="isDetail">
      <template v-if="formData[attr]">
        <div class="des-text" v-html="addNodeByWXInText(formData[attr].taskMemoText)"></div>
        <el-button v-if="QWtodotask && formData[attr].taskMemoText" type="text" @click="copy"
          >复制文字</el-button
        >

        <img-list v-if="imgList.length" class="des-item" :img-list="imgList" />
        <el-button v-if="QWtodotask && imgList.length" type="text" @click="onDownload"
          >一键下载图片</el-button
        >
        <file-list v-if="fileList.length" :file-list="fileList" />
      </template>
    </div>
    <!-- 新建编辑 -->
    <taskDescription
      v-else
      v-model="formData[fieldInfo.attr]"
      :field-info="fieldInfo"
      :form-data="formData"
      :is-design="isDesign"
      :readonly="readonly"
    ></taskDescription>
  </FormItemLayout>
</template>

<script>
import taskDescription from './task-description'
import commonMixin from './../all-mixin-new/common'
import defaultValMixin from './../all-mixin-new/default-val'
import roulesMixin from './../all-mixin-new/roules'
import fileList from '@/components/common/communicate-card/components/file-content/file-list.vue'
import imgList from '@/components/common/communicate-card/components/img-content/img-list.vue'
import xbb from '@xbb/xbb-utils'
import {
  getCopyContent,
  getStageCopyContent
} from '@/api/scrm/customer-operation/automated-operation.js'
import { copyText } from '@/views/scrm/utils/index.js'
import { TASK_MATERIAL_TYPES } from '@/views/scrm/constants/enum.sop.js'

export default {
  components: {
    TaskDescription: taskDescription,
    FileList: fileList,
    ImgList: imgList
  },

  mixins: [commonMixin, defaultValMixin, roulesMixin],
  computed: {
    imgList() {
      const img = xbb._get(this.formData[this.attr], 'taskMemoImages', [])
      return img.map((item) => item.url)
    },
    fileList() {
      return xbb._get(this.formData[this.attr], 'taskMemoLink', [])
    },
    // 工作任务不展示
    QWtodotask() {
      return [6301, 100900, 108, 8005].includes(+this.businessType)
    }
  },
  data() {
    return {
      copyContent: '',
      isWxPC: xbb.isThirdPC(['wx'], false)
    }
  },

  mounted() {
    this.QWtodotask && this.getCopy()
  },
  methods: {
    // 企微获取要复制的文案
    @xbb.debounceWrap(500)
    getCopy() {
      const param = {
        id: this.formData.id || null,
        dataId: this.formData.dataId || null,
        stageWorkId: this.formData.stageWorkId || null,
        businessType: this.formData.businessType || null
      }
      const api = this.formData.id ? getCopyContent : getStageCopyContent
      api(param).then((res) => {
        this.copyContent = res.result.copyContent
      })
    },
    // 复制
    copy() {
      copyText({
        value: this.copyContent,
        message: '复制成功'
      })
    },
    // 企微 返回可渲染的素材格式 type-IMAGES/FILES
    returnTaskMaterialList(type, data) {
      if (!data.length) return []
      let list = []
      data.forEach((item) => {
        if (TASK_MATERIAL_TYPES[type].includes(item.type)) {
          list = list.concat(item.list)
        }
      })
      return list
    },
    // 图片下载
    onDownload() {
      const img = xbb._get(this.formData[this.attr], 'taskMemoImages', [])
      img.forEach((item) => {
        setTimeout(() => {
          utils.fileDownload(item.url, item.name)
        }, 200)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.des-text {
  white-space: pre-wrap;
}
.des-item {
  margin-bottom: 12px;
}
.el-button {
  color: $link-base-color-6;
}
</style>
