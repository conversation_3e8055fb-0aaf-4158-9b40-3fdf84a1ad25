<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 选择产品的弹窗
 -->
<template>
  <div class="product-dialog">
    <el-dialog append-to-body :title="title" top="8vh" :visible.sync="dialogShow" width="935px">
      <!-- 内容 -->
      <el-dialog
        :append-to-body="true"
        :title="$t('operation.commonFilterSettings')"
        :visible.sync="filterDialogVisible"
        width="930px"
      >
        <div class="common-filter-setting">
          <el-transfer
            v-model="currentScreen"
            :data="computedHeadList"
            :props="{ key: 'attr', label: 'attrName' }"
            :titles="[$t('operation.allFields'), $t('operation.selectedFields')]"
          ></el-transfer>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="filterDialogVisible = false">{{ $t('operation.cancel') }}</el-button>
          <el-button type="primary" @click="changeScreenModel">{{
            $t('operation.confirm')
          }}</el-button>
        </span>
      </el-dialog>

      <div class="relation-data-dialog">
        <div class="relation-data-dialog--header">
          <div v-if="!hideFilterSet" class="filter-setting" @click="openFilterSetting">
            <i class="web-icon-filter web-iconfont"></i>
          </div>
          <template v-if="isFormShowAddButton || isDetailShowAddButton">
            <div class="add-product" @click="openAddDialog">
              + {{ $t('formDataEdit.addProduct') }}
            </div>
          </template>
          <ProductFilter
            :all-condition-map="allConditionMap"
            :attr="attr"
            :contract-params="contractParams"
            :dialog-type="dialogType"
            :distributor-id="distributorId"
            :link-info="getRealLinkInfo"
            :screen-list="screenList"
            @conditionChange="conditionChange"
          />
        </div>
        <div class="relation-data-dialog--body">
          <div class="_table" :class="isFullScreen ? '_table-fullScreen' : ''">
            <el-table
              v-if="headList.length > 0"
              v-loading="tableLoading"
              border
              class="relation-table-table__table"
              :data="formatDataList"
              style="width: 100%"
            >
              <el-table-column align="center" width="55px">
                <!-- element-ui 特性，自定表头需保留 slot-scope="scope"-->
                <template v-if="multiple" slot="header" slot-scope="scope">
                  <el-checkbox :value="checkboxAllVal" @change="checkboxAll"> </el-checkbox>
                </template>
                <template slot-scope="{ row }">
                  <template v-if="multiple">
                    <template v-if="productIdIn.includes(row.id)">
                      <el-checkbox
                        :disabled="productIdIn.includes(row.id)"
                        :value="productIdIn.includes(row.id)"
                      >
                      </el-checkbox>
                    </template>
                    <template v-else>
                      <el-checkbox
                        :value="
                          multipleSelectList.some(
                            (item) => item.id === row.id || item.id === row.dataId
                          )
                        "
                        @change="(val) => checkboxSelect(val, row)"
                      >
                      </el-checkbox>
                    </template>
                    <!-- <el-checkbox @change="(val) => checkboxSelect(val,row)" :value="multipleSelectList.some(item=> item.id ===row.id ) || productIdIn.includes(row.id)" :disabled="productIdIn.includes(row.id)">
                    </el-checkbox> -->
                  </template>
                  <template v-else>
                    <el-radio
                      :label="true"
                      :value="currentSelectData && currentSelectData.id === row.id"
                      @change="checkboxChange(row)"
                    >
                      {{ '' }}
                    </el-radio>
                  </template>
                </template>
              </el-table-column>
              <!-- 增加序号列 -->
              <el-table-column align="center" type="index" width="50">
                <template #header>
                  <i
                    class="web-iconfont"
                    :class="isFullScreen ? 'web-icon-tuichuquanping' : 'web-icon-quanping1'"
                    style="cursor: pointer"
                    @click="fullScreen"
                  >
                  </i>
                </template>
              </el-table-column>
              <!-- 渲染字段列 -->
              <template v-for="(head, index) in headList">
                <!-- 子表单列 -->
                <template v-if="/^(20003|10006)$/.test(head.fieldType)">
                  <el-table-column
                    :key="head.attr + index"
                    :label="head.attrName"
                    :prop="head.attr"
                    show-overflow-tooltip
                  >
                    <el-table-column
                      v-for="sub in head.subForm.items"
                      :key="head.attr + '.' + sub.attr"
                      :label="sub.attrName"
                      :prop="head.attr"
                      show-overflow-tooltip
                    >
                      <template slot-scope="{ row }">
                        <BaseTableTd :field-info="sub" :row="row" :sub-form-info="head" />
                      </template>
                    </el-table-column>
                  </el-table-column>
                </template>

                <!-- 不同列 -->
                <template v-else>
                  <el-table-column
                    :key="head.attr + index"
                    :label="head.attrName"
                    :prop="head.attr"
                    show-overflow-tooltip
                  >
                    <template slot-scope="{ row }">
                      <BaseTableTd :field-info="head" :row="row" />
                      <!-- {{row[head.attr] && row[head.attr][0].name}} -->
                    </template>
                  </el-table-column>
                </template>
              </template>
            </el-table>

            <div class="relation-table-table__pagination">
              <v-pagination
                :current-page.sync="page.page"
                layout="slot,prev, pager, next, jumper"
                :page-size="page.pageSize"
                :total="page.total"
                @current-change="page.handleCurrentChange"
              >
              </v-pagination>
            </div>
          </div>
        </div>
      </div>
      <!-- foot -->
      <span slot="footer" class="dialog-footer">
        <!-- <span v-if="multiple" style="float: left;">{{ $t('formDataEdit.selectedProducts') }}: {{ multipleSelectLength }} </span> -->
        <span v-if="multiple" style="float: left"
          >{{ selectHint }}: {{ originAlreadyProductsLen }}
        </span>
        <el-button @click="dialogShow = false">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('operation.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import ProductFilter from './product-filter'
import dialogMixin from '@/mixin/dialog'
import BaseTableTd from '@/components/base/base-table-td.vue'
import VPagination from '@/components/common/v-pagination'
import {
  getSselectProductList,
  setScreenSelectProduct,
  getSelectContractList,
  setSelectContractList,
  getSelectPurchaseList,
  setSelectPurchaseList
} from '@/api/formData'
import { getConditionList } from '@/api/statistics.js'
import { mapActions } from 'vuex'
import i18n from '@/lang'
import xbb from '@xbb/xbb-utils'
import { filterList } from '@/constants/common/filter.js'
import { delCommafy } from '@/utils/unit'

export default {
  name: 'ProductDialog',
  components: {
    BaseTableTd,
    VPagination,
    ProductFilter
  },

  mixins: [dialogMixin],
  inject: {
    // 注入详情中的对象
    referenceDetail: {
      default: () => null
    },
    appId: { default: '' },
    menuId: { default: '' },
    formId: { default: '' },
    saasMark: { default: '' },
    businessType: { default: '' },
    formDialog: { default: () => ({}) }
  },

  props: {
    contractParams: {
      // 合同视图 和 产品视图下 筛选条件 需要传合同视图参数
      type: Object,
      default: () => ({})
    },
    distributorId: Number,
    // 已选产品(需禁用)
    productIdIn: {
      type: Array,
      default: () => []
    },
    // 弹窗的类型，默认为产品（用作请求接口的判断）
    dialogType: {
      type: String,
      default: () => ''
    },
    title: {
      default: i18n.t('formDataEdit.selectAssociatedData')
    },
    selectHint: {
      default: i18n.t('formDataEdit.selectedProducts')
    },
    attr: {
      default: ''
    },
    multiple: {
      default: false
    },
    /**
     * 多选时的回选数据
     */
    defaultMultipleSelects: {
      default: () => []
    },
    selectData: {},
    linkInfo: {
      default: () => ({})
    },
    tableLoading: {
      default: false
    },
    headList: {
      default: () => []
    },
    dataList: {
      default: () => []
    },
    page: {
      default: () => {
        return {
          page: 1,
          pageSize: 10,
          total: 0,
          handleCurrentChange() {}
        }
      }
    },
    // 弹窗是否显示关联新建选项
    showAdd: {
      type: Boolean
    }
  },

  data() {
    return {
      multipleSelectList: [],
      currentFilterQueryInfo: {}, // 当前列表的查询参数，当linkInfo中的值和当前列表不一致时，使用当前
      hideFilterSet: true, // 隐藏筛选设置按钮
      currentScreen: [],
      allConditionMap: {},
      transferData: [],
      specialFilterParams: {},
      multipleSelects: {},
      advanceFilterEcho: [], // 存放累计筛选
      currentSelectData: undefined,
      screen: [],
      screenList: [],
      formExplainList: [], // 筛选项的解释
      otherScreenList: [],
      filterDialogVisible: false,
      isFullScreen: false
    }
  },

  computed: {
    // 表单里面关联新建显示
    isFormShowAddButton() {
      return this.formDialog && !this.formDialog.isChild && this.showAdd && this.notFlowApprove
    },
    // 详情关联新建产品
    isDetailShowAddButton() {
      return this.referenceDetail && this.showAdd
    },
    // 查询参数不对的时候，使用接口里给的替换
    getRealLinkInfo() {
      return {
        ...this.linkInfo,
        ...this.currentFilterQueryInfo
      }
    },
    multipleSelectLength() {
      if (!this.multiple) return
      return this.multipleSelectList.length
      // return Object.keys(this.multipleSelects).filter(key => this.multipleSelects[key]).length
    },

    originAlreadyProductsLen() {
      const arr = this.multipleSelectList || []
      const idArr = arr.map((item) => item.id)
      return [...new Set(idArr)].length
    },

    computedHeadList() {
      return this.otherScreenList
        .concat(this.formExplainList)
        .filter((item) => filterList.includes(item.fieldType))
    },
    // screenModel: {
    //   set (val = []) {
    //     let attrMap = {}
    //     this.headList.forEach(item => {
    //       attrMap[item.attr] = item
    //     })
    //     let res = val.map(item => attrMap[item])
    //     this.screen = res
    //   },
    //   get () {
    //     return this.screen.map(item => item.attr)
    //   }
    // },
    checkboxAllVal() {
      return (
        this.dataList.length &&
        this.dataList.every(({ id }) => this.multipleSelectList.some((item) => id === item.id))
      )
    },
    /**
     * 渲染的表格数据，将data 和 流水号平级
     */
    formatDataList() {
      const arr = JSON.parse(JSON.stringify(this.dataList || []))
      return arr.map((item) => {
        item.data['num_1'] = delCommafy(item.data['num_1'])
        return {
          ...item,
          ...item.data
        }
      })
    },
    notFlowApprove() {
      // 审批中的发起审批需要显示新建
      if (sessionStorage.getItem('localApprove') === 'true') {
        return true
      } else {
        const { name } = this.$route
        return name !== 'flowApproveIndex'
      }
    }
  },

  watch: {
    screen: {
      handler(val) {
        this.screenList = []
        const attrMap = {}
        this.computedHeadList.forEach((item) => {
          attrMap[item.attr] = item
        })
        this.screen.forEach((item) => {
          if (attrMap[item]) {
            this.screenList.push(attrMap[item])
          }
        })
        // this.screenList = this.screen.map(item => attrMap[item])
      }
    },
    selectData: {
      immediate: true,
      handler(val) {
        if (val) {
          this.currentSelectData = JSON.parse(JSON.stringify(val))
        }
      }
    },
    defaultMultipleSelects: {
      immediate: true,
      handler(val) {
        if (val && val.length) {
          // let map = {}
          // val.forEach(item => {
          //   map[item.id] = item
          // })
          // this.multipleSelects = map
          this.multipleSelectList = [...val]
        }
      }
    },
    advanceFilterEcho: {
      handler(val) {
        this.$emit('productFilterChange', val)
      }
    }
  },

  destroyed() {
    this.closePicturePreview()
  },

  created() {
    this.getSselectProductList()
  },

  mounted() {},

  methods: {
    ...mapActions('formDataDialogChild', ['formDataAdd']),
    // 全屏
    fullScreen() {
      this.isFullScreen = !this.isFullScreen
    },
    init() {},
    // 关闭图片预览
    closePicturePreview() {
      const box = document.getElementsByClassName('lg-preview-wrapper')[0]
      box && box.remove()
    },
    getConditionList() {
      return new Promise((resolve, reject) => {
        getConditionList({})
          .then(({ result: { conditionList } }) => {
            this.allConditionMap = conditionList
            // this.conditions = this.getConditions()
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    changeScreenModel() {
      // let list = this.screen
      this.filterDialogVisible = false
      this.screen = [].concat(this.currentScreen)
      this.setScreenSelectProduct()
      this.$emit('productFilterChange', [])
    },
    openFilterSetting() {
      this.currentScreen = [].concat(this.screen)
      this.filterDialogVisible = true
    },
    checkboxAll(val) {
      if (this.businessType === 1001) {
        // 供应商
        this.formatDataList.some((item) => {
          if (this.referenceDetail && !this.productIdIn.includes(item.id)) {
            return this.checkboxSelect(val, item)
          }
          return false
        })
      } else {
        this.formatDataList.forEach((item) => {
          this.checkboxSelect(val, item)
        })
      }
    },
    checkboxSelect(val, row) {
      const res = false
      if (val) {
        // this.$set(this.multipleSelects, row.id, {
        //   dataId: row.dataId,
        //   refProductId: row.refProductId,
        //   id: row.id,
        //   data: row,
        //   sourceData: row.sourceData
        // })
        const index = this.multipleSelectList.findIndex((item) => item.id === row.id)

        if (index !== -1) {
          this.multipleSelectList.splice(index, 1)
        }
        this.multipleSelectList.push({
          dataId: row.dataId,
          refProductId: row.refProductId,
          id: row.id,
          data: row,
          sourceData: row.sourceData
        })
        if (this.referenceDetail && this.multipleSelectList.length >= 201) {
          this.$message({
            type: 'error',
            message: this.$t('formDataEdit.selectedProductsOver')
          })
          this.$nextTick(() => {
            this.multipleSelectList.pop()
          })
          return true
        }
      } else {
        const index = this.multipleSelectList.findIndex((item) => row.id === item.id)
        this.multipleSelectList.splice(index, 1)
        // this.$set(this.multipleSelects, row.id, undefined)
      }
      if (this.referenceDetail) {
        return res
      }
    },
    submit() {
      this.dialogShow = false
      const selectedData = this.multiple ? this.multipleSelectList : this.currentSelectData
      if (!selectedData) return // 规避selectedData为undefined的情况
      console.log(selectedData)
      this.$emit('dialogSubmit', selectedData, this.multiple)
    },
    checkboxChange(row) {
      this.currentSelectData = {
        dataId: row.dataId,
        refProductId: row.refProductId,
        id: row.id,
        data: row,
        sourceData: row.sourceData
      }
    },
    /**
     * 确定筛选设置的接口
     *
     * @param {object} params 接口的传参
     */
    confirmScreenInterface(params) {
      switch (this.dialogType) {
        case 'waitInstockPurchase':
          params.businessType = 1101
          return setSelectPurchaseList(params)
        case 'relyContract':
          params.businessType = 201
          return setSelectContractList(params)
        default:
          return setScreenSelectProduct(params)
      }
    },
    /**
     * 设置选择产品常用筛选项
     */
    setScreenSelectProduct() {
      this.confirmScreenInterface({
        appId: this.appId,
        formId: this.formId,
        attr: this.attr,
        saasMark: this.saasMark,
        businessType: this.businessType,
        subBusinessType: this.subBusinessType || this.businessType,
        screenList: this.screen
        // 'screenList': [
        //   'text_1',
        //   'text_2',
        //   'num_12'
        // ]
      }).then(() => {
        this.$message({
          message: this.$t('message.setSuccess'),
          type: 'success'
        })
      })
    },
    conditionChange(condition, operate) {
      const obj = xbb.deepClone(condition)
      const arr = this.advanceFilterEcho.map((item) => {
        return item.attr + '.' + item.subAttr
      })
      const index = arr.indexOf(obj.attr + '.' + obj.subAttr)
      if (operate === 'add') {
        if (index > -1) {
          this.advanceFilterEcho.splice(index, 1, obj)
        } else {
          this.advanceFilterEcho.push(obj)
        }
      } else if (operate === 'delete' && index > -1) {
        this.advanceFilterEcho.splice(index, 1)
      }
      // TODO:这里后期如果想要和高级筛选的逻辑统一，需要独立出去一个方法
      this.specialFilterParams.conditions = this.advanceFilterEcho.map((condition) => {
        // 非数组转成数组
        if (!Array.isArray(condition.value)) {
          condition.value = condition.value || condition.value === 0 ? [condition.value] : []
        }
        if (condition.attr.includes('.')) {
          const arr = condition.attr.split('.')
          condition.attr = arr[0]
          condition.subAttr = arr[1]
        }
        return condition
      })
    },
    /**
     * 获取设置筛选项
     */
    getSselectProductList() {
      const p1 = this.getConditionList()
      const waitFormId = this.dialogType === 'relyContract' ? this.contractParams.formId : undefined // 以销定购列表 合同筛选项 的参数传合同视图的合同formId
      const params = {
        appId: this.appId,
        formId: waitFormId || this.formId,
        attr: this.attr,
        saasMark: this.saasMark,
        businessType: this.businessType,
        distributorId: this.distributorId,
        subBusinessType: this.subBusinessType || this.businessType,
        sourceBusinessType: +this.$route.query.subBusinessType
      }
      let p2
      switch (this.dialogType) {
        case 'waitInstockPurchase':
          params.businessType = 1101
          p2 = getSelectPurchaseList(params)
          break
        case 'relyContract':
          params.businessType = 201
          p2 = getSelectContractList(params)
          break
        default:
          if (
            [
              'waitInWarehouseList',
              'waitOutWarehouseList',
              'smartReplenishmentList',
              'waitPurchaseContractList'
            ].includes(this.$route.name)
          ) {
            params.subBusinessType = params.sourceBusinessType
            params.sourceBusinessType = undefined
          }
          p2 = getSselectProductList(params)
          break
      }
      Promise.all([p1, p2]).then(([res1, res2]) => {
        const {
          result: {
            formExplainList = [],
            screen = [],
            screenList = [],
            hideFilter,
            sourceInfo = {}
          }
        } = res2
        this.formExplainList = formExplainList
        this.otherScreenList = screenList
        this.screen = screen
        this.currentFilterQueryInfo = sourceInfo
        this.hideFilterSet = hideFilter
      })
    },
    openAddDialog() {
      const {
        linkAppId,
        linkBusinessType,
        linkFormId,
        linkMenuId,
        linkSaasMark,
        linkSubBusinessType
      } = this.linkInfo
      const params = {
        appId: linkAppId,
        businessType: linkBusinessType,
        formId: linkFormId,
        menuId: linkMenuId,
        saasMark: linkSaasMark,
        subBusinessType: linkSubBusinessType || ''
      }
      this.formDataAdd(params).then(() => {
        this.dialogShow = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.common-filter-setting {
  .el-transfer {
    padding-left: 20px;
  }
  .el-transfer__buttons .el-button {
    display: block;
    margin: 0;
    span {
      margin: 0;
    }
  }
  .el-transfer__buttons .el-button:first-child {
    margin-bottom: 10px;
  }
}
.relation-data-dialog {
  & > .relation-data-dialog--header {
    position: relative;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    min-height: 20px;
    margin-bottom: 10px;
    & > .filter-setting {
      flex: 1;
      cursor: pointer;
    }
    & > .add-product {
      margin-right: 4px;
      color: $brand-color-5;
      white-space: nowrap;
      cursor: pointer;
    }
  }
  & > .relation-data-dialog--body {
    border: 1px solid $neutral-color-3;
    :deep(.text_left) {
      white-space: pre;
    }
  }

  ._table {
    & > .relation-table-table__table {
      border-left-color: transparent;
      :deep(.el-table__body-wrapper) {
        :deep(td) {
          padding: 0;
          td > .cell {
            padding: 0;
          }
        }
      }
    }
    & > .relation-table-table__pagination {
      display: flex;
      flex-direction: row-reverse;
      margin: 10px;
    }
  }

  ._table-fullScreen {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 3000;
    width: 100%;
    height: 100%;
    background: white;
  }
}
</style>
