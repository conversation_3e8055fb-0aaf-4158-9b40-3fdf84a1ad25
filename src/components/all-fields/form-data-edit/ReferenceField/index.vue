<template>
  <FormItemLayout
    v-if="!!visible"
    :key="fieldInfo.attr"
    class="form-item-base"
    :field-info="fieldInfo"
    :field-position="fieldPosition"
    :label="showLabel ? label : ''"
    :memo="memo"
    :mode="mode"
    :package-limit="fieldInfo.packageLimit"
    :prop="prop"
    :rules="roules"
    :show-message="showMessage"
  >
    <ReferenceField
      v-model="formData[fieldInfo.attr]"
      :base-form-data="baseFormData"
      :current-row-index="currentRowIndex"
      :editable="editable"
      :father-data="fatherData"
      :field-info="fieldInfo"
      :form-data="formData"
      :is-design="isDesign"
      :is-edit="isEdit"
      :is-see="isSee"
      :mode="mode"
      :readonly="readonly"
      :show-title="showTitle"
    />
  </FormItemLayout>
</template>

<script>
import ReferenceField from './ReferenceField'

import commonMixin from './../all-mixin-new/common'
import defaultValMixin from './../all-mixin-new/default-val'
import roulesMixin from './../all-mixin-new/roules'

export default {
  components: {
    ReferenceField
  },
  mixins: [commonMixin, defaultValMixin, roulesMixin],
  props: {
    index: {
      type: Number,
      default: 0
    }
  },
  computed: {
    // 注入当前表单
    formInfo() {
      return this.$store.state.form.formInfo
    },
    // 计算当前表单的属性
    formInfoAttr() {
      return this.formInfo && this.formInfo.formAttr
    }
  }
}
</script>
