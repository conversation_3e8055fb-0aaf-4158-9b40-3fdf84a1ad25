<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="isFullscreen"
    :visible.sync="dialogVisible"
  >
    <template #title>
      <span class="el-dialog__title">{{ $t('operation.pasteAdd') }}</span>
      <button class="fullscreen-btn">
        <i
          class="fullscreen-icon web-iconfont"
          :class="[isFullscreen ? 'web-icon-tuichuquanping' : 'web-icon-quanping1']"
          @click="isFullscreen = !isFullscreen"
        />
      </button>
    </template>

    <Tips :tips="tips" />
    <el-alert
      v-if="errorCount > 0"
      :closable="false"
      style="margin-bottom: 16px"
      :title="errorTip"
      type="error"
    />
    <div class="table-wrapper" :style="tableWrapperStyle">
      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        element-loading-text="excel数据解析中"
        :empty-text="tableEmptyText"
        height="100%"
      >
        <el-table-column v-for="(item, index) in fieldList" :key="index" :label="item.attrName">
          <template #default="{ row }">
            <span
              :class="{
                'is-error': row[item.attr].isError
              }"
              >{{ row[item.attr].displayValue }}</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer">
      <span v-if="tableData.length" class="footer-tips">共{{ tableData.length }}行</span>
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import Tips from '@/components/common/tips.vue'

const validateFunctionMap = {
  // 数字校验
  2: function (value) {
    return !isNaN(value)
  },
  // 日期校验
  4: function (value) {
    return !isNaN(Date.parse(value))
  }
}

const processFunctionMap = {
  // 数字字段转成数字
  2: function (value) {
    return Number(value)
  },
  // 日期转为时间戳
  4: function (value) {
    return Date.parse(value) / 1000
  }
}

export default {
  name: 'PasteAddDialog',

  components: {
    Tips
  },

  props: {
    visible: Boolean,
    currentRowCount: Number,
    maxRowCount: Number,
    originalFieldList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },

  data() {
    return {
      tips: [
        '使用快捷键 Ctrl + V 将 Excel 内容粘贴至表格中',
        '已自动隐藏禁止编辑的字段、不支持粘贴的字段，请注意字段顺序',
        `当前子表单行数上限为 ${this.maxRowCount} 行，已有 ${this.currentRowCount} 行，还可以添加 ${
          this.maxRowCount - this.currentRowCount
        } 行，超出行数上限的数据将自动忽略`
      ],
      loading: false,
      isFullscreen: false,
      errorCount: 0,
      tableData: []
    }
  },

  computed: {
    errorTip() {
      return `表格数据存在 ${this.errorCount} 处错误，请修改后再保存。如不修改，错误的数据会被忽略。`
    },
    tableEmptyText() {
      return this.fieldList.length ? '将 Excel 数据粘贴到此处' : '当前子表单不存在支持粘贴的字段'
    },
    tableWrapperStyle() {
      return this.isFullscreen
        ? {
            flex: 1,
            'min-height': 0
          }
        : {
            height: '400px'
          }
    },
    dialogVisible: {
      set(val) {
        this.$emit('update:visible', val)
      },
      get() {
        return this.visible
      }
    },
    fieldList() {
      const fieldList = []

      this.originalFieldList.forEach((item) => {
        if (!item.editable) return

        if (Number(item.fieldType) === 12) {
          // 地址字段要做拆分
          fieldList.push(...this.splitAddressField(item))
        } else {
          const field = xbb._pick(item, ['attrName', 'fieldType', 'attr'])
          if (validateFunctionMap[field.fieldType]) {
            field.validateFn = validateFunctionMap[field.fieldType]
          }
          if (processFunctionMap[field.fieldType]) {
            field.processFn = processFunctionMap[field.fieldType]
          }
          fieldList.push(field)
        }
      })

      return fieldList
    }
  },

  watch: {
    fieldList(value) {
      this.tableData = []
    },
    isFullscreen(value) {
      if (!value) {
        this.$nextTick(() => {
          this.$refs.table.doLayout()
        })
      }
    }
  },

  created() {
    document.addEventListener('paste', this.handlePaste)
    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener('paste', this.handlePaste)
    })
  },

  beforeDestroy() {},

  methods: {
    splitAddressField(originalFieldItem) {
      return [
        {
          attrName: `${originalFieldItem.attrName}(省/自治州/直辖市)`,
          fieldType: originalFieldItem.fieldType,
          attr: `${originalFieldItem.attr}-province`
        },
        {
          attrName: `${originalFieldItem.attrName}(市)`,
          fieldType: originalFieldItem.fieldType,
          attr: `${originalFieldItem.attr}-city`
        },
        {
          attrName: `${originalFieldItem.attrName}(县/区)`,
          fieldType: originalFieldItem.fieldType,
          attr: `${originalFieldItem.attr}-district`
        },
        {
          attrName: `${originalFieldItem.attrName}(详细地址)`,
          fieldType: originalFieldItem.fieldType,
          attr: `${originalFieldItem.attr}-address`
        }
      ]
    },
    mergeAddressField(row) {
      const addressFieldKey = Object.keys(row).filter((key) => key.includes('address'))
      if (!addressFieldKey.length) return

      addressFieldKey.forEach((key) => {
        const parts = key.split('-')
        const addressKey = parts[0]
        const addressPart = parts[1]

        if (!row[addressKey]) {
          row[addressKey] = {
            value: {}
          }
        }

        row[addressKey].value[addressPart] = row[key].value
        delete row[key]
      })
    },
    handlePaste(e) {
      if (!this.fieldList.length) return

      try {
        const $doc = new DOMParser().parseFromString(
          e.clipboardData.getData('text/html'),
          'text/html'
        )
        // 检查复制的内容里是否解析出表格
        let $trs = Array.from($doc.querySelectorAll('table tr'))
        if (!$trs.length) {
          this.$message.error('粘贴失败，请检查excel数据')
          return
        }

        // 舍去超出行数上限的数据
        $trs = $trs.slice(0, this.maxRowCount - this.currentRowCount)

        this.tableData = []
        this.errorCount = 0
        this.loading = true
        // 遍历每一个单元格
        $trs.forEach((tr) => {
          const originalRowData = []
          const $tds = tr.querySelectorAll('td')
          $tds.forEach((td, index) => {
            originalRowData[index] = td.innerText
          })
          this.processRowData(originalRowData)
        })

        e.preventDefault()
        e.stopPropagation()
        this.$message.success('粘贴添加成功')
      } catch (e) {
        console.error(e)
        this.$message.error('粘贴失败，请检查excel数据')
      } finally {
        this.loading = false
      }
    },
    processRowData(originalRowData) {
      const rowData = {}
      this.fieldList.forEach((item, index) => {
        const validateResult =
          originalRowData[index] && item.validateFn ? item.validateFn(originalRowData[index]) : true
        if (!validateResult) this.errorCount = this.errorCount + 1
        rowData[item.attr] = {
          displayValue: validateResult ? originalRowData[index] : '数据错误',
          value:
            validateResult && originalRowData[index]
              ? item.processFn
                ? item.processFn(originalRowData[index])
                : originalRowData[index]
              : undefined,
          isError: !validateResult
        }
      })
      this.tableData.push(rowData)
    },
    handleConfirm() {
      if (!this.fieldList.length) {
        this.closeDialog()
        return
      }

      const clonedTableData = xbb.deepClone(this.tableData)
      const finalData = []
      clonedTableData.forEach((row) => {
        const finalRowData = {}

        this.mergeAddressField(row)
        Object.keys(row).forEach((key) => {
          finalRowData[key] = row[key].value
        })

        finalData.push(finalRowData)
      })

      console.table(finalData)
      this.$emit('paste-confirm', finalData, this.closeDialog)
    },
    closeDialog() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.fullscreen-btn {
  position: absolute;
  top: 20px;
  right: 50px;
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: none;
  outline: none;
  .fullscreen-icon {
    font-size: 12px;
    transition: color 0.3s;
  }
  &:hover {
    .fullscreen-icon {
      color: $brand-color-5;
    }
  }
}

.fullscreen-icon {
  color: #909399;
}

.footer-tips {
  margin-right: 4px;
  font-size: 14px;
  color: $text-main;
}

.is-error {
  color: $error-base-color-6;
}
:deep() {
  .el-dialog.is-fullscreen {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .el-dialog__body {
      display: flex;
      flex: 1;
      flex-direction: column;
      min-height: 0;
    }
  }
}
</style>
