<!-- eslint vue/return-in-computed-property: 1 -->

<template>
  <div v-if="!!visible && fieldInfo.fieldMapHidden !== 1" class="form-item subForm_label_position">
    <!-- :rules="roules" -->
    <FormItemLayout
      :key="fieldInfo.attr"
      class="form-item-base"
      :field-info="fieldInfo"
      :field-position="fieldPosition"
      :label="showLabel ? fieldInfo.attrName : ''"
      :memo="memo"
      :package-limit="fieldInfo.packageLimit"
      :prop="fieldInfo.attr"
      :rules="roules"
    >
      <SubForm
        v-model="formData[fieldInfo.attr]"
        :base-form-data="formData"
        :distributor-id="distributorId"
        :father-data="fatherData"
        :field-info="fieldInfo"
        :field-list="fieldList"
        :form-data="formData"
        :is-contract="isContract"
        :is-design="isDesign"
        :is-edit="isEdit"
        :is-price-edit="isPriceEdit"
        :is-retuen-edit="isRetuenEdit"
        :is-see="isSee"
        :is-sub-form-set-default="isSubFormSetDefault"
        mode="base"
        :no-add="noAdd"
        :readonly="readonly"
        :ref-id="refId"
        :ref-type="refType"
        :single="single"
        :source-form-id="sourceFormId"
      />

      <!-- 描述信息 -->
      <!-- <el-popover
        popper-class="field-memo__popper"
        placement="top"
        v-if="memo && fieldInfo.memo && mode !== 'sub' "
        trigger="hover"
        :content="fieldInfo.memo">
        <i slot="reference" class="el-icon-question field-memo-tooltip"></i>
      </el-popover> -->
    </FormItemLayout>
  </div>
</template>

<script>
/* eslint vue/return-in-computed-property: 1 */

import SubForm from './SubFormCopy'
// import fieldMixin from './../AllMixin'

import commonMixin from './../all-mixin-new/common'
import defaultValMixin from './../all-mixin-new/default-val'
import roulesMixin from './../all-mixin-new/roules'
import commonBusinessType from '@/constants/common/business-type'
import xbb from '@xbb/xbb-utils'

export default {
  // mixins: [fieldMixin],
  components: {
    SubForm
  },
  mixins: [commonMixin, defaultValMixin, roulesMixin],
  props: {
    // 价目表编辑
    isPriceEdit: Boolean,
    single: {
      // 是否是单条模式
      type: Boolean,
      default: false
    }
  },
  data: function () {
    return {
      noAdd: false
    }
  },
  computed: {
    // 经销商id，选产品价目表
    distributorId() {
      // 合同、订货单、报价单、销售机会
      if (
        this.fieldInfo.saasAttr === 'products' &&
        [
          commonBusinessType.QUOTATION,
          commonBusinessType.SALES_OPPORTUNITY,
          commonBusinessType.CONTRACT
        ].includes(this.businessType)
      ) {
        const attr =
          this.businessType === commonBusinessType.SALES_OPPORTUNITY ? 'text_3' : 'text_2'
        return xbb._get(this.formData, `${attr}[0].id`)
      }
    }
  },
  created() {
    this.formData[this.fieldInfo.attr] = this.formData[this.fieldInfo.attr] || []
  }
}
</script>

<style lang="scss">
// .subForm_label_position > .form-item-base > .el-form-item__content {
//   margin-left: 14px !important
// }
</style>
