<template>
  <!-- <div class="form-item" v-if="!!visible && fieldInfo.fieldMapHidden !==1"> -->
  <FormItemLayout
    v-if="!!visible"
    :field-info="fieldInfo"
    :field-position="fieldPosition"
    :label="showLabel ? label : ''"
    :memo="memo"
    :mode="mode"
    :package-limit="fieldInfo.packageLimit"
    :prop="prop"
    :rules="roules"
    :show-message="showMessage"
  >
    <InputTextArea
      ref="InputTextArea"
      v-model="formData[fieldInfo.attr]"
      :base-form-data="baseFormData"
      :field-info="fieldInfo"
      :form-data="formData"
      :is-see="isSee"
      :mode="mode"
      :readonly="readonly"
      :show-title="showTitle"
      @hasChanged="hasChanged"
      @inputTextChange="modelChange"
    />

    <!-- 描述信息 -->
    <!-- <el-popover
        popper-class="field-memo__popper"
        placement="top"
        v-if="memo && fieldInfo.memo && mode !== 'sub'"
        trigger="hover"
        :content="fieldInfo.memo">
        <i slot="reference" class="el-icon-question field-memo-tooltip"></i>
      </el-popover> -->
  </FormItemLayout>
  <!-- </div> -->
</template>

<script>
import InputTextArea from './InputTextArea'
// import defaultValue from './../AllMixin/defaultValue'

import commonMixin from './../all-mixin-new/common'
import defaultValMixin from './../all-mixin-new/default-val'
import roulesMixin from './../all-mixin-new/roules'

export default {
  components: {
    InputTextArea
  },
  mixins: [commonMixin, defaultValMixin, roulesMixin],

  methods: {
    hasChanged() {
      this.hasChange = true
    },
    modelChange() {
      // debugger
      // this.changeValidate()
    }
  }
}
</script>

<style lang="scss"></style>
