/*
 * @Description: 用来管理字段之间的通信
 */

const BUS = new Vue()
const eventType = '_change'
export default {
  /**
   * 添加订阅
   * eventName: string
   * cb fn
   */
  addChangeSub(eventName, cb) {
    BUS.$on(eventName + eventType, cb)
  },
  /**
   *
   * @param { string } eventName
   * @param  {...any} arg
   */
  triggerChange(eventName, val, oldVal) {
    BUS.$emit(eventName + eventType, val, oldVal)
  },
  /**
   *
   * @param { string } eventName
   */
  offEvent(eventName, cb) {
    BUS.$off(eventName + eventType, cb)
  }
}
