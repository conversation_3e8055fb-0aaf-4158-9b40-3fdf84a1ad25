<template>
  <div class="input-text">
    <!-- 等于 -->
    <template v-if="condition.symbol === 'equal' || condition.symbol === 'noequal'">
      <el-select
        :key="condition.symbol"
        v-model="modelVal"
        class="block-select"
        clearable
        :filter-method="remoteMethod"
        filterable
        multiple
        reserve-keyword
        size="mini"
        @visible-change="visibleChange"
      >
        <el-option v-for="item in options" :key="item.value" :label="item.text" :value="item.value">
          <!-- <el-checkbox :value="test.includes(item.value)">{{ item.text }}</el-checkbox> -->
        </el-option>
      </el-select>
    </template>

    <!-- 包含一个  全部包含 -->
    <template v-else-if="condition.symbol === 'include' || condition.symbol === 'allinclude'">
      <el-select
        :key="condition.symbol"
        v-model="modelVal"
        class="block-select"
        clearable
        :filter-method="remoteMethod"
        filterable
        multiple
        reserve-keyword
        size="mini"
        @visible-change="visibleChange"
      >
        <!-- <el-option key="all" value="all" disabled>
          <el-checkbox :value="false">{{ $t('operation.selectAll') }}</el-checkbox>
        </el-option>
        <el-option key="empty" value="empty" disabled>
          <el-checkbox :value="false">{{ $t('formDataEdit.notFilled') }}</el-checkbox>
        </el-option> -->
        <el-option v-for="item in options" :key="item.value" :label="item.text" :value="item.value">
          <!-- <el-checkbox :value="modelVal && modelVal.includes(item.value)">{{ item.text }}</el-checkbox> -->
        </el-option>
      </el-select>
    </template>

    <!-- 包含 -->
    <!-- <template v-else-if=" condition.symbol === 'include' || condition.symbol === 'noinclude' ">
      <el-input v-model="fieldValue" :placeholder="$t('placeholder.contentPls')"></el-input>
    </template> -->

    <!-- 为空 -->
    <template v-else-if="condition.symbol === 'empty' || condition.symbol === 'noempty'">
    </template>
  </div>
</template>

<script>
import mixin from './mixin'

export default {
  mixins: [mixin],
  inject: ['query', 'getConnectConditionFieldVal'],
  data() {
    return {
      fieldValue: '',
      options: []
      // remoteMethod: (val) => {
      //   this.get(val)
      // }
    }
  },
  computed: {
    modelVal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('modelChange', val)
      }
    }
  }
}
</script>
