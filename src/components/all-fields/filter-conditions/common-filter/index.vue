<!--
 * @Description: 筛选的常用框
 -->
<template>
  <div class="input-common">
    <!-- 等于 -->
    <template
      v-if="
        (condition.symbol === 'equal' || condition.symbol === 'noequal') && !isEqualSelectMutiple
      "
    >
      <el-cascader
        v-if="condition.saasAttr === 'currentPhase' && condition.attr === 'text_5'"
        v-model="midVal"
        clearable
        :options="specialOptions"
        :props="props"
        @change="handleCascaderChange"
        @visible-change="visibleChange"
      >
      </el-cascader>
      <el-select
        v-else
        :key="condition.symbol"
        ref="select"
        v-model="modelVal"
        v-lazyLabel="{
          value: modelVal,
          options: options,
          key: 'value'
        }"
        class="block-select"
        clearable
        :filterable="allowSearch"
        :placeholder="placeholder"
        remote
        :remote-method="remoteMethod"
        reserve-keyword
        @focus="openOptions"
        @keyup.enter.native="enterPush"
        @visible-change="visibleChange"
      >
        <div
          v-infinite-scroll="loadMore"
          infinite-scroll-delay="300"
          infinite-scroll-immediate="false"
        >
          <template v-for="item in filterOptions">
            <el-option
              v-if="item.value || item.value === 0"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </template>
        </div>
        <p v-if="isNomoreData" class="nomore_data">暂无更多数据</p>
      </el-select>
    </template>

    <!-- 等于任意一个值 -->
    <template
      v-else-if="condition.symbol === 'in' || condition.symbol === 'noin' || isEqualSelectMutiple"
    >
      <el-cascader
        v-if="condition.saasAttr === 'currentPhase' && condition.attr === 'text_5'"
        v-model="midVal"
        clearable
        collapse-tags
        :options="specialOptions"
        :props="multipleProps"
        @change="handleCascaderChange"
        @visible-change="visibleChange"
      >
      </el-cascader>
      <el-select
        v-else
        :key="condition.symbol"
        ref="select"
        v-model="modelVal"
        v-lazyLabel="{
          value: modelVal,
          options: options,
          key: 'value'
        }"
        class="block-select"
        clearable
        filterable
        multiple
        :placeholder="placeholder"
        remote
        :remote-method="remoteMethod"
        reserve-keyword
        @focus="openOptions"
        @keyup.enter.native="enterPush"
        @visible-change="visibleChange"
      >
        <div
          v-infinite-scroll="loadMore"
          infinite-scroll-delay="300"
          infinite-scroll-immediate="false"
        >
          <template v-for="item in filterOptions">
            <el-option
              v-if="item.value || item.value === 0"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </template>
        </div>
        <p v-if="isNomoreData" class="nomore_data">暂无更多数据</p>
      </el-select>
    </template>

    <!-- 包含 -->
    <template v-else-if="condition.symbol === 'like' || condition.symbol === 'nolike'">
      <el-input
        v-model="modelValCopy"
        clearable
        :placeholder="placeholder"
        @change="handleInputChange"
      ></el-input>
    </template>

    <!-- 包含一个  全部包含 -->
    <template v-else-if="condition.symbol === 'include' || condition.symbol === 'allinclude'">
      <el-select
        :key="condition.symbol"
        ref="select"
        v-model="modelVal"
        v-lazyLabel="{
          value: modelVal,
          options: options,
          key: 'value'
        }"
        class="block-select"
        clearable
        :default-first-option="true"
        filterable
        multiple
        :placeholder="placeholder"
        remote
        :remote-method="remoteMethod"
        reserve-keyword
        @focus="openOptions"
        @visible-change="visibleChange"
      >
        <div
          v-infinite-scroll="loadMore"
          infinite-scroll-delay="300"
          infinite-scroll-immediate="false"
        >
          <template v-for="item in filterOptions">
            <el-option v-if="item.value" :key="item.value" :label="item.text" :value="item.value">
              <!-- <el-checkbox :value="modelVal && modelVal.includes(item.value)">{{ item.text }}</el-checkbox> -->
            </el-option>
          </template>
        </div>
        <p v-if="isNomoreData" class="nomore_data">暂无更多数据</p>
      </el-select>
    </template>

    <!-- 为空 -->
    <template
      v-else-if="
        condition.symbol === 'empty' || condition.symbol === 'noempty' || condition.symbol === 'all'
      "
    >
      <el-input :placeholder="placeholder" readonly></el-input>
    </template>
  </div>
</template>

<script>
import mixin from '../mixin'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'CommonFilter',
  mixins: [mixin],
  inject: ['query', 'getConnectConditionFieldVal'],
  model: {
    prop: 'value',
    event: 'modelChange'
  },
  data() {
    return {
      // modelVal: '',
      selectLoading: false,
      options: [],
      specialOptions: [],
      isPushEnter: false,
      selectItems: [],
      props: {
        value: 'nodeId',
        label: 'nodeName',
        children: 'nodeList'
      },
      multipleProps: {
        value: 'nodeId',
        label: 'nodeName',
        children: 'nodeList',
        multiple: true
      },
      midVal: [],
      lastSelectedValue: null,
      currentPage: 1
    }
  },
  computed: {
    filterOptions() {
      return this.options.length ? this.options : this.selectItems
    },
    // 诗选是否支持滚动加载
    isEnableScroll() {
      return [3, 9, 10000, 10001, 10030, 10008, 10032].includes(this.condition.fieldType)
    },
    // 是否是关联数据的筛选框，支持回车搜索交互的优化
    isLinkData() {
      return [20001, 20002, 10008].includes(this.condition.fieldType)
    },
    // 有些字段不允许手动输入搜索，比如生日
    allowSearch() {
      return ![37].includes(this.condition.fieldType)
    },
    // 复选框组、下拉多选、成员多选、部门多选、负责人、协同人的等于不等于条件要支持多选
    isEqualSelectMutiple() {
      const isMutiple =
        [9, 10001, 10010, 10012, 10017, 10018].includes(this.condition.fieldType) &&
        ['equal', 'noequal'].includes(this.condition.symbol)
      return isMutiple
    },
    modelVal: {
      get() {
        let val = this.value

        if (
          (['in', 'noin', 'include', 'allinclude'].includes(this.condition.symbol) ||
            this.isEqualSelectMutiple) &&
          this.value &&
          this.value.length
        ) {
          val = this.value.map((i) => {
            if (Object.prototype.toString.call(i) === '[object Object]') {
              return i.id
            } else {
              return i
            }
          })
        }
        // 由于后端在设置分组筛选条件的时候，筛选会回显，回显的是数组，导致这里需要处理成字符串
        if (
          ['equal', 'noequal', 'like', 'nolike'].includes(this.condition.symbol) &&
          !this.isEqualSelectMutiple
        ) {
          Array.isArray(val) && val.length && (val = val[0])
          if (Object.prototype.toString.call(val) === '[object Object]') {
            let isBoolean = true
            this.options.forEach((item) => {
              if (item.value === val.id) {
                isBoolean = false
              }
            })
            if (isBoolean) {
              // eslint-disable-next-line vue/no-side-effects-in-computed-properties
              this.options.push({ text: val.name, value: val.id })
            }
            val = val.id ? val.id : val
          }
        }
        return val
      },
      set(val) {
        // 清空值的时候，同步清空选中的值
        if (!val) {
          this.condition.selectItems = []
          this.selectItems = []
        }
        // 这里因为options是会分页，所以不能传出去
        // 等于不等于，下拉的时，有option
        if (/^(equal|noequal)$/.test(this.condition.symbol) && !this.isEqualSelectMutiple) {
          this.options.forEach((item) => {
            if (item.value === val) {
              this.condition.text = [item.text]
              this.condition.selectItems = [item]
            }
          })
          // 包含不包含，普通输入文本框
        } else if (/^(like|nolike)$/.test(this.condition.symbol)) {
          this.condition.text = [val]
          // 等于任意一个，多选
        } else if (
          /^(in|noin|include|allinclude)$/.test(this.condition.symbol) ||
          this.isEqualSelectMutiple
        ) {
          const tempArr = this.condition.selectItems || []
          this.options.forEach((item) => {
            if (val.includes(item.value)) {
              tempArr.push(item) // 将选中的对象添加到已选数组中
            }
          })
          // 去重
          this.condition.selectItems = xbb
            .noRepeatArray(tempArr, 'value')
            .filter((item) => val.includes(item.value))
          // 生成回显文本
          this.condition.text = this.condition.selectItems.map((item) => item.text)
        }

        if (/^(like|nolike)$/.test(this.condition.symbol)) {
          // 包含不包含的时候需要防抖
          // xbb.debounce(() => this.$emit('modelChange', val))()
          this.$emit('modelChange', val)
        }
        this.$emit('modelChange', val)
      }
    }
  },

  watch: {
    condition: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val.selectItems && val.selectItems.length) {
          this.selectItems = [...val.selectItems]
        }
      }
    },
    inputValue(val) {
      this.currentPage = 1
    }
  },

  created() {
    this.optionsInit()
  },
  methods: {
    openOptions() {
      this.currentPage = 1
    },
    loadMore() {
      if (!this.isEnableScroll) return
      // 获取滚动元素，防止多次触发接口
      let el = null
      if (['equal', 'noequal'].includes(this.condition.symbol)) {
        el = this.$refs.select.$children[1].$children[0].$el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
      } else {
        el = this.$refs.select.$children[2].$children[0].$el.querySelector(
          '.el-select-dropdown .el-select-dropdown__wrap'
        )
      }
      if (this.isNomoreData || el.scrollTop === 0) return
      this.currentPage += 1
      this.remoteMethod(this.inputValue, this.currentPage)
    },
    optionsInit() {
      const getArr = () => {
        if (this.condition.valueItems) {
          return this.condition.valueItems.map((element) => {
            return {
              text: element.text,
              value: element.value
            }
          })
        } else if (Array.isArray(this.value)) {
          return this.value
            .filter((item) => item)
            .map((element, index) => {
              if (typeof element === 'object') {
                return {
                  text: element.name,
                  value: element.id
                }
              }
              return {
                text: this.condition.text?.[index] || element,
                value: element
              }
            })
        }
        return []
      }
      this.options = [...this.options, ...getArr()]
    },
    // 输入下拉框的搜索,禁止回车选择第一个搜索.回车操作改为输入后按回车直接搜索输入的内容
    enterPush() {
      // 只有关联数据的下拉输入搜索框支持回车的交互优化
      // if (!this.isLinkData) {
      //   return false
      // }
      if (this.inputValue === '') {
        return false
      }
      // 单选
      if (/^(equal|noequal)$/.test(this.condition.symbol)) {
        let find = false
        // 如果输入的内容存在于下拉的选项中
        for (let i = 0; i < this.options.length; i++) {
          if (this.options[i].text === this.inputValue) {
            this.modelVal = this.options[i].value
            find = true
            break
          }
        }
        // 不存在下拉选项中则直接查询当前输入的内容（实际就是返回空的查询结果）
        if (!find) {
          this.condition.text = [this.inputValue]
          this.condition.selectItems = [{ text: this.inputValue, value: this.inputValue }]
          this.$emit('modelChange', this.inputValue)
        }
      } else if (/^(in|noin|include|allinclude)$/.test(this.condition.symbol)) {
        // 多选
        let selected = this.modelVal || []
        let find = false
        // 如果输入的内容存在于options选项中
        for (let i = 0; i < this.options.length; i++) {
          if (this.options[i].text === this.inputValue) {
            // 重复输入，如果modelVal中已经存在，则取消勾选，不存在则push
            selected.includes(this.options[i].value)
              ? (selected = this.deleteRepeat(selected, this.options[i].value))
              : selected.push(this.options[i].value)
            this.modelVal = selected
            find = true
            break
          }
        }
        // 如果不存在于options
        if (!find) {
          selected.push(this.inputValue)
          // 去重目的是防止重复输入
          this.modelVal = Array.from(new Set(selected))
        }
      }
    },
    // 删除重复
    deleteRepeat(array, value) {
      const arr = array
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] === value) {
          arr.splice(i, 1)
          i--
        }
      }
      return arr
    },
    handleCascaderChange(value, selectedData) {
      // 这里value值会是[null,xxxx]的格式,手动去掉null
      // 有可能会有嵌套一层的情况,拍平
      this.modelVal = [].concat(...this.removeNulls(value))
    },
    // 去除数组中所有的null
    removeNulls(arr) {
      // 使用map方法遍历数组，并对每个元素应用过滤逻辑
      // 对于数组元素，递归调用removeNulls以处理嵌套数组
      return arr
        .map((item) => {
          if (Array.isArray(item)) {
            // 若当前元素是数组，则递归调用自身处理该数组
            return this.removeNulls(item)
          }
          // 明确返回非null的项
          // 对于null显式返回null（此行对null的处理无意义,只是为了过eslint校验）
          return item !== null ? item : null
        })
        .filter(Boolean) // 最后使用filter(Boolean)进一步过滤掉undefined和false，确保数组中没有空洞
    }
  }
}
</script>

<style lang="scss">
.input-common {
  .el-select__tags {
    flex-wrap: nowrap;
    overflow: auto;
    & > span {
      display: flex;
    }
  }
  .el-select__input {
    min-width: 10px;
  }
}
.nomore_data {
  padding: 10px 0px;
  font-size: 14px;
  color: $text-plain;
  text-align: center;
}
</style>
