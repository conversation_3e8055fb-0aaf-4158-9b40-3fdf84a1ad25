<!--
 * @Description: 省市区的选择控件
 -->

<template>
  <div class="input-address-select">
    <!-- 等于不等于 -->
    <div v-show="condition.symbol === 'equal' || condition.symbol === 'noequal'">
      <el-cascader
        :key="condition.symbol"
        change-on-select
        class="block-select"
        clearable
        :options="cityData"
        :placeholder="placeholder"
        :props="{ label: 'text', value: 'text' }"
        :value="modelVal"
        @change="addressChange"
      >
      </el-cascader>
    </div>
    <!-- 包含 -->
    <template v-if="condition.symbol === 'like' || condition.symbol === 'nolike'">
      <el-input
        v-model="modelValCopy"
        clearable
        :placeholder="placeholder"
        @change="handleInputChange"
      ></el-input>
    </template>
    <!-- 为空 -->
    <template
      v-else-if="
        condition.symbol === 'empty' || condition.symbol === 'noempty' || condition.symbol === 'all'
      "
    >
      <el-input :placeholder="placeholder" readonly></el-input>
    </template>
  </div>
</template>

<script>
import mixin from '../mixin'
import xbb from '@xbb/xbb-utils'
import tempVue from '@/utils/temp-vue'

export default {
  name: 'InputAddressSelect',
  mixins: [mixin],
  // inject: ['query', 'getConnectConditionFieldVal', 'distributorMapObj'],
  inject: {
    query: { default: () => ({}) },
    getConnectConditionFieldVal: { default: () => null },
    distributorMapObj: { default: () => ({}) } // 经销商档案列表页provide了
  },
  model: {
    prop: 'value',
    event: 'modelChange'
  },

  data() {
    return {
      cityData: [] // 省市区数据源
    }
  },

  computed: {
    modelVal: {
      get() {
        let val
        // 由于后端在设置分组筛选条件的时候，筛选会回显，回显的是数组，导致这里需要处理成字符串
        // if (['equal', 'noequal', 'like', 'nolike'].includes(this.condition.symbol)) {
        //   Array.isArray(val) && val.length && (val = val[0])
        // }
        if (/^(like|nolike)$/.test(this.condition.symbol)) {
          // 包含不包含的时候不是数组类型
          val = this.value
        } else {
          // 级联的时候需要数组
          val = (Array.isArray(this.value) && this.value) || []
        }
        return val
      },
      set(val) {
        // 等于不等于，下拉的时，有option
        if (/^(equal|noequal)$/.test(this.condition.symbol)) {
          this.condition.text = val.join('/')
          this.condition.selectItems = val
          // 包含不包含，普通输入文本框
        } else if (/^(like|nolike)$/.test(this.condition.symbol)) {
          this.condition.text = [val]
          // 等于任意一个，多选
        }
        if (/^(like|nolike)$/.test(this.condition.symbol)) {
          // 包含不包含的时候需要防抖
          this.modelValEmitDebounce(val)
        } else {
          this.$emit('modelChange', val)
        }
      }
    }
  },
  created() {
    this.init()
  },

  methods: {
    @xbb.debounceWrap()
    modelValEmitDebounce(val) {
      this.$emit('modelChange', val)
    },
    async init() {
      // 调用utils方法得到省市区
      this.cityData = await utils.getCityData()
    },
    addressChange(val) {
      this.modelVal = val
    }
  },
  watch: {
    'distributorMapObj.distributorAddress': {
      handler(val, oldVal) {
        if (this.query.businessType === 100 && this.query.distributorMark === 1) {
          // 经销商档案特殊逻辑:仅处理“等于”的情况
          if (
            Object.prototype.toString.call(this.modelVal) === '[object Array]' &&
            this.condition.symbol !== 'equal' &&
            JSON.stringify(val) !== JSON.stringify(oldVal)
          )
            return
          tempVue.$emit('distributorClickMap')
          this.condition.selectItems = val
          this.$emit('modelChange', val)
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.input-address-select {
  position: relative;
  width: 100%;
  .block-select {
    position: relative;
  }
}
</style>
