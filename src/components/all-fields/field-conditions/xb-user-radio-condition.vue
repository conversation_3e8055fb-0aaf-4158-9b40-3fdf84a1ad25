<!--
 * @Description:
 -->
<template>
  <div class="xb-user-radio-condition">
    <ConditionLayout
      v-model="itemCondition.symbol"
      :disabled="disabled"
      :field-name="itemCondition.subName || itemCondition.name"
      :symbol-list="whereList"
      @deleteCondition="deleteCondition"
    >
      <template slot-scope="{ symbolVal }">
        <div :key="symbolVal">
          <!-- 等于 -->
          <template v-if="/^(equal|noequal)$/.test(symbolVal)">
            <el-select
              v-model="strValue"
              v-lazyLabel="{
                value: strValue,
                options: options,
                key: 'value'
              }"
              :disabled="disabled"
              :filter-method="filterMethod"
              filterable
              :placeholder="$t('placeholder.choosePls', { attr: '' })"
              style="width: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
          <template v-else-if="/^(in|noin|include|allinclude)$/.test(symbolVal)">
            <el-select
              v-model="arrValue"
              v-lazyLabel="{
                value: strValue,
                options: options,
                key: 'value'
              }"
              :disabled="disabled"
              :filter-method="filterMethod"
              filterable
              multiple
              :placeholder="$t('placeholder.choosePls', { attr: '' })"
              style="width: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </div>
      </template>
    </ConditionLayout>
  </div>
</template>

<script>
import mixin from './mixin'

export default {
  name: 'XbUserRadioCondition',

  mixins: [mixin]
}
</script>
