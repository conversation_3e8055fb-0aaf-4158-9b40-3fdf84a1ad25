<!--
 * @Description: 数组过滤条件
 -->
<template>
  <div class="xb-number-condition">
    <ConditionLayout
      v-model="itemCondition.symbol"
      :disabled="disabled"
      :field-name="itemCondition.subName || itemCondition.name"
      :symbol-list="whereList"
      @deleteCondition="deleteCondition"
    >
      <template slot-scope="{ symbolVal }">
        <div :key="symbolVal">
          <!-- 等于 -->
          <template
            v-if="/^(equal|noequal|greaterequal|greatethan|lessequal|lessthan)$/.test(symbolVal)"
          >
            <el-input-number
              v-model="numValue"
              class="left-align"
              :controls="false"
              :disabled="disabled"
              :max="numberLimit.max"
              :min="numberLimit.min"
              style="width: 100%"
            ></el-input-number>
          </template>
          <!-- 包含 -->
          <template v-else-if="/^(range)$/.test(symbolVal)">
            <div class="_rang">
              <el-input-number
                v-model="rangeValueStart"
                :controls="false"
                :disabled="disabled"
                :max="numberLimit.max"
                :min="numberLimit.min"
                style="flex: 1"
              ></el-input-number>
              <span class="_text">~</span>
              <el-input-number
                v-model="rangeValueEnd"
                :controls="false"
                :disabled="disabled"
                :max="numberLimit.max"
                :min="numberLimit.min"
                style="flex: 1"
              ></el-input-number>
            </div>
          </template>
        </div>
      </template>
    </ConditionLayout>
  </div>
</template>

<script>
import mixin from './mixin'

const MAX = 100000000000
const MIN = -100000000000

export default {
  name: 'XbNumberCondition',

  mixins: [mixin],

  data() {
    return {
      isDate: false
    }
  },

  computed: {
    numberLimit() {
      return {
        max: MAX,
        min: MIN
      }
    },
    noSearch() {
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.xb-number-condition {
  ._rang {
    display: flex;
    align-items: center;
    ._text {
      flex-shrink: 0;
      width: 27px;
      text-align: center;
    }
  }
  .left-align {
    :deep(input) {
      text-align: left;
    }
  }
}
</style>
