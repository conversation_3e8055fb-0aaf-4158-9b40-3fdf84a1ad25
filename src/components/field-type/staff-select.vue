<!--
 * @Description: 员工选择控件
 -->

<!--
  员工选择控件
  prop:
    defaultValue: 默认值
    placeholder: 默认为选择员工
    multiple: 是否多选
  emit:
    change: 选中后触发change事件
 -->

<template>
  <div class="staff-select">
    <el-select
      v-model="staff"
      :disabled="disabled"
      filterable
      :loading="loading"
      multiple
      :multiple-limit="multiple ? 0 : 1"
      :placeholder="placeholder"
      remote
      :remote-method="remoteMethod"
      value-key="id"
      @change="handleChange"
    >
      <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item">
        <span style="float: left" v-html="addNodeByWX(item.name)"></span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import i18n from '@/lang'
import { userList } from '@/api/system.js'

export default {
  name: 'StaffSelect',

  props: {
    defaultValue: {
      type: Array,
      default: () => []
    },

    placeholder: {
      type: String,
      default: i18n.t('authoritySettting.pleaseSelectEmployees')
    },

    multiple: {
      type: Boolean,
      default: false
    },

    disabled: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      userList: [],
      staff: [],
      loading: false
    }
  },

  watch: {
    defaultValue: {
      handler(val) {
        this.staff = val
      },
      deep: true
    }
  },

  created() {
    this.staff = JSON.parse(JSON.stringify(this.defaultValue))
    // console.log(this.staff)
    this.getUserList()
  },

  methods: {
    // 远程搜索
    remoteMethod(query) {
      this.loading = true
      this.getUserList(query)
    },

    // 获取员工数据
    getUserList(query, userIds) {
      // 对userIds做兼容处理
      if (userIds) {
        userIds = userIds.map((item) => {
          if (typeof item === 'object') {
            return item.id
          } else {
            return item
          }
        })
      }
      const params = {
        nameLike: query || '',
        userIds: userIds
      }
      userList(params)
        .then((data) => {
          this.loading = false
          const selectIds = this.staff.map((item) => {
            return item.id
          })
          this.userList = data.result.userList
            .filter((item) => selectIds.indexOf(item.userId) === -1)
            .map((item) => {
              return {
                editable: 1,
                id: item.userId,
                name: item.name,
                property: 'user'
              }
            })
          this.userList = this.userList.concat(this.staff)
          // 兼容处理，重新获取所有员工列表
          if (userIds && userIds.length) {
            this.getUserList()
          }
        })
        .catch(() => {})
    },

    handleChange(value) {
      this.$emit('update:defaultValue', value)
      this.$emit('change', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-select {
  .el-select__tags-text {
    display: inline-block;
    max-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .el-tag__close {
    top: -5px !important;
  }
}
</style>
