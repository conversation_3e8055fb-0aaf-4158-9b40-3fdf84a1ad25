<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @ Author: ch<PERSON><PERSON><PERSON>
 * @ Create Time: 2020-08-12 09:40:26
 * @ Modified by: ch<PERSON><PERSON><PERSON>
 * @ Modified time: 2020-09-15 16:43:02
 * @ Description: 外链背景样式修改
 -->

<template>
  <div class="outer-style">
    <div class="setting-content">
      <div class="setting-title">
        <span>背景</span>
      </div>
      <div class="setting-bg">
        <div class="setting-left">
          <el-select v-model="bgValue" placeholder="请选择">
            <el-option
              v-for="item in bgOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="setting-right">
          <el-color-picker v-if="bgValue === 1" v-model="bg_color" show-alpha></el-color-picker>
          <el-upload
            v-else
            ref="upload"
            :accept="imgAccept"
            :action="uploadUlr"
            :before-upload="handleBeforeUpload"
            class="avatar-uploader"
            :data="uploadData"
            :on-error="handleError"
            :on-success="upScuccess"
            :show-file-list="false"
          >
            <div><i class="avatar-uploader-icon el-icon-plus"></i></div>
            <div class="el-upload__text">上传</div>
          </el-upload>
        </div>
      </div>
    </div>
    <div class="setting-content">
      <div class="setting-title">
        <span>文字</span>
      </div>
      <div class="setting-bg">
        <div class="setting-left">
          <el-select v-model="configInfo.fontSize" placeholder="请选择">
            <el-option
              v-for="item in fontOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="setting-right">
          <el-color-picker v-model="configInfo.fontColor" show-alpha></el-color-picker>
        </div>
      </div>
    </div>
    <div class="setting-content">
      <div class="setting-title">
        <span>标题位置</span>
      </div>
      <div class="setting-bg">
        <div class="setting-center">
          <div v-for="item in 3" :key="item" class="setting-lable">
            <div
              v-for="lines in 3"
              :key="lines"
              class="setting-line"
              :class="[{ hoverSetting: configInfo.position == `${item}${lines}` }]"
              @click="positionClik(`${item}${lines}`)"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import uploadMixin from '@/mixin/uploadMixin'

export default {
  name: 'OuterStyle',
  mixins: [uploadMixin],
  props: {
    configInfo: {
      type: Object,
      default: () => {
        return {
          fontSize: 20,
          fontColor: '',
          position: 0,
          bgColor: '',
          bgUrl: ''
        }
      }
    },
    themeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    const textSize = () => {
      const sizeArr = []
      for (let i = 14; i < 30; i += 2) {
        sizeArr.push({
          value: i + '',
          label: i
        })
      }
      return sizeArr
    }
    return {
      // 背景
      bgOptions: [
        {
          value: 0,
          label: '图片'
        },
        {
          value: 1,
          label: '颜色'
        }
      ],
      bgValue: 0,
      // // 字体
      fontOptions: textSize()
    }
  },
  computed: {
    bg_color: {
      set(val) {
        this.configInfo.bgUrl = ''
        this.configInfo.bgColor = val
        if (this.themeList.length) this.$emit('clearThemeList')
      },
      get() {
        return this.configInfo.bgColor
      }
    }
  },
  watch: {
    'configInfo.bgUrl': {
      handler(newV) {
        this.bgValue = newV.length ? 0 : 1
      }
    }
  },
  methods: {
    upScuccess(response, file) {
      const newImg = this.fileUrl[file.uid] + '_1080xauto.jpg'
      this.configInfo.bgUrl = newImg
      this.configInfo.bgColor = ''
      this.$emit('clearThemeList')
    },
    // 位置点击
    positionClik(item) {
      this.configInfo.position = +item
    }
  }
}
</script>

<style lang="scss" scoped>
.outer-style {
  display: flex;
  flex-direction: column;
  padding: 10px 20px;
  .setting-content {
    padding-bottom: 20px;
    .setting-title {
      padding-bottom: 10px;
      span {
        font-size: 14px;
        color: $text-main;
      }
    }
    .setting-bg {
      display: flex;
      .setting-center {
        display: flex;
        flex-direction: column;
        width: 100%;
        border: 1px solid $neutral-color-3;
        border-radius: 6px;
        .setting-lable {
          display: flex;
          justify-content: space-between;
          padding: 10px;
          .setting-line {
            width: 15%;
            height: 10px;
            background-color: rgba(255, 142, 61, 0.2);
            border: 1px solid white;
            &:focus,
            &:hover {
              border: 1px solid $brand-color-5;
            }
          }
          .hoverSetting {
            border: 1px solid $brand-color-5;
          }
        }
      }
      .setting-left {
        width: 65%;
      }
      .setting-right {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        padding-left: 10px;
      }
    }
  }
}
:deep(.el-color-picker) {
  width: 100%;
  .el-color-picker__trigger {
    width: 100%;
    .el-color-picker__color {
      border: none;
    }
    .el-icon-close {
      display: none;
    }
  }
}

:deep(.avatar-uploader) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 1px solid $neutral-color-3;
  border-radius: 6px;
  .el-upload--text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    &:focus,
    &:hover {
      color: $brand-color-5;
    }
    .avatar-uploader-icon {
      font-size: 12px;
      color: #8c939d;
      text-align: center;
    }
  }
}
</style>
