<!-- eslint vue/no-mutating-props: 1 -->

/* * @Description: 删除表单弹窗 */

<template>
  <el-dialog :before-close="cancel" class="delete-box" :visible.sync="isActive" width="620px">
    <span slot="title">
      <div class="delete-title">{{ $t('deleteFormDialog.m3', { attr: item.name }) }}</div>
    </span>
    <div class="warning">
      <i class="el-icon-warning"></i>
    </div>
    <div v-if="dataCount" class="alert-body">
      <div>{{ $t('deleteFormDialog.m4') }}</div>
      <div>{{ $t('deleteFormDialog.m1') }}</div>
      <el-input v-model="formTitle"></el-input>
    </div>
    <div v-else class="alert-body">
      <div class="deleteAppText">
        {{ $t('deleteFormDialog.m5') }}
      </div>
      <div class="deleteAppText">{{ $t('deleteFormDialog.m2') }}</div>
      <el-input
        v-model="formTitle"
        class="inputStyle"
        :placeholder="$t('placeholder.inputPls', { attr: $t('nouns.applicationName') })"
      ></el-input>
    </div>
    <span slot="footer">
      <el-button @click="cancel">{{ $t('operation.cancel') }} </el-button>
      <el-button :disabled="disabled" type="primary" @click="submit">{{
        $t('operation.delete')
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

export default {
  name: 'DeleteFormDialog',
  props: {
    isActive: {
      type: Boolean,
      default: false
    },
    item: Object,
    dataCount: Number
  },
  data() {
    return {
      formTitle: '',
      disabled: true
    }
  },
  watch: {
    formTitle(val) {
      if (val === this.item.name) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    }
  },

  methods: {
    cancel() {
      this.$emit('dialogCancel')
    },
    submit() {
      this.$emit('dialogDeleteFormSubmit')
    },
    init() {
      this.formTitle = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.delete-title {
  padding-right: 10px;
  font-size: 18px;
  color: $text-main;
}
.warning {
  display: inline-block;
  width: 24px;
  height: 24px;
  font-size: 24px;
  color: $warning-color-5;
  vertical-align: top;
}
.alert-body {
  display: inline-block;
  max-width: 450px;
  margin-left: 15px;
  font-size: 14px;
}
.deleteAppText {
  margin-bottom: 20px;
  color: $text-plain;
}
.inputStyle {
  width: 100%;
}
.el-icon-warning {
  font-size: 24px;
  color: $warning-color-5;
}
</style>

<style lang="scss">
.delete-box {
  .el-dialog {
    .el-dialog__header {
      border: none;
    }
  }
}
</style>
