<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 背景设置（颜色&图片）
-->
<template>
  <div class="background-setting">
    <div class="flex-layout">
      <div class="opertion-box">
        <!-- 颜色设置 -->
        <div>
          <!-- 预设颜色弹窗 -->
          <el-popover offset="30" placement="bottom" trigger="click" width="270">
            <div class="preview-box">
              <div class="title">
                {{ $t('dataWarn.addWarnData.presetColors') }}
              </div>
              <div class="preview-color-list">
                <div
                  v-for="(color, index) in previewColorList"
                  :key="index"
                  class="color"
                  :style="colorBoxStyle(color)"
                  @click="selectColor(color)"
                ></div>
              </div>
              <div class="title">
                {{ $t('dataWarn.addWarnData.customColors') }}
              </div>
              <div class="preview-color-list">
                <div class="color">
                  <i class="el-icon-plus icon-plus"></i>
                  <div class="cunstom">
                    <el-color-picker @change="customColorChange"></el-color-picker>
                  </div>
                </div>
                <div
                  v-for="(color, index) in customColors"
                  :key="index"
                  class="color"
                  :style="colorBoxStyle(color)"
                  @click="selectColor(color)"
                ></div>
              </div>
            </div>
            <div slot="reference" class="color-box" :style="{ background: backgroundValue }">
              <!-- 当颜色为diff时，显示 ··· -->
              <div v-if="backgroundValue === 'diff'" class="diff-mask">
                <i class="el-icon-more"></i>
              </div>
              <!-- 右侧复原颜色按钮 -->
              <div class="resetBox" @click.stop="resetColorHandler">
                <i class="delete-icon el-icon-delete"></i>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

// 最多可以存储自定义颜色的数量
import mixin from '@/mixin/uploadMixin'

const MAX_CUSTOM_COLOR = 10

export default {
  mixins: [mixin],
  props: {
    // 颜色色值/图片地址
    background: String,
    // 自定义的颜色
    customColors: Array,
    // 可还原到的颜色
    resetColor: String
  },
  data() {
    return {
      maxCustomColorCount: MAX_CUSTOM_COLOR,
      previewColorList: ['#FF8C2E', '#F7716C', '#29DAE0', '#FFC849', '#4DACFF', '#5776FF'] // 预设颜色
    }
  },
  computed: {
    backgroundValue: {
      get() {
        return this.background
      },
      set(val) {
        // 避免重复点击当前已选
        if (this.background === val) {
          return
        }
        this.$emit('update:background', val)
      }
    }
  },
  created() {},
  methods: {
    // 颜色显示，已选颜色标记
    colorBoxStyle(color) {
      const background = color
      const boxshadow = `0 0 4px 1px ${color}`
      return {
        background: background,
        'box-shadow': this.backgroundValue === color ? boxshadow : 'none'
      }
    },
    selectColor(color) {
      this.backgroundValue = color
    },
    // 添加自定义的颜色
    customColorChange(color) {
      if (this.customColors.length > this.maxCustomColorCount - 1) {
        this.customColors.shift()
      }
      this.$nextTick(() => {
        this.customColors.push(color)
      })
    },
    // 还原至当前风格的配色
    resetColorHandler() {
      // 根据风格方案还原风格对应的颜色
      if (!this.resetColor) {
        return
      }
      this.backgroundValue = ''
      this.$nextTick(() => {
        this.backgroundValue = this.resetColor
      })
    }
  }
}
</script>

<style lang="scss">
.picture-setting {
  .el-upload--picture-card {
    width: 90px;
    height: 27px;
    font-size: 12px;
    line-height: 27px;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
    .picture-icon {
      font-size: 15px;
      color: $brand-color-5;
    }
  }
  .el-upload__tip {
    margin-top: 10px;
    color: $neutral-color-4;
  }
}
</style>

<style lang="scss" scoped>
.background-setting {
  margin-bottom: 15px;
  transition: all 0.4s;
  .flex-layout {
    display: flex;
    font-size: 14px;
    color: $text-plain;
    .title-box {
      width: 70px;
    }
    .line-height-30 {
      line-height: 30px;
    }
    .opertion-box {
      flex: 1;
      .radio-box {
        margin-bottom: 10px;
      }
      .color-box {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: 30px;
        cursor: pointer;
        border: 2px solid $base-white;
        box-shadow: 0 0 0 2px $neutral-color-3;
        .diff-mask {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 10;
          width: 100%;
          height: 26px;
          font-size: 16px;
          line-height: 26px;
          color: $text-auxiliary;
          text-align: center;
          background: $base-white;
        }
        .resetBox {
          position: absolute;
          top: 1px;
          right: 1px;
          width: 24px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: $base-white;
          border-radius: 2px;
          box-shadow: 0 0 3px 0px $neutral-color-3;
          opacity: 0;
          transition: all 0.3s;
          .delete-icon {
            font-size: 14px;
          }
        }
        &:hover .resetBox {
          opacity: 1;
        }
      }
      .cover-setting {
        display: flex;
        margin-top: 10px;
        .title {
          width: 150px;
          font-size: 13px;
          line-height: 32px;
        }
      }
    }
  }
}
.preview-box {
  box-sizing: border-box;
  .title {
    font-size: 13px;
    line-height: 30px;
    color: $text-auxiliary;
  }
  .preview-color-list {
    display: flex;
    flex-wrap: wrap;
    .color {
      position: relative;
      box-sizing: border-box;
      width: 36px;
      height: 36px;
      margin-right: 10px;
      margin-bottom: 10px;
      text-align: center;
      cursor: pointer;
      border: 1px solid $neutral-color-3;
      transition: all 0.3s;
      animation: transform 0.4s;
    }
    .icon-plus {
      font-size: 18px;
      line-height: 35px;
      color: $neutral-color-3;
      pointer-events: none;
    }
    .cunstom {
      position: absolute;
      top: 0;
      opacity: 0;
    }
    .color:nth-child(6) {
      margin-right: 0;
    }
    @keyframes transform {
      from {
        opacity: 0;
        transform: scale(0.3);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }
  }
}
</style>
