<!-- 颜色选择弹窗 -->
<template>
  <el-popover
    class="color-popover"
    :disabled="readOnly"
    offset="30"
    placement="bottom"
    trigger="click"
    :width="popoverWidth"
    @show="handleShowColorSelect"
  >
    <div class="preview-box">
      <div class="title">
        {{ $t('dataWarn.addWarnData.presetColors') }}
      </div>
      <div class="preview-color-list">
        <div
          v-for="(color, index) in previewColor"
          :key="index"
          class="color"
          :style="colorBoxStyle(color)"
          @click="selectColor(color)"
        ></div>
      </div>
      <div class="title">
        {{ $t('dataWarn.addWarnData.customColors') }}
      </div>
      <div class="preview-color-list">
        <div class="color">
          <i class="el-icon-plus icon-plus"></i>
          <div class="custom">
            <el-color-picker @change="customColorChange"></el-color-picker>
          </div>
        </div>
        <div
          v-for="(color, index) in customColorsList"
          :key="index"
          class="color"
          :style="colorBoxStyle(color)"
          @click="selectColor(color)"
        ></div>
      </div>
    </div>
    <div slot="reference" class="color-box" :class="{ 'read-only': readOnly }" :style="inputStyle">
      <span class="color-box__inner" :style="{ background: colorValue }"></span>
      <!-- 右侧复原颜色按钮 -->
      <div v-if="!readOnly && showDelete" class="reset-box" @click.stop="resetColorHandler">
        <i class="delete-icon el-icon-delete"></i>
      </div>
    </div>
  </el-popover>
</template>

<script>
export default {
  name: 'ColorPopover',

  components: {},

  model: {
    prop: 'value',
    event: 'model-change'
  },

  props: {
    value: {
      // 选择的颜色
      type: String,
      default: ''
    },
    previewColor: {
      type: Array,
      default: () => ['#FF8C2E', '#F7716C', '#29DAE0', '#FFC849', '#4DACFF', '#5776FF']
    },
    customColors: {
      // 自定义的颜色
      type: Array,
      default: () => []
    },
    maxCustomColorCount: {
      // 自定义颜色上限
      type: Number,
      default: -1
    },
    resetColor: {
      type: String,
      default: ''
    },
    // 默认值
    defaultColor: {
      type: String,
      default: ''
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    showDelete: {
      type: Boolean,
      default: true
    },
    inputStyle: {
      type: Object
    },
    popoverWidth: {
      type: [Number, String],
      default: 270
    }
  },

  data() {
    return {}
  },

  computed: {
    colorValue: {
      // 选中的颜色
      get() {
        return this.value
      },
      set(val) {
        if (this.value === val) return
        this.$emit('model-change', val)
      }
    },
    customColorsList: {
      get() {
        return this.customColors || []
      },
      set(val) {
        this.$emit('update:customColors', val)
      }
    }
  },

  mounted() {
    this.setDefaultColor()
  },

  methods: {
    // 设置默认颜色
    setDefaultColor() {
      if (!this.colorValue) {
        this.selectColor(this.defaultColor)
      }
    },
    /**
     * @description: 颜色显示，已选颜色标记
     * @param {String} color 指定的颜色
     * @return {Object} css 对象
     */
    colorBoxStyle(color) {
      const boxShadow = `0 0 4px 1px ${color}`
      return {
        background: color,
        'box-shadow': this.colorValue === color ? boxShadow : 'none'
      }
    },
    /**
     * @description: 设置当前选择颜色
     * @param {String} color 要设置的颜色值
     * @return {*}
     */
    selectColor(color) {
      this.colorValue = color || undefined
    },
    /**
     * @description: 设置自定义的颜色
     * @param {String} color 要设置的颜色值
     * @return {*}
     */
    customColorChange(color) {
      if (this.customColorsList.includes(color)) return
      const index =
        this.maxCustomColorCount > 0 && this.customColorsList.length >= this.maxCustomColorCount
          ? 1
          : 0
      this.customColorsList = [...this.customColorsList.slice(index), color]
    },
    // 还原至当前风格的配色
    resetColorHandler() {
      // 根据风格方案还原风格对应的颜色
      this.selectColor(this.resetColor)
    },
    handleShowColorSelect() {
      this.$emit('handleShowColorSelect')
    }
  }
}
</script>

<style lang="scss" scoped>
.color-popover {
  .color-box {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 64px;
    min-width: 64px;
    height: 32px;
    min-height: 32px;
    padding: 3px;
    font-size: 0;
    cursor: pointer;
    border: 1px solid $neutral-color-3;
    border-radius: 3px;
    .color-box__inner {
      box-sizing: border-box;
      display: block;
      width: 100%;
      height: 100%;
    }
    .reset-box {
      position: absolute;
      top: 50%;
      right: 4px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      vertical-align: middle;
      background: $base-white;
      border-radius: 2px;
      box-shadow: 0 0 3px 0px $neutral-color-3;
      opacity: 0;
      transition: all 0.3s;
      transform: translate(0, -50%);
      .delete-icon {
        font-size: 14px;
        line-height: 20px;
      }
    }
    &:hover .reset-box {
      opacity: 1;
    }
  }
  .read-only {
    cursor: not-allowed;
  }
}
.preview-box {
  box-sizing: border-box;
  .title {
    font-size: 13px;
    line-height: 30px;
    color: $text-auxiliary;
  }
  .preview-color-list {
    display: flex;
    flex-wrap: wrap;
    margin-right: -12px;
    // justify-content: space-around;
    .color {
      position: relative;
      box-sizing: border-box;
      // display: inline-block;
      width: 36px;
      height: 36px;
      margin-right: 10px;
      margin-bottom: 10px;
      text-align: center;
      cursor: pointer;
      border: 1px solid $neutral-color-3;
      transition: all 0.3s;
      animation: transform 0.4s;
    }
    .icon-plus {
      font-size: 18px;
      line-height: 35px;
      color: $neutral-color-3;
      pointer-events: none;
    }
    .custom {
      position: absolute;
      top: 0;
      opacity: 0;
    }
    // .color:nth-child(6) {
    //   margin-right: 0;
    // }
    @keyframes transform {
      from {
        opacity: 0;
        transform: scale(0.3);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }
  }
}
</style>
