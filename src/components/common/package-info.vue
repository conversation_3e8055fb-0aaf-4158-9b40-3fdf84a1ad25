<!--
 * @Description:
-->
<template>
  <div v-if="show" class="package-container">
    <template>
      <transition name="el-fade-in-linear">
        <div class="package-content">
          <div class="package-content-right">
            <i class="el-icon-error el-message__icon package-content-right__icon"></i>
            <span class="content-right__message">{{ message }}</span>
          </div>
          <div class="package-content-left">
            <!-- <el-button type="text" class="button-left">删除按钮</el-button> -->
            <el-button class="button-right" type="text">{{
              $t('packageDowngrade.updatePackage')
            }}</el-button>
          </div>
          <div class="package-content__close">
            <i class="el-icon-close" @click="close"></i>
          </div>
        </div>
      </transition>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: '这是降套餐消息'
    }
  },
  mounted() {
    // this.show = false
    // setTimeout(() => {
    //   this.show = true
    // }, 2000)
  },
  methods: {
    close() {
      this.$emit('update:show', false)
    },
    handleDelete() {
      console.log('handleDelete')
    }
  }
}
</script>

<style lang="scss" scoped>
.package-container {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 4000;
  display: flex;
  justify-content: center;
  width: 100vw;
  overflow: hidden;
  transform: translateX(-50%);
  .package-content {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    padding: 10px 20px;
    background-color: #fef0f0;
    // width: 50vw;
    // align-items: center;
    border: $error-base-color-6 solid 1px;
    border-radius: 5px;
    &-left {
      width: auto;
      margin-left: 20px;
    }
    &-right {
      width: auto;
      &__icon {
        color: $error-base-color-6;
      }
    }
  }
}
.el-button--text {
  padding: 0px;
  margin: 0 25px;
  color: $link-base-color-6;
  text-decoration: underline;
}
.el-col {
  margin: 5px 0px;
  text-align: center;
}
</style>
