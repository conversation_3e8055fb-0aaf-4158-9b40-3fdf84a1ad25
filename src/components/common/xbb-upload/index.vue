<template>
  <div id="upload-wrap">
    <el-upload
      ref="upload"
      :accept="accept"
      :action="action"
      :before-upload="beforeUpload"
      :class="!!onlyCamera ? 'upload-disable' : 'upload'"
      :data="data"
      :disabled="disabled"
      drag
      :file-list="mode === 'file' ? fileList : pictureList"
      :http-request="httpRequest"
      :limit="limit"
      :list-type="listType"
      :multiple="multiple"
      :on-error="onError"
      :on-exceed="onExceed"
      :on-preview="onPreview"
      :on-remove="onRemove"
      :on-success="onSuccess"
      :show-file-list="false"
    >
      <div
        ref="uploadBox"
        class="upload-text"
        @click.stop.prevent="handleUploadFocus"
        @keydown="handleFocus"
        @paste="handlePaste"
      >
        <template v-if="showPasteText">
          {{ OSnow() === 2 ? 'Command' : 'Ctrl' }} + V 粘贴上传
        </template>
        <template v-else>
          <el-button
            :class="!!onlyCamera ? 'disable-button' : ''"
            :icon="iconType"
            size="medium"
            type="text"
            @click.stop="uploadClick"
            >点击上传
          </el-button>
          / 拖拽或粘贴到此处上传
        </template>
      </div>
      <div slot="tip" class="el-upload__tip">
        <slot name="tip"></slot>
      </div>
    </el-upload>
    <slot name="btn"></slot>
    <template v-if="mode === 'image'">
      <div v-if="successUploadImg.length > 0" class="file-img-list">
        <div
          v-for="(item, index) in successUploadImg"
          :key="item.url"
          class="file-list-item"
          style="margin-top: 6px; margin-bottom: 0"
        >
          <div class="file-list-item-img-block">
            <img v-lazy="thumbnail(item.url, 90)" alt="" />
            <label class="file-list__item-status-label">
              <i class="el-icon-check el-icon-upload-success"></i>
            </label>
            <span class="file-list__item-actions">
              <span
                class="file-list__item-preview"
                @click.stop="previewPicture(successUploadImg, index)"
              >
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="file-list__item-delete" @click.stop="deletePicture(index)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </div>
      </div>
    </template>
    <template v-if="mode === 'file'">
      <div v-for="(itemFile, itemIndex) in fileShowList" :key="itemFile.uid" class="breakFileItem">
        <div class="fileBlock">
          <div class="file_info">
            <i class="el-icon-document"></i>
            <span class="fileName" @click="previewFile(fileShowList, itemIndex)">{{
              itemFile.filename
            }}</span>
          </div>
          <i v-if="editable" class="closeBtn el-icon-close" @click="deleteFile(itemFile)"></i>
          <em
            v-show="itemFile.uploadProgress && itemFile.uploadProgress < 100"
            class="uploadProgress"
            >{{ itemFile.uploadProgress }}%</em
          >
          <i
            v-show="itemFile.uploadProgress && itemFile.uploadProgress >= 100"
            class="el-icon-circle-check successIcon"
          ></i>
        </div>
        <div style="width: 100%">
          <el-progress
            v-show="itemFile.uploadProgress && itemFile.uploadProgress !== 100"
            :percentage="itemFile.uploadProgress"
            :show-text="false"
            :stroke-width="2"
          ></el-progress>
        </div>
      </div>
    </template>
    <!-- 附件预览 -->
    <lg-preview
      v-if="isPreviewShow"
      v-appendToBody
      :index="previewIndex"
      :list="previewList"
      @close="isPreviewShow = false"
    ></lg-preview>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'

export default {
  name: 'UploadDialog',
  props: {
    mode: {
      type: String,
      default: 'image' // image | file
    },
    editable: {
      type: Boolean,
      default: true
    },
    accept: {
      type: String,
      default: undefined
    },
    action: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    },
    fileList: {
      type: Array,
      default: () => []
    },
    pictureList: {
      type: Array,
      default: () => []
    },
    fileShowList: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 3
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    listType: {
      type: String,
      default: ''
    },
    beforeUpload: {
      type: Function,
      default: () => {}
    },
    onError: {
      type: Function,
      default: () => {}
    },
    onSuccess: {
      type: Function,
      default: () => {}
    },
    onPreview: {
      type: Function,
      default: () => {}
    },
    onRemove: {
      type: Function,
      default: () => {}
    },
    onExceed: {
      type: Function,
      default: () => {}
    },
    onlyCamera: {
      type: Number || Boolean,
      default: 0
    },
    httpRequest: {
      type: Function,
      default: undefined
    },
    successUploadImg: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showPasteText: false,
      previewList: [],
      previewIndex: 0,
      isPreviewShow: false
    }
  },
  components: {
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },
  mounted() {
    setTimeout(() => {
      const uploadWrapEl = this.$refs['upload']?.$refs['upload-inner'].$el
      if (!uploadWrapEl) return
      // uploadWrapEl.addEventListener('focus', this.handleUploadFocus)
      uploadWrapEl.addEventListener('blur', this.handleUploadBlur)
      this.$once('hook:beforeDestroy', () => {
        uploadWrapEl.removeEventListener('focus', this.handleUploadFocus)
        uploadWrapEl.removeEventListener('blur', this.handleUploadBlur)
      })
    })
  },
  computed: {
    iconType() {
      return this.mode === 'image' ? 'el-icon-picture-outline' : 'el-icon-paperclip'
    }
  },
  methods: {
    OSnow() {
      const agent = navigator.userAgent.toLowerCase()
      const isMac = /macintosh|mac os x/i.test(navigator.userAgent)
      if (agent.indexOf('win32') >= 0 || agent.indexOf('wow32') >= 0) {
        return 0
      }
      if (agent.indexOf('win64') >= 0 || agent.indexOf('wow64') >= 0) {
        return 1
      }
      if (isMac) {
        return 2
      }
    },
    thumbnail(img, size = 40) {
      return xbb.thumbnail(img, size)
    },
    previewPicture(pictureList, index) {
      const urls = []
      for (const item in pictureList) {
        urls.push(pictureList[item].url)
      }
      this.previewFile(urls, index)
    },
    deletePicture(index) {
      this.$emit('handlePictureDelete', index)
      this.$refs.upload.handleRemove(this.pictureList[index])
    },
    previewFile(fileList, index = 0) {
      this.previewList = fileList // 将要预览的存放图片地址的数组
      this.previewIndex = index // 当前预览的图片的索引
      this.isPreviewShow = true // 打开预览文件组件的开关
    },
    deleteFile(file) {
      const index = this.fileList.findIndex(
        (item) => item.attachIndex === file.url || item.uid === file.uid
      )
      this.$refs.upload.handleRemove(this.fileList[index])
      this.$emit('handleFileDelete', file)
    },
    getNowFile() {
      return this.mode === 'file' ? this.fileList : this.pictureList
    },
    clearFiles() {
      this.$refs.upload.clearFiles()
    },
    abort() {},
    handleUploadFocus() {
      if (this.disabled) return
      this.showPasteText = true
    },
    handleUploadBlur() {
      this.showPasteText = false
    },
    uploadClick() {
      if (this.disabled) return
      this.$refs['upload'].$refs['upload-inner'].handleClick()
    },
    handleFocus() {
      if (this.disabled) return
      this.$refs.uploadBox.blur()
    },
    handlePaste(event) {
      if (this.disabled) return
      if (!this.showPasteText) return
      const clipboardItems = (event.clipboardData || window.clipboardData).items
      const clipboardFiles = (event.clipboardData || window.clipboardData).files
      event.preventDefault()
      const files = []
      if (!clipboardItems || clipboardItems.length === 0) {
        this.$message.error('当前浏览器不支持本地剪切板')
        return
      }
      if (this.mode === 'image') {
        for (const clipboardItem of clipboardItems) {
          if (clipboardItem.type.indexOf('image') !== -1) {
            files.push(clipboardItem.getAsFile())
          }
        }
      } else {
        Array.from(clipboardFiles).forEach((file) => {
          files.push(file)
        })
      }

      if (!files.length) {
        this.$message.error(`粘贴内容非${this.mode === 'image' ? '图片' : '文件'}`)
        return
      }
      this.$refs.upload.$refs['upload-inner'].uploadFiles(files)
    }
  }
}
</script>

<style lang="scss" scoped>
#upload-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-width: 250px;
  max-width: 430px;
  // :deep(.el-upload-dragger ){
  //   width: 100%;
  //   height: 100%;
  // }
  .el-upload__tip {
    margin-top: 0;
  }
  .breakFileItem {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 4px;
    margin-bottom: 4px;
    line-height: 30px;
    cursor: pointer;
    border-radius: 2px;
    &:hover {
      background-color: $neutral-color-1;
      .fileName {
        color: $brand-color-5;
      }
      .closeBtn {
        display: block !important;
      }
      .successIcon {
        display: none;
      }
      .uploadProgress {
        display: none;
      }
    }
    .fileBlock {
      display: flex;
      align-items: center;
      justify-content: space-between;
      i {
        font-size: 14px;
        &.closeBtn {
          display: none;
          cursor: pointer;
        }
        &.successIcon {
          color: #67c23a;
        }
      }
      span {
        display: inline-block;
        margin: 0 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .uploadProgress {
        font-style: normal;
        cursor: pointer;
      }
    }
    .file_info {
      display: flex;
      align-items: center;
      overflow: hidden;
    }
  }
  .file-img-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    & > .file-list-item {
      box-sizing: border-box;
      display: inline-block;
      width: 90px;
      height: 90px;
      margin: 0 8px 8px 0;
      overflow: hidden;
      background-color: $base-white;
      border: 1px solid $line-cut-table;
      border-radius: 6px;
      .file-list-item-img-block {
        position: relative;
        width: 100%;
        height: 100%;
        & > img {
          width: 100%;
          height: 100%;
        }
        .file-list__item-status-label {
          position: absolute;
          top: -6px;
          right: -15px;
          display: block;
          width: 40px;
          height: 24px;
          text-align: center;
          background: #13ce66;
          box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);
          transform: rotate(45deg);
          .el-icon-check {
            position: absolute;
            right: 15px;
            margin-top: 11px;
            font-size: 12px;
            color: $base-white;
            transform: rotate(-45deg);
          }
        }
        .file-list__item-actions {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          font-size: 20px;
          color: $base-white;
          cursor: default;
          background-color: rgba(0, 0, 0, 0.5);
          opacity: 0;
          transition: opacity 0.3s;
          &:hover {
            opacity: 1;
            span {
              display: inline-block;
            }
          }
          span {
            display: none;
            cursor: pointer;
          }
          span + span {
            margin-left: 15px;
          }
        }
      }
    }
  }
}
.upload {
  width: 100%;
  overflow: hidden;
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 100%;
    .upload-text {
      line-height: 98px;
      color: $text-auxiliary;
      .el-icon-picture-outline {
        font-size: 14px;
        color: $brand-color-5;
      }
    }
  }
  :deep(.el-upload) {
    width: 100%;
    height: 100px;
    border: 0;
    &:focus {
      .upload-text {
        background: $brand-color-1;
      }
    }
    .is-dragover {
      background-color: $brand-color-1;
      border: 1px dashed $brand-base-color-6;
    }
  }
}
.upload-text {
  line-height: 98px;
  color: $text-auxiliary;
}
.upload-disable {
  width: 100%;
  overflow: hidden;
  :deep(.el-upload) {
    width: 100%;
    height: 100px;
    border: 0;
  }
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 100%;
    .upload-text {
      line-height: 98px;
      color: $text-grey;
      cursor: not-allowed;
      background-color: $neutral-color-1;
    }
  }
}
.disable-button {
  color: $text-grey;
  cursor: not-allowed;
}
[contenteditable]:focus {
  outline: none;
}
</style>
