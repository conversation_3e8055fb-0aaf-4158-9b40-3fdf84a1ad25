<template>
  <div v-clickoutside="closeDial" class="dial-modal" :style="dialStyle">
    <ul class="phone-list">
      <li
        v-for="(item, index) in phoneList"
        :key="index"
        class="item"
        @click="duyanCall(item.phoneNum)"
      >
        <span class="label">{{ item.name }}</span>
        <span class="phone-number">{{ item.telNum }}</span>
        <i class="web-icon-callout web-iconfont"></i>
      </li>
    </ul>
    <div class="dial-arrow"></div>
  </div>
</template>

<script>
export default {
  name: 'DialModal',

  props: {
    dialStyle: {
      type: Object,
      required: true
    },
    phoneList: {
      type: Array,
      required: true
    }
  },

  methods: {
    // 调用CTI进行呼出
    duyanCall(phone) {
      window.CALL_CENTER.callme(phone)
    },

    // 关闭拨号弹层
    closeDial() {
      this.$emit('closeModal')
    }
  }
}
</script>

<style lang="scss" scoped>
.dial-modal {
  position: fixed;
  z-index: 21010;
  display: block;
  width: 200px;
  font-size: 12px;
  background-color: $base-white;
  border: 1px solid $neutral-color-3;
  border-radius: 2px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12), 0px 0px 6px 0px rgba(0, 0, 0, 0.04);
  .phone-list {
    .item {
      display: flex;
      padding: 0 10px;
      line-height: 30px;
      cursor: pointer;
      &:hover {
        background-color: $neutral-color-1;
      }
      .phone-number {
        color: $link-base-color-6;
      }
      .label,
      .phone-number {
        display: inline-block;
        flex: 1 1 auto;
      }
      .web-icon-callout {
        flex: 0 0 auto;
        font-size: 14px;
        line-height: 30px;
        color: $success-base-color-6;
      }
    }
  }
  .dial-arrow,
  .dial-arrow::after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
  }
  .dial-arrow {
    top: 10px;
    left: -6px;
    margin-bottom: 3px;
    border-width: 6px;
    border-right-color: $neutral-color-3;
    border-left-width: 0;
    &:after {
      bottom: -6px;
      left: 1px;
      content: '';
      border-width: 6px;
      border-right-color: $base-white;
      border-left-width: 0;
    }
  }
}
</style>
