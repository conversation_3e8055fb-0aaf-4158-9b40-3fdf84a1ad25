s
<!--
 * @Description: 带上传附件的输入框（客户阶段的反馈在用）
 -->

<template>
  <div class="comment-box stage-comment-box" :class="{ active: true || open }">
    <!-- 客户阶段这边目前都是打开状态，先写死,代码先不删，以后可能用到 -->
    <at-who ref="at" v-model="textarea" :affix="elementTemplate">
      <textarea
        ref="textArea"
        v-model="textarea"
        class="enter"
        :placeholder="placeholderText"
        @focus="openBox"
      >
      </textarea>
    </at-who>
    <!-- <div class="mention-result" v-show="mentionList.length">
      <ul>
        <li class="first">
          <span class="text">选择你要@的人</span>
        </li>
        <li
          class="me-item"
          v-for="(item, index) in mentionList"
          :key="item.id"
          :class="{actived: Mcurrent === index}"
          @click="selectMention(item)"
          @mouseover="changeCurrent(index)">
          <span class="text">{{item.value}}</span>
        </li>
      </ul>
    </div> -->
    <div class="options">
      <ul class="menu">
        <!-- 上传图片 -->
        <li class="opt-item">
          <el-badge
            class="item"
            :hidden="successUploadImg.length === 0"
            :value="successUploadImg.length"
          >
            <el-popover
              ref="imgPopover"
              placement="bottom-start"
              :title="$t('operation.uploadPictures')"
              trigger="click"
              width="400"
            >
              <xbb-upload
                ref="refUploadImg"
                :accept="imgAccept"
                :action="uploadUlr"
                :before-upload="handleBeforeUpload"
                class="stage-comment-img"
                :data="uploadData"
                :limit="9"
                multiple
                :on-error="handleError"
                :on-exceed="fileUploadLimitHook"
                :on-success="imgAdd"
                :picture-list="images"
                :success-upload-img="successUploadImg"
                @handlePictureDelete="imgRemove"
              >
              </xbb-upload>
              <i
                slot="reference"
                class="text web-icon-img web-iconfont"
                @click="set_upload_param"
              ></i>
            </el-popover>
          </el-badge>
        </li>
        <li class="opt-item">
          <el-badge class="item" :hidden="fileAttrValue.length === 0" :value="fileAttrValue.length">
            <el-popover
              ref="filePopover"
              placement="bottom-start"
              :title="$t('stageCommentBox.uploadAttachments')"
              trigger="click"
              width="400"
            >
              <xbb-upload
                ref="uploadFile"
                :action="uploadUlr"
                :before-upload="handleBeforeFileUpload"
                :data="uploadData"
                :file-show-list="fileAttrValue"
                :limit="9"
                mode="file"
                multiple
                :on-error="handleError"
                :on-exceed="fileUploadLimitHook"
                :on-success="fileAdd"
                @handleFileDelete="fileRemove"
              >
              </xbb-upload>
              <i
                slot="reference"
                class="text web-icon-file web-iconfont"
                @click="set_upload_param"
              ></i>
            </el-popover>
          </el-badge>
        </li>
      </ul>
      <el-button class="submit" size="small" type="primary" @click="handleEmit">{{
        $t('operation.submit')
      }}</el-button>
      <el-button class="cancel" size="small" @click="closeBox">{{
        $t('operation.cancel')
      }}</el-button>
    </div>
  </div>
</template>

<script>
import { addComment } from '@/api/detail-handle'
import { Message } from 'element-ui'
// import atSomeone from './at-someone'
import uploadMix from '@/mixin/uploadMixin'
import AtWho from '@/components/at-who'
import specialJudge from '@/utils/form-data-special-judge'
// 业务枚举
import commonBusinessType from '@/constants/common/business-type'
import { addWorkOrderV2Comment } from '@/api/work-order-v2/work-order-detail'
import XbbUpload from '@/components/common/xbb-upload/index.vue'

export default {
  components: {
    AtWho,
    XbbUpload
  },
  mixins: [uploadMix],
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    defUserId: {
      type: String,
      required: false
    },
    query: {
      required: true,
      default: () => {
        return {}
      }
    },
    refType: {
      type: String,
      required: true
    },
    nowStageId: {
      type: Number,
      required: false
    }
  },
  data() {
    return {
      pageBusinessType: commonBusinessType, // 业务枚举
      elementTemplate: {
        front: '<span class="at-person">',
        end: '</span>'
      },
      successUploadImg: [],
      textarea: '',
      open: this.isOpen,
      fileAttrValue: [],
      images: [],
      placeholderText: ''
    }
  },
  created() {
    if (this.refType === 'nowStage') {
      this.placeholderText = this.$t('placeholder.inputPls', {
        attr: this.$t('stageCommentBox.feedbackContent')
      })
    } else if (this.refType === 'dynamic') {
      this.placeholderText = this.$t('stageCommentBox.workOrderStatus')
    }
  },
  methods: {
    // 展开
    openBox() {
      this.open = true
    },
    // 收回
    closeBox() {
      this.open = false
    },
    // 提交回复
    handleEmit() {
      const atUserIds = this.$refs.at.getInfo().templateArr
      const content = this.$refs.at.getInfo().template
      if (this.textarea.length > 2000) {
        Message({
          type: 'error',
          message: this.$t('rule.lessThanFie', { attr: '', num: 2000 }) + '！'
        })
        return
      }
      let imagesArr = []
      imagesArr = this.successUploadImg.map((it) => {
        return it.url + '?' + it.uid
      })
      const params = {
        ...this.query,
        refId: this.nowStageId,
        refType: this.refType,
        content: content,
        attachmentList: this.fileAttrValue,
        atUserIds: atUserIds,
        images: imagesArr
      }
      if (this.query.businessType === this.pageBusinessType.CUSTOMER_MANAGEMENT) {
        // 客户阶段
        params.businessType = 108
      } else if (this.query.businessType === this.pageBusinessType.SALES_OPPORTUNITY) {
        // 销售机会阶段
        params.businessType = 304
      } else if (this.query.businessType === this.pageBusinessType.SALES_LEADS) {
        // 销售机会阶段
        params.businessType = 8005
      } else if (this.query.businessType === this.pageBusinessType.CONTRACT) {
        params.businessType = 210
      }
      const isWorkOrderV2 = specialJudge.isWorkOrderV2(Number(params.businessType))
      let promise = addComment
      if (isWorkOrderV2) {
        promise = addWorkOrderV2Comment
        params.workOrderId = params.dataId
      }
      promise(params)
        .then((data) => {
          this.fileAttrValue = []
          this.images = []
          this.successUploadImg = []
          this.textarea = ''
          this.open = false
          Message({
            type: 'success',
            message: data.msg || this.$t('message.operateSuccessSymbol')
          })
          this.$refs.uploadFile.clearFiles() // 这里清空上传文件列表和上传图片列表
          this.$refs.uploadFile.abort()
          this.$refs.refUploadImg.clearFiles()
          this.$refs.refUploadImg.abort()

          // this.$refs.uploadImg.clearFiles()
          // this.$refs.uploadImg.abort()
          this.$emit('refresh')
        })
        .catch(() => {})
    },
    // 判断文件名或uid，有任意一个则视为相等
    fileRemove(file) {
      if (file) {
        const arr = this.fileAttrValue
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].attachIndex === file.url || arr[i].uid === file.uid) {
            arr.splice(i, 1)
          }
        }
        this.$nextTick(() => {
          this.$refs.filePopover.updatePopper()
        })
      }
    },
    fileAdd(response, file) {
      const newFile = {
        filename: file.name,
        attachIndex: this.fileUrl[file.uid],
        ext: file.name.replace(/.+\./, '').toLowerCase(),
        size: file.size,
        uid: file.uid,
        name: file.name
      }
      this.fileAttrValue.push(newFile)
      this.$refs.filePopover.updatePopper()
    },
    // 移除图片
    imgRemove(index) {
      // this.images.splice(index, 1)
      this.successUploadImg.splice(index, 1)
      this.$nextTick(() => {
        this.$refs.imgPopover.updatePopper()
      })
    },
    // 上传图片
    imgAdd(response, file) {
      const imgItem = {
        url: this.fileUrl[file.uid],
        uid: file.uid,
        name: file.name
      }
      // this.images.push(imgItem)
      this.successUploadImg.push(imgItem)
      this.$nextTick(() => {
        this.$refs.imgPopover.updatePopper()
      })
    }
  }
}
</script>

<style lang="scss">
.stage-comment-box {
  .opt-item {
    position: relative;

    .el-upload-list {
      position: absolute;
      min-width: 200px;
      background: $base-white;
      .el-upload-list__item {
        border: 1px solid $neutral-color-2;
        border-top: 0;
        border-bottom: 0;
      }
      .el-upload-list__item:first-child {
        border-top: 1px solid $neutral-color-2;
      }
      .el-upload-list__item:last-child {
        border-bottom: 1px solid $neutral-color-2;
      }
    }
  }
}
.stage-comment-img {
  & li.el-upload-list__item,
  & .el-upload--picture-card {
    width: 80px !important;
    height: 80px !important;
  }
  :deep(.el-upload-list__item-actions) {
    font-size: 12px;
    text-align: left;
    .el-upload-list__item-preview {
      position: relative;
      left: 2px;
    }
    .el-upload-list__item-delete {
      position: relative;
      right: 6px;
    }
  }
  :deep(.el-upload-list__item) {
    width: 42px !important;
    height: 42px !important;
    line-height: 42px !important;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
  i.el-icon-plus {
    position: relative;
    top: -30px;
    line-height: 80px;
  }
}
.comment-box {
  .options {
    position: relative;
    height: 44px;
    background: $neutral-color-1;
    .menu {
      padding: 13px 0 0 18px;
      :deep(.el-badge__content) {
        height: 14px;
        padding: 0px 4px;
        font-size: 12px;
        line-height: 14px;
        background-color: $neutral-color-4;
      }
      .opt-item {
        display: inline-block;
        padding-right: 48px;
        font-size: 13px;
        &:first-child {
          padding-top: 1px;
          padding-bottom: 1px;
        }
        &:last-child {
          border-right-width: 0;
        }
        &.required::after {
          position: absolute;
          top: 2px;
          left: 10px;
          color: $error-color-8;
          content: '*';
        }
        .text {
          font-size: 18px;
          color: $neutral-color-5;
          word-break: break-all;
          cursor: pointer;
        }
      }
    }
    .submit {
      position: absolute;
      top: 7px;
      right: 80px;
      padding: 6px 15px;
    }
    .cancel {
      position: absolute;
      top: 7px;
      right: 10px;
      padding: 6px 15px;
    }
  }
  &.active {
    .enter {
      height: 62px;
    }
    .btn-group {
      display: block;
    }
  }
  .enter {
    box-sizing: border-box;
    display: block;
    width: 100%;
    height: 32px;
    padding: 7px;
    font-size: 13px;
    color: $text-plain;
    resize: none;
    background: $base-white;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
    transition: height 0.5s;
  }
  .mention-result {
    position: absolute;
    top: 78px;
    left: 16px;
    z-index: 50;
    width: 200px;
    padding: 2px;
    overflow: auto;
    font-size: 13px;
    background-color: $base-white;
    border-radius: 2px;
    box-shadow: 0 0 4px 0 $neutral-color-6;
    .first {
      padding: 5px 10px;
    }
    .me-item {
      padding: 8px 10px;
      cursor: pointer;
      &:hover,
      &.actived {
        color: $brand-color-5;
        background-color: $neutral-color-2;
      }
    }
  }
}
</style>
