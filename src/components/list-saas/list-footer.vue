<!-- eslint vue/no-mutating-props: 1 -->

<template>
  <div ref="listFooter" v-clickoutside="handleHideSelectedList" class="list-footer">
    <!-- 批量操作区 -->
    <div v-if="ifShowBatch && selectedRow.length > 0" class="foot-bg">
      <div class="selected">
        已选<strong @click="handleToggleSelectedList">{{ selectedRow.length }}</strong
        >条
      </div>
      <span class="remove-all" @click="handleRemoveAllSelectedRow">取消</span>
      <ul class="foot-menu">
        <slot name="batchButton"></slot>
      </ul>
    </div>

    <!-- 已选数据 -->
    <transition name="fold">
      <div v-show="showList" class="select-list">
        <div class="list-head">
          <h4 class="list-tit">{{ $t('form.selected') }} ({{ selectedRow.length }})</h4>
          <i class="close el-icon-close" @click="handleHideSelectedList"></i>
        </div>
        <div class="list-con">
          <ul>
            <li v-for="(row, index) in selectedRow" :key="index" class="item">
              <span class="text">{{ row[rowNameKey] }}</span>
              <i
                class="close el-icon-circle-close"
                @click="handleRemoveSelectedRow(row[rowIdKey])"
              ></i>
            </li>
          </ul>
        </div>
      </div>
    </transition>

    <!-- 分页器 -->
    <div ref="pagination" class="pagination">
      <v-pagination
        :current-page.sync="pageHelper.currentPage"
        layout="slot, sizes, prev, pager, next, jumper"
        :page-size="pageHelper.pageSize"
        :page-sizes="pageSize"
        :tool-tip="$t('listTable.pageToolTips')"
        :total="pageHelper.total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </v-pagination>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

export default {
  name: 'ListFooter',

  components: {},

  mixins: [],

  props: {
    pageSize: {
      type: Array,
      default: () => [20, 30, 50, 100, 200]
    },
    pageHelper: {
      type: Object,
      required: true
    },
    ifShowBatch: {
      type: Boolean,
      default: true
    },
    rowIdKey: {
      type: String,
      required: true
    },
    rowNameKey: {
      type: String,
      required: true
    },
    selectedRow: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      showList: false,
      currentPageHelper: {
        ...this.pageHelper
      }
    }
  },

  computed: {},

  watch: {
    selectedRow: {
      deep: true,
      handler(value) {
        if (value.length === 0) {
          this.handleHideSelectedList()
        }
      }
    }
  },

  created() {},

  beforeDestroy() {},

  methods: {
    handleHideSelectedList() {
      this.showList = false
    },
    handleToggleSelectedList() {
      this.showList = !this.showList
    },
    handleRemoveAllSelectedRow() {
      this.$emit('remove-all-selected-row')
    },
    handleRemoveSelectedRow(rowId) {
      this.$emit('remove-selected-row', rowId)
    },
    handleCurrentChange(val) {
      this.currentPageHelper = {
        pageSize: this.pageHelper.pageSize,
        currentPage: val
      }
      this.pageChange(this.currentPageHelper)
    },
    handleSizeChange(val) {
      this.currentPageHelper = {
        pageSize: val,
        currentPage: 1
      }
      this.pageChange(this.currentPageHelper)
    },
    pageChange(pageHelper) {
      this.$emit('pageHelper-change', pageHelper)
    }
  }
}
</script>

<style lang="scss" scoped>
.list-footer {
  position: relative;
  display: flex;
  flex-basis: 44px;
  flex-grow: 0;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background: $base-white;
  .foot-bg {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    .remove-all {
      padding-right: 12px;
      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      color: $link-base-color-6;
      cursor: pointer;
      border-right: 1px solid $line-dialog;
    }
    .selected {
      display: inline-block;
      height: 24px;
      margin-right: 12px;
      font-size: 13px;
      line-height: 24px;
      white-space: nowrap;
      vertical-align: top;
      background-color: $base-white;
      strong {
        display: inline-block;
        width: 20px;
        font-size: 12px;
        font-weight: 400;
        color: $link-base-color-6;
        text-align: center;
        cursor: pointer;
      }
    }
    .foot-menu {
      display: flex;
      flex-wrap: wrap;
      margin-left: 20px;
    }
  }
  .select-list {
    position: absolute;
    top: -15px;
    left: 0;
    z-index: 999;
    width: 360px;
    overflow: hidden;
    border-radius: 0 0 2px 2px;
    box-shadow: 0 0 4px 0 rgba(105, 105, 105, 0.5);
    transform: translate3d(0, -100%, 0);
    &.fold-enter-active,
    &.fold-leave-active {
      transition: all 0.3s ease-out;
    }
    &.fold-enter,
    &.fold-leave-active {
      transform: translate3d(0, 0, 0);
    }
    .list-head {
      position: relative;
      padding: 15px;
      color: $base-white;
      background-color: $bg-menu;
      border-radius: 2px 2px 0 0;
      .list-tit {
        font-size: 16px;
        line-height: 1;
      }
      .close {
        position: absolute;
        top: 14px;
        right: 15px;
        font-size: 18px;
        cursor: pointer;
      }
    }
    .list-con {
      max-height: 500px;
      padding: 10px;
      overflow: auto;
      background-color: $base-white;
      .item {
        position: relative;
        padding: 15px 8px;
        border-bottom: 1px dashed $neutral-color-3;
        &:last-child {
          border-bottom-width: 0;
        }
        .text {
          display: block;
          margin-right: 24px;
          overflow: hidden;
          font-size: 13px;
          line-height: 1;
          color: $text-plain;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .close {
          position: absolute;
          top: 9px;
          right: 2px;
          padding: 5px;
          font-size: 16px;
          line-height: 1;
          color: $error-color-5;
          cursor: pointer;
        }
      }
    }
  }
  .pagination {
    margin-left: auto;
  }
}
</style>
