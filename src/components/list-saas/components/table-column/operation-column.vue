<template>
  <el-table-column :fixed="fixed" :label="label" :width="width">
    <template #default="{ row }">
      <el-button
        v-for="(operation, index) in outerOperationList"
        :key="index"
        :class="`operation-column-${operation.command}`"
        plain
        size="mini"
        :type="operation.type"
        @click="handleEmitOperation(operation.command, row)"
        >{{ operation.label }}</el-button
      >

      <el-dropdown
        style="margin-left: 10px"
        trigger="click"
        @command="(command) => handleEmitOperation(command, row)"
      >
        <el-button plain size="mini"><i class="icon-more-2-fill t-iconfont" /></el-button>

        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="(operation, index) in innerOperationList"
            :key="index"
            :class="`operation-column-${operation.command}`"
            :command="operation.command"
            ><span
              :class="{
                'font-color-error': operation.type === 'danger'
              }"
              >{{ operation.label }}</span
            ></el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'OperationColumn',

  props: {
    label: {
      type: String,
      default: '操作'
    },
    fixed: {
      type: [String, Boolean]
    },
    width: [Number, String],
    // {
    //    type: String, // primary / success / warning / danger / info
    //    command: String,
    //    label: String,
    // }
    operationList: {
      type: Array,
      default: () => []
    },
    outerOperationLength: {
      type: Number,
      default: 2
    }
  },

  computed: {
    showMoreOperation() {
      return this.operationList.length > this.outerOperationLength
    },
    outerOperationList() {
      return this.operationList.slice(0, this.outerOperationLength)
    },
    innerOperationList() {
      return this.showMoreOperation ? this.operationList.slice(this.outerOperationLength) : []
    }
  },

  methods: {
    handleEmitOperation(command, row) {
      this.$emit('command', command, row)
    }
  }
}
</script>

<style lang="scss" scoped>
.operation-column-sort {
  cursor: move;
}
</style>
