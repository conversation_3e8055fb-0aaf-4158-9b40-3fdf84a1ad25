<template>
  <el-dialog
    append-to-body
    :before-close="close"
    :title="$t('formDesign.editTag')"
    :visible="dialogShow"
    width="620px"
  >
    <div class="button-container">
      <el-button v-if="showNewBtn" type="text" @click="createLabel"
        ><i class="el-icon-plus" />{{ $t('operation.add') }}
      </el-button>
      <el-button v-if="showManageBtn" type="text" @click="manageLabel"
        ><i class="el-icon-setting" />{{ $t('teamManage.Administration') }}
      </el-button>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-if="registerMode.includes('formTag')"
        :label="$t('teamManage.formLabel')"
        name="formTag"
      >
        <div class="label">
          <el-input
            v-model.trim="searchTagName.formTag"
            :placeholder="$t('formDataEdit.searchTag')"
            prefix-icon="el-icon-search"
          />
          <div class="label-container">
            <div v-if="searchTagName.formTag && !showTagList.formTag.length" class="not-find">
              {{ $t('teamManage.noSearchLable') }}
            </div>
            <template v-else>
              <v-tag
                v-for="(tag, tagIndex) in showTagList.formTag"
                :key="tagIndex"
                :color="tag.color"
                :content="tag.name"
                :margin-bottom="10"
                :type="selectedLabelMap.formTag.includes(tag.id) ? '' : 'info'"
                @click="selectLabel(tag)"
              />
            </template>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane
        v-if="registerMode.includes('systemTag')"
        :label="$t('teamManage.systemLabel')"
        name="systemTag"
      >
        <div class="label">
          <el-input
            v-model.trim="searchTagName.systemTag"
            :placeholder="$t('formDataEdit.searchTag')"
            prefix-icon="el-icon-search"
          />
          <div class="label-container">
            <div v-if="searchTagName.systemTag && !showTagList.systemTag.length">
              {{ $t('teamManage.noSearchLable') }}
            </div>
            <template v-else>
              <v-tag
                v-for="(tag, tagIndex) in showTagList.systemTag"
                :key="tagIndex"
                :color="tag.color"
                :content="tag.name"
                :margin-bottom="10"
                :type="selectedLabelMap.systemTag.includes(tag.id) ? '' : 'info'"
                @click="selectLabel(tag)"
              />
            </template>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="line" />
    <div class="label-selected">
      <template v-if="registerMode.includes('formTag')">
        <v-tag
          v-for="tag in doTagSelected.formTag"
          :key="tag.id"
          closable
          :color="tag.color"
          :content="tag.name"
          :margin-bottom="10"
          @close="deleteSelectedLabel(tag)"
        />
      </template>
      <template v-if="registerMode.includes('systemTag')">
        <v-tag
          v-for="tag in doTagSelected.systemTag"
          :key="tag.id"
          closable
          :color="tag.color"
          :content="tag.name"
          :margin-bottom="10"
          @close="deleteSelectedLabel(tag)"
        />
      </template>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t('operation.cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('operation.confirm') }}</el-button>
    </div>
    <TeamTagManage
      v-if="registerMode.includes('formTag') && showManageBtn"
      ref="teamTagManage"
      :show.sync="teamManageShow"
      @specialTagCreateAfter="getFormTagList"
    />
    <SystemTagManage
      v-if="registerMode.includes('systemTag') && showManageBtn"
      ref="systemTagManage"
      :show.sync="systemManageShow"
      @specialTagCreateAfter="getSystemTagList"
    />
  </el-dialog>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import dialogMixin from '@/mixin/dialog'
import TeamTagManage from './team-tag-manage'
import SystemTagManage from './system-tag-manage'
import VTag from '@/components/base/v-tag.vue'
import {
  getFormTagSampleList,
  getSystemTagSampleList,
  getFormLabelTabList
} from '@/api/team-member.js'

export default {
  name: 'TagDataDialog',
  components: {
    TeamTagManage,
    SystemTagManage,
    VTag
  },
  mixins: [dialogMixin],
  props: {
    // 要使用标签 formTag 表单的团队成员，systemTag 组织架构系统标签
    registerMode: {
      type: Array,
      default: () => ['formTag', 'systemTag']
    },
    // 表单标签选中的标签
    formTagSelectedList: {
      type: Array,
      default: () => []
    },
    // 系统标签选中的标签
    systemTagSelectedList: {
      type: Array,
      default: () => []
    },
    // 控制新建按钮
    showNewBtn: {
      type: Boolean,
      default: true
    },
    // 控制管理按钮
    showManageBtn: {
      type: Boolean,
      default: true
    },
    // 参数补充
    query: {
      type: Object
    },
    // 来源
    origin: {
      type: String,
      default: 'form'
    }
  },
  data() {
    return {
      // 激活的业务
      activeName: '',
      // 查询的名称
      searchTagName: {},
      // 接口回来所有可选的标签
      allTagList: {},
      // 组件内部操作的标签
      doTagSelected: {},
      // 表单团队成员管理控制
      teamManageShow: false,
      // 系统标签管理控制
      systemManageShow: false
    }
  },
  computed: {
    selectedLabelMap() {
      const value = {}
      this.registerMode.forEach((key) => {
        value[key] = this.doTagSelected[key].map((label) => label.id)
      })
      return value
    },
    // 展示的所有的标签（用于名称查询后过滤）
    showTagList() {
      const value = {}
      this.registerMode.forEach((key) => {
        const searchContent = this.searchTagName[key]
        const list = this.allTagList[key] || []
        value[key] = xbb.deepClone(
          searchContent ? list.filter((tag) => tag.name.indexOf(searchContent) > -1) : list
        )
      })
      return value
    }
  },
  watch: {
    show: {
      immediate: true,
      handler: function (val) {
        if (!val) return
        const map = {
          formTag: 'formTagSelectedList',
          systemTag: 'systemTagSelectedList'
        }
        const value = {}
        this.registerMode.forEach((key) => {
          value[key] = xbb.deepClone(this[map[key]])
        })
        this.doTagSelected = value
        if (this.registerMode.includes('formTag')) {
          this.getFormTagList()
        }
        if (this.registerMode.includes('systemTag')) {
          this.getSystemTagList()
        }
      }
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    getFormTagList() {
      const {
        saasMark,
        formId,
        businessType,
        distributorMark = 0
      } = this.query || this.$route.query
      const requestFn = this.origin === 'tab' ? getFormLabelTabList : getFormTagSampleList
      return new Promise((resolve) => {
        requestFn({
          saasMark,
          formId,
          businessType,
          distributorMark
        }).then(({ result }) => {
          this.$set(this.allTagList, 'formTag', result.labelListVO)
          resolve()
        })
      })
    },
    getSystemTagList() {
      const { saasMark, formId, businessType, distributorMark } = this.query || this.$route.query
      return new Promise((resolve) => {
        getSystemTagSampleList({
          saasMark,
          formId,
          businessType,
          distributorMark
        }).then((res) => {
          console.log(res.result.labelListVO)
          this.$set(this.allTagList, 'systemTag', res.result.labelListVO)
          resolve()
        })
      })
    },
    initData() {
      this.registerMode.forEach((key) => {
        this.$set(this.searchTagName, key, '')
      })
      this.activeName = this.registerMode[0]
    },
    submit() {
      this.dialogShow = false
      this.$emit('submit', this.doTagSelected)
    },
    close() {
      this.initData()
      this.dialogShow = false
    },
    selectLabel(tag) {
      const activeTagSelectedData = this.doTagSelected[this.activeName]
      const tagSelectedIndex = activeTagSelectedData.findIndex((item) => item.id === tag.id)
      tagSelectedIndex !== -1
        ? activeTagSelectedData.splice(tagSelectedIndex, 1)
        : activeTagSelectedData.push(tag)
    },
    deleteSelectedLabel(tag) {
      for (const key of this.registerMode) {
        this.doTagSelected[key] = this.doTagSelected[key].filter((item) => item.id !== tag.id)
      }
    },
    createLabel() {
      const map = {
        formTag: 'teamTagManage',
        systemTag: 'systemTagManage'
      }
      this.$refs[map[this.activeName]].createTag()
    },
    manageLabel() {
      if (this.activeName === 'formTag') this.teamManageShow = true
      else if (this.activeName === 'systemTag') this.systemManageShow = true
    }
  }
}
</script>

<style scoped lang="scss">
.button-container {
  position: absolute;
  right: 90px;
  left: 500px;
  z-index: 10;
  width: 100px;
}

.label-container {
  height: 200px;
  margin-top: 16px;
  overflow: scroll;

  .tag-item {
    margin-right: 8px;
    margin-bottom: 10px;
    cursor: pointer;
  }

  .not-find {
    width: 100%;
    height: 100%;
    line-height: 200px;
    color: $text-auxiliary;
    text-align: center;
  }
}

.line {
  border-bottom: 1px $neutral-color-3 solid;
}

.label-selected {
  height: 100px;
  margin-top: 12px;
  overflow: scroll;

  .tag-item {
    margin-right: 8px;
    margin-bottom: 10px;
  }
}
</style>
