<!--
 * @Description: 产品分类选择 origin 部门选择
 -->
<template>
  <div class="prd-tab">
    <el-scrollbar class="tab-item-scroll user-scroll">
      <div class="prd-tree">
        <el-tree
          v-if="!killTree"
          ref="prdTree"
          check-strictly
          class="filter-tree"
          :data="prdList"
          :default-checked-keys="defaultCheckedKeys"
          default-expand-all
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          node-key="id"
          :props="defaultProps"
          show-checkbox
          @check="prdCheckChange"
        >
        </el-tree>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import { getPrdSelected } from '@/api/system'

export default {
  name: 'PrdTab',

  props: {
    // 选中的所有节点数组
    selectedTag: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    limitSameLevel: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      killTree: false,
      // 产品分类选择列表
      prdList: [],
      // 产品分类树配置
      defaultProps: {
        children: 'children',
        label: 'label',
        disabled: this.limitSameLevelNode
      },
      // 默认选中的节点
      defaultCheckedKeys: [],
      // 产品分类树的扁平结构
      prdFlatList: [],
      // 临时存储遍历树的层级
      temArr: []
    }
  },

  watch: {
    selectedTag: {
      handler(val) {
        if (val.length === 0) {
          this.$refs['prdTree'].setCheckedKeys([])
        }
      },
      deep: true
    }
  },

  mounted() {
    this.getPrdList()
    this.showBackNodes()
  },
  computed: {
    limitLevel() {
      return this.selectedTag.length
        ? this.prdFlatList.filter((item) => {
            return `${item.id}` === `${this.selectedTag[0].id}`
          })[0].level
        : -1
    }
  },

  methods: {
    limitSameLevelNode(data) {
      if (!this.limitSameLevel || !this.selectedTag.length) return false
      return data.level !== this.limitLevel
    },
    /**
     * 获取产品分类结构树
     */
    getPrdList() {
      getPrdSelected({
        formId: localStorage.getItem('PrdFormId')
      }).then((res) => {
        if (res && res.code === 1) {
          this.prdList = res.result.categoryTree || []
        }
        // 对后台给予的分类数据做处理
        this.prdList.forEach((item) => {
          this.treeParamManageWithLevel(item)
          this.treeToList(item)
        })
        this.$emit('listFinish', this.prdFlatList)
      })
    },
    // 树结构扁平化
    treeToList(obj) {
      this.prdFlatList.push(obj)
      if (obj.children) {
        obj.children.forEach((item) => {
          this.treeToList(item)
        })
      }
    },
    // 树结构递归每个参数赋值
    treeParamManage(obj) {
      this.$set(obj, 'property', 'prd')
      // 为了兼容已有的树形数据转换方法
      this.$set(obj, 'attr', '')
      this.$set(obj, 'id', Number(obj.id))
      this.$set(obj, 'name', obj.label)
      if (obj.children) {
        obj.children.forEach((item) => {
          this.treeParamManage(item)
        })
      }
    },

    /**
     * @description: 树结构递归每个参数赋值带上层级
     * @param obj
     * @param level
     */
    treeParamManageWithLevel(obj, level = 0) {
      this.$set(obj, 'property', 'prd')
      // 为了兼容已有的树形数据转换方法
      this.$set(obj, 'attr', '')
      this.$set(obj, 'id', Number(obj.id))
      this.$set(obj, 'name', obj.label)
      this.$set(obj, 'level', level)
      if (obj.children) {
        obj.children.forEach((item) => {
          this.treeParamManageWithLevel(item, level + 1)
        })
      }
    },

    /**
     * prdCheckChange ()
     * 树节点的选中状态发生变化
     * data: 传递给 data 属性的数组中该节点所对应的对象
     * checked: 节点本身是否被选中
     * node: 节点的子树中是否有被选中的节点
     */
    repeatCheck(item, data) {
      if (JSON.stringify(item).includes(`"id":${data.id},`)) {
        this.temArr.push(item)
      }
      if (JSON.stringify(data).includes(`"id":${item.id},`)) {
        this.temArr.push(item)
      }
    },
    prdCheckChange(data, checkedObj, node) {
      const checked = this.$refs['prdTree'].getNode(data.id).checked
      if (this.setLoading) {
        this.setLoading = false
        return
      }
      let arr = JSON.parse(JSON.stringify(this.selectedTag))
      // 之前所展示的选中的产品分类
      const prdArrId = this.selectedTag
        .filter((item) => {
          return item.property === 'prd'
        })
        .map((item) => {
          return String(item.id)
        })
      if (checked) {
        // 处理父子 互斥关系
        arr.forEach((item, num) => {
          this.repeatCheck(item, data)
        })
        arr = arr.filter((item) => {
          return !this.temArr
            .map((item) => {
              return item.id
            })
            .includes(item.id)
        })
        this.temArr = []
        // 去重
        if (!prdArrId.includes(String(data.id))) {
          arr.push(data)
        }
      } else {
        const deleteIndex = arr.findIndex((item) => {
          return item.property === 'prd' && Number(item.id) === Number(data.id)
        })
        if (deleteIndex !== -1) {
          arr.splice(deleteIndex, 1)
        }
      }
      if (!this.multiple) {
        arr = arr.splice(arr.length - 1, 1)
      }
      const keysArr = arr.map((item) => Number(item.id))
      this.$refs['prdTree'].setCheckedKeys(keysArr)
      this.$emit('update:selectedTag', arr)
    },
    // 选中的节点删除，手动取消勾选
    cancelCheck(node) {
      this.$refs.prdTree.setChecked(Number(node.id), false, false)
    },
    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 搜索框变化时执行
    filterList(val) {
      this.$refs.prdTree.filter(val)
    },
    // 选中节点回显
    showBackNodes() {
      this.defaultCheckedKeys = this.selectedTag
        .filter((item) => {
          return item.property === 'prd'
        })
        .map((item) => {
          return Number(item.id)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.prd-tab {
  position: relative;

  .tab-item-scroll {
    height: 100%;

    &--multiple {
      height: calc(100% - 30px);
    }
  }

  &__bottom {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 30px;
    border-top: 1px solid $line-input;
  }

  &__label {
    padding: 5px 0 5px 15px;
  }
}
</style>
