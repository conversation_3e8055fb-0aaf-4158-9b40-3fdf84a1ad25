<!--
 * @Description: 核心表格td-- 仅仅适用于小表哥
 -->
<template>
  <div class="base-table-td">
    <template v-if="listMode">
      <el-form>
        <template v-if="isSubfield">
          <template v-for="(item, index) in subFormVal">
            <div :key="index" class="base-table-td__value-warp">
              <div v-if="fieldType !== 4" class="list-mode--item">
                <span>{{ item[attr] }}</span>
              </div>
              <component
                :is="fieldInfo | componentValue"
                v-else
                :field-info="fieldInfo"
                :form-data="item"
                :is-design="false"
                :is-edit="true"
                :is-see="true"
                mode="sub"
                :no-link="noLink"
                :readonly="true"
                :show-label="false"
                :show-message="false"
                :show-title="false"
                :sub-attr="attr"
                @linkItemClick="linkItemClick"
              >
              </component>
            </div>
          </template>
        </template>
        <template v-else>
          <div class="base-table-td__value-warp">
            <div v-if="!/^(4|10014|10015|6|8)$/.test(fieldType)" class="listMode">
              {{ row[attr] }}
            </div>
            <component
              :is="fieldInfo | componentValue"
              v-else
              :field-info="fieldInfo"
              :form-data="row"
              :is-design="false"
              :is-edit="true"
              :is-see="true"
              mode="sub"
              :no-link="noLink"
              :readonly="true"
              :show-label="false"
              :show-message="false"
              :show-title="false"
              :sub-attr="attr"
              @linkItemClick="linkItemClick"
            >
            </component>
          </div>
        </template>
      </el-form>
    </template>
    <el-form v-else>
      <template v-if="isSubfield">
        <template v-for="(item, index) in subFormVal">
          <div :key="index" class="base-table-td__value-warp">
            <component
              :is="fieldInfo | componentValue"
              :field-info="fieldInfo"
              :form-data="item"
              :is-design="false"
              :is-edit="true"
              :is-see="true"
              mode="sub"
              :no-link="noLink"
              :readonly="true"
              :show-label="false"
              :show-message="false"
              :show-title="false"
              :sub-attr="attr"
            >
            </component>
          </div>
        </template>
      </template>
      <template v-else>
        <template v-if="fieldType === 800000">
          <el-tooltip :content="tagContent" :disabled="!tagContent">
            <div class="base-table-td__value-warp">
              <component
                :is="fieldInfo | componentValue"
                v-if="getComponentValue(fieldInfo)"
                :field-info="fieldInfo"
                :form-data="row"
                :is-design="false"
                :is-edit="true"
                :is-see="true"
                :link-show="true"
                mode="sub"
                :no-link="noLink"
                :readonly="true"
                :show-label="false"
                :show-message="false"
                :show-title="false"
                :sub-attr="attr"
              >
              </component>
            </div>
          </el-tooltip>
        </template>
        <div v-else class="base-table-td__value-warp">
          <component
            :is="fieldInfo | componentValue"
            v-if="getComponentValue(fieldInfo)"
            :field-info="fieldInfo"
            :form-data="row"
            :from-link-table="fromLinkTable"
            :is-design="false"
            :is-edit="true"
            :is-see="true"
            mode="sub"
            :no-link="noLink"
            :readonly="true"
            :show-label="false"
            :show-message="false"
            :show-title="false"
            :sub-attr="attr"
          >
          </component>
        </div>
      </template>
    </el-form>
  </div>
</template>

<script>
import { fieldsMap } from '@/constants/common'

export default {
  name: 'BaseTableTd',
  filters: {
    // 计算动态组件选择的组件名称
    componentValue({ fieldType, saasAttr }) {
      // 如果是流水号暂时以当行文本展示
      if (fieldType === 10019) {
        return fieldsMap[1]['componentsName'] + 'FormData'
      }
      // if (saasAttr === 'address') {
      //   return fieldsMap[1]['componentsName'] + 'FormData'
      // }
      if (fieldType === 20010) {
        return fieldsMap[1]['componentsName'] + 'FormData'
      }
      if (fieldType === 20009) {
        return fieldsMap[1]['componentsName'] + 'FormData'
      }
      return fieldsMap[fieldType] && fieldsMap[fieldType]['componentsName'] + 'FormData'
    }
  },
  props: {
    listMode: {
      default: false
    },
    noLink: {
      default: false
    },
    row: {},
    subFormInfo: {
      // 所属父表单字段详细
      default: () => null
    },
    fieldInfo: {
      // 当前字段详细
      default: () => {
        return {}
      }
    },
    // 是否是在关联字段的选择弹窗里用到这里用来判断某些关联字段是不是需要校验
    fromLinkTable: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    fieldType() {
      return this.fieldInfo.fieldType
    },
    subFormVal() {
      let res = []
      if (this.subFormInfo) {
        res = this.row[this.subAttr]
      }
      return res
    },
    subAttr() {
      return this.subFormInfo && this.subFormInfo.attr
    },
    attr() {
      return this.fieldInfo.attr
    },
    isSubfield() {
      let res = false
      if (this.subFormInfo) {
        res = true
      }
      return res
    },
    // 标签展示内容
    tagContent() {
      return (this.row[this.attr] && this.row[this.attr].map((item) => item.name).join(';')) || ''
    }
  },
  methods: {
    getComponentValue({ fieldType, saasAttr }) {
      // 如果是流水号暂时以当行文本展示
      if (fieldType === 10019) {
        return fieldsMap[1]['componentsName'] + 'FormData'
      }
      // if (saasAttr === 'address') {
      //   return fieldsMap[1]['componentsName'] + 'FormData'
      // }
      if (fieldType === 20010) {
        return fieldsMap[1]['componentsName'] + 'FormData'
      }
      if (fieldType === 20009) {
        return fieldsMap[1]['componentsName'] + 'FormData'
      }
      return fieldsMap[fieldType] && fieldsMap[fieldType]['componentsName'] + 'FormData'
    }
  }
}
</script>

<style lang="scss">
.base-table-td {
  width: 100%;
  overflow: hidden;
  & > .el-form {
    & > .base-table-td__value-warp {
      box-sizing: content-box;
      height: 32px;
      padding: 2px 0;
      overflow: hidden;
      font-size: 12px;
      border-bottom: 1px solid $neutral-color-3;
      .listMode {
        @include singleline-ellipsis;
      }
      &:last-child {
        border: none;
      }
      & > .el-form-item {
        margin-bottom: 0 !important;
      }
      & > .list-mode--item {
        display: flex;
        align-items: center;
        height: 100%;
        & > span {
          @include singleline-ellipsis;
        }
        @include singleline-ellipsis;
      }
      .input-text {
        .text_left {
          white-space: pre;
        }
      }
    }
  }
}
</style>
