@import '../mixins/mixins';

.v-modal-enter {
  animation: v-modal-in 0.2s ease;
}

.v-modal-leave {
  animation: v-modal-out 0.2s ease forwards;
}

@keyframes v-modal-in {
  0% {
    opacity: 0;
  }
  100% {
  }
}

@keyframes v-modal-out {
  0% {
  }
  100% {
    opacity: 0;
  }
}

.v-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: $text-main;
  opacity: 0.5;
}

@include b(popup-parent) {
  @include m(hidden) {
    overflow: hidden;
  }
}
