<!--
 * @Description: 产品分类设置
 -->
<template>
  <div class="product-class-setting">
    <v-top-bottom-page>
      <div slot="layout-title" class="header">
        <span class="el-icon-arrow-left header-back" @click="$router.go(-1)"></span>
        <span class="header-title">{{
          $t('product.productClassSetting.productClassificationSettings')
        }}</span>
      </div>
      <div slot="layout-column" class="content">
        <!-- 模糊搜索 -->
        <div class="content-main">
          <div class="content-main-box">
            <div class="menu-search">
              <el-input
                v-model="nameLike"
                clearable
                :placeholder="$t('placeholder.inputPls', { attr: $t('nouns.categoryName') })"
                prefix-icon="el-icon-search"
                @clear="clearSearch"
                @keyup.enter.native="menuSearch"
              >
              </el-input>
            </div>
            <div ref="menuList" class="menu-operate">
              <!-- 菜单搜索列表 -->
              <!-- <template v-if="isSearch">
                <div v-if="!searchResult.length" class="search-empty">未搜索到名字中包括“{{nameLike}}”的分类</div>
                <template v-else>
                  <div class="menu-item"
                    v-for="item in searchResult"
                    :key="item.id">
                    <div class="menu-title">
                      <span class="menu-title-text">
                        <span>{{item.name}}</span>
                      </span>
                      <classHandle
                        v-if="item.id === getCurrentNode.id"
                        :currentClass="item">
                      </classHandle>
                    </div>
                  </div>
                </template>
              </template> -->
              <!-- 菜单操作 -->
              <template>
                <v-tree
                  ref="nodeTree"
                  :expand-all="true"
                  :is-search="isSearch"
                  :menu-list.sync="menuList"
                  @dragEnd="dragEnd"
                  @getClassList="getClassList"
                ></v-tree>
              </template>
            </div>
            <!-- 新建分组 -->
            <div class="menu-new">
              <div>
                <!-- 一级分组上限为100 -->
                <el-button
                  class="menu-new__btn"
                  :disabled="menuList.length >= 100"
                  type="text"
                  @click="openDialog"
                >
                  <i class="menu-new__icon web-icon-addFile web-iconfont"></i
                  >{{ $t('product.productClassSetting.addALevel1Classification') }}
                </el-button>
                <span v-if="menuList.length >= 100" class="menu-new__tip">{{
                  $t('product.productClassSetting.maxLimited')
                }}</span>
              </div>
              <span
                v-if="getMenuUnfoldList.length"
                class="menu-new__allfold"
                @click="updateMenuUnfold('')"
                >{{ $t('product.productClassSetting.collapseAll') }}</span
              >
              <span v-else class="menu-new__allfold" @click="foldMenuList">{{
                $t('product.productClassSetting.unfoldAll')
              }}</span>
            </div>
          </div>
          <transition name="fade">
            <div v-if="visibleOpt" class="content-main__right">
              <div class="content-main__content">
                <header class="content-main__header">
                  <p>
                    <span v-if="switchData.name">{{ switchData.name }} -</span>
                    <span>
                      {{ $t('operation.groupOption') }}
                    </span>
                  </p>
                </header>
                <p class="content-main__tips">
                  {{ $t('message.confirmTitle') }}: {{ $t('product.newProductSet') }}
                </p>
                <div v-if="switchData.loading" class="content-main__loading">
                  {{ $t('message.loading') }}...
                </div>
                <template v-else>
                  <div v-if="switchData.enableBatchShelfManagement" class="switch__box">
                    <el-switch
                      v-model="switchData.enableBatchShelfLife"
                      :active-value="1"
                      :inactive-value="0"
                      @change="updateSetup($event, 0)"
                    >
                    </el-switch>
                    <span>{{ $t('message.isOpened') }}{{ $t('business.jxcBatch') }}</span>
                  </div>
                  <div
                    v-if="
                      switchData.enableBatchShelfLife === 1 && switchData.enableBatchShelfManagement
                    "
                    class="content-main__input"
                  >
                    <el-form ref="optionForm" label-position="right" label-width="180px">
                      <el-form-item :label="$t('business.shelfLifeDays')">
                        <el-input
                          v-model="switchData.shelfLifeDays"
                          clearable
                          @blur="checkAlertDays"
                          @input="checkNum(0)"
                        />
                      </el-form-item>
                      <el-form-item :label="$t('business.expirationAlertDays')">
                        <el-input
                          v-model="switchData.expirationAlertDays"
                          clearable
                          @blur="checkAlertDays"
                          @input="checkNum(1)"
                        />
                      </el-form-item>
                    </el-form>
                  </div>
                  <div v-if="switchData.enableSeqManagement" class="switch__box">
                    <el-switch
                      v-model="switchData.enableSerialNumber"
                      :active-value="1"
                      :inactive-value="0"
                      @change="updateSetup($event, 1)"
                    >
                    </el-switch>
                    <span>{{ $t('message.isOpened') }}{{ $t('business.jxcSerialNumber') }}</span>
                  </div>
                  <!-- 多单位 -->
                  <div class="switch__box">
                    <p>{{ $t('multiUnit.productUnit') }}</p>
                    <template
                      v-if="switchData.enableMultiUnit && switchData.enableMultiUnitManagement"
                    >
                      <!-- 多单位 -->
                      <productUnit
                        ref="productMuiltUnit"
                        v-model="switchData.unitValue"
                        class="productUnit"
                        :field-info="fieldInfo"
                        :multi-items="switchData.multiItems"
                      ></productUnit>
                    </template>
                    <template v-else>
                      <!-- 单单位 -->
                      <el-select
                        v-model="modelVal"
                        class="productUnit"
                        clearable
                        filterable
                        :placeholder="$t('multiUnit.pleaseChoose')"
                      >
                        <el-option
                          v-for="item in switchData.singleItems"
                          :key="item.value"
                          :label="item.text"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </template>
                    <template v-if="switchData.enableMultiUnitManagement">
                      <el-switch
                        v-model="switchData.enableMultiUnit"
                        :active-value="1"
                        :inactive-value="0"
                        @change="switchUnit"
                      >
                      </el-switch>
                      <span>{{ $t('multiUnit.enableMultiUnit') }}</span>
                    </template>
                  </div>
                </template>
              </div>
              <footer class="content-main__footer">
                <span style="margin-right: 10px">{{ $t('operation.operate') }}: </span>
                <el-button type="primary" @click="confirm">{{ $t('operation.confirm') }}</el-button>
                <el-button @click="visibleOpt = false">{{ $t('operation.cancel') }}</el-button>
              </footer>
            </div>
          </transition>
        </div>
        <div class="footer">
          <el-button @click="$router.go(-1)">{{ $t('operation.back') }}</el-button>
          <!-- <el-button @click="cancelSort">{{ $t('operation.cancel') }}</el-button>
          <el-button type="primary" @click="saveSort">{{ $t('operation.save') }}</el-button> -->
        </div>
      </div>
      <template slot="layout-dialog">
        <classHandleDialog :dialog-visible.sync="dialogVisible" @submit="submit">
          <span slot="title">{{
            $t('product.productClassSetting.createANewLevel1Classification')
          }}</span>
        </classHandleDialog>
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import vTopBottomPage from '@/components/layout-page/v-layout'
import vTree from './components/class-tree-node'
import productUnit from '@/components/all-fields/form-data-edit/SelectRadio/product-unit.vue' // 多单位
import {
  addProductClass,
  getProductClass,
  productClassSort,
  productClassUpdate,
  productClassDelete,
  getCategoryProperty,
  saveCategoryProperty
} from '@/api/product-class.js'
import productClassMixin from './mixins/product-class.js'
import bus from '@/utils/temp-vue'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'ProductClassSetting',

  components: {
    VTopBottomPage: vTopBottomPage,
    VTree: vTree,
    ProductUnit: productUnit
  },

  mixins: [productClassMixin],

  provide() {
    return {
      jxcSet: this.getSet,
      getClassList: this.getClassList
    }
  },

  data() {
    return {
      fieldInfo: {
        editable: 1
      },
      nameLike: '', // 模糊搜索
      menuList: [], // 菜单列表
      catchMenuList: [],
      operation: 0, // 当前要执行的操作
      info: {}, // 当前分组的信息
      switchData: {
        loading: true
      }, // 开关属性
      searchResult: [], // 菜单搜索结果
      jxcSet: null, // 是否开启进销存
      isSearch: false, // 是否是搜索状态
      unfold: [], // 展开的一级菜单
      groupingShow: false, // 是否显示新建分组弹窗
      setOptionShow: false, // 是否显示分组设置项
      visibleOpt: false // 是否显示右侧配置项
    }
  },

  beforeDestroy() {
    this.$root.eventHub.$off('menuScrollBottom')
    bus.$off('groupSetupChange')
  },

  created() {
    this.$root.eventHub.$on('menuScrollBottom', () => {
      const top = this.$refs['menuList'].scrollHeight
      utils.scrollTo(this.$refs['menuList'], top)
    })
  },

  mounted() {
    bus.$on('groupSetupChange', (e) => {
      this.group(e)
    })
    this.init()
  },

  computed: {
    ...mapGetters(['getMenuUnfoldList']),
    modelVal: {
      get() {
        // 遍历数组查找switchData.unitValue是否存在数组中，若不存在则置空
        const unit = this.switchData.singleItems.filter(
          (item, index) => item.value === this.switchData.unitValue
        )
        return unit.length ? unit[0].value : ''
      },
      set(val) {
        this.switchData.unitValue = val
      }
    }
  },

  methods: {
    ...mapActions(['updateMenuUnfold']),
    init() {
      this.getClassList()
    },

    getSet() {
      return this.jxcSet
    },
    // 开关多单位时清空下拉框数据
    switchUnit(event) {
      this.switchData.unitValue = ''
    },
    // 校验正整数
    checkNum(type) {
      if (type === 0) {
        this.switchData.shelfLifeDays = this.switchData.shelfLifeDays.replace(/\D/g, '')
      } else {
        this.switchData.expirationAlertDays = this.switchData.expirationAlertDays.replace(/\D/g, '')
      }
    },

    checkAlertDays() {
      if (
        !this.switchData.shelfLifeDays &&
        +this.switchData.shelfLifeDays !== 0 &&
        this.switchData.expirationAlertDays
      ) {
        this.switchData.expirationAlertDays = null
        this.$message.error(this.$t('formDataEdit.expirationDaysBeNull'))
      }
    },

    // 保存分类设置
    confirm() {
      if (this._locked) return
      this._locked = true
      if (!this.switchData.enableBatchShelfLife) {
        // 若未启用批次保质期
        this.switchData.shelfLifeDays = null
        this.switchData.expirationAlertDays = null
      } else {
        // 若启用 未填写传null 已填写则转换int类型
        this.switchData.shelfLifeDays =
          this.switchData.shelfLifeDays || parseInt(this.switchData.shelfLifeDays) === 0
            ? +this.switchData.shelfLifeDays
            : null
        this.switchData.expirationAlertDays =
          this.switchData.expirationAlertDays || parseInt(this.switchData.expirationAlertDays) > 0
            ? +this.switchData.expirationAlertDays
            : null
        // 校验规则
        if (this.switchData.expirationAlertDays === 0) {
          this._locked = false
          this.$message.error(this.$t('product.expirationAlertDays'))
          return
        }
        if (
          this.switchData.expirationAlertDays &&
          this.switchData.shelfLifeDays <= this.switchData.expirationAlertDays
        ) {
          this._locked = false
          this.$message.error(this.$t('product.optionsDays'))
          return
        }
      }
      let value = ''
      if (this.switchData.enableMultiUnitManagement && this.switchData.enableMultiUnit) {
        // 多单位
        value = this.switchData.unitValue && this.switchData.unitValue.value
      } else {
        // 单单位
        value = this.switchData.unitValue
      }
      saveCategoryProperty({ ...this.switchData, unitValue: value })
        .then(() => {
          this.$message.success(this.$t('message.operateSuccessSymbol'))
        })
        .finally(() => {
          this._locked = false
        })
    },

    // 切换分类设置
    updateSetup(e, index) {
      if (index === 0 && e === 1 && this.switchData.enableSerialNumber === 1) {
        this.switchData.enableBatchShelfLife = 0
        this.$message.error(
          this.$t('product.isOpenProduct', {
            firstName: this.$t('business.jxcSerialNumber'),
            secondName: this.$t('business.jxcBatch')
          })
        )
      } else if (index === 1 && e === 1 && this.switchData.enableBatchShelfLife === 1) {
        this.switchData.enableSerialNumber = 0
        this.$message.error(
          this.$t('product.isOpenProduct', {
            firstName: this.$t('business.jxcBatch'),
            secondName: this.$t('business.jxcSerialNumber')
          })
        )
      }
    },

    group(e) {
      this.visibleOpt = true
      this.switchData.loading = true
      getCategoryProperty({ categoryId: e.id }).then(({ result }) => {
        this.switchData = result
        this.switchData.name = e.name
        this.switchData.categoryId = e.id
        console.log('this.switchData.unitValue', this.switchData.unitValue)
        // 初始化多单位下拉
        if (this.switchData.enableMultiUnitManagement && this.switchData.enableMultiUnit) {
          const productUnit = this.switchData.multiItems.filter((item) => {
            return item.value === this.switchData.unitValue
          })
          console.log('productUnit', productUnit)
          // this.switchData.unitValue = productUnit[0]
          // 已选OR未选时候回显
          let selectUnit = {}
          if (productUnit[0]) {
            selectUnit = productUnit[0]
          }
          this.$nextTick(() => {
            this.$refs.productMuiltUnit.getProductUnit(selectUnit)
          })
        }
      })
    },
    /**
     * @description: 展开全部菜单
     * @param {type}
     * @return:
     */
    foldMenuList() {
      // 获取所有父节点菜单id
      const menuIdList = this.getMenuIdList(this.menuList)
      let flatIdList
      // 数组扁平化
      flatIdList = menuIdList
        .toString()
        .split(',')
        .map((item) => {
          return Number(item)
        })
      // 数组去重
      flatIdList = Array.from(new Set(flatIdList))
      // while (menuIdList.some(item => Array.isArray(item))) {
      //   menuIdList = [].concat(...menuIdList)
      // }
      this.updateMenuUnfold(flatIdList)
    },

    /**
     * @description: 扁平化菜单
     * @param {type}
     * @return:
     */
    getMenuIdList(list) {
      return list.map((item) => {
        if (item.childList.length) {
          return [...this.getMenuIdList(item.childList), item.parentId]
        } else {
          return item.parentId
        }
      })
    },

    // 打开操作弹窗
    openDialog() {
      this.dialogVisible = true
      // this.$refs['nodeTree'].handleNode('addBrother')
    },

    submit(val) {
      const params = {
        parentId: 0,
        name: val
      }
      addProductClass(params)
        .then((data) => {
          this.getClassList()
            .then(() => {
              this.$root.eventHub.$emit('menuScrollBottom')
              // let top = this.$refs['menuList'].scrollHeight
              // utils.scrollTo(this.$refs['menuList'], top)
            })
            .catch(() => {})
          this.$message({
            message: data.msg || this.$t('message.newSuccess'),
            type: 'success'
          })
        })
        .catch(() => {})
    },

    clearSearch() {
      this.isSearch = false
      this.getClassList()
    },

    /**
     * @description: 取消保存
     * @param {type}
     * @return:
     */
    cancelSort() {
      this.menuList = xbb.deepClone(this.catchMenuList)
    },

    /**
     * @description: 排序的保存
     * @param {type}
     * @return:
     */
    saveSort() {
      const params = {
        productCategoryList: this.exchangeMenuList(this.menuList, 0)
      }
      productClassSort(params)
        .then((data) => {
          this.$message({
            message: data.msg,
            type: 'success'
          })
        })
        .finally(() => {
          this.getClassList()
        })
    },

    exchangeMenuList(arr, parentId) {
      return arr.map((item) => {
        item.parentId = parentId
        item.childList && (item.childList = this.exchangeMenuList(item.childList, item.id))
        return item
      })
    },

    // 模糊搜索
    menuSearch() {
      this.nameLike = this.nameLike.replace(/\s+/g, '')
      this.menuList = []
      const params = {
        nameLike: this.nameLike
      }
      this.getClassList(params)
      this.isSearch = true
    },

    dragEnd(val) {
      this.saveSort()
      // this.menuList = val
      // debugger
      console.log(val)
    },

    // 获取产品分类列表
    getClassList(params = {}) {
      return new Promise((resolve, reject) => {
        getProductClass(params)
          .then((data) => {
            // this.menuList = data.result.productCategoryList
            this.jxcSet = data.result.jxcEnable
            this.menuList = this.exchangeClassList(data.result.productCategoryList)
            this.catchMenuList = xbb.deepClone(this.menuList)
            resolve()
          })
          .catch(() => {})
      })
    },
    exchangeClassList(list) {
      return list.map((item) => {
        if (item.childList) {
          item.childList = this.exchangeClassList(item.childList)
        } else {
          item.childList = []
        }
        return item
      })
    },
    sortClass() {
      productClassSort()
    },
    updateClass() {
      productClassUpdate()
    },
    deleteClass() {
      productClassDelete()
    }
  },

  watch: {
    // menuList: {
    //   handler (val) {
    //     console.log(val, 'menulist')
    //   },
    //   deep: true
    // }
  }
}
</script>

<style lang="scss">
.product-class-setting {
  .menu-search {
    .el-input--small {
      & .el-input__inner {
        background-color: $bg-tag;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$theme-color: $brand-color-5;
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.product-class-setting {
  display: flex;
  height: 100%;
  .header {
    &-back {
      // font-weight: 900;
      // font-size: 30px;
      cursor: pointer;
      &:hover {
        color: $link-base-color-6;
      }
    }
    &-title {
      color: $text-plain;
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    width: 100%;
    &-main {
      display: flex;
      flex: auto;
      min-height: 0;
      background-color: $neutral-color-2;
      &__input {
        color: $text-main;
        .el-input {
          width: 40%;
          margin-left: 10px;
        }
      }
      &__loading {
        padding: 20px;
        font-size: 13px;
      }
      &__tips {
        padding: 14px 20px;
        margin: 20px 10px;
        margin-bottom: 15px;
        font-size: 14px;
        color: $text-main;
        background-color: $neutral-color-1;
        border-radius: 4px;
      }
      &__right {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-left: 10px;
        background-color: $base-white;
        border: 1px solid $neutral-color-3;
      }
      &__header {
        height: 44px;
        padding: 0 10px;
        line-height: 44px;
        border-bottom: 2px solid $neutral-color-2;
        p {
          display: inline-block;
          border-bottom: 2px solid $theme-color;
        }
      }
      &__content {
        height: 100%;
        .switch__box {
          display: flex;
          align-items: center;
          padding: 10px;
          margin-left: 20px;
          font-size: 14px;
          .productUnit {
            margin: 0 20px;
          }
          span {
            margin-left: 20px;
          }
        }
      }
      &__footer {
        height: 50px;
        padding: 0 10px;
        line-height: 50px;
        border-top: 1px solid $neutral-color-3;
      }
      &-box {
        box-sizing: border-box;
        display: flex;
        flex: 0 0 520px;
        flex-direction: column;
        width: 520px;
        background: $base-white;
        border: 1px solid $neutral-color-3;
        .menu-search {
          box-sizing: border-box;
          height: 60px;
          padding: 12px 20px;
        }
        .menu-operate {
          position: relative;
          flex: 1;
          overflow-x: hidden;
          overflow-y: auto;
          .menu-item {
            line-height: 40px;
            cursor: pointer;
            &--clone {
              background: rgba(255, 142, 61, 0.4);
            }
          }
        }
        .menu-new {
          display: flex;
          justify-content: space-between;
          height: 50px;
          padding: 0 20px;
          font-size: 12px;
          line-height: 50px;
          color: $neutral-color-9;
          background: $base-white;
          border-top: 1px solid $neutral-color-3;
          &__btn {
            color: $neutral-color-9;
          }
          &__icon {
            padding-right: 8px;
            font-size: 14px;
          }
          &__tip {
            padding-left: 5px;
            font-size: 12px;
            color: $error-base-color-6;
          }
          &__allfold {
            cursor: pointer;
          }
        }
      }
    }
    .footer {
      display: flex;
      flex: none;
      align-items: center;
      justify-content: center;
      height: 70px;
      border-top: 1px solid $neutral-color-3;
    }
  }
}
</style>
