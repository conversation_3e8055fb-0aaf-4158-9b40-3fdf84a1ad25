<!--
 * @Description: 通话记录页面
 -->
<template>
  <div class="call-log list">
    <list-head :placeholder="$t('callCenter.etcSearch')" @searchEmit="search">
      <span slot="title">{{ $t('business.callRecord') }}</span>
      <template slot="options">
        <!-- <el-button @click="routeTo('/callCenter/agent')" v-if="getCTIRole.isAdmin">{{ $t('business.seatsManage') }}</el-button> -->
        <!-- <el-button @click="routeTo('/callCenter/setting')">{{ $t('callCenter.moreSet') }}</el-button> -->
      </template>
    </list-head>

    <div class="content">
      <div class="content-head">
        <el-radio-group v-model="callType" fill="#ff8c2e" @change="handleCallType">
          <el-radio-button label="">{{ $t('callCenter.allCall') }}</el-radio-button>
          <el-radio-button :label="1">{{ $t('callCenter.callOutRecord') }}</el-radio-button>
          <el-radio-button :label="0">{{ $t('callCenter.callInRecord') }}</el-radio-button>
          <el-radio-button :label="2">{{ $t('callCenter.missedCalls') }}</el-radio-button>
        </el-radio-group>
        <div class="list-panel">
          <div v-if="getCTIRole.isAdmin || getCTIRole.isSupervisor" class="form-item">
            <div class="list-filter">
              <label class="label">{{ $t('callCenter.filterRange') }}</label>
              <el-input
                v-model="scopeName"
                class="scope-filter"
                :placeholder="$t('placeholder.choosePls', { attr: '' })"
                readonly
                size="small"
                @click.native="scopeFilter"
              >
              </el-input>
              <i v-if="scopeName" class="el-icon-close" @click="clearSelect"></i>
              <!-- <div class="dropbox-wrapper">
                <transition name="fade">
                  <v-dropbox v-if="scopeShow" @staff="staffSelect" @department="departmentSelect"></v-dropbox>
                </transition>
              </div> -->
            </div>
          </div>

          <div class="form-item">
            <div class="list-filter">
              <label class="label">{{ $t('callCenter.chooseDate') }}</label>
              <el-date-picker
                v-model="filter.dateRange"
                align="right"
                class="date-range"
                :clearable="false"
                date-selected="dateSelected"
                :end-placeholder="$t('label.endTime')"
                :picker-options="pickerOptions"
                size="small"
                :start-placeholder="$t('label.startTime')"
                type="daterange"
                unlink-panels
                value-format="yyyy-MM-dd"
                @blur="clearSelectDateLimit"
                @change="handleDataRange"
              >
              </el-date-picker>
            </div>
          </div>

          <div v-if="isAudioTranslateEnable" class="form-item">
            <div class="list-filter">
              <label class="label">{{ $t('callCenter.translateCase') + ':' }}</label>
              <el-select
                v-model="isAudioTranslated"
                class="scope-filter"
                :placeholder="$t('placeholder.choosePls', { attr: '' })"
                size="small"
                @change="getList"
              >
                <el-option
                  v-for="item in AudioTranslatedOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <el-table
        v-loading="loading"
        border
        class="call-list"
        :data="callList"
        :height="tableHeight"
        style="width: 100%"
      >
        <el-table-column
          :label="$t('callCenter.callObject')"
          min-width="150"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-tooltip
              class="icon"
              :content="$t('callCenter.callInPhone')"
              effect="light"
              placement="top"
            >
              <span class="icon">
                <i v-if="!scope.row.type" class="web-icon-callin web-iconfont"></i>
              </span>
            </el-tooltip>
            <span
              v-if="scope.row.refId"
              class="link"
              :class="{ active: scope.row.refId }"
              @click="openDetail(scope.row)"
              >{{ scope.row.refName }}</span
            >
            <span v-else>
              <span>{{ scope.row.refName }}</span>
              <el-button size="small" type="primary" @click="addNew(scope.row)">{{
                $t('callCenter.turnCustomer')
              }}</el-button>
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('callCenter.seat')" min-width="70" prop="username">
          <template slot-scope="scope">
            <span>
              {{ scope.row.username }}
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('callCenter.callTime')" min-width="70" prop="duration">
          <template slot-scope="scope">
            <span v-if="scope.row.result">
              {{ callingStatus(scope.row) }}
            </span>
            <span v-else class="red">
              {{ $t('callCenter.communicateFail') }}
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('callCenter.callTape')" width="450">
          <template slot-scope="scope">
            <span v-if="scope.row.result !== 1">{{ $t('unit.no') }}</span>
            <template v-else-if="!scope.row.recordUrl">
              <span
                v-if="scope.row.moduleType === 3602"
                class="link"
                @click="getRecorUrl(scope.row)"
                >{{ $t('callCenter.manualGet') }}</span
              >
              <span v-else class="link" @click="getRecorUrl(scope.row)"></span>
            </template>
            <div v-else-if="scope.row.recordUrl.indexOf('http') > -1">
              <a
                :download="downloadMp3FileName(scope.row.recordUrl)"
                :href="downloadMp3(scope.row.recordUrl)"
                >{{ $t('callCenter.mp3') }}</a
              >
              <audio class="audio" controls="controls" :src="scope.row.recordUrl"></audio>
              <a v-if="isAudioTranslateEnable" @click="audioTranslate(scope.row)">{{
                scope.row.translateStatus
              }}</a>
            </div>
            <span v-else>{{ scope.row.recordUrl }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('callCenter.recordUrlStatus')"
          min-width="80"
          prop="callTimeShow"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.recordUrlStatus }}
            </span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('callCenter.callOutTime')" min-width="110" prop="callTimeShow">
          <template slot-scope="scope">
            <span>
              {{ scope.row.callTimeShow }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-bot">
        <ul class="options"></ul>

        <v-pagination
          :current-page.sync="page.currentPage"
          layout="slot, sizes, prev, pager, next, jumper"
          :page-size="page.pageSize"
          :page-sizes="[20, 30, 50, 100]"
          :total="page.total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        >
        </v-pagination>
        <!-- <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="page.currentPage"
          :page-sizes="[20, 30, 50, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total">
        </el-pagination> -->
      </div>
    </div>

    <!-- 新建 -->
    <add-new
      v-if="addnewShow"
      ref="addNew"
      :addnew-prop="addnewProp"
      @closeDialog="addnewShow = false"
      @refresh="getList"
    >
    </add-new>

    <!-- 详情 -->
    <transition name="slide">
      <detail
        v-if="detailShow"
        :id="detailId"
        ref="detail"
        :module-name="popModel"
        @addNew="newWorkFlow"
        @close="closeDetail"
        @edit="editItem"
        @recall="handleRecall"
        @refresh="getList"
      >
      </detail>
    </transition>
    <transition name="fade">
      <div v-if="detailShow" class="detail-bg" @click="detailShow = false"></div>
    </transition>

    <!-- 人员部门选择 -->
    <person-select
      :default-val.sync="userOrDep"
      :dialog-visible.sync="scopeShow"
      :show-tabs-name="['dept', 'user']"
      :tab-multiple="false"
    >
    </person-select>

    <!-- 语音转写弹窗 -->
    <el-dialog
      :title="$t('callCenter.voiceToContent')"
      :visible.sync="audioTranslateDialogVisible"
      width="45%"
    >
      <div v-if="audioContents.length > 0" class="audio-body">
        <p class="audio-center-content">{{ formatDateTime(audioContents[0].time) }}</p>
        <div v-for="(it, index) in audioContents" :key="index">
          <div v-if="it.speaker === '1'" style="width: 100%">
            <p class="audio-left-speaker audio-time" style="width: 100%">
              {{ formatDateSeconds(it.time) }}
            </p>
            <div class="audio-left-div">
              <div class="audio-left-speaker" style="width: 5%">A:&gt;</div>
              <div class="audio-left-speaker" style="width: 94%">
                <div class="audio-left-content">{{ it.content }}</div>
              </div>
            </div>
          </div>
          <div v-else style="width: 100%">
            <p class="audio-right audio-right-speaker audio-time" style="width: 100%">
              {{ formatDateSeconds(it.time) }}
            </p>
            <div class="audio-right audio-right-div">
              <div class="audio-right-speaker" style="width: 94%">
                <div class="audio-right-content">{{ it.content }}</div>
              </div>
              <div class="audio-right-speaker" style="width: 5%">&lt;:B</div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="audioTranslateDialogVisible = false">{{
          $t('operation.close')
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { callLogList, callLogRecordStatic, audioTranslate } from '@/api/callCenter'
// import { LS } from '@/utils/index'
import { mapGetters, mapActions, mapMutations } from 'vuex'
import { Message, MessageBox } from 'element-ui'
// import Clickoutside from 'element-ui/lib/utils/clickoutside'
// import addNew from '@/components/modal/add-new'
// import detail from '@/components/list/detail'
import listHead from './components/list-head'
// import vDropbox from '@/components/common/dropbox'
import PersonSelect from '@/components/person/person-select.vue'
import xbb from '@xbb/xbb-utils'
import moment from 'moment'

const tableHeight = window.innerHeight - 295 - 13 - 20
let _min_date = null
let _max_date = null
const defaultDateRange = () => {
  return [moment().subtract(3, 'months').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
}

export default {
  name: 'CallLog',

  data() {
    return {
      // 筛选人员、部门
      userOrDep: [],
      page: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      tableHeight: tableHeight,
      nameLike: '',
      loading: false,
      callList: [], // 通话记录
      callType: '', // 通话类型
      // 呼叫状态 0失败 1成功
      result: '',
      // 筛选项
      filter: {
        depId: '',
        agentUserId: '',
        dateRange: defaultDateRange()
      },
      scopeName: '', // 范围筛选结果
      scopeShow: false,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$t('callCenter.latestThree'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('label.latestWeek'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('label.latestMonth'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ],
        /**
         * 确定是否应禁用给定日期。
         *
         * @param {Date} time - 要检查是否应禁用的日期
         * @return {Boolean} 如果应该禁用日期，则为true，否则为false
         */
        disabledDate(time) {
          if (_min_date && _max_date) {
            return time < _min_date || time > _max_date || time.getTime() > Date.now()
          }
          return time.getTime() > Date.now()
        },
        onPick({ minDate }) {
          if (minDate) {
            _min_date = moment(minDate).subtract(1, 'year')
            _max_date = moment(minDate).add(1, 'year')
          }
        }
      },
      addnewShow: false,
      addnewProp: {
        // id: id
      },
      detailShow: false,
      detailId: '',
      popModel: '',
      // 获取呼叫中心的moduleType
      dateSelected: false,
      moduleType: Number(utils.LS.get('VUE-CTI-moduleType')) || 3605,
      // 是否有使用翻译功能的权限
      isAudioTranslateEnable: false,
      // 是否显示语音转写弹窗
      audioTranslateDialogVisible: false,
      // 语音转写内容
      audioContents: [],
      // 筛选已经翻译好的通话记录
      isAudioTranslated: '',
      // 语音翻译完成筛选的筛选项
      AudioTranslatedOptions: [
        {
          value: '',
          label: this.$t('label.total')
        },
        {
          value: 'true',
          label: this.$t('callCenter.finished')
        },
        {
          value: 'false',
          label: this.$t('callCenter.notTranslate')
        }
      ]
    }
  },

  computed: {
    ...mapGetters(['getCTIRole'])
  },

  mounted() {
    this.result = this.$route.query.result
    if (this.result === '0') {
      this.callType = 2
    }
    this.getList()
  },

  methods: {
    ...mapActions(['setContactType', 'updateMissCallCount', 'formDataAdd']),
    ...mapMutations(['SET_DETAIL_DIALOG_SHOW', 'SET_DETAIL_QUERY']), // 获取通话状态
    callingStatus(row) {
      let status = ''
      switch (row.result) {
        case 1:
          status = this.formatTime(row.duration)
          break
        // 之前3是未接通，4是已挂断，现在合并了，后续如果要删再改
        case 3:
        case 4:
          status = this.$t('callCenter.callingFail') + '/' + this.$t('callCenter.callingOver')
          break
        default:
          break
      }
      return status
    },
    handleCallType(val) {
      this.userOrDep.length && this.userOrDep.splice(0, 1)
      this.scopeName = ''
      this.filter = {
        depId: '',
        agentUserId: '',
        dateRange: defaultDateRange()
      }
      if (val === 2) {
        this.result = 0
      } else {
        this.result = ''
      }
      console.log('result', this.result)
      this.getList()
    },

    // 获取通话记录
    getList() {
      this.loading = true
      // const url = '/callcenter/callLog/list.do'
      const params = {
        callSource: 0, // 用于区分老呼叫中心的呼叫记录
        moduleType: this.moduleType,
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        dateRange: this.filter.dateRange,
        depId: this.filter.depId,
        agentUserId: this.filter.agentUserId,
        type: this.getCallTypeCode(this.callType),
        fuzzySearch: this.nameLike,
        result: this.result,
        audioTranslated: this.isAudioTranslated
      }

      // 如果当前查询的是未接来电，则清空未接来电数
      if (Number(params.result) === 0) {
        this.updateMissCallCount(0)
      }

      // 过滤无效参数
      for (const key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }

      // api.post(url, params, (data) => {
      //   this.callList = data.result.callLogList
      //   for (let index in this.callList) {
      //     let log = this.callList[index]
      //     if (log.isTranslated) {
      //       log.translateStatus = this.$t('callCenter.viewTranslate')
      //     } else {
      //       log.translateStatus = this.$t('callCenter.translate')
      //     }
      //   }
      //   this.page.total = data.result.totalElements
      //   this.isAudioTranslateEnable = data.result.isAudioTranslateEnable
      //   this.loading = false
      // }, (errorData) => {
      //   this.loading = false
      // })
      callLogList(params)
        .then((data) => {
          this.callList = data.result.callLogList
          for (const index in this.callList) {
            const log = this.callList[index]
            if (log.isTranslated) {
              log.translateStatus = this.$t('callCenter.viewTranslate')
            } else {
              log.translateStatus = this.$t('callCenter.translate')
            }
          }
          console.log('callList', this.callList)
          this.page.total = data.result.totalElements
          this.isAudioTranslateEnable = data.result.isAudioTranslateEnable
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    // 路由跳转
    routeTo(path) {
      this.$router.push(path)
    },

    // 右上角搜索
    search(key) {
      this.page.currentPage = 1
      this.nameLike = key.trim()
      this.getList()
    },

    // 获取通话记录的状态
    getCallTypeCode(type) {
      if (type === 2) {
        return 0
      } else {
        return type
      }
    },

    // 手动获取通话记录
    getRecorUrl(row) {
      // const url = '/callcenter/callLog/record/static.do'
      const params = {
        moduleType: row.moduleType,
        callLogId: row.id,
        anotherPhoneNum: row.anotherPhoneNum,
        callTime: row.callTime
      }
      // api.post(url, params, (data) => {
      //   this.getList()
      // })
      callLogRecordStatic(params)
        .then((data) => {
          this.getList()
        })
        .catch(() => {})
    },

    // 数字转字符串
    arrToStr(arr) {
      return arr.join(', ')
    },

    // 监听日期筛选
    handleDataRange() {
      this.getList()
    },

    clearSelectDateLimit() {
      _max_date = null
      _min_date = null
    },

    // 弹出范围筛选框
    scopeFilter() {
      this.scopeShow = true
    },

    // 关闭范围筛选框
    closeDropbox() {
      this.scopeShow = false
    },

    // 清空范围筛选
    clearSelect() {
      this.filter.depId = ''
      this.filter.agentUserId = ''
      this.scopeName = ''
      this.userOrDep.splice(0, 1)
      this.getList()
    },
    // 坐席筛选
    staffSelect(id, name) {
      this.filter.depId = ''
      this.filter.agentUserId = id
      this.scopeName = name
      this.getList()
    },

    // 组织架构筛选
    departmentSelect(id, name) {
      this.filter.depId = id
      this.filter.agentUserId = ''
      this.scopeName = name
      this.getList()
    },

    // 改变页数
    handleSizeChange(num) {
      this.page.pageSize = num
      this.getList()
    },

    // 改变当前页码
    handleCurrentChange(num) {
      this.page.currentPage = num
      this.getList()
    },

    // 打开详情页
    openDetail(row) {
      // const modules = {
      //   '2': 'customerApi',
      //   '3': 'contactApi'
      // }

      // if (modules[row.refType]) {
      //   this.popModel = modules[row.refType]
      // } else {
      //   this.popModel = this.moduleName
      // }
      // this.detailId = row.refId
      // this.detailShow = true
      if (!row.refId) {
        return
      }
      if (!row.entity) {
        // 被删除的客户
        Message({
          type: 'error',
          message: this.$t('callCenter.notExist')
        })
        return
      }
      this['SET_DETAIL_DIALOG_SHOW'](true)
      this['SET_DETAIL_QUERY']({
        ...row.entity
      })
    },

    closeDetail() {
      this.detailShow = false
    },

    // 撤回
    handleRecall(id) {
      MessageBox.confirm(
        `${this.moduleMap[this.moduleName]}正在审批中，是否要进行撤回?`,
        this.$t('callCenter.recallConfirm'),
        {
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          // let url = `/approval/recall.html`
          // let params = {
          //   refId: id,
          //   refType: this.moduleName
          // }
          // api.post(url, params, (data) => {
          //   Message({
          //     type: 'success',
          //     message: this.$t('callCenter.recallSuccess')
          //   })
          //   this.getList()
          // })
        })
        .catch(() => {})
    },

    // 打开新建窗口
    openAddnew() {
      if (!this.addnewShow) {
        this.addnewShow = true
      } else {
        // 获取子组件的props
        const childAnp = this.$refs.addNew.addnewProp // 已经传给子组件的props
        const parentAnp = this.addnewProp // 即将传给子组件的props

        /*
         * childAnp 条件成立时，说明新建窗口被打开过，并且没有被销毁
         * childAnp.id，有id值，说明上一次是编辑操作
         * childAnp.module 有module，说明上一次操作是另一个模块的新建动作
         * parentAnp 存在id属性说明当前操作时编辑，不是新建
         * parentAnp 存在module属性说明当前要打开另一个模块的新建
         */
        if (
          (childAnp && childAnp.id) ||
          (childAnp && childAnp.module) ||
          parentAnp.id ||
          parentAnp.module
        ) {
          this.addnewShow = false
          this.$nextTick(() => {
            this.openAddnew()
          })
        } else {
          if (this.moduleName === 'customerApi') {
            const str = 'customerApi-item'
            this.setContactType(str)
          }
          this.$refs.addNew.dialogFormVisible = true
        }
      }
    },

    // 新建表单
    addNew(item) {
      // this.addnewProp = {
      //   phone: phone,
      //   module: 'customerApi'
      // }
      // this.openAddnew()
      // debugger
      this.formDataAdd({
        ...item.entity,
        saasSpecialParamPojo: {
          phoneNum: item.anotherPhoneNum
        }
      })
    },

    // 业务流转生成表单
    newWorkFlow(obj) {
      this.addnewProp = obj
      this.openAddnew()
    },

    // 编辑行
    editItem(prop, relationid) {
      if (typeof prop === 'number') {
        this.addnewProp = {
          id: prop
        }
        if (relationid) {
          this.addnewProp.relationId = relationid
        }
      } else {
        this.addnewProp = prop
      }
      this.openAddnew()
    },
    /**
     * 语音转写任务提交
     */
    audioTranslate(log) {
      if (log.translateStatus === this.$t('callCenter.translate')) {
        log.translateStatus = this.$t('callCenter.translating')
        Message({
          type: 'info',
          message: this.$t('callCenter.translateWait')
        })
      }
      const logId = log.id
      // const url = '/callcenter/audioTranslate.do'
      const params = {
        moduleType: log.moduleType,
        logId: logId,
        platform: log.moduleType === 3602 ? 'duyan' : 'web'
      }
      // api.post(url, params, (data) => {
      //   console.log(data.result)
      //   if (data.result === 'waiting') {
      //     log.translateStatus = this.$t('callCenter.translating')
      //     // 任务执行中，显示弹窗（加载中页）并在弹窗内每隔15s重新请求该API
      //     setTimeout(() => {
      //       this.audioTranslate(log)
      //     }, 15000)
      //     Message({
      //       type: 'info',
      //       message: this.$t('callCenter.translateWait')
      //     })
      //   } else if (data.result === 'failed') {
      //     log.translateStatus = this.$t('callCenter.translateFail')
      //     // 语音转写失败，显示弹窗（失败页）
      //     Message({
      //       type: 'error',
      //       message: this.$t('callCenter.failToFeedback')
      //     })
      //   } else if (data.result.length > 0) {
      //     log.translateStatus = this.$t('callCenter.viewTranslate')
      //     this.showAudioTranslateContent(data.result)
      //   } else {
      //     log.translateStatus = this.$t('callCenter.noDialogue')
      //     // 未知异常，显示空白页或失败页，不显示弹窗，告知对话无内容
      //     Message({
      //       type: 'warn',
      //       message: this.$t('callCenter.npDialContent')
      //     })
      //   }
      // })
      audioTranslate(params)
        .then(({ result: { taskResult, recordContents } }) => {
          // console.log(data.result)
          if (taskResult === 'waiting') {
            log.translateStatus = this.$t('callCenter.translating')
            // 任务执行中，显示弹窗（加载中页）并在弹窗内每隔15s重新请求该API
            setTimeout(() => {
              this.audioTranslate(log)
            }, 15000)
            Message({
              type: 'info',
              message: this.$t('callCenter.translateWait')
            })
          } else if (taskResult === 'failed') {
            log.translateStatus = this.$t('callCenter.translateFail')
            // 语音转写失败，显示弹窗（失败页）
            Message({
              type: 'error',
              message: this.$t('callCenter.failToFeedback')
            })
          } else if (taskResult === 'success') {
            log.translateStatus = this.$t('callCenter.viewTranslate')
            this.showAudioTranslateContent(recordContents)
          } else {
            log.translateStatus = this.$t('callCenter.noDialogue')
            // 未知异常，显示空白页或失败页，不显示弹窗，告知对话无内容
            Message({
              type: 'warning',
              message: this.$t('callCenter.npDialContent')
            })
          }
        })
        .catch(() => {})
    },
    /**
     * 显示语音转写的内容
     */
    showAudioTranslateContent(data) {
      this.audioContents = data
      // 显示翻译内容Dialog
      this.audioTranslateDialogVisible = true

      // 以下是MessageBox的显示方式
      // let contentArray = []
      // const h = this.$createElement
      // let startTimeYMD = xbb.formatDate(data[0].time, 'datetime')
      // contentArray.push(h('p', {'class': 'audio-center-content'}, startTimeYMD))
      // for (let key in data) {
      //   let it = data[key]
      //   let time = xbb.formatDate(it.time, 'seconds')
      //   if (it.speaker === '1') {
      //     contentArray.push(h('p', {'class': 'audio-left-speaker'}, 'A:>' + it.content))
      //     contentArray.push(h('p', {'class': 'audio-left-speaker audio-time'}, time))
      //   } else {
      //     contentArray.push(h('p', {'class': 'audio-right-speaker'}, it.content + '<:B'))
      //     contentArray.push(h('p', {'class': 'audio-right-speaker audio-time'}, time))
      //   }
      // }
      // // 翻译成功且有对话内容，显示弹窗（对话内容）
      // MessageBox.alert('', {
      //   title: this.$t('callCenter.voiceToContent'),
      //   message: h('div', null, contentArray),
      //   confirmButtonText: this.$t('operation.confirm')
      // })
    },
    /**
     * 得到时间的 YMD HM 格式
     * @param time 10位整形的时间，int，单位：秒
     */
    formatDateTime(time) {
      return xbb.formatDate(time, 'datetime')
    },
    /**
     * 得到时间的 时分秒 格式
     * @param time 10位整形的时间，int，单位：秒
     */
    formatDateSeconds(time) {
      return xbb.formatDate(time, 'seconds')
    },
    /**
     * mp3 文件下载
     * @param recordUrl 录音路径
     */
    downloadMp3(recordUrl) {
      if (recordUrl && this.moduleType === 3602) {
        // 度言平台，直接替换oga为mp3
        return recordUrl.replace('.oga', '.mp3')
        // window.open(recordUrl)
        // 以下代码为下载mp3文件使用
        // let urlArray = recordUrl.split('/')
        // let fileName = urlArray[urlArray.length - 1]
        // let aLink = document.createElement('a')
        // let blob = new Blob([recordUrl])
        // let evt = document.createEvent('HTMLEvents')
        // // initEvent 不加后两个参数在FF下会报错, 感谢 Barret Lee 的反馈
        // evt.initEvent('click', false, false)
        // aLink.download = fileName
        // aLink.href = URL.createObjectURL(blob)
        // aLink.dispatchEvent(evt)
      }
      return recordUrl
    },
    /**
     * mp3 文件下载名称
     * @param recordUrl 录音路径
     */
    downloadMp3FileName(recordUrl) {
      const urlArray = recordUrl.split('/')
      const fileName = urlArray[urlArray.length - 1]
      return fileName.substring(0, fileName.length - 4)
    },
    // 格式化时间
    formatTime(seconds) {
      const hour = parseInt(seconds / 3600)
      const minite = parseInt((seconds % 3600) / 60)
      const second = (seconds % 3600) % 60

      // 对于个数位，前面补0
      function addZero(origin) {
        if (origin < 10) {
          return '0' + origin
        } else {
          return origin
        }
      }

      return `${addZero(hour)}:${addZero(minite)}:${addZero(second)}`
    }
  },

  watch: {
    userOrDep: {
      handler(val) {
        if (val.length) {
          const value = val[0]
          this.scopeName = value.name
          this.filter.agentUserId = value.property === 'user' ? value.id : ''
          this.filter.depId = value.property === 'user' ? '' : value.id
          this.getList()
        }
      },
      deep: true,
      immediate: true
    }
  },

  components: {
    ListHead: listHead,
    // vDropbox,
    PersonSelect
    // addNew,
    // detail
  }

  // directives: { Clickoutside }
}
</script>

<style lang="scss" scoped>
@import '../../styles/saas/list-panel.scss';
.call-log {
  // flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  height: 100%;
  // background: $neutral-color-3;
  // position: relative;
  // height: 300px;
  // box-sizing: border-box;
  // padding-left: 50px;
  // margin-left: 0;
  // transition: margin-left .3s;
  .content {
    box-sizing: border-box;
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    padding: 15px;
    margin: 10px;
    overflow: hidden;
    background-color: $base-white;
  }
  .list-panel {
    .list-filter {
      position: relative;
      z-index: 99;
      .label {
        display: inline-block;
        margin-right: 6px;
        font-size: 13px;
        line-height: 30px;
        vertical-align: top;
      }
      .scope-filter {
        width: 254px;
      }
      // .date-range {
      //   width: 200px;
      // }
      .el-icon-close {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 12px;
        // color: @primary;
        cursor: pointer;
      }
    }
  }
  .call-list {
    .icon {
      display: inline-block;
      width: 20px;
      font-size: 15px;
      color: $text-auxiliary;
      text-align: center;
      vertical-align: middle;
    }
    .link {
      // color: @primary;
      cursor: pointer;
      &.active {
        color: $link-base-color-6;
      }
    }
    .red {
      color: $error-base-color-6;
    }
    .audio {
      display: inline-block;
      vertical-align: middle;
    }
  }
  .table-bot {
    display: flex;
    margin-top: 15px;
    .options {
      flex: 1 1 auto;
      height: 32px;
      line-height: 32px;
      .item {
        display: inline-block;
        font-size: 13px;
        // .link {
        //   color: @primary;
        // }
      }
    }
  }
}
</style>

<style lang="scss">
.audio-body {
  width: 82%;
  max-height: 800px;
  margin: 0 auto;
  overflow-x: hidden;
  overflow-y: auto;
}
.audio-left-div {
  display: flex;
  float: left;
  width: 100%;
  margin-top: 6px;
  margin-right: 25px;
  text-align: left;
}
.audio-right-div {
  display: flex;
  float: right;
  width: 100%;
  margin-top: 6px;
  margin-left: 25px;
  text-align: right;
}
.audio-left-speaker {
  float: left;
  margin-right: 5px;
  text-align: left;
}
.audio-right-speaker {
  float: right;
  margin-left: 5px;
  text-align: right;
}
.audio-left-content {
  float: left;
  padding: 10px;
  background: #f3f3f5;
  border-radius: 8px;
}
.audio-right-content {
  float: right;
  padding: 10px;
  background: #daf3ff;
  border-radius: 8px;
}
.audio-right {
  margin-right: 5px;
}
p.audio-center-content {
  text-align: center;
}
p.audio-time {
  margin-top: 12px;
  font-size: 12px;
  color: lightgray;
}
</style>
