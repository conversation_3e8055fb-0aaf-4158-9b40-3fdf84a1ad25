<!-- eslint vue/return-in-computed-property: 1 -->

<!--
  列表页头部组件
  props:
    placeholder: 搜索框的提示文字
  emit:
    searchEmit: 提交搜索请求
 -->

<template>
  <div class="call-center-list-header">
    <div class="children-head">
      <h1 class="title">
        <slot name="back"></slot>
        <slot name="title"></slot>
      </h1>
      <div class="template">
        <slot name="template"></slot>
      </div>
      <div v-show="showSearch" class="search">
        <template v-if="moduleName == 'workOrder'">
          <el-input
            v-model="search"
            class="input-text"
            :placeholder="$t('placeholder.inputPls', { attr: selectLable })"
            @change.native="searchKeydown"
          >
            <el-select
              slot="prepend"
              v-model="searchType"
              :placeholder="$t('placeholder.choosePls', { attr: '' })"
            >
              <el-option :label="$t('business.workOrderNum')" value="0"></el-option>
              <el-option :label="$t('business.contract')" value="1"></el-option>
              <el-option :label="$t('nouns.customer')" value="2"></el-option>
              <el-option :label="$t('business.opportunity')" value="5"></el-option>
            </el-select>
          </el-input>
          <i v-if="search" class="el-icon-close" @click="clearValue"></i>
        </template>
        <template v-else>
          <input
            v-model="search"
            class="input-text"
            :placeholder="placeholder"
            type="text"
            @change="searchKeydown"
          />
          <i v-if="search" class="el-icon-close" @click="clearValue"></i>
        </template>
      </div>
      <div class="options">
        <slot name="options"></slot>
        <slot name="otherBtn"></slot>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/return-in-computed-property: 1 */

import i18n from '@/lang'
import { mapActions } from 'vuex'

export default {
  props: {
    placeholder: {
      type: String,
      default: () => {
        return i18n.t('placeholder.contentPls')
      }
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    moduleName: {
      type: String
    },
    nameLike: {
      type: String
    }
  },
  data() {
    return {
      search: this.nameLike || '',
      searchType: '0'
    }
  },
  // mounted () {
  //   alert(this.moduleName)
  // },
  methods: {
    ...mapActions(['setSidemenuStatus']),
    clearValue(attr) {
      this.search = ''
      this.searchKeydown()
    },
    searchKeydown() {
      // 按下键盘回车才会执行搜索
      if (this.moduleName === 'workOrder') {
        this.$emit('searchEmit', [this.search, this.searchType])
      } else {
        this.$emit('searchEmit', this.search)
      }
    }
  },
  computed: {
    selectLable() {
      // 搜索输入框随选择类型改变placeholder
      switch (this.searchType) {
        case '0':
          return this.$t('business.workOrderNum')
        case '1':
          return this.$t('business.contract')
        case '2':
          return this.$t('nouns.customer')
        case '5':
          return this.$t('business.opportunity')
      }
    }
  },
  watch: {
    searchType() {
      // 工单搜索选项改变时触发
      this.$emit('searchEmit', [this.search, this.searchType])
    }
  }
}
</script>

<style lang="scss" scoped>
// @import "../../assets/less/variable.less";
.pendingOrder {
  .children-head {
    .title {
      top: 15px;
    }
  }
}
.call-center-list-header {
  .children-head {
    position: relative;
    display: flex;
    flex: 0 0 60px;
    height: 60px;
    font-size: 0;
    background-color: $base-white;
    .title {
      flex: 1 1 auto;
      margin-left: 20px;
      font-size: 16px;
      line-height: 60px;
      color: $text-main;
    }
    .template {
      flex: 1 1 auto;
      margin-left: 30px;
      line-height: 60px;
      color: #060606;
      .template-box {
        .template-item {
          display: inline-block;
          height: 45px;
          padding: 0 5px;
          margin-right: 32px;
          font-size: 12px;
          cursor: pointer;
          outline: none;
          &.active {
            color: #ff9800;
            border-bottom: 2px solid #ff9800;
          }
        }
      }
    }
    .search,
    .options {
      flex: 0 0 auto;
      margin-top: 12px;
      margin-right: 25px;
    }
    .search {
      position: relative;
      .el-select {
        width: 100px;
      }
      .el-input__inner {
        height: 34px;
      }
      .input-text {
        box-sizing: border-box;
        width: 220px;
        height: 32px;
        padding: 0 10px;
        font-size: 13px;
        line-height: 32px;
        background-color: $neutral-color-1;
        border-radius: 2px;
      }
      .el-input-group {
        width: 300px;
        padding: 0;
      }
      .input-text + .el-icon-close {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 12px;
        color: $link-base-color-6;
        cursor: pointer;
        transform: scale(0.8);
      }
    }
    .options {
      .el-button,
      .el-dropdown {
        margin-left: 15px;
        &:first-child {
          margin-left: 0;
        }
        i {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
