<!--
 * @Description: 描述
 -->
/* * @Description: 销帮帮-呼叫中心展示界面 */
<template>
  <div class="xbb-view">
    <div v-if="isExpiring" class="is-not-chrome">
      <p class="text">{{ $t('callCenter.expiring') }}</p>
    </div>
    <template v-else-if="isConnected && isWebsocketWorking && isChrome">
      <!-- 正常状态 -->
      <template v-if="currentStatus === 0">
        <!-- 强弱提醒 -->
        <template v-if="callBlockTriggerWay === 1 && countDownTime > 0">
          <div v-if="countDownTime > 0" class="is-sealing">
            <text-warning
              :value="
                $t('appCallCenter.limReminder' + remindLevel, { attr: $t('appCallCenter.pls') })
              "
            ></text-warning>
            <div class="circle">
              <div class="text">
                <div class="text-tip">{{ $t('appCallCenter.surplus') }}</div>
                <div class="text-time">{{ TimeFormat(countDownTime) }}</div>
              </div>
            </div>
            <div class="reminder">{{ $t('appCallCenter.rest') }}</div>
          </div>
        </template>
        <template v-else>
          <div>
            <text-warning
              v-if="countDownTime > 0"
              :value="
                $t('appCallCenter.limReminder' + remindLevel, {
                  attr: $t('appCallCenter.suggests'),
                  time: TimeFormat(countDownTime)
                })
              "
            ></text-warning>
            <div class="normal-status">
              <div class="dial">
                <el-input
                  v-model="callForm.phoneNum"
                  class="input-with-select"
                  :placeholder="$t('placeholder.inputPls', { attr: $t('nouns.phoneNumber') })"
                  @keyup.native.enter="makeCall(callForm.phoneNum, true)"
                >
                  <el-button
                    slot="append"
                    type="primary"
                    @click="makeCall(callForm.phoneNum, true)"
                    >{{ $t('callCenter.dial') }}</el-button
                  >
                </el-input>
              </div>
              <div class="call-log">
                <h4 class="title">{{ $t('callCenter.latelyCall') }}</h4>
                <ul class="call-list">
                  <li v-for="(item, index) in callLogList" :key="index" class="item">
                    <span class="status" :class="{ red: !item.result }">
                      <i
                        class="web-iconfont"
                        :class="[item.type ? 'web-icon-callout' : 'web-icon-callin']"
                      ></i>
                    </span>
                    <div class="infos">
                      <p class="phone-num" @click="makeCall(item.anotherPhoneNum)">
                        {{ item.hiddenPhoneNum }}
                      </p>
                      <p class="name">{{ item.refName }}</p>
                    </div>
                    <span class="date">{{ item.callTimeShow | formatDate }}</span>
                  </li>
                  <li v-if="!callLogList.length" class="empty">{{ $t('callCenter.noRecord') }}</li>
                </ul>
              </div>
            </div>
          </div>
        </template>
      </template>

      <div v-else class="incall-status">
        <div class="calling">
          <div v-if="btnNumShow && currentStatus === 3" class="box btn-num">
            <div class="tap-content"><span ref="tapTarget"></span></div>
            <div class="numeric-keyboard">
              <!-- *样式需要单独调整 -->
              <span v-for="item in btnNum" :key="item" class="btn-key" @click="numTap($event)"
                ><i>{{ item }}</i></span
              >
            </div>
          </div>
          <div v-else class="box info">
            <div class="tap-content">{{ phoneNumForShow }}</div>
            <div class="owner-name">{{ callForm.customerName }}</div>
            <div class="call-status">{{ statusName }}</div>
          </div>
          <div class="btn-group">
            <span class="hang-up web-icon-dial web-iconfont" @click="handleOption(7)"></span>
            <span
              v-if="currentStatus === 1"
              class="pick-up web-icon-dial web-iconfont"
              @click="handleOption(6)"
            ></span>
            <span v-if="currentStatus === 3" class="to-btn" @click="btnNumShow = !btnNumShow"
              ><i class="web-iconfont" :class="btnNumShow ? 'el-icon-close' : 'web-icon-Oval'"></i
            ></span>
          </div>
        </div>
      </div>
    </template>
    <div v-else-if="isWebsocketWorking && !isChrome" class="is-not-chrome">
      <p class="text">{{ $t('callCenter.notSupportXbb') }}</p>
    </div>
    <div v-else-if="!isWebsocketWorking && watingFlag" class="change-card">
      <template>
        <div class="is-sealing">
          <div class="text_waiting">
            {{ $t('callCenter.waitingChange') }}
            <br />{{ $t('callCenter.restart') }}
          </div>
          <div class="circle">
            <div class="text">
              <div class="text-tip">{{ $t('appCallCenter.waited') }}</div>
              <div class="text-time">{{ TimeFormat(watingCountTime) }}</div>
            </div>
          </div>
          <div class="reminder">{{ $t('appCallCenter.rest') }}</div>
        </div>
      </template>
    </div>
    <div v-else class="websocket-error">
      <h4 class="title">{{ $t('callCenter.linkAnomaly') }}</h4>
      <p class="text">{{ $t('callCenter.pA') }}</p>
      <p class="text">
        {{ $t('callCenter.pB')
        }}<a
          href="https://xbongbong.oss-cn-hangzhou.aliyuncs.com/down/newxbbcall/SmartPhoneBoxCWebsocket_21.8.4.1-XbbCall.exe"
          rel="noopener noreferrer"
          target="_blank"
          >{{ $t('callCenter.xbbCall') }}</a
        >）；
      </p>
      <p class="text">{{ $t('callCenter.pC') }}</p>
      <p class="tel text">{{ $t('callCenter.serviceCall') }}</p>
    </div>
    <audio ref="ring" loop>
      <source src="./imgs/ring.mp3" type="audio/mp3" />
    </audio>
    <div v-if="getCtiModal && currentStatus !== 0 && leftBtnList.length" class="xbb-view__left-btn">
      <i
        v-for="btn in leftBtnList"
        :key="btn.type"
        v-tooltip.left="btn.name"
        class="t-iconfont"
        :class="[btn.icon, showCommunicateView && btn.type === 'newRecord' ? 'active' : '']"
        @click="handleLeftBtnClick(btn)"
      ></i>
    </div>
    <div
      v-if="getCtiModal && currentStatus !== 0 && showCommunicate && showCommunicateView"
      class="xbb-view__record"
    >
      <RecordView
        :customer-info="customerInfo"
        :record-info="recordInfo"
        @newRemind="linkAdd"
        @refreshSelectTag="refreshSelectTag"
      ></RecordView>
    </div>
  </div>
</template>

<script>
import {
  getCustomerDisturb,
  callLogSave,
  callLogList,
  getFindRef,
  accountSocketReckon,
  accountUpdateAccount,
  getBlockRuleRemind,
  getConfig,
  findUserInfoCondition
} from '@/api/callCenter'
import { mapGetters, mapActions } from 'vuex'
import { Message } from 'element-ui'
import TextWarning from './text-warning'
import xbb from '@xbb/xbb-utils'
import bus from '@/utils/temp-vue'
import store from '@/store'
import { getDetailLinkAdd } from '@/api/form-detail-tabs'
import { getLinkAddVal, setFormatData } from '@/components/form-data-edit/utils'
import { getWorkOrderV2DetailLinkAdd } from '@/api/work-order-v2/common.js'
import specialJudge from '@/utils/form-data-special-judge'
import RecordView from './record-view.vue'
// websocket
let websocket = null

// 监听窗口关闭事件，当窗口关闭时，主动关闭websocket链接
window.onbeforeunload = function () {
  if (websocket !== null) {
    websocket.close()
  }
}

// 呼叫中心套餐状态
const xbbCallStatusMap = {
  normal: 'normal', // 正常
  expiring: 'expiring', // 过期
  nearExpiring: 'nearExpiring' // 即将过期
}
const Command = {
  Dial_Multi: 'Dial_Multi', // 拨打
  Answer_Multi: 'Answer_Multi', // 接听
  HangUp_Multi: 'HangUp_Multi', // 挂断
  GetCCID_Multi: 'GetCCID_Multi', // ccid
  OpenDevice_Multi: 'OpenDevice_Multi', // 连接设备
  GetConnectedState_Multi: 'GetConnectedState_Multi', // 获取状态
  GetCBCurrState_Multi: 'GetCBCurrState_Multi', // 获取卡槽状态
  SetConfig: 'SetConfig', // 修改配置
  CBSwitchNext_Multi: 'CBSwitchNext_Multi', // 顺序换卡
  GetUpdloadErrorLog: 'GetUpdloadErrorLog', // 获取通话记录推送失败数量
  GetUploadErrorRecord: 'GetUploadErrorRecord', // 获取通话录音推送失败数量
  RePushAutoUpload: 'RePushAutoUpload', // 重新推送
  SetUpdateID: 'SetUpdateID', // 定向升级标记
  SendDTMF_Multi: 'SendDTMF_Multi' // 发送DTMF
}

export default {
  name: 'XbbView',

  components: {
    TextWarning,
    RecordView
  },

  props: {
    // 呼叫中心套餐状态
    xbbCallstatus: {
      type: String,
      default: xbbCallStatusMap.normal
    }
  },
  data() {
    return {
      callLogList: [], // 通话记录列表
      currentStatus: 0, // 当前状态 0空闲, 1来电, 2呼叫中, 3通话中
      callForm: {
        phoneNum: '', // 电话号码
        customerName: '' // 客户
      },
      refInfos: {}, // 号码关联信息
      isWebsocketWorking: false, // websocket链接状态
      // 发送给服务端的基本参数
      basicParams: {
        command: '',
        arguments: {
          devicename: ''
        }
      },
      specialSendData: {
        command: ''
      },
      phoneNumForShow: '',
      timeout: null,
      // 滚动的标题
      scrollTitle: '',
      // 弹窗提醒的实例
      notify: null,
      isChrome: navigator.userAgent.indexOf('Chrome') > -1,
      btnNumShow: false, // 控制小键盘是否显示
      btnNum: [1, 2, 3, 4, 5, 6, 7, 8, 9, '*', 0, '#'],
      countDownTime: 0, // 倒计时毫秒
      watingCountTime: 0, // 等待计时
      watingFlag: false, // 是否开启倒计时
      // 呼叫限制的参数
      limitParams: {
        corpid: utils.LS.get('corpid'),
        userId: utils.LS.get('userId'),
        platform: 'web',
        noErrorMessage: true,
        moduleType: 3605, // 呼叫平台
        ccId: null // sim卡识别码
      },
      remindLevel: 0, // 警告等级  3:时间间隔 2:三十分钟 1:一天
      callBlockTriggerWay: 0, // 强提醒1,若提醒2,0为默认状态
      callBlockDaySwitch: 0,
      callBlockThirtySwitch: 0,
      interval: null, // 定时器
      interval2: null, // 换卡等待时间
      isExpiring: false, // 套餐是否过期
      isSingleCard: true, // 是否是单卡
      cBNext: '', // 是否在换卡且换卡前ccid是什么
      ccids: new Map(), // 卡id
      isConnected: false, // 机器是否连接
      visibilityChangeEvent: '',
      onVisibilityChange: '',
      isNowTapFlag: true, // 判断是否在当前页面
      callInfo: null,
      showAddWorkOrderV2: false, // 是否显示新建工单按钮
      showCommunicate: false, // 是否显示新建跟进记录按钮
      showCommunicateView: false, // 是否显示新建跟进记录弹窗
      recordInfo: {}, // 当前呼叫用户跟进记录信息
      customerInfo: {} // 当前呼叫用户匹配的客户信息
    }
  },

  computed: {
    filterPhone() {
      return this.callForm.phoneNum
    },
    statusName() {
      if (this.currentStatus === 1) {
        return this.$t('callCenter.incoming')
      } else if (this.currentStatus === 2) {
        return this.$t('callCenter.calling')
      } else if (this.currentStatus === 3) {
        return this.$t('callCenter.communicating')
      } else {
        return ''
      }
    },
    ...mapGetters(['getCtiModal']),
    leftBtnList() {
      const list = []
      // if (this.showAddWorkOrderV2) {
      //   list.push({
      //     icon: 'icon-file-list-line',
      //     type: 'newWorkOrder',
      //     name: '新建工单'
      //   })
      // }
      if (this.showCommunicate) {
        list.push({
          icon: 'icon-edit-line',
          type: 'newRecord',
          name: '新建跟进记录'
        })
      }
      return list
    }
  },

  filters: {
    formatDate(val) {
      const seconds = new Date(val).getTime() / 1000
      return xbb.formatTimeCompare(seconds)
    }
  },
  watch: {
    // 监听输入的号码，存在规则以外的符号过滤掉
    filterPhone(nval, oval) {
      // debugger
      if (nval.search(/[^0-9+-]/g) !== -1) {
        this.callForm.phoneNum = oval
      }
    },
    // 监听当前呼叫状态
    currentStatus(val) {
      // 来电
      if (val === 1) {
        this.checkNotification()
      } else {
        this.closeNotify()
      }
    },
    btnNumShow(val) {
      if (val) {
        document.addEventListener('keyup', this.keyboardEvent)
        this.$once('hook:beforeDestroy', function () {
          document.removeEventListener('keyup', this.keyboardEvent)
        })
      } else {
        document.removeEventListener('keyup', this.keyboardEvent)
      }
    },
    isWebsocketWorking(val) {
      if (val) {
        window.clearInterval(this.interval2)
        this.watingCountTime = 0
        this.watingFlag = false
      }
    }
  },

  mounted() {
    bus.$on('callInfo', (obj) => {
      if (obj) {
        const temObj = {
          refId: '',
          refType: ''
        }
        obj.dataId && (temObj.refId = obj.dataId)
        obj.businessType && (temObj.refType = obj.businessType)
        this.callInfo = temObj
      }
    })
    this.initWebSocket()
    this.isnowTap()
    this.getCalllog()
    this.resetCallme()
    const ccids = utils.LS.get(`ccids`)
    try {
      ccids.forEach((e) => {
        this.ccids.set(e[0], e[1])
      })
    } catch (error) {}
  },
  beforeDestroy() {
    document.addEventListener(this.visibilityChangeEvent, this.onVisibilityChange)
    // bus.$off('callInfo')
  },

  methods: {
    ...mapActions([
      'toggleCtiModal',
      'updateMissCallCount' // 更新未接来电数
    ]),
    // 初始化websocket
    initWebSocket() {
      if (this.expiringRemind()) return
      // 判断当前浏览器是否支持Websocket
      if ('WebSocket' in window) {
        // if (xbb.isDevEnv()) return
        // 本地调试时，请使用chrome访问https://**************:4649/Echo，然后点击高级，继续访问
        websocket = new WebSocket('ws://127.0.0.1:8555/api') // 本地 WebSocket 地址
        // 鉴于经常忘记修改,这里添加一个强判断
        if (process.env.NODE_ENV === 'production') {
          websocket = new WebSocket('ws://127.0.0.1:8555/api') // 线上改为localhost
        }
        // 链接发生错误
        websocket.onerror = (event) => {
          console.warn('WebSocket 连接失败，请注意！')
          this.isWebsocketWorking = false
        }

        // 连接成功建立的回调方法
        websocket.onopen = (event) => {
          // 心跳检测
          setInterval(() => {
            websocket.send('HeartBeatData')
          }, 60000)
          this.OnOpenDevice()
          this.isWebsocketWorking = true
          // websocket.send('{"command":"CloseOldWs"}')

          // setTimeout(() => {
          //   this.getCurrentCalllog()
          // }, 200)
        }

        // 接收到消息的回调方法
        websocket.onmessage = (event) => {
          if (!event.data) {
            return
          }
          const data = JSON.parse(event.data)
          console.log(data)

          // if (data.result && data.result.ccid) {
          //   // 如果ccid与存的ccid不同说明换卡成功或者localstore不存在说明超时 都不再轮询初始化
          //   if (this.cBNext !== data.result.ccid || utils.LS.get('cBNext') === undefined) {
          //     clearInterval(this.cbInterval)
          //   }
          //   this.limitParams.ccId = data.result.ccid
          //   this.limitMessage()
          // }

          // 插拔卡后重新判断单卡多卡
          if (data.type === 'CBCallback') {
            const sendData = Object.assign({}, this.basicParams)
            sendData.command = Command.GetCBCurrState_Multi
            websocket.send(JSON.stringify(sendData))
          }
          // 机器 断和开 连接状态处理
          if (data.type === 'DeviceConnectedState') {
            this.isConnected = data.dynamicdata.state
          }
          // 事件回调
          if (data.type === 'RealTimeState') {
            this.handleCallback(data.dynamicdata.realtimestate, data.dynamicdata)
          }
          // 主动事件回调
          if (data.type === 'CommandResponse') {
            if (data.data.invoke_command === Command.GetCCID_Multi) {
              this.limitParams.ccId = data.dynamicdata.ccid
              setTimeout(() => {
                this.limitMessage()
              }, 1500)
            } else if (data.data.invoke_command === Command.GetConnectedState_Multi) {
              if (data.dynamicdata.devicelist === '') {
                this.isConnected = false
              } else {
                this.isConnected = true
                this.basicParams.arguments.devicename = data.dynamicdata.devicelist
                const sendData = Object.assign({}, this.basicParams)
                sendData.command = Command.GetCCID_Multi
                websocket.send(JSON.stringify(sendData))
              }
            } else if (data.data.invoke_command === Command.GetDeviceInfo_Multi) {
              // 客户端版本接口还没,后期加上
              this.sendClientInfo({
                firmWareVersion: data.dynamicdata.hw_version,
                clientVersion: '',
                model: data.dynamicdata.model_name
              })
            } else if (data.data.invoke_command === Command.GetCBCurrState_Multi) {
              const numberOfCards = data.dynamicdata.split(',').filter((e) => e === '1').length
              this.isSingleCard = numberOfCards <= 1
            } else if (
              data.data.invoke_command === Command.CBSwitchNext_Multi &&
              data.data.state === true
            ) {
              this.$message({
                type: 'success',
                message: this.$t('callCenter.changeSuccessfully')
              })
              const sendData = Object.assign({}, this.basicParams)
              sendData.command = Command.GetCCID_Multi
              websocket.send(JSON.stringify(sendData))
              // 通话记录/录音重新推送的回调
            } else if (data.data.invoke_command === Command.GetUpdloadErrorLog) {
              const sendData = Object.assign({}, this.specialSendData)
              if (data.dynamicdata.count === '0') {
                sendData.command = Command.GetUploadErrorRecord
                websocket.send(JSON.stringify(sendData))
              } else {
                sendData.command = Command.RePushAutoUpload
                websocket.send(JSON.stringify(sendData))
              }
            } else if (
              data.data.invoke_command === Command.GetUploadErrorRecord &&
              data.dynamicdata.count !== '0'
            ) {
              const sendData = Object.assign({}, this.specialSendData)
              sendData.command = Command.RePushAutoUpload
              websocket.send(JSON.stringify(sendData))
            }
          }
        }

        // 连接关闭的回调方法
        websocket.onclose = (event) => {
          // console.log(this.$t('callCenter.closeWebSocket'))
          websocket = null
          // console.log(this.$t('callCenter.tryWebSocket'))
          this.initWebSocket()
        }
      } else {
        this.$message({
          type: 'error',
          message: this.$t('callCenter.notSupportWebSocket')
        })
      }
    },
    isnowTap() {
      // 判断当前窗口是否被显示
      const hiddenProperty =
        'hidden' in document ? 'hidden' : 'webkitHidden' in document ? 'webkitHidden' : 'mozHidden'
      this.visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange')
      const that = this
      this.onVisibilityChange = function () {
        if (!document[hiddenProperty]) {
          that.isNowTapFlag = true
        } else {
          that.isNowTapFlag = false
        }
      }
      document.addEventListener(this.visibilityChangeEvent, this.onVisibilityChange)
    },
    OnOpenDevice() {
      let sendData = Object.assign({}, this.basicParams)
      // 设置定向升级标记
      sendData.command = Command.SetUpdateID
      sendData.arguments.content = 'xbb'
      websocket.send(JSON.stringify(sendData))
      sendData = Object.assign({}, this.basicParams)
      // 获取当前状态
      setTimeout(() => {
        sendData.command = Command.GetCBCurrState_Multi
        websocket.send(JSON.stringify(sendData))
      }, 2000)
      // 获取连接状态
      setTimeout(() => {
        sendData.command = Command.GetConnectedState_Multi
        websocket.send(JSON.stringify(sendData))
      }, 1000)
      // 设置通话记录参数
      setTimeout(() => {
        getConfig().then(({ result }) => {
          sendData.command = Command.SetConfig
          sendData.arguments = this.ObjtoLowerCase(result)
          websocket.send(JSON.stringify(sendData))
        })
      }, 3000)
      // 定时进行通话记录/录音推送失败的查询和重推
      setInterval(() => {
        // 空闲状态每5分钟进行
        if (this.currentStatus === 0) {
          const sendData = Object.assign({}, this.specialSendData)
          sendData.command = Command.GetUpdloadErrorLog
          websocket.send(JSON.stringify(sendData))
        }
      }, 1000 * 60 * 5)
    },
    ObjtoLowerCase(assetData) {
      const res = {}
      for (const key in assetData) {
        const lowerkey = key.toLowerCase()
        // 赋给新的属性名，删除旧的
        res[lowerkey] = assetData[key]
      }
      return res
    },
    // 键盘联动
    keyboardEvent(evt) {
      if (evt.shiftKey) {
        if (evt.keyCode === 51 || evt.keyCode === 56) {
          this.numTap(evt)
        }
      } else {
        if (evt.keyCode >= 48 && evt.keyCode <= 57) {
          this.numTap(evt)
        }
      }
    },

    numTap(evt) {
      console.log('evt', evt)
      const val = evt.key ? evt.key : evt.target.innerText
      if (this.$refs.tapTarget) {
        // 显示用户输入的内容
        this.$refs.tapTarget.innerText += val
        // 长度超过9显示省略号
        if (this.$refs.tapTarget.innerText.length > 9) {
          const str = this.$refs.tapTarget.innerText
          this.$refs.tapTarget.innerText = '...' + str.slice(str.length - 9, str.length)
        }
      }
      this.handleOption(9, val)
    },

    // 监听用户主动操作
    handleOption(type, phoneNum = this.callForm.phoneNum) {
      // @type 5拨打 6接听 7挂断
      const sendData = Object.assign({}, this.basicParams)
      // 拨打电话
      if (type === 5) {
        // 强提醒状态下阻断
        if (this.callBlockTriggerWay === 1 && this.countDownTime > 0) {
          return
        }
        // 格式化电话号码
        const tel = phoneNum.replace(/[^0-9+-]/g, '')
        // let tel = encodeURIComponent(phoneNum)
        // let info = JSON.stringify(this.callInfo)
        // 获取客户信息
        this.getCustomerInfo(tel)
        sendData.command = Command.Dial_Multi
        sendData.arguments.phone = tel
        sendData.arguments.extraparam = this.callInfo
        this.callInfo = null
        // sendData.message = {
        //   phoneNumber: tel
        // }
        this.currentStatus = 2
        // 接听电话
      } else if (type === 6) {
        this.currentStatus = 3
        sendData.command = Command.Answer_Multi
        delete sendData.arguments.phone
        delete sendData.arguments.extraparam
        // 挂断电话
      } else if (type === 7) {
        this.currentStatus = 0
        sendData.command = Command.HangUp_Multi

        // 挂断后，清空相关信息
        this.clearInfos()
        this.getCalllog()
        // 小键盘
      } else if (type === 9) {
        // sendData.message = {
        //   number: phoneNum
        // }
        delete sendData.arguments.extraparam
        sendData.command = Command.SendDTMF_Multi
        sendData.arguments.content = phoneNum
      }
      this.emitCallStatus(type, phoneNum)
      websocket.send(JSON.stringify(sendData))
      // this.getCurrentCalllog()
    },

    // 重置呼叫方法
    resetCallme() {
      // 拨打电话
      window.CALL_CENTER.callme = (tel) => {
        this.makeCall(tel)
      }
    },

    // 防骚扰请求
    disturb(phone, cb) {
      // const url = '/callcenter/customer/disturb.do'
      // api.post(url, {phone}, (data) => {
      //   cb(phone)
      // })
      getCustomerDisturb({ phone })
        .then((data) => {
          cb(phone)
        })
        .catch(() => {})
    },

    /**
     * 拨打电话
     * @param {Number} phone - 拨打的电话
     * @param {Boolen} isdial - 判断是否通过拨号输入的电话，是则添加输入验证提醒用户
     */
    makeCall(phone, isdial) {
      // debugger
      if (!this.getCtiModal) {
        this.toggleCtiModal(true)
      }
      if (!phone) {
        Message({
          type: 'error',
          message: this.$t('callCenter.existNumber')
        })
        return
      } else if (isdial && phone !== phone.replace(/[^0-9+-]/g, '')) {
        Message({
          type: 'error',
          message: this.$t('callCenter.errorNumber')
        })
        return
      }

      phone = phone.trim()

      // 防骚扰请求
      this.disturb(phone, () => {
        this.handleOption(5, phone)
      })
    },

    // 监听被动操作
    handleCallback(type, message) {
      // 来电
      if (type === 'incoming') {
        console.log(this.$t('callCenter.comeCallBack'), message)
        this.currentStatus = 1

        this.$set(this.callForm, 'phoneNum', message.number)
        // 获取客户信息
        this.getCustomerInfo(message.number)

        // 挂断
      } else if (type === 'hangup') {
        // 挂断后，清空相关信息
        console.log(this.$t('callCenter.breakCallBack'), message)
        this.currentStatus = 0
        this.clearInfos()

        setTimeout(() => {
          this.limitMessage()
        }, 2000)
        this.getCalllog()
        // 挂断后更新未接来电
        // let params = {
        //   moduleType: 3605,
        //   page: 1,
        //   pageSize: 10
        // }
        // // 未接来电
        // missedCallList(params).then(res => {
        //   this.updateMissCallCount(res.result.totalElements) // 拿到数目
        // }).catch(() => {})
        // 通话结束
        // } else if (type === 3) {
        //   console.log(this.$t('callCenter.finishCallBack'), message)
        //   const callLog = {
        //     moduleType: 3605,
        //     startTime: new Date(message.startTime).getTime() / 1000,
        //     endTime: new Date(message.endTime).getTime() / 1000,
        //     answerTime: message.answerTime ? new Date(message.answerTime).getTime() / 1000 : null,
        //     anotherPhoneNum: message.number,
        //     type: message.callType === '来电' ? 0 : 1,
        //     recordUrl: message.recordUrl, // 录音文件
        //     connectTime: '', // 拿不到
        //     agentPhoneNum: '' // 拿不到
        //   }
        //   this.currentStatus = 0
        //   // connectState
        //   this.saveCalllog(callLog)

        // 通话接听回调
      } else if (['outconnected', 'inconnected'].includes(type)) {
        this.currentStatus = 3
        console.log(this.$t('callCenter.answerPhone'))

        // 获取客户端软硬件信息
        // } else if (type === 101) {
        //   this.sendClientInfo(message)
        //   return
        // 通话中电话打入
        // } else if (type === 104) {
        //   const callLog = {
        //     moduleType: 3605,
        //     startTime: new Date(message.startTime).getTime() / 1000,
        //     endTime: new Date(message.endTime).getTime() / 1000,
        //     answerTime: message.answerTime ? new Date(message.answerTime).getTime() / 1000 : null,
        //     anotherPhoneNum: message.number,
        //     type: message.callType === '来电' ? 0 : 1,
        //     recordUrl: message.recordUrl, // 录音文件
        //     connectTime: '', // 拿不到
        //     agentPhoneNum: '' // 拿不到
        //   }
        //   callLogSave({...callLog, ccId: ''}).then().catch(e => {})
      } else if (type === 'idle') {
        this.currentStatus = 0
      }
      this.emitCallStatus(type, message.number)
    },

    // 将xbb的呼叫状态格式化后，向上提交
    emitCallStatus(type, phoneNum) {
      console.log('emit', type)
      // 通话状态 0 来电 1 呼出 2 通话中 3 正常
      if (type === 'incoming') {
        this.$emit('callStatus', 0, phoneNum)
        this.$emit('openCallPanel', true)
      } else if (type === 5) {
        if (this.isWebsocketWorking) {
          this.$emit('callStatus', 1, phoneNum)
          this.$emit('openCallPanel', true)
        }
      } else if (['outconnected', 'inconnected'].includes(type)) {
        this.$emit('callStatus', 2)
      } else if (type === 7 || type === 'hangup' || type === 'idle') {
        this.$emit('callStatus', 3)
      }
    },

    // 保存通话记录 --已经不用
    saveCalllog(callLog) {
      // const url = '/callcenter/callLog/save.do'
      const params = Object.assign({ noErrorMessage: true }, callLog, this.refInfos, {
        ccId: this.limitParams.ccId
      })
      // api.post(url, params, (response) => {
      //   // 更新未接来电数量
      //   const missCallLogSum = response.missCallLogSum
      //   if (missCallLogSum) {
      //     this.updateMissCallCount(missCallLogSum)
      //   }
      //   this.getCalllog()

      //   // 清空关联信息
      //   this.refInfos = {}
      // })
      callLogSave(params)
        .then((response) => {
          // 更新未接来电数量
          const missCallLogSum = response.missCallLogSum
          if (missCallLogSum) {
            this.updateMissCallCount(missCallLogSum)
          }
          this.getCalllog()

          // 清空关联信息
          this.refInfos = {}
        })
        .then((e) => {
          this.limitMessage()
        })
        .catch(() => {})
    },

    // 获取近期通话
    getCalllog() {
      // const url = '/callcenter/callLog/list.do'
      const params = {
        callSource: 0, // callSource 0:老呼叫中心 1：云呼
        moduleType: 3605,
        page: 1,
        pageSize: 5
      }
      setTimeout(() => {
        callLogList(params)
          .then((data) => {
            this.callLogList = data.result.callLogList || []
          })
          .catch(() => {})
      }, 2000)
    },

    // 获取客户信息
    getCustomerInfo(phoneNum) {
      this.showCommunicate = false
      // debugger
      // const url = '/callcenter/findRef.do'
      const params = {
        mobile: phoneNum
      }
      // api.post(url, params, (data) => {
      //   this.refInfos = {
      //     refName: data.result.refName,
      //     refId: data.result.refId,
      //     refType: data.result.refType
      //   }
      //   this.phoneNumForShow = data.result.hiddenRefMobile
      //   // 设置客户名
      //   if (this.refInfos.refType === 3) {
      //     this.callForm.customerName = `${data.result.customerName}(${this.refInfos.refName})`
      //   } else {
      //     this.callForm.customerName = this.refInfos.refName
      //   }
      // })
      getFindRef(params)
        .then((data) => {
          this.refInfos = {
            refName: data.result.refName,
            refId: data.result.refId,
            refType: data.result.refType
          }
          this.phoneNumForShow = data.result.hiddenRefMobile
          // 设置客户名
          if (this.refInfos.refType === 3) {
            this.callForm.customerName = `${data.result.customerName}(${this.refInfos.refName})`
          } else {
            this.callForm.customerName = this.refInfos.refName
          }
        })
        .catch(() => {})
      findUserInfoCondition({ anotherPhoneNum: phoneNum })
        .then(({ result }) => {
          if (!result.showCommunicate) return
          // 筛选选中的标签
          const flatArr = result.labelTreePojos.reduce(
            (acc, cur) => acc.concat(cur.labelEntities),
            []
          )
          const selectTagList = result.labelsList
            .map((id) => flatArr.find((item) => item.id === id))
            .filter((item) => item !== undefined)
          // 跟进记录相关信息
          const { labelsList, labelTreePojos, summaryList, refId, refType, refName, showLabel } =
            result
          this.recordInfo = {
            labelsList,
            labelTreePojos,
            summaryList,
            selectTagList,
            refId,
            refType,
            refName,
            showLabel,
            new: result.new
          }
          // 客户信息——用于关联新建
          const { appId, dataId, saasMark, businessType, subBusinessType, formId } = result
          this.customerInfo = {
            appId,
            dataId,
            saasMark,
            businessType,
            subBusinessType,
            formId,
            phoneNum
          }
          this.showCommunicate = result.showCommunicate
          this.showAddWorkOrderV2 = result.showAddWorkOrderV2
        })
        .catch(() => {})
    },

    // 清空已有信息
    clearInfos() {
      this.callForm = {
        phoneNum: '',
        customerName: ''
      }
      this.phoneNumForShow = ''
    },

    // 获取当日通话统计，并通过websocket发送给话机
    getCurrentCalllog() {
      // const url = '/callcenter/account/socketReckon.do'
      // const params = { timeType: 1, type: 1 }
      // api.post(url, params, (data) => {
      //   const callLog = data.callCenterAccountPojoList
      //   const sendData = {
      //     messageType: '99',
      //     message: {}
      //   }
      //   sendData.message.callTime = callLog.getCallTime
      //   sendData.message.callCount = callLog.totalCount
      //   sendData.message.successCallCount = callLog.successCount

      //   websocket.send(JSON.stringify(sendData))
      //   // 考虑到websocket有可能会丢包，2秒钟后再发一次
      //   setTimeout(() => {
      //     websocket.send(JSON.stringify(sendData))
      //   }, 2000)
      // })
      accountSocketReckon()
        .then(({ result: { count, duration, successCount } }) => {
          // const callLog = data.callCenterAccountPojoList
          const sendData = {
            messageType: '99',
            message: {}
          }
          sendData.message.callTime = duration
          sendData.message.callCount = count
          sendData.message.successCallCount = successCount

          websocket.send(JSON.stringify(sendData))
          // 考虑到websocket有可能会丢包，2秒钟后再发一次
          setTimeout(() => {
            websocket.send(JSON.stringify(sendData))
          }, 2000)
        })
        .catch(() => {})
    },

    // 检查Notification APi
    checkNotification() {
      // 先检查浏览器是否支持
      if (!('Notification' in window)) {
        console.log('This browser does not support desktop notification')

        // 检查用户是否同意接受通知
      } else if (Notification.permission === 'granted') {
        // If it's okay let's create a notification
        this.openNotify()

        // 否则我们需要向用户获取权限
      } else if (Notification.permission === 'denied') {
        Notification.requestPermission(function (permission) {
          // 如果用户同意，就可以向他们发送通知
          if (permission === 'granted') {
            this.openNotify()
          }
        })
      }
    },

    // 关闭提醒
    closeNotify() {
      // 停止播放来电铃声
      this.controlRing(false)

      // 先检查浏览器是否支持
      if ('Notification' in window && this.notify) {
        this.notify.close()
      }
      clearTimeout(this.timeout)
      document.title = this.$t('callCenter.Xbb')
    },

    // 弹出提醒
    openNotify() {
      const message = `${this.callForm.phoneNum} ${this.$t('callCenter.isCalling')}`
      this.notify = new Notification(this.$t('callCenter.remindCall'), {
        lang: 'zh-CN',
        icon: 'https://dingtalkapi.xbongbong.com/images/logo.png',
        body: message
      })

      // 滚动title
      this.scrollTitle = message
      this.scrollStart()

      // 播放来电铃声
      this.controlRing()
    },

    // 滚动title
    scrollStart() {
      clearTimeout(this.timeout)
      document.title =
        this.scrollTitle.substring(1, this.scrollTitle.length) + this.scrollTitle.substring(0, 1)
      this.scrollTitle = document.title.substring(0, this.scrollTitle.length)
      this.timeout = setTimeout(this.scrollStart, 100)
    },

    // 控制来电铃声
    controlRing(play = true) {
      const audio = this.$refs.ring
      if (play) {
        audio.play()
      } else {
        audio.pause()
        audio.currentTime = 0
      }
    },

    // 发送客户端信息
    sendClientInfo(clientInfo) {
      const params = Object.assign({ moduleType: 3605 }, clientInfo)
      console.log(params)

      // const url = '/callcenter/account/updateAccount.do'
      // api.post(url, params, (data) => {
      //   console.log(this.$t('callCenter.sendSuccess'))
      // })
      accountUpdateAccount(params)
        .then((data) => {
          console.log(this.$t('callCenter.sendSuccess'))
        })
        .catch(() => {})
    },
    // 呼叫限制倒计时
    countDown() {
      if (this.interval) window.clearInterval(this.interval)
      const t2 = Date.now() + this.countDownTime // 延迟多少毫秒
      this.interval = window.setInterval(() => {
        this.countDownTime = t2 - Date.now() > 0 ? t2 - Date.now() : 0 // 延迟时间距离当前时间的时间戳
        if (this.countDownTime <= 0) {
          window.clearInterval(this.interval)
        }
      }, 1000)
    },
    watingTime() {
      if (this.interval2) window.clearInterval(this.interval2)
      // let t2 = Date.now() + this.countDownTime// 延迟多少毫秒
      const t1Now = Date.now()
      this.watingFlag = true
      this.interval2 = window.setInterval(() => {
        this.watingCountTime = Date.now() - t1Now
        if (this.watingCountTime > 120000) {
          window.clearInterval(this.interval2)
          this.watingCountTime = 0
          this.watingFlag = false
        }
      }, 1000)
    },
    // 获取呼叫限制
    limitMessage() {
      if (!this.limitParams.ccId) return
      return new Promise((resolve, reject) => {
        getBlockRuleRemind(this.limitParams)
          .then((res) => {
            if (this.interval) window.clearInterval(this.interval)
            this.countDownTime = res.result.countDown || 0
            this.remindLevel = res.result.remindLevel || 0
            this.callBlockTriggerWay = res.result.callBlockTriggerWay || 0
            this.callBlockThirtySwitch = res.result.callBlockThirtySwitch || 0
            this.callBlockDaySwitch = res.result.callBlockDaySwitch || 0
            if (this.countDownTime !== 0) {
              const sendData = Object.assign({}, this.basicParams)
              sendData.command = Command.CBSwitchNext_Multi
              // 多卡条件
              // ccids存了每个卡的下次能换卡的时间 比如卡1换卡了下次能换卡就必须在半个小时或第二天
              // 只有触发强防封30分钟或1天时才会换卡
              const changeFlag =
                !(
                  this.ccids.get(this.limitParams.ccId) &&
                  +this.ccids.get(this.limitParams.ccId) > Date.now()
                ) &&
                ((this.callBlockThirtySwitch === 1 && this.remindLevel === 2) ||
                  (this.callBlockDaySwitch === 1 && this.remindLevel === 1))
              // 不能换卡标识符
              const cannotChange =
                (this.callBlockThirtySwitch === 1 && this.remindLevel === 2) ||
                (this.callBlockDaySwitch === 1 && this.remindLevel === 1)
              // 单卡
              if (this.isSingleCard) {
                // this.countDown()
              } else if (changeFlag && this.isNowTapFlag) {
                this.$message({
                  message: '开始换卡'
                })
                websocket.send(JSON.stringify(sendData))
                this.ccids.set(this.limitParams.ccId, Date.now() + this.countDownTime)
                utils.LS.set('ccids', this.ccids, 24 * 60 * 1000)
              } else if (cannotChange && this.isNowTapFlag) {
                this.$message({
                  type: 'warning',
                  message: this.$t('callCenter.tip1')
                })
              }
              this.countDown()
            }
            resolve()
          })
          .catch(() => {})
      })
    },
    TimeFormat(Time) {
      const leave1 = Time % (24 * 3600 * 1000) // 获取毫秒数
      const hours = Math.floor(leave1 / (3600 * 1000)) // 获取小时数
      const leave2 = leave1 % (3600 * 1000) // 排除小时的毫秒数
      const minutes = Math.floor(leave2 / (60 * 1000)) // 获取分钟
      const leave3 = leave2 % (60 * 1000) // 排除分钟的毫秒数
      const seconds = Math.round(leave3 / 1000) // 获取秒
      return (
        (hours > 9 ? hours : '0' + hours) +
        ':' +
        (minutes > 9 ? minutes : '0' + minutes) +
        ':' +
        (seconds > 9 ? seconds : '0' + seconds)
      )
    },
    expiringRemind() {
      // 先判断套餐状态
      if (this.xbbCallstatus === xbbCallStatusMap.expiring) {
        this.isExpiring = true
        return true
      } else if (this.xbbCallstatus === xbbCallStatusMap.nearExpiring) {
        if (utils.LS.get('expiringRemind')) return
        utils.LS.set('expiringRemind', 1, 1 * 24 * 60)
        this.$alert(this.$t('callCenter.nearExpiring'), this.$t('callCenter.expiringTitle'))
      }
    },
    handleLeftBtnClick(btn) {
      const { saasMark, businessType, dataId } = this.customerInfo
      switch (btn.type) {
        case 'newWorkOrder':
          this.linkAdd({
            businessType: 20300,
            dataIdList: [dataId],
            linkBusinessType: businessType,
            saasMark
          })
          break
        case 'newRecord':
          this.showCommunicateView = !this.showCommunicateView
          break
        default:
          break
      }
    },
    linkAdd(options) {
      const promise = specialJudge.isWorkOrderV2(options.businessType)
        ? getWorkOrderV2DetailLinkAdd
        : getDetailLinkAdd
      promise(options).then(
        ({
          result,
          result: {
            explainList,
            serialNo,
            groupNumber,
            paasFormEntity: {
              appId,
              menuId,
              id,
              saasMark,
              businessType,
              name,
              fieldPosition = 'left'
            },
            saasObj,
            writeOffAmount,
            lowCodeConfig
          }
        }) => {
          store.commit('SET_MODE', 'add') // 编辑跟进提醒时，设置为编辑模式
          store.commit('SET_USE_DRAFT', false)
          store.commit('SET_GROUP_NO', groupNumber)
          store.commit('SET_ADD_TYPE', 'batchPushNotify') // 设置关联新建的类型  新建回款单的时候 走不同的保存接口
          store.commit('SET_LINK_ADD_DATA', options.dataIdList) // 设置关联新建的数据
          store.commit('SET_FORM_SAASOBJ', saasObj)
          store.commit('SET_FORM_DATA_ID', null)
          store.commit('SET_FIELD_POSITION', fieldPosition)
          const linkAddData = getLinkAddVal(explainList)
          const formatData = setFormatData(linkAddData, explainList)
          store.commit('SET_FORM_DATA', formatData)
          store.commit('SET_MATE_DATA', result.data)
          store.commit('SET_DIALOG_FORM_NAME', name)
          store.commit('SET_IS_MERGE_INVOICE', saasObj ? saasObj.isMergeInvoice : 0)
          const params = {
            appId,
            menuId,
            formId: id, // 工作报告是formId为0的，需要那get接口拿到的id
            saasMark,
            businessType,
            dataIdList: options.dataIdList
          }
          store.commit('SET_FORM_QUERY', params)
          store.commit('SET_SAAS_SPECIA_PARAM_POJO', {})
          store.commit('SET_SERIAL_NO', serialNo)
          store.commit('SET_EXPKAIN_LIST', explainList)
          store.commit('SET_DIALOG_SHOW', true)
          store.commit('SET_WRITE_OFF_AMOUNT', writeOffAmount || 0)
        }
      )
      console.log(options)
    },
    refreshSelectTag(val) {
      this.recordInfo.selectTagList = val
    }
  }
}
</script>

<style lang="scss" scoped>
.xbb-view {
  box-sizing: border-box;
  height: 432px;
  background-color: $base-white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  .normal-status {
    padding: 20px;
    overflow: hidden;
    .dial {
      padding: 10px 0;
    }
    .call-log {
      .title {
        padding: 10px 0;
        font-size: 14px;
        line-height: 1;
        color: $text-plain;
      }
      .call-list {
        border-bottom: 1px solid $neutral-color-3;
        .item {
          box-sizing: border-box;
          display: flex;
          height: 50px;
          border-top: 1px solid $neutral-color-3;
          .status {
            flex: 0 0 35px;
            width: 35px;
            padding-top: 8px;
            color: #c0c8d8;
            text-indent: 5px;
            &.red {
              color: red;
            }
          }
          .infos {
            flex: 1 1 auto;
            padding-top: 8px;
            overflow: hidden;
            font-size: 12px;
            text-overflow: ellipsis;
            .phone-num {
              margin-bottom: 7px;
              color: $bg-menu;
              cursor: pointer;
            }
            .name {
              overflow: hidden;
              color: $text-auxiliary;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .date {
            flex: 0 0 50px;
            width: 50px;
            padding-top: 6px;
            font-size: 12px;
            line-height: 1.4;
            color: $text-auxiliary;
            text-align: right;
          }
        }
        .empty {
          height: 50px;
          font-size: 14px;
          line-height: 50px;
          text-align: center;
          border-top: 1px solid $neutral-color-3;
        }
      }
    }
  }
  .incall-status {
    height: 100%;
    color: $base-white;
    text-align: center;
    background: url('./imgs/xbb-call-bg.png') center center no-repeat;
    background-size: 100% 100%;
    .calling {
      position: relative;
      box-sizing: border-box;
      height: 100%;
      padding: 30px 0;
      .box {
        box-sizing: border-box;
        height: 240px;
        &.info {
          padding-top: 15px;
        }
        &.btn-num {
          position: absolute;
          width: 100%;
        }
        .tap-content {
          position: relative;
          height: 24px;
          overflow: hidden;
          font-size: 23px;
          letter-spacing: 2px;
        }
        .owner-name {
          margin: 10px auto 0;
          font-size: 12px;
          text-align: center;
        }
        .call-status {
          margin-top: 40px;
          font-size: 16px;
          opacity: 0.8;
        }
        .numeric-keyboard {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-around;
          width: 204px;
          margin: 18px auto 0;
          overflow: hidden;
          .btn-key {
            position: relative;
            width: 32px;
            height: 32px;
            margin: 9px 18px;
            font-size: 20px;
            line-height: 32px;
            color: $base-white;
            text-align: center;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.3);
            border: 0;
            border-radius: 50%;
            outline: none;
            &:hover {
              background: rgba(255, 255, 255, 0.2);
            }
            i {
              font-style: normal;
            }
            &:nth-of-type(10) {
              i {
                position: absolute;
                top: 3px;
                left: 8px;
                font-size: 28px;
              }
            }
          }
        }
      }
      .btn-group {
        position: absolute;
        bottom: 30px;
        width: 100%;
        .web-icon-dial {
          position: relative;
          display: inline-block;
          width: 60px;
          height: 60px;
          margin: 0 28px;
          font-size: 60px;
          cursor: pointer;
          border-radius: 30px;
          transition: all 0.3s;
          &:after {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: -1;
            width: 40px;
            height: 40px;
            content: '';
            background-color: $base-white;
            transition: all 0.3s;
          }
          &.pick-up {
            color: #65b35a;
            transform: rotate(-90deg);
          }
          &.hang-up {
            color: $error-color-5;
            transform: rotate(136deg);
          }
        }
        .to-btn {
          position: absolute;
          top: 15px;
          right: 65px;
          display: block;
          width: 30px;
          height: 30px;
          line-height: 30px;
          cursor: pointer;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }
          .web-icon-Oval {
            font-size: 12px;
          }
        }
      }
    }
  }
  .websocket-error {
    padding: 20px;
    .title {
      padding-bottom: 20px;
      font-size: 16px;
      color: $text-main;
      text-align: center;
    }
    .text {
      margin-bottom: 10px;
      font-size: 13px;
      line-height: 1.6;
      color: $text-plain;
    }
    .tel {
      margin-top: 10px;
      font-size: 14px;
      color: $text-main;
    }
  }
  .is-not-chrome {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    text-align: center;
    .text {
      position: absolute;
      top: 50%;
      right: 20px;
      left: 20px;
      margin-top: -20px;
      line-height: 1.6;
      color: #fc484f;
    }
  }
  .is-sealing {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    overflow: hidden;
    text-align: center;
    .text_waiting {
      margin: 18px 22px;
      font-size: 15px;
      line-height: 20px;
      color: $text-plain;
      text-align: left;
    }
    .circle {
      position: absolute;
      top: 110px;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 120px;
      color: rgba(16, 16, 16, 1);
      text-align: center;
      background-color: rgba(206, 161, 112, 1);
      border: 1px solid rgba(255, 255, 255, 0);
      border-radius: 50%;
      transform: translate(-50%);
      .text {
        color: $base-white;
        cursor: default;
        &-time {
          margin-top: 4px;
        }
      }
    }
    .reminder {
      position: absolute;
      bottom: 47px;
      left: 50%;
      color: rgba(16, 16, 16, 0.33);
      cursor: default;
      transform: translate(-50%);
    }
  }
  .xbb-view__left-btn {
    position: absolute;
    bottom: 0;
    left: -32px;
    z-index: 3;
    display: flex;
    flex-direction: column;
    align-self: flex-end;
    padding: 2px;
    margin-right: 6px;
    text-align: center;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 2px 5px 2px rgba(0, 0, 0, 0.1);
    .t-iconfont {
      padding: 4px;
      font-size: 16px;
      color: #646566;
      cursor: pointer;
      &:hover {
        background-color: #f2f3f5;
      }
    }
    .active {
      color: #ff6a00;
      background-color: #f2f3f5;
    }
  }
  .xbb-view__record {
    position: absolute;
    bottom: 0;
    left: -420px;
    width: 375px;
    height: 518px;
  }
}
.change-card {
  height: 100%;
}
</style>
