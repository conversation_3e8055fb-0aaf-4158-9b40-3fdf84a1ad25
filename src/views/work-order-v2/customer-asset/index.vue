<!--
 * @Description: 客户资产列表页
-->
<template>
  <div class="service-evaluation">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          ref="listPanel"
          :custom-mode-config="{ viewConfig, viewButton }"
          :gantt-head-list="ganttViewInfo.ganttHeadList"
          :is-stage-view="viewStatus.isSaasStage"
          :show-filter-list="specialFilter"
          :stage-head-list="stageViewInfo.stageHeadList"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @changeGroupLaneVersion="changeGroupLaneVersion"
          @getFormData="panelFilterParams"
          @getLaneVersionData="getLaneVersionData"
          @getShowSwitchPhase="getShowSwitchPhase"
          @onViewChange="onViewChange"
        >
        </list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams,
            commonFilter,
            catchAppInfo,
            ...stageProps
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onStageView="onStageView"
          @onTableView="onTableView"
          @oncalendarView="oncalendarView"
        >
        </component>
      </div>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'
import { getWorkOrderV2List } from '@/api/work-order-v2/common'

export default {
  name: 'ServiceEvaluation',

  mixins: [listMixin, listEditMixin],

  data() {
    return {}
  },

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      const { menuId } = this.catchAppInfo
      params.menuId = menuId
      this.initViewType({ params, api: getWorkOrderV2List })
    }
  }
}
</script>

<style lang="scss" scoped>
.service-evaluation {
  height: 100%;
}
</style>
