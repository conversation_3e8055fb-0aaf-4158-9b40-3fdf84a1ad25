<template>
  <div id="staff-table">
    <div v-if="isSingleStaff" class="single-staff-content">
      <div class="single-staff-content__title">
        <p class="staff-name">{{ singleStaffInfo.userName }}</p>
        <p>距离：{{ singleStaffInfo.distance | formatDistance }}</p>
        <p>进行中的工单数：{{ singleStaffInfo.unfinishedNum }}</p>
      </div>
      <div class="single-staff-content__card-list">
        <card
          v-for="item in singleStaffInfo.workOrderList"
          :key="item.sheetNo"
          :show-border-bottom="true"
          :show-info="item | formatShowInfo"
          :work-order-info="item.linkParams"
        ></card>
      </div>
    </div>
    <el-table v-else v-loading="loading" v-scroll="loadMore" :data="tableData" height="220px">
      <el-table-column
        align="left"
        header-align="center"
        label="员工"
        prop="userName"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="staff-name" v-html="addNodeByWX(scope.row.userName)"></div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="未完成工单" prop="unfinishedNum"> </el-table-column>
      <el-table-column align="center" label="距离" prop="distance">
        <template slot-scope="scope">
          {{ scope.row.distance | formatDistance }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <div class="assign-operate" @click="assignStaff(scope.row)">
            <i class="icon-sendMessage iconfont"></i>
            <span>指派</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { Loading } from 'element-ui'
import Card from '@/views/work-order-v2/components/workorder-map/work-order-card.vue'

export default {
  components: {
    Card
  },
  props: {
    isSingleStaff: {
      type: Boolean,
      default: false
    },
    singleStaffInfo: {
      type: Object,
      default: () => ({})
    },
    tableData: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    wholeLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageInfo: {
        page: 1,
        pageSize: 10
      },
      windowLoading: null
    }
  },
  watch: {
    wholeLoading(val) {
      if (val) {
        this.windowLoading = Loading.service({
          target: document.getElementById('staff-table')
        })
      } else {
        this.windowLoading && this.windowLoading.close()
      }
    }
  },
  filters: {
    formatShowInfo(info) {
      if (!info) return []
      const arr = []
      arr.push(info.sheetNo)
      arr.push(`预计开始时间： ${info.planStartDate}`)
      arr.push(`预计时长： ${info.planDate}`)
      arr.push(`预计结束时间： ${info.planEndDate}`)
      return arr
      // return [
      //   info.sheetNo,
      //   `预计开始时间： ${info.planStartDate}`,
      //   `预计时长： ${info.planDate}`
      //   `预计结束时间： ${info.planEndDate}`
      // ]
    },
    formatDistance(distance) {
      if (isNaN(+distance)) return distance
      if (+distance > 1000) {
        return (distance / 1000).toFixed(2) + 'km'
      } else {
        return `${distance}m`
      }
    }
  },
  methods: {
    loadMore() {
      this.pageInfo.page += 1
      this.$emit('load-more', this.pageInfo)
    },
    assignStaff(row) {
      this.$emit('assign-staff', row.userId, row)
    }
  },
  directives: {
    scroll: {
      bind(el, binding) {
        const target = el.querySelector('.el-table__body-wrapper')
        target.addEventListener('scroll', function () {
          const scrollTop = target.scrollTop
          if (scrollTop + target.clientHeight >= target.scrollHeight) {
            const loadData = binding.value
            loadData()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
#staff-table {
  width: 360px;
  padding: 0 16px 16px;
  .staff-name {
    @include singleline-ellipsis;
    width: 100%;
  }
  .single-staff-content {
    &__title {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 10px;
      background-color: $bg-table;
      .staff-name {
        @include singleline-ellipsis;
        max-width: 80px;
      }
      p {
        height: 20px;
        font-size: 14px;
        line-height: 20px;
      }
    }
    &__card-list {
      height: 180px;
      overflow-y: scroll;
    }
  }
  .assign-operate {
    cursor: pointer;
  }
}
</style>
