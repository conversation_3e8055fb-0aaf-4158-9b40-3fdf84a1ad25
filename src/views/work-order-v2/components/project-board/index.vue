<template>
  <div ref="stagesContainer" class="stages-container">
    <div class="stage-sort">
      <div
        v-for="(stage, index) in stageList"
        :id="stage.id"
        :key="stage.id"
        class="stages-container__item"
      >
        <stage
          :ref="`stage-${stage.id}`"
          :add-risk-authority="addRiskAuthority"
          :add-task-authority="addTaskAuthority"
          :condition-list="conditionList"
          :need-get-list-num="needGetListNum"
          :query="query"
          :risk-status-list="riskStatusList"
          :stage-index="index"
          :stage-info="stage"
          :stage-operate-list="stageOperateList"
          :task-list="stage.items"
          :task-status-list="taskStatusList"
          @add-stage-after="newStageAfter(stage)"
          @adjust-time="adjustTime(stage)"
          @alter-status="alterStageStatus"
          @delete="deleteStage(stage)"
          @edit="editStage(stage)"
          @setExecutionEvent="setExecutionEvent"
        />
        <div class="new-stage-container" :class="`new-stage-container__${stage.id}`"></div>
      </div>
    </div>
    <div class="stages-container__item">
      <div
        class="new-stage-button"
        :class="{ 'text-active': showNewStageCard && stageOperateInfo.operateType === 'add-stage' }"
        @click="newStage($event)"
      >
        +新建项目阶段
      </div>
      <div class="new-stage-container new-stage-container__new"></div>
    </div>
    <!-- 阶段更多操作--在此后新增、编辑的新建弹窗 -->
    <div v-show="false" id="add-stage-after-container">
      <div id="add-stage-after">
        <new-stage-card
          v-if="showNewStageCard"
          :key="stageOperateInfo.id"
          ref="newStageCard"
          :stage-operate-info="stageOperateInfo"
          @cancel="clearNewEdit"
          @stage-card-confirm="handleStageCardConfirm"
        />
      </div>
    </div>
    <!-- 调整时间弹窗 -->
    <AdjustTime
      :adjust-visible.sync="adjustVisible"
      :query="query"
      set-project
      @handler-adjust-time="handlerAdjustTime"
    />
    <ExecutionEventDialog
      v-if="executionEventVisible"
      :current-task-params="currentTaskParams"
      :update-setting-tip-fn="updateSettingTipFn"
      @close="executionEventVisible = false"
      @update-set="updateExecutionEvent"
    />
  </div>
</template>

<script>
import Stage from './stage/index.vue'
import NewStageCard from './stage/new-stage-card.vue'
import AdjustTime from '@/components/form-data-detail/components/tabs/work-order-v2/detail-bord/components/gantt/components/adjust-time.vue'
import ExecutionEventDialog from '@/views/work-order-v2/components/project-board/components/execution-event-dialog.vue'
import Sortable from 'sortablejs'
import { sortStage, sortTask, getTaskStatusList } from '@/api/work-order-v2/project-stage.js'
import { sortTemplateStage, sortTemplateTask } from '@/api/work-order-v2/project-setting.js'
import xbb from '@xbb/xbb-utils'
import { mapGetters, mapState } from 'vuex'
import { useLowCodeBeforeStageChangeHook } from '@/components/cloudcode/hooks/saas-project'

export default {
  name: 'ProjectBoard',
  components: {
    Stage,
    NewStageCard,
    AdjustTime,
    ExecutionEventDialog
  },
  inject: ['isBoardTemplateSet'],
  props: {
    // 阶段的更多操作列表
    stageOperateList: {
      type: Array,
      default: () => [
        {
          value: 'edit',
          label: '编辑'
        },
        {
          value: 'add-stage-after',
          label: '在此后新增阶段'
        },
        {
          value: 'delete',
          label: '删除'
        }
      ]
    },
    // 阶段列表
    stageList: {
      type: Array,
      default: () => []
    },
    // 风险的编辑权限
    addRiskAuthority: {
      type: Number,
      default: 1
    },
    // 任务的编辑权限
    addTaskAuthority: {
      type: Number,
      default: 1
    },
    // 筛选条件
    conditionList: {
      type: Array,
      default: () => []
    },
    query: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showNewStageCard: false,
      stageOperateInfo: null,
      echoFormData: {}, // 编辑时，弹框回显的阶段信息
      adjustVisible: false,
      executionEventVisible: false,
      containerEl: null,
      boardScrollLeft: 0,
      boardWidth: 0,
      taskStatusList: [],
      riskStatusList: [],
      currentTaskParams: {},
      updateSettingTipFn: null
    }
  },
  mounted() {
    this.containerEl = this.$refs.stagesContainer
    this.containerEl.addEventListener('scroll', this.updateBoardScrollLeft)
    // 监听看板大小，实时更新宽度
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        this.boardWidth = entry.contentRect.width
      }
    })
    observer.observe(this.containerEl)
    this.initStageSort()
    // 获取任务、风险状态列表
    getTaskStatusList().then(({ result }) => {
      this.taskStatusList = this.formmatTaskStatusList(xbb._get(result, 'itemMap.task')) || []
      this.riskStatusList = this.formmatTaskStatusList(xbb._get(result, 'itemMap.risk')) || []
    })
  },
  computed: {
    stageListLength() {
      return this.stageList.length
    },
    sortRequestOption() {
      return {
        sortStageApi: this.isBoardTemplateSet ? sortTemplateStage : sortStage,
        sortTaskApi: this.isBoardTemplateSet ? sortTemplateTask : sortTask
      }
    },
    needGetListNum() {
      return Math.ceil((this.boardWidth + this.boardScrollLeft) / 280)
    },
    ...mapState({
      lowCode: function (state) {
        return state.lowcode.instant[this.query.formId]
      }
    }),
    ...mapGetters(['feeType'])
  },
  watch: {
    stageListLength: {
      handler: function () {
        this.$nextTick(() => {
          this.initTaskSort()
        })
      },
      immediate: true
    }
  },
  methods: {
    formmatTaskStatusList(list) {
      if (!Array.isArray(list)) return 0
      return list.map((item) => {
        const { text, value, color } = item
        return {
          text,
          value,
          color,
          isHide: (value === '3' && this.isBoardTemplateSet) || value === '4'
        }
      })
    },
    // 更新看板向左滚动的距离
    updateBoardScrollLeft() {
      this.boardScrollLeft = this.containerEl.scrollLeft // 容器的滚动位置
    },
    initStageSort() {
      const el = document.querySelector('.stage-sort')
      this.sortable = Sortable.create(el, {
        handle: '.stage__header',
        scroll: true,
        fallbackOnBody: true,
        ghostClass: 'not-use-default',
        onEnd: (evt) => {
          // eslint-disable-next-line vue/no-mutating-props
          const targetRow = this.stageList.splice(evt.oldIndex, 1)[0]
          // eslint-disable-next-line vue/no-mutating-props
          this.stageList.splice(evt.newIndex, 0, targetRow)
          this.sortStage()
        }
      })
    },
    initTaskSort() {
      const elList = Array.from(document.getElementsByClassName('task-card-sort'))
      if (!elList.length) return
      elList.forEach((el) => {
        Sortable.create(el, {
          handle: '.task-card',
          group: 'shared', // set both lists to same group
          animation: 150,
          scroll: true,
          fallbackOnBody: true,
          ghostClass: 'not-use-default',
          onEnd: (evt) => {
            this.sortTask(evt)
          }
        })
      })
    },
    sortStage() {
      const sortList = this.stageList.map((item, index) => {
        return {
          sort: index + 1,
          id: item.id
        }
      })
      const params = {
        list: sortList
      }
      if (this.isBoardTemplateSet) {
        params.templateId = xbb._get(this, 'stageList[0].templateId')
      } else {
        params.dataId = xbb._get(this, 'stageList[0].projectQuery.dataId')
        params.businessType = xbb._get(this, 'stageList[0].projectQuery.businessType')
        params.menuId = xbb._get(this, 'stageList[0].projectQuery.menuId')
        params.saasMark = xbb._get(this, 'stageList[0].projectQuery.saasMark')
      }
      this.sortRequestOption
        .sortStageApi(params)
        .then((res) => {
          this.$message({
            message: res.msg,
            type: 'success'
          })
        })
        .catch(() => {})
    },
    sortTask(evt) {
      const beforeStageId = evt.from.getAttribute('data-stage-id')
      const changStageId = evt.to.getAttribute('data-stage-id')
      const beforeStageEl = this.$refs[`stage-${beforeStageId}`] || {}
      const changeStageEl = this.$refs[`stage-${changStageId}`] || {}
      // 存一下原始数据，接口请求错误恢复之前的样子
      const originBeforeTaskList = beforeStageEl[0].taskList.slice()
      const originchangeTaskList = changeStageEl[0].taskList.slice()
      // 拖动元素原先所在列表
      const beforeTaskList = JSON.parse(evt.from.getAttribute('data-task-list'))
      // 当前拖动的元素
      const targetRow = beforeTaskList.splice(evt.oldIndex, 1)[0]
      // 拖动元素拖动到的列表
      let changTaskList = JSON.parse(evt.to.getAttribute('data-task-list')) || []
      if (beforeStageId === changStageId) {
        beforeTaskList.splice(evt.newIndex, 0, targetRow)
        changTaskList = beforeTaskList
        // 更新改变阶段的任务列表
        changeStageEl[0].taskList = beforeTaskList
      } else {
        this.changeTargetTaskStage(targetRow, changeStageEl[0]) // 改变当前拖动任务的所属阶段
        changTaskList.splice(evt.newIndex, 0, targetRow)
        // 更新改变阶段的任务列表
        beforeStageEl[0].taskList = beforeTaskList
        changeStageEl[0].taskList = changTaskList
      }

      const sortList = changTaskList.map((item, index) => {
        return {
          sort: index + 1,
          id: item.id,
          stageId: +changStageId
        }
      })
      const params = {
        list: sortList
      }
      if (this.isBoardTemplateSet) {
        params.templateId = xbb._get(this, 'stageList[0].templateId')
      } else {
        params.dataId = xbb._get(this, 'stageList[0].projectQuery.dataId')
      }
      this.sortRequestOption
        .sortTaskApi(params)
        .then((res) => {
          this.$message({
            message: res.msg,
            type: 'success'
          })
        })
        .catch(() => {
          // 恢复拖动前的样子
          beforeStageEl[0].taskList = originBeforeTaskList
          changeStageEl[0].taskList = originchangeTaskList
        })

      console.log(sortList)
    },
    /**
     * 改变任务的所属阶段
     *
     * @param {Object} task 要改变的任务。
     * @param {Object} targetStage 要变更的目标阶段
     */
    changeTargetTaskStage(task, targetStage) {
      const stageField = task.fieldList.find((item) => item.attr === 'text_6')
      if (stageField) {
        const targetStageName = targetStage.stageInfo.name
        stageField.value = targetStageName
      }
    },
    // 新增项目阶段
    newStage(event) {
      this.showNewStageCard = true
      this.stageOperateInfo = {
        operateType: 'add-stage'
      }
      const newDiv = document.getElementById('add-stage-after')
      const appointedDiv = document.querySelector('.new-stage-container__new')
      appointedDiv.insertAdjacentElement('afterbegin', newDiv)
    },
    beforeStageChange(data) {
      // 低代码HOOK 项目阶段被修改时(增、删、改、调整阶段状态、调整时间，根据data['type']做区分)
      if (!this.$feeType.checkFeatureEnable('lowcode')) return { flag: true, exection: false }
      return useLowCodeBeforeStageChangeHook(
        {
          ...this.query
        },
        data
      )
    },
    // 阶段更多操作-改变阶段状态
    async alterStageStatus(data) {
      const res = await this.beforeStageChange({
        data,
        type: 'alter-status'
      })
      if (!res.flag) {
        return false
      }
      this.$emit('trigger-board-event', {
        type: 'alter-status',
        data
      })
    },
    // 阶段更多操作-在此后新建
    newStageAfter(stage) {
      this.stageOperateInfo = {
        ...stage,
        operateType: 'add-stage-after'
      }
      this.showNewStageCard = true
      // this.$refs.newStageCard && this.$refs.newStageCard.clearFormData()
      this.showSatgeCard(stage.id)
    },
    // 阶段更多操作-编辑
    editStage(stage) {
      this.stageOperateInfo = {
        ...stage,
        operateType: 'edit'
      }
      this.showNewStageCard = true
      this.echoFormData = {
        stageName: stage.name,
        timeRange: [stage.startTime * 1000, stage.endTime * 1000]
      }
      this.$nextTick(() => {
        // this.$refs.newStageCard && this.$refs.newStageCard.assignFormData(formData)
        this.showSatgeCard(stage.id, 'edit')
      })
    },
    // 阶段更多操作-删除
    async deleteStage(stage) {
      const res = await this.beforeStageChange({
        type: 'delete',
        data: {
          stageId: stage.id,
          templateId: stage.templateId
        }
      })
      if (!res.flag) {
        return false
      }
      this.$emit('trigger-board-event', {
        type: 'delete',
        data: {
          stageId: stage.id,
          templateId: stage.templateId
        }
      })
    },
    // 阶段更多操作-调整时间
    adjustTime(stage) {
      this.stageOperateInfo = stage
      this.adjustVisible = true
    },
    // 显示阶段新增、编辑的卡片
    showSatgeCard(id, type) {
      const newDiv = document.getElementById('add-stage-after')
      let appointedDiv
      if (type === 'edit') {
        appointedDiv = document.querySelector(`.new-stage-container__${id}`)
        appointedDiv.insertAdjacentElement('afterbegin', newDiv)
      } else {
        appointedDiv = document.getElementById(id)
        appointedDiv.insertAdjacentElement('afterend', newDiv)
      }
    },
    // 将在此后新增(或编辑)时弹出的卡片隐藏掉
    clearNewEdit() {
      this.showNewStageCard = false
      const card = document.getElementById('add-stage-after')
      const cardContainer = document.getElementById('add-stage-after-container')
      cardContainer.insertAdjacentElement('afterbegin', card)
    },
    async handleStageCardConfirm(info) {
      let emitData = {}
      switch (info.operateType) {
        case 'add-stage-after':
          emitData = {
            ...info.data,
            frontStageId: this.stageOperateInfo.id
          }
          break
        case 'edit':
          emitData = {
            ...info.data,
            stageId: this.stageOperateInfo.id,
            templateId: this.stageOperateInfo.templateId
          }
          break
        case 'add-stage':
          emitData = {
            ...info.data
          }
          break
        default:
          break
      }
      const res = await this.beforeStageChange({
        type: info.operateType,
        data: emitData
      })
      if (!res.flag) {
        return false
      }
      this.clearNewEdit()
      this.$emit('trigger-board-event', {
        type: info.operateType,
        data: emitData
      })
    },
    async handlerAdjustTime(data, callback) {
      const res = await this.beforeStageChange({
        data: {
          ...data,
          stageId: this.stageOperateInfo.id
        },
        type: 'adjust-time'
      })
      if (!res.flag) {
        return false
      }
      this.$emit('trigger-board-event', {
        type: 'adjust-time',
        data: {
          ...data,
          stageId: this.stageOperateInfo.id
        },
        callback
      })
      this.adjustVisible = false
    },
    // 预置任务设置执行事件
    setExecutionEvent(data, updateSettingTip) {
      this.currentTaskParams = data
      this.executionEventVisible = true
      this.updateSettingTipFn = updateSettingTip
    },
    // 更新执行事件
    updateExecutionEvent() {}
  }
}
</script>

<style lang="scss" scoped>
.stages-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 200px;
  overflow-x: scroll;
  overflow-y: hidden;
  .stage-sort {
    display: flex;
    height: 100%;
  }
  &__item {
    position: relative;
    .new-stage-button {
      width: 120px;
      padding: 10px;
      margin-right: 200px;
      font-size: 16px;
      color: $text-plain;
      cursor: pointer;
    }
    .new-stage-button:hover,
    .text-active {
      color: $brand-color-5;
    }
    .new-stage-container {
      position: absolute;
      top: 26px;
      left: 0;
    }
  }
}
#add-stage-after {
  padding: 10px;
}
</style>
