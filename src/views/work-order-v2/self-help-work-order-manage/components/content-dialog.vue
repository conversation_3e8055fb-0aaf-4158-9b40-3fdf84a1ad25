<template>
  <el-dialog
    append-to-body
    :before-close="closeContentDialog"
    class="content-dialog custom-dialog"
    title="消息内容"
    :visible.sync="dialogVisible"
    width="800px"
  >
    <div class="content-dialog-tooltip">
      消息内容存在字数限制，若超过限制则会导致消息发送失败，详见
      <a @click="openNewDocument">微信消息接口文档</a>
    </div>

    <p>{{ $t('nouns.field') }}({{ fieldLength }}/1) {{ $t('nouns.custom') }} ({{ zLen }}/1)</p>
    <div class="tag-div">
      <el-tag
        v-for="(tag, index) in selectWidgetList"
        :key="index"
        closable
        :disable-transitions="false"
        size="medium"
        :type="tag.attr ? '' : 'info'"
        @close="handleClose(tag)"
      >
        {{ tag.attrName }}
      </el-tag>
      <el-input
        v-if="inputVisible"
        ref="saveTagInput"
        v-model="inputValue"
        class="input-new-tag"
        :max="5"
        :maxlength="5"
        size="mini"
        @blur="handleInputConfirm"
        @keyup.enter.native="handleInputConfirm"
      >
      </el-input>
      <template v-else>
        <el-button
          v-if="zLen < 1"
          class="button-new-tag"
          icon="el-icon-plus"
          size="mini"
          @click="showInput"
          >{{ $t('nouns.custom') }}</el-button
        >
      </template>
    </div>
    <div class="select-list">
      <template v-if="widgetList.length <= 0">
        <div class="no-fields">
          <span>没有可作为内容的字段</span>
        </div>
      </template>
      <template v-else>
        <el-checkbox-group v-model="selectWidgetList" :max="max">
          <el-checkbox
            v-for="item in widgetList"
            :key="item.attr"
            :label="item"
            style="margin-bottom: 5px"
            @click.native="checkboxClick(item.attr)"
          >
            {{ item.attrName }}
          </el-checkbox>
        </el-checkbox-group>
      </template>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeContentDialog">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    // 控制显隐
    contentDialogVisible: {
      type: Boolean,
      default: false
    },
    // 标识对应的字段标题
    tableData: {
      type: Object,
      default: () => {}
    },
    // 所有可选系统字段列表
    widgetList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectMap: {},
      selectWidgetList: [], // 选中的字段列表
      modelValList: [],
      inputVisible: false,
      inputValue: ''
    }
  },
  computed: {
    attrNameMap() {
      const res = {}
      this.widgetList.forEach((item) => {
        res[item.attr] = item.attrName
      })
      return res
    },
    fieldMap() {
      const res = {}
      this.widgetList.forEach((item) => {
        res[item.attr] = item
      })
      return res
    },
    max() {
      const res = 1
      const constTitleLength = this.selectWidgetList.filter((item) => !item.attr).length
      return res + constTitleLength
    },
    fieldLength() {
      let res = 0
      res = this.selectWidgetList.filter((item) => item.attr).length
      return res
    },
    zLen() {
      return this.selectWidgetList.filter((item) => !item.attr).length
    }
  },
  watch: {
    contentDialogVisible(val) {
      this.dialogVisible = val
    }
  },
  methods: {
    // 删除标签
    handleClose(tag) {
      tag.check = false
      this.selectWidgetList.splice(this.selectWidgetList.indexOf(tag), 1)
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      const inputValue = this.inputValue.trim()
      const zLen = this.selectWidgetList.filter((item) => item.content).length
      if (inputValue && zLen < 1) {
        this.selectWidgetList.push({
          attr: '', // 生成标签时，前端生成的唯一键
          attrName: inputValue // 子表单内部attr
        })
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    // 选中标签
    checkboxClick(attr) {
      const fieldTitles = this.selectWidgetList.filter((item) => item.attr)
      if (fieldTitles.length >= 1) {
        const attrs = fieldTitles.map((item) => item.attr)
        if (!attrs.includes(attr)) {
          this.$message({
            message: '数据最多选择1个字段',
            type: 'warning'
          })
        }
      }
    },
    init() {
      if (this.tableData.messageContent) {
        this.selectWidgetList = JSON.parse(JSON.stringify(this.tableData.messageContent))
      }
    },
    submit() {
      this.$emit('submit', { selectWidgetList: this.selectWidgetList, key: this.tableData.key })
      this.closeContentDialog()
    },
    // 关闭弹窗
    closeContentDialog() {
      this.$emit('closeContentDialog')
      this.selectWidgetList = []
    },
    openNewDocument() {
      window.open(
        'https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html'
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.content-dialog {
  :deep(.el-dialog__body) {
    padding-top: 15px;
  }
  &-tooltip {
    height: 32px;
    padding: 0 16px;
    margin-bottom: 8px;
    font-size: 12;
    font-weight: 400;
    line-height: 32px;
    color: $text-auxiliary;
    background: $bg-table;
  }
  .no-fields {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 20px;
    & > span {
      font-size: 20px;
    }
  }
  .el-checkbox {
    margin-left: 10px;
  }
  .el-checkbox + .el-checkbox {
    margin-right: 10px;
    margin-left: 10px;
  }

  .tag-div {
    height: 90px;
    padding: 10px 10px;
    margin: 10px auto;
    border: 1px solid $text-grey;
    & > .el-tag {
      margin-right: 5px;
    }
  }
  .input-new-tag {
    width: 90px;
  }
  .button-new-tag {
    width: 90px;
  }
  .select-list {
    height: 200px;
    padding: 10px 0;
    margin: 10px auto;
    overflow: scroll;
    border: 1px solid $text-grey;
  }
}
</style>
