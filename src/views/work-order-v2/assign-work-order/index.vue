<template>
  <div class="assign-work-order">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          ref="listPanel"
          :custom-mode-config="{ viewConfig, viewButton }"
          :gantt-head-list="ganttViewInfo.ganttHeadList"
          :is-stage-view="viewStatus.isSaasStage"
          :show-column-setting="isFieldSetting"
          :show-filter-list="specialFilter"
          :stage-head-list="stageViewInfo.stageHeadList"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @changeGroupLaneVersion="changeGroupLaneVersion"
          @getFormData="panelFilterParams"
          @getLaneVersionData="getLaneVersionData"
          @getShowSwitchPhase="getShowSwitchPhase"
          @onViewChange="onViewChange"
        >
        </list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams,
            commonFilter,
            catchAppInfo,
            ...stageProps,
            tableData: workOrderTableData
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          v-loading="boxLoading"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onStageView="onStageView"
          @onTableView="onTableView"
          @onWorkOderGanttView="onWorkOderGanttView"
          @onWorkOderMapView="onWorkOderMapView"
          @oncalendarView="oncalendarView"
        >
        </component>
      </div>
      <template slot="layout-dialog">
        <WorkOrderV2AssginDialog
          v-if="showBatchAssgindialog"
          active-tab-name="user"
          :dialog-visible.sync="showBatchAssgindialog"
          :multiple="{ user: false }"
          :show-tabs-name="['user']"
          :tab-multiple="true"
          :work-order-info="{ formId: getAppInfo.formId, workOrderId: null }"
          @dialog-submit="submitWorkOrderV2BatchAssgin"
        />
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'
import workOrderV2BatchMixin from '@/views/work-order-v2/mixin/batch-handle-mixin.js'
import viewLoadingMixin from '@/views/work-order-v2/mixin/view-loading-mixin'
import { getWorkOrderV2List } from '@/api/work-order-v2/common.js'
import { ViewTypeEnum } from '@/constants/enum/ui-paas/enum.view-type.js'

export default {
  name: 'AssignWorkOrder',

  mixins: [listMixin, listEditMixin, workOrderV2BatchMixin, viewLoadingMixin],

  data() {
    return {
      // ui-paas
      // TODO: 兼容未开通ui-paas化，塞入老逻辑默认视图
      // viewConfig: [
      //   { type: ViewTypeEnum.PAAS_LISTVIEW, name: '列表视图', icon: 't-iconfont icon-table-2' },
      //   {
      //     type: ViewTypeEnum.SAAS_WORK_ORDER_MAPVIEW,
      //     name: '地图视图',
      //     icon: 't-iconfont icon-road-map-fill'
      //   },
      //   {
      //     type: ViewTypeEnum.SAAS_WORK_ORDER_GANTVIEW,
      //     name: '甘特视图',
      //     icon: 't-iconfont icon-timeline-view'
      //   }
      // ]
    }
  },

  created() {
    // TODO: 兼容未开通ui-paas化，塞入老逻辑默认视图
    this.isWorkOrderMultipleView &&
      this.viewConfig.push(
        {
          type: ViewTypeEnum.SAAS_WORK_ORDER_MAPVIEW,
          name: '地图视图',
          icon: 't-iconfont icon-road-map-fill'
        },
        {
          type: ViewTypeEnum.SAAS_WORK_ORDER_GANTVIEW,
          name: '甘特视图',
          icon: 't-iconfont icon-timeline-view'
        }
      )
  },

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      const { menuId } = this.catchAppInfo
      params.menuId = menuId
      this.initViewType({ params, api: getWorkOrderV2List })
    }
  }
}
</script>

<style lang="scss" scoped>
.assign-work-order {
  height: 100%;
}
</style>
