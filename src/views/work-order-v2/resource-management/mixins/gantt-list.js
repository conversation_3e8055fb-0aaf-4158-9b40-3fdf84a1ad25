import {
  groupMember,
  getMemberTask,
  setGroupMember
} from '@/api/work-order-v2/resource-management.js'
import xbb from '@xbb/xbb-utils'

import { mapMutations } from 'vuex'

export default {
  data() {
    return {
      rootPersonList: [],
      groupData: { defaultGroup: 0, listGroupId: 0 },
      currentRequestTime: '',
      leftRequestTime: '',
      rightRequestTime: ''
    }
  },

  computed: {
    // 当前年份
    currentYear() {
      return new Date().getFullYear()
    },
    // 当前月份
    currentMonth() {
      return new Date().getMonth() + 1
    },
    // 当前日
    currentDay() {
      return new Date().getDate()
    }
  },

  created() {
    this.currentRequestTime =
      new Date(`${this.currentYear}/${this.currentMonth}/${this.currentDay}`).getTime() / 1000
  },

  methods: {
    ...mapMutations(['SET_DETAIL_DIALOG_SHOW', 'SET_DETAIL_QUERY']),
    async getGanttDataPerson() {
      const params = { ...this.queryNew, userName: this.nameLike }
      const result = await groupMember(params)
      const {
        result: {
          list,
          taskForm,
          riskForm,
          riskEditable,
          taskEditable,
          taskParentTask,
          riskParentTask,
          addRisk,
          addTask
        }
      } = result
      this.dataList = list.map((i) => {
        i.level = 0
        i.type = '3'
        i.isListView = true
        i.id = i.userId || 'noperson'
        return {
          ...i
        }
      })
      const type = localStorage.getItem('noperson-position')
      if (type === 'last') {
        const last = this.dataList.splice(0, 1)
        this.dataList.push(last[0])
      }
      this.rootPersonList = xbb.deepClone(this.dataList)
      this.taskForm = taskForm
      this.riskForm = riskForm
      this.riskEditable = riskEditable
      this.taskEditable = taskEditable
      this.taskParentTask = taskParentTask
      this.riskParentTask = riskParentTask
      this.addRiskAuthority = addRisk
      this.addTaskAuthority = addTask
      this.getGanttDataNew({ time: null })
    },
    async getGanttDataNew({ time }) {
      let _time
      if (!time) {
        // 初始化
        _time = this.currentRequestTime
      } else {
        _time = time
        this.currentRequestTime = time
      }
      const AMOUNTHTIME = 30 * 24 * 60 * 60
      const pre1 = _time - AMOUNTHTIME
      const next1 = _time + AMOUNTHTIME
      // 默认拿当前时间的 前1个月 到 后1个月

      try {
        const { taskData, memberTaskRetract } = await this.getTaskData({
          startTime: pre1,
          endTime: next1
        })
        console.log('格式化后的任务1', taskData)
        await this.dataListFormatNew(this.dataList, taskData, memberTaskRetract)
      } catch (err) {
        console.log(err)
      }
      this.ganttLoading = false
      console.log('🦃 🚀this.dataList', this.dataList)
      if (this.$refs.easygantt) {
        this.$refs.easygantt.list = []
        this.$refs.easygantt.dataFormat()
      }
    },
    /**
     * @description: 将子任务 塞入对应父级
     * @param {Array} dataList 父级数组
     * @param {Array} taskData 所有父子的子任务数组
     * @params {Array} memberTaskRetract 所有人员的数据预览
     */
    dataListFormatNew(dataList, taskData, memberTaskRetract) {
      const len = taskData.length
      return new Promise((resolve) => {
        let index = 0
        while (index < len) {
          this.$set(dataList[index], 'children', taskData[index])
          this.$set(dataList[index], 'memberTaskRetract', memberTaskRetract[index])
          index++
        }
        resolve()
      })
    },
    async getTaskData({ startTime: pre1, endTime: next1 }) {
      const result = await this.requestDataNew({ startTime: pre1, endTime: next1 })
      const memberTaskRetract = []
      const taskData = result.map((res, index) => {
        memberTaskRetract.push(res.result.memberTaskRetract)
        const data = res.result.hasTimeTaskList
        const noTime = res.result.noTimeTaskList
        const taskFormat = ({ list, level, originIds }) => {
          list.forEach((item) => {
            const getType = (type, milestone) => {
              if (milestone === 1) {
                return '4' // 里程碑
              } else if (type === 'task') {
                return '1' // 任务
              } else {
                return '2' // 风险
              }
            }
            item.type = getType(item.type, item.milestone)
            item.level = level
            item.parentId = originIds[0]
            item.originIds = originIds
            item.startTime = item.startTime && item.startTime * 1000
            item.endTime = item.endTime && item.endTime * 1000
          })
        }
        taskFormat({ list: data, level: 1, originIds: [this.dataList[index].id] })
        taskFormat({
          list: noTime,
          level: 2,
          originIds: [this.dataList[index].id, `${this.dataList[index].id}-noTime`]
        })
        data.push({
          type: '1',
          id: `${this.dataList[index].id}-noTime`,
          name: `未分配时间（${noTime.length}）`,
          level: 1,
          parentId: this.dataList[index].id,
          originIds: [this.dataList[index].id],
          startTime: null,
          endTime: null,
          hasChildren: true,
          childIdList: noTime.map((item) => item.id),
          children: noTime
        })

        return data
      })
      return { taskData, memberTaskRetract }
    },
    /**
     * @description: 获取每个人 当前时间范围的任务风险
     */
    async requestDataNew({ startTime: pre1, endTime: next1 }) {
      const arr = []
      this.dataList.forEach((member) => {
        const ownerId = member.userId || ''
        const requset = this.taskGetNew({
          startTime: pre1,
          endTime: next1,
          ownerId
        })
        arr.push(requset)
      })

      const task = await Promise.all(arr)
      return task
    },
    /**
     * @description: 组装 每个人的请求当前时间范围的任务风险
     */
    taskGetNew({ startTime: pre1, endTime: next1, ownerId }) {
      const getConditiond = (conditionList) => {
        const data = {}
        conditionList.forEach((item) => {
          data[item.attr] = item.value
        })
        return data
      }
      const { defaultGroup, listGroupId } = this.groupData
      return getMemberTask({
        startTime: pre1,
        ...getConditiond(this.conditionList),
        endTime: next1,
        ownerId,
        defaultGroup,
        listGroupId,
        ...this.queryNew
      })
    },
    handlerOpenDetailNew(data) {
      console.log(data)
      const detailInfo = {
        ...this.queryNew,
        ...data
      }

      this['SET_DETAIL_DIALOG_SHOW'](true)
      this['SET_DETAIL_QUERY']({
        appId: detailInfo.appId,
        dataId: detailInfo.dataId, // 存在父产品id的时候，要和父产品比较
        distributorMark: detailInfo.distributorMark,
        saasMark: detailInfo.saasMark,
        businessType: +detailInfo.businessType,
        subBusinessType: +detailInfo.subBusinessType,
        menuId: detailInfo.menuId,
        formId: detailInfo.formId
      })
    },
    /**
     * @description: 添加人员
     * @param {Array} val 所添加人员的userid数组
     */
    handlerAddPerson(val) {
      setGroupMember({
        ...this.queryNew,
        type: 1,
        groupOwnerId: val
      }).then((res) => {
        this.$message.success('添加成功')
        this.refreshGnattPerson()
      })
    },
    /**
     * @description: 移除人员
     * @param {String} id 移除人员的userid
     */
    handlerDeletePerson({ id }) {
      setGroupMember({
        ...this.queryNew,
        type: 2,
        groupOwnerId: [id]
      }).then((res) => {
        this.$message.success('移除成功')
        this.refreshGnattPerson()
      })
    },
    /**
     * @description: 刷新资源管理 甘特图
     * @param {String} type search 搜索人员
     */
    refreshGnattPerson(type) {
      this.ganttLoading = true
      this.dataList = []
      this.getGanttDataPerson()
    },
    handlerChangeGroup(val) {
      const { defaultGroup, listGroupId } = val
      this.groupData.defaultGroup = defaultGroup
      this.groupData.listGroupId = listGroupId
      this.refreshGnattPerson()
    },
    @xbb.debounceWrap(50)
    getActiveTime(val) {
      if (this.ganttLoading === true) return
      const chartCenterTime = val / 1000
      const AMOUNTHTIME = 30 * 24 * 60 * 60
      // const DAYTIME7 = 7 * 24 * 60 * 60
      const pre1 = this.currentRequestTime - AMOUNTHTIME // 请求开始区域
      const next1 = this.currentRequestTime + AMOUNTHTIME // 请求结束区域
      if (chartCenterTime <= pre1 || chartCenterTime >= next1) {
        this.dataList = xbb.deepClone(this.rootPersonList)
        this.getGanttDataNew({ time: chartCenterTime })
      }
    }
  }
}
