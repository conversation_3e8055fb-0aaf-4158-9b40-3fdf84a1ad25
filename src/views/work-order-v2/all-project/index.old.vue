<!--
 * @Description: 全部项目列表页
-->
<template>
  <div class="all-project">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :data-list="parSubFieldList"
          @getDataSummary="getTableSummary"
          @getFormData="panelFilterParams"
        >
        </list-panel>
        <list-table
          ref="table"
          :column-invisible-field="columnInvisibleField"
          :data-list="parSubFieldList"
          :loading="loading"
          :summary="summary"
          :table-data="tableData"
          :table-head="tableHead"
          :top-permissions="topPermissions"
          @editClick="editTable"
          @editForm="editFormDispatch"
          @topBtnHandle="topBtnHandle"
        ></list-table>
        <list-footer
          :archived="archived"
          :page-helper="pageHelper"
          :permission="batchEditButtons"
          @batch-handle="batchHandle"
          @getFormData="footerFilterParams"
        >
          <!-- 批量操作按钮 -->
          <template slot="permission" slot-scope="props">
            <el-button
              plain
              size="mini"
              :type="buttonType(props.type.attr)"
              @click="batchHandle(props.type)"
            >
              <div class="list-button-icon">
                <i :class="props.type.icon + ' iconfont'"></i>
                <span>{{ props.text }}</span>
              </div>
            </el-button>
          </template>
        </list-footer>
      </div>
      <template slot="layout-dialog">
        <!-- 成员选择的弹窗 -->
        <organization-select
          v-if="showOrganizationSelect"
          :active-tab-name="organizationSelectActiveName"
          :default-val.sync="selectPersonsDefault"
          :dialog-visible.sync="showOrganizationSelect"
          :multiple="tabOption"
          :range-params="rangeParams"
          :show-tabs-name="organizationSelectShowTab"
          @dialogSubmit="SubmitSelectPersons"
        >
        </organization-select>
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'
import { getWorkOrderV2List } from '@/api/work-order-v2/common'
import tempVue from '@/utils/temp-vue'

export default {
  name: 'AllProject',

  mixins: [listMixin, listEditMixin],

  data() {
    return {}
  },
  mounted() {
    tempVue.$on('refresh-list', this.getFormList)
  },
  beforeDestroy() {
    tempVue.$off('refresh-list')
  },
  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      getWorkOrderV2List(params)
        .then((data) => {
          // 处理请求数据格式
          this.disposeFormListData(data)
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.all-project {
  height: 100%;
}
</style>
