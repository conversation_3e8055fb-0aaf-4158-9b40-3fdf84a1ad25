// 侧边栏-mixin
import xbb from '@xbb/xbb-utils'
import Tao from '@musiteam/tao'
import { getFrontUrl } from '@/api/login'
import axios from 'axios'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      pluginProps: {
        uid: '',
        sessionId: '',
        dingtalkToken: ''
      },
      siderInfo: {
        uid: ''
      },
      dingtalkUrl: '',
      fileList: [],
      currentIndex: 0,
      previewVisible: false
    }
  },
  computed: {
    ...mapGetters(['isGray']),
    pluginUrl() {
      const url = `${window.location.protocol}//${window.location.hostname}:8081/#/kefuSidebar`
      const issetcorpid = () => {
        // if (this.isGray) {
        const corpid = localStorage.corpid
        return `?corpid=${corpid}`
        // } else {
        //   return ''
        // }
      }
      return xbb.isDevEnv() ? url : `${this.dingtalkUrl}index.html${issetcorpid()}#/kefuSidebar`
    }
  },
  methods: {
    // 向侧边栏传值
    msgToSidebar(msg) {
      if (!this.$refs.pluginContainer) return
      Tao.sendMessage('im-sidebar', { type: 'custom-change', data: { msg } })
      this.$nextTick(() => {
        const dom = document.querySelector('#im-sidebar')
        this.postMessage(dom, msg)
      })
    },
    // 通信方法
    postMessage(dom, msg) {
      dom.contentWindow.postMessage(msg, '*')
    },
    // 收到侧边栏并发送内容
    messageHandler(msg) {
      const {
        data: { message }
      } = msg
      const { type } = msg
      // 按类型分流处理
      switch (type) {
        case 'downLoadFile':
          this.downLoadFileHandle(message) // 下载
          break
        case 'downLoadFileYunHu':
          this.downLoadFileYunHuHandle(message) // 通话记录云呼下载
          break
        case 'previewFile':
          this.previewFileHandle(message) // 预览
          break
        case 'openWechatEnterprise':
          this.openWechatEnterpriseHandle(message) // 预览
          break
        case 'knowLedgeSend':
          this.sendMsgHandle(message) // 知识库发送
          break
      }
    },
    // 对侧边栏发送过来的message响应操作
    // 侧边栏下载操作
    downLoadFileHandle(message) {
      utils.fileDownload(message.url, message.fileName)
    },
    // 通话记录云呼下载
    downLoadFileYunHuHandle(message) {
      if (RegExp(/uincall/).test(message.url)) {
        // 优音直接下载
        window.location = message.url
      } else {
        // 音频地址
        const audioPath = message.url
        // 音频名称
        const audioName = '下载的录音文件.mp3'
        axios({
          method: 'get',
          url: audioPath,
          responseType: 'blob'
        }).then((res) => {
          // 为blob设置文件类型
          const blob = new Blob([res.data])
          // 创建一个临时的url指向blob对象
          const url = window.URL.createObjectURL(blob)
          // 创建一个a标签，用来下载
          const a = document.createElement('a')
          a.href = url
          a.download = audioName
          a.click()
          // 释放这个临时的对象url
          window.URL.revokeObjectURL(url)
        })
      }
    },

    // 侧边栏预览操作
    previewFileHandle(message) {
      this.fileList.splice(0, 1, message.file)
      this.handlePreview()
    },
    // 侧边栏企微打开好友聊天操作
    openWechatEnterpriseHandle(message) {
      utils.openEnterpriseChat(message.userObj)
    },
    // 知识库发送操作
    sendMsgHandle(message) {
      const sendLimit = {
        video: {
          size: 10485760, // 10MB
          errorText: '视频文件大小不能超过10MB'
        },
        files: {
          size: 20971520, // 20MB
          errorText: '文件大小不能超过20MB'
        },
        image: {
          size: 2097152, // 2MB
          errorText: '图片大小不能超过2MB'
        }
      }
      // 需要限制发送大小的文件类型
      if (
        ['video', 'files', 'image'].includes(message.msgtype) &&
        message[message.msgtype].size >= sendLimit[message.msgtype].size
      ) {
        return this.$message.error(sendLimit[message.msgtype].errorText)
      }
      let content
      let type
      switch (message.msgtype) {
        case 'text':
        case 'image':
          type = message.msgtype
          break
        case 'news':
          type = 'text'
          break
        default:
          type = 'files'
      }
      if (type === 'files') {
        const url = message[message.msgtype].content
        const size = message[message.msgtype].size
        const fileName = url.replace(/.+\/(.+)$/, '$1')
        const extIndex = fileName.lastIndexOf('.')
        const ext = extIndex > -1 ? fileName.substr(extIndex) : ''
        content = {
          ext, // 文件类型
          fileName, // 文件名
          size: size || 223, // 文件大小
          uid: Date.now(),
          percent: 100,
          url
        }
      } else if (type === 'text' && message.msgtype === 'news') {
        content = message[message.msgtype].link
      } else {
        content = message[message.msgtype].content
      }
      this.sendSidebarMessage(type, content)
    },
    handlePreview() {
      this.$nextTick(() => {
        this.previewVisible = true
      })
    },
    getFrontUrl() {
      getFrontUrl().then((data) => {
        const pro = process.env.NODE_ENV
        // 开发环境
        if (pro === 'development') {
          this.dingtalkUrl = data.result.dingtalkFrontUrl
        } else {
          // 线上环境pro的移动端入口前需添加pro/
          this.dingtalkUrl = data.result.dingtalkFrontUrl + 'pro/'
        }
        // window.location.href = dingtalkUrl
      })
    },
    /**
     * @description: 侧边栏发送消息
     * @param {String} [type] 消息类型
     * @param {Object、String} [content] 消息内容
     */
    sendSidebarMessage(type, content) {
      const that = this
      const message = {
        type,
        content,
        id: Date.now(),
        fromUser: this.IMUI.user,
        toContactId: this.IMUI.currentContactId,
        sendTime: Date.now(),
        status: 'going'
      }
      this.IMUI.appendMessage(message, true)
      this.IMUI._emitSend(message, function () {
        that.IMUI.updateContact({
          id: message.toContactId,
          lastContent: that.IMUI.lastContentRender(message),
          lastSendTime: message.sendTime
        })

        that.IMUI.CacheDraft.remove(message.toContactId)
      })
    }
  }
}
