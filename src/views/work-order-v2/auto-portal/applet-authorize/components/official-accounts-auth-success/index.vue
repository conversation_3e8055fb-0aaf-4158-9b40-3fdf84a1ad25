<!--
 * @Description: 扫描二维码page（非导航栏page）
-->
<template>
  <div class="official-accounts-auth-success">
    <div v-if="authResult" class="content-box">
      <i class="el-icon-success"></i>
      <div class="content-box__title">{{ returnName }}授权成功</div>
      <div class="content-box__tip">授权成功，请返回列表查看详情</div>
      <el-button size="small" @click="backOfficialList">返回列表</el-button>
    </div>
    <div v-else-if="authResult === false" class="content-box">
      <i class="el-icon-warning"></i>
      <div class="content-box__title">{{ returnName }}授权失败</div>
      <div class="content-box__tip">请检查授权微信号,返回列表重新授权</div>
      <el-button size="small" @click="backOfficialList">返回列表</el-button>
    </div>
  </div>
</template>

<script>
import { commitAuthorizationCode } from '@/api/work-order-v2/portal.js'

export default {
  name: 'OfficialAccountsAuthSuccess',

  components: {},

  filters: {},

  mixins: [],

  props: {},

  data() {
    return {
      authResult: undefined,
      accountType: ''
    }
  },

  computed: {
    returnName() {
      return this.accountType === 'applets' ? '小程序' : '公众号'
    }
  },

  watch: {},

  created() {},

  mounted() {
    this.authSuccess()
    this.accountType = this.getUrlCode('accountType')
  },

  methods: {
    // 扫码授权成功后的地址处理
    authSuccess() {
      const hrefUrl = window.location.href
      console.log('重定向链接', hrefUrl)
      if (hrefUrl.includes('?')) {
        let authCode = hrefUrl.split('?')[1].split('&')[0].split('=')[1]
        authCode = decodeURIComponent(authCode)
        console.log('获取到的授权的authCode', authCode)
        commitAuthorizationCode({ authCode })
          .then((result) => {
            console.log('authCode上传成功')
            this.authResult = true
          })
          .catch(() => {
            this.authResult = false
          })
      }
    },
    backOfficialList() {
      this.$emit('accountSuccess')
    },
    // 获取url参数
    getUrlCode(name) {
      const reg = new RegExp('(^|&?)' + name + '=([^&]*)(&|$)')
      const r = window.location.href.match(reg)
      if (r != null) {
        return unescape(r[2])
      }
      return null
    }
  }
}
</script>

<style lang="scss" scoped>
.official-accounts-auth-success {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  .content-box {
    text-align: center;
    &__title {
      padding-top: 30px;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.85);
    }
    &__tip {
      padding-top: 8px;
      padding-bottom: 24px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
    }
    .el-icon-success {
      font-size: 70px;
      color: $tag-green;
    }
    .el-icon-warning {
      font-size: 70px;
      color: $tag-red;
    }
  }
}
</style>
