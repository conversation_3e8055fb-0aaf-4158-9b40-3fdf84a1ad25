<!--
 * @Description: 小程序授权页
-->
<template>
  <div class="applet-authorize" :class="{ 'is-authorized': isSuccessAccount }">
    <div class="authorize-item">
      <div class="authorize-item__title">公众小程序</div>
      <div class="authorize-item__tips">
        公共小程序不允许设置对外显示小程序名称、logo和主体信息，如有需要请进行微信公众平台小程序授权
      </div>
      <div class="authorize-item__text">关联自助门户模版</div>
      <el-select
        v-model="publicTemplateId"
        :disabled="qrCodeLoading"
        placeholder="请选择"
        @change="changePublicAppletTemplate"
      >
        <el-option
          v-for="item in templateList"
          :key="item.formId"
          :label="item.formName"
          :value="item.formId"
        >
        </el-option>
      </el-select>
      <div class="authorize-item__text margin-top">二维码分享</div>
      <QrCode v-if="publicQrCode" v-loading="qrCodeLoading" :url="publicQrCode"></QrCode>
    </div>
    <div class="authorize-item">
      <div class="authorize-item__title">认证小程序</div>
      <div class="authorize-item__tips">
        认证小程序允许设置对外显示小程序名称、logo和主体信息。且认证小程序可以和公众号关联，实现自动化消息通知的功能。
      </div>
      <div class="authorize-item__content">
        <!-- 已授权页面 -->
        <success-accounts
          v-if="isSuccessAccount"
          :account-info="accountInfo"
          :template-list="templateList"
        ></success-accounts>
        <!-- 未授权页面 -->
        <el-button
          v-else
          class="operate-bar-button"
          icon="t-iconfont icon-add-line"
          @click="getHappendControl"
          >添加授权</el-button
        >
      </div>
    </div>

    <!-- 新增小程序Dialog -->
    <add-official-accounts
      v-if="addOfficialAccountDialog"
      account-type="applets"
      :dialog-visiable.sync="addOfficialAccountDialog"
      title="添加授权"
    >
    </add-official-accounts>
  </div>
</template>

<script>
import AddOfficialAccounts from './components/add-official-accounts'
import SuccessAccounts from './components/success-accounts'
import QrCode from './components/qr-code.vue'
import {
  getPublicAppletInfo,
  updatePublicAppletTemplate,
  getAppletInfo,
  getPortalTemplateList
} from '@/api/work-order-v2/portal.js'

export default {
  name: 'AppletAuthorize',

  components: {
    AddOfficialAccounts,
    SuccessAccounts,
    QrCode
  },
  data() {
    return {
      isSuccessAccount: false, // 是否授权
      accountInfo: {
        headImg: require('@/assets/mini-program/default-logo.png'),
        name: '',
        principalName: '',
        verifyType: -1,
        currentVersion: '',
        pushVersion: '',
        pushStatus: 0,
        pushTime: '',
        portalId: ''
      }, // 授权成功后信息
      addOfficialAccountDialog: false, // 新建公众号dialog
      templateList: [],
      publicTemplateId: '',
      publicQrCode: '',
      qrCodeLoading: false
    }
  },
  mounted() {
    getPortalTemplateList()
      .then(({ result }) => {
        this.templateList = result.formList || []
      })
      .catch((e) => {
        console.log(e)
      })
    this.getPublicAppletInfo()
    this.getAppletInfo()
  },
  methods: {
    // 新增公众号
    getHappendControl() {
      this.addOfficialAccountDialog = true
    },
    getPublicAppletInfo() {
      getPublicAppletInfo().then(({ result }) => {
        this.publicTemplateId = result.portalId || ''
        this.publicQrCode = result.qrCode
      })
    },
    changePublicAppletTemplate(portalId) {
      this.qrCodeLoading = true
      updatePublicAppletTemplate({ portalId })
        .then(({ result }) => {
          this.$message.success('操作成功')
          this.publicQrCode = result.qrCode
        })
        .catch(() => {})
        .finally(() => {
          this.qrCodeLoading = false
        })
    },
    // 查询用户是否授权成功
    getAppletInfo() {
      getAppletInfo().then((res) => {
        console.log(res)
        if (res.result && res.result.authorizerAppid) {
          this.accountInfo = Object.assign({}, this.accountInfo, res.result)
          this.isSuccessAccount = true
        } else {
          this.isSuccessAccount = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.applet-authorize {
  padding: 12px 24px 24px;
  .authorize-item {
    &__title {
      height: 44px;
      font-size: 14px;
      font-weight: 600;
      line-height: 44px;
      color: $text-plain;
      border-bottom: 1px dashed $line-cut-table;
    }
    &__tips {
      padding: 12px 16px;
      margin: 12px 0 20px 0;
      font-size: 12px;
      color: $text-auxiliary;
      background-color: $neutral-color-1;
    }
    &__text {
      height: 20px;
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 20px;
      color: $text-plain;
    }
    :deep(.el-select) {
      width: 340px;
    }
    .qr-code {
      margin-bottom: 12px;
    }
    &__content {
      .add-button {
        display: inline-block;
        padding: 8px;
        font-size: 12px;
        color: $text-auxiliary;
        cursor: pointer;
        border: 1px solid $line-cut-table;
        border-radius: 2px;
        &:hover {
          color: $brand-color-5;
        }
      }
    }
  }
  .margin-top {
    margin-top: 20px;
  }
}
.is-authorized {
  display: flex;
  flex-direction: column-reverse;
}
</style>
