<!-- 添加字段条件 -->
<template>
  <div class="add-field-input" :class="{ disabled: readOnly }" @click="editFieldHandler">
    <div v-if="!fieldList || fieldList.length === 0" class="add-field-input--empty">
      <span class="add-field-input__text">
        <i class="el-icon-plus"></i>
        {{ $t('stageProcess.setFieldCondition') }}
      </span>
    </div>
    <div v-else class="add-field-input--tag">
      <el-scrollbar style="height: 100%">
        <el-tag
          v-for="(tag, index) in fieldList"
          :key="tag.attr"
          :closable="!readOnly"
          style="margin: 5px 0 0 5px"
          @close="closeFieldTagHandler(index)"
        >
          {{ tag.attrName || tag.attr }}
        </el-tag>
      </el-scrollbar>
    </div>
    <add-field-condition-dialog
      v-if="conditionDialogShow"
      v-model="curFieldList"
      :current-task-params="currentTaskParams"
      :read-only="readOnly"
      :show.sync="conditionDialogShow"
      :type="type"
    />
  </div>
</template>

<script>
import addFieldConditionDialog from './add-field-condition-dialog'

export default {
  name: 'AddFieldInput',

  components: {
    AddFieldConditionDialog: addFieldConditionDialog
  },

  mixins: [],

  model: {
    prop: 'fieldList',
    event: 'update'
  },

  props: {
    fieldList: {
      type: Array,
      default: () => []
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number
    },
    currentTaskParams: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      conditionDialogShow: false
    }
  },

  computed: {
    curFieldList: {
      get() {
        return this.fieldList
      },
      set(val) {
        this.$emit('update', val)
      }
    }
  },

  watch: {},

  mounted() {
    console.log(this.type)
  },

  methods: {
    editFieldHandler() {
      if (this.readOnly) return
      this.conditionDialogShow = true
    },
    closeFieldTagHandler(index) {
      this.curFieldList.splice(index, 1)
      this.$emit('update', this.curFieldList)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-field-input {
  height: 80px;
  margin: 0 0 12px;
  cursor: pointer;
  border: 1px dashed $neutral-color-3;
  &--empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    & > .add-field-input__text {
      color: $text-plain;
      & > i {
        color: $brand-color-5;
      }
    }
  }
  &--tag {
    box-sizing: border-box;
    height: 100%;
    padding: 3px;
  }
}
.add-field-input.disabled {
  pointer-events: none;
  .add-field-input__text,
  .add-field-input__text i {
    color: $text-grey;
  }
}
</style>
