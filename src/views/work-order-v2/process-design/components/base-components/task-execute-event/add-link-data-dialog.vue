<template>
  <el-dialog
    :append-to-body="true"
    :before-close="addLinkDialogCloseHandler"
    class="add-link-data-dialog"
    destroy-on-close
    :model="false"
    :title="$t('stageProcess.addFieldCondition')"
    :visible.sync="dialogShow"
    width="800px"
  >
    <div v-if="noLinkForm" class="add-link-data-dialog--empty add-link-data-dialog__content">
      {{ $t('otherSetting.noData') }}
    </div>
    <div
      v-else-if="step === 1"
      class="add-link-data-dialog__content add-link-data-dialog__one"
      :class="dialogClass"
    >
      <div class="add-link-data-dialog__content-step">
        <div>
          <span class="step_number1">1</span>
          <span class="step_text">选择关联字段</span>
          <span><i class="el-icon-arrow-right"></i></span>
          <span class="step_number2">2</span>
          <span>设置字段条件</span>
        </div>
      </div>
      <div class="add-link-data-dialog__content-step__cont">
        <el-form
          ref="firstFormRef"
          label-position="left"
          label-width="110px"
          :model="firstForm"
          :rules="firstFormRules"
          size="small"
        >
          <el-form-item :label="$t('stageProcess.chooseLinkField')" prop="linkField">
            <el-select
              v-model="firstForm.linkField"
              :disabled="readOnly"
              :placeholder="$t('placeholder.choosePls', { attr: '' })"
              @change="linkFieldChangeHandler"
            >
              <el-option
                v-for="item in linkFormList"
                :key="item.attr"
                :label="item.attrName"
                :value="item.attr"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div
      v-else
      class="add-link-data-dialog__content add-link-data-dialog__two"
      :class="dialogClass"
    >
      <div class="add-link-data-dialog__content-step">
        <div>
          <span class=""></span>
          <span>选择关联字段</span>
          <span><i class="el-icon-arrow-right"></i></span>
          <span class="step_number1">2</span>
          <span class="step_text">设置字段条件</span>
        </div>
      </div>
      <add-field-condition
        v-if="!linkInfoMultiTemplate"
        v-model="fieldList"
        :all-field-list="allFieldList"
        :read-only="readOnly"
      />
      <add-field-condition-multi
        v-if="linkInfoMultiTemplate"
        v-model="fieldList"
        :all-field-list="allFieldList"
        :cur-link-form="curLinkForm"
        :link-data="curLinkInfoMulti"
        :read-only="readOnly"
        @updataMultiILinkFormList="updataMultiILinkFormList"
      />
    </div>
    <div slot="footer" class="add-field-condition-dialog__footer">
      <template v-if="step === 1">
        <el-button @click="addLinkDialogCloseHandler">{{ $t('operation.cancel') }}</el-button>
        <el-button
          v-if="!noLinkForm"
          :disabled="readOnly"
          type="primary"
          @click="nextStepHandler"
          >{{ $t('operation.nextStep') }}</el-button
        >
      </template>
      <template v-else>
        <el-button @click="lastStepHandler">{{ $t('operation.previousStep') }}</el-button>
        <el-button :disabled="readOnly" type="primary" @click="addLinkDialogSaveHandler">{{
          $t('operation.confirm')
        }}</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import { mapGetters } from 'vuex'
import dialogMixin from '@/mixin/dialog'
import { getLinkAttrExplain, getFieldCondition } from '@/api/stage-process-design'
import addFieldCondition from './add-field-condition'
import addFieldConditionMulti from './add-field-condition-linkInfoMultiTemplate.vue'

export default {
  name: 'AddLinkDataDialog',

  components: {
    AddFieldCondition: addFieldCondition,
    AddFieldConditionMulti: addFieldConditionMulti
  },

  mixins: [dialogMixin],

  model: {
    prop: 'linkData',
    event: 'update'
  },

  props: {
    linkInfoMultiTemplate: {
      type: Number
    },
    linkInfoMulti: {
      type: Object,
      default: () => ({})
    },
    linkData: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number
    },
    currentTaskParams: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      step: 1, // 第几步 只有 1 和 2
      curLinkData: {}, // 传入弹窗副本
      curLinkInfoMulti: {},
      linkFormList: [], // 关联字段列表
      firstForm: {
        // 第一个弹窗的表单
        linkField: undefined
      },
      allFieldList: [] // 所有字段列表
    }
  },

  computed: {
    ...mapGetters(['formInfo']),
    dialogClass() {
      return this.step === 2 ? 'large-height' : 'small-height'
    },
    firstFormRules() {
      return {
        linkField: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', {
              attr: this.$t('stageProcess.chooseLinkField')
            }),
            trigger: 'change'
          },
          {
            validator: this.errorSelectCheck(),
            trigger: 'change'
          }
        ]
      }
    },
    fieldList: {
      // 选中的字段列表
      get() {
        return this.curLinkData.fieldList
      },
      set(val) {
        this.$set(this.curLinkData, 'fieldList', val)
      }
    },
    curLinkForm() {
      // 当前被选中的表单信息
      return this.linkFormList.find((item) => item.attr === this.firstForm.linkField)
    },
    noLinkForm() {
      return !this.linkFormList || this.linkFormList.length <= 0
    }
  },

  watch: {},

  created() {
    this.getLinkField().then(() => {
      this.curLinkData = xbb.deepClone(this.linkData)
      this.curLinkInfoMulti = xbb.deepClone(this.linkInfoMulti) || {} // 副本
      this.firstForm.linkField = this.linkInfoMultiTemplate
        ? this.linkInfoMulti.attr
        : this.curLinkData.attr
      if (this.linkInfoMultiTemplate) return
      this.getAllFieldList(this.curLinkForm)
    })
  },

  methods: {
    errorSelectCheck() {
      return (rule, value, callback) => {
        if (
          !this.linkInfoMultiTemplate &&
          value &&
          (!this.curLinkForm || this.curLinkForm.formId !== this.curLinkData.formId)
        ) {
          return callback(new Error(this.$t('stageProcess.errorLinkFieldChoose')))
        } else {
          return callback()
        }
      }
    },
    getAllFieldList(formAttr) {
      if (!formAttr) return
      const params = {
        appId: formAttr.appId,
        menuId: formAttr.menuId,
        formId: formAttr.formId,
        saasMark: formAttr.saasMark,
        businessType: formAttr.businessType,
        type: this.type
      }
      return getFieldCondition(params).then(({ result: { explainList } }) => {
        this.allFieldList = explainList
      })
    },
    getLinkField() {
      const { appId, menuId, formId, saasMark, businessType, distributorMark } = this.$route.query

      const params = {
        appId,
        menuId,
        formId,
        saasMark,
        businessType,
        distributorMark,
        type: this.type
      }
      if (+businessType === 20800) {
        params.businessType = this.currentTaskParams.businessType
        params.formId = this.currentTaskParams.formId
      }
      return getLinkAttrExplain(params).then(({ result: { stageLinkAttrPojoList } }) => {
        this.linkFormList = stageLinkAttrPojoList.map((item) => {
          let attr = item.attr
          let attrName = item.attrName
          if (item.parentAttr) {
            attr = `${item.parentAttr}.${attr}`
            attrName = `${item.parentAttrName}.${attrName}`
          }
          // 多模版情况
          if (item.linkInfoMultiTemplate) {
            return {
              attr,
              attrName,
              linkInfoList: item.linkInfoList,
              linkInfoMultiTemplate: 1
            }
          } else {
            return {
              attr,
              attrName,
              formId: item.linkInfo.linkFormId,
              appId: item.linkInfo.linkAppId,
              menuId: item.linkInfo.linkMenuId,
              saasMark: item.linkInfo.linkSaasMark,
              businessType: item.linkInfo.linkBusinessType
            }
          }
        })
      })
    },
    addLinkDialogCloseHandler() {
      this.dialogShow = false
    },
    addLinkDialogSaveHandler() {
      if (this.linkInfoMultiTemplate) {
        // 把值传递到上一层
        this.dialogShow = false
      } else {
        const len = this.fieldList.length
        this.fieldList.forEach((item, index) => {
          item.sort = len - index
        })
        this.$emit('update', this.curLinkData)
        this.dialogShow = false
      }

      this.$emit('update', this.curLinkData)
      this.dialogShow = false
    },
    updataMultiILinkFormList(list) {
      this.$set(this.curLinkInfoMulti, 'list', list)
      this.$emit('updateCurLinkInfoMulti', this.curLinkInfoMulti)
    },
    nextStepHandler() {
      this.$refs['firstFormRef'].validate((valid) => {
        if (valid) {
          this.step = 2
        }
      })
    },
    lastStepHandler() {
      this.step = 1
    },
    linkFieldChangeHandler(val) {
      const selectFormlinkInfo = this.linkFormList.find((item) => item.attr === val)
      if (selectFormlinkInfo?.linkInfoMultiTemplate) {
        // 修改 父亲父亲组件里面的 linkInfoMultiTemplate 了
        this.$emit('ChangeMultiTemplate', 1)
        this.curLinkInfoMulti = {
          attr: val,
          list: []
        }
      } else {
        this.$emit('ChangeMultiTemplate', 0)
        this.getAllFieldList(this.curLinkForm)
        this.curLinkData = {
          attr: val,
          businessType: this.curLinkForm.businessType,
          formId: this.curLinkForm.formId,
          saasMark: this.curLinkForm.saasMark,
          fieldList: []
        }
      }
    }
  }
}
</script>

<style lang="scss">
.add-link-data-dialog .el-dialog .el-dialog__body {
  // height: 500px;
  padding: 0;
}
.step_number1 {
  padding: 3px 8px;
  color: $brand-base-color-6;
  background: $brand-color-1;
  border-radius: 61%;
}
.step_number2 {
  padding: 3px 8px;
  color: $text-plain;
  background: $neutral-color-1;
  border-radius: 61%;
}
.step_text {
  font-size: 14px;
  color: $brand-base-color-6;
}
.small-height {
  height: 260px;
}

.large-height {
  min-height: 500px;
}
.add-link-data-dialog {
  &--empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: $text-auxiliary;
  }
  &__one {
    // display: flex;
    // align-items: center;
    // justify-content: center;
  }
  &__content {
    // padding: 24px;
    &-step {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      border-bottom: 1px solid $neutral-color-3;
      &__cont {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 240px;
      }
    }
    // height: 100%;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 12px;
  .el-dialog__header {
    background: $base-white;
    border-bottom: 1px solid $neutral-color-2;
  }
  .el-dialog__body {
    padding: 0px;
    p {
      font-size: 14px;
      color: $text-main;
    }
  }
}
:deep(.el-input__inner) {
  border-radius: 2px;
}
</style>
