<!-- eslint vue/no-mutating-props: 1 -->

<template>
  <div class="trigger-condition">
    <el-form
      v-if="fieldList && fieldList.length"
      ref="triggerConditionFormRef"
      class="trigger-condition-content"
      label-width="0"
      :model="conditionForm"
    >
      <el-form-item
        v-for="(item, index) in triggerConditions"
        :key="`${item.attr}_${index}`"
        class="condition-row"
        :prop="`filter.${index}`"
        :rules="triggerConditionItemRules"
      >
        <div class="condition-row__attr">{{ item.attrName }}</div>
        <div class="condition-row__setting">
          <div class="symbol">
            <el-select
              v-model="item.symbol"
              :placeholder="$t('placeholder.choosePls', { attr: $t('formDesign.ruleType') })"
              @change="selectChange(item)"
            >
              <el-option
                v-for="logic in logicListMap[item.attr]"
                :key="logic.symbol"
                :label="logic.memo"
                :value="logic.symbol"
              />
            </el-select>
            <!-- </el-form-item> -->
          </div>
          <div class="value">
            <!-- 需求优化为新的地址级联选择器，新增多选功能，这里拦截下，走这个组件 -->
            <NewInputAddressVue
              v-if="item.fieldType === 12 && !loading"
              ref="NewInputAddressVueRef"
              v-model="item.value"
              :condition="item"
            />
            <!-- 这里老逻辑复制过来的，不敢动     fieldInfo 传值需要兼容一下 -->
            <component
              :is="componentName(item.fieldType, item)"
              v-else
              v-model="item.value"
              :class-name-rule="classNameRule"
              :condition="item"
              :field-info="getFieldInfo(item.attr)"
              :field-list="[]"
              :is-design="false"
              :is-edit="true"
              :is-see="false"
              mode="sub"
              :prop="item.attr"
              :readonly="false"
              :show-label="false"
            />
          </div>
          <el-button
            icon="el-icon-delete"
            size="mini"
            style="font-size: 16px"
            type="text"
            @click="deleteCondition(index)"
          />
        </div>
      </el-form-item>
    </el-form>
    <el-dropdown trigger="click">
      <el-button class="adding-conditions" icon="el-icon-plus" type="text">{{
        $t('formDesign.addingConditions')
      }}</el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in filterLinkWeightList"
          :key="item.attr"
          :disabled="hasOtherSubFormField(item.attr, cloneTriggerConditions)"
          @click.native="addCondition(item)"
        >
          {{ item.attrName }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import triggerConditionMixins from '@/views/edit/new-form-design/components/business-rules/component/triggler-condition-mixins'
import xbb from '@xbb/xbb-utils'
import NewInputAddressVue from './NewInputAddress.vue'

export default {
  name: 'TriggerCondition',
  mixins: [triggerConditionMixins],
  props: {
    fieldList: {
      type: Array,
      default: () => []
    },
    triggerConditions: {
      type: Array,
      default: () => []
    }
  },
  components: { NewInputAddressVue },
  data() {
    return {
      loading: false,
      cloneTriggerConditions: []
    }
  },
  computed: {
    conditionForm() {
      return {
        filter: this.triggerConditions
      }
    },
    filterLinkWeightList: {
      get() {
        return this.fieldList.filter((item) => {
          const c2 = item.editHide !== 1 // 设计阶段隐藏的字段过滤掉 （https://xbb.yuque.com/xbb/vsf9sv/kubvxk）
          const c3 = item.showType === undefined ? true : item.showType === 0
          return c2 && c3
        })
      }
    },
    logicListMap() {
      const map = {}
      this.fieldList.forEach((item) => {
        map[item.attr] = item.logicList || []
      })
      return map
    },
    triggerConditionItemRules() {
      return [
        {
          validator: (rule, value, callback) => {
            const customValue = value.value
            const isEmptySymbol = !['empty', 'noempty'].includes(value.symbol) // 排除条件是为空的情况
            const isEmptyBool = !customValue && isEmptySymbol
            const isEmptyArr =
              Array.isArray(customValue) && customValue.length === 0 && isEmptySymbol
            let isEmptyAddressVal
            // 客户地址加个校验
            if (value.attr === 'address_1' && isEmptySymbol) {
              if (!Array.isArray(value.value)) {
                isEmptyAddressVal = !value.value.province
              } else {
                isEmptyAddressVal = value.value.length && !value.value[0].province
              }
            }
            if (!value || isEmptyBool || isEmptyArr || isEmptyAddressVal) {
              callback(new Error(this.$t('stageProcess.conditionNotComplete')))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ]
    }
  },
  mounted() {
    this.cloneTriggerConditions = xbb.deepClone(this.triggerConditions)
  },
  provide() {
    return {
      saasMark: +this.$route.query.saasMark
    }
  },
  methods: {
    hasOtherSubFormField(attr = '', conditions = [], index) {
      // 如果待判断的是子表单
      if (/subForm/.test(attr)) {
        //  如果条件中有 主 对 子 则不能添加 子 and *
        const hasBaseAndSub = this.cloneTriggerConditions.find((item, _index) => {
          return (
            item.valueType === 1 &&
            item.valueAttr &&
            this.getFieldType(item.valueAttr) !== 'base' &&
            this.getFieldType(item.attr) === 'base'
          )
        })
        if (hasBaseAndSub) {
          return true
        }
        // 如果已经有了 子 对 其他 则只能选这个子
        const hasSubAndOther = this.cloneTriggerConditions.find((item, _index) => {
          return this.getFieldType(item.attr) !== 'base'
        })
        if (hasSubAndOther) {
          return this.getFieldType(attr) !== this.getFieldType(hasSubAndOther.attr)
        }
      }
      return this.triggerConditions.some((item) => item.attr === attr)
    },
    getFieldType(attr) {
      if (!attr) return NaN
      if (!/subForm_/.test(attr)) {
        return 'base'
      }
      return attr.replace(/\.\w*$/, '')
    },
    addCondition(item) {
      const { logicList, ...reset } = item
      if (this.triggerConditions.length >= 5) {
        this.$message({
          message: this.$t('message.mostSet', { attr: this.$t('nouns.filterCondition'), num: 5 }),
          type: 'warning'
        })
        return
      }
      const condition = { ...reset }
      condition['rel'] = 'and' // 并且：and 或者：or
      condition['valueType'] = 2 // 值类型，1表示动态值，2表示固定值
      condition['symbol'] = item.logicList[0]['symbol']
      condition['value'] = [12, 10003].includes(item.fieldType)
        ? [{ city: '', address: '', district: [], province: '' }]
        : undefined
      console.log(condition, 'condition')
      this.triggerConditions.push(condition)
    },
    deleteCondition(index) {
      this.triggerConditions.splice(index, 1)
    },
    // 校验
    validate() {
      if (!this.$refs.triggerConditionFormRef) return Promise.resolve()
      return new Promise((resolve, reject) => {
        this.$refs.triggerConditionFormRef.validate((res) => {
          if (res) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    selectChange(item) {
      if ([12].includes(item.fieldType)) {
        item.value = [{ city: '', district: [], province: '' }]
        this.loading = true
        this.$nextTick(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.trigger-condition-content {
  .adding-conditions {
    color: $icon-orange;
  }
  .condition-row {
    margin-bottom: 10px;
    .condition-row__attr {
      margin-bottom: 5px;
      font-size: 14px;
      line-height: 22px;
    }
    .condition-row__setting {
      display: flex;
      .symbol {
        width: 130px;
      }
      .value {
        width: 300px;
        margin: 0 10px;
        :deep(.field-date) {
          width: 100%;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.trigger-condition-content .condition-row .condition-row__setting .value .custom-checkbox__wrapper {
  width: 300px !important;
}
.el-dropdown-menu {
  max-height: 200px;
  overflow: auto;
}
</style>
