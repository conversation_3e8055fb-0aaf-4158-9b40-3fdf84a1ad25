<template>
  <div style="min-height: 100%">
    <FlowSettingLayout :tag-type="nodeConfig.type" :title="$t('workflowDesign.nodeName')">
      <el-input v-model="nodeConfig.nodeName" maxlength="30" show-word-limit />
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('workOrderV2.btnName')">
      <img-tips slot="title" :type="1" />
      <el-input v-model="nodeConfig.buttonName" maxlength="12" show-word-limit />
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('workOrderV2.linkStatus')">
      <img-tips slot="title" :type="2" />
      <div class="work-order-status">
        {{ $t('workOrderV2.workOrderStatus') }}
        <el-select
          v-model="nodeConfig.linkWorkOrderStatus"
          :placeholder="$t('formDesign.pleaseChoose')"
          style="margin-left: 10px"
        >
          <el-option
            v-for="item in workOrderStatusOptions"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('workOrderV2.linkTemplate')">
      <div v-if="![1, 2].includes(versionType)" class="link-visit-form work-order-status">
        {{ $t('workOrderV2.linkCommentTemplate') }}
        <el-select
          v-model="nodeConfig.linkVisitFormId"
          :placeholder="$t('formDesign.pleaseChoose')"
          style="margin-left: 10px"
        >
          <el-option
            v-for="item in commentTemplateList"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="work-order-status">
        {{ $t('workOrderV2.linkCommentSms') }}
        <el-select
          v-model="nodeConfig.smsFormId"
          :disabled="!smsEnable"
          :placeholder="$t('formDesign.pleaseChoose')"
          style="margin-left: 10px"
        >
          <el-option
            v-for="item in smsTemplateList"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('workOrderV2.customerBack')">
      <el-checkbox
        v-model="nodeConfig.enableSendSms"
        class="base-checkout"
        :false-label="0"
        :true-label="1"
      >
        短信通知
        <el-tooltip :content="$t('workOrderV2.sendMessageHint')" effect="dark" placement="top">
          <i class="icon-question-line t-iconfont tooltip-icon"></i>
        </el-tooltip>
      </el-checkbox>
      <div v-if="nodeConfig.enableSendSms" class="base-checkout__extra">
        {{ $t('workOrderV2.enterNode') }}
        <el-input-number
          v-model="nodeConfig.sendAfterTime"
          :controls="false"
          :max="9999"
          style="width: 64px; margin: 0 4px"
        />
        {{ $t('workOrderV2.timeSendMessage') }}
      </div>
      <el-checkbox
        v-model="nodeConfig.enableSendWxms"
        class="base-checkout margin-top-12"
        :false-label="0"
        :true-label="1"
        @change="handleChange"
      >
        公众号通知
        <el-tooltip
          :content="$t('workOrderV2.sendWechatNotificationMessageHint')"
          effect="dark"
          placement="top"
        >
          <i class="icon-question-line t-iconfont tooltip-icon"></i>
        </el-tooltip>
      </el-checkbox>
      <div v-if="nodeConfig.enableSendWxms" class="base-checkout__extra">
        {{ $t('workOrderV2.enterNode') }}
        <el-input-number
          v-model="nodeConfig.sendWxmsAfterTime"
          :controls="false"
          :max="9999"
          style="width: 64px; margin: 0 4px"
        />
        {{ $t('workOrderV2.timeSendWechat') }}
      </div>
      <el-checkbox
        v-model="nodeConfig.enableSendEmail"
        class="base-checkout margin-top-12"
        :false-label="0"
        :true-label="1"
      >
        邮件通知
        <el-tooltip content="使用邮件功能需要开通邮件应用" effect="dark" placement="top">
          <i class="icon-question-line t-iconfont tooltip-icon"></i>
        </el-tooltip>
      </el-checkbox>
      <div v-if="nodeConfig.enableSendEmail" class="base-checkout__extra">
        {{ $t('workOrderV2.enterNode') }}
        <el-input-number
          v-model="nodeConfig.sendEmailAfterTime"
          :controls="false"
          :max="9999"
          :min="0"
          style="width: 64px; margin: 0 4px"
        />
        分钟自动发送
        <el-button class="margin-top-12" icon="el-icon-setting" @click="mailSetShow = true"
          >设置邮件模板</el-button
        >
      </div>
      <el-checkbox
        v-model="nodeConfig.autoWorkOrder"
        class="base-checkout"
        :false-label="0"
        style="margin-top: 10px"
        :true-label="1"
        >{{ $t('workOrderV2.allowFillIn') }}</el-checkbox
      >
      <el-checkbox
        v-model="nodeConfig.fillAutoPass"
        class="base-checkout margin-top-12"
        :false-label="0"
        :true-label="1"
        >{{ $t('workOrderV2.autoPass') }}</el-checkbox
      >
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('flow.backSetting')">
      <div class="back-setting">
        <div class="back-setting__switch">
          <div>
            允许跨节点回退
            <el-tooltip
              class="item"
              content="开启后，可以在指定的节点之间实现回退。"
              effect="dark"
              placement="top"
            >
              <i class="icon-question-line t-iconfont"></i>
            </el-tooltip>
          </div>
          <el-switch
            v-model="backConfig.crossNodeRollbackFlag"
            :active-value="1"
            :inactive-value="0"
          />
        </div>
        <BackRangeSelect
          v-if="backConfig.crossNodeRollbackFlag"
          :back-config.sync="backConfig"
        ></BackRangeSelect>
      </div>
    </FlowSettingLayout>
    <MailTemplateSet
      v-if="mailSetShow"
      :current-mail-set="nodeConfig.visitNodeEmailPojo"
      :flow-params="flowParams"
      :system-mail-list="systemMailList"
      @close="mailSetShow = false"
      @confirm="saveMailSet"
    />
  </div>
</template>

<script>
import FlowSettingLayout from '../base-components/flow-setting-layout'
import BackRangeSelect from '../base-components/back-range-select.vue'
import settingMixin from './setting-mixin'
import {
  getEnableCommentTemplateList,
  getSmsTemplateList
} from '@/api/work-order-v2/comment-setting.js'
import MailTemplateSet from './components/mail-template-set.vue'
import { getMailAccountList } from '@/api/mail'
import { mapActions } from 'vuex'
import ImgTips from '@/views/work-order-v2/process-design/components/setting-block/components/img-tips.vue'

export default {
  name: 'VisitSettingBlock',
  components: {
    FlowSettingLayout,
    BackRangeSelect,
    MailTemplateSet,
    ImgTips
  },
  mixins: [settingMixin],
  props: {
    itemNode: {
      type: Object,
      required: true,
      default: () => ({})
    },
    flowParams: {
      // 后端返回的核心配置对象
      type: Object,
      default: () => ({})
    },
    explainList: {
      type: Array,
      default: () => []
    },
    workOrderStatusOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      commentTemplateList: [],
      smsTemplateList: [],
      smsEnable: false,
      mailSetShow: false
    }
  },
  computed: {
    // 当前工单套餐类型 工单版本: 1.新用户标准版 2.历史用户标准版 3.高级版 4.旗舰版
    versionType() {
      return Number(this.$store.getters.versionType)
    },
    backConfig: {
      get() {
        return this.nodeConfig.backConfig
      },
      set(newVal) {
        this.nodeConfig.backConfig = newVal
      }
    }
  },
  mounted() {
    const { saasMark } = this.flowParams
    getEnableCommentTemplateList({
      businessType: 20360,
      saasMark
    })
      .then(({ result }) => {
        this.commentTemplateList = result.map((item) => {
          return {
            text: item.formName,
            value: item.formId
          }
        })
      })
      .catch(() => {})
    getSmsTemplateList({
      tabType: 1
    })
      .then(({ result }) => {
        this.smsEnable = result.isEnable
        this.smsTemplateList = result.tablist.map((item) => {
          return {
            text: item.name,
            value: item.templateId
          }
        })
      })
      .catch(() => {})
    const params = {
      isSystem: 1,
      isPersonal: 0,
      pageNumber: 1,
      pageSize: 9999
    }
    getMailAccountList(params)
      .then((res) => {
        this.systemMailList = res.result.list
      })
      .catch(() => {})
  },
  methods: {
    ...mapActions(['setFeeDialigConfig']),
    checkFlowParams() {
      return this.checkCommon(this.nodeConfig)
    },
    saveMailSet(config) {
      this.nodeConfig.visitNodeEmailPojo = config
      this.mailSetShow = false
    },
    handleChange(val) {
      // 工单标准版不支持该功能
      if ([1, 2].includes(this.versionType)) {
        this.setFeeDialigConfig({
          feeDialogVisible: true,
          feeDialogTextTip: '当前的套餐版本暂不支持此功能，可联系客服咨询升级'
        })
        this.nodeConfig.enableSendWxms = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.work-order-status {
  font-size: 14px;
  color: $text-plain;
}
.link-visit-form {
  margin-bottom: 10px;
}
.base-checkout {
  display: block;
  margin-top: 2px;
  &-tooltip {
    color: $text-grey;
    &:hover {
      color: $brand-base-color-6;
    }
  }
  &__extra {
    padding-left: 26px;
    margin-top: 12px;
    font-size: 14px;
    color: $text-plain;
  }
  :deep(.el-checkbox__label) {
    color: $text-plain;
  }
}
.back-setting {
  .back-setting__switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
    .icon-question-line {
      margin-left: 4px;
      color: $text-grey;
      &:hover {
        color: $brand-base-color-6;
      }
    }
  }
}
.margin-top-12 {
  margin-top: 12px;
}
</style>
