<template>
  <div class="work-order-design">
    <template>
      <div class="flow-chart-panel">
        <div class="top-tool">
          <!--  版本控制  -->
          <flow-version
            v-if="workFlowInfo"
            v-model="currentFlowId"
            :add-loading.sync="addLoading"
            :flow-list="flowList"
            :work-flow-info="workFlowInfo"
            @addFlowVersion="addFlowVersion"
            @change="changeVersion"
            @delFlowVersion="delFlowVersion"
            @flowVersionInit="flowVersionInit"
          />
          <!-- 工作流启停控制  -->
          <div v-if="Object.keys(workFlowInfo).length !== 0" class="top-tool__opration">
            <el-button v-if="workFlowInfo.enable" @click="enableClick(workFlowInfo.enable)">
              {{ $t('workflowDesign.pauseProcess') }}
            </el-button>
            <el-button
              v-else
              :disabled="enableLoading"
              :loading="enableLoading"
              type="primary"
              @click="enableClick(workFlowInfo.enable)"
            >
              {{ $t('workflowDesign.enablingProcess') }}
            </el-button>
          </div>
        </div>
        <!-- 节点树 -->
        <FlowDesign
          v-if="Object.keys(processTree).length"
          ref="flowDesign"
          v-loading="processDesignLoading"
          :prop-node-data="processTree"
          :select-item="itemNode"
          style="height: calc(100% - 45px)"
          :work-flow-info="workFlowInfo"
          @nodeClick="nodeClick"
          @nodeDelete="nodeDelete"
          @showSelectAction="showSelectAction"
          @treeFinally="treeFinally"
          @treeUpdate="treeUpdate"
          @updateNodeName="updateNodeName"
        />
      </div>
      <!--   右侧节点内容配置   -->
      <RightSetting
        v-model="activeName"
        :explain-list="filterExplainList"
        :flow-params="flowParams"
        :is-show-setting="isShowSetting"
        :item-node="itemNode"
        :process-tree="processTree"
        :rela-service-templates="relaServiceTemplates"
        :work-flow-info="workFlowInfo"
        :work-order-status-options="workOrderStatusOptions"
        @addUserClick="addUserClick"
        @changeShowStatus="changeShowStatus"
        @treeUpdate="treeUpdate"
      />
    </template>
    <!-- 右边划出选择框 -->
    <select-action
      ref="action"
      :process-tree="processTree"
      :work-flow-info="workFlowInfo"
      @treeFinally="treeFinally"
      @treeUpdate="treeUpdate"
    />
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import {
  getWorkOrderProcess,
  getFlowList,
  enableWorkOrderProcess,
  getProcessNodeSet,
  getProcessExplain,
  getWorkOrderStatus,
  deleteProcessVersion,
  addProcessVersion,
  getRelaServiceTemplates
} from '@/api/work-order-v2'
import PersonSelect from '@/components/person-select/index'
import RightSetting from './components/right-setting'
import FlowDesign from './flow-design'
import FlowVersion from './components/flow-version'
import SelectAction from './flow-design/components/select-action'
import { mapMutations, mapGetters } from 'vuex'
import { coreConfigurationMap, TYPES } from './process-design-config'

export default {
  name: 'WorkOrderProcess',
  components: {
    RightSetting,
    FlowDesign,
    FlowVersion,
    SelectAction
  },
  data() {
    return {
      addLoading: true, // 添加按钮loading状态
      flowList: [], // 流程列表
      currentFlowId: 0, // 当前流程id
      limitNum: 0, // 0代表不限制 大于0以上代表限制的个数
      explainList: [],
      processTree: {}, // 工作节点树
      needApproveFields: [], // 需审批字段
      itemSelectUserType: '',
      itemSelectUserDefaultVal: [],
      personDialogShow: false,
      activeName: 'node',
      attrType: this.$t('workflowDesign.basicAttribute'),
      itemNode: null, // 选中的当前节点树的配置
      flowParams: null, // 节点详情(后端返回)
      cloneInfo: null,
      workFlowInfo: {},
      selectNodeName: '', // 当前节点名称
      enableLoading: false,
      isShowSetting: false,
      workOrderStatusOptions: [],
      relaServiceTemplates: [],
      tabMultiple: false
    }
  },
  computed: {
    ...mapGetters(['processDesignLoading']),
    personSelectShowTabsName() {
      return ['user', 'dynamicManager']
    },
    filterExplainList() {
      const filterFields = [10002]
      return this.explainList.filter((item) => {
        return !filterFields.includes(item.fieldType)
      })
    }
  },
  mounted() {
    this.flowVersionInit()
  },
  beforeDestroy() {
    this.processTree = null
    this.flowParams = null
    this.itemNode = null
  },
  methods: {
    ...mapMutations({
      setProcessName: 'SET_PROCESS_NAME',
      setForkProcessName: 'SET_FORK_PROCESS_NAME',
      setFlowDesignLoad: 'SET_PROCESS_DESIGN_LOAD'
    }),
    nodeClick(data) {
      // 先获取节点的详情
      if (data === this.itemNode || data.type === TYPES.START_NODE) return
      // 选择节点前要先判断当前设置节点是不是发生的更改 发生更改则给出提示
      this.needChangeSave().catch(() => {
        // 更改选择的节点
        this.changSelectNode(data)
      })
    },
    nodeDelete() {
      this.flowParams = null
      this.itemNode = null
      this.cloneInfo = null
    },
    changSelectNode(data) {
      this.activeName = 'node'
      this.flowParams = null
      const { processNodeId, processId, type } = data
      this.attrType = this.$t('workflowDesign.basicAttribute')

      this.getWorkFlowNodeSet(processNodeId, processId, type)
      this.itemNode = data
      this.$nextTick(() => {
        this.isShowSetting = true
      })
    },
    treeUpdate(value) {
      this.processTree = value
      this.changeShowStatus()
    },
    treeFinally() {
      this.setFlowDesignLoad(false)
    },
    // 展开select-action
    showSelectAction(nodeData) {
      this.needChangeSave().catch(() => {
        // 不保存才允许打开新建侧边栏
        this.$refs.action.drawer = true
        this.$refs.action.nodeData = nodeData
      })
    },
    // 判断节点是否发生更改
    needChangeSave() {
      return new Promise((resolve, reject) => {
        // cloneInfo 为最开始的节点设置 flowParams 为现在的节点设置 不相等 则发生了更改
        if (this.cloneInfo && !xbb._isEqual(this.cloneInfo, this.flowParams)) {
          this.$confirm(
            this.$t('workflow.isSaveNodeSettingBefore'),
            this.$t('workflow.isSaveNodeSetting'),
            {
              confirmButtonText: this.$t('workflow.yesSave'),
              cancelButtonText: this.$t('workflow.noGiveUp'),
              type: 'warning'
            }
          )
            .then(() => {
              this.scrollSpecifiedNode(this.flowParams.nodeId)
              resolve()
            })
            .catch(() => {
              reject()
            })
        } else {
          reject()
        }
      })
    },
    updateNodeName(value) {
      if (value.nodeId === this.flowParams.nodeId) {
        this.flowParams.name = value.name
      }
    },
    getWorkFlowNodeSet(processNodeId, processId, type) {
      getProcessNodeSet({
        processNodeId,
        processId,
        type
      }).then(({ result }) => {
        // 核心配置是在新建节点的时候塞进去
        result.config.assignedNodeConfigData?.assignedRule.forEach((item) => {
          item.users.forEach((user) => {
            if (user.property === 'dept') {
              user.id = Number(user.id)
            }
          })
        })
        this.flowParams = result
        setTimeout(() => {
          this.cloneInfo = xbb.deepClone(this.flowParams)
        })
      })
    },
    personDialogSubmit(val) {
      const configKey = Object.keys(coreConfigurationMap[this.itemNode.type])[0]
      this.flowParams.config[configKey]['mainUserList'] = val
    },

    addUserClick() {
      const configKey = Object.keys(coreConfigurationMap[this.itemNode.type])[0]
      this.itemSelectUserDefaultVal = this.flowParams.config[configKey]['mainUserList']
      // this.personDialogShow = true
      PersonSelect({
        activeTabName: 'user',
        showTabsName: this.personSelectShowTabsName,
        defaultVal: this.itemSelectUserDefaultVal,
        limitNum: this.limitNum,
        tabMultiple: this.tabMultiple,
        multiple: {
          user: false,
          dynamicManager: true
        },
        otherParams: {
          formQuery: {
            formId: Number(this.flowParams.formId),
            businessType: Number(this.flowParams.businessType)
          },
          deptVisibleRule: true,
          isChecked: false,
          isWorkOrder: true
        }
      }).then((res) => {
        if (res.data) {
          this.personDialogSubmit(res.data)
        }
      })
    },
    // 工作流是否启用
    enableClick(enable) {
      enable = enable === 0 ? 1 : 0
      enableWorkOrderProcess({
        enable,
        formId: this.$route.query.formId,
        processId: this.currentFlowId
      })
        .then((res) => {
          this.$message.success(res.msg)
          this.workFlowInfo.enable = enable
          this.flowVersionInit()
          if (enable === 1) {
            utils.SS.set('processId', this.currentFlowId)
          }
        })
        .finally(() => {
          this.itemNode = null
          this.cloneInfo = null
          this.isShowSetting = false
        })
    },
    scrollSpecifiedNode(nodeId) {
      // 获取到scroll的DOM节点
      let flowDesignScrollbar =
        this.$refs['flowDesign'] &&
        this.$refs['flowDesign'].$refs['flowDesignScrollbar'] &&
        this.$refs['flowDesign'].$refs['flowDesignScrollbar'].wrap
      // 先把scrollTop设置为0，确保getBoundingClientRect拿到的值准确
      flowDesignScrollbar.scrollTop = 0
      flowDesignScrollbar.scrollLeft = 0
      // 如果是首节点直接返回
      if (nodeId === 'startNode') return
      // Scrollbar与window顶点的距离
      const DESIGN_SCROLLBAR_DISTANCE = 200
      // 查询后端返回workFlowNodeId的DOM节点，再拿到节点的top,left
      let el = document.getElementById(nodeId)
      const { top, left } = el.getBoundingClientRect()
      // 滑动到具体位置
      flowDesignScrollbar.scrollTop = top - DESIGN_SCROLLBAR_DISTANCE
      flowDesignScrollbar.scrollLeft = left - 500
      el = null
      flowDesignScrollbar = null
    },
    // 删除流程版本
    delFlowVersion(id) {
      this.$confirm(this.$t('workflowDesign.deleteHint'), this.$t('message.confirmTitle'), {
        closeOnClickModal: false,
        closeOnPressEscape: false,
        distinguishCancelAndClose: true,
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(() => {
          deleteProcessVersion({
            formId: this.$route.query.formId,
            processId: id
          }).then(() => {
            this.$message.success(this.$t('message.operateSuccessSymbol'))
            this.flowVersionInit()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: this.$t('workflowDesign.cancelled')
          })
        })
    },
    // 添加流程版本
    addFlowVersion() {
      addProcessVersion({
        processId: this.workFlowInfo.workflowId,
        formId: this.$route.query.formId
      })
        .then(() => {
          this.flowVersionInit()
          this.addLoading = true
        })
        .finally(() => {
          this.addLoading = true
        })
    },
    // 获取流程列表
    flowVersionInit() {
      getFlowList({
        formId: this.$route.query.formId,
        archive: 2, // 1 归档 2 未归档
        page: 1, // 页数
        pageSize: 20 // 一页数量
      }).then((res) => {
        this.flowList = res.result.list
        const versionItem = this.currentFlowId === 0 ? this.flowList[0] : { id: this.currentFlowId }
        this.getWorkFlowSet(versionItem)
      })
    },
    // 获取工作流设置
    getWorkFlowSet(processItem) {
      return new Promise((resolve, reject) => {
        getWorkOrderProcess({
          processId: processItem.id
        })
          .then(({ result }) => {
            this.setFlowDesignLoad(false)
            this.processTree = result.workOrderNodeTree
            const { saasMark, businessType, distributorMark } = this.$route.query
            this.workFlowInfo = {
              ...result.workflow,
              saasMark,
              businessType,
              distributorMark
            }
            // 加一下，保证等会流程能通
            this.workFlowInfo.workflowId = result.workflow.id
            // 首节点加要用
            this.workFlowInfo.processNodeId = result.workOrderNodeTree.processNodeId
            // 缓存一下，保证刷新时正常
            utils.SS.set('processId', result.workflow.id)
            utils.SS.set('processName', result.workflow.name)
            this.setProcessName(result.workflow.name)
            this.setForkProcessName(result.workflow.name)
            this.currentFlowId = result.workflow.id

            this.getProcessTemplateExplain()
            this.getWorkOrderStatus()
            this.getRelaServiceTemplates()
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    getWorkOrderStatus() {
      getWorkOrderStatus({
        formId: this.$route.query.formId
      }).then((res) => {
        this.workOrderStatusOptions = res.result
      })
    },
    getRelaServiceTemplates() {
      getRelaServiceTemplates({ formId: this.$route.query.formId })
        .then((res) => {
          this.relaServiceTemplates = res.result
        })
        .catch(() => {})
    },
    getProcessTemplateExplain() {
      getProcessExplain({
        formId: this.$route.query.formId
      }).then((res) => {
        this.explainList = res.result
      })
    },
    changeVersion(processItemId) {
      if (processItemId === this.workFlowInfo.id) return
      this.getWorkFlowSet({ id: processItemId }).then(() => {
        this.changeShowStatus()
      })
    },
    // 节点保存
    saveNode() {
      this.itemNode = null
      this.cloneInfo = null
    },
    changeShowStatus() {
      this.isShowSetting = false
      this.itemNode = null
      this.cloneInfo = null
    },
    cloneFlowInfo(value) {
      this.cloneInfo = xbb.deepClone(value)
    }
  }
}
</script>

<style lang="scss">
.work-order-design {
  position: relative;
  display: flex;
  flex-flow: row;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: $neutral-color-1;
  .flow-chart-panel {
    flex: 1;
    width: calc(100% - 420px);
    height: 100%;
    .top-tool {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 50px;
      background: $neutral-color-1;
      .top-tool__opration {
        display: flex;
        flex: 1;
        align-content: center;
        justify-content: flex-end;
        & > .el-button {
          margin-right: 20px;
          margin-left: 0;
        }
      }
    }
  }
  .right-panel {
    box-sizing: border-box;
    flex-shrink: 0;
    width: 420px;
    height: 100%;
    background: white;
    box-shadow: -1px 0px 10px 0px rgba(0, 0, 0, 0.05);
    transition: all 0.5s;
    .tips-world {
      margin-top: 10px;
      font-size: 12px;
      line-height: 20px;
      color: $text-auxiliary;
    }
    .el-tabs {
      height: 100%;
      .el-tabs__content {
        .el-scrollbar__wrap {
          overflow-x: hidden;
        }
        height: calc(100% - 43px);
        .el-tab-pane {
          height: 100%;
        }
      }
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
    }
    .el-tabs__item {
      height: 50px;
      font-size: 14px;
      line-height: 44px;
      text-align: center;
    }
  }
}
</style>
