<!--
 * @Description: 条件节点的包裹组件
 -->
<template>
  <div class="new-branch-wrap">
    <div class="branch-box-wrap">
      <div class="branch-box">
        <el-button
          v-show="
            ![3, 4].includes(nodeData.conditionNodes[0].conditionType) &&
            workFlowInfoFake.enable === 0
          "
          key="btn"
          class="add-branch list-complete-item"
          @click="addCondition"
        >
          {{ $t('authoritySettting.addingConditions') }}
        </el-button>
        <template v-for="(cond, index) in nodeData.conditionNodes">
          <div :key="cond.nodeId" class="col-box list-complete-item">
            <ConditionNode
              :key="`condition-node-${cond.nodeId}`"
              :index="index"
              :node-data="cond"
              :parent-node="nodeData"
              @afterSort="afterSort"
              @beforeSort="beforeSort"
            />
            <div class="conditionLine"></div>
          </div>
        </template>
      </div>

      <!-- 新建节点 -->
      <div class="add-node-btn-box">
        <div v-if="!hasCondition" class="work-order-flow__arrow"></div>
        <div class="add-node-btn">
          <AddNodeButton :is-wrap-btn="true" :node-data="nodeData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AddNodeButton from './add-node-button'
import ConditionNode from './condition-node'
import { TYPES } from '../../process-design-config'
import { guid } from '@xbb/xbb-utils'
import { mapMutations } from 'vuex'
import { saveProcessNode } from '@/api/work-order-v2'

export default {
  name: 'BranchWrap',
  components: {
    ConditionNode,
    AddNodeButton
  },
  inject: {
    getWorkFlowInfo: {},
    emitCommand: {}
  },

  props: {
    isInCondition: {
      type: Boolean,
      default: false
    },
    nodeData: {
      /**
       * 节点数据对象
       */
      type: Object,
      default: () => ({}),
      required: true
    }
  },

  data() {
    return {
      showBorder: true
    }
  },

  computed: {
    workFlowInfoFake() {
      return this.getWorkFlowInfo()
    },
    hasCondition() {
      return this.isInCondition
    }
  },

  methods: {
    ...mapMutations({
      setFlowDesignLoad: 'SET_PROCESS_DESIGN_LOAD'
    }),
    // 添加一个普通条件分支
    addCondition() {
      this.beforeSort()
      const newNodeId = guid()
      const prevId = this.nodeData.nodeId
      const priority = this.nodeData.conditionNodes.length + 1
      const { id, triggerType, appId, formId, menuId } = this.workFlowInfoFake
      const postData = {
        processId: id,
        type: TYPES.CONDITION_NODE,
        processNode: {
          type: TYPES.CONDITION_NODE,
          name: `条件${priority}`,
          appId,
          formId,
          menuId,
          saasMark: 1,
          businessType: 20300,
          triggerType,
          nodeId: newNodeId,
          prevId,
          priority,
          wrapperId: prevId,
          config: {
            conditionNodeConfigData: {
              nodeName: `条件${priority}`,
              conditionType: 1,
              condition: {},
              type: 1,
              editedData: 1
            }
          }
        }
      }
      this.setFlowDesignLoad(true)
      saveProcessNode(postData)
        .then((res) => {
          const { workOrderNodeTree } = res.result
          this.emitCommand('treeUpdate', workOrderNodeTree)
        })
        .finally(() => {
          this.emitCommand('treeFinally')
          setTimeout(() => {
            this.afterSort()
          }, 800)
        })
    },
    beforeSort() {
      this.showBorder = false
    },
    afterSort() {
      this.showBorder = true
    }
  }
}
</script>

<style lang="scss">
/* 过度动画 */
.list-complete-enter, .list-complete-leave-to
/* .list-complete-leave-active for below version 2.1.8 */ {
  opacity: 0;
  transform: translateY(-60px);
}
.list-complete-leave-active {
  position: absolute;
}
.list-complete-move {
  position: absolute;
}
/* 布局 */
.new-branch-wrap {
  display: inline-flex;
  width: 100%;
  & > .branch-box-wrap {
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    flex-shrink: 0;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    min-height: 270px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -ms-flex-negative: 0;
    & > .branch-box {
      position: relative;
      display: flex;
      height: auto;
      min-height: 180px;
      overflow: visible;
      transition: all 1s;
      & > div:last-child {
        border: none;
        & > .conditionLine {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          width: 50%;
          border-top: 2px solid $neutral-color-3;
          border-bottom: 2px solid $neutral-color-3;
        }
      }
      & > div:nth-child(2) {
        border: none;
        & > .conditionLine {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          width: 50%;
          border-top: 2px solid $neutral-color-3;
          border-bottom: 2px solid $neutral-color-3;
        }
      }
      // margin-top: 15px;
      & > .add-branch {
        position: absolute;
        top: -16px;
        left: 50%;
        z-index: 10;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        padding: 0 10px;
        font-size: 12px;
        line-height: 30px;
        color: $text-auxiliary;
        cursor: pointer;
        user-select: none;
        background: $base-white;
        border: none;
        border-radius: 15px;
        outline: none;
        box-shadow: 0px 2px 15px 2px rgba(0, 0, 0, 0.05);
        -webkit-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        transform: translateX(-50%);
        transform-origin: center center;
      }
      .add-branch:hover {
        color: $brand-color-5;
      }
      & > .col-box {
        position: relative;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        background: $neutral-color-1;
        border-top: 2px solid $neutral-color-3;
        border-bottom: 2px solid $neutral-color-3;
        transition: all 0.4s;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-box-align: center;
        &::before {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          z-index: 0;
          width: 2px;
          height: 100%;
          margin: auto;
          content: '';
          background-color: $neutral-color-3;
        }
      }
    }

    & > .add-node-btn-box {
      position: relative;
      display: inline-flex;
      flex-shrink: 0;
      width: 240px;
      -ms-flex-negative: 0;
      -webkit-box-flex: 1;
      -ms-flex-positive: 1;
      &::before {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: -1;
        width: 2px;
        height: 100%;
        margin: auto;
        content: '';
        background-color: $neutral-color-3;
      }
      & > .add-node-btn {
        display: flex;
        flex-grow: 1;
        flex-shrink: 0;
        justify-content: center;
        width: 240px;
        user-select: none;
        -webkit-box-pack: center;
        -webkit-box-flex: 1;
      }
      .work-order-flow__arrow {
        position: absolute;
        bottom: 0;
        left: 114px;
        content: '';
        background-color: $neutral-color-1;
        border-top: 10px solid $neutral-color-3;
        border-right: 6px solid transparent;
        border-left: 6px solid transparent;
      }
    }
  }
}
</style>
