<template>
  <div class="work-order-pool">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :data-list="parSubFieldList"
          :is-work-order-map-view="isWorkOrderMapView"
          :is-work-order-multiple-view="true"
          :show-filter-list="specialFilter"
          @getDataSummary="getTableSummary"
          @getFormData="panelFilterParams"
          @openMapView="openMapView"
          @openTableView="openTableView"
        >
        </list-panel>
        <workorder-map
          v-if="isWorkOrderMapView && formListParams.businessType"
          id="map-view"
          :form-list-params="formListParams"
          :table-data="tableData"
          @get-card-list="getWorkOrderCardList"
        ></workorder-map>
        <list-table
          v-if="!isWorkOrderMapView"
          ref="table"
          :column-invisible-field="columnInvisibleField"
          :data-list="parSubFieldList"
          :loading="loading"
          :summary="summary"
          :table-data="tableData"
          :table-head="tableHead"
          @editClick="editTable"
          @editForm="editFormDispatch"
        ></list-table>
        <list-footer
          v-if="!isWorkOrderMapView"
          :archived="archived"
          :page-helper="pageHelper"
          :permission="batchEditButtons"
          @batch-handle="batchHandle"
          @getFormData="footerFilterParams"
        >
          <!-- 批量操作按钮 -->
          <template slot="permission" slot-scope="props">
            <el-button
              plain
              size="mini"
              :type="buttonType(props.type.attr)"
              @click="batchHandle(props.type)"
            >
              <div class="list-button-icon">
                <i :class="props.type.icon + ' iconfont'"></i>
                <span>{{ props.text }}</span>
              </div>
            </el-button>
          </template>
        </list-footer>
      </div>
      <template slot="layout-dialog">
        <WorkOrderV2AssginDialog
          v-if="showBatchAssgindialog"
          active-tab-name="user"
          :dialog-visible.sync="showBatchAssgindialog"
          :multiple="{ user: false }"
          :show-tabs-name="['user']"
          :tab-multiple="true"
          @dialog-submit="submitWorkOrderV2BatchAssgin"
        />
        <work-order-v2-batch-handle-dialog
          v-if="showBatchHandledialog"
          :dialog-info="batchDialogInfo"
          :params="batchHandleBaseParams"
          @close="showBatchHandledialog = false"
          @submit-form="submitWorkOrderV2BatchHandle"
        />
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'
import workOrderV2BatchMixin from '@/views/work-order-v2/mixin/batch-handle-mixin.js'
import viewLoadingMixin from '@/views/work-order-v2/mixin/view-loading-mixin'
import { getWorkOrderV2List } from '@/api/work-order-v2/common.js'
import WorkorderMap from '@/views/work-order-v2/components/workorder-map'

export default {
  name: 'WorkOrderPool',
  components: {
    WorkorderMap
  },

  mixins: [listMixin, listEditMixin, workOrderV2BatchMixin, viewLoadingMixin],

  data() {
    return {
      isWorkOrderMapView: false
    }
  },
  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.isWorkOrderMapView && this.viewLoding('map')
      params.poolId = this.getAppInfo.poolId
      getWorkOrderV2List(params)
        .then((data) => {
          // 处理请求数据格式
          this.disposeFormListData(data)
        })
        .finally(() => {
          this.isWorkOrderMapView && this.closeViewLoding('map')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.work-order-pool {
  height: 100%;
}
</style>
