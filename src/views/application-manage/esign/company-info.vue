<!--
 * @Description: 电子合同-公司信息
-->
<template>
  <div class="company-info">
    <div class="children-head">
      <h1 class="title">
        <span slot="title">{{ $t('eContract.eContract') }}-{{ $t('eContract.companyInfo') }}</span>
      </h1>
    </div>
    <div class="container">
      <p>{{ companyName }}</p>
      <div v-if="!isRealName" class="no-auth">
        <i class="el-icon-error"></i>
        <span>{{ $t('eContract.noAuth') }}</span>
        <span class="go-auth" @click="goAuth">{{ $t('eContract.goAuth') }}</span>
      </div>
      <div v-else class="auth-success">
        <i class="el-icon-success"></i>
        <span>{{ $t('eContract.authSuccess') }}</span>
        <el-button type="text" @click="unauthenticate">解除认证</el-button>
      </div>
    </div>
    <el-dialog
      :append-to-body="true"
      custom-class="cert-dialog"
      title="企业认证"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <div>
        <div class="input-suffix">
          <div class="input-suffix-name">机构名称：</div>
          <el-input v-model="entryCorpName" placeholder="请输入e签宝内认证的机构名称"> </el-input>
        </div>
        <p class="cert-tips">
          若e签宝尚未完成实名认证，请参照教程先完成认证&nbsp;&nbsp;<a
            href="javascript:;"
            @click="viewTutorial"
            >查看教程</a
          >
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="checkEsignCommonEnterpriseCert">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getEsignCorpInfo, getEsignCorpRealname, esignUnauthenticate } from '@/api/esign'
import * as dd from 'dingtalk-jsapi'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'CompanyInfo',
  data() {
    return {
      isRealName: false,
      companyName: this.$store.state.user.companyName.corpName,
      isEsignCommon: false,
      dialogVisible: false,
      entryCorpName: ''
    }
  },

  created() {
    getEsignCorpInfo().then((res) => {
      console.log(res)
      this.isRealName = res.result.isRealName
      if (res.result.orgRealName) {
        this.companyName = res.result.orgRealName
      }
      this.isEsignCommon = res.result.esignCommon
    })
  },
  mounted() {
    // console.log(this.$store.state.user.companyName.corpName)
  },
  methods: {
    goAuth() {
      const param = {
        redirectUrl: window.location.origin
      }
      if (this.isEsignCommon) {
        this.dialogVisible = true
      } else {
        if (xbb.isDingTalk()) {
          getEsignCorpRealname(param).then((res) => {
            dd.biz.util.openLink({
              url: res.result.pcUrl
            })
          })
        } else {
          this.$message({
            type: 'warning',
            message: this.$t('appModule.pleaseOpenInDingTalk')
          })
        }
      }
    },
    viewTutorial() {
      window.open('https://help.esign.cn/detail?id=pyswk8&nameSpace=cs3-dept%2Fexboae')
    },
    checkEsignCommonEnterpriseCert() {
      if (!this.entryCorpName) {
        this.$message({
          type: 'warning',
          message: '请输入e签宝内认证的机构名称'
        })
        return
      }
      getEsignCorpRealname({
        orgName: this.entryCorpName
      }).then((res) => {
        window.open(res.result.pcUrl)
        this.dialogVisible = false
        this.entryCorpName = ''
        this.$alert('跳转成功，如您签署完成回到销帮帮请刷新页面查看', '提示').then(() => {
          window.location.reload()
        })
      })
    },
    unauthenticate() {
      this.$confirm(
        '重新认证后，之前签署的数据，从e签宝获取签署信息可能会失败，确定解除企业认证吗？',
        '解除认证',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          esignUnauthenticate().then((res) => {
            if (res.success) {
              window.location.reload()
            } else {
              this.$message({
                type: 'warning',
                message: res.msg
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消解除'
          })
        })
    }
  }
}
</script>

<style lang="scss">
.cert-dialog {
  overflow: hidden;
  border-radius: 12px;
  .el-dialog__header {
    background-color: $base-white !important;
  }
}
</style>

<style lang="scss" scoped>
.company-info {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .children-head {
    position: relative;
    flex: 0 0 50px;
    height: 50px;
    font-size: 0;
    text-align: right;
    background: $base-white;
    background-color: $base-white;
    .title {
      position: absolute;
      left: 20px;
      margin-left: 0px;
      font-size: 16px;
      line-height: 50px;
      color: $text-main;
    }
  }
  .container {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: calc(100% - 20px);
    height: 200px;
    padding: 20px;
    margin: 10px;
    text-align: center;
    background: $base-white;
    p {
      font-size: 16px;
    }
    .auth-success {
      margin-top: 10px;
      font-size: 14px;
      color: #59be61;
    }
    .no-auth {
      margin-top: 10px;
      color: #9b9b9b;
      .go-auth {
        color: $brand-color-5;
        cursor: pointer;
      }
    }
  }
}
.input-suffix {
  display: flex;
  align-items: center;
  justify-content: center;
  .input-suffix-name {
    width: 100px;
  }
}
.cert-tips {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 36px;
  padding-left: 16px;
  margin-top: 20px;
  color: $text-auxiliary;
  background-color: $bg-table;
}
</style>
