<template>
  <div class="menu-list">
    <div class="menu-list__options">
      <el-button icon="el-icon-plus" size="small" @click="handleAddGroup()">添加分组</el-button>
      <el-input
        v-model="filterText"
        class="menu-list__options-search"
        clearable
        placeholder="请输入分组或表单名称"
        prefix-icon="el-icon-search"
        size="small"
        @change="handleFilter"
      >
      </el-input>
    </div>
    <el-scrollbar ref="menuList" class="menu-list__scroll">
      <el-tree
        ref="tree"
        v-loading="loading"
        :allow-drop="allowDrop"
        :data="dataList"
        :default-expand-all="expandAll"
        :default-expanded-keys="expandedArr"
        :draggable="draggable"
        :filter-node-method="filterNode"
        icon-class="icon-arrow-right-s-line t-iconfont"
        node-key="layoutGroupId"
        :props="defaultProps"
        @node-collapse="handleNodeCollapse"
        @node-drag-end="handleDragEnd"
        @node-drop="handleDrop"
        @node-expand="handleNodeExpand"
      >
        <template slot-scope="{ node, data }">
          <div
            class="custom-tree-node"
            :class="`menu-item-${data.layoutGroupId}`"
            @click="handleClick(node)"
          >
            <i
              v-if="[0, 1].includes(data.type) && data.subMenuList.length === 0"
              class="fake-icon icon-arrow-right-s-line t-iconfont"
              :class="{ 'icon-expand': node.expanded }"
            ></i>
            <div class="custom-tree-node__left">
              <i class="icon iconfont" :class="data.icon" :style="{ color: data.color }"></i>
              <span class="text">{{ data.name }}</span>
              <span v-if="data.saasMark === 1" class="origin-name">{{ data.originName }}</span>
            </div>
            <div class="custom-tree-node__right">
              <i v-show="draggable" class="icon-drag-move-2-fill icon-view t-iconfont"></i>
              <i
                class="icon-edit-line icon-opereate icon-view t-iconfont"
                @click.stop="handleEdit(data)"
              ></i>
              <el-popover
                v-if="[0, 1].includes(data.type)"
                ref="myPopover"
                placement="bottom"
                trigger="click"
                width="150"
              >
                <div class="menu-list__opereate">
                  <div
                    v-if="data.level === 1"
                    class="btn"
                    @click="handleAddGroup(data.layoutGroupId)"
                  >
                    添加分组
                  </div>
                  <div class="btn" @click="handleAddForm(data)">添加表单</div>
                </div>
                <i
                  slot="reference"
                  class="icon-add-line icon-opereate icon-view t-iconfont"
                  @click.stop
                ></i>
              </el-popover>

              <i
                class="icon-delete-bin-line icon-opereate icon-view t-iconfont"
                @click.stop="handleDelete(data)"
              ></i>
              <div v-if="commonFormList.includes(data.id)" class="flag">常</div>
              <div v-if="quickNewFormList.includes(data.id)" class="flag">快</div>
            </div>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>
    <el-dialog
      append-to-body
      class="new-style-dialog"
      :title="title"
      :visible.sync="editVisible"
      width="675px"
    >
      <app-form
        v-if="editVisible"
        ref="appForm"
        :edit-data="checkGroup"
        :is-menu="isMenu"
        mode="menu"
      ></app-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false">取 消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="handleEditConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      append-to-body
      class="new-style-dialog"
      title="确认删除吗?"
      :visible.sync="deleteVisile"
      width="480px"
    >
      <div class="delete-content">
        <i class="delete-icon icon-error-warning-fill t-iconfont"></i>
        <div class="delete-text">删除分组后，该分组下的子菜单也将被删除</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteVisile = false">取 消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="handleDeleteGroup(checkGroup)"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      v-if="selectVisible"
      append-to-body
      class="new-style-dialog select-form-dialog"
      title="添加表单"
      :visible.sync="selectVisible"
      width="800px"
    >
      <select-form ref="selectForm" :check-group="checkGroup"></select-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getMenu } from '@/api/system'
import xbb from '@xbb/xbb-utils'

import SelectForm from './select-form.vue'
import AppForm from './app-form.vue'
import { HashCompare } from '@/utils'

const hashCompare = new HashCompare()

export default {
  name: 'MenuList',

  components: {
    SelectForm,
    AppForm
  },

  computed: {
    isEdit() {
      return !!this.checkGroup.id
    },

    title() {
      const prefix = this.isEdit ? '编辑' : '新增'
      const suffix = this.isMenu ? '菜单' : '分组'
      return `${prefix}${suffix}`
    }
  },
  watch: {
    filterText(val) {
      if (val) {
        this.draggable = false
      } else {
        this.draggable = true
      }
    }
  },

  props: {
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      draggable: true,
      hasEdit: false, // 是否编辑过，是的话 要提示
      filterText: '',
      scrollId: [],
      isMenu: true,
      defaultCursor: '',
      loading: false,
      dataList: [],
      commonFormList: [], // 常用表单列表id集合
      quickNewFormList: [], // 快捷新建表单列表id集合
      defaultProps: {
        children: 'subMenuList',
        label: 'name'
      },
      btnLoading: false,
      checkGroup: {}, // 选中分组数据
      editVisible: false,
      deleteVisile: false,
      selectVisible: false,
      expandAll: false,
      expandedArr: []
    }
  },
  updated() {
    if (!this.scrollId.length) {
      return
    } else if (this.scrollId.length === 1) {
      const element = this.$refs.menuList.wrap.querySelector(`.menu-item-${this.scrollId[0]}`)
      element && this.jumpAndTips({ element, jump: true, shine: true })
    } else if (this.scrollId.length > 1) {
      this.scrollId.forEach((item, index) => {
        let scroll = true
        if (index !== 0) {
          scroll = false
        }
        const element = this.$refs.menuList.wrap.querySelector(`.menu-item-${item}`)
        element && this.jumpAndTips({ element, jump: scroll, shine: true })
      })
    }
    this.scrollId = []
  },
  mounted() {
    this.getMenuList()
  },
  methods: {
    handleClick(node) {
      if ([0, 1].includes(node.data.type) && node.data.subMenuList.length === 0) {
        node.expanded = !node.expanded
      }
    },
    getMenuList() {
      // 新增模式不请求接口
      if (this.editData.mode === 'add') {
        this.$emit('initEnd', true)
        return
      }
      this.loading = true
      this.dataList = []
      getMenu({
        layoutId: this.editData.id
      })
        .then((res) => {
          const { menuList, quickNewFormList, commonFormList } = res.result
          this.dataList = this.dataFormat({ data: menuList, init: true })
          this.quickNewFormList = quickNewFormList
          this.commonFormList = commonFormList
          hashCompare.init({ dataList: this.dataList, quickNewFormList, commonFormList })
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
          this.scrollId = []
          this.$emit('initEnd', true)
        })
    },
    // 给父组件调用的方法
    isChange() {
      return !hashCompare.compare({
        dataList: this.dataList,
        quickNewFormList: this.quickNewFormList,
        commonFormList: this.commonFormList
      })
    },
    /**
     * @description: 过滤
     * @param {*} value
     * @param {*} data
     */
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleDrop(draggingNode, dropNode, dropType, ev) {
      this.dataList = this.dataFormat({ data: xbb.deepClone(this.dataList) })
      this.$nextTick(() => {
        this.$refs.tree.filter(this.filterText)
      })
      this.hasEdit = true
    },
    /**
     * @description: 校验拖拽节点是否允许插入
     * @param {Object} draggingNode 拖拽节点
     * @param {Object} dropNode 目标节点
     */
    inneerJuadge(draggingNode, dropNode) {
      // 1. 只有目标节点是分组可以 插入至目标节点
      // 2. 分组和表单 一共不可以超过3层
      // 2.1 第二层分组 只能插入表单
      // 2.2 第一层分组 可以插入一层仅分组 无子项
      // 2.3 第一层分组 可以插入一层仅有表单的分组
      // 2.4 第一层分组 可以插入表单
      const draggingType = draggingNode.data.type // 拖拽节点的类型 [0, 1]表示分组 其余表示表单
      const draggingSubDeep = draggingNode.data.subDeep
      const draggingHasSubGroup = draggingNode.data.hasSubGroup
      const dropType = dropNode.data.type // 目标节点的类型 [0, 1]表示分组 其余表示表单
      const dropLevel = dropNode.level
      const isDropGroup = [0, 1].includes(dropType)
      const isDraggingForm = ![0, 1].includes(draggingType)

      const condition1 = dropLevel === 2 && isDraggingForm // 2.1 第二层分组 只能插入表单
      const condition2 = dropLevel === 1 && [0, 1].includes(draggingType) && draggingSubDeep === 0 // 2.2 第一层分组 可以插入一层仅分组 无子项
      const condition3 =
        dropLevel === 1 &&
        [0, 1].includes(draggingType) &&
        draggingSubDeep === 1 &&
        !draggingHasSubGroup // 2.3 第一层分组 可以插入一层仅有表单的分组
      const condition4 = dropLevel === 1 && isDraggingForm // 2.4 第一层分组 可以插入表单
      return isDropGroup && (condition1 || condition2 || condition3 || condition4)
    },
    /**
     * @description: 校验拖拽节点是否允许插上插下
     * @param {Object} draggingNode 拖拽节点
     * @param {Object} dropNode 目标节点
     */
    beforeAfterJuadge(draggingNode, dropNode) {
      // 1. 第一层 随便插上插下, 除表单外
      // 2.1 第二层 可以插 仅有表单的分组 有1层子以内的分组
      // 3 拖拽节点是表单可以随意插上或者插下
      // 4 除了第一层，表单可以随意插上插下
      const draggingType = draggingNode.data.type // 拖拽节点的类型 [0, 1]表示分组 其余表示表单
      const draggingSubDeep = draggingNode.data.subDeep
      const isDraggingForm = ![0, 1].includes(draggingType)
      const condition1 = dropNode.level === 1 && !isDraggingForm // 1. 第一层 随便插上插下, 除表单外
      const condition2 = dropNode.level === 2 && draggingSubDeep <= 1 // 2.1 第二层 可以插 仅有表单的分组 有1层子以内的分组
      const condition3 = dropNode.level !== 1 && isDraggingForm // 4 除了第一层，表单可以随意插上插下
      // const condition1 =
      return condition1 || condition2 || condition3
    },
    /**
     * @description: 拖拽时判定目标节点能否被放置。
     * @param {Object} draggingNode 拖拽节点
     * @param {Object} dropNode 目标节点
     * @param {String} type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后
     */
    allowDrop(draggingNode, dropNode, type) {
      if (type === 'inner' && this.inneerJuadge(draggingNode, dropNode)) {
        // 插入需要满足的条件
        return true
      } else if (type !== 'inner' && this.beforeAfterJuadge(draggingNode, dropNode)) {
        // 插上或者插下需要满足的条件
        return type !== 'inner'
      } else {
        return false
      }
    },
    /**
     * @description: 给数据添加层级
     * @param {Object} data 数据列表
     * @param {Number} level 当前层级
     */
    dataFormat({ data, level = 1, parentIds = [], init = false }) {
      const getSubDeep = (list) => {
        // 不用递归处理，菜单最多只有3层，有2层子是最大情况
        const data = {
          deep: 0,
          hasSubGroup: false
        }
        if (list && list.length) {
          data.deep = 1
          data.hasSubGroup = list.some((item) => [0, 1].includes(item.type))
          if (data.hasSubGroup) {
            data.deep = 2
          }
        }
        return data
      }
      const list = data.map((item, index) => {
        // 递归处理数据格式
        item.parentIds = parentIds
        if (level === 1) {
          item.parentId = 0
        }
        if (item.subMenuList && item.subMenuList.length) {
          const newIds = [...item.parentIds, item.layoutGroupId]
          item.subMenuList = this.dataFormat({
            data: item.subMenuList,
            level: level + 1,
            parentIds: newIds,
            init
          })
        }

        item.subDeep = getSubDeep(item.subMenuList).deep
        item.hasSubGroup = getSubDeep(item.subMenuList).hasSubGroup
        item.sort = index + 1
        if (level !== 1) {
          item.parentId = parentIds[parentIds.length - 1]
        }
        if (init && [0, 1].includes(item.type)) {
          this.expandedArr.push(item.layoutGroupId)
        }
        return {
          ...item,
          level
        }
      })
      return list
    },

    /**
     * @description: 节点收起
     * @param {Object} node 收起的节点数据
     */
    handleNodeCollapse(node) {
      const ids = [node.layoutGroupId]
      if (node.hasSubGroup) {
        const subGroup = node.subMenuList.filter((item) => [0, 1].includes(item.type))
        subGroup.forEach((item) => {
          ids.push(item.layoutGroupId)
        })
      }
      this.expandedArr = this.expandedArr.filter((item) => !ids.includes(item))
      this.expandAll = false
    },
    /**
     * @description: 节点展开
     * @param {Object} node 展开的节点数据
     */
    handleNodeExpand(node) {
      this.expandAll = false
      this.expandedArr.push(node.layoutGroupId)
    },
    /**
     * @description: 新增分组
     * @param {Object} data 新增分组的数据
     */
    handleAdd(data) {
      const {
        id = null, // null 为 新建；不为空为编辑
        name = '未命名菜单分组',
        nameEn = '未命名菜单分组',
        parentId = 0,
        color = '#C9CDD4',
        icon = 'icon-cloud'
      } = data
      if (!id) {
        const layoutGroupId = new Date().getTime()
        const dataSet = (target, level) => {
          target.push({
            color,
            corpid: localStorage.getItem('corpid'),
            hasSubGroup: false,
            icon,
            layoutGroupId,
            isNew: true,
            level,
            name,
            nameEn,
            parentId,
            parentIds: level === 2 ? [parentId] : [], // 由于菜单最多只有2层分组，第二层无法添加分组。所以父id链只有一级父
            sort: target.length + 1,
            subDeep: 0,
            subMenuList: [],
            type: level - 1 // 第一层给0，第二层给1
          })
        }
        if (parentId === 0) {
          dataSet(this.dataList, 1)
        } else {
          const index = this.dataList.findIndex((item) => item.layoutGroupId === parentId)
          dataSet(this.dataList[index].subMenuList, 2)
        }
        // el-scrollbar 滚动底部
        this.$nextTick(() => {
          const element = this.$refs.menuList.wrap.querySelector(`.menu-item-${layoutGroupId}`)
          this.jumpAndTips({ element, jump: true, shine: true })
        })
      } else {
        this.silentFresh(this.checkGroup)
      }
      this.hasEdit = true
      this.editVisible = false
    },
    /**
     * @description: 元素滚动至对应位置 并闪动提示
     * @param {*} element
     * @param {Boolean} jump 是否跳至元素行
     * @param {Boolean} shine 是否高亮闪动2次元素行
     */
    jumpAndTips({ element, jump = false, shine = false }) {
      // this.$refs.menuList.wrap.scrollTop = element.offsetTop
      if (jump) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
      if (shine) {
        // 添加闪动效果
        let count = 0
        const flashInterval = setInterval(() => {
          if (count % 2 === 0) {
            element.style.backgroundColor = '#fff5e8'
          } else {
            element.style.backgroundColor = 'unset'
          }
          count++
          if (count === 4) {
            // 闪动2次
            clearInterval(flashInterval)
          }
        }, 500) // 闪动间隔为500毫秒
      }
    },
    /**
     * @description: 不刷新请求接口，更新指定数据
     */
    silentFresh(data) {
      const dataSet = (dataSet, data) => {
        dataSet.name = data.name
        dataSet.nameEn = data.nameEn
        dataSet.color = data.color
        dataSet.icon = data.icon
      }
      if (data.parentId !== 0 && data.parentIds.length === 1) {
        const index1 = this.dataList.findIndex((item) => item.layoutGroupId === data.parentIds[0])
        const index2 = this.dataList[index1].subMenuList.findIndex(
          (item) => item.layoutGroupId === data.layoutGroupId
        )
        dataSet(this.dataList[index1].subMenuList[index2], data)
      } else if (data.parentId !== 0 && data.parentIds.length === 2) {
        const index1 = this.dataList.findIndex((item) => item.layoutGroupId === data.parentIds[0])
        const index2 = this.dataList[index1].subMenuList.findIndex(
          (item) => item.layoutGroupId === data.parentIds[1]
        )
        const index3 = this.dataList[index1].subMenuList[index2].subMenuList.findIndex(
          (item) => item.layoutGroupId === data.layoutGroupId
        )
        dataSet(this.dataList[index1].subMenuList[index2].subMenuList[index3], data)
      } else {
        const index = this.dataList.findIndex((item) => item.layoutGroupId === data.layoutGroupId)
        dataSet(this.dataList[index], data)
      }
    },
    /**
     * @description: 不刷新请求接口，删除指定数据
     */
    slientDelete(data) {
      if (data.level === 1) {
        const index = this.dataList.findIndex((item) => item.layoutGroupId === data.layoutGroupId)
        this.dataList.splice(index, 1)
      } else if (data.level === 2) {
        const index1 = this.dataList.findIndex((item) => item.layoutGroupId === data.parentIds[0])
        const index2 = this.dataList[index1].subMenuList.findIndex(
          (item) => item.layoutGroupId === data.layoutGroupId
        )
        this.dataList[index1].subMenuList.splice(index2, 1)
      } else if (data.level === 3) {
        const index1 = this.dataList.findIndex((item) => item.layoutGroupId === data.parentIds[0])
        const index2 = this.dataList[index1].subMenuList.findIndex(
          (item) => item.layoutGroupId === data.parentIds[1]
        )
        const index3 = this.dataList[index1].subMenuList[index2].subMenuList.findIndex(
          (item) => item.layoutGroupId === data.layoutGroupId
        )
        this.dataList[index1].subMenuList[index2].subMenuList.splice(index3, 1)
      }
    },
    /**
     * @description: 编辑分组
     * @param {Object} data 被编辑的分组数据
     */
    handleEdit(data) {
      this.setCheckData(data)
      if ([0, 1].includes(data.type)) {
        this.isMenu = false
      } else {
        this.isMenu = true
      }
      this.editVisible = true
    },
    handleEditConfirm() {
      this.$refs.appForm.$refs.formData.validate((valid) => {
        if (valid) {
          // this.editVisible = false
          const { appForm } = this.$refs.appForm
          const { commonForm, quickNewForm } = appForm
          if (!this.judageCanAdd(commonForm, quickNewForm, this.checkGroup.id)) return
          this.checkGroup = appForm
          this.handleAdd({
            ...this.checkGroup,
            id: this.checkGroup.layoutGroupId
          })
          this.handleAddCommonAndQuick(commonForm, quickNewForm)
        }
      })
    },
    /**
     * @description: 判断是否可以添加表单和快捷新建
     * @param {Boolean} commonForm 添加常用表单操作
     * @param {Boolean} quickNewForm 添加快捷新建操作
     * @param {Number} id 当前表单id
     */
    judageCanAdd(commonForm, quickNewForm, id) {
      // 常用表单，最多7个，快捷新建，最多12个。
      const juageIsRight = ({ value, key, ruleName, ruleNum }) => {
        if (value && this[key].length === ruleNum && !this[key].includes(id)) {
          this.$message({
            type: 'error',
            message: `${ruleName}最多可设置${ruleNum}个`
          })
          return false
        } else {
          return true
        }
      }
      const common = juageIsRight({
        value: commonForm,
        key: 'commonFormList',
        ruleName: '常用表单',
        // ruleNum: 7
        ruleNum: 8
      })
      const quick = juageIsRight({
        value: quickNewForm,
        key: 'quickNewFormList',
        ruleName: '快捷新建',
        ruleNum: 12
      })
      return common && quick
    },
    /**
     * @description: 设置常用表单和快捷新建
     */
    handleAddCommonAndQuick(commonForm, quickNewForm) {
      const dataSet = (value, key) => {
        if (value && !this[key].includes(this.checkGroup.id)) {
          this[key].push(this.checkGroup.id)
        } else if (!value) {
          this[key] = this[key].filter((item) => item !== this.checkGroup.id)
        }
      }
      dataSet(commonForm, 'commonFormList')
      dataSet(quickNewForm, 'quickNewFormList')
    },
    /**
     * @description: 删除分组
     * @param {Object} data 被删除的分组数据
     */
    handleDelete(data) {
      if (data.subMenuList && data.subMenuList.length) {
        this.deleteVisile = true
        this.checkGroup = data
      } else {
        this.handleDeleteGroup(data)
      }
    },

    /**
     * @description: 删除分组或表单
     * @param {Object} data 被删除的分组或表单数据
     */
    handleDeleteGroup(data) {
      this.slientDelete(data)
      this.deleteVisile = false
      this.hasEdit = true
    },
    handleAddForm(data) {
      this.setCheckData(data)
      this.selectVisible = true
    },
    /**
     * @description: 添加分组
     * @param {*} data
     */
    handleAddGroup(parentId = 0) {
      this.checkGroup = {
        name: '',
        nameEn: '未命名菜单分组',
        parentId,
        color: '#C9CDD4',
        icon: 'icon-cloud'
      }

      this.isMenu = false
      this.editVisible = true
    },
    setCheckData(data) {
      const { id } = data
      const tempData = xbb.deepClone(data)
      if (this.commonFormList.includes(id)) {
        tempData.commonForm = true
      }
      if (this.quickNewFormList.includes(id)) {
        tempData.quickNewForm = true
      }
      this.checkGroup = tempData
    },
    /**
     * @description: 添加表单
     * @param {*} data
     */
    handleConfirm() {
      this.scrollId = []

      const { level, parentIds, layoutGroupId } = this.checkGroup
      const id = new Date().getTime()
      const sortBase = this.checkGroup.subMenuList.length
      const _parentIds = [...parentIds, layoutGroupId]
      const checkList = this.$refs.selectForm.checkList

      const arr = checkList.map((item, index) => {
        this.scrollId.push(id + index)
        return {
          ...item,
          isNew: true,
          level: level + 1,
          parentId: layoutGroupId,
          parentIds: _parentIds,
          layoutGroupId: id + index,
          sort: sortBase + index + 1
        }
      })
      this.addFormToDatalist(arr, layoutGroupId)
      this.selectVisible = false

      this.expandedArr.push(layoutGroupId)
      this.hasEdit = true
    },

    // 将新增的表单添加到对应分组
    addFormToDatalist(arr, layoutGroupId) {
      // 定义一个递归函数，来遍历所有的菜单
      const addRecursively = (menuList) => {
        menuList.some((item) => {
          // 如果找到对应的分组，就往里面添加，并终止遍历
          if (item.layoutGroupId === layoutGroupId) {
            item.subMenuList = item.subMenuList.concat(arr)
            return true
          } else if (item.subMenuList && item.subMenuList.length) {
            return addRecursively(item.subMenuList)
          }
          return false
        })
      }
      addRecursively(this.dataList)
    },
    /**
     * @description 拖拽结束，分组的type需要设置对应的0或者1。0表示最外层，1表示内1层，内2层不可能是分组类型
     */
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      if ([0, 1].includes(draggingNode.data.type)) {
        // 只有分组可以改变type
        if (['before', 'after'].includes(dropType)) {
          draggingNode.data.type = dropNode.data.level - 1
        } else if (['inner'].includes(dropType)) {
          draggingNode.data.type = dropNode.data.level
        }
      }
    },
    handleFilter(val) {
      this.$refs.tree.filter(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-list :deep(.el-tree-node__content) {
  box-sizing: border-box;
  gap: 12px;
  height: 40px;
  // margin-bottom: 4px;
}
.menu-list :deep(.el-tree-node__content .el-tree-node__expand-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  padding: 0;
  font-size: 20px;
}
.menu-list :deep(.el-tree-node.is-drop-inner .el-tree-node__content) {
  background-color: $brand-color-1;
}
.menu-list {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  &__options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    margin-bottom: 16px;
    &-search {
      width: 280px;
    }
  }
  &__scroll {
    box-sizing: border-box;
    flex: 1 1 auto;
    height: 600px;
    background-color: $base-white;
    border: 1px solid $neutral-color-3;
    border-radius: 8px;
  }
  :deep(.el-scrollbar__wrap) {
    // max-height: 248px;
    height: 100%;
    padding: 0 20px;
  }
  .custom-tree-node {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding-right: 8px;
    .fake-icon {
      position: absolute;
      left: -32px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      padding: 0;
      font-size: 20px;
      color: $text-grey;
      transition: transform 0.3s ease-in-out;
    }
    .icon-expand {
      transform: rotate(90deg);
    }
    .custom-tree-node__left {
      .icon {
        font-size: 18px;
      }
      .text {
        margin-left: 12px;
        font-size: 14px;
        line-height: 18px;
        color: $text-plain;
      }
      .origin-name {
        margin-left: 12px;
        font-size: 12px;
        line-height: 16px;
        color: $text-grey;
      }
    }
    .custom-tree-node__right {
      display: flex;
      gap: 12px;
      .icon-drag-move-2-fill {
        cursor: move !important;
      }
      .flag {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        font-size: 12px;
        color: $brand-color-5;
        background: $brand-color-1;
        border-radius: 4px;
      }
      .icon-view {
        padding: 1px;
        color: $text-auxiliary;
        visibility: hidden;
      }
      .icon-opereate:hover {
        color: $brand-base-color-6;
        background-color: $brand-color-2;
        border-radius: 4px;
      }
    }
  }
  .custom-tree-node:hover {
    .icon-view {
      visibility: visible;
    }
  }
}
</style>

<style lang="scss">
.new-style-dialog {
  .delete-content {
    display: flex;
    gap: 12px;
    .delete-icon {
      color: $brand-base-color-6;
    }
    .delete-text {
      font-size: 14px;
      color: $text-plain;
    }
  }
}

.select-form-dialog {
  .el-dialog__body {
    position: relative;
    height: 448px; // 头部48 + 底部60 + padding-top24 padding-bottom24 600 - 48 - 56 - 24 - 24 = 448
    padding: 24px !important;
  }
}
.menu-list__opereate {
  .btn {
    display: flex;
    align-items: center;
    height: 32px;
    cursor: pointer;
  }
  .btn:hover {
    color: $brand-color-5;
    background-color: $brand-color-1;
  }
}
</style>
