<!--
 * @Description:
-->
<template>
  <div class="select-form">
    <span>{{ label }}</span>
    <!-- :currentFormId="currentFormId" -->

    <AppFormSelectTree
      ref="targetForm"
      :app-id.sync="appId"
      :business-rule-flag="businessRuleFlag"
      :business-type.sync="syncBusinessType"
      :form-id.sync="syncFormId"
      :menu-id.sync="menuId"
      :name.sync="name"
      :saas-mark.sync="syncSaasMark"
      :style="{ width: width }"
      @change="linkFormIdChange"
    >
    </AppFormSelectTree>
    <!-- :appId.sync="syncAppId" -->
    <!-- :menuId.sync="syncMenuId" -->
    <!-- :name.sync="syncName" -->
  </div>
</template>

<script>
import AppFormSelectTree from '@/views/edit/new-form-design/components/other/form-select-tree.vue'

export default {
  name: 'SelectForm',

  components: {
    AppFormSelectTree
  },

  filters: {},

  mixins: [],

  props: {
    width: {
      type: String,
      default: '200px'
    },
    label: {
      type: String,
      default: ''
    },
    businessRuleFlag: {
      type: Number,
      default: 1 // 业务规则 1 其他 2
    },
    formId: {},
    saasMark: {},
    businessType: {}
  },

  data() {
    return {
      name: '',
      appId: '',
      menuId: ''
    }
  },

  computed: {
    syncFormId: {
      get() {
        return this.formId
      },
      set(val) {
        this.$emit('update:formId', val)
      }
    },
    syncSaasMark: {
      get() {
        return this.saasMark
      },
      set(val) {
        this.$emit('update:saasMark', val)
      }
    },
    syncBusinessType: {
      get() {
        return this.businessType
      },
      set(val) {
        this.$emit('update:businessType', val)
      }
    }
  },

  watch: {},
  mounted() {},

  methods: {
    init() {},
    linkFormIdChange() {}
  }
}
</script>

<style lang="scss" scoped>
.select-form {
  display: flex;
  align-items: center;
}
</style>
