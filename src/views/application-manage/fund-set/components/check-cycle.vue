<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 对账设置->对账周期
-->
<template>
  <div class="check-cycle">
    <div class="check-cycle__item">
      <span>对账周期：</span>
      <el-select
        v-model="rule[0].type"
        :disabled="isEditPage"
        placeholder="请选择"
        @change="cycleChange"
      >
        <el-option
          v-for="c in cycleType"
          :key="c.value"
          :label="c.label"
          :value="c.value"
        ></el-option>
      </el-select>
    </div>
    <div class="check-cycle__item">
      <div class="check-cycle__item_text">起止日期：</div>
      <el-col class="select-box">
        <el-select v-model="rule[0].prefix" :disabled="isEditPage">
          <el-option v-for="b in prefixType" :key="b.value" :label="b.label" :value="b.value">
          </el-option>
        </el-select>
      </el-col>

      <el-col v-if="rule[0].type === 3" class="select-box">
        <el-select v-model="rule[0].month" :disabled="isEditPage" @change="monthChange">
          <el-option v-for="m in monthList" :key="m.value" :label="m.label" :value="m.value">
          </el-option>
        </el-select>
      </el-col>

      <el-col class="select-box">
        <el-select v-model="rule[0].day" :disabled="isEditPage">
          <el-option v-for="d in dayList" :key="d.value" :label="d.label" :value="d.value">
          </el-option>
        </el-select>
      </el-col>
      <div>至：</div>
      <el-col class="select-box">
        <el-select v-model="endVal.prefix" disabled> </el-select>
      </el-col>

      <el-col v-if="rule[0].type === 3" class="select-box">
        <el-select v-model="endVal.month" disabled> </el-select>
      </el-col>

      <el-col class="select-box">
        <el-select v-model="endVal.day" disabled> </el-select>
      </el-col>
    </div>

    <div class="check-cycle__item">
      <div>对账单生成日期：</div>
      <div>{{ endVal.createDate }}</div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import { endDateStrategy, getMonthDayList } from './fund-set'

export default {
  name: 'CheckCycle',
  props: {
    isEditPage: {
      type: Boolean,
      default: () => true
    },
    rule: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      cycleType: [
        { value: 1, label: '按周' },
        { value: 2, label: '按月' },
        { value: 3, label: '按年' }
      ],
      prefixType: [
        { value: 'last', label: '' },
        { value: 'this', label: '' }
      ],
      dayList: [],
      monthList: []
    }
  },
  computed: {
    endVal() {
      const { day, month, prefix, type } = this.rule[0] // 1周 2月 3年
      // 利用策略模式
      return endDateStrategy[type][prefix](day, month) // 直接打中对应的策略
    }
  },

  mounted() {
    for (let index = 1; index <= 12; index++) {
      this.monthList.push({
        value: index,
        label: index + '月'
      })
    }
    this.dataInit()
  },

  methods: {
    /**
     * @description 值初始化
     */
    dataInit() {
      const key = this.rule[0].type
      this.getprefixType(key)
      this.getDayList(key)
    },
    /**
     * @description 对账周期改变
     */
    cycleChange() {
      this.rule[0].month = 1
      this.rule[0].day = 1
      this.rule[0].prefix = 'this'
      const key = this.rule[0].type
      this.getprefixType(key)
      this.getDayList(key)
    },
    /**
     * @description 特殊的策略模式：锁定对应的展示文案
     * @param {Number} [key] 对应的策略，即：周、月、年
     */
    getprefixType(key) {
      const prefixEnum = {
        1: ['上周', '本周'],
        2: ['上月', '本月'],
        3: ['去年', '今年']
      }
      this.prefixType = this.prefixType.map((item, index) => {
        item.label = prefixEnum[key][index]
        return item
      })
    },
    /**
     * @description 获取时间选项-day
     * @param {Number} [key] 选择按周、月、年
     */
    getDayList(key) {
      this.dayList = []
      if (key === 1) {
        // 按周
        const weekArr = [
          this.$t('fundSet.monday'),
          this.$t('fundSet.tuesday'),
          this.$t('fundSet.wednesday'),
          this.$t('fundSet.thursday'),
          this.$t('fundSet.friday'),
          this.$t('fundSet.saturday'),
          this.$t('fundSet.sunday')
        ]
        weekArr.forEach((item, index) => {
          this.dayList.push({ value: index + 1, label: item })
        })
      } else if (key === 2) {
        for (let i = 1; i <= 28; i++) {
          this.dayList.push({ value: i, label: i + '日' })
        }
      } else if (key === 3) {
        this.getMonthDayList()
      }
    },
    /**
     * @description 获取时间选项-month
     */
    getMonthDayList() {
      this.dayList = []
      const allDay = getMonthDayList(this.rule[0].month)
      for (let index = 1; index <= allDay; index++) {
        this.dayList.push({
          value: index,
          label: index + '日'
        })
      }
    },
    /**
     * @description 修改月份
     */
    monthChange() {
      this.rule[0].day = 1
      this.getMonthDayList()
    }
  }
}
</script>

<style lang="scss" scoped>
.check-cycle {
  .check-cycle__item {
    display: flex;
    align-items: center;
    margin-top: 15px;
    &_text {
      flex: 0 0 70px;
    }
    .select-box {
      width: 115px;
      min-width: 115px;
      margin-right: 15px;
    }
  }
}
</style>
