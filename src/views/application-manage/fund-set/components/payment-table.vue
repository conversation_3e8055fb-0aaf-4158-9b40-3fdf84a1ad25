<!-- eslint vue/return-in-computed-property: 1 -->

<!--
 * @Description: 账期表格
-->
<template>
  <div class="payment-table">
    <el-table border :data="tableData" style="width: 100%">
      <el-table-column :label="modelName" min-width="10">
        <template slot-scope="scope">
          {{ scope.row.formName | showName }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('fundSet.defaultDate')" min-width="17">
        <template slot-scope="scope">
          {{ scope.row | showTitle }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('operation.operate')" min-width="7">
        <template slot-scope="scope">
          <el-button v-if="!isEditPage" type="text" @click="editTrigger(scope.row)">{{
            $t('operation.edit')
          }}</el-button>
          <el-button v-else-if="isEditPage" type="text" @click="editTrigger(scope.row)">{{
            $t('operation.view')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
/* eslint vue/return-in-computed-property: 1 */

import xbb from '@xbb/xbb-utils'

export default {
  name: 'PaymentTable',
  filters: {
    showName(value) {
      return value || '--'
    },
    showTitle(value) {
      let title = '--'
      if (value.attrName && value.rule.length) {
        title = `已根据“${value.attrName}”设置默认账期`
      }
      return title
    }
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    manageType: {
      type: Number,
      required: true
    },
    isEditPage: {
      type: Boolean,
      default: () => true
    }
  },
  computed: {
    modelName() {
      if (this.manageType === 3) {
        return this.$t('fundSet.customerTemplate')
      } else if (this.manageType === 4) {
        return this.$t('fundSet.contractTemplat')
      }
    }
  },
  methods: {
    @xbb.debounceWrap(300)
    editTrigger(rowData) {
      this.$emit('editTrigger', rowData)
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-table {
  width: 100%;
}
</style>
