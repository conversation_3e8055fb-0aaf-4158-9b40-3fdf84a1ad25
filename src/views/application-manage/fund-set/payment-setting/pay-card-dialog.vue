<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 支付设置的弹窗
-->
<template>
  <div v-if="dialogVisible" class="pay-card-dialog">
    <el-dialog
      :before-close="handleClose"
      :title="computedTitle + '支付设置'"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <div v-if="showTypeArray.includes(alias)" class="dialog-tips">
        <span class="web-icon-gantanhao web-iconfont"></span>
        <span> 设置账号前，请确保您已在{{ computedTitle }}平台完成相关设置。</span>
        <span class="help" @click="helpClick">查看帮助</span>
      </div>
      <!-- 支付宝设置 -->
      <el-form
        v-if="alias === 'alipay'"
        ref="alipay"
        label-position="left"
        label-width="120px"
        :model="alipay"
      >
        <el-form-item label="应用公钥证书">
          <file-upload v-model="alipay.applicationPublicKey"> </file-upload>
        </el-form-item>
        <el-form-item label="支付宝公钥证书">
          <file-upload v-model="alipay.publicKey"> </file-upload>
        </el-form-item>
        <el-form-item label="支付宝根证书">
          <file-upload v-model="alipay.rootKey"> </file-upload>
        </el-form-item>
      </el-form>
      <!-- 微信支付 -->
      <el-form
        v-else-if="alias === 'weChat'"
        ref="weChat"
        label-position="left"
        label-width="120px"
        :model="weChat"
      >
        <el-form-item label="商户API证书">
          <file-upload v-model="weChat.merchantApi"></file-upload>
        </el-form-item>
        <el-form-item label="平台证书">
          <file-upload v-model="weChat.platformCertificate"></file-upload>
        </el-form-item>
        <el-form-item label="商户API私钥">
          <el-input v-model="weChat.merchantApiSecret"></el-input>
        </el-form-item>
        <el-form-item label="平台公钥">
          <el-input v-model="weChat.platformPublicKey"></el-input>
        </el-form-item>
        <el-form-item label="APIv3密钥">
          <el-input v-model="weChat.apivSecretKey"></el-input>
        </el-form-item>
      </el-form>
      <!-- 银联支付 -->
      <el-form
        v-else-if="alias === 'unionPay'"
        ref="unionPay"
        label-position="left"
        label-width="115px"
        :model="unionPay"
      >
        <el-form-item label="敏感加密证书">
          <file-upload v-model="unionPay.sensitiveEncryptionKey"></file-upload>
        </el-form-item>
        <el-form-item label="根证书">
          <file-upload v-model="unionPay.rootKey"></file-upload>
        </el-form-item>
        <el-form-item label="中级证书">
          <file-upload v-model="unionPay.intermediateKey"></file-upload>
        </el-form-item>
        <el-form-item label="商户私钥证书">
          <file-upload v-model="unionPay.merchantPrivateKey"></file-upload>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="unionPay.password"></el-input>
        </el-form-item>
      </el-form>
      <!-- 线下支付 -->
      <el-form
        v-else-if="alias === 'offline'"
        ref="offline"
        label-position="left"
        label-width="80px"
        :model="offline"
      >
        <el-form-item label="账户名称">
          <el-input v-model="offline.accountName"></el-input>
        </el-form-item>
        <el-form-item label="开户银行">
          <el-input v-model="offline.accountBank"></el-input>
        </el-form-item>
        <el-form-item label="银行账户">
          <el-input v-model="offline.accountId"></el-input>
        </el-form-item>
      </el-form>
      <div v-if="showTypeArray.includes(alias)" class="footer-tips">
        完成以上步骤操作，即可点击下方【确定】按钮，完成{{ computedTitle }}在线签约
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="sure">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import { getPayListItem, paySave } from '@/api/fund-account.js'
import fileUpload from './file-upload'

export default {
  name: 'PayCardDialog',

  components: {
    FileUpload: fileUpload
  },

  filters: {},

  mixins: [],

  props: {
    dialogVisible: {
      type: Boolean,
      default: () => false
    },
    currentItem: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      alipay: {
        applicationPublicKey: [],
        publicKey: [],
        rootKey: []
      }, // 支付宝相关信息
      weChat: {
        merchantApi: [],
        platformCertificate: []
      }, // 微信相关信息
      unionPay: {
        sensitiveEncryptionKey: [],
        rootKey: [],
        intermediateKey: [],
        merchantPrivateKey: []
      }, // 银联相关信息
      offline: {}, // 线下相关信息
      showTypeArray: ['alipay', 'weChat', 'unionPay']
      // rules: {
      //   appID: [{required: true, message: '不能为空'}],
      //   appSecret: [{required: true, message: '不能为空'}],
      //   mchid: [{required: true, message: '不能为空'}],
      //   appKey: [{required: true, message: '不能为空'}]
      // }
    }
  },

  computed: {
    computedTitle() {
      const obj = {
        alipay: '支付宝',
        weChat: '微信',
        unionPay: '银联',
        offline: '线下'
      }
      return obj[this.alias]
    },
    alias() {
      return this.currentItem.alias || ''
    }
  },

  watch: {},

  created() {
    this.getPayListItem()
  },

  mounted() {},

  methods: {
    getPayListItem() {
      getPayListItem({ alias: this.alias }).then((res) => {
        const { result } = res
        this[this.alias] = result || {}
      })
    },
    // 取消
    cancel() {
      this.close()
    },
    // 确定
    sure() {
      this.$refs[this.alias].validate((v) => {
        if (v) {
          this.paySave().then((res) => {
            this.$message({
              message: '设置成功！',
              duration: 3000,
              type: 'success'
            })
            this.close()
            this.$emit('refresh') // 刷新
          })
        }
      })
    },
    close() {
      this.$emit('update:dialogVisible', false)
    },
    handleClose() {
      this.close()
    },
    helpClick() {
      console.log('查看帮助')
      // todo
    },
    paySave() {
      return new Promise((resolve, reject) => {
        const params = { alias: this.alias, name: this.currentItem.name, ...this[this.alias] }
        paySave(params)
          .then((res) => {
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pay-card-dialog {
  .web-icon-gantanhao {
    color: $brand-color-5;
  }
  .dialog-tips .help {
    color: $brand-color-5;
    cursor: pointer;
  }
  .dialog-tips {
    margin-bottom: 20px;
  }
}
</style>
