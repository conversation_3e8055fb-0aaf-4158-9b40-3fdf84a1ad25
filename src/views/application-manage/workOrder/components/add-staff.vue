<!--
 * @Description: 新建&编辑工单服务人员
           showAddNew: 是否显示该弹窗
 -->

<template>
  <div class="add-staff">
    <el-dialog
      :before-close="closeDialog"
      :title="title + $t('workOrder.orderService')"
      :visible.sync="dialogVisible"
      width="700px"
    >
      <el-form v-if="showForm" ref="form" label-width="120px" :model="form" :rules="rules">
        <el-form-item
          v-clickoutside="closeDropbox"
          :label="$t('nouns.servicePersonal')"
          prop="userId"
        >
          <el-input
            class="input-width"
            clearable
            :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.servicePersonal') })"
            readonly
            :value="form.userId.userName"
            @focus="scopeFilter"
          >
          </el-input>

          <!-- 选择人员和部门 -->
          <div v-if="isDropboxShow" class="dropbox-wrap">
            <v-dropbox :type="1" @staff="getStaff"></v-dropbox>
          </div>
        </el-form-item>
        <el-form-item :label="$t('workOrder.serviceTag')" prop="service">
          <el-select
            v-model="form.service"
            allow-create
            class="input-width"
            filterable
            :loading="loading"
            multiple
            :placeholder="$t('workOrder.chooseOrAdd')"
            value-key="id"
          >
            <el-option v-for="item in tagLists" :key="item.id" :label="item.value" :value="item">
            </el-option>
          </el-select>
          <el-tooltip :content="$t('workOrder.canAddService')" effect="light" placement="right">
            <span class="el-icon-question icon"></span>
          </el-tooltip>
        </el-form-item>
        <el-form-item :label="$t('workOrder.serviceRangeS')" prop="address">
          <!-- 地址 -->
          <div class="address-wrapper">
            <input-address
              v-model="form.address"
              :field-info="addressInfo"
              :form-data="form"
              :is-see="false"
              mode="base"
            />
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">{{ $t('operation.cancel') }} </el-button>
        <el-button :disabled="isDisabled" type="primary" @click="handleSubmit"
          >{{ $t('operation.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  editWorkOrderPerson,
  workOrderPersonGetServiceAjax,
  saveWorkOrderPerson
} from '@/api/workOrder-person.js'
import vDropbox from '@/components/common/dropbox'
import InputAddress from '@/components/all-fields/form-data-edit/InputAddress/InputAddress'
import { Message } from 'element-ui'
import i18n from '@/lang'

// 验证地址信息
function checkAddress(rule, value, callback) {
  console.log(value)
  if (typeof value !== 'object') {
    callback(new Error(i18n.t('workOrder.inputAddress')))
  } else if (!value.province) {
    callback(new Error(i18n.t('workOrder.completeAddress')))
  } else {
    callback()
  }
}

// 服务标签验证规则
function serviceRule(rule, value, callback) {
  if (!(value instanceof Array) || !value.length) {
    callback(new Error(i18n.t('workOrder.chooseOrAdd')))
  } else {
    let empty = false
    let max = false
    value.forEach((item) => {
      if (typeof item === 'string') {
        if (item.trim() === '') {
          empty = true
        } else if (item.trim().length > 5) {
          max = true
        }
      }
    })
    if (empty) {
      callback(new Error(i18n.t('workOrder.cannotEmpty')))
    } else if (max) {
      callback(new Error(i18n.t('workOrder.wordsFive')))
    } else {
      callback()
    }
  }
}

export default {
  name: 'AddStaff',

  components: {
    VDropbox: vDropbox,
    InputAddress
  },

  props: {
    id: {
      type: Number,
      required: true
    }
  },

  data() {
    return {
      dialogVisible: false,
      showForm: false,
      addressInfo: {
        editable: 1,
        showDetailAddress: 1
      },
      form: {
        userId: '',
        service: [],
        address: {
          city: '', // 沈阳市
          address: '', // 辽宁省沈阳市大东区错么
          district: '', // 大东区
          province: '' // 辽宁省
        }
      },
      rules: {},
      loading: true,
      tagLists: [], // 服务标签
      isDropboxShow: false,
      dropboxPostion: {}, // Dropbox弹窗的位置
      isDisabled: false // 是否禁用提交按钮
    }
  },

  computed: {
    title() {
      return this.id ? this.$t('operation.edit') : this.$t('operation.add')
    }
  },

  mounted() {
    // 编辑
    if (this.id) {
      this.getServiceStaff()
    } else {
      this.showForm = true

      // 初始化验证规则
      this.setRules()
    }

    this.dialogVisible = true

    // 获取服务标签列表
    this.getServiceList()
  },

  methods: {
    // 编辑服务人员信息
    getServiceStaff() {
      const params = {
        id: this.id
      }
      editWorkOrderPerson(params)
        .then((data) => {
          this.form = data.result
          this.showForm = true
          // 初始化验证规则
          this.setRules()
        })
        .catch(() => {})
    },

    // 初始化验证
    setRules() {
      this.rules = {
        userId: {
          required: true,
          type: 'object',
          message: this.$t('placeholder.choosePls', { attr: this.$t('nouns.servicePersonal') }),
          trigger: 'change'
        },
        service: { required: true, validator: serviceRule, trigger: 'change' },
        address: { validator: checkAddress, required: true, trigger: 'change' }
      }
    },

    // 清除筛选项
    clearFilter() {
      this.$set(this.form, 'userId', '')
    },

    // 筛选服务人员
    scopeFilter(e) {
      const position = e.target.getBoundingClientRect()

      this.dropboxPostion = {
        left: `${position.left}px`,
        top: `${position.top + 40}px`
      }

      this.isDropboxShow = true
    },

    // 监听员工选择
    getStaff(id, name) {
      this.$set(this.form, 'userId', {
        userId: id,
        userName: name
      })
      this.isDropboxShow = false
    },

    // 搜索服务标签
    remoteMethod(key) {
      if (key !== '') {
        setTimeout(() => {
          this.getServiceList(key)
        }, 300)
      }
    },

    // 获取服务标签
    getServiceList(key) {
      this.loading = true
      const params = {
        type: 'list',
        nameLike: key,
        page: 1,
        pageSize: 50
      }
      workOrderPersonGetServiceAjax(params)
        .then((data) => {
          this.tagLists = data.result.serviceList
          this.loading = false
        })
        .catch(() => {})
    },

    // 提交前验证
    handleSubmit() {
      // 验证条件设置
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submit()
        } else {
          Message({
            type: 'error',
            message: this.$t('workOrder.reSubmit')
          })
          return false
        }
      })
    },

    // 提交表单
    submit() {
      this.isDisabled = true

      // 格式化新增标签
      const service = this.form.service.map((item) => {
        if (typeof item === 'object') {
          return item
        } else {
          return {
            id: 0,
            value: item
          }
        }
      })

      const params = {
        id: this.id,
        serviceHumanId: this.form.userId,
        service: service,
        address: this.form.address || {
          address: '西兴街道江陵路3栋',
          city: '杭州市',
          district: '滨江区',
          location: { lon: 120.21602, lat: 30.21253 },
          province: this.$t('workOrder.ZheJiang')
        }
      }
      saveWorkOrderPerson(params)
        .then((data) => {
          Message({
            type: 'success',
            message: data.msg || this.$t('message.saveSuccess')
          })

          this.isDisabled = false
          this.$emit('refresh')
          this.closeDialog()
        })
        .catch(() => {
          this.isDisabled = false
        })
    },

    // 窗口关闭前触发的事件
    closeDialog() {
      this.$emit('update:showAddNew', false)
    },

    closeDropbox() {
      // 判断是否存在级联选择框，不存在或者存在时display为none时为真
      if (
        !document.querySelector('.el-cascader-menus') ||
        document.querySelector('.el-cascader-menus').style.display
      ) {
        this.isDropboxShow = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-staff {
  .address-wrapper {
    height: 110px;
  }
  .input-width {
    width: 360px;
  }
  .dropbox-wrap {
    position: absolute;
    top: 45px;
    left: 0;
    z-index: 501;
    width: 360px;
    height: 450px;
  }
  .icon {
    margin-left: 8px;
    font-size: 16px;
    color: $text-auxiliary;
    cursor: pointer;
  }
  .tips {
    font-size: 13px;
    line-height: 1;
    color: $text-auxiliary;
  }
}
</style>
