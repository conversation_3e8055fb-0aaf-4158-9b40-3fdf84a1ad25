<!--
 * @Description: 表单模板设置的左侧列表
 -->
<!--
组件关联页面：表单模板设置页面的左侧列表
-->
<template>
  <div>
    <slot name="addNewForm"></slot>
    <!-- <ul class="left-list">
      <li
        class="left-list_item"
        :class="{'left-list_item-active': item.menuId === activeMenuId}"
        v-for="(item, index) in list"
        :key="index"
        @click="selectItem(item)"
        >
        {{item.menuName}}
      </li>
    </ul> -->
    <div class="left-list">
      <div class="menu">
        <el-menu
          ref="menu"
          :default-active="defaultActive"
          unique-opened
          @close="menuClose"
          @open="menuOpen"
        >
          <template v-for="value in list">
            <el-submenu
              v-if="value.subMenuList && value.subMenuList.length > 0"
              :key="value.menuId"
              background-color="#fff000"
              :index="value.menuId + ''"
            >
              <template slot="title">
                <span>{{ value.menuName }}</span>
                <template>
                  <i
                    v-if="Number(currentOpenMenuId) !== value.menuId"
                    class="el-icon-caret-right el-submenu__icon-more"
                  ></i>
                  <i v-else class="el-icon-caret-bottom el-submenu__icon-more"></i>
                </template>
              </template>

              <!-- 渲染子 -->
              <template v-for="sub in value.subMenuList">
                <el-menu-item
                  :key="sub.menuId"
                  background-color="#fff000"
                  :class="{ 'left-list_item-active': sub.menuId == activeMenuId }"
                  :index="sub.menuId + ''"
                  :title="sub.menuName"
                  @click="selectItem(sub)"
                >
                  <template>
                    <span slot="title">{{ sub.menuName }}</span>
                  </template>
                </el-menu-item>
              </template>
            </el-submenu>

            <!-- 渲染路由级别 -->
            <el-menu-item
              v-else
              :key="value.menuId"
              :class="{ 'left-list_item-active': value.menuId == activeMenuId }"
              :index="value.menuId + ''"
              :title="value.menuName"
              @click="selectItem(value)"
            >
              <template>
                <span slot="title">{{ value.menuName }}</span>
              </template>
            </el-menu-item>
          </template>
          <!-- <ElMenuTree :menuList="list" /> -->
        </el-menu>
      </div>
    </div>
  </div>
</template>

<script>
import ElMenuTree from '@/components/layout/app-layout/aside-menu.vue'

export default {
  name: 'LeftList',
  components: {
    ElMenuTree
  },

  props: {
    // 菜单列表
    list: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      defaultActive: '0',
      activeMenuId: 0, // 当前选中的菜单item
      currentOpenMenuId: '', // 当前展开的一级菜单
      appName: this.$route.query.appName
    }
  },

  watch: {
    list: {
      handler(newName, oldName) {
        if (!oldName || oldName.length === 0) {
          return this.initChoose()
        }
        // 判断list是不是JXC的数据
        const oldIsJxc = oldName.some((e) => {
          return e.subMenuList !== undefined
        })
        const newIsJxc = newName.some((e) => {
          return e.subMenuList !== undefined
        })
        const init = oldIsJxc !== newIsJxc
        // // 如果list还是空则不比较
        if (!oldName || oldName.length === 0 || init) {
          return this.initChoose()
        }
        // 比较list被删除的值

        for (let i = 0; i < oldName.length; i++) {
          // 一级目录
          if (!newName[i]) {
            this.initChoose()
          } else {
            // 二级目录
            if (oldName[i].subMenuList) {
              for (let x = 0; x < oldName[i].subMenuList.length; x++) {
                if (!newName[i].subMenuList[x]) {
                  this.initChoose(oldName[i].subMenuList[x])
                }
              }
            }
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始进入或者刷新初始化选择
    // obj:被删除的item
    initChoose(obj) {
      // 是否在url中存在menuId，存在就继续选中这个菜单
      if (this.$route.query.menuId) {
        // 可能被删除之后menuId匹配不到，这时候需要重新选中第一个
        // 扁平化list
        const flatList = []
        if (this.list.length !== 0) {
          this.list.forEach((e) => {
            if (e.subMenuList) {
              e.subMenuList.forEach((l) => {
                flatList.push(l)
              })
            }
            flatList.push(e)
          })
          const some = flatList.some((item) => {
            return item.menuId === Number(this.$route.query.menuId) && this.selectItem(item)
          })
          if (!some) {
            // 如果obj为空则直接去第一个列表页
            if (obj === undefined) {
              this.list.length && this.list[0].subMenuList !== undefined
                ? this.selectItem(this.list[0].subMenuList[0])
                : this.selectItem(this.list[0])
              return
            }
            // 否则选择当前二级目录的第一个页面
            flatList.some((item) => {
              return item.parentId === Number(obj.parentId) && this.selectItem(item)
            })
            // (this.list.length && this.list[0].subMenuList !== undefined ? this.selectItem(this.list[0].subMenuList[0]) : this.selectItem(this.list[0]))
          }
        }
      } else {
        // 不存在的时候选中第一个
        this.list.length && this.list[0].subMenuList !== undefined
          ? this.selectItem(this.list[0].subMenuList[0])
          : this.selectItem(this.list[0])
      }
    },
    // 选中目标菜单
    selectItem(item) {
      if (item === undefined) return
      this.currentOpenMenuId = item.parentId
      this.activeMenuId = item.menuId
      // 选中的id放到url中，刷新后还会停留这个页面
      const query = { ...this.$route.query, menuId: this.activeMenuId }
      this.$router.push({ query })
      this.defaultActive = item.id + ''
      this.$emit('chooseItem', item)
      return true
    },
    menuOpen(index, menu) {
      this.$emit('subOpen', +index)
      if (Array.isArray(menu)) {
        this.currentOpenMenuId = menu[0]
      }
    },
    menuClose() {
      this.currentOpenMenuId = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.left-list {
  width: auto;
  overflow: hidden;
  .el-submenu {
    :deep(.el-submenu__icon-arrow) {
      display: none;
    }
    :deep(.el-submenu__title) {
      display: flex;
      justify-content: space-between;
      height: 50px;
      padding: 0 10px 0 15px !important;
      line-height: 50px;
      color: $text-plain;
      background-color: $base-white;
      &:hover {
        background-color: $neutral-color-1;
      }
    }
    :deep(.el-submenu__icon-more) {
      line-height: 50px;
      color: $neutral-color-3;
    }
  }
  .el-submenu {
    .el-menu {
      border: 0;
      &-item {
        height: auto !important;
        padding: 12px 13px 12px 20px !important;
        line-height: 18px !important;
        color: $neutral-color-9;
        white-space: pre-wrap !important;
        background-color: $neutral-color-1;
        :deep(.el-menu-item__txt) {
          width: 117px;
          word-wrap: break-word;
        }
        :deep(.el-menu-item.is-active) {
          :deep(.el-menu-item__txt) {
            font-weight: 600;
            color: $bg-menu;
          }
        }
      }
      &-item.left-list_item-active {
        background-color: $neutral-color-1;
      }
    }
  }
  .el-menu {
    border: 0;
    :deep(.el-menu-item.left-list_item-active) {
      background: $neutral-color-1;
      border-left: 2px solid $brand-color-5;
    }
    :deep(.el-menu-item) {
      height: auto !important;
      padding: 12px 13px 12px 20px !important;
      line-height: 18px !important;
      color: $neutral-color-9;
      white-space: pre-wrap !important;
      background-color: $base-white;
      :deep(.el-menu__txt) {
        width: 117px;
        word-wrap: break-word;
      }
      :deep(.el-menu.is-active) {
        span {
          font-weight: 700;
        }
      }
    }
  }
}
</style>
