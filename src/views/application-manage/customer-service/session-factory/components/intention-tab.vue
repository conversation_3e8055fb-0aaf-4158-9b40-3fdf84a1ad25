<template>
  <div class="intention-tab">
    <div class="intention-tab__operate">
      <el-button type="primary" @click="newIT">新建</el-button>
    </div>
    <div class="intention-tab__list">
      <v-table border :data="tableData" style="width: 100%">
        <el-table-column label="意图名称" prop="name"> </el-table-column>
        <el-table-column label="意图描述" prop="description"> </el-table-column>
        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.addTime | showTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button size="small" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </v-table>

      <el-pagination
        :current-page.sync="page"
        layout="total, sizes, prev, pager, next"
        :page-size="pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="rowsCount"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>

    <intention-dialog
      v-if="newIntentionVisible"
      :intent-data="intentData"
      :visible.sync="newIntentionVisible"
      @addIntent="addIntent"
    ></intention-dialog>
  </div>
</template>

<script>
import VTable from '@/components/v-table'
import IntentionDialog from './intention-dialog.vue'

import { getIntentList, deleteIntent, getIntent } from '@/api/work-order-v2/im'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'IntentionTab',

  components: {
    VTable,
    IntentionDialog
  },

  filters: {
    showTime(val) {
      return xbb.timestampToTimeString(val, 'yyyy-MM-dd HH:mm')
    }
  },

  mixins: [],
  props: {},

  data() {
    return {
      page: 1,
      pageSize: 20,
      rowsCount: 0,
      tableData: [],
      intentData: {},
      newIntentionVisible: false
    }
  },

  computed: {},

  watch: {},

  mounted() {
    this.listInit()
  },

  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      this.page = 1
      this.listInit()
    },
    handleCurrentChange(val) {
      this.page = val
      this.listInit()
    },
    listInit() {
      this.tableData = []
      const params = {
        page: this.page,
        pageSize: this.pageSize
      }
      getIntentList(params)
        .then((res) => {
          this.tableData = res.result.intentList
        })
        .catch(() => {})
    },
    addIntent() {
      this.listInit()
    },
    newIT() {
      console.log('新建')
      this.intentData = {}
      this.newIntentionVisible = true
    },
    handleEdit(row) {
      const id = row.id
      getIntent({ id })
        .then((res) => {
          this.intentData = res.result
          this.newIntentionVisible = true
        })
        .catch(() => {})
    },
    handleDelete(row) {
      deleteIntent({ id: row.id })
        .then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.listInit()
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.intention-tab {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: calc(100% - 40px);
  height: 100%;
  padding: 0 20px;
  .intention-tab__operate {
    display: flex;
    flex: 0 0 32px;
    justify-content: end;
  }
  .intention-tab__list {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 10px;
    overflow: hidden;
    :deep(.el-table) {
      flex: 1;
    }
    :deep(.el-pagination) {
      display: flex;
      justify-content: end;
    }
  }
}
</style>
