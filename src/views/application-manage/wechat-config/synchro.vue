<!--
 * @Description: 企微同步设置（老版本的，为了兼容，目前只让白名单公司过）
-->
<template>
  <div class="synchro">
    <v-layout>
      <span slot="layout-title">{{ $t('router.wechatConfig.syncSettings') }}</span>
      <div slot="layout-column" class="content">
        <!-- 未绑定自建应用 -->
        <template v-if="!hasBindApp">
          <div class="no-bind">
            <img src="@/assets/empty.png" />
            <span>{{ $t('wechatConfig.noBindTips') }}</span>
          </div>
        </template>
        <!-- 绑定了自建应用且有互通账号 -->
        <template v-else-if="defaultVisible">
          <div class="content__synchro-wx">
            <div class="show-text">{{ $t('wechatConfig.syncP1') }}</div>
            <el-checkbox v-model="isCheck" :disabled="syncIsOpen">{{
              $t('wechatConfig.syncP2')
            }}</el-checkbox>
          </div>
          <div class="content__synchro-wx">
            <div class="show-text">{{ $t('wechatConfig.syncObject') }}</div>
            <template v-for="item in configList">
              <el-radio
                :key="item.value"
                v-model="syncCode"
                :disabled="syncIsOpen"
                :label="item.value"
                >{{ item.text }}</el-radio
              >
            </template>
          </div>
          <div class="content__synchro-user">
            <!-- <div class="show-text">{{ $t('wechatConfig.syncP1') }}</div> -->
            <el-checkbox v-model="syncTobeCoUser" :disabled="!syncTobeCoUserFlag">{{
              $t('wechatConfig.syncP7')
            }}</el-checkbox>
          </div>
          <p class="content__synchro-tips">{{ $t('wechatConfig.syncP8') }}</p>
          <p class="content__synchro-tips">{{ $t('wechatConfig.syncP3') }}</p>
          <div class="content-main">
            <el-button class="save" type="primary" @click="onSave">{{
              $t('operation.save')
            }}</el-button>
          </div>
        </template>
        <!-- 绑定了自建应用但是没有互通账号 -->
        <template v-else-if="!defaultVisible">
          <div class="no-account">
            <h3>同步企业微信好友设置：</h3>
            <p class="tip">开启后，企业微信中拥有互通账号的员工添加的外部客户将自动同步至CRM</p>
            <p class="warning">该企业暂无同步权限</p>
            <h3>开启同步权限条件：</h3>
            <p class="tip">
              <span>企业成员拥有互通账号</span> &nbsp;&nbsp;&nbsp;&nbsp;
              <a :href="ENUM_GUIDE_LICENSE.ACCOUNT" target="blank">了解更多</a>
            </p>
          </div>
        </template>
      </div>
    </v-layout>
  </div>
</template>

<script>
import { customerSyncConfigSet, customerSyncConfigGet, getCompanyAgent } from '@/api/wx-config.js'
import VLayout from '@/components/layout-page/v-layout'
import { getAccountUsage } from '@/api/scrm/license/index.js'
import { ENUM_GUIDE_LICENSE } from '@/views/scrm/constants/enum.license.js'
import { mapGetters } from 'vuex'

export default {
  name: 'WxSynchro',
  components: {
    VLayout
  },
  data() {
    return {
      ENUM_GUIDE_LICENSE,
      configList: [],
      syncCode: 0,
      syncIsOpen: false,
      isCheck: false,
      hasBindApp: false,
      hasAccount: false, // 企微判断企业有无互通账号
      syncTobeCoUser: false, // 三方协同人选项
      syncTobeCoUserFlag: false // 是否允许编辑
    }
  },
  computed: {
    ...mapGetters(['getUserInfo']),

    defaultVisible() {
      return this.getUserInfo.isWhite || this.hasAccount
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getCompanyAgent().then((res) => {
        this.hasBindApp = res.result.isSet
      })
      customerSyncConfigGet().then(({ result }) => {
        const { configList, syncIsOpen, syncCode, syncTobeCoUser, syncTobeCoUserFlag } = result
        this.configList = configList
        this.syncIsOpen = this.isCheck = syncIsOpen
        this.syncCode = syncCode || 0
        this.syncTobeCoUser = syncTobeCoUser
        this.syncTobeCoUserFlag = syncTobeCoUserFlag
      })
      getAccountUsage().then((res) => {
        if (res.result.externalContactUsage) this.hasAccount = true
      })
    },
    onSave() {
      if (!this.isCheck || !this.syncCode) {
        const msg = !this.isCheck
          ? this.$t('wechatConfig.turnSync')
          : this.$t('wechatConfig.syncObject')
        this.$message.error(this.$t('placeholder.choosePls', { attr: msg }))
        return
      }
      if (!this.syncIsOpen) {
        this.$confirm(this.$t('wechatConfig.syncP6'), this.$t('wechatConfig.syncConfirm'), {
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        })
          .then(() => {
            this.setConfig()
          })
          .catch((_) => {})
      } else {
        this.setConfig()
      }
    },
    // 同步设置
    setConfig() {
      console.log('this.syncTobeCoUser:', this.syncTobeCoUser)
      customerSyncConfigSet({
        syncCode: this.syncCode,
        syncIsOpen: this.isCheck,
        syncTobeCoUser: this.syncTobeCoUser
      })
        .then((_) => {
          this.isCheck = this.syncIsOpen = true
          this.syncTobeCoUserFlag = false
          this.$message({
            type: 'success',
            message: this.$t('message.saveSuccess')
          })
        })
        .catch((_) => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.synchro {
  height: 100%;
  font-family: SourceHanSansSC-regular;
  .content {
    box-sizing: border-box;
    width: 100%;
    padding: 20px;
    .no-bind {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 80%;
      span {
        margin-top: 20px;
        font-size: 16px;
        color: $text-auxiliary;
      }
    }
    .no-account {
      font-size: 14px;
      h3 {
        font-weight: 600;
        line-height: 40px;
      }
      .tip {
        line-height: 20px;
        color: $text-plain;
      }
      .warning {
        margin-top: 30px;
        font-weight: 500;
        line-height: 20px;
        color: $error-base-color-6;
      }
    }
    &__synchro-wx,
    &__synchro-user {
      display: flex;
      align-items: center;

      .show-text {
        min-width: 120px;
        margin-right: 20px;
        font-size: 15px;
        text-align: right;
      }
    }
    &__synchro-wx {
      margin-bottom: 20px;
    }
    &__synchro-user {
      padding-left: 140px;
      margin-bottom: 5px;
    }
    &__synchro-tips {
      margin-bottom: 5px;
      margin-left: 165px;
      font-size: 12px;
      color: $text-grey;
    }
    .content-main {
      height: calc(100% - 90px);
      margin-top: 20px;
      font-size: 14px;
      &__title {
        padding-left: 10px;
        font-size: 15px;
        line-height: 24px;
        color: $text-auxiliary;
        border-left: 4px solid $brand-color-5;
      }
      p {
        margin: 20px 20px 20px 0;
        .copy {
          margin-left: 10px;
          font-size: 13px;
          color: #2693ef !important;
        }
      }
      .save {
        display: block;
        margin-left: 142px;
      }
    }
  }
}
</style>
