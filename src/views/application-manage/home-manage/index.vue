<!--
 * @Description:首页分配
 -->
<template>
  <div class="home-manage">
    <div class="home-manage__header">
      <h2 class="home-manage__header-title">
        {{ $t('homeManage.homeManage') }}
        <!-- <i class="icon-information-line t-iconfont" @click="switchMenu"></i> -->
        <change-version
          v-if="isShowVersionSwitch"
          :new-version="isNewVersion"
          @submit="switchVersion"
        ></change-version>
      </h2>

      <div v-if="boardSetPermission" class="home-manage__header-options">
        <el-button @click="$router.push('/board/intercalate')">{{
          $t('home.targetManage')
        }}</el-button>
        <!-- <i v-tooltip="$t('homeManage.targetTooltip')" class="icon-question-line t-iconfont"></i> -->
      </div>
    </div>
    <div class="home-manage__content">
      <p class="home-manage__tips">
        {{ $t('homeManage.explain') }}
      </p>
      <div class="home-manage__panel">
        <div class="home-manage__panel-search">
          <el-input
            v-model="keywords"
            clearable
            :placeholder="$t('placeholder.inputPls', { attr: $t('homeManage.homePageName') })"
            prefix-icon="el-icon-search"
          ></el-input>
        </div>
        <el-button class="home-manage__panel-btn" type="primary" @click="newPage">{{
          $t('operation.add')
        }}</el-button>
      </div>
      <el-table
        border
        class="home-manage__table"
        :data="filterTaleData"
        height="100%"
        row-class-name="home-manage__row"
        style="width: 100%"
      >
        <el-table-column :label="$t('homeManage.homePageName')" prop="name" width="200">
          <template slot-scope="scope">
            <span>
              {{ scope.row.name }}
            </span>
            <span
              v-tooltip.top.delay="$t('operation.edit')"
              class="edit icon-edit-line t-iconfont"
              @click="handleBtn(scope.row, 'name')"
            ></span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('layoutDesign.description')" min-width="240" prop="description">
          <template slot-scope="scope">
            <div
              v-tooltip.top="{
                content: scope.row.description,
                disabled: scope.row.description?.length < 36
              }"
              class="home-manage__row-text"
            >
              {{ scope.row.description || '--' }}
            </div>
            <span
              v-tooltip.top.delay="$t('operation.edit')"
              class="edit icon-edit-line t-iconfont"
              @click="handleBtn(scope.row, 'description')"
            ></span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('homeManage.applicationScope')" min-width="240" prop="rangeStr">
          <template slot-scope="scope">
            <div
              v-tooltip.top="{
                content: scope.row.rangeStr,
                disabled: scope.row.rangeStr?.length < 36
              }"
              class="home-manage__row-text"
            >
              {{ scope.row.rangeStr || '--' }}
            </div>
            <span
              v-tooltip.top.delay="$t('operation.edit')"
              class="edit icon-edit-line t-iconfont"
              @click="handleBtn(scope.row, 'rangeStr')"
            ></span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('homeManage.state')" width="70">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enable"
              active-color="#FF8C2E"
              :active-value="1"
              inactive-color="#f0f0f2"
              :inactive-value="0"
              @change="stateChange(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="$t('utils.creationTime')" prop="addTime" width="120">
          <template slot-scope="scope">
            {{ scope.row.addTime | formatTime }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('nouns.updateTime')" prop="updateTime" width="120">
          <template slot-scope="scope">
            {{ scope.row.updateTime | formatTime }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('homeManage.operation')" width="180">
          <template slot-scope="scope">
            <div style="font-size: 0">
              <el-button
                v-for="btn in optionList.slice(0, 2)"
                :key="btn.attr"
                class="mini-button"
                size="mini"
                @click="commonOption(btn.attr, scope.row)"
              >
                {{ btn.label }}
              </el-button>
              <el-dropdown
                v-if="optionList.length > 2"
                trigger="click"
                @command="(command) => commonOption(command, scope.row)"
              >
                <el-button class="mini-button" size="mini" style="margin-left: 8px">
                  <i class="icon-more-line t-iconfont"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    v-for="btn in optionList.slice(2)"
                    :key="btn.attr"
                    :command="btn.attr"
                  >
                    {{ btn.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <v-pagination
        v-if="pageHelper.rowsCount"
        class="home-manage__pagination"
        :current-page="pageHelper.currentPageNum"
        layout="slot, sizes, prev, pager, next, jumper"
        :page-size="20"
        :total="pageHelper.rowsCount"
        @current-change="pageChange"
        @size-change="handleSizeChange"
      >
      </v-pagination>

      <el-dialog
        v-if="dialogSwitcher.textStr"
        :close-on-click-modal="true"
        :close-on-press-escape="true"
        :title="textDialogTitle[dialogType]"
        :visible.sync="dialogSwitcher.textStr"
        width="30%"
      >
        <el-input
          v-model="homePageSetInfo"
          :maxlength="textInputArgs[dialogType].maxlength"
          :placeholder="`请输入${textDialogTitle[dialogType]}`"
          show-word-limit
          :type="textInputArgs[dialogType].type"
        ></el-input>
        <div slot="footer" class="dialog-footer">
          <el-button @click="textDialogCancel">取 消</el-button>
          <el-button type="primary" @click="textDialogSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import PersonSelect from '@/components/person-select/index'

import {
  homePageManageCopy,
  homePageManageDelete,
  homePageManageEnable,
  homePageManageGet,
  homePageManageList,
  homePageManageSaveSet
} from '@/api/homePageManage'
import xbb from '@xbb/xbb-utils'
import { MessageBox } from 'element-ui'
import { mapGetters } from 'vuex'
import ChangeVersion from './components/change-version'

export default {
  name: 'HomePageManage',
  components: {
    ChangeVersion
  },
  data() {
    return {
      activeTab: 'home',
      keywords: '',
      homePageData: [],
      pageHelper: {},
      paging: {
        page: 1,
        pageSize: 20
      },
      dialogSwitcher: {
        textStr: false
      },
      dialogType: '',
      homePageSetInfo: {},
      textDialogTitle: {
        name: this.$t('homeManage.homePageName'),
        description: this.$t('layoutDesign.description')
      },
      textInputArgs: {
        name: {
          maxlength: 20,
          type: 'text'
        },
        description: {
          maxlength: 200,
          type: 'textarea'
        }
      },
      currentTemplateInfo: {},
      isNewVersion: false, //新版首页
      isShowVersionSwitch: false,
      isBoss: false, // 是否为超管
      hasCustomPage: false // 是否有自定义首页的管理权限
    }
  },
  mounted() {
    this.getHomePageManageList()
  },
  computed: {
    ...mapGetters(['feeType']),
    // 是否显示指标设置
    boardSetPermission() {
      return this.isBoss && this.hasCustomPage
    },
    filterTaleData() {
      if (this.keywords.trim()) {
        return this.homePageData.filter((item) => item.name.includes(this.keywords))
      } else {
        return this.homePageData
      }
    },
    optionList() {
      const list = [
        {
          label: this.$t('homeManage.edit'),
          attr: 'edit',
          type: 'old'
        },
        {
          label: '网页端',
          attr: 'web',
          type: 'new'
        },
        {
          label: '移动端',
          attr: 'mobile',
          type: 'new'
        },
        {
          label: this.$t('homeManage.copy'),
          attr: 'copy'
        },
        {
          label: this.$t('homeManage.delete'),
          attr: 'delete'
        }
      ]
      const filterType = this.isNewVersion ? 'old' : 'new'
      return list.filter((item) => item.type !== filterType)
    }
  },

  filters: {
    formatTime(stamp) {
      if (!stamp) return ''
      return xbb.timestampToTimeString(stamp, 'yyyy-MM-dd HH:mm')
    }
  },
  methods: {
    // 切换新旧首页
    switchVersion() {
      this.getHomePageManageList()
    },
    /**
     * @description: 设置首页配置 保存
     */
    async setHomePageConfig(data) {
      const { code } = await homePageManageSaveSet(data)
      if (code === 1) {
        this.$message({
          type: 'success',
          message: this.$t('message.setSuccess')
        })
        this.getHomePageManageList()
      } else {
        this.$message({
          type: 'error',
          message: this.$t('message.setFail')
        })
      }
    },

    /**
     * @description: 文本框弹窗取消关闭
     */
    textDialogCancel() {
      this.dialogSwitcher.textStr = false
    },

    /**
     * @description: 文本框弹窗提交
     */
    textDialogSubmit() {
      // 校验名称是否必填
      if (this.dialogType === 'name' && !this.homePageSetInfo) {
        this.$message.error('首页名称不能为空')
        return
      }

      this.dialogSwitcher.textStr = false

      const payload = {
        [this.dialogType]: this.homePageSetInfo
      }

      this.setHomePageConfig({
        ...this.currentTemplateInfo,
        ...payload
      })
    },

    /**
     * @description: 适用范围提交
     */
    rangeStrSubmit(info) {
      const payload = {
        range: info
      }

      this.setHomePageConfig({
        ...this.currentTemplateInfo,
        ...payload
      })
    },

    /**
     * @description: 按钮点击事件
     */
    handleBtn(templateInfo, type) {
      this.currentTemplateInfo = templateInfo
      this[`${type}SetHandler`](templateInfo)
    },

    /**
     * @description: 名称设置
     */
    nameSetHandler(homePageInfo) {
      this.homePageSetInfo = homePageInfo.name
      this.dialogSwitcher.textStr = true
      this.dialogType = 'name'
    },

    /**
     * @description: 描述设置
     */
    descriptionSetHandler(homePageInfo) {
      this.homePageSetInfo = homePageInfo.description
      this.dialogSwitcher.textStr = true
      this.dialogType = 'description'
    },

    /**
     * @description: 适用范围设置
     */
    rangeStrSetHandler(homePageInfo) {
      // console.log('=====>', homePageInfo)
      // this.dialogSwitcher.rangeStr = true
      this.homePageSetInfo = homePageInfo.range
      PersonSelect({
        title: '适用范围',
        defaultVal: this.homePageSetInfo,
        showTabsName: ['dept', 'role'],
        otherParams: {
          // 是否控制组织架构权限范围
          deptVisibleRule: true,
          parentStrictly: true
        }
      }).then((res) => {
        if (res.data) {
          // 模板启用时，适用范围不得置空
          if (homePageInfo.enable && !res.data.length) {
            this.$message.error('当前模板已启用，适用范围不得为空')
            return
          }
          this.rangeStrSubmit(res.data)
        }
      })
    },

    getHomePageManageList() {
      const data = {
        page: this.paging.page,
        pageSize: this.paging.pageSize
      }
      homePageManageList(data).then((res) => {
        const { list, openUiPaas, pageHelper, uiPaas, isBoss, hasCustomPage } = res.result
        this.homePageData = list
        this.isShowVersionSwitch = openUiPaas
        this.isNewVersion = uiPaas
        this.pageHelper = pageHelper
        this.isBoss = isBoss
        this.hasCustomPage = hasCustomPage
      })
    },
    newPage() {
      if (this.pageHelper.rowsCount >= 50) {
        this.$message({
          type: 'warning',
          message: this.$t('homeManage.limitTip')
        })
        return
      }
      // ui-paas 走新配置
      if (this.isNewVersion) {
        this.$router.push({ path: '/ui-paas-index', query: {} })
        return
      }
      this.$router.push({ name: 'setHomePage', query: { isEdit: 0 } })
    },
    commonOption(attr, row) {
      switch (attr) {
        case 'edit':
        case 'web':
        case 'mobile':
          this.editPage(row, attr)
          break
        case 'copy':
          this.copyPage(row)
          break
        case 'delete':
          this.deletePage(row)
          break
        default:
          break
      }
    },
    // 编辑
    editPage(params, plateForm) {
      // ui-paas 走新配置
      if (this.isNewVersion) {
        this.$router.push({ path: '/ui-paas-index', query: { plateForm, layoutId: params.id } })
        return
      }
      homePageManageGet({ id: params.id }).then((res) => {
        const homeParams = {
          id: res.result.id,
          name: res.result.name,
          description: res.result.description,
          range: res.result.range,
          refIds: res.result.refId
        }
        this.$router.push({
          name: 'setHomePage',
          query: { homeParams: JSON.stringify(homeParams), isEdit: 1 }
        })
      })
    },
    // 复制
    copyPage({ id }) {
      // ui-paas 走新配置
      if (this.isNewVersion) {
        this.$router.push({ path: '/ui-paas-index', query: { layoutId: id } })
        return
      }
      homePageManageCopy({ id }).then(() => {
        this.getHomePageManageList()
      })
    },
    deletePage(params) {
      if (params.enable) {
        this.$message({
          type: 'warning',
          message: this.$t('homeManage.operateTip', {
            attr: this.$t('homeManage.delete')
          })
        })
        return
      }
      MessageBox.confirm(this.$t('homeManage.deletePageTip'), this.$t('message.delRemind'), {
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      }).then(() => {
        const data = {
          id: params.id
        }
        homePageManageDelete(data).then(() => {
          this.getHomePageManageList()
        })
      })
    },
    stateChange(row) {
      const { enable, id, range = [] } = row
      // 适用范围为空时，不允许启用
      if (!range.length) {
        this.$message.error('适用范围为空时，不允许启用')
        row.enable = 0
        return
      }
      const data = {
        id,
        enable
      }
      homePageManageEnable(data).catch(() => {
        row.enable = 0
      })
    },
    pageChange(val) {
      this.paging.page = val
      this.getHomePageManageList()
    },
    handleSizeChange(val) {
      this.paging.pageSize = val
      this.getHomePageManageList()
    }
  }
}
</script>

<style lang="scss">
.home-manage {
  .el-table::before {
    bottom: 0;
    left: 0;
    height: 0px;
  }
}
</style>

<style lang="scss" scoped>
.home-manage {
  height: 100%;
  &__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 50px;
    padding: 0 20px;
    background-color: $base-white;
    &-title {
      font-size: 16px;
      line-height: 50px;
    }
    &-options {
      font-size: 14px;
      line-height: 50px;
      .t-iconfont {
        color: $text-grey;
      }
    }
  }
  &__content {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: calc(100% - 65px);
    padding: 20px;
    margin: 10px;
    background: $base-white;
  }
  &__tips {
    flex: 1 1 auto;
    padding-left: 16px;
    margin-bottom: 12px;
    font-size: 12px;
    line-height: 32px;
    color: $text-plain;
    background-color: $bg-table;
  }
  &__panel {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 32px;
    margin-bottom: 12px;
    &-search {
      flex: 0 0 280px;
    }
    &-btn {
      flex: 0 0 56px;
    }
  }
  &__table {
    flex: 1 1 auto;
    overflow-x: hidden;
    overflow-y: scroll;
    .web-icon-field-update-date {
      margin-left: 6px;
      color: $tag-orange;
      cursor: pointer;
    }
  }
  &__row {
    &-text {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .edit {
      position: absolute;
      top: 50%;
      right: 0;
      display: none;
      padding: 0 6px;
      color: $text-auxiliary;
      cursor: pointer;
      transform: translateY(-50%);
      &:hover {
        color: $brand-color-5;
      }
    }
    &:hover {
      .edit {
        display: inline-block;
      }
    }
    .mini-button {
      padding: 5px 8px;
      font-size: 12px;
      border-radius: 4px;
      & + .tiny-button {
        margin: 0 8px;
      }
    }
  }
  &__pagination {
    box-sizing: border-box;
    display: flex;
    flex-direction: row-reverse;
    height: 32px;
    margin-top: 10px;
  }
}
</style>
