<template>
  <div class="wrap">
    <div class="header">{{ $t('workOrderV2.sparePartSet.headTitle') }}</div>
    <div class="content">
      <div class="directory">
        <div>
          <span class="isOpenTitleText">{{ $t('workOrderV2.sparePartSet.OpenSparePart') }}</span>
          <el-switch
            v-model="isOpen"
            :active-value="1"
            :inactive-value="0"
            @change="changeSparePartsSwitch"
          >
          </el-switch>
        </div>
        <div class="describeText">
          {{ $t('workOrderV2.sparePartSet.OpenSparePartTip') }}
          <br />
          {{ $t('workOrderV2.sparePartSet.OpenSparePartTip2') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { setPoolMainStatus } from '@/api/work-order-v2/work-order-pool-set.js'
import { getSparePartSetting } from '@/api/work-order-v2/spare-parts-set.js'

export default {
  name: 'SparePartsSettings',
  data() {
    return {
      isOpen: 0
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getSparePartSet()
    },
    getSparePartSet() {
      getSparePartSetting({
        alias: 'sparePartsSettings'
      }).then((res) => {
        this.isOpen = +res.result.settingValue
      })
    },
    getConfirmInfo(status) {
      return {
        tips: status
          ? this.$t('workOrderV2.sparePartSet.openSparePart')
          : this.$t('workOrderV2.sparePartSet.closeSparePart'),
        message: status
          ? this.$t('workOrderV2.sparePartSet.confirmOpen')
          : this.$t('workOrderV2.sparePartSet.confirmClose')
      }
    },
    changeSparePartsSwitch(status) {
      const confirmInfo = this.getConfirmInfo(status)
      this.$confirm(confirmInfo.message, confirmInfo.tips, {
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(() => {
          // 这里跟工单池启用开关用同一个接口
          setPoolMainStatus({
            alias: 'sparePartsSettings',
            value: this.isOpen
          })
            .then(() => {
              this.$message({
                type: 'success',
                message: this.$t('workOrderV2.operateSuccess')
              })
            })
            .catch(() => {
              this.isOpen = status ? 0 : 1
            })
        })
        .catch(() => {
          this.isOpen = status ? 0 : 1
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .header {
    padding: 16px 20px;
    font-size: 16px;
    background: $base-white;
  }
  .content {
    width: calc(100% - 20px);
    height: 90%;
    padding: 10px;
    font-size: 16px;
    .directory {
      height: 100%;
      padding: 21px;
      background: $base-white;
      .isOpenTitleText {
        margin-right: 10px;
      }
      .describeText {
        margin-top: 20px;
        font-size: 12px;
        line-height: 17px;
        color: $text-auxiliary;
      }
    }
  }
}
</style>
