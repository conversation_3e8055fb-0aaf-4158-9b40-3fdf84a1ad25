<template>
  <div class="contract-template-library">
    <header class="header">
      <span>合同模板库</span>
      <div class="header__operation-list">
        <el-button type="primary" @click="handleCreateContractTemplate">{{
          $t('operation.add')
        }}</el-button>
      </div>
    </header>

    <section class="content">
      <div class="filter">
        <div class="conditions-item__wrapper">
          <div v-clickoutside="closeDropbox" class="conditions-item__content">
            <span class="label">创建人：</span>
            <div class="body">
              <el-input
                class="input-width"
                clearable
                readonly
                :value="filterCreatorUser.userName"
                @focus="isDropboxShow = true"
              >
              </el-input>
              <i
                v-if="filterCreatorUser.userId"
                class="el-icon-circle-close range-select__close"
                @click="clearSelect"
              />
              <div v-if="isDropboxShow" class="dropbox-wrap">
                <v-dropbox :type="1" @staff="getStaff"></v-dropbox>
              </div>
            </div>
          </div>
        </div>
        <div class="conditions-item__wrapper">
          <div class="conditions-item__content">
            <span class="label">模板名称：</span>
            <div class="body">
              <el-input
                v-model="filterForm.templateName"
                clearable
                placeholder="请输入模板名称"
                @change="getDataList"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="conditions-item__wrapper">
          <div class="conditions-item__content">
            <span class="label">附件名称：</span>
            <div class="body">
              <el-input
                v-model="filterForm.fileName"
                clearable
                placeholder="请输入附件名称"
                @change="getDataList"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="conditions-item__wrapper">
          <div class="conditions-item__content">
            <span class="label">适用表单：</span>
            <div class="body">
              <el-select
                v-model="filterForm.formId"
                clearable
                filterable
                placeholder="请选择适用表单"
                @change="handleFormChange"
              >
                <el-option
                  v-for="(item, index) in applicativeFormList"
                  :key="index"
                  :label="item.formName"
                  :value="item.formId"
                />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <el-table v-loading="loading" border :data="contractTemplateList" height="100%">
        <el-table-column label="模板名称" prop="templateName" />
        <el-table-column label="模板附件">
          <template slot-scope="scope">
            <a @click="handlePreviewFile(scope.row)">{{ scope.row.fileName }}</a>
          </template>
        </el-table-column>
        <el-table-column label="文件格式" prop="fileExt" />
        <el-table-column :formatter="fileSizeFormatter" label="文件大小" prop="fileSize" />
        <el-table-column :formatter="timeStringFormatter" label="上传时间" prop="fileUploadTime" />
        <el-table-column label="查看权限" prop="viewPermissionStr" />
        <el-table-column label="适用表单">
          <template #default="{ row }">
            <span>{{ row.appName }}.{{ row.formName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="creatorUserNmae" />
        <el-table-column label="启用">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enable"
              :active-value="1"
              :inactive-value="0"
              @change="handleToggleContractTemplateEnable($event, scope.row.id, scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleEditContractTemplate(scope.row)">{{
              $t('operation.edit')
            }}</el-button>
            <el-button type="text" @click="handleDeleteContractTemplate(scope.row.id)">{{
              $t('operation.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </section>

    <ContractTemplateEditDialog
      v-if="contractTemplateEditDialogVisible"
      ref="contractTemplateEditDialog"
      :form-data="currentContractTemplateData"
      :mode="contractTemplateEditDialogMode"
      :visible.sync="contractTemplateEditDialogVisible"
      @submit="handleContractTemplateEditDialogSubmit"
    />
    <lg-preview v-if="previewVisible" :list="fileList" @close="previewVisible = false" />
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import { formatUnit } from '@/utils/file-list'
import ContractTemplateEditDialog from './components/contract-template-edit-dialog'
import vDropbox from '@/components/common/dropbox'
import {
  managementCenterGetContractTemplateList,
  getApplicativeFormList,
  saveContractTemplate,
  deleteContractTemplate,
  toggleContractTemplateEnable
} from '@/api/office-online-edit'

export default {
  name: 'ContractTemplateLibrary',

  components: {
    VDropbox: vDropbox,
    ContractTemplateEditDialog,
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },

  mixins: [],

  props: {},

  data() {
    return {
      // 搜索条件
      filterForm: {
        templateName: '',
        fileName: '',
        createUserId: '',
        formId: '',
        businessType: ''
      },
      filterCreatorUser: { userId: '', userName: '' },

      loading: false,
      previewVisible: false,
      isDropboxShow: false,
      applicativeFormList: [],
      fileList: [],
      currentContractTemplateData: {},
      contractTemplateEditDialogMode: 'create',
      contractTemplateEditDialogVisible: false,
      contractTemplateList: []
    }
  },

  computed: {},

  watch: {
    'filterForm.createUserId': {
      handler() {
        this.getDataList()
      }
    }
  },

  created() {
    this.getDataList()
    this.getApplicativeFormList()
  },

  beforeDestroy() {},

  methods: {
    async getDataList() {
      try {
        this.loading = true

        const params = { ...this.filterForm }
        const result = await managementCenterGetContractTemplateList(params)
        this.contractTemplateList = result.result.data
      } catch (error) {
      } finally {
        this.loading = false
      }
    },
    getApplicativeFormList() {
      getApplicativeFormList().then((result) => {
        this.applicativeFormList = result.result.list
      })
    },
    handleCreateContractTemplate() {
      this.contractTemplateEditDialogMode = 'create'
      this.contractTemplateEditDialogVisible = true
    },
    handleEditContractTemplate(contractTemplateData) {
      this.currentContractTemplateData = {
        id: contractTemplateData.id,
        templateName: contractTemplateData.templateName,
        viewPermission: contractTemplateData.viewPermission,
        fileList: [
          {
            filename: contractTemplateData.fileName,
            attachIndex: contractTemplateData.fileUrl,
            ext: contractTemplateData.fileExt,
            size: contractTemplateData.fileSize
          }
        ],
        applicativeForm: contractTemplateData.formId
          ? {
              formId: contractTemplateData.formId,
              businessType: contractTemplateData.businessType
            }
          : null
      }
      this.contractTemplateEditDialogMode = 'edit'
      this.contractTemplateEditDialogVisible = true
    },
    handleDeleteContractTemplate(templateId) {
      this.$confirm(
        '删除后，新建合同时将无法选择该合同模板，是否继续？',
        this.$t('mixin.deleteConfirmed'),
        {
          confirmButtonText: this.$t('operation.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        }
      ).then(async () => {
        try {
          const params = {
            id: templateId
          }
          await deleteContractTemplate(params)
          this.$message.success(this.$t('message.delSuccess'))
          this.getDataList()
        } catch (error) {}
      })
    },
    async handleToggleContractTemplateEnable(enableValue, templateId, row) {
      try {
        const params = {
          enable: enableValue,
          id: templateId
        }
        await toggleContractTemplateEnable(params)
        this.$message.success(this.$t('message.operateSuccessSymbol'))
      } catch (error) {
        this.$message.error(this.$t('decisionTree.failMessage'))
      }
    },
    handleContractTemplateEditDialogSubmit(data) {
      const { id } = data
      const isUpdate = !!id

      const params = {
        templateName: data.templateName,
        viewPermission: data.viewPermission,
        fileName: data.fileList[0].filename,
        fileUrl: data.fileList[0].attachIndex,
        fileExt: data.fileList[0].ext,
        fileSize: data.fileList[0].size,
        formId: data.applicativeForm.formId,
        businessType: data.applicativeForm.businessType
      }
      if (isUpdate) {
        params.id = data.id
      }
      saveContractTemplate(params)
        .then((result) => {
          const successMessage = isUpdate
            ? this.$t('message.editorialSuccess')
            : this.$t('message.newSuccess')
          this.$message.success(successMessage)
          this.$refs.contractTemplateEditDialog.closeDialog()
          this.getDataList()
        })
        .finally(() => {
          this.$refs.contractTemplateEditDialog &&
            this.$refs.contractTemplateEditDialog.stopConfirmBtnLoading()
        })
    },
    handlePreviewFile(row) {
      this.previewVisible = true
      this.fileList = [
        {
          attachIndex: row.fileUrl,
          filename: row.fileName,
          ext: row.fileExt
        }
      ]
    },
    getStaff(id, name) {
      this.filterCreatorUser = {
        userId: id,
        userName: name
      }
      this.isDropboxShow = false
      this.filterForm.createUserId = id
    },
    handleFormChange(value) {
      const selectedApplicativeForm = value
        ? this.applicativeFormList.find((item) => item.formId === value)
        : null

      this.filterForm.businessType = selectedApplicativeForm
        ? selectedApplicativeForm.businessType
        : ''
      this.filterForm.formId = selectedApplicativeForm ? selectedApplicativeForm.formId : ''

      this.getDataList()
    },
    closeDropbox() {
      this.isDropboxShow = false
    },
    clearSelect() {
      this.filterCreatorUser = { userId: '', userName: '' }
      this.filterForm.createUserId = ''
    },
    timeStringFormatter(row, column, cellValue, index) {
      return xbb.formatDate(cellValue, 'datetime')
    },
    fileSizeFormatter(row, column, cellValue, index) {
      return formatUnit(cellValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.contract-template-library {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding-right: 20px;
  font-size: 16px;
  line-height: 50px;
  text-indent: 20px;
  background: $base-white;
}

.content {
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  padding: 20px;
  margin: 10px 0;
  background: $base-white;
}

.filter {
  margin-bottom: 16px;
}

.conditions-item__wrapper {
  display: inline-block;
  padding: 10px 0 0 15px;
}

.conditions-item__content {
  display: flex;
  align-items: center;
  .body {
    position: relative;
    min-width: 220px;
  }
}

.dropbox-wrap {
  position: absolute;
  top: 45px;
  left: 0;
  z-index: 501;
  width: 360px;
  height: 450px;
}

.range-select__close {
  position: absolute;
  top: 6px;
  right: 4px;
  font-size: 19px;
  color: $text-grey;
  cursor: pointer;
}
</style>
