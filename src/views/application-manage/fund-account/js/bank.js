/*
 * @Description: 银行枚举
 */
import i18n from '@/lang'

const BANK_LIST = [
  i18n.t('bank.peopleBankOfChina'),
  i18n.t('bank.bankOfCommunications'),
  i18n.t('bank.industrialAndCommercialBankOfChina'),
  i18n.t('bank.chinaConstructionBank'),
  i18n.t('bank.agriculturalBankOfChina'),
  i18n.t('bank.bankOfChina'),
  i18n.t('bank.postalSavingsBankOfChina'),
  i18n.t('bank.chinaMerchantsBank'),
  i18n.t('bank.shanghaiPudongDevelopmentBank'),
  i18n.t('bank.chinaCiticBank'),
  i18n.t('bank.chinaEverbrightBank'),
  i18n.t('bank.chinaGuangfaBank'),
  i18n.t('bank.pingAnBank'),
  i18n.t('bank.huaXiaBank'),
  i18n.t('bank.chinaMinshengBank'),
  i18n.t('bank.industrialBank'),
  i18n.t('bank.hengFengBank'),
  i18n.t('bank.chinaZheshangBank'),
  i18n.t('bank.chinaBohaiBank'),
  i18n.t('bank.otherBank')
]
export default BANK_LIST
