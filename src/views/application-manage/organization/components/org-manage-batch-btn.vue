<template>
  <div class="batch-btn">
    <div
      v-for="btn in batchShowBtns"
      :key="btn.type"
      class="batch-btn__btn show-batch"
      @click="handleBatchBtn(btn.type)"
    >
      {{ btn.name }}
    </div>
    <div v-if="batchMoreBtns.length" class="batch-btn__btn show-batchmore">
      <el-dropdown ref="batch-dropdown" placement="top" trigger="click">
        <span class="el-dropdown-link">
          <i class="icon-more-line t-iconfont"></i>
        </span>
        <el-dropdown-menu slot="dropdown" style="overflow: unset">
          <template v-for="moreBtn in batchMoreBtns">
            <el-dropdown-item :key="moreBtn.type" @click.native="handleBatchBtn(moreBtn.type)">{{
              moreBtn.name
            }}</el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrgManageBatchBtn',

  props: {
    batchShowBtns: {
      type: Array,
      default: () => []
    },
    batchMoreBtns: {
      type: Array,
      default: () => []
    }
  },

  methods: {
    handleBatchBtn(type) {
      this.$emit('handleBatchBtn', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.batch-btn {
  display: flex;
  .batch-btn__btn {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $text-plain;
    cursor: pointer;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
  }
  .show-batch {
    height: 28px;
    padding: 0 11px;
    margin-right: 12px;
  }
  .show-batchmore {
    width: 40px;
    height: 28px;
  }
  .show-batchmore :deep(.el-dropdown) {
    width: 100%;
    height: 100%;
  }
  .show-batchmore :deep(.el-dropdown .el-dropdown-link) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .batch-btn__btn:hover {
    color: $brand-color-5;
    background-color: #fff4ea;
    border-color: #ffddc0;
  }
}
</style>
