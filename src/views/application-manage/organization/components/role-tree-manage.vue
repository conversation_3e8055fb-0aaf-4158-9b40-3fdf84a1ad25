<!-- 角色树 -->
<template>
  <div class="role-tree-manage">
    <div class="role-tree-manage__filter">
      <el-input
        v-model="filterValue"
        clearable
        :placeholder="$t('placeholder.searchRoleName', { attr: '' })"
        prefix-icon="el-icon-search"
        @input="nodeFilterChangeHandler"
      />
      <fee-filter
        :fee-list="feeList"
        :is-multi-fee="isMultiFee"
        :selected-fee.sync="licenseList"
        @change="handleChangeFeeFilter"
      ></fee-filter>
    </div>
    <div v-if="showSelectFee.length" class="role-tree-manage__desc">
      {{ showDesc }}
    </div>
    <el-scrollbar
      ref="scroll-wrap"
      class="role-tree-manage__scrollbar"
      :class="{ 'has-fee-desc': showSelectFee.length }"
    >
      <el-tree
        ref="tree"
        :allow-drag="allowDragJudge"
        :allow-drop="allowDropJudge"
        check-on-click-node
        class="role-tree-manage__content"
        :current-node-key="currentSelectedNodeId"
        :data="localRoleList"
        default-expand-all
        :draggable="!isInputMode && !readOnly && isSubAdmin !== 1"
        :expand-on-click-node="true"
        :filter-node-method="filterRoleNode"
        highlight-current
        node-key="nodeId"
        :props="defaultProps"
        @node-click="handleRoleSelect"
        @node-drag-end="nodeDragEndHandler"
        @node-drag-enter="nodeDragEnterHandler"
        @node-drag-start="nodeDragStartHandler"
        @node-drop="nodeDropHandler"
      >
        <span
          slot-scope="{ data: nodeData, node }"
          class="role-tree-manage__item"
          :class="{
            'is-current': nodeData.nodeId === currentSelectedNodeId,
            'is-input': showNodeInput(nodeData) && !readOnly
          }"
        >
          <i
            v-if="nodeData.type === 1"
            class="item-group-icon web-icon-field-users web-iconfont"
          ></i>
          <template v-if="showNodeInput(nodeData) && !readOnly">
            <!-- 添加、输入场景 -->
            <el-input
              ref="nodeInputRef"
              v-model="tempEditNode.name"
              class="item-temp"
              clearable
              :maxlength="maxLength"
              show-word-limit
              size="mini"
              @blur="() => handleNodeInputBlur(nodeData)"
              @keydown.native.enter.prevent="handleNodeInputEnter"
              @keydown.native.esc.stop.prevent="handleNodeInputEsc"
            />
          </template>
          <template v-else>
            <div class="item-text">
              <div class="item-name">{{ nodeData.roleName }}</div>
              <div class="item-extra">{{ getRoleNameExtra(nodeData) }}</div>
            </div>
            <el-dropdown
              v-if="!readOnly && isSubAdmin !== 1"
              class="item-dropdown"
              placement="bottom"
              trigger="hover"
              @command="(curCommand) => handleDropdown(curCommand, nodeData, node)"
            >
              <i class="el-icon-setting item-dropdown__btn"></i>
              <el-dropdown-menu class="item-dropdown__menu">
                <el-dropdown-item
                  v-for="nodeOperate in getNodeOperateList(nodeData)"
                  :key="nodeOperate.key"
                  class="item-dropdown__option"
                  :command="nodeOperate.key"
                  >{{ nodeOperate.label }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
            <i v-else-if="nodeData.inPosition" class="el-icon-check item-enable"></i>
          </template>
        </span>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import { roleSort, roleCopy, roleDelete, sysRecovery, roleAdd, roleEditName } from '@/api/system'

import FeeFilter from '@/components/person/components/fee-filter.vue'

export default {
  name: 'RoleTreeManage',

  components: {
    FeeFilter
  },

  props: {
    // 角色列表
    roleList: {
      type: Array,
      default: () => []
    },
    // 选择的节点nodeId: catalogId_id 拼接起来的id，用于唯一确定节点
    selectedNodeId: {
      type: String,
      default: null
    },
    // 切换节点操作前的检查
    beforeSwitchCheckMethod: {
      type: Function
    },
    // 标识是否使用新建、编辑等操作
    readOnly: {
      type: Boolean,
      default: false
    },
    isMultiFee: {
      type: Boolean,
      default: false
    },
    feeList: {
      type: Array,
      default: () => []
    },
    isSubAdmin: {
      type: Number,
      default: 0
    }
  },

  model: {
    event: 'change',
    prop: 'selectedNodeId'
  },

  data() {
    return {
      licenseList: [],
      filterValue: '', // 列表查询
      localRoleList: [], // 角色树
      tempEditNode: {
        nodeId: undefined, // 对应节点的唯一标识
        name: '', // 角色/分组名
        roleId: undefined, // 角色id
        type: 0, // 1 分组，0 角色
        parentId: undefined, // 角色所属分组id
        catalogId: undefined // 分组id
      }, // 新建、编辑时的临时节点内容
      isInputMode: false, // 是否是节点树输入模式
      sortParams: {
        type: undefined, // 0:角色 1:分组
        roleId: undefined, // 移动的是角色才有
        oldParentId: undefined, //父级Id ("0"表示与一级目录平级)
        newParentId: undefined, //父级Id ("0"表示与一级目录平级)
        catalogId: undefined, // 移动的是目录才有
        oldSortIndex: undefined, // 从1开始，老的位置
        newSortIndex: undefined // 从1开始，新的位置
      },
      drogTargetNode: null // 拖拽的目标节点
    }
  },

  computed: {
    maxLength() {
      // 分组 10 角色 20
      return this.tempEditNode.type === 1 ? 10 : 20
    },
    currentSelectedNodeId: {
      get() {
        return this.selectedNodeId || null
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    defaultProps() {
      return {
        children: 'nodes',
        label: 'roleName'
      }
    },
    cumputedSortParams() {
      const baseParams = {
        type: this.sortParams.type,
        oldParentId: this.sortParams.oldParentId,
        newParentId: this.sortParams.newParentId,
        oldSortIndex: this.sortParams.oldSortIndex,
        newSortIndex: this.sortParams.newSortIndex
      }
      const typeParams =
        this.sortParams.type === 1
          ? {
              catalogId: this.sortParams.catalogId
            }
          : {
              roleId: this.sortParams.roleId
            }
      return {
        ...baseParams,
        ...typeParams
      }
    },
    showSelectFee() {
      return this.feeList
        .filter((item) => {
          return this.licenseList.includes(item.feeType)
        })
        .map((item) => item.feeName)
    },
    showDesc() {
      const prefix = this.showSelectFee.length === 1 ? '' : '同时'
      const name = this.showSelectFee.join('、')
      return `以下为${prefix}授权了“${name}”账户的角色`
    }
  },

  watch: {
    // 更新列表和默认选中
    roleList: {
      deep: true,
      handler(val, oldVal) {
        if (xbb._isEqual(val, this.localRoleList)) {
          return
        }

        this.localRoleList = xbb.deepClone(val)
        if (this.localRoleList?.length) {
          // 当树发生变化时，重新设置下当前节点，确保回显正确
          this.$nextTick(() => {
            this.setCurrentKey(this.selectedNodeId)
          })
        }
      }
    }
  },

  mounted() {
    this.localRoleList = xbb.deepClone(this.roleList)
  },

  methods: {
    handleChangeFeeFilter(val) {
      this.filterValue = ''
      this.$emit('handleChangeLicense', { value: val, callback: this.nodeFilterChangeHandler })
    },
    /**
     * @description: 用于获取角色树特定key的节点数据，给外部使用
     * @param {string} key
     * @return {object}
     */
    getNodeData(key) {
      const targetNode = this.$refs.tree?.getNode(key)
      return targetNode?.data
    },
    /**
     * @description: 设置el-tree选中的节点
     * @param {string|number} key nodeId
     * @return {*}
     */
    setCurrentKey(key) {
      this.$refs.tree?.setCurrentKey(key)
    },

    /**
     * @description: 获取节点回显名
     * @param {object} nodeData 节点内容
     * @return {string} 节点回显名
     */
    getRoleName(nodeData) {
      if (!nodeData) return ''

      const baseName = nodeData.roleName
      const extraName = this.getRoleNameExtra(nodeData)
      return `${baseName}[${extraName}]`
    },
    getRoleNameExtra(nodeData) {
      let extraName = ''
      // 系统字段
      if (nodeData.corpid === '0' && nodeData.type !== 1) {
        extraName = nodeData.modified
          ? `${this.$t('system.systemModify')}`
          : `${this.$t('system.system')}`
      }
      return extraName
    },
    /**
     * @description: 获取节点操作列表
     * @param {object} nodeData 节点内容
     * @return {object[]} 节点选项列表
     */
    getNodeOperateList(nodeData) {
      const allRoleOperateList = [
        {
          key: 'edit-role',
          label: this.$t('organization.roleManage.editName'),
          show: ![1, 2].includes(nodeData.id) && nodeData.corpid !== '0'
        }, // 超管，老板不让编辑
        { key: 'copy-role', label: this.$t('operation.copy'), show: true }, // 所有角色都有
        {
          key: 'restore-role',
          label: this.$t('organization.roleManage.roleButtons.restore'),
          show: nodeData.modified
        },
        { key: 'delete-role', label: this.$t('operation.delete'), show: nodeData.corpid !== '0' }
      ]
      const allGroupOperateList = [
        { key: 'add-role', label: this.$t('organization.roleManage.addRoleLabel'), show: true },
        { key: 'edit-group', label: this.$t('organization.roleManage.editName'), show: true },
        { key: 'delete-group', label: this.$t('operation.delete'), show: true }
      ]
      const operateMap = {
        0: allRoleOperateList,
        1: allGroupOperateList
      }
      const nodeType = nodeData.type || 0

      return (operateMap[nodeType] || []).filter((item) => item.show)
    },

    /** 筛选 strat */
    @xbb.debounceWrap(200)
    nodeFilterChangeHandler(val) {
      this.$refs.tree.filter(val)
    },
    /**
     * @description: filter调用
     * @param {string} value 筛选值
     * @param {object} data 节点数据
     * @return {boolean} true 满足条件
     */
    filterRoleNode(value, data) {
      const roleName = this.getRoleName(data)
      return !value || roleName.indexOf(value) !== -1
    },
    /** 筛选 end */

    /** 拖拽相关 start */
    resetSortParams() {
      this.sortParams = {
        type: undefined, // 0:角色 1:分组
        roleId: undefined, // 移动的是角色才有
        oldParentId: undefined, //父级Id ("0"表示与一级目录平级)
        newParentId: undefined, //父级Id ("0"表示与一级目录平级)
        catalogId: undefined, // 移动的是目录才有
        oldSortIndex: undefined, // 从1开始，老的位置
        newSortIndex: undefined // 从1开始，新的位置
      }
    },
    /**
     * @description: 判断节点能否被拖拽
     * @param {object} draggingNode 拖拽的节点
     * @return {*}
     */
    allowDragJudge(draggingNode) {
      // 输入模式时不允许拖拽
      return !this.isInputMode
    },
    /**
     * @description: 判断节点能否被拖入
     * @param {*} draggingNode
     * @param {*} dropNode
     * @param {*} type
     * @return {*}
     */
    allowDropJudge(draggingNode, dropNode, type) {
      // 拖拽的是分组，只能在顶层同级拖拽
      if (draggingNode.data.type === 1) {
        return dropNode.level === 1 && type !== 'inner'
      } else {
        // 拖拽的是角色，不允许拖拽到角色下级
        return (
          dropNode.data.type === 1 ||
          draggingNode.data.nodeId === dropNode.data.nodeId ||
          type !== 'inner'
        )
      }
    },
    /**
     * @description: 拖拽开始
     * @param {*} draggingNode
     * @param {*} ev
     * @return {*}
     */
    nodeDragStartHandler(draggingNode, ev) {
      console.log('start drag', draggingNode, ev)

      this.beforeSwitchCheck(() => {
        // 因为el-tree在拖拽时，被拖拽的节点拿不到parent数据，只能用下方这种相对写死的逻辑
        let oldParentId = '0'
        let oldSortIndex
        // 顶层
        if (draggingNode.level === 1) {
          oldSortIndex =
            this.localRoleList.findIndex(
              (nodeData) => nodeData.nodeId === draggingNode.data.nodeId
            ) + 1
          oldParentId = '0'
        } else {
          const parentCatalogId = draggingNode.data.nodeId.split('_')[0]
          const oldParent = this.localRoleList.find(
            (nodeData) => nodeData.catalogId === parentCatalogId
          )
          oldParentId = oldParent.catalogId
          oldSortIndex =
            oldParent.nodes.findIndex((nodeData) => nodeData.nodeId === draggingNode.data.nodeId) +
            1
        }
        this.sortParams = {
          ...this.sortParams,
          type: draggingNode.data.type || 0,
          catalogId: draggingNode.data.catalogId,
          roleId: draggingNode.data.id,
          oldParentId,
          oldSortIndex
        }

        // 下方控制拖拽开始时相关样式
        // ev.currentTarget.classList.add('is-draggable')
        ev.dataTransfer.setDragImage(ev.currentTarget.childNodes[0], 0, 0)
        // 展开的分组要收起
        if (
          draggingNode.data.type === 1 &&
          draggingNode.data.nodes?.length &&
          draggingNode.expanded
        ) {
          // 收起分组后，后续样式会有问题，要处理下
          draggingNode.collapse()
          // if (!ev.currentTarget.classList.contains('is-draggable')) {
          //   ev.currentTarget.classList.add('is-draggable')
          //   ev.dataTransfer.setDragImage(ev.currentTarget.childNodes[0], 0, 0)
          // }
        }
      })
    },
    /**
     * @description: 拖拽成功
     * @param {*} draggingNode
     * @param {*} dropNode
     * @param {*} dropType
     * @param {*} ev
     * @return {*}
     */
    nodeDropHandler(draggingNode, dropNode, dropType, ev) {
      let newParentId = '0'
      let newSortIndex
      // 拖拽到分组内
      if (dropType === 'inner') {
        const newParent = dropNode.data
        newParentId = newParent.catalogId
        newSortIndex =
          newParent.nodes.findIndex((nodeData) => draggingNode.data.nodeId === nodeData.nodeId) + 1
        // 拖拽到顶层边
      } else if (dropNode.level === 1) {
        newParentId = '0'
        newSortIndex =
          dropNode.parent.data.findIndex(
            (nodeData) => nodeData.nodeId === draggingNode.data.nodeId
          ) + 1
      } else {
        // 拖拽到非顶层节点边
        const newParent = dropNode.parent.data
        newParentId = newParent.catalogId
        newSortIndex =
          newParent.nodes.findIndex((nodeData) => draggingNode.data.nodeId === nodeData.nodeId) + 1
      }
      this.sortParams = {
        ...this.sortParams,
        newParentId,
        newSortIndex
      }
      console.log('drag end', draggingNode, dropNode, dropType, ev)

      roleSort(this.cumputedSortParams)
        .then((res) => {
          this.$message({
            message: '排序成功',
            type: 'success'
          })
          let oldNodeId, newNodeId
          // nodeId是后端根据节点的catalogId和id拼的，当调整所属分组时，是会变化的，因此需要在这里手动拼出目标节点
          if (this.sortParams.type === 1) {
            oldNodeId = `${this.sortParams.catalogId}_${this.sortParams.roleId}`
            newNodeId = `${this.sortParams.catalogId}_${this.sortParams.roleId}`
          } else {
            oldNodeId = `${this.sortParams.oldParentId}_${this.sortParams.roleId}`
            newNodeId = `${this.sortParams.newParentId}_${this.sortParams.roleId}`
          }
          if (oldNodeId === this.currentSelectedNodeId) {
            this.refreshList(newNodeId)
          } else {
            this.refreshList(this.currentSelectedNodeId)
          }
        })
        .catch((err) => {
          this.refreshList(this.currentSelectedNodeId)
        })
    },
    // 拖拽结束，处理一下临时数据
    nodeDragEndHandler(draggingNode, dropNode, dropType, ev) {
      this.drogTargetNode = null
      // ev.currentTarget.classList.remove('is-draggable')
    },
    nodeDragEnterHandler(draggingNode, dropNode, ev) {
      this.drogTargetNode = dropNode
      setTimeout(() => {
        if (
          this.drogTargetNode?.id === dropNode.id &&
          dropNode.data.type === 1 &&
          draggingNode.id !== dropNode.id
        ) {
          dropNode.expand()
        }
      }, 500)
    },

    /** 拖拽相关 end */

    /**
     * @description: 切换节点操作前的回调，用于检查外部相关数据情况
     * @param {Function} cb 执行完毕后的回调
     * @return {*}
     */
    beforeSwitchCheck(cb) {
      if (typeof this.beforeSwitchCheckMethod !== 'function') {
        cb()
      } else {
        this.beforeSwitchCheckMethod(cb)
      }
    },

    /**
     * @description: 选择节点
     * @param {object} nodeData 节点数据
     * @return {*}
     */
    handleRoleSelect(nodeData) {
      console.log('nodeSet', nodeData)
      // 分组节点不选中；输入框状态下不允许切换节点；
      if (nodeData.type === 1 || this.isInputMode) {
        // 手动恢复之前的树节点
        this.setCurrentKey(this.currentSelectedNodeId)
        return
      } else if (this.currentSelectedNodeId === nodeData.nodeId) {
        return
      }

      this.beforeSwitchCheck(() => {
        this.currentSelectedNodeId = nodeData.nodeId
        this.$emit('node-click', nodeData.nodeId)
      })
    },
    /**
     * @description: 节点操作分配
     * @param {string} command 节点操作key
     * @param {object} nodeData 节点数据对象
     * @param {object} node 操作的节点对象
     * @return {*}
     */
    handleDropdown(command, nodeData, node) {
      switch (command) {
        case 'delete-role':
        case 'delete-group':
          this.handleDelete(nodeData, node)
          break
        case 'copy-role':
          this.handleCopy(nodeData, node)
          break
        case 'restore-role':
          this.handleRestore(nodeData)
          break
        case 'edit-role':
        case 'edit-group':
          this.handleEdit(nodeData, node)
          break
        case 'add-role':
          this.handleAdd(0, nodeData, node)
          break
        default:
          break
      }
    },

    /** 复制 start */
    /**
     * @description: 复制节点
     * @param {object} nodeData 节点数据
     * @param {object} node 操作的节点对象
     * @return {*}
     */
    handleCopy(nodeData, node) {
      if (!nodeData || nodeData.type === 1) return

      const parentNodeData = node.parent.data
      const params = {
        copyRoleId: nodeData.id,
        parentId: parentNodeData.catalogId || '0'
      }
      this.beforeSwitchCheck(() => {
        this.$confirm(
          this.$t('organization.roleManage.copyConfirm', { role: nodeData.roleName }),
          this.$t('message.confirmTitle'),
          {
            confirmButtonText: this.$t(`operation.confirm`),
            cancelButtonText: this.$t(`operation.cancel`),
            type: 'warning'
          }
        )
          .then(() => {
            this.copyRole(params)
          })
          .catch(() => {})
      })
    },
    /**
     * @description: 请求接口复制角色
     * @param {object} params 复制参数 {copyRoleId, parentId}
     * @return {*}
     */
    copyRole(params) {
      roleCopy(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })

          const { roleId, catalogId } = res.result
          const firstId = catalogId || params.parentId || 0
          const secondId = roleId || 0
          const nodeId = `${firstId}_${secondId}`
          this.refreshList(nodeId)
        })
        .catch(() => {})
    },
    /** 复制 end */

    /** 删除 start */
    /**
     * @description: 删除角色
     * @param {object} deleteData 要删除的节点数据
     * @param {object} node 操作的节点对象
     * @return {*}
     */
    handleDelete(deleteData, node) {
      if (!deleteData) return

      const parentNodeData = node.parent.data
      const configMap = {
        0: {
          params: { roleId: deleteData.id, parentId: parentNodeData.catalogId || '0', type: 0 },
          msg: '确定删除角色？'
        },
        1: {
          params: { catalogId: deleteData.catalogId, type: 1 },
          msg: '确定删除已选择分组与分组下的角色？'
        }
      }
      const config = configMap[deleteData.type || 0]

      const deleteFunc = () => {
        this.$confirm(config.msg, this.$t('message.confirmTitle'), {
          confirmButtonText: this.$t(`operation.confirm`),
          cancelButtonText: this.$t(`operation.cancel`),
          type: 'warning'
        })
          .then(() => {
            this.deleteRole(config.params, deleteData)
          })
          .catch((err) => {
            console.log('delete', err)
          })
      }

      // 删除的不是当前选中的节点，需要先检查保存
      if (this.currentSelectedNodeId !== deleteData.nodeId) {
        this.beforeSwitchCheck(() => {
          deleteFunc()
        })
      } else {
        deleteFunc()
      }
    },
    /**
     * @description: 删除角色/分组
     * @param {object} params { roleId, parentId, catalogId }
     * @param {object} nodeData 删除的节点数据
     * @return {*}
     */
    deleteRole(params, nodeData) {
      roleDelete(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })

          const selectId =
            this.currentSelectedNodeId !== nodeData.nodeId ? this.currentSelectedNodeId : undefined

          this.refreshList(selectId)
        })
        .catch(() => {})
    },
    /** 删除 end */

    /** 重置 start */
    /**
     * @description: 角色还原
     * @param {object} nodeData 节点数据
     * @return {*}
     */
    handleRestore(nodeData) {
      if (!nodeData || nodeData.type === 1) return

      const restoreFunc = () => {
        this.$confirm(
          this.$t('organization.roleManage.restoreConfirm'),
          this.$t('organization.roleManage.restoreConfirmTitle'),
          {
            confirmButtonText: this.$t(`operation.confirm`),
            cancelButtonText: this.$t(`operation.cancel`),
            type: 'warning'
          }
        )
          .then(() => {
            this.restore({ roleId: nodeData.id }, nodeData.nodeId)
          })
          .catch(() => {})
      }

      // 当选中节点不是当前操作节点时，需要检查保存
      if (this.currentSelectedNodeId !== nodeData.nodeId) {
        this.beforeSwitchCheck(() => {
          restoreFunc()
        })
      } else {
        restoreFunc()
      }
    },
    /**
     * @description: 还原系统角色权限
     * @param {object} params { roleId }
     * @param {string} nodeId 用于定位具体节点
     * @return {*}
     */
    restore(params, nodeId) {
      sysRecovery(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.refreshList(nodeId)
        })
        .catch(() => {})
    },
    /** 重置 end */

    /** 新建编辑 start */
    /**
     * @description: 判断对应节点是哪种渲染模式
     * @param {object} nodeData 对应节点的数据
     * @return {boolean}
     */
    showNodeInput(nodeData) {
      const isTargetEditNode = !nodeData.nodeId || this.tempEditNode.nodeId === nodeData.nodeId
      return this.isInputMode && isTargetEditNode
    },
    /**
     * @description: 修改角色
     * @param {object} nodeData 操作的节点数据
     * @param {object} node 操作的节点
     * @return {*}
     */
    handleEdit(nodeData, node) {
      this.beforeSwitchCheck(() => {
        const nodeType = nodeData.type || 0
        const baseData = {
          nodeId: nodeData.nodeId,
          name: nodeData.roleName,
          type: nodeType
        }
        const typeData =
          nodeType === 1
            ? {
                catalogId: nodeData.catalogId
              }
            : {
                roleId: nodeData.id
              }
        this.tempEditNode = {
          ...baseData,
          ...typeData
        }
        this.isInputMode = true
        this.focusInput()
      })
    },
    /**
     * @description: 新建角色/分组
     * @param {*} addType 0 角色 1 分组
     * @param {*} parentNodeData 顶层新加节点，则为空
     * @return {*}
     */
    handleAdd(addType, parentNodeData, parentNode) {
      this.beforeSwitchCheck(() => {
        const addNodeFunc = () => {
          // 临时节点数据
          const tempNode = {
            roleName: '',
            type: addType
          }
          // 父节点的子节点列表
          let parentNodes
          if (!parentNodeData) {
            parentNodes = this.localRoleList
          } else {
            if (!parentNodeData.nodes) {
              this.$set(parentNodeData, 'nodes', [])
            }
            parentNodes = parentNodeData.nodes
          }
          parentNodes.push(tempNode)
          // 输入的节点内容，用于input操作和接口
          this.tempEditNode = {
            name: '',
            type: addType,
            parentId: (parentNodeData || {}).catalogId || '0' // '0'标识一级目录
          }
          this.isInputMode = true

          this.focusInput()
        }

        // 展开和聚焦
        if (parentNode && !parentNode.expanded) {
          parentNode.expand(() => {
            // 延迟后才能确保展开后正确聚焦
            this.$nextTick(() => {
              addNodeFunc()
            })
          })
        } else {
          addNodeFunc()
        }
      })
    },
    // 复原节点树和输入状态
    revetTree() {
      this.localRoleList = xbb.deepClone(this.roleList)
      this.resetInputInfo()
      this.$nextTick(() => {
        // 确保取消操作后，节点正确被高亮
        this.setCurrentKey(this.currentSelectedNodeId)
      })
    },
    // 取消输入
    handleNodeInputEsc() {
      this.revetTree()
    },
    // enter 输入
    handleNodeInputEnter() {
      this.$refs.nodeInputRef.blur()
    },
    // 节点输入操作结束后
    handleNodeInputBlur(nodeData) {
      console.log('blur', this.tempEditNode.name, nodeData.roleName)
      // 名字为空取消操作
      if (!this.tempEditNode.name || this.tempEditNode.name === nodeData.roleName) {
        this.revetTree()
      } else {
        // 走保存逻辑
        this.saveNode(this.tempEditNode)
          .then(({ roleId, catalogId }) => {
            let nodeId = this.tempEditNode.nodeId
            // 若操作的是分组，刷新不应该被选中，那就选中之前选中的那个节点
            if (this.tempEditNode.type === 1) {
              nodeId = this.currentSelectedNodeId
            } else if (!nodeId) {
              // 新建场景
              const firstId = catalogId || this.tempEditNode.parentId || 0
              const secondId = roleId || 0
              nodeId = `${firstId}_${secondId}`
            }

            this.refreshList(nodeId)
            this.resetInputInfo()
          })
          .catch(() => {
            this.focusInput()
          })
      }
    },
    resetInputInfo() {
      this.isInputMode = false
      this.tempEditNode = {
        nodeId: undefined,
        name: '',
        roleId: undefined,
        type: 0,
        parentId: undefined,
        catalogId: undefined
      }
    },
    saveNode(params = {}) {
      // 编辑保存
      const isEdit = !!params.nodeId
      const { nodeId, ...restParams } = params
      const saveApi = isEdit ? roleEditName : roleAdd
      return saveApi(restParams).then((res) => {
        this.$message({
          type: 'success',
          message: res.msg
        })
        const { roleId, catalogId } = res.result
        return { roleId, catalogId }
      })
    },
    focusInput() {
      this.$nextTick(() => {
        this.$refs.nodeInputRef.focus()
      })
    },
    /** 新建编辑 end */

    /**
     * @description: 刷新角色列表
     * @param {string} targetNodeId nodeData.nodeId
     * @return {*}
     */
    refreshList(targetNodeId) {
      this.resetInputInfo()
      this.$emit('refresh-list', targetNodeId)
      // this.currentSelectedNodeId = targetNodeId
    }
  }
}
</script>

<style lang="scss" scoped>
.role-tree-manage {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0 10px 10px 10px;
  .role-tree-manage__filter {
    display: flex;
    gap: 12px;
    height: 32px;
    margin-bottom: 7px;
    :deep(input) {
      font-size: 12px;
    }
  }
  .role-tree-manage__desc {
    padding-top: 9px;
    padding-bottom: 12px;
    font-size: 12px;
    line-height: 16px;
    color: $text-auxiliary;
  }
  .role-tree-manage__scrollbar {
    height: calc(100% - 39px);
    .role-tree-manage__content {
      :deep(.el-tree-node__content) {
        height: 38px;
        background-color: unset;
        &:focus-within {
          background: #fff6ee;
          .is-input .item-group-icon {
            color: $brand-color-5;
          }
        }
      }
      :deep(.el-tree-node.is-drop-inner .el-tree-node__content) {
        background-color: #fff6ee;
      }
      .role-tree-manage__item {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: space-between;
        width: 0; // 给个初始宽度，保证子元素宽度计算正确
        height: 32px;
        padding-right: 10px;
        font-size: 14px;
        line-height: 32px;
        .item-text {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: space-between;
          width: 0;
          color: $text-plain;
          vertical-align: bottom;
          .item-name {
            flex: 1;
            font-size: 14px;
            @include singleline-ellipsis;
          }
          .item-extra {
            flex: 0 0 auto;
            font-size: 12px;
            color: $text-auxiliary;
          }
        }
        .item-group-icon {
          display: inline-block;
          width: 16px;
          margin-right: 6px;
          font-size: 14px;
        }
        .item-dropdown {
          display: none;
          width: 14px;
          margin-left: 2px;
        }
        .item-enable {
          display: inline-block;
          width: 14px;
          margin-left: 2px;
          color: $brand-color-5;
        }
        .item-temp {
          flex: 1;
          width: 0;
          :deep(.el-input__inner) {
            height: 24px;
            font-size: 12px;
            line-height: 24px;
          }
        }
        &.is-current {
          .item-group-icon,
          .item-text {
            color: $brand-color-5;
          }
          .item-dropdown {
            // display: inline-block;
            color: $brand-color-5;
          }
        }
        &:hover {
          .item-dropdown {
            display: inline-block;
          }
        }
      }
    }
  }
  .has-fee-desc {
    height: calc(100% - 92px);
  }
}
</style>
