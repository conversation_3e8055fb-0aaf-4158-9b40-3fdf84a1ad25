<!--
   * @Description: 新建子管理组
   -->
<template>
  <div>
    <el-dialog
      class="sub-admin-dialog__dialog"
      fullscreen
      :modal="false"
      :show-close="false"
      :visible.sync="visibleAdd"
      width="100%"
    >
      <div slot="title" class="edit-dialog__header" style="display: flex">
        <div class="edit-dialog__header--title">
          <slot name="header">{{
            edit
              ? $t('operation.edit') + $t('organization.subManagementGroup')
              : $t('operation.add') + $t('organization.subManagementGroup')
          }}</slot>
        </div>
        <div class="edit-dialog__header--btns">
          <slot name="btns">
            <el-button @click="cancel">{{ $t('operation.cancel') }}</el-button>
            <el-button plain type="primary" @click="handleSave">{{
              $t('operation.save')
            }}</el-button>
          </slot>
        </div>
      </div>
      <!-- 主体内容 -->
      <div class="edit-dialog__content">
        <!-- 主体内容 -->
        <el-form
          ref="formDataForm"
          class="form-list"
          label-position="left"
          label-width="115px"
          :model="form"
          :rules="rules"
        >
          <el-form-item :label="$t('organization.subAdminForm.name')" prop="name">
            <el-input
              v-model="form.name"
              class="input--small"
              maxlength="20"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('organization.subAdminForm.member')" prop="user">
            <select-tags
              ref="userSelectTags"
              v-model="form.user"
              :btn-disabled="userDisable"
              class-name="user"
              :limit-num="100"
              multiple
              @handleAddClick="handleOpenPersonSelect('user')"
            ></select-tags>
          </el-form-item>
          <el-form-item :label="$t('organization.subAdminForm.department')" prop="manageDept">
            <select-tags
              ref="deptSelectTags"
              v-model="form.manageDept"
              class-name="dept"
              multiple
              @handleAddClick="handleOpenPersonSelect('dept')"
            ></select-tags>
          </el-form-item>
          <el-form-item
            class="no-required"
            :label="$t('organization.subAdminForm.assignableRole')"
            prop="manageRole"
          >
            <select-tags
              v-model="form.manageRole"
              class-name="role"
              multiple
              @handleAddClick="handleOpenPersonSelect('role')"
            ></select-tags>
          </el-form-item>
          <el-form-item class="no-required" prop="manageMenu">
            <span slot="label">
              {{ $t('organization.subAdminForm.form') }}
              <el-popover
                content="当前子管理组成员可编辑设计的表单"
                placement="top"
                popper-class="popper"
                trigger="hover"
                width="200"
              >
                <i
                  slot="reference"
                  class="icon-question-line organization-fee t-iconfont"
                  style="color: #c8c9cc"
                ></i>
              </el-popover>
            </span>

            <select-tags
              v-model="form.manageMenu"
              class-name="manageMenu"
              multiple
              :remote="false"
              :show-button="false"
            ></select-tags>
          </el-form-item>
        </el-form>
        <!-- <PersonSelect
          v-if="dialogShow"
          :active-tab-name="activeTabName"
          :default-val="defaultVal"
          :dialog-visible.sync="dialogShow"
          :show-tabs-name="showTabsName"
          @dialogSubmit="rangeSubmit"
        ></PersonSelect> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveSubAdmin, getSubAdmin } from '@/api/system'
import SelectTags from '@/components/selectTags.vue'
import PersonSelect from '@/components/person-select/index'

export default {
  components: {
    SelectTags
  },
  props: {
    edit: {
      type: Boolean,
      default: false
    },
    show: {
      type: Boolean,
      default: false
    },
    dataId: {
      type: Number,
      default: null
    }
  },

  data() {
    return {
      form: {
        name: '',
        user: [],
        manageDept: [],
        manageRole: [],
        manageMenu: []
      },
      rules: {
        name: [{ required: true, message: '请输入子管理员组名称', trigger: 'blur' }],
        user: [{ required: true, message: '请至少选择 1 名员工', trigger: 'blur' }],
        manageDept: [{ required: true, message: '请至少选择 1 个部门', trigger: 'blur' }]
      },
      defaultVal: [],
      dialogShow: false,
      activeTabName: '',
      showTabsName: [],
      tabsName: '' //记录打开的哪个tab
    }
  },
  computed: {
    visibleAdd: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    },
    userDisable: {
      get() {
        return this.form.user.length === 100
      }
    }
  },

  mounted() {
    if (this.edit) {
      // 编辑时需要等子组件getMembers执行完之后再获取表单详情
      setTimeout(() => {
        this.getDetail()
      }, 100)
    }
  },
  methods: {
    handleOpenPersonSelect(tabsName) {
      this.tabsName = tabsName
      let title = ''
      switch (tabsName) {
        case 'user':
          this.defaultVal = this.form.user
          title = '添加成员'
          break
        case 'dept':
          this.defaultVal = this.form.manageDept
          title = '添加部门'
          break
        case 'role':
          this.defaultVal = this.form.manageRole
          title = '添加角色'
          break
        default:
      }
      PersonSelect({
        title: title,
        activeTabName: tabsName,
        showTabsName: [tabsName],
        defaultVal: this.defaultVal,
        multiple: {
          dept: true
        },
        tabMultiple: false,
        limitNum: {
          user: 100
        },
        otherParams: {
          // 是否控制组织架构权限范围
          deptVisibleRule: true,
          parentStrictly: true
        }
      }).then((res) => {
        if (res.data) {
          this.rangeSubmit(res.data)
        }
      })

      this.dialogShow = true
    },
    cancel() {
      this.visibleAdd = false
      this.$refs.formDataForm.resetFields()
    },
    rangeSubmit(val) {
      switch (this.tabsName) {
        case 'user':
          this.$refs.userSelectTags.insertOption(val)
          this.form.user = val
          break
        case 'dept':
          this.$refs.deptSelectTags.insertOption(val)
          this.form.manageDept = val
          break
        case 'role':
          this.form.manageRole = val
          break
        default:
      }
    },
    handleSave() {
      this.$refs.formDataForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.form
          }
          if (this.edit) {
            params['dataId'] = this.dataId
          }
          saveSubAdmin(params).then((res) => {
            this.cancel()
            this.$emit('refreshList')
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getDetail() {
      getSubAdmin({ dataId: this.dataId }).then((res) => {
        this.$refs.deptSelectTags.insertOption(res.result.manageDept)
        this.$refs.userSelectTags.insertOption(res.result.user)
        this.form.name = res.result.name
        this.form.manageDept = res.result.manageDept
        this.form.user = res.result.user
        this.form.manageRole = res.result.manageRole
        this.form.manageMenu = res.result.manageMenu
      })
    }
  }
}
</script>

<style lang="scss">
.sub-admin-dialog__dialog {
  overflow: hidden;
  & > .el-dialog {
    width: 100%;
    height: 100%;
    margin-top: 0;
    margin-bottom: 0;
    & > .el-dialog__body {
      height: calc(100% - 60px) !important;
      padding: 0;
      background: $neutral-color-1;
      .position-tool {
        position: absolute;
        left: calc(50% - 600px - 170px);
        z-index: 999;
        box-sizing: border-box;
        width: 160px;
        padding: 60px 5px 50px 13px;
      }
    }
  }
  .edit-dialog__header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 0 20px;
    & > .edit-dialog__header--title {
      font-size: 22px;
      font-weight: 500;
      color: $text-main;
    }
  }
  // 中间内容
  .edit-dialog__content {
    .subform-header {
      line-height: 32px;
    }
    .input--small {
      width: 430px;
    }
    box-sizing: border-box;
    // height: 2000px;
    width: 1200px;
    min-height: 100%;
    padding: 50px 40px 100px;
    margin: 0px auto;
    background: $base-white;
  }
}
.no-required {
  .el-form-item__label {
    padding-left: 11px;
  }
}
.popper {
  box-sizing: content-box;
  padding: 7px 12px;
  margin-bottom: 4px !important;
  font-size: 12px;
  color: $base-white;
  text-align: center;
  background-color: $base-black;
  opacity: 0.7;
}
.el-popover[x-placement^='top'] .popper__arrow::after {
  border-top-color: $base-black;
}
.organization-fee {
  color: $text-grey;
}
.organization-fee:hover {
  color: $brand-base-color-6 !important;
  cursor: pointer;
}
</style>
