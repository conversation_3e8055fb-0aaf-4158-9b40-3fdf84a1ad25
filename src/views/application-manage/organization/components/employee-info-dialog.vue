<!--
 * @Author: haoh<PERSON>.dang
 * @Date: 2021-09-15 14:38:03
 * @LastEditors: haohao.dang
 * @LastEditTime: 2021-11-10 19:49:16
 * @Description:组织架构-员工信息
-->
<template>
  <div class="employee-info-dialog">
    <transition name="el-zoom-in-right">
      <el-dialog
        v-if="dialogShow"
        ref="dialogRef"
        class="form-data-detail__dialog"
        :close-on-click-modal="true"
        custom-class="form-data-detail__dialog__body"
        :modal-append-to-body="true"
        :show-close="false"
        :visible.sync="dialogShow"
        width="900px"
      >
        <template slot="title">
          <div class="employee-info-dialog__head">
            <div class="title">
              {{ dialogData.name + $t('unit.of') + $t('organization.employee.employeeInfoTitle') }}
            </div>
            <org-manage-row-btn
              class="btn"
              :row="dialogData"
              :row-more-btns="rowMoreBtns"
              :row-show-btns="rowShowBtns"
              @operateHandler="operateHandler"
            />
          </div>
        </template>
        <div class="title__close-btn" @click="dialogShow = false">
          <i class="el-icon-close"></i>
        </div>

        <div class="desc">基本信息</div>
        <div class="border"></div>
        <el-form label-position="left" label-width="80px">
          <el-form-item :label="$t('organization.employee.baseInfo')[0]">
            <span v-if="userData.avatar === '-'">{{ userData.avatar }}</span>
            <img v-else-if="!userData.avatar.includes('default.jpg')" :src="userData.avatar" />
            <avatar-img
              v-else-if="userData.avatar.includes('default.jpg')"
              :alt="userData.name"
              :size="30"
              :src="userData.avatar"
            />
          </el-form-item>
          <el-form-item :label="$t('organization.employee.baseInfo')[1]"
            ><span>{{ userData.name }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[2]"
            ><span>{{ userData.mobile }}</span></el-form-item
          >
          <el-form-item :label="$t('label.mainDepartment')"
            ><span>{{ userData.mainDepartmentName }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[3]"
            ><span>{{ userData.departmentConcat }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[4]"
            ><span>{{ userData.position }}</span></el-form-item
          >
        </el-form>

        <div class="desc">权限信息</div>
        <div class="border"></div>
        <el-form label-position="left" label-width="80px">
          <el-form-item v-if="isMultiFee" :label="$t('organization.authorizedAccount')"
            ><span>{{ showLicenseName(userData.licenseArray) }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[5]"
            ><span>{{ userData.roleConcat }}</span></el-form-item
          >
        </el-form>

        <div v-if="platform !== 1">
          <h3>其他信息</h3>
          <div class="border"></div>
          <el-form label-position="left" label-width="80px">
            <el-form-item v-if="platform !== 1" :label="$t('organization.employee.otherInfo')[0]"
              ><span>{{ userData.email }}</span></el-form-item
            >
            <el-form-item v-if="platform === 3" :label="$t('organization.employee.otherInfo')[1]">
              <span v-if="userData.QRCode === '-'">{{ userData.QRCode }}</span>
              <img v-else :src="userData.QRCode" />
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
    </transition>
  </div>
</template>

<script>
import { getDetial } from '@/api/system'
import xbb from '@xbb/xbb-utils'
import AvatarImg from '@/components/base/avatar-img.vue'
import OrgManageRowBtn from './org-manage-row-btn.vue'

export default {
  name: 'EmployeeInfoDialog',
  components: { AvatarImg, OrgManageRowBtn },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => ({})
    },
    feeList: {
      type: Array,
      default: () => []
    },
    isMultiFee: {
      type: Boolean,
      default: false
    },
    rowShowBtns: {
      type: Array,
      default: () => []
    },
    rowMoreBtns: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogShow: false,
      userId: '',
      platform: 1,
      userData: {
        avatar: '',
        name: '',
        mobile: '',
        mainDepartmentName: '',
        departmentConcat: '',
        position: '',
        roleConcat: '',
        email: '',
        QRCode: ''
      }
    }
  },
  watch: {
    // 控制sync 动画
    show: {
      immediate: true,
      handler(val) {
        // this.dialogShow = val
        this.$nextTick(() => {
          this.dialogShow = val
        })
      }
    },
    // 双向绑定控制动画
    dialogShow(val) {
      if (val) {
        console.log('show', new Date())
        this.$nextTick(() => {
          // this.detailLoading = this.$loading({target: '.form-data-detail__dialog__body'})
        })
        this.$emit('update:show', val)
      } else {
        setTimeout(() => {
          this.$emit('update:show', val)
        }, 400)
      }
    }
  },
  mounted() {
    this.getEmployeeInfo()
    this.platform = this.getShelfVersion()
    console.log('当前环境：', this.getShelfVersion())
  },
  methods: {
    // 获取员工信息
    async getEmployeeInfo() {
      const data = {
        userId: utils.LS.get('userId'),
        checkUserId: this.dialogData.userId,
        corpid: this.dialogData.corpid
      }
      const licnseFormat = (arr) => {
        const licnseObj = {}
        arr.forEach((item) => {
          // 这边后端给的是 [{ 38: 1 }, { 32: 1 }]，给不了{ 38: 1, 32: 1 }，前端这边自己转
          const data = Object.entries(item)
          const key = data[0][0]
          const value = data[0][1]
          licnseObj[key] = value
        })
        return licnseObj
      }
      console.log('data:', data)
      try {
        const res = await getDetial(data)
        const { user } = res.result
        this.userData.avatar = user.avatar
        this.userData.name = user.name
        this.userData.mobile = user.mobile
        this.userData.mainDepartmentName = user.mainDepartmentName
        this.userData.departmentConcat = user.departmentConcat
        this.userData.position = user.position
        this.userData.roleConcat = user.roleConcat
        this.userData.del = user.del
        this.userData.email = user.email
        this.userData.QRCode = user.QRCode
        this.userData.licenseArray = licnseFormat(user.licenseArray)
        console.log('员工信息：', data, user)
      } catch (err) {
        console.log(err)
      }
    },
    // 判断当前环境
    getShelfVersion() {
      if (xbb.isDevEnv()) {
        return 1 // 本地调试
      } else {
        const platList = ['dd', 'h5', 'wx', 'lark'] // 顺序不要改变，与传递的值相关
        const shelfVersion = platList.findIndex((item) => xbb.isThirdPC([item], true)) + 1
        return shelfVersion || 1
      }
    },

    /**
     * @description 授权账户行 回显的数据规则
     * @param {Object} val 数据格式类似{crm:1,服务云：1,客服云：0}
     * @returns {String} 表格回显的字符串
     */
    showLicenseName(val) {
      const licenseNameObj = {}
      const feeNameArr = []
      this.feeList.forEach((item) => {
        licenseNameObj[item.feeType] = item.feeName
      })
      const getName = (value) => {
        Object.keys(value).forEach((key) => {
          if (value[key]) {
            feeNameArr.push(licenseNameObj[key])
          }
        })
        return feeNameArr.join('、')
      }
      return val && Object.keys(val).length ? getName(val) : '--'
    },

    operateHandler(type, row) {
      this.$emit('operateHandler', type, row)
    }
  }
}
</script>

<style lang="scss" scope>
.el-zoom-in-right-enter-active {
  transition: all 0.6s ease;
}
.el-zoom-in-right-leave-active {
  // transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  transition: all 0.8s ease;
}
.el-zoom-in-right-enter, .el-zoom-in-right-leave-to
/* .el-zoom-in-right-leave-active for below version 2.1.8 */ {
  opacity: 0;
  transform: translateX(600px);
}
.employee-info-dialog {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  .employee-info-dialog__head {
    display: flex;
    padding: 18px 0;
    .title {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      line-height: 26px;
      color: $text-main;
      letter-spacing: 0em;
    }
    .btn {
      flex: 0;
    }
  }
  .desc {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: $text-main;
  }
  .border {
    width: 100%;
    margin-bottom: 20px;
    border-bottom: 1px dashed $neutral-color-3;
  }
  & > .form-data-detail__dialog {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow-y: hidden;
    .el-dialog__header {
      border-bottom: 0px;
    }
    & > .form-data-detail__dialog__body {
      position: absolute;
      right: 0px;
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 0 20px 20px 20px;
      margin: 0px;
      margin-top: 0px !important;
      & > .el-dialog__header {
        height: auto;
        padding: 0px;
        line-height: normal;
        // line-height: 75px;
        background: $base-white;
        & > .form-data-detail__dialog__title {
          position: relative;
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      }
      & > .el-dialog__body {
        box-sizing: border-box;
        display: flex;
        flex: 1;
        flex-direction: column;
        // height: calc(100% - 115px);
        padding: 0;
        margin-bottom: 20px;
        overflow: auto;
        & > .form-data-detail__dialog__content {
          // background: skyblue;
          // height: 100%;
          flex: 1;
          overflow: auto;
          background: $base-white;
          // background: red;
        }
      }
    }
  }
  & > .v-modal {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  .el-form-item {
    margin-bottom: 10px;
    img {
      width: 32px;
      height: 32px;
    }
  }
  .el-form-item--small .el-form-item__label {
    color: $text-plain;
  }
  .el-form-item--small .el-form-item__content {
    color: black;
  }
}
.title__close-btn {
  position: absolute;
  top: 0px;
  left: -51px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50px;
  height: 50px;
  font-size: 20px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  background: $base-white;
}
</style>
