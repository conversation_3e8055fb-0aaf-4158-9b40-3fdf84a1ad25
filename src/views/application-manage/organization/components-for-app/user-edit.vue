/* * @Author: kai.yang * @Date: 2018-11-07 17:10:24 * @LastEditors: kai.yang * @LastEditTime:
2018-11-07 20:03:39 * @Description: 新增、修改员工 */

<template>
  <div class="edit-user">
    <el-dialog
      :before-close="handleClose"
      :close-on-click-modal="false"
      :title="dialogTitele"
      :visible.sync="dialogFormVisible"
    >
      <div class="info-box">
        <h3>{{ $t('formDataDetail.tabs.essentialInformation') }}</h3>
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item
            :label="$t('formDataDetail.tabs.headPortrait')"
            :label-width="formLabelWidth"
            prop="avatar"
          >
            <avatar-img
              v-if="!form.avatar || form.avatar.includes('default.jpg')"
              :alt="form.name"
              :size="30"
              :src="form.avatar"
              style="float: left"
            />
            <img v-else :src="form.avatar" />
            <a href="javascript:;" @click="changeAvatorDialogShow = true">{{
              $t('operation.update')
            }}</a>
          </el-form-item>
          <el-form-item :label="$t('label.fullName')" :label-width="formLabelWidth" prop="name">
            <el-input v-model="form.name" auto-complete="off"></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('placeholder.phone')"
            :label-width="formLabelWidth"
            prop="mobile"
          >
            <el-input v-if="!isEdit" v-model="form.mobile" auto-complete="off"></el-input>
            <span v-else>{{ form.mobile || '-' }}</span>
          </el-form-item>
          <el-form-item label="登录邮箱" :label-width="formLabelWidth" prop="email">
            <el-input v-if="!isEdit" v-model="form.email" auto-complete="off"></el-input>
            <span v-else>{{ form.email || '-' }}</span>
          </el-form-item>
          <el-form-item
            :label="$t('label.subordinateDepartments')"
            :label-width="formLabelWidth"
            prop="depIds"
          >
            <el-select
              ref="elSelect"
              v-model="depIds"
              multiple
              :placeholder="
                $t('placeholder.choosePls', { attr: $t('label.subordinateDepartments') })
              "
              style="width: 100%"
              @focus="changeDepartmentBatch"
              @remove-tag="removeDepIds"
            >
              <!-- <el-option v-show="false"></el-option>  -->
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('label.mainDepartment')"
            :label-width="formLabelWidth"
            prop="mainDepIds"
          >
            <el-select
              ref="elMainSelect"
              v-model="mainDepIds"
              multiple
              :placeholder="$t('placeholder.choosePls', { attr: $t('label.mainDepartment') })"
              style="width: 100%"
              @focus="changeMainDepartmentBatch"
              @remove-tag="removeMainDepIds"
            >
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('organization.position')"
            :label-width="formLabelWidth"
            prop="position"
          >
            <el-input v-model="form.position" auto-complete="off"></el-input>
          </el-form-item>
          <div class="same-tag"></div>
          <el-form-item :label="$t('label.role')" :label-width="formLabelWidth" prop="role">
            <el-select
              v-model="form.roleIds"
              class="roleSelect"
              multiple
              :multiple-limit="5"
              :placeholder="$t('placeholder.choosePls', { attr: $t('label.role') })"
              style="width: 100%"
            >
              <el-option
                v-for="item in roleList"
                :key="item.id"
                :label="item.roleName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="info-box">
        <h3>{{ $t('formDataDetail.tabs.otherInformation') }}</h3>
        <el-form :model="form">
          <el-form-item
            :label="$t('nouns.accountId')"
            :label-width="formLabelWidth"
            prop="accountId"
          >
            {{ form.id }}
          </el-form-item>
          <el-form-item
            :label="$t('nouns.userWorkID')"
            :label-width="formLabelWidth"
            prop="userWorkID"
          >
            <el-input v-model="form.jobnumber" auto-complete="off"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{ $t('operation.cancel') }}</el-button>
        <el-button :disabled="loading" type="primary" @click="submitForm('form')">{{
          $t('operation.confirm')
        }}</el-button>
      </div>
    </el-dialog>
    <person-select
      v-if="isPersonSelectShow"
      active-tab-name="dept"
      :default-val="showTag"
      :dialog-visible.sync="isPersonSelectShow"
      :is-manager="true"
      :show-tabs-name="['dept']"
      :sub-admin="subAdmin"
      :tab-multiple="true"
      :title="$t('organization.chooseDepartment')"
      @dialogSubmit="handleDepartment"
    >
    </person-select>

    <person-select
      v-if="isMianDeptSelectShow"
      active-tab-name="dept"
      :default-val="showMainTag"
      :dialog-visible.sync="isMianDeptSelectShow"
      :show-tabs-name="['dept']"
      :sub-admin="subAdmin"
      :tab-multiple="false"
      :title="$t('operation.choose') + $t('label.mainDepartment')"
      @dialogSubmit="handleMainDepartment"
    >
    </person-select>
    <avatar-upload-dialog
      v-if="changeAvatorDialogShow"
      v-model="avatarUrl"
      :show.sync="changeAvatorDialogShow"
    />
  </div>
</template>

<script>
import AvatarUploadDialog from '@/views/application-manage/organization/components/avatar-upload-dialog'
import { userSave } from '@/api/for-app'
import PersonSelect from '@/components/person/person-select'
import AvatarImg from '@/components/base/avatar-img.vue'
import { roleList } from '@/api/system'

export default {
  name: 'EditUser',

  components: {
    PersonSelect,
    AvatarImg,
    AvatarUploadDialog
  },

  props: {
    editData: {
      type: Object,
      required: true
    },
    subAdmin: {
      type: Boolean,
      default: false
    },
    isSubAdmin: {
      type: Number,
      default: 0
    }
  },

  data() {
    const roleRule = (rule, value, callback) => {
      if (this.form.roleIds.length < 1) {
        callback(new Error(this.$t('placeholder.choosePls', { attr: this.$t('label.role') })))
      } else {
        callback()
      }
    }
    const depRule = (rule, value, callback) => {
      if (this.depIds.length < 1) {
        callback(new Error(this.$t('workOrder.departmentStaff')))
      } else {
        callback()
      }
    }
    return {
      dialogFormVisible: false,
      changeAvatorDialogShow: false,
      formLabelWidth: '90px',
      departmentName: '',
      form: {},
      rules: {
        name: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('formDataDetail.tabs.employeeName')
            }),
            trigger: 'blur'
          },
          {
            max: 50,
            message: this.$t('rule.lessThanFie', {
              attr: this.$t('formDataDetail.tabs.employeeName'),
              num: 50
            }),
            trigger: 'blur'
          }
        ],
        position: {
          max: 50,
          message: this.$t('rule.lessThanFie', {
            attr: this.$t('organization.positionName'),
            num: 50
          }),
          trigger: 'blur'
        },
        depIds: { required: true, validator: depRule, trigger: 'blur' },
        mobile: {
          // required: true,
          message: this.$t('placeholder.inputPls', { attr: this.$t('placeholder.phone') }),
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (!this.form.email && !/^1[3456789]\d{9}$/.test(value)) {
              callback(new Error('请输入正确的手机号'))
            } else {
              callback()
            }
          }
        },
        email: {
          // required: true,
          message: this.$t('placeholder.inputPls', { attr: '登录邮箱' }),
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (
              !this.form.mobile &&
              !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)
            ) {
              callback(new Error('请输入正确的登录邮箱'))
            } else {
              callback()
            }
          }
        },
        role: { required: true, validator: roleRule, trigger: 'blur' }
      },
      loading: false,
      isPersonSelectShow: false,
      // 组织架构选择弹窗是否显示
      isMianDeptSelectShow: false,
      // userDepInfoList: [],
      depIds: [],
      mainDepIds: [],
      roleList: []
    }
  },

  computed: {
    avatarUrl: {
      get() {
        return this.form.avatar === '/images/default.jpg' ? undefined : this.form.avatar
      },
      set(val) {
        this.form.avatar = val || '/images/default.jpg'
      }
    },
    // 新建还是编辑
    isEdit() {
      return !!this.editData.id
    },
    dialogTitele() {
      if (this.isEdit) {
        return this.$t('organization.editEmployeeInfo')
      } else {
        return this.$t('organization.addEmployeeInfo')
      }
    },
    showTag() {
      let res = []
      const arr = this.form.userDepInfoList || []
      res = arr.map((item) => {
        return {
          id: item.depId, // id
          name: item.name, // 显示的label
          property: 'dept', // 类型:user、dept、role、manager
          isLeader: item.isLeader,
          editable: item.editable
        }
      })

      return res
    },
    showMainTag() {
      let res
      if (this.form.mainDepartment) {
        res = [
          {
            id: this.form.mainDepartment,
            name: this.form.mainDepartmentName,
            property: 'dept',
            editable: this.form.mainDepartmentName === '' && this.isSubAdmin ? 0 : undefined
          }
        ]
      } else {
        res = []
      }
      return res
    }
  },

  mounted() {
    this.initData()
  },

  methods: {
    // 初始化数据
    initData() {
      const {
        userId,
        name,
        position,
        userDepInfoList,
        mainDepartmentName,
        mainDepartment,
        mobile,
        roleSet,
        avatar,
        del,
        email,
        isLeaderInDepts = '{}',
        userWorkID
      } = JSON.parse(JSON.stringify(this.editData))
      if (userDepInfoList) {
        const leaders = JSON.parse(JSON.stringify(isLeaderInDepts))
        this.depIds = userDepInfoList.map((item) => {
          const leaderName = leaders[item.depId] ? '(主管)' : ''
          return item.name + leaderName
        })
        const hideCloseIndex = []
        userDepInfoList.forEach((item, index) => {
          if (item.editable === 0) {
            hideCloseIndex.push(index)
          }
        })
        setTimeout(() => {
          const tagCloseTemp = this.$refs.elSelect.$el.querySelectorAll('.el-tag__close')
          tagCloseTemp.forEach((tagEl, index) => {
            if (hideCloseIndex.includes(index)) {
              tagEl.style.display = 'none'
            }
          })
        })
      }
      if (mainDepartment) {
        this.mainDepIds.push(mainDepartmentName)
        if (mainDepartmentName === '' && this.isSubAdmin) {
          setTimeout(() => {
            const tagCloseTemp = this.$refs.elMainSelect.$el.querySelectorAll('.el-tag__close')
            tagCloseTemp[0].style.display = 'none'
          })
        }
      }
      if (this.isEdit) {
        const roleIds = roleSet.map((item) => {
          return item.id
        })
        const hideCloseIndex = []
        roleSet.forEach((item, index) => {
          if (item.editable === 0) {
            hideCloseIndex.push(index)
          }
        })
        setTimeout(() => {
          const tagCloseTemp = document.querySelectorAll('.roleSelect .el-tag__close')
          const tagTemp = document.querySelectorAll('.roleSelect .el-tag')
          tagCloseTemp.forEach((tagEl, index) => {
            if (hideCloseIndex.includes(index)) {
              tagEl.style.display = 'none'
            }
          })
          tagTemp.forEach((tagEl, index) => {
            if (hideCloseIndex.includes(index)) {
              tagEl.style.color = '#969799'
              tagEl.innerHTML = roleSet[index].roleName
            }
          })
        })

        this.form = {
          id: userId,
          name,
          position,
          mobile,
          roleIds,
          userDepInfoList,
          avatar,
          mainDepartment,
          mainDepartmentName,
          del,
          email,
          userWorkID
        }
      } else {
        this.form = {
          name: '',
          position: '',
          mobile: '',
          roleIds: [],
          userDepInfoList,
          mainDepartment,
          mainDepartmentName,
          avatar: '',
          del: 0,
          email: '',
          userWorkID: ''
        }
      }
      this.getRoleList()
      this.dialogFormVisible = true
    },

    handleClose() {
      this.$emit('close')
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (!this.form.mobile && !this.form.email) {
          this.$message({
            type: 'error',
            message: '手机号、登录邮箱项至少一项必填'
          })
        }
        if (valid) {
          this.sendData()
        } else {
          return false
        }
      })
    },

    sendData() {
      this.loading = true

      /* let userDepInfoList = this.form.userDepInfoList
      if(userDepInfoList) */
      const params = {
        id: this.form.id,
        name: this.form.name,
        position: this.form.position,
        mobile: this.form.mobile.replace(/\s+/g, ''),
        roleIds: this.form.roleIds,
        userDepInfoList: this.form.userDepInfoList,
        mainDepartmentName: this.form.mainDepartmentName || undefined,
        mainDepartment: this.form.mainDepartment || undefined,
        avatar: this.form.avatar,
        email: this.form.email,
        userWorkID: this.form.userWorkID,
        del: this.form.del,
        jobnumber: this.form.jobnumber
      }

      userSave(params)
        .then((res) => {
          if (this.form.email && !this.isEdit) {
            this.$message({
              type: 'success',
              message: '员工邀请邮件已发送，请告知员工登录邮箱查收'
            })
          } else {
            this.$message({
              type: 'success',
              message: res.msg
            })
          }
          this.handleClose()
          this.$emit('refresh')
        })
        .finally(() => {
          this.loading = false
        })
        .catch(() => {})
    },

    changeDepartmentBatch() {
      this.$refs.elSelect.blur()
      this.isPersonSelectShow = true
    },
    changeMainDepartmentBatch() {
      this.$refs.elMainSelect.blur()
      this.isMianDeptSelectShow = true
    },
    handleMainDepartment(departments) {
      console.log('departments', departments)
      this.form.mainDepartmentName = ''
      this.form.mainDepartment = ''
      this.mainDepIds = []

      departments.forEach((item, idx) => {
        this.form.mainDepartment = item.id
        this.form.mainDepartmentName = item.name
        this.mainDepIds.push(item.name)
      })
    },
    handleDepartment(departments) {
      // debugger
      this.form.userDepInfoList = []
      this.depIds = []
      let name = ''
      departments.forEach((item, idx) => {
        if (item.isLeader) {
          if (item.name.indexOf('主管') === -1) {
            name = item.name + '(主管)'
          } else {
            name = item.name
          }
        } else {
          name = item.name.replace('(主管)', '')
        }

        // 判断isLeader是否为NaN，如果是的话转换成0
        let isLeader = Number(item.isLeader)
        if (isNaN(isLeader)) {
          isLeader = 0
        }

        this.form.userDepInfoList.push({
          depId: item.id,
          isLeader: isLeader,
          name
        })
        if (!this.isEdit) {
          this.form.mainDepartment = this.form.userDepInfoList[0].id
          this.form.mainDepartmentName = this.form.userDepInfoList[0].name
        }

        this.depIds.push(name)
      })
    },

    removeMainDepIds(dep) {
      this.form.mainDepartment = ''
      this.form.mainDepartmentName = ''
    },
    removeDepIds(dep) {
      this.form.userDepInfoList = this.form.userDepInfoList.filter((item) => {
        return item.name !== dep
      })
    },
    // 获取角色列表
    getRoleList() {
      roleList({ subAdmin: this.subAdmin })
        .then((res) => {
          this.roleList = res.result.roleList
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-select__tags) {
  & .el-tag--info {
    color: $brand-base-color-6;
    background-color: $neutral-color-1;
    border-color: #ffe8d8;
  }
  & .el-tag__close {
    color: $brand-base-color-6;
    background-color: transparent;
  }
}
.edit-user {
  .info-box {
    h3 {
      padding-bottom: 10px;
      margin-bottom: 20px;
      font-size: 15px;
      font-weight: bold;
      color: black;
      border-bottom: 1px solid $neutral-color-3;
    }
    img {
      float: left;
      width: 36px;
      height: 36px;
    }
    a {
      float: left;
      margin-left: 10px;
      color: $brand-base-color-6;
    }
  }
}
</style>
