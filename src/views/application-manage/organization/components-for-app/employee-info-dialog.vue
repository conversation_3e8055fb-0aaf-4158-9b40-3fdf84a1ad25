<!--
 * @Author: haoh<PERSON>.dang
 * @Date: 2021-09-15 14:38:03
 * @LastEditors: weimin.wei
 * @LastEditTime: 2023-05-29 09:28:21
 * @Description:组织架构-员工信息
-->
<template>
  <div class="employee-info-dialog">
    <transition name="el-zoom-in-right">
      <el-dialog
        v-if="dialogShow"
        ref="dialogRef"
        class="form-data-detail__dialog"
        :close-on-click-modal="true"
        custom-class="form-data-detail__dialog__body"
        :modal-append-to-body="true"
        :show-close="false"
        :visible.sync="dialogShow"
        width="900px"
      >
        <span
          slot="title"
          class="el-dialog__title"
          v-html="
            addNodeByWX(dialogData.name) +
            $t('unit.of') +
            $t('organization.employee.employeeInfoTitle')
          "
        ></span>
        <div class="title__close-btn" @click="dialogShow = false">
          <i class="el-icon-close"></i>
        </div>
        <form-data ref="formDataForm" :field-list="[]" :form-data="{}"> </form-data>

        <h3>基本信息</h3>
        <div class="border"></div>
        <el-form label-position="left" label-width="80px">
          <el-form-item :label="$t('organization.employee.baseInfo')[0]">
            <span v-if="userData.avatar === '-'">{{ userData.avatar }}</span>
            <img v-else-if="!userData.avatar.includes('default.jpg')" :src="userData.avatar" />
            <avatar-img
              v-else-if="userData.avatar.includes('default.jpg')"
              :alt="userData.name"
              :size="30"
              :src="userData.avatar"
            />
          </el-form-item>
          <el-form-item :label="$t('organization.employee.baseInfo')[1]">
            <span v-html="addNodeByWX(userData.name)"></span
          ></el-form-item>
          <el-form-item v-if="platform !== 1" :label="$t('organization.employee.baseInfo')[2]"
            ><span>{{ userData.mobile }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[3]"
            ><span v-html="addNodeByWX(userData.mainDepartmentName, 1)"></span
          ></el-form-item>
          <el-form-item :label="$t('organization.employee.baseInfo')[4]"
            ><span v-html="addNodeByWX(userData.departmentConcat, 1)"></span
          ></el-form-item>
          <el-form-item :label="$t('organization.employee.baseInfo')[5]"
            ><span>{{ userData.position }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[6]"
            ><span>{{ userData.roleConcat }}</span></el-form-item
          >
          <el-form-item :label="$t('organization.employee.baseInfo')[7]">
            <v-tag
              v-for="tag in dialogData.labelSet || []"
              :key="tag.id"
              class="form-data__tag"
              :color="tag.color"
              :content="tag.name"
            />
            <span v-if="!dialogData.labelSet || !dialogData.labelSet.length">--</span>
          </el-form-item>
        </el-form>
        <div v-if="platform !== 1">
          <h3>其他信息</h3>
          <div class="border"></div>
          <el-form label-position="left" label-width="80px">
            <el-form-item v-if="platform !== 1" :label="$t('organization.employee.otherInfo')[0]"
              ><span>{{ userData.email }}</span></el-form-item
            >
            <el-form-item v-if="platform === 3" :label="$t('organization.employee.otherInfo')[1]">
              <span v-if="userData.qRCode === '-'">{{ userData.qRCode }}</span>
              <img v-else :src="userData.qRCode" style="width: 80px; height: 80px" />
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
    </transition>
  </div>
</template>

<script>
import formData from '@/components/form-data-edit/form-data-form.vue'
import { getDetial } from '@/api/system'
import xbb from '@xbb/xbb-utils'
import AvatarImg from '@/components/base/avatar-img.vue'
import VTag from '@/components/base/v-tag.vue'

export default {
  name: 'EmployeeInfoDialog',
  components: { FormData: formData, AvatarImg, VTag },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogShow: false,
      userId: '',
      platform: 1,
      userData: {
        avatar: '',
        name: '',
        mobile: '',
        departmentConcat: '',
        position: '',
        roleConcat: '',
        email: '',
        qRCode: ''
      }
    }
  },
  watch: {
    // 控制sync 动画
    show: {
      immediate: true,
      handler(val) {
        // this.dialogShow = val
        this.$nextTick(() => {
          this.dialogShow = val
        })
      }
    },
    // 双向绑定控制动画
    dialogShow(val) {
      if (val) {
        console.log('show', new Date())
        this.$nextTick(() => {
          // this.detailLoading = this.$loading({target: '.form-data-detail__dialog__body'})
        })
        this.$emit('update:show', val)
      } else {
        setTimeout(() => {
          this.$emit('update:show', val)
        }, 400)
      }
    }
  },
  mounted() {
    this.getEmployeeInfo()
    this.platform = this.getShelfVersion()
    console.log('当前环境：', this.getShelfVersion())
  },
  methods: {
    // 获取员工信息
    async getEmployeeInfo() {
      const data = {
        userId: utils.LS.get('userId'),
        checkUserId: this.dialogData.userId,
        corpid: this.dialogData.corpid
      }
      try {
        const res = await getDetial(data)
        const { user } = res.result
        this.userData.avatar = user.avatar
        this.userData.name = user.name
        this.userData.mobile = user.mobile
        this.userData.mainDepartmentName = user.mainDepartmentName
        this.userData.departmentConcat = user.departmentConcat
        this.userData.position = user.position
        this.userData.roleConcat = user.roleConcat
        this.userData.del = user.del
        this.userData.email = user.email
        this.userData.qRCode = user.qRCode
        console.log('员工信息：', data, user)
      } catch (err) {
        console.log(err)
      }
    },
    // 判断当前环境
    getShelfVersion() {
      if (xbb.isDevEnv()) {
        return 1 // 本地调试
      } else {
        const platList = ['dd', 'h5', 'wx', 'lark'] // 顺序不要改变，与传递的值相关
        const shelfVersion = platList.findIndex((item) => xbb.isThirdPC([item], true)) + 1
        return shelfVersion || 1
      }
    }
  }
}
</script>

<style lang="scss" scope>
.el-zoom-in-right-enter-active {
  transition: all 0.6s ease;
}
.el-zoom-in-right-leave-active {
  // transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  transition: all 0.8s ease;
}
.el-zoom-in-right-enter, .el-zoom-in-right-leave-to
/* .el-zoom-in-right-leave-active for below version 2.1.8 */ {
  opacity: 0;
  transform: translateX(600px);
}
.employee-info-dialog {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  h3 {
    font-size: 16px;
    font-weight: bold;
    line-height: 32px;
    color: black;
  }
  .border {
    width: 100%;
    margin-bottom: 20px;
    border-bottom: 1px solid $neutral-color-3;
  }
  & > .form-data-detail__dialog {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow-y: hidden;
    .el-dialog__header {
      border-bottom: 0px;
    }
    & > .form-data-detail__dialog__body {
      position: absolute;
      right: 0px;
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: 0 20px 20px 20px;
      margin: 0px;
      margin-top: 60px !important;
      & > .el-dialog__header {
        height: auto;
        padding: 0px;
        line-height: normal;
        // line-height: 75px;
        background: #fff;
        & > .form-data-detail__dialog__title {
          position: relative;
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      }
      & > .el-dialog__body {
        box-sizing: border-box;
        display: flex;
        flex: 1;
        flex-direction: column;
        // height: calc(100% - 115px);
        padding: 0;
        margin-bottom: 20px;
        overflow: auto;
        & > .form-data-detail__dialog__content {
          // background: skyblue;
          // height: 100%;
          flex: 1;
          overflow: auto;
          background: #fff;
          // background: red;
        }
      }
      .form-data__tag {
        margin-bottom: 10px;
      }
    }
  }
  & > .v-modal {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  .el-form-item {
    margin-bottom: 10px;
    img {
      width: 32px;
      height: 32px;
    }
  }
  .el-form-item--small .el-form-item__label {
    color: $text-plain;
  }
  .el-form-item--small .el-form-item__content {
    color: black;
  }
}
.title__close-btn {
  position: absolute;
  top: 0px;
  left: -51px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50px;
  height: 50px;
  font-size: 20px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  background: #fff;
}
</style>
