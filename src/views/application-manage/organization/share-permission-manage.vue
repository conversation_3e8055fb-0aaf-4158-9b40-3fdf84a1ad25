<!-- 管理中心-共享权限页 -->
<template>
  <div class="share-permission-manage">
    <container-wrap :title="$t('router.appManagement.sharePermission')">
      <template slot="sub-head">
        <el-button
          class="header-button"
          :loading="updateAuthorityLoading"
          @click="updateAuthorityHandler"
          >{{ $t('organization.sharePermission.updateAuthority') }}</el-button
        >
        <el-button class="header-button" type="primary" @click="addPermissionHandler">{{
          $t('operation.add')
        }}</el-button>
      </template>
      <side-layout v-loading="appListLoading" class="left-nav-wrap" :show-title="false">
        <left-nav-list v-model="activeNavIndex" label="name" :list="fullAppList" />
      </side-layout>
      <main-layout class="main-wrap" :show-title="false">
        <share-permission-table
          ref="permissionTableRef"
          class="main-table"
          :form-query="formQuery"
        />
      </main-layout>
    </container-wrap>
  </div>
</template>

<script>
import { getShareAppList } from '@/api/system.js'
import ContainerWrap from './components/container-wrap'
import SideLayout from './components/side-layout'
import MainLayout from './components/main-layout'
import LeftNavList from './components/left-nav-list'
import SharePermissionTable from './components/share-permission-table'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'SharePermissionManage',

  components: {
    ContainerWrap,
    SideLayout,
    MainLayout,
    LeftNavList,
    SharePermissionTable
  },

  data() {
    return {
      appList: null,
      appListLoading: false,
      activeNavIndex: 0,
      updateAuthorityLoading: false
    }
  },

  computed: {
    // 表单信息
    formQuery() {
      // 管理中心场景下，只需要传appId
      const curNavItem = this.fullAppList[this.activeNavIndex] || {}
      return {
        appId: curNavItem.id || null
      }
    },
    // 侧边栏列表
    fullAppList() {
      return [
        {
          alias: 'all',
          id: 0, // 虚构的id
          name: this.$t('CRM.all'),
          saasMark: 1,
          sort: 0
        },
        ...(this.appList || [])
      ]
    }
  },

  created() {
    this.getLeftAppList()
  },

  methods: {
    /**
     * @description: 获取应用列表
     * @return {*}
     */
    getLeftAppList() {
      this.appListLoading = true
      getShareAppList()
        .then((res) => {
          this.appList = xbb._get(res, 'result.appList')
        })
        .catch(() => {
          this.appList = null
          this.activeNavIndex = 0
        })
        .finally(() => {
          this.appListLoading = false
        })
    },
    /**
     * @description: 新建规则
     * @return {*}
     */
    addPermissionHandler() {
      this.$refs.permissionTableRef.operateClickHandler('add')
    },
    /**
     * @description: 更新授权员工
     * @return {*}
     */
    updateAuthorityHandler() {
      this.updateAuthorityLoading = true
      this.$refs.permissionTableRef.updateAuthorityHandler().then(() => {
        this.updateAuthorityLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.share-permission-manage {
  position: relative;
  box-sizing: border-box;
  height: 100%;
  border: 1px solid transparent; // 解决子元素margin溢出导致的滚动问题

  .left-nav-wrap {
    flex: 0 0 135px;
    // padding: 5px 0;
    // width: 135px;
  }
  .main-wrap {
    flex: 1;
    padding: 5px 10px 0 10px;
    .main-table {
      box-sizing: border-box;
      flex: 1 1 auto; // main-wrap的组件里带了flex
      width: 100%;
      overflow: hidden;
    }
  }
}
</style>
