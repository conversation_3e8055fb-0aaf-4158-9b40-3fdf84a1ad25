<template>
  <div class="organization-manage">
    <resignationPerson
      v-if="showDisableUser"
      :show-disable-user.sync="showDisableUser"
    ></resignationPerson>
    <div v-else class="container-wrap organization-block">
      <div class="container-header">
        <div>
          <el-tabs v-model="activeTab">
            <el-tab-pane name="1">
              <span slot="label">
                {{ $t('organization.deptMemberMgt') }}
                <el-popover v-if="isMultiFee" placement="bottom" trigger="hover" width="280">
                  <div class="fee-list">
                    <div class="fee-list__desc">{{ $t('nouns.authorizedAccountDetails') }}：</div>
                    <div v-for="fee in feeList" :key="fee.feeType" class="fee-list__item">
                      <div class="fee-list__item-title">{{ fee.feeName }}</div>
                      <div class="fee-list__item-fee">{{ fee | getFeeText }}</div>
                    </div>
                  </div>
                  <i slot="reference" class="icon-information-line organization-fee t-iconfont"></i>
                </el-popover>
              </span>
            </el-tab-pane>
            <el-tab-pane
              :disabled="!$feeType.checkFeatureEnable('OrgPermission.organizationalVisibleRules')"
              name="2"
            >
              <span slot="label">
                {{ $t('organization.organizationalStructureRules') }}
                <i
                  class="icon-question-line organization-fee t-iconfont"
                  @click="tipsDialogVisible = true"
                ></i>
              </span>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="option-remind-time">
          <template v-if="activeTab === '1'">
            <div class="sub-head-item">
              <el-input
                v-model="userListParams.nameLike"
                clearable
                :placeholder="$t('placeholder.searchPls', { attr: $t('label.staff') })"
                prefix-icon="el-icon-search"
                @clear="handleSearch"
                @keyup.native="handleSearch"
              >
              </el-input>
            </div>
            <div class="sub-head-item">
              <role-radio
                :select-role="searchRole"
                :sub-admin="true"
                @change="searchRoleChangeHandler"
              />
            </div>
            <div v-if="isQw" class="sub-head-item">
              <el-select
                v-model="userListParams.licenseAccountType"
                clearable
                placeholder="筛选账号类型"
                size="mini"
                @change="getUserList"
              >
                <el-option
                  v-for="item in Object.values(ENUM_WX_ACCOUNT_TYPE)"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </div>
            <el-button
              v-if="isQw"
              v-show="!showDisableUser"
              class="sub-header-item"
              :loading="isSyncingAccount"
              type="primary"
              @click="hanldeSyncAccount"
              >同步账号</el-button
            >
            <el-button
              v-show="!showDisableUser && getCompanyLicenseType !== 5"
              :loading="isSyncing"
              @click="hanldeSync"
              >{{ $t('organization.syncOrganizational') }}
            </el-button>
            <el-dropdown
              class="ml10"
              split-button
              @click="showDisableUser = !showDisableUser"
              @command="disableSet"
            >
              {{ $t('organization.disabledPersonnelQuery') }}
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>{{ $t('organization.disabledPersonnelSet') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button @click.native="showVisibleRangeRuleDialog">
              {{ $t('operation.visibleRangeRule') }}
            </el-button>
            <el-button class="ml10" type="primary" @click="handleAddRule">
              {{ $t('operation.add') }}
            </el-button>
          </template>
        </div>
      </div>
      <div v-if="activeTab === '1'" class="container">
        <side-layout :title="$t('organization.origanizationOfCompany')">
          <el-input
            v-model="filterValue"
            class="node-filter-input"
            clearable
            :placeholder="$t('placeholder.searchDep')"
            prefix-icon="el-icon-search"
            size="mini"
            @input="nodeFilterChangeHandler"
          />
          <el-scrollbar style="height: calc(100% - 39px)">
            <el-tree
              ref="tree"
              check-on-click-node
              :data="departmentList"
              :default-expanded-keys="defaultExpandedKeys"
              :expand-on-click-node="false"
              :filter-node-method="filterDepartNode"
              highlight-current
              node-key="pid"
              :props="defaultProps"
              @node-click="nodeClick"
            >
              <template slot-scope="{ node }">
                <span v-html="addNodeByWX(node.label, 1)"></span>
              </template>
            </el-tree>
          </el-scrollbar>
        </side-layout>

        <main-layout
          :title="
            noAuthority ? '' : addNodeByWX(departmentActive, 1) + $t('organization.employeeList')
          "
        >
          <template v-if="isQw" #sub-head>
            <!-- 只在企微容器的组织架构出现 -->
            <el-button v-if="isWechatAdmin && isWxPC" @click="WxVisibleRangeDialogShow">{{
              $t('login.viewRangeSet')
            }}</el-button>
            <div class="account-usage">
              基础账号（{{ accountUsage.baseUsage }}/{{
                accountUsage.baseCount
              }}）&nbsp;&nbsp;&nbsp; 互通账号（{{ accountUsage.externalContactUsage }}/{{
                accountUsage.externalContactCount
              }}）
            </div>
          </template>
          <div class="list-content">
            <div v-if="noAuthority" class="unopened">
              <Vempty empty-title="暂无可见权限" emptybackground-color="transparent"></Vempty>
            </div>
            <v-table
              v-else
              key="1"
              ref="table"
              v-loading="loading"
              border
              cell-class-name="v-cell"
              :data="userList"
              :header-cell-style="rowClass"
              :height="tableHeight"
              size="small"
              style="width: 100%"
              @select="handleSelectChange"
              @select-all="handleSelectAll"
            >
              <!-- 多选框免费版无批量操作（feeType===-2）-->
              <el-table-column
                v-if="feeType !== -2"
                fixed="left"
                type="selection"
                width="40"
              ></el-table-column>
              <el-table-column :label="$t('label.fullName')" prop="name" width="120">
                <template slot="header" slot-scope="scope">
                  <el-popover
                    class="table-filter-popover"
                    placement="bottom"
                    trigger="hover"
                    width="120"
                  >
                    <div slot="reference" class="flex-center">
                      <span class="search-title">{{ $t('label.fullName') }}</span>
                      <span class="iconCss"><i class="web-icon-shaixuan web-iconfont"></i></span>
                    </div>
                    <el-input
                      v-model="userListParams.nameLike"
                      clearable
                      :placeholder="$t('operation.filter', { attr: $t('label.fullName') })"
                      @clear="handleSearch"
                      @keyup.native="handleSearch"
                    >
                    </el-input>
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  <a
                    href="javascript:;"
                    @click="showEmployeeInfo(scope.row)"
                    v-html="addNodeByWX(scope.row.name)"
                  ></a>
                </template>
              </el-table-column>

              <el-table-column
                :label="$t('label.subordinateDepartments')"
                min-width="240"
                prop="departmentConcat"
              >
                <template slot-scope="scope">
                  <span v-html="addNodeByWX(scope.row.departmentConcat, 1)"></span>
                </template>
              </el-table-column>

              <el-table-column min-width="240" prop="mainDepartmentName">
                <!-- element-ui 特性，自定表头需保留 slot-scope="scope"-->
                <template slot="header" slot-scope="scope">
                  {{ $t('label.mainDepartment') }}
                  <i
                    class="icon-question-line t-iconfont"
                    style="cursor: pointer"
                    @click="showTips = true"
                  ></i>
                </template>
                <template slot-scope="scope">
                  <span v-html="addNodeByWX(scope.row.mainDepartmentName, 1)"></span>
                </template>
              </el-table-column>

              <el-table-column :label="$t('organization.position')" prop="position" width="120">
                <template slot="header" slot-scope="scope">
                  <el-popover
                    class="table-filter-popover"
                    placement="bottom"
                    trigger="hover"
                    width="120"
                  >
                    <div slot="reference" class="flex-center">
                      <span class="search-title">{{ $t('organization.position') }}</span>
                      <span class="iconCss"><i class="web-icon-shaixuan web-iconfont"></i></span>
                    </div>
                    <el-input
                      v-model="userListParams.positionLike"
                      clearable
                      :placeholder="$t('operation.filter', { attr: $t('organization.position') })"
                      @clear="handleSearch"
                      @keyup.native="handleSearch"
                    >
                    </el-input>
                  </el-popover>
                </template>
                <template slot-scope="scope">
                  {{ scope.row.position }}
                </template>
              </el-table-column>

              <el-table-column
                v-if="$feeType.checkFeatureEnable('OrgPermission.systemMemberTag')"
                :label="$t('label.label')"
                min-width="260"
                prop="roleConcat"
              >
                <template slot-scope="scope">
                  <el-tooltip
                    :content="tagShowText(scope.row.labelSet)"
                    :disabled="tagToolTipShow"
                    effect="dark"
                    placement="top-start"
                  >
                    <div
                      style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                      @mouseenter="tagCellWidth"
                    >
                      <template v-if="!scope.row.labelSet || !scope.row.labelSet.length">
                        <span> -- </span>
                      </template>
                      <v-tag
                        v-for="(tag, tagIndex) in scope.row.labelSet"
                        :key="tagIndex"
                        :color="tag.color"
                        :content="tag.name"
                      />
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <!-- 授权账户 start -->
              <el-table-column v-if="isMultiFee" min-width="120">
                <template slot="header" slot-scope="scope">
                  <org-manage-filter
                    :fee-list="feeList"
                    :title="$t('organization.authorizedAccount')"
                    @acount-change="handleChangeAcount"
                  />
                </template>
                <template slot-scope="scope">
                  <div>{{ showLicenseName(scope.row.licenseArray) }}</div>
                </template>
              </el-table-column>
              <!-- 授权账户 end -->
              <el-table-column :label="$t('label.role')" min-width="240">
                <template slot="header">
                  <el-popover
                    class="table-filter-popover"
                    placement="bottom"
                    trigger="hover"
                    width="120"
                  >
                    <div slot="reference" class="flex-center">
                      <span class="search-title">{{ $t('label.role') }}</span>
                      <span class="iconCss"><i class="web-icon-shaixuan web-iconfont"></i></span>
                    </div>
                    <role-radio :select-role="searchRole" @change="searchRoleChangeHandler" />
                  </el-popover>
                </template>
                <el-table-column min-width="240">
                  <template slot-scope="scope"> {{ scope.row.roleConcat }}</template>
                </el-table-column>
                <el-table-column v-if="isQw" label="账号类型" prop="" width="200">
                  <template slot-scope="scope">{{ licenseCodeTypeComputed(scope.row) }}</template>
                </el-table-column>
                <el-table-column min-width="240">
                  <template slot-scope="scope">
                    <el-popover
                      placement="bottom"
                      popper-class="popoverStyle"
                      trigger="hover"
                      :visible-arrow="false"
                      width="400"
                    >
                      <div
                        v-for="(item, index) in scope.row.permissionGroup?.roleList"
                        :key="index"
                        class="group-role-hover"
                      >
                        <div class="group-role-hover-item">
                          <p class="group-name">{{ item.name }}</p>
                          <div class="tag-box">
                            <el-tag
                              v-for="tag in item.role"
                              :key="tag.id"
                              class="mg-r-10 tag-box__item"
                              type="info"
                              >{{ tag.name }}</el-tag
                            >
                          </div>
                        </div>
                      </div>
                      <div slot="reference" class="flex-center">
                        {{ scope.row.permissionGroup?.str }}
                      </div>
                    </el-popover>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="员工编号" prop="jobnumber" width="150">
                <template slot-scope="scope">
                  {{ scope.row.jobnumber || '--' }}
                </template>
              </el-table-column>
              <el-table-column fixed="right" :label="$t('operation.operate')" :width="opreateWidth">
                <template slot-scope="scope">
                  <org-manage-row-btn
                    :row="scope.row"
                    :row-more-btns="rowMoreBtns"
                    :row-show-btns="rowShowBtns"
                    @operateHandler="operateHandler"
                  />
                </template>
              </el-table-column>
            </v-table>
          </div>
          <div v-if="!noAuthority" class="table-bottom">
            <div class="bottom-left">
              <div class="edit-selected">
                <div class="prefix">{{ $t('form.selected') }}</div>
                <div class="count">{{ selectCount }}</div>
                <div class="subfix">{{ $t('unit.strip') }}</div>
                <div v-if="selectCount" class="cancel" @click="clearSelection">
                  {{ $t('operation.cancel') }}
                </div>
              </div>
              <org-manage-batch-btn
                v-if="selectCount"
                :batch-more-btns="batchMoreBtns"
                :batch-show-btns="batchShowBtns"
                @handleBatchBtn="handleBatchBtn"
              ></org-manage-batch-btn>
            </div>
            <div class="bottom-right">
              <el-pagination
                :current-page="userListParams.page"
                layout="total, sizes, prev, pager, next"
                :page-size="userListParams.pageSize"
                :page-sizes="[20, 50, 100, 200]"
                :total="rowsCount"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
              >
              </el-pagination>
            </div>
          </div>
        </main-layout>

        <user-batch-edit-role
          v-if="isBatcheditShow"
          :option-type="batchOptionType"
          :sub-admin="true"
          :user-ids="selectUserids"
          @close="isBatcheditShow = false"
          @refresh="getUserList"
        >
        </user-batch-edit-role>
        <!-- 组织架构授权弹窗引导图 -->
        <AuthorizeDialog v-if="showTips" @confirm="showTips = false"></AuthorizeDialog>

        <oauth-user-sign-dialog
          v-if="OauthUserShow"
          :user-ids="oauthUserID"
          @close="OauthUserShow = false"
        >
        </oauth-user-sign-dialog>

        <user-batch-edit-label
          v-if="batchEditLabelShow"
          :all-system-tag="systemTagList"
          :option-type="batchOptionType"
          :show.sync="batchEditLabelShow"
          :user-ids="selectUserids"
          @refresh="getUserList"
        />

        <role-permission
          v-if="isRolePermissionShow"
          :fee-list="feeList"
          :is-multi-fee="isMultiFee"
          is-role-permission
          :item="currentSelect"
          @close="isRolePermissionShow = false"
          @refresh="getUserList"
        >
        </role-permission>

        <hand-over
          v-if="isHandOverShow"
          :item="currentSelect"
          @close="isHandOverShow = false"
          @refresh="getUserList"
        >
        </hand-over>
      </div>
      <div v-else class="container">
        <OrganizationalStructureRules ref="structureRulesList"></OrganizationalStructureRules>
        <visibleRangeRuleDialog
          v-if="visibleRangeRuleDialogVisibility"
          @handleClose="handleClose"
        ></visibleRangeRuleDialog>
        <addVisibleRangeRule
          v-if="ruleDialogVisible"
          :edit="false"
          :show.sync="ruleDialogVisible"
          @refreshList="handleSearchVisibleRangeRule"
        ></addVisibleRangeRule>
      </div>
    </div>
    <RoleDialog
      v-if="showDialog"
      :dialog-data="dialogData"
      :fee-list="feeList"
      :is-multi-fee="isMultiFee"
      :show.sync="showDialog"
    />
    <EmployeeInfoDialog
      v-if="showInfoDialog"
      :dialog-data="dialogData"
      :fee-list="feeList"
      :is-multi-fee="isMultiFee"
      :row-more-btns="rowMoreBtns"
      :row-show-btns="rowShowBtns"
      :show.sync="showInfoDialog"
      @operateHandler="operateHandler"
    />
    <AccountSetting
      v-if="isAccountSetting"
      :account-type="accountType"
      :account-usage="accountUsage"
      :is-batch="isBatchAccountSetting"
      :user="currentSelect"
      :user-list="getSelectRows"
      :visible.sync="isAccountSetting"
      @callback="bindCallback"
    ></AccountSetting>
    <FirstBinding
      v-if="isFirstBinding"
      :account-usage="accountUsage"
      :visible.sync="isFirstBinding"
      @callback="bindCallback"
    ></FirstBinding>
    <tag-data-dialog
      v-if="labelDataDialogShow"
      :register-mode="['systemTag']"
      :show.sync="labelDataDialogShow"
      :system-tag-selected-list="systemTagList"
      @createTag="createTag"
      @submit="pushTag"
    />
    <!-- 授权账户 -->
    <fee-dialog
      :authorize-visible.sync="authorizeVisible"
      :batch-type="batchType"
      :is-batch="isBatch"
      :license-list="licenseList"
      @batch-confirm="handleBatchSetFee"
      @row-confirm="handleSetFee"
    ></fee-dialog>
    <el-dialog class="tips-dialog" title="详细说明" :visible.sync="tipsDialogVisible" width="800px">
      <div class="tips">
        <p class="title">组织架构可见范围规则是什么？</p>
        <span>
          管理员可设置可见规则，用于控制成员在人员/部门选择器中可见的组织架构范围，提升企业组织架构的保密性。
        </span>
      </div>
      <div class="tips">
        <p class="title">组织架构可见范围规则解决什么问题？</p>
        <div style="margin-bottom: 10px">
          阿米巴模式企业：每个子公司都有自己完整的公司架构，在选人/选部门组件上，显示全集团的成员将会降低操作效率，子公司的成员可见范围仅看到当前子公司内的人员即可。
        </div>
        <div>
          经销/分销模式企业：总部希望经销商之间在组织架构上相互不可见，经销商仅需要看到本部门和上级/总部即可。
        </div>
      </div>
      <div class="tips">
        <p class="title">组织架构可见范围规则影响范围：</p>
        <div class="special-wrap">
          <div style="margin-right: 12px">
            <p>1.选人/选部门组件里，组织架构的显示</p>
            <div class="img-wrap">
              <img
                src="@/assets/appendUser.png"
                style="width: 326px; height: 246px"
                @click="picturePreview(require('@/assets/appendUser.png'))"
              />
            </div>
          </div>
          <div>
            <p>2.管理中心-组织架构的显示</p>
            <div class="img-wrap">
              <img
                src="https://cdn3.xbongbong.com/xbbProPrd/ding51d0a413630fcca735c2f4657eb6378f/0302320829841607/png/1734316184525572f5c910bcaa467db1c2a77905898bc.png"
                style="width: 240px; height: 246px; margin-left: 40px"
                @click="
                  picturePreview(
                    'https://cdn3.xbongbong.com/xbbProPrd/ding51d0a413630fcca735c2f4657eb6378f/0302320829841607/png/1734316184525572f5c910bcaa467db1c2a77905898bc.png'
                  )
                "
              />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <lg-preview
      v-if="isPreview1Show"
      v-appendToBody="true"
      :index="currentIndex"
      :list="imgs"
      @close="isPreview1Show = false"
    ></lg-preview>
    <el-dialog class="disable-set-dialog" title="停用员工设置" :visible.sync="disabledSetDialog">
      <div v-loading="disabledSetLoading" class="disable-set-body">
        <el-checkbox v-model="disabledSetData" false-label="0" true-label="1"
          >负责人离职或停用设置</el-checkbox
        >
        <p class="disable-set-content">
          若已离职或停用的员工是数据的唯一负责人时，允许数据新建或修改成功。操作场景包含但不限于页面手动操作、审批中操作等。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="disabledSetDialog = false">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="disabledSetSubmit">{{ $t('operation.save') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAccountUsage, syncAccount } from '@/api/scrm/license/index.js'
import {
  departmentList,
  departmentRefresh,
  departmentRefreshStatus,
  userList,
  getOwnerLeaveEnableSet,
  saveOwnerLeaveEnableSet
} from '@/api/system'
import VTable from '@/components/v-table'
import { addResizeListener, removeResizeListener } from 'element-ui/src/utils/resize-event'
import { addPersonTag } from '@/api/team-member.js'
import { mapActions, mapGetters } from 'vuex'
import SideLayout from './components/side-layout'
import MainLayout from './components/main-layout'
import UserBatchEditRole from './components/user-batch-edit-role'
import UserBatchEditLabel from './components/user-batch-edit-label'
import OauthUserSignDialog from './components/OauthUser-sign-dialog'
import HandOver from './components/hand-over'
import RolePermission from './components/role-permission'
import RoleDialog from './components/role-dialog'
import ResignationPerson from './components/resignation-person'
import xbb from '@xbb/xbb-utils'
import EmployeeInfoDialog from './components-for-app/employee-info-dialog'
import AccountSetting from './components/account-setting/account-setting.vue'
import FirstBinding from './components/account-setting/first-binding.vue'
import TagDataDialog from '@/components/label-setting/tag-data-dialog.vue'
// enum
import {
  ENUM_LOGIN_COMPANY_TYPE,
  ENUM_WX_ACCOUNT_TYPE
} from '@/views/scrm/constants/enum.license.js'
import VTag from '@/components/base/v-tag.vue'
import RoleRadio from './components/role-radio'
import { oauthConnectorExist } from '@/api/singleSO'
import AuthorizeDialog from './components/authorize-dialog'
import FeeOperate from './fee-operate.js'
import OrgManageBatchBtn from './components/org-manage-batch-btn.vue'
import OrgManageRowBtn from './components/org-manage-row-btn.vue'
import OrgManageFilter from './components/org-manage-filter.vue'
import OrganizationalStructureRules from './components/organizational-structure-rules.vue'
import visibleRangeRuleDialog from './components/visible-range-rule-dialog.vue'
import addVisibleRangeRule from './components/add-visible-range-rule.vue'
import Vempty from '@/views/work-order-v2/im-chat/components/empty.vue'
import LgPreview from '@/components/files/file-picture-preview'

export default {
  name: 'OrganizationManage',
  components: {
    SideLayout,
    MainLayout,
    UserBatchEditRole,
    UserBatchEditLabel,
    HandOver,
    RolePermission,
    RoleDialog,
    EmployeeInfoDialog,
    ResignationPerson,
    AccountSetting,
    FirstBinding,
    OauthUserSignDialog,
    TagDataDialog,
    VTag,
    VTable,
    RoleRadio,
    AuthorizeDialog,
    OrgManageBatchBtn,
    OrgManageRowBtn,
    OrgManageFilter,
    OrganizationalStructureRules,
    VisibleRangeRuleDialog: visibleRangeRuleDialog,
    AddVisibleRangeRule: addVisibleRangeRule,
    Vempty,
    LgPreview
  },

  mixins: [FeeOperate],

  data() {
    return {
      ENUM_WX_ACCOUNT_TYPE, // 企微账号类型枚举
      disabledSetDialog: false,
      disabledSetData: false,
      disabledSetLoading: false,
      // tipsWidth: 264, // tips宽度默认UI稿 296，需要弹性伸缩.
      batchShowBtns: [], // 底部展示的按钮，其余要根据宽度放到更多里 逻辑在混入文件FeeOperate里
      batchMoreBtns: [], // 底部更多按钮 逻辑在混入文件FeeOperate里
      // 部门列表
      departmentList: [],
      showTips: false,
      // 当前选择的部门
      departmentActive: '',
      defaultProps: {
        children: 'nodes',
        label: 'text'
      },
      defaultExpandedKeys: [],
      loading: false,
      tableHeight: window.innerHeight - 258, // 减去出table以外的内容高度
      // 员工列表
      userList: [],
      oauthUserID: '',
      searchRole: null,
      userListParams: {
        pageSize: 20,
        page: 1,
        nameLike: '',
        roleId: '',
        licenseAccountType: '',
        departmentId: '',
        positionLike: ''
      },
      // 员工总计
      rowsCount: 0,
      isBatcheditShow: false,
      OauthUserShow: false,
      batchEditLabelShow: false,
      // 批量操作方式 add or delete
      batchOptionType: '',
      // 角色权限分配相关
      isRolePermissionShow: false,
      currentSelect: null,
      isHandOverShow: false,
      accountType: '', // 企微账号操作类型 set 授权，handover 交接
      isAccountSetting: false, // 企微-账号设置
      isBatchAccountSetting: false, // 企微-账号设置-批量
      isFirstBinding: false, // 企微-初次绑定
      // 组织架构同步中
      isSyncing: false,
      isSyncingAccount: false, // 企微同步账号loading
      showDialog: false,
      dialogData: {},
      showInfoDialog: false,
      showDisableUser: false,
      isWxPC: xbb.isThirdPC(['wx'], false), //是否是企微平台
      isDdPC: xbb.isThirdPC(['dd'], true), //是否是叮叮平台
      isWechatAdmin: 0, //是否是企微的超管
      // 企微账号使用量
      accountUsage: {
        baseCount: 0,
        baseUsage: 0,
        externalContactCount: 0,
        externalContactUsage: 0
      },
      // 标签超出显示tooltip
      tagToolTipShow: true,
      // 标签显隐
      labelDataDialogShow: false,
      selectedRow: '',
      systemTagList: [],
      // 公司是否存在授权连接器
      isHasConnector: false,
      filterValue: '',
      activeTab: '1',
      visibleRangeRuleDialogVisibility: false, //可见范围规则校验弹窗显隐
      ruleDialogVisible: false,
      customizeRoles: [1, 2, 3, 111, 222, 333, 444, 555, 666, 7894654, 52, 484, 4593, 1223],
      arr: [{ name: ******** }, { name: ********* }, { name: ******** }, { name: ********* }],
      noAuthority: false,
      tipsDialogVisible: false,
      isPreview1Show: false,
      imgs: [], // 将要预览的存放图片地址的数组
      currentIndex: 0 // 当前预览的图片的索引
    }
  },

  computed: {
    ...mapGetters(['getSelectRows', 'getCompanyLicenseType', 'feeType']), //feeType===2  免费版
    userListSearch() {
      return {
        ...this.userListParams,
        roleId: this.searchRole?.id || '',
        subAdmin: true
      }
    },

    // 已选中数量
    selectCount() {
      return this.getSelectRows.length
    },

    // 已选中的所有用户的userId
    selectUserids() {
      return this.getSelectRows.map((item) => item.userId)
    },
    isQw() {
      return this.getCompanyLicenseType === ENUM_LOGIN_COMPANY_TYPE.THIRD_WEXIN_SOURCE.code
        ? true
        : false
    },
    // 是否多套餐
    isMultiFee() {
      return this.feeList.length >= 2
    },

    // 操作列宽度
    opreateWidth() {
      // TODO 判断是否有授权按钮，无则 -60px -8px
      if (this.isMultiFee) {
        return 184
      } else {
        return 184 - 60 - 8
      }
    },

    // 行操作按钮
    rowShowBtns() {
      // 禁止在这里加按钮， 列表的按钮一律加在下面的 行操作按钮 - 更多按钮 里 rowMoreBtns
      const btn = [{ name: this.$t('organization.assignRole'), type: 'assignRole' }]
      if (this.isMultiFee) {
        // 授权账户
        btn.push({ name: this.$t('organization.authorizedAccount'), type: 'authorizedAccount' })
      }
      return btn
    },

    // 行操作按钮 - 更多按钮
    rowMoreBtns() {
      const btn = [
        { name: this.$t('nouns.addPermission'), type: 'seePermission' },
        { name: this.$t('organization.handover'), type: 'handOver' }
      ]
      if (this.isQw) {
        btn.push({ name: '授权企微账号', type: 'accountSettingSet' })
        btn.push({ name: '交接企微账号', type: 'accountSettingHandover' })
      }
      if (this.$feeType.checkFeatureEnable('OrgPermission.systemMemberTag')) {
        btn.push({ name: this.$t('formDesign.editTag'), type: 'editLabel' })
      }
      if (this.isHasConnector) {
        btn.push({ name: this.$t('organization.editIdentity'), type: 'editOauthUser' })
      }
      return btn
    },

    // 底部全部按钮
    allBatchBtns() {
      const btn = [
        { name: this.$t('organization.addRole'), type: 'addRole' },
        { name: this.$t('organization.deleteRole'), type: 'deleteRole' }
      ]
      if (this.isMultiFee) {
        // 授权账户 移除账户
        btn.push({ name: this.$t('organization.authorizedAccount'), type: 'authorizedAccount' })
        btn.push({ name: this.$t('organization.removeAccount'), type: 'removeAccount' })
      }
      if (this.isQw) {
        // 账号设置
        btn.push({ name: '授权企微账号', type: 'accountSetting' })
      }
      if (this.$feeType.checkFeatureEnable('OrgPermission.systemMemberTag')) {
        // 添加标签 删除标签
        btn.push({ name: this.$t('organization.addLabel'), type: 'addLabel' })
        btn.push({ name: this.$t('organization.deleteLabel'), type: 'deleteLabel' })
      }
      return btn
    }
  },
  watch: {
    showDisableUser(val) {
      if (val === false) {
        this.resetSearchParams()
        this.getUserList()
      }
    }
  },
  methods: {
    ...mapActions(['setSelectRows']),
    rowClass({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 1) {
        return {
          display: 'none'
        }
      }
    },
    resetSearchParams(type = '') {
      console.log('sd')
      this.userListParams = {
        pageSize: 20,
        page: 1,
        nameLike: '',
        departmentId: ''
      }
      this.searchRole = null
      if (type === 'filterReset') this.handleSearch()
      if (type === 'close') this.handleSearch()
    },

    // 初始化
    init() {
      this.getDepartmentList()
      this.getUserList()
      this.getAccountUsage()
      this.getFeeList()

      // 清空已选中的数据
      this.setSelectRows({ operationType: 'clear' })
    },
    // 打开可见范围规则校验
    showVisibleRangeRuleDialog() {
      this.visibleRangeRuleDialogVisibility = true
    },
    handleClose() {
      this.visibleRangeRuleDialogVisibility = false
    },
    handleAddRule() {
      this.ruleDialogVisible = true
    },
    handleSearchVisibleRangeRule() {
      this.$refs.structureRulesList.handleSearch()
    },
    picturePreview(url) {
      this.imgs = []
      this.imgs.push(url) // 将要预览的存放图片地址的数组
      this.isPreview1Show = true // 打开预览文件组件的开关
    },
    // 动态监听table组件高度
    watchTableHeight() {
      if (this.$refs.listContent) {
        // 监听窗口大小的变化，动态改变table高度
        this.tableResizeListener = () => {
          this.tableHeight = window.innerHeight - 258
        }
        addResizeListener(this.$refs.listContent, this.tableResizeListener)
      } else {
        setTimeout(() => {
          this.watchTableHeight()
        }, 50)
      }
    },

    // 获取部门树
    getDepartmentList() {
      departmentList({ subAdmin: true })
        .then((res) => {
          this.departmentList = res.result.departmentTree
          this.defaultExpandedKeys = [this.departmentList[0].pid]
          // if (res.data) {
          //   this.checkList.push(res.data[0].children[0].id)
          // }
        })
        .catch(() => {})
    },
    /**
     * @description:  授权账户过滤
     * @param {*} val
     */
    handleChangeAcount(val) {
      const value = val ? [val] : []
      this.$set(this.userListParams, 'licenseList', value)
      this.getUserList()
    },
    searchRoleChangeHandler(val) {
      this.searchRole = val
      this.getUserList()
    },
    disableSet() {
      this.disabledSetDialog = true
      this.disabledSetLoading = true
      getOwnerLeaveEnableSet()
        .then((res) => {
          this.disabledSetData = res.result.configValue
        })
        .finally(() => {
          this.disabledSetLoading = false
        })
    },

    disabledSetSubmit() {
      saveOwnerLeaveEnableSet({ enable: this.disabledSetData }).then((res) => {
        this.$message({
          type: 'success',
          message: res.msg
        })
        this.disabledSetDialog = false
      })
    },
    // 获取员工列表
    getUserList(options = {}) {
      if (!this.isQw) delete this.userListParams.licenseAccountType // 非企微环境删除账号类型参数
      this.loading = true
      const params = this.userListSearch
      if (xbb.isThirdPC(['h5'], true)) {
        params.showHidden = true
      }
      userList(this.userListSearch)
        .then((res) => {
          const licnseFormat = (arr) => {
            if (!arr) return
            const licnseObj = {}
            arr.forEach((item) => {
              // 这边后端给的是 [{ 38: 1 }, { 32: 1 }]，给不了{ 38: 1, 32: 1 }，前端这边自己转
              const data = Object.entries(item)
              const key = data[0][0]
              const value = data[0][1]
              licnseObj[key] = value
            })
            return licnseObj
          }
          this.userList = res.result.userList.map((item) => {
            item.licenseArray = licnseFormat(item.licenseArray)
            return {
              ...item
            }
          })
          this.rowsCount = res.result.pageHelper.rowsCount
          this.isWechatAdmin = res.result.isWechatAdmin
          // 清楚已选中的数据
          if (options.clearSelected) {
            this.clearSelection()
          } else {
            // 恢复已选中的数据
            this.recoverSelected()
          }
        })
        .finally(() => {
          this.loading = false
        })
        .catch(() => {})
    },
    bindCallback() {
      this.getUserList()
      this.getAccountUsage()
    },
    // getAccountUsage
    getAccountUsage() {
      // 企微获取基础/互通账号使用量
      if (!this.isQw) return
      try {
        getAccountUsage().then((res) => {
          this.accountUsage = { ...this.accountUsage, ...res.result }
          const flag =
            !this.accountUsage.baseUsage &&
            !this.accountUsage.externalContactUsage &&
            (this.accountUsage.baseCount || this.accountUsage.externalContactCount)
          if (flag) {
            this.isFirstBinding = true
          }
        })
      } catch (err) {
        console.log('ERR', err)
      }
    },

    // 节点点击事件
    nodeClick(node) {
      console.log('🚀 ~ nodeClick ~ node:', node)
      const { pid, text, routerIds, disabled } = node
      /**
       * @disabled 设置子管理员后新加字段，表示没有父节点权限
       */
      if (disabled) {
        console.log(**********)
        this.noAuthority = true
        return
      } else {
        this.noAuthority = false
      }
      if (this.userListParams.departmentId !== pid) {
        this.resetSearchParams()
        this.userListParams.departmentId = pid
        if (routerIds.split('|').length > 3) {
          this.departmentActive = text
        } else {
          this.departmentActive = this.$t('organization.wholeCompany')
        }
        this.getUserList()
      }
    },

    // 搜索
    handleSearch() {
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        // 搜索时，页码重置到第一页
        this.userListParams.page = 1
        this.getUserList()
      }, 500)
    },

    // 同步组织架构
    hanldeSync() {
      this.isSyncing = true
      departmentRefresh({})
        .then((res) => {
          this.isSyncing = false
          this.$message({
            type: 'success',
            message: '同步成功'
          })
          // this.isRefreshed()
        })
        .catch((res) => {
          this.isSyncing = false
        })
    },

    // 企微-同步账号
    hanldeSyncAccount() {
      this.isSyncingAccount = true
      syncAccount()
        .then((res) => {
          if (res.code === 1) {
            this.isSyncingAccount = false
            this.$message.success('账号同步成功')
            this.bindCallback()
          }
        })
        .catch((err) => {
          console.log(err)
          this.isSyncingAccount = false
        })
    },

    // 是否同步完成
    isRefreshed() {
      departmentRefreshStatus({})
        .then((res) => {
          // 同步状态，0.未开始同步，1.成功，2，正在同步中 >2 错误信息
          const syncCode = res.result.syncCode
          if (syncCode === 2) {
            setTimeout(() => {
              this.isRefreshed()
            }, 1000)
          } else {
            this.isSyncing = false
            if (syncCode === 1) {
              this.$message({
                type: 'success',
                message: this.$t('organization.syncComplete')
              })
              this.getDepartmentList()
              this.getUserList()
            } else if (syncCode > 2) {
              this.$message({
                type: 'warning',
                message: res.msg
              })
            }
          }
        })
        .catch((res) => {
          this.isSyncing = false
        })
    },

    // 监听批量操作添加或删除
    handleBatch(attr) {
      if (!this.getSelectRows.length) {
        this.$message({
          type: 'error',
          message: this.$t('organization.operateData')
        })
        return
      }
      this.batchOptionType = attr
      this.isBatcheditShow = true
    },
    // 打开唯一码弹窗
    editOauthUser(rowValue) {
      this.OauthUserShow = true
      this.oauthUserID = rowValue.userId
    },
    // 监听批量操作添加或删除标签数据
    labelHandleBatch(attr) {
      if (!this.getSelectRows.length) {
        this.$message({
          type: 'error',
          message: this.$t('organization.operateData')
        })
        return
      }
      this.batchOptionType = attr
      this.batchEditLabelShow = true
    },

    handleBatchBtn(type) {
      switch (type) {
        case 'addRole':
          // 新增角色
          this.handleBatch('add')
          break
        case 'deleteRole':
          // 删除角色
          this.handleBatch('delete')
          break
        case 'authorizedAccount':
          // 授权账户
          this.handleBatchAccount('add')
          break
        case 'removeAccount':
          // 移除账户
          this.handleBatchAccount('remove')
          break
        case 'accountSetting':
          // 账号设置
          this.batchAccountSetting()
          break
        case 'addLabel':
          // 添加标签
          this.labelHandleBatch('add')
          break
        case 'deleteLabel':
          // 删除标签
          this.labelHandleBatch('delete')
          break
        default:
          break
      }
    },

    operateHandler(type, row) {
      switch (type) {
        case 'assignRole':
          // 分配角色
          this.assignRole(row)
          break
        case 'authorizedAccount':
          // 授权账户
          this.authorizedAccount(row)
          break
        case 'seePermission':
          // 查看权限
          this.showRoles(row)
          break
        case 'handOver':
          // 交接
          this.handleHandover(row)
          break
        case 'editLabel':
          // 编辑标签
          this.openLabelDataDialog(row)
          break
        case 'accountSettingSet':
          // 授权企微账号
          this.accountSetting(row, 'set')
          break
        case 'accountSettingHandover':
          // 交接企微账号
          this.accountSetting(row, 'handover')
          break
        case 'editOauthUser':
          // 编辑标识
          this.editOauthUser(row)
          break
        default:
          break
      }
    },

    // 分配角色
    assignRole(row) {
      this.currentSelect = row
      this.isRolePermissionShow = true
    },

    // 交接
    handleHandover(row) {
      this.currentSelect = row
      this.isHandOverShow = true
    },
    // 账号设置
    accountSetting(row, type) {
      this.currentSelect = row
      // 账号使用量为0时初始化账号设置
      // if (!this.accountUsage.baseUsage && !this.accountUsage.externalContactUsage) {
      //   this.isFirstBinding = true
      // } else {
      //   this.isAccountSetting = true
      // }
      this.accountType = type
      this.isAccountSetting = true
      this.isBatchAccountSetting = false
    },

    // 批量设置账号
    batchAccountSetting() {
      if (!this.getSelectRows.length) {
        return this.$message({
          type: 'error',
          message: this.$t('organization.operateData')
        })
      }
      console.log('当前所选', this.getSelectRows)
      this.accountType = 'set'
      this.isBatchAccountSetting = true
      this.isAccountSetting = true
    },
    // 企微-表格内账户类型渲染
    licenseCodeTypeComputed(row) {
      if (!row.licenseCodeType || !row.licenseCodeType.length) {
        return '暂无'
      }
      const arr = []
      row.licenseCodeType.forEach((item) => {
        const type = item === 1 ? '基础账号' : '互通账号'
        arr.push(type)
      })
      return arr.join('、')
    },
    // 监听全选
    handleSelectAll(section) {
      // 新增
      if (section.length) {
        const operationType = 'add'
        section.forEach((row) => {
          this.setSelectRows({ operationType, row })
        })
      } else {
        // 删除
        const operationType = 'del'
        this.userList.forEach((row) => {
          this.setSelectRows({ operationType, row })
        })
      }
    },

    // 监听选择事件
    handleSelectChange(section, row) {
      const operationType = section.includes(row) ? 'add' : 'del'
      this.setSelectRows({ operationType, row })
    },

    // 清空已选中数据
    clearSelection() {
      this.$refs.table.clearSelection()
      this.setSelectRows({ operationType: 'clear' })
    },

    // 恢复已选中的数据
    recoverSelected(selectedRows = this.getSelectRows) {
      if (!selectedRows.length) return
      this.$nextTick(() => {
        this.userList.forEach((row) => {
          const selected = selectedRows.find((item) => item.id === row.id)
          this.$refs.table.toggleRowSelection(row, !!selected)
        })
      })
    },

    // 计算table高度
    countTableHeight() {
      const listHeight = document.getElementsByClassName('common-list')[0].offsetHeight
      const bottomHeight = document.getElementsByClassName('table-bottom')[0].offsetHeight
      let filterHeight = 0
      if (this.showFilter) {
        filterHeight = document.getElementsByClassName('table-filter')[0].offsetHeight
      }
      this.tableHeight = listHeight - bottomHeight - filterHeight - 20
    },

    // 翻页
    handleCurrentChange(val) {
      this.userListParams.page = val
      this.getUserList()
    },
    // 调整页码
    handleSizeChange(val) {
      this.userListParams.pageSize = val
      this.userListParams.page = 1
      this.getUserList()
    },
    showRoles(data) {
      console.log(data)
      this.showDialog = true
      this.dialogData = data
    },
    // 员工详情
    showEmployeeInfo(row) {
      this.dialogData = row
      this.showInfoDialog = true
    },
    //跳转到企微管理后台
    WxVisibleRangeDialogShow() {
      utils.openWXmanagementBg()
    },
    // 标签这一列所需的函数
    // 标签显示文本
    tagShowText(tagList) {
      return (
        (tagList &&
          tagList.length &&
          tagList.reduce((pre, cur) => {
            return pre + cur.name + ';'
          }, '')) ||
        ''
      )
    },
    tagCellWidth(event) {
      const e = event || window.event
      const children = e.target.children
      const totalWidth =
        children &&
        [].reduce.call(
          children,
          (pre, cur) => {
            return pre + cur.offsetWidth
          },
          0
        )
      this.tagToolTipShow = totalWidth <= e.target.offsetWidth - 10
    },
    openLabelDataDialog(row) {
      this.selectedRow = row.userId
      this.systemTagList = row.labelSet
      this.labelDataDialogShow = true
    },
    async oauthConnectorExist() {
      console.log('oauthConnectorExist')
      const { result } = await oauthConnectorExist()
      this.isHasConnector = result.hasConnector
    },
    pushTag(data) {
      const labels = []
      data.systemTag.forEach((item) => {
        labels.push(item.id)
      })
      // addPersonTag
      return new Promise((resolve) => {
        addPersonTag({ labels: labels, personId: this.selectedRow }).then((res) => {
          this.getUserList()
          resolve()
        })
      })
    },
    createTag() {},
    //免费版全选按钮隐藏
    cellClass(row) {
      if (row.columnIndex === 0 && this.feeType === -2) {
        return 'disableheadselection'
      }
    },
    filterDepartNode(value, data) {
      if (!value) return true
      return data.text.indexOf(value) !== -1
    },
    @xbb.debounceWrap(200)
    nodeFilterChangeHandler(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.init()
    this.watchTableHeight()
    this.oauthConnectorExist()
  },
  beforeDestroy() {
    // 取消监听事件的绑定
    if (this.tableResizeListener)
      removeResizeListener(this.$refs.listContent, this.tableResizeListener)
  }
}
</script>

<style lang="scss" scoped>
:deep(.disableheadselection > .cell .el-checkbox__inner) {
  display: none;
}
.organization-manage {
  height: 100%;
  overflow: hidden;
}
.organization-fee {
  color: $text-grey;
}
.organization-fee:hover {
  color: $brand-base-color-6;
  cursor: pointer;
}
.container-wrap {
  box-sizing: border-box;
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  height: calc(100% - 20px);
  padding: 0;
  margin: 10px;
  overflow: hidden;
  .set-text {
    float: left;
    margin-left: 10px;
    font-size: 12px;
    font-weight: normal;
    line-height: 30px;
    color: $link-base-color-6;
    cursor: pointer;
  }
  .container-header {
    // 页面头部
    position: relative;
    display: flex;
    flex: 0 0 50px;
    justify-content: space-between;
    height: 50px;
    padding-left: 20px;
    margin-bottom: 10px;
    font-size: 0;
    text-align: right;
    background-color: $base-white;
    :deep(.el-tabs__item) {
      height: 50px;
      line-height: 50px;
    }
    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }
    :deep(.staff-select) {
      display: inline-block;
    }
    .option-remind-time {
      display: inline-block;
      margin-top: 9px;
      margin-right: 25px;
      .ml10 {
        margin-left: 10px;
      }
    }

    .sub-head-item {
      display: inline-block;
      width: 200px;
      margin-right: 10px;
      text-align: left;
      vertical-align: top;
    }
    .setting-icon {
      margin-right: 8px;
      font-size: 20px;
      color: #d6d6d6;
      cursor: pointer;
    }
  }
  .container {
    display: flex;
    width: 100%;
    height: calc(100% - 60px);
    overflow: hidden;
  }
  .node-filter-input {
    width: 220px;
    height: 32px;
    margin: 0 10px 7px 10px;
  }

  .main-layout {
    width: 400px;
    .account-usage {
      float: right;
      width: 240px;
      height: 30px;
      font-size: 12px;
      line-height: 30px;
      color: #666;
    }
    .list-content {
      box-sizing: border-box;
      flex: 1 1 auto;
      width: 100%;
      padding: 0 10px;
      overflow: hidden;
      .unopened {
        text-align: center;
      }
      .v-cell {
        position: relative;
      }
      .iconCss {
        cursor: pointer;
      }
    }

    .table-bottom {
      position: relative;
      box-sizing: border-box;
      display: flex;
      flex: 0 0 56px;
      justify-content: space-between;
      height: 56px;
      padding-top: 12px;
      padding-left: 10px;
      overflow: hidden;

      .bottom-left {
        display: flex;
        flex: 1;
        align-items: center;
        height: 28px;
        overflow: hidden;
        font-size: 12px;
        .edit-selected {
          display: flex;
          flex: 0 0 auto;
          align-items: center;
          height: 28px;
          padding-right: 12px;
          margin-right: 12px;
          line-height: 16px;
          color: $text-plain;
          border-right: 1px solid $neutral-color-3;
          .count {
            padding: 0 4px;
            color: $link-base-color-6;
          }
          .cancel {
            padding-left: 12px;
            color: $link-base-color-6;
            cursor: pointer;
          }
        }
      }

      .bottom-right {
        display: flex;
        flex: 0 0 500px;
        justify-content: end;
      }
    }
    .table-bottom :deep(.el-pagination .number) {
      min-width: 24px;
      font-size: 12px;
    }
  }
}

.sync-setting-tips {
  font-size: 12px;
}

.table-filter-popover {
  .el-popover {
    min-width: 100px;
  }
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tips {
  box-sizing: border-box;
  width: 752px;
  padding: 20px;
  margin-top: 24px;
  line-height: 1.5;
  color: $text-main;
  background: $neutral-color-2;
  border-radius: 8px;
  .title {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
  }
  .special-wrap {
    display: flex;
    overflow: hidden;
    .img-wrap {
      box-sizing: border-box;
      width: 350px;
      height: 266px;
      padding: 10px;
      margin-top: 8px;
      background: $base-white;
      border-radius: 12px;
    }
  }
}
.tips-dialog {
  :deep(.el-dialog) {
    border-radius: 8px !important;
    .el-dialog__header {
      height: 48px;
      padding: 13px 24px;
      line-height: normal;
      background: $base-white;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      .el-dialog__title {
        font-size: 16px;
        line-height: normal;
      }
      .el-dialog__headerbtn {
        top: 16px;
      }
      .el-dialog__close {
        font-size: 20px;
      }
    }
    .el-dialog__body {
      box-sizing: border-box;
      max-height: 552px;
      padding: 0px 24px 24px 24px;
      overflow: auto;
      overflow-x: hidden;
    }
  }
}
.disable-set-dialog {
  :deep(.el-dialog) {
    border-radius: 8px !important;
    .el-dialog__header {
      height: 48px;
      padding: 13px 24px;
      line-height: normal;
      background: $base-white;
      border-top-left-radius: 8px !important;
      border-top-right-radius: 8px !important;
      .el-dialog__title {
        font-size: 16px;
        line-height: normal;
      }
      .el-dialog__headerbtn {
        top: 16px;
      }
      .el-dialog__close {
        font-size: 20px;
      }
    }
    .disable-set-body {
      .el-checkbox__label {
        font-size: 14px;
        font-weight: 600;
      }
      .disable-set-content {
        margin-top: 10px;
        line-height: 1.5;
      }
    }
  }
}
</style>

<style lang="scss">
.el-table__column-filter-trigger {
  margin-left: 60px !important;
}
.el-tree-node__label {
  overflow: hidden;
  text-overflow: ellipsis;
}
.fee-list {
  position: relative;
  padding: 8px 12px;
  .fee-list__desc {
    padding-bottom: 12px;
    font-size: 12px;
    line-height: 17px;
    color: $text-auxiliary;
    border-bottom: 1px solid $neutral-color-3;
  }
  .fee-list__item {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 20px;
    margin-top: 12px;
    .fee-list__item-title {
      font-size: 14px;
      line-height: 20px;
      color: $text-main;
    }
    .fee-list__item-fee {
      margin-left: 12px;
      font-size: 12px;
      line-height: 16px;
      color: $text-auxiliary;
    }
  }
}

.el-popover.popoverStyle {
  box-sizing: border-box;
  height: 228px;
  padding: 20px;
  margin: 0px;
  overflow: auto;
  overflow-x: hidden;
}
.group-role-hover {
  .group-name {
    margin-bottom: 4px;
    font-size: 14px;
    color: $text-auxiliary;
  }
  .tag-box {
    margin-bottom: 12px;
    .tag-box__item {
      margin-top: 8px;
      color: $text-main;
    }
  }
}
.structure-rules-popper {
  max-height: 600px;
  padding: 0px !important;
  overflow: scroll;
  .el-popover__title {
    padding: 13px 24px;
    border-bottom: 1px solid $neutral-color-2;
  }
}
</style>
