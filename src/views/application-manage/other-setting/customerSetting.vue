<!--
 * @Description: 客户公海池设置
-->

<template>
  <section class="customer-setting">
    <div class="content-column">
      <div class="side">
        <h3 class="title">
          <span class="name">{{ $t('otherSetting.setOption') }}</span>
        </h3>
        <ul class="cate-list">
          <li
            v-for="(rule, index) in ruleList"
            :key="index"
            class="item"
            :class="{ active: currentRule === rule.ruleType }"
            @click="itemDetail(rule.ruleType)"
          >
            <span class="name">{{ rule.typeName }}</span>
            <span
              v-if="rule.ruleType !== 7"
              class="status"
              :class="{ closeStatus: !rule.status }"
              >{{ rule.status ? $t('otherSetting.isOpen') : $t('otherSetting.isClose') }}</span
            >
          </li>
          <li v-if="!ruleList.length" class="no-data">{{ $t('message.noData') }}</li>
        </ul>
      </div>
      <div class="main">
        <div class="main-info">
          <h3 class="title">
            <span class="name">{{ typeName }}</span>
          </h3>
          <div v-if="![7].includes(currentRule)" class="tips">
            <p class="item">{{ $t('otherSetting.Pa') }}</p>
            <p class="item">{{ $t('otherSetting.Pa2', { attr: sendBackRemindDays }) }}</p>
            <p class="tip">{{ $t('otherSetting.tips') }}</p>
          </div>
          <!-- 不退回客户复选框部分 开始 -->
          <div v-if="![5, 6, 7].includes(currentRule)" class="notSendBackCus">
            <h2>{{ $t('otherSetting.notBack') }}</h2>
            <el-form label-width="80px" :model="getForm">
              <el-form-item :label="$t('otherSetting.contractSituation')">
                <el-checkbox
                  v-model="getForm.ruleValue.contract"
                  :false-label="0"
                  :label="$t('otherSetting.haveContract')"
                  :true-label="1"
                ></el-checkbox>
              </el-form-item>
              <el-form-item :label="$t('otherSetting.saleStatus')">
                <el-checkbox
                  v-model="getForm.ruleValue.opportunity"
                  :false-label="0"
                  :label="$t('otherSetting.saleChanceCustomer')"
                  :true-label="1"
                ></el-checkbox>
              </el-form-item>
              <el-form-item :label="$t('label.customerStatus')">
                <el-checkbox-group
                  v-model="getForm.ruleValue.customerStatus"
                  @change="updataCustomerCode"
                >
                  <el-checkbox
                    v-for="(item, key, index) in getForm.typeArray"
                    :key="key + index"
                    :label="key"
                    name="customerStatus"
                    >{{ item }}</el-checkbox
                  >
                </el-checkbox-group>
              </el-form-item>
              <el-form-item :label="$t('label.customerLabel')">
                <v-tag
                  v-for="tag in clueLabelList"
                  :key="tag.id"
                  closable
                  :color="tag.color"
                  :content="tag.formName ? `${tag.formName}/${tag.name}` : tag.name"
                  disable-transitions
                  @close="deleteTag(index)"
                />
                <el-button @click="tagAddDialogShow = true">
                  <i class="el-icon-plus"></i> {{ $t('formDataEdit.addTag') }}
                </el-button>
              </el-form-item>
            </el-form>
            <!-- 无新建xx规则 最大拥有客户数规则 开始 -->
            <div v-if="[4].includes(currentRule)" class="setupCustomer">
              <div v-if="currentRule === 4" class="rule-second-title">
                {{ $t('otherSetting.maxNumberLimit') }}
              </div>
              <div class="rule-table">
                <el-radio-group v-model="type" @change="changeType">
                  <div
                    v-for="(item, index) in childrenTableType"
                    :key="item.key + index"
                    class="rule-table-cell"
                  >
                    <el-radio :label="item.key">
                      <span>{{ item.value }}</span>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
              <template v-if="type === choosedKey">
                <!-- 客户标签 -->
                <template v-if="[1, 2, 3, 4].includes(currentRule) && type === 3">
                  <el-table
                    border
                    class="current-table"
                    :data="clueLabelTable"
                    :span-method="mergeLine"
                    style="width: 1150px"
                  >
                    <el-table-column :label="$t('label.label')" width="400">
                      <template slot-scope="scope">
                        <div class="item-select">
                          <span v-if="!scope.row.label.length" class="item-select-hint">
                            {{ $t('marketManage.chooseLabel') }}
                          </span>
                          <template v-else>
                            <v-tag
                              v-for="tag in scope.row.label"
                              :key="tag.id"
                              closable
                              :color="tag.color"
                              :content="tag.formName ? `${tag.formName}/${tag.name}` : tag.name"
                              disable-transitions
                              @close="deleteLabel(index, scope.$index)"
                            />
                          </template>
                          <el-button
                            class="select-btn"
                            type="text"
                            @click="chooseLabel(scope.$index)"
                            >{{ $t('operation.choose') }}</el-button
                          >
                        </div>
                      </template>
                    </el-table-column>

                    <!-- 无新建xxx线索规则 -->
                    <el-table-column
                      v-if="[1, 2, 3].includes(currentRule)"
                      align="center"
                      :label="$t('otherSetting.noFollowDay')"
                      props="value"
                      width="250"
                    >
                      <template slot-scope="scope">
                        <span
                          >{{ $t('otherSetting.moreThan') }}
                          <el-input v-model.number="scope.row.value" size="mini"> </el-input>
                          <span v-if="currentRule === 1">{{ $t('otherSetting.backToSea') }}</span>
                          <span v-if="currentRule === 2">{{
                            $t('otherSetting.noChanceBackToSea')
                          }}</span>
                          <span v-if="currentRule === 3">{{
                            $t('otherSetting.noContractBackToSea')
                          }}</span>
                        </span>
                      </template>
                    </el-table-column>

                    <!-- 最大拥有线索数量规则 -->
                    <el-table-column
                      v-if="currentRule === 4"
                      align="center"
                      :label="$t('otherSetting.maxClue')"
                      width="300"
                    >
                      <template slot-scope="scope">
                        <span
                          >{{ $t('otherSetting.staffMaxCustomer') }}
                          <el-input v-model.number="scope.row.value" size="mini"> </el-input>
                          {{ $t('unit.an') }}
                        </span>
                      </template>
                    </el-table-column>

                    <el-table-column
                      align="center"
                      :label="$t('otherSetting.applyStaff')"
                      prop="users"
                      width="300"
                    >
                      <template slot-scope="scope">
                        <span
                          >{{ $t('otherSetting.theRule') }}
                          <template v-if="scope.row.users">
                            <div class="item-select">
                              <span v-if="!scope.row.users.length" class="item-select-hint">{{
                                $t('placeholder.plsChoose')
                              }}</span>
                              <span
                                v-for="(item, index) in scope.row.users"
                                v-else
                                :key="item.id"
                                class="item"
                              >
                                <span
                                  class="text"
                                  v-html="addNodeByWX(item.name, item.property === 'dept' ? 1 : 0)"
                                ></span>
                                <i
                                  class="del el-icon-close"
                                  @click="delScopeRule(index, scope.$index)"
                                ></i>
                              </span>
                              <el-button
                                class="select-btn"
                                type="text"
                                @click="handleSelect(scope.$index)"
                                >{{ $t('operation.choose') }}</el-button
                              >
                            </div>
                          </template>
                          <span v-else>{{ $t('otherSetting.outOfSpecial') }}</span>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" :label="$t('operation.operate')">
                      <template slot-scope="scope">
                        <a
                          style="margin-right: 5px; color: #ff8e3d"
                          @click="addRule(scope.$index, clueLabelTable)"
                        >
                          {{ $t('marketManage.newRule') }}
                        </a>
                        <template v-if="scope.$index === 0">
                          <a style="color: #c8c9cc; cursor: not-allowed">
                            {{ $t('operation.delete') }}
                          </a>
                        </template>
                        <template v-else>
                          <a style="color: red" @click="deleteRule(scope.$index, clueLabelTable)">
                            {{ $t('operation.delete') }}
                          </a>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="add-special">
                    <el-button
                      icon="el-icon-plus"
                      size="small"
                      type="text"
                      @click.native="addLabelRow"
                    >
                      {{ $t('otherSetting.addSpecial') }}
                    </el-button>
                  </div>
                </template>
                <template v-else>
                  <el-table
                    border
                    class="current-table"
                    :data="currentTable"
                    :span-method="combineColumn"
                    style="width: 1000px"
                  >
                    <el-table-column :label="$t('nouns.customer')" width="200">
                      <template slot-scope="scope">
                        <span v-if="scope.row.key === 0 || scope.row.key === '0'">{{
                          $t('otherSetting.allCustomer')
                        }}</span>
                        <span v-if="type === 1">{{ getStatus(scope.row.key) }}</span>
                        <span v-if="type === 2">{{ getStars(scope.row.key) }}</span>
                      </template>
                    </el-table-column>

                    <!-- 无新建xxx客户规则 -->
                    <el-table-column
                      v-if="[1, 2, 3].includes(currentRule)"
                      align="center"
                      :label="$t('otherSetting.noFollowDay')"
                      width="300"
                    >
                      <template slot-scope="scope">
                        <span
                          >{{ $t('otherSetting.moreThan') }}
                          <el-input
                            v-model.number="scope.row.value"
                            :disabled="
                              getForm.ruleValue.customerStatus.includes(scope.row.key) && type === 1
                            "
                            size="mini"
                          >
                          </el-input>
                          <span v-if="currentRule === 1">{{ $t('otherSetting.backToSea') }}</span>
                          <span v-if="currentRule === 2">{{
                            $t('otherSetting.noChanceBackToSea')
                          }}</span>
                          <span v-if="currentRule === 3">{{
                            $t('otherSetting.noContractBackToSea')
                          }}</span>
                        </span>
                      </template>
                    </el-table-column>

                    <!-- 最大拥有客户数量规则 -->
                    <el-table-column
                      v-if="currentRule === 4"
                      align="center"
                      :label="$t('otherSetting.maxCustomer')"
                      width="300"
                    >
                      <template slot-scope="scope">
                        <span
                          >{{ $t('otherSetting.staffMaxCustomer') }}
                          <el-input
                            v-model.number="scope.row.value"
                            :disabled="
                              getForm.ruleValue.customerStatus.includes(scope.row.key) && type === 1
                            "
                            size="mini"
                          >
                          </el-input>
                          {{ $t('unit.an') }}
                        </span>
                      </template>
                    </el-table-column>

                    <el-table-column
                      align="center"
                      :label="$t('otherSetting.applyStaff')"
                      prop="users"
                      width="300"
                    >
                      <template slot-scope="scope">
                        <span
                          >{{ $t('otherSetting.theRule') }}
                          <template v-if="scope.row.users">
                            <div class="item-select">
                              <span v-if="!scope.row.users.length" class="item-select-hint">{{
                                $t('placeholder.plsChoose')
                              }}</span>
                              <span
                                v-for="(item, index) in scope.row.users"
                                v-else
                                :key="item.id"
                                class="item"
                              >
                                <span
                                  class="text"
                                  v-html="addNodeByWX(item.name, item.property === 'dept' ? 1 : 0)"
                                ></span>
                                <i
                                  class="del el-icon-close"
                                  @click="delScopeRule(index, scope.$index)"
                                ></i>
                              </span>
                              <el-button
                                class="select-btn"
                                type="text"
                                @click="handleSelect(scope.$index)"
                                >选择</el-button
                              >
                            </div>
                          </template>
                          <span v-else>{{ $t('otherSetting.outOfSpecial') }}</span>
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" :label="$t('operation.operate')">
                      <template slot-scope="scope">
                        <span v-if="scope.row.users">
                          <a style="color: red" @click="deleteList(scope.$index, currentTable)">{{
                            $t('operation.delete')
                          }}</a></span
                        >
                        <span v-else>{{ $t('otherSetting.cannotOperate') }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="add-special">
                    <el-button icon="el-icon-plus" size="small" type="text" @click.native="addRow">
                      {{ $t('otherSetting.addSpecial') }}
                    </el-button>
                  </div>
                </template>
              </template>
              <template v-if="currentRule === 4">
                <div class="rule-second-title">{{ $t('otherSetting.backRules') }}</div>
                <div class="rule-table">
                  <el-radio-group v-model="backType">
                    <div class="rule-table-cell">
                      <el-radio :label="1">
                        <span>{{ $t('otherSetting.limitToGet') }}</span>
                      </el-radio>
                    </div>
                    <div class="rule-table-cell">
                      <el-radio :label="2">
                        <span
                          >{{ $t('otherSetting.byStatus') }}
                          <el-tooltip
                            :content="$t('otherSetting.notSetStatus')"
                            placement="top-start"
                          >
                            <i class="backtip el-icon-question"></i>
                          </el-tooltip>
                        </span>
                      </el-radio>
                    </div>
                    <div class="rule-table-cell">
                      <el-radio :label="3">
                        <span
                          >{{ $t('otherSetting.byLevel') }}
                          <el-tooltip
                            :content="$t('otherSetting.notSetStar')"
                            placement="top-start"
                          >
                            <i class="backtip el-icon-question"></i>
                          </el-tooltip>
                        </span>
                      </el-radio>
                    </div>
                  </el-radio-group>
                  <ul v-if="backType === 2" class="backRuleList">
                    <li class="back-rule-list-title">
                      <span>{{ $t('label.customerStatus') }}</span>
                      <i>{{ $t('otherSetting.backOrder') }}</i>
                    </li>
                    <VueDraggable v-model="backRule[2]" v-draggable>
                      <li v-for="(item, index) in backRule[2]" :key="'backRule2' + index">
                        <span>{{ getStatus(item) }}</span>
                        <i class="drag el-icon-sort icon"></i>
                      </li>
                    </VueDraggable>
                  </ul>
                  <ul v-if="backType === 3" class="backRuleList">
                    <li class="back-rule-list-title">
                      <span>{{ $t('otherSetting.customerStar') }}</span>
                      <i>{{ $t('otherSetting.backOrder') }}</i>
                    </li>
                    <VueDraggable v-model="backRule[3]" v-draggable>
                      <li v-for="(item, index) in backRule[3]" :key="'backRule3' + index">
                        <span>{{ getStarsSort(item) }}</span>
                        <i class="drag el-icon-sort icon"></i>
                      </li>
                    </VueDraggable>
                  </ul>
                </div>
              </template>
            </div>
          </div>
          <!-- 不退回客户复选框部分 结束 -->

          <!-- 前负责人规则 捞取频率限制规则 模块 开始 -->
          <div v-if="[5, 6].includes(currentRule)">
            <el-table border class="current-table" :data="oldLeaderOrFrequency">
              <el-table-column
                align="center"
                :label="$t('otherSetting.dayNumber')"
                prop="value"
                width="300px"
              >
                <template slot-scope="scope">
                  <span v-if="currentRule === 5"
                    >{{ $t('otherSetting.exCharge') }}
                    <el-input v-model.number="scope.row.value" size="mini"></el-input>
                    {{ $t('otherSetting.notAllowRob') }}
                  </span>
                  <span v-else
                    >{{ $t('otherSetting.mostOneDay') }}
                    <el-input v-model.number="scope.row.value" size="mini"></el-input>
                    {{ $t('otherSetting.oneCustomer') }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                :label="$t('otherSetting.applyStaff')"
                prop="users"
                width="400px"
              >
                <template slot-scope="scope">
                  <span
                    >{{ $t('otherSetting.theRule') }}
                    <template v-if="scope.row.users">
                      <div class="item-select">
                        <span v-if="!scope.row.users.length" class="item-select-hint">{{
                          $t('placeholder.plsChoose')
                        }}</span>
                        <span
                          v-for="(item, index) in scope.row.users"
                          v-else
                          :key="item.id"
                          class="item"
                        >
                          <span
                            class="text"
                            v-html="addNodeByWX(item.name, item.property === 'dept' ? 1 : 0)"
                          ></span>
                          <i
                            class="del el-icon-close"
                            @click="delScopeRule(index, scope.$index)"
                          ></i>
                        </span>
                        <el-button
                          class="select-btn"
                          type="text"
                          @click="handleSelect(scope.$index)"
                          >选择</el-button
                        >
                      </div>
                    </template>
                    <span v-else>{{ $t('otherSetting.outOfSpecial') }}</span>
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" :label="$t('operation.operate')" prop="users">
                <template slot-scope="scope">
                  <span v-if="scope.row.users">
                    <a style="color: red" @click="deleteList(scope.$index, oldLeaderOrFrequency)">{{
                      $t('operation.delete')
                    }}</a></span
                  >
                  <span v-else>{{ $t('otherSetting.cannotOperate') }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="add-special">
              <el-button icon="el-icon-plus" size="small" type="text" @click.native="addRow">
                {{ $t('otherSetting.addSpecial') }}
              </el-button>
            </div>
          </div>

          <div v-if="[7].includes(currentRule)">
            <h3 class="time-title">{{ $t('otherSetting.backRemindSet') }}</h3>
            <div class="time-content">
              <span>{{ $t('otherSetting.beforeBack') }}</span>
              <el-select v-model="sendBackRemindDays" style="width: 96px">
                <el-option v-for="i in 5" :key="i" :label="i" :value="i"></el-option>
              </el-select>
              <span>{{ $t('otherSetting.remindCharge') }}</span>
            </div>
          </div>
        </div>
        <div class="main-btn">
          <el-button :disabled="saveHigh" type="primary" @click="handleSave">
            {{ $t('operation.save') }}
          </el-button>
          <!-- <el-button @click="getLeftList">{{ $t('operation.cancel') }}</el-button> -->
          <el-button v-if="!enable && currentRule !== 7" @click.native="handleOpenItemGroup">{{
            $t('otherSetting.open')
          }}</el-button>
          <el-button
            v-if="enable && currentRule !== 7"
            type="danger"
            @click.native="handleOpenItemGroup"
            >{{ $t('otherSetting.close') }}</el-button
          >
        </div>
      </div>
    </div>
    <!-- 不退回线索选择 选择标签 -->
    <addTagDialog
      v-if="tagAddDialogShow"
      :business-type="100"
      :limit-flag="true"
      :limit-num="50"
      :select-tag-list="clueLabelList"
      :show.sync="tagAddDialogShow"
      :template-list="formList"
      @dialogSubmit="dialogSubmit"
    >
    </addTagDialog>
    <!-- 退回线索选择 选择标签 -->
    <addTagDialog
      v-if="labelDialogShow"
      :business-type="100"
      :limit-flag="true"
      :limit-num="50"
      :select-tag-list="chooseLabelList"
      :show.sync="labelDialogShow"
      :template-list="formList"
      @dialogSubmit="chooseLabelSubmit"
    >
    </addTagDialog>
  </section>
</template>

<script>
import { getPublicRuleDetail, savePublicRule } from '@/api/publicRuleSet'
import {
  getLeftGroupList,
  openItemGroup,
  getCommonRemindTime,
  saveRemindTime
} from '@/api/publicGroupSet'
import { Message } from 'element-ui'
import PersonSelect from '@/components/person-select/index'
import addTagDialog from './add-tag-dialog'
import seasPoolMixin from '@/mixin/seas-pool-mixin.js'
import VTag from '@/components/base/v-tag.vue'

export default {
  name: 'CustomerSetting',
  components: {
    AddTagDialog: addTagDialog,
    VTag
  },
  mixins: [seasPoolMixin],
  props: {
    isEditStatus: {
      type: Boolean,
      default: false
    },
    businessType: {
      type: Number
    },
    activeTabIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      sendBackRemindDays: 1, // 退回公海池提醒天数
      enable: false, // 是否启用分组
      getData: '', // 详情页接口的数据
      commomRuleId: 1, // 审批通用设置id
      ruleList: [], // 审批列表
      currentRule: 1, // 当前选中的设置规则
      tip: '', // 设置switch的提示文本
      status: 0, // 规则开启状态
      typeName: '', // 规则的名称
      userList: [], // 获取到的联系人列表
      selectUsers: [], // 保存选中的联系人
      getForm: {
        ruleValue: {
          contract: 0,
          opportunity: 0,
          customerStatus: []
        }
      }, // 获取的详情页数据
      setupType: '', // 客户选择类型
      oldLeaderOrFrequency: [], // 前负责人或者捞取频次
      backType: 1, // 退回的类型
      backRule: {
        1: [],
        2: [], // 客户状态退回排序
        3: [] // 客户星级退回排序
      },
      getSetupType: {},
      formList: [], // 线索模板的数据
      clueLabelTable: [
        {
          label: [],
          value: '14',
          users: null
        }
      ] // 根据线索标签表格
    }
  },
  computed: {
    getValue4InputDetail() {
      // 根据不同规则，设置不同的默认人数天数
      let value
      switch (this.currentRule) {
        case 4:
          value = 100
          break
        case 5:
          value = 7
          break
        case 6:
          value = 10
          break
        default:
          value = 14
      }
      return value
    }
  },
  watch: {
    sendBackRemindDays: {
      handler() {
        console.log('sendBackRemindDays', this.getReady)
        if (this.getReady) {
          this.saveHigh = false
        }
      }
    },
    oldLeaderOrFrequency: {
      handler() {
        if (this.getReady) {
          this.saveHigh = false
        }
      },
      deep: true
    },
    currentRule: {
      handler(val) {
        this.saveHigh = true
        this.getReady = false
      }
    }
  },
  created() {
    this.getLeftList()
    this.pushMergeNum() // 处理线索状态数据
    this.currentRule = 4
  },
  methods: {
    warnTipsStyle(index) {
      let res = {
        display: 'none'
      }
      if (this.emptySave && this.emptyIndex.includes(index)) {
        res = {
          color: '#F7716C',
          display: 'inline-block'
        }
      }
      return res
    },
    changeValue(index) {
      if (!this.emptySave) {
        return false
      }
      const nowIndex = this.emptyIndex.findIndex((item) => item === index)
      if (nowIndex > -1) {
        this.emptyIndex.splice(nowIndex, 1)
      }
      !this.emptyIndex.length && (this.emptySave = false)
    },
    handleOpenItemGroup() {
      openItemGroup({
        type: this.currentRule,
        businessType: this.businessType,
        tabType: 5,
        status: +!this.enable
      })
        .then((res) => {
          Message({
            type: 'success',
            message: res.msg || this.$t('message.saveSuccess')
          })
          this.getLeftList()
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取选中的分组权限
    listSubmit(data) {
      this.scopeRule = data
      if ([5, 6].includes(this.currentRule)) {
        this.oldLeaderOrFrequency[this.scopeIndex].users = data
      } else if ([1, 2, 3, 4].includes(this.currentRule) && this.type === 3) {
        this.clueLabelTable[this.scopeIndex].users = data
      } else {
        this.currentTable[this.scopeIndex].users = data
      }
    },
    // 打开分组权限的弹框
    handleSelect($index) {
      if ([5, 6].includes(this.currentRule)) {
        this.scopeRule = this.oldLeaderOrFrequency[$index].users
      } else if ([1, 2, 3, 4].includes(this.currentRule) && this.type === 3) {
        this.scopeRule = this.clueLabelTable[$index].users
      } else {
        this.scopeRule = this.currentTable[$index].users
      }
      this.scopeIndex = $index
      // this.listSelectShow = true
      // this.listSelectShow = true
      PersonSelect({
        activeTabName: 'dept',
        showTabsName: ['dept', 'user', 'role'],
        defaultVal: this.scopeRule,
        otherParams: {
          // 是否控制组织架构权限范围
          deptVisibleRule: true,
          parentStrictly: true
        }
      }).then((res) => {
        if (res.data) {
          this.listSubmit(res.data)
        }
      })
    },
    // 删除选中的分组权限
    delScopeRule(index, $index) {
      if ([5, 6].includes(this.currentRule)) {
        this.oldLeaderOrFrequency[$index].users.splice(index, 1)
      } else if ([1, 2, 3, 4].includes(this.currentRule) && this.type === 3) {
        this.clueLabelTable[$index].users.splice(index, 1)
      } else {
        this.currentTable[$index].users.splice(index, 1)
      }
    },
    deleteList(index, data) {
      // 删除特殊规则
      let length = 1
      if (this.type === 1) {
        length = this.detailTable1.length
      } else if (this.type === 2) {
        length = this.detailTable2.length
      }
      data.splice(index, length)
    },
    getStars(index) {
      // 渲染客户星级表格
      let value = ''
      this.getForm.importantDegreeArray.forEach((item) => {
        if (Number(item.key) === Number(index)) {
          value = item.value
        }
      })
      return value
    },
    getStarsSort(index) {
      let value = ''
      this.getForm.importantDegreeArraySort.forEach((item) => {
        if (Number(item.key) === Number(index)) {
          value = item.value
        }
      })
      return value
    },
    updataCustomerCode(value) {
      this.saveHigh = false
      // 根据不退回客户状态的选中与否，改变下面根据客户状态设置表格的可否编辑
      if (!value) value = []
      if (this.type === 1) {
        const keys = Object.keys(this.getForm.typeArray) // 客户状态key的数组
        const arr = keys.filter((item) => value.indexOf(item) < 0) // 客户状态有哪些没有选中
        this.currentTable.forEach((item) => {
          if (arr.includes(item.key) && item.value === null) {
            // 如果存在没有选中且值为null则给默认值14
            item.value = 14
          }
          if (this.getForm.ruleValue.customerStatus.includes(item.key)) {
            // 从客户状态checkbox选中的数组里检索，从而确定天数输入框的状态
            item.value = null
          }
        })
      }
    },
    addRow() {
      // 新增特殊员工
      const newTable = []
      if ([5, 6].includes(this.currentRule)) {
        this.oldLeaderOrFrequency.push({
          value: this.getValue4InputDetail,
          users: []
        })
      } else {
        this[this.detailTableNames[this.type]].forEach((item) => {
          if (this.type === 1 && this.getForm.ruleValue.customerStatus.includes(item.key)) {
            newTable.push({
              key: item.key,
              value: null,
              users: []
            })
          } else {
            newTable.push({
              key: item.key,
              value: item.value,
              users: []
            })
          }
        })
        this.currentTable = this.currentTable.concat(newTable)
      }
    },
    getLeftList() {
      // 获取左侧列表信息
      getLeftGroupList({
        businessType: this.businessType,
        tabType: 5
      })
        .then((data) => {
          console.log(data)
          this.ruleList = data.result.ruleList
          this.ruleList.forEach((v, i) => {
            if (v.day) {
              this.ruleList[i] = {
                name: v.name,
                typeName: v.typeName,
                ruleType: 7,
                status: 0
              }
            }
          })
          this.formList = data.result.formList
          this.openDetail(this.currentRule)
        })
        .catch(() => {})
    },
    getRemindTime() {
      // 回显提示中的天数
      getCommonRemindTime({
        businessType: 100
      })
        .then((data) => {
          this.sendBackRemindDays = data.result.backDay
          this.$nextTick(() => {
            this.getReady = true
          })
        })
        .catch(() => {})
    },
    transformTable() {
      // 转化请求到的数据成table组件需要的格式
      if (!this.getForm.ruleValue) {
        // 服务端没有存数据，新打开规则的默认值显示
        this.getForm.ruleValue = {
          // 初始化默认值
          contract: 0,
          customerStatus: [],
          label: [],
          restrict: [
            {
              array: JSON.parse(JSON.stringify(this.detailTable0))
            }
          ],
          opportunity: 0,
          type: 0
        }
      }
      if (this.getForm.ruleValue.backRule) {
        // 最大拥有客户数规则，退回排序存在时
        const rule = this.getForm.ruleValue.backRule
        this.backType = rule.backType // 退回类型
        if (rule.sort.length) {
          // 若数组不为空，则使用排序，否则走默认
          this.backRule[this.backType] = rule.sort // 退回顺序
        }
      } else {
        // 最大拥有客户数规则，退回排序不存在时
        this.backType = 1
      }
      this.clueLabelList = this.getForm.ruleValue.label || [] // 线索标签对应值
      this.type = this.getForm.ruleValue.type // 规则类型
      this.cache = this.type // 缓存
      if ([1, 2, 3, 4].includes(this.currentRule) && this.type === 3) {
        this.clueLabelTable = []
        this.clueLabelMergeRule = []
        this.getForm.ruleValue.restrict.forEach((item, index) => {
          this.clueLabelMergeRule.push(item.array.length)
          item.array.forEach((el, i) => {
            const obj = {
              users: item.users ? item.users : null,
              label: JSON.parse(el.key),
              value: el.value
            }
            if (i === 0) {
              obj.mergeNum = item.array.length
            }
            this.clueLabelTable.push(obj)
          })
        })
      } else {
        this.currentTable = []
        this.getForm.ruleValue.restrict.forEach((item, index) => {
          item.array.forEach((value, index) => {
            const newObj = {
              users: item.users ? item.users : null,
              key: value.key
            }
            if (this.type === 1 && this.getForm.ruleValue.customerStatus.includes(value.key)) {
              // 从客户状态checkbox选中的数组里检索，从而确定天数输入框的状态
              newObj.value = null
            } else {
              newObj.value = value.value
            }
            this.currentTable.push(newObj)
          })
        })
      }
    },
    transformSpecialTable() {
      // 初始化或者获取后端数据，渲染最大拥有客户数 下面的退回规则表格
      this.oldLeaderOrFrequency = [] // 初始化数组
      if (this.getData.ruleValue) {
        this.getData.ruleValue.forEach((item) => {
          this.oldLeaderOrFrequency.push({
            value: item.value,
            users: item.users || null
          })
        })
      } else {
        this.oldLeaderOrFrequency = [
          {
            value: this.getValue4InputDetail,
            users: null
          }
        ]
      }
    },
    // 点击每个设置选项
    itemDetail(ruleType) {
      if (!this.saveHigh) {
        this.$confirm(this.$t('seasPool.leaveTip'), this.$t('message.confirmTitle'), {
          confirmButtonText: this.$t('formDataDetail.confirm'),
          cancelButtonText: this.$t('operation.cancel'),
          type: 'warning'
        })
          .then((res) => {
            this.openDetail(ruleType)
          })
          .catch(() => {})
      } else {
        this.openDetail(ruleType)
      }
    },
    // 展开右侧详情
    openDetail(id) {
      this.getReady = false
      // id是传入的ruleType ,新打开默认传入1，点击左侧列表传入相应ruleType
      this.ruleList.forEach((item, index) => {
        // 右侧列表状态同步到详情页
        if (item.ruleType === id) {
          this.status = item.status
          this.typeName = item.typeName
          this.currentRule = item.ruleType
          this.tip = item.tip
          this.enable = item.status
        }
      })
      const params = {
        ruleType: id
      }
      // 初始化
      this.clueLabelTable = [
        {
          label: [],
          value: '14',
          users: null,
          mergeNum: 1
        }
      ]
      this.clueLabelMergeRule = [1]
      if (this.currentRule === 7) {
        this.getRemindTime()
      } else {
        getPublicRuleDetail(params)
          .then((data) => {
            // 获取详情页其它信息
            this.getData = data.result
            if ([5, 6].includes(this.currentRule)) {
              // 前负责人和捞取频率时，使用的表格数据
              this.transformSpecialTable() // 转化数据与设置默认值
              this.$nextTick(() => {
                this.getReady = true
              })
            } else {
              // 其它前四个使用的表格数据
              this.getForm = data.result
              this.choosedKey = data.result.ruleValue.type
              this.childrenTableType = this.getForm.childTypeArray // 表格的名称和对应值
              this.exchangeAllCustermer() // 所有客户统一设置
              this.exchangeTypeArray() // 将客户状态列表保存
              this.exchangeImportantDegreeArray() // 将客户重要程度列表保存
              this.transformTable() // 将数据转成ui支持的格式
              this.$nextTick(() => {
                this.getReady = true
              })
            }
          })
          .catch(() => {})
      }
    },
    exchangeTypeArray() {
      // 保存获取的客户状态的列表，以便新增特殊用户的时候渲染同样一份
      this.detailTable1 = []
      this.backRule[2] = []
      for (const key in this.getForm.typeArray) {
        this.detailTable1.push({
          key: key,
          value: this.getValue4InputDetail,
          users: null
        })
        this.backRule[2].push(key)
      }
      this.cacheTable1 = JSON.parse(JSON.stringify(this.detailTable1))
    },
    exchangeImportantDegreeArray() {
      // 保存获取的客户星级的列表，以便新增特殊用户的时候渲染同样一份
      this.detailTable2 = []
      this.backRule[3] = []
      this.getForm.importantDegreeArray.forEach((item, index) => {
        this.detailTable2.push({
          key: item.key,
          value: this.getValue4InputDetail,
          users: null
        })
        this.backRule[3].push(item.key)
      })
      this.cacheTable2 = JSON.parse(JSON.stringify(this.detailTable2))
    },
    isEmptyParams(params) {
      this.emptyIndex = []
      const isEmpty =
        params.ruleValue.restrict.map((res, resIndex) => {
          const firstLength = res.array.length
          return res.array.filter((arr, index) => {
            if (arr.value === '') {
              if (resIndex > 0) {
                this.emptyIndex.push(index + firstLength * resIndex)
              } else {
                this.emptyIndex.push(index)
              }
            }
            return arr.value === ''
          })
        }) || []
      return isEmpty.some((empty) => empty.length)
    },
    // 处理params数据
    exchangeParams() {
      const rule = {
        // 需要保存的规则数据
        status: this.status, // 规则开启状态
        ruleType: this.currentRule, // 规则类型
        ruleValue: {
          contract: this.getForm.ruleValue.contract, // 保存不退回客户选择 合同状态
          opportunity: this.getForm.ruleValue.opportunity, // 保存不退回客户选择 销售情况
          customerStatus: this.getForm.ruleValue.customerStatus || [], // 保存不退回客户选择 客户状态
          label: this.clueLabelList.map((item) => item.id) || [], // 保存不退回客户标签
          type: this.type, // 根据客户类型设置
          restrict: [] // 表格设置的数据
        }
      }
      if (this.currentRule === 4) {
        // 最大拥有客户数规则时，多传一个backRule对象，包含了退回的排序
        switch (this.backType) {
          case 1:
            rule.ruleValue.backRule = {
              // 禁止捞取
              backType: 1,
              sort: []
            }
            break
          case 2:
            rule.ruleValue.backRule = {
              // 根据客户状态退回
              backType: 2,
              sort: this.backRule[2]
            }
            break
          case 3:
            rule.ruleValue.backRule = {
              // 根据客户星级退回
              backType: 3,
              sort: this.backRule[3]
            }
            break
        }
      }
      const everyRuleObj = {
        array: []
      }
      const deleteUsers = ''
      if ([1, 2, 3, 4].includes(this.currentRule) && this.isSettingTag) {
        this.dealTagParams(everyRuleObj, deleteUsers, rule)
      } else {
        this.currentTable.forEach((item, index) => {
          if (this.type === 0) {
            // 所有客户统一设置
            this.dealSameParams(everyRuleObj, deleteUsers, rule, item)
          } else if (this.type === 1) {
            // 根据客户状态设置
            this.dealStatusParams(everyRuleObj, deleteUsers, rule, item, index)
          } else if (this.type === 2) {
            // 根据客户星级设置
            this.dealStarParama(everyRuleObj, deleteUsers, rule, item, index)
          }
        })
      }
      return rule
    },
    // 保存接口
    handleSave() {
      if (this.clueLabelTable.length > 50) {
        Message({
          message: this.$t('marketManage.ruleNoMore'),
          type: 'error'
        })
      } else {
        let params
        if ([5, 6].includes(this.currentRule)) {
          // 前负责人和捞取频率保存数据
          params = {
            status: this.status, // 规则开启状态
            ruleType: this.currentRule, // 规则类型
            leadOrFre: this.oldLeaderOrFrequency
          }
        } else {
          params = this.exchangeParams()
          if (this.isEmptyParams(params)) {
            Message({
              message: this.$t('otherSetting.noneTip'),
              type: 'error'
            })
            this.emptySave = true
            return
          }
        }
        if ([7].includes(this.currentRule)) {
          saveRemindTime({
            backDay: this.sendBackRemindDays,
            businessType: 100
          })
            .then((data) => {
              Message({
                message: this.$t('message.saveSuccess'),
                type: 'success'
              })
              this.openDetail(7)
              this.saveHigh = true
            })
            .catch((err) => {
              Message({
                message: this.$t('otherSetting.saveFail'),
                type: 'error'
              })
            })
        } else {
          savePublicRule(params)
            .then((data) => {
              // 保存成功
              if (data.code) {
                Message({
                  message: this.$t('message.saveSuccess'),
                  type: 'success'
                })
                this.saveHigh = true
              } else {
                Message({
                  message: this.$t('otherSetting.saveFail'),
                  type: 'error'
                })
              }
            })
            .catch((err) => {
              console.log(err)
            })
        }
      }
    },
    // 刷新
    refresh() {
      this.openDetail(this.currentRule)
    }
  }
  // directives: { Clickoutside }
}
</script>

<style lang="scss" scoped>
@import '../../../styles/saas/item-select.scss';
.customer-setting {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  height: 100%;
  .remindSet {
    padding: 0 80px;
    line-height: 30px;
  }
  .content-column {
    display: flex;
    flex: 1 1 auto;
    height: calc(100% - 60px);
    .side {
      box-sizing: border-box;
      flex: 0 0 300px;
      width: 300px;
      padding: 15px;
      overflow: auto;
      background-color: $base-white;
    }
    .main {
      box-sizing: border-box;
      flex: 1 1 auto;
      padding: 15px;
      margin-left: 10px;
      overflow: auto;
      background-color: $base-white;
    }
    .title {
      height: 30px;
      font-size: 15px;
      border-bottom: 2px solid $neutral-color-3;
      .name {
        display: inline-block;
        height: 30px;
        padding: 0 5px;
        line-height: 24px;
        border-bottom: 2px solid $brand-color-5;
      }
    }
  }
  .children-head {
    // 页面头部
    position: relative;
    display: flex;
    flex: 0 0 50px;
    justify-content: space-between;
    height: 50px;
    padding-left: 30px;
    font-size: 0;
    text-align: right;
    background-color: $base-white;
    .title {
      position: absolute;
      left: 20px;
      margin-left: 0px;
      font-size: 16px;
      line-height: 50px;
      color: $text-main;
    }
    .option-remind-time {
      display: inline-block;
      margin-top: 9px;
      margin-right: 25px;
    }
  }
  :deep(.el-dialog__wrapper) {
    // dialog里面的select的样式
    .el-select {
      width: 80px;
    }
  }
  .content-column {
    .side {
      .cate-list {
        margin-top: 20px;
        .item {
          height: 38px;
          padding: 0 12px;
          margin-bottom: 5px;
          overflow: hidden;
          line-height: 38px;
          text-align: right;
          cursor: pointer;
          transition: all 0.3s;
          .name {
            float: left;
            max-width: 200px;
            overflow: hidden;
            font-size: 14px;
            color: $bg-menu;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .status {
            font-size: 13px;
            color: $text-auxiliary;
            &.on {
              color: $text-plain;
            }
          }
          &.active,
          &.active:hover {
            background-color: $bg-blue;
            .name {
              font-size: 14px;
              color: $bg-menu;
            }
            .status {
              color: $base-white;
            }
          }
          &:hover {
            background-color: $bg-primary;
          }
        }
      }
    }
    .main {
      position: relative;
      display: flex;
      flex-direction: column;
      overflow-x: hidden;
      .item-select {
        line-height: 34px;
        text-align: left;
      }
      .current-table {
        margin: 20px 0;
      }
      .time_limit {
        position: absolute;
        padding-left: 4px;
        line-height: 28px;
      }
      .add-special {
        // 新增特殊员工
        padding: 5px 0;
      }
      .setupCustomer {
        .rule-second-title {
          padding-left: 20px;
          margin: 10px 0;
          font-size: 16px;
          line-height: 30px;
          color: $neutral-color-6;
          border-left: 4px solid $neutral-color-6;
        }
        .rule-table {
          padding-left: 20px;
          .rule-table-cell {
            line-height: 40px;
            span {
              color: $text-plain;
            }
            .text {
              color: $brand-color-5;
            }
            .item .del {
              color: $brand-color-5;
              &:hover {
                color: $base-whitehite !important;
                background-color: $brand-color-5;
              }
            }
          }
          .backRuleList {
            width: 500px;
            margin: 20px 0;
            .sortable-chosen {
              background: $link-color-1;
            }
            .back-rule-list-title {
              line-height: 40px;
              background: $link-color-1;
            }
            li {
              line-height: 35px;
              color: $text-plain;
              border: 1px solid $neutral-color-3;
              border-top: none;
            }
            span,
            i {
              display: inline-block;
              font-style: normal;
              text-align: center;
            }
            span {
              width: 30%;
              padding-left: 20px;
              text-align: left;
            }
            i {
              width: 60%;
            }
          }
        }
      }
      .tips {
        position: relative;
        padding: 10px 10px 10px 60px;
        margin-top: 20px;
        color: $text-plain;
        background-color: $neutral-color-1;
        .item {
          font-size: 13px;
          line-height: 1.8;
        }
        .tip {
          position: absolute;
          top: 14px;
          left: 19px;
        }
      }
      .app-status {
        margin: 25px 0;
        overflow: hidden;
        .text {
          margin-left: 10px;
          vertical-align: middle;
        }
        .set-attr {
          float: right;
          .name {
            display: inline-block;
            margin-right: 5px;
            line-height: 1;
            vertical-align: top;
          }
        }
        .set-con {
          box-sizing: border-box;
          padding-left: 80px;
          margin-top: 15px;
          clear: both;
          font-size: 13px;
          line-height: 1.8;
          color: $text-plain;
          .name {
            display: inline-block;
            margin-left: -80px;
            color: $text-auxiliary;
            vertical-align: middle;
          }
        }
      }
      .notSendBackCus {
        padding: 10px 0;
        h2 {
          margin-bottom: 20px;
          font-size: 16px;
          font-weight: 900;
          line-height: 25px;
        }
      }
      .main-info {
        flex: 1;
        overflow: auto;
      }
      .main-btn {
        padding: 16px 16px 0;
        margin: 0 -16px;
        box-shadow: $shadow-top;
      }
    }
  }
}
</style>

<style lang="scss">
.customer-setting {
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .staff-select {
    display: inline-block;
  }
  .main {
    .is-checked {
      .el-checkbox__label {
        color: $text-plain;
      }
    }
    // .el-select {
    //   width: 200px;
    // }
    .el-input--mini {
      width: 40px;
    }
    .el-input--mini .el-input__inner {
      padding: 0px;
      text-align: center;
    }
  }
  .time-title {
    margin: 24px 0 16px;
    font-size: 16px;
  }
  .time-content {
    display: flex;
    align-items: center;
    color: $text-main;
    .el-select {
      margin: 0 8px;
    }
    span {
      font-size: 14px;
      color: $text-plain;
    }
  }
}
</style>
