<!--
 * @Description: 线索公海池设置-提醒时间设置
-->
<template>
  <el-dialog
    :title="$t('otherSetting.backRemindSet')"
    :visible.sync="dialogShow"
    width="600px"
    @close="dialogShow = false"
  >
    <div class="remindSet">
      <span>{{ $t('otherSetting.clueBeforeBack') }}</span>
      <el-select v-model="sendBackRemindDays" size="mini">
        <el-option v-for="i in 5" :key="i" :label="i" :value="i"></el-option>
      </el-select>
      <span>{{ $t('otherSetting.clueRemindCharge') }}</span>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogShow = false">{{ $t('operation.cancel') }} </el-button>
      <el-button type="primary" @click="saveClueSetRemindTime">{{
        $t('operation.save')
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getClueSetRemindTime, saveClueSetRemindTime } from '@/api/market-manage'
import { Message } from 'element-ui'
import dialogMixin from '@/mixin/dialog.js'

export default {
  name: 'TrailSetDialog',
  mixins: [dialogMixin],
  data() {
    return {
      sendBackRemindDays: 1 // 退回公海池提醒天数
    }
  },
  mounted() {
    this.getClueSetRemindTime()
  },
  methods: {
    saveClueSetRemindTime() {
      // 保存提醒时间
      saveClueSetRemindTime({ day: this.sendBackRemindDays })
        .then((data) => {
          if (data.code) {
            Message({
              message: this.$t('message.saveSuccess'),
              type: 'success'
            })
          } else {
            Message({
              message: this.$t('otherSetting.saveFail'),
              type: 'error'
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.dialogShow = false
        })
    },
    getClueSetRemindTime() {
      // 打开提醒时间并设置
      getClueSetRemindTime({})
        .then((data) => {
          this.sendBackRemindDays = data.result.day
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../styles/saas/item-select.scss';
.publicRule-set {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  height: 98%;
  .remindSet {
    padding: 0 80px;
    line-height: 30px;
  }
  .content-column {
    display: flex;
    flex: 1 1 auto;
    height: calc(100% - 60px);
    padding: 10px;
    .side {
      box-sizing: border-box;
      flex: 0 0 300px;
      width: 300px;
      padding: 15px;
      overflow: auto;
      background-color: $base-white;
    }
    .main {
      box-sizing: border-box;
      flex: 1 1 auto;
      padding: 15px;
      margin-left: 10px;
      overflow: auto;
      background-color: $base-white;
    }
    .title {
      height: 30px;
      font-size: 15px;
      border-bottom: 2px solid $neutral-color-3;
      .name {
        display: inline-block;
        height: 30px;
        padding: 0 5px;
        line-height: 24px;
        border-bottom: 2px solid $brand-color-5;
      }
    }
  }
  .children-head {
    // 页面头部
    position: relative;
    display: flex;
    flex: 0 0 50px;
    justify-content: space-between;
    height: 50px;
    padding-left: 30px;
    font-size: 0;
    text-align: right;
    background-color: $base-white;
    .title {
      position: absolute;
      left: 20px;
      margin-left: 0px;
      font-size: 16px;
      line-height: 50px;
      color: $text-main;
    }
    .option-remind-time {
      display: inline-block;
      margin-top: 9px;
      margin-right: 25px;
    }
  }
  :deep(.el-dialog__wrapper) {
    // dialog里面的select的样式
    .el-select {
      width: 80px;
    }
  }
  .content-column {
    .side {
      .cate-list {
        margin-top: 20px;
        .item {
          height: 38px;
          padding: 0 12px;
          margin-bottom: 5px;
          overflow: hidden;
          line-height: 38px;
          text-align: right;
          cursor: pointer;
          transition: all 0.3s;
          .name {
            float: left;
            max-width: 200px;
            overflow: hidden;
            font-size: 14px;
            color: $text-plain;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .status {
            font-size: 13px;
            color: $text-auxiliary;
            &.on {
              color: $text-plain;
            }
          }
          &.active,
          &.active:hover {
            background-color: $neutral-color-2;
            border-left: 2px solid $brand-color-5;
            .name,
            .status {
              color: $base-white;
            }
          }
          &:hover {
            background-color: $neutral-color-2;
          }
        }
      }
    }
    .main {
      display: flex;
      display: -webkit-flex; /* Safari */
      flex-direction: column;
      flex-wrap: nowrap;
      justify-content: space-between;
      .item-select {
        line-height: 34px;
        text-align: left;
      }
      .current-table {
        margin: 20px 0;
      }
      .add-special {
        // 新增特殊员工
        padding: 5px 0;
      }
      .setupCustomer {
        .rule-second-title {
          padding-left: 20px;
          margin: 10px 0;
          font-size: 16px;
          line-height: 30px;
          color: $neutral-color-6;
          border-left: 4px solid $neutral-color-6;
        }
        .rule-table {
          padding-left: 20px;
          .rule-table-cell {
            line-height: 40px;
            span {
              color: $text-plain;
            }
            .text {
              color: $brand-color-5;
            }
            .item .del {
              color: $brand-color-5;
              &:hover {
                color: $base-white !important;
                background-color: $brand-color-5;
              }
            }
          }
          .backRuleList {
            width: 500px;
            margin: 20px 0;
            .sortable-chosen {
              background: $link-color-1;
            }
            .back-rule-list-title {
              line-height: 40px;
              background: $link-color-1;
            }
            li {
              line-height: 35px;
              color: $text-plain;
              border: 1px solid $neutral-color-3;
              border-top: none;
            }
            span,
            i {
              display: inline-block;
              font-style: normal;
              text-align: center;
            }
            span {
              width: 30%;
              padding-left: 20px;
              text-align: left;
            }
            i {
              width: 70%;
            }
          }
        }
      }
      .tips {
        position: relative;
        padding: 10px 10px 10px 60px;
        margin-top: 20px;
        color: $text-plain;
        background-color: $neutral-color-1;
        .item {
          font-size: 13px;
          line-height: 1.8;
        }
        .tip {
          position: absolute;
          top: 14px;
          left: 19px;
        }
      }
      .app-status {
        margin: 25px 0;
        overflow: hidden;
        .text {
          margin-left: 10px;
          vertical-align: middle;
        }
        .set-attr {
          float: right;
          .name {
            display: inline-block;
            margin-right: 5px;
            line-height: 1;
            vertical-align: top;
          }
        }
        .set-con {
          box-sizing: border-box;
          padding-left: 80px;
          margin-top: 15px;
          clear: both;
          font-size: 13px;
          line-height: 1.8;
          color: $text-plain;
          .name {
            display: inline-block;
            margin-left: -80px;
            color: $text-auxiliary;
            vertical-align: middle;
          }
        }
      }
      .notSendBackCus {
        padding: 10px;
        h2 {
          margin-bottom: 20px;
          font-size: 16px;
          font-weight: 900;
          line-height: 25px;
        }
      }
      .main-info {
        align-self: flex-start;
        width: 100%;
        overflow: auto;
      }
      .main-btn {
        right: 0;
        align-self: flex-end;
        width: 100%;
        line-height: 50px;
        box-shadow: $shadow-top;
        .form-bot {
          background: $base-white;
          .label,
          .con {
            display: inline-block;
          }
          .con {
            padding-left: 300px;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.publicRule-set {
  .children-head {
    // 页面头部
    .el-tabs__item {
      height: 50px;
      line-height: 50px;
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .staff-select {
    display: inline-block;
  }
  .main {
    .is-checked {
      .el-checkbox__label {
        color: $text-plain;
      }
    }
    .el-input--mini {
      width: 40px;
    }
    .el-select {
      width: 200px;
    }
    .el-input--mini .el-input__inner {
      padding: 0px;
      text-align: center;
    }
  }
}
</style>
