<!--
  1、新建自定义指标时，需先获取数据来源，由选择的数据来源去拿统计方式、归属人、统计时间、高级筛选，选择统计方式后才能渲染计算方式和单位
 -->
<template>
  <el-dialog
    append-to-body
    :before-close="handleClose"
    class="intercalate-dialog"
    :title="$t('home.addPk')"
    :visible.sync="dialogVisible"
    width="900px"
  >
    <div class="create-tips">
      {{ $t('home.pkTips') }}
    </div>
    <el-form
      ref="ruleForm"
      v-loading="formLoading"
      label-position="right"
      label-width="100px"
      :model="ruleForm"
      :rules="rules"
    >
      <el-form-item :label="$t('home.targetName')" prop="name">
        <el-input v-model="ruleForm.name" maxlength="10"></el-input>
      </el-form-item>
      <el-form-item :label="$t('home.targetNamePr')" prop="memo">
        <el-input v-model="ruleForm.memo" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item :label="$t('home.dataSource')" prop="driverSources">
        <el-cascader
          expand-trigger="hover"
          :options="driverSourcesArray"
          :value="ruleForm.driverSources"
          @change="handleDriverSources"
        >
        </el-cascader>
      </el-form-item>
      <template v-if="ruleForm.driverSources.length">
        <el-form-item :label="$t('home.statsField')" prop="sourceAttr">
          <el-select
            v-model="ruleForm.sourceAttr"
            :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.field') })"
            value-key="attr"
            @change="handleSourceAttr"
          >
            <el-option
              v-for="item in summaryAble"
              :key="item.atrr"
              :label="item.attrName"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('nouns.belonger')" prop="belongAttr">
          <el-select
            v-model="ruleForm.belongAttr"
            :placeholder="$t('home.chooseBelonging')"
            value-key="attr"
          >
            <el-option
              v-for="item in belongAble"
              :key="item.attr"
              :label="item.attrName"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('home.statsTime')" prop="timeAttr">
          <el-select
            v-model="ruleForm.timeAttr"
            :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.timeField') })"
            value-key="attr"
          >
            <el-option
              v-for="item in timeAble"
              :key="item.attr"
              :label="item.attrName"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <template v-if="ruleForm.sourceAttr.attr">
          <el-form-item :label="$t('home.computeMode')" prop="statisticsWay">
            <el-radio-group v-model="ruleForm.statisticsWay">
              <el-radio v-for="item in statisticsWayArray" :key="item.key" :label="item.key">{{
                item.value
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('unit.unit')" prop="unit">
            <el-radio-group v-model="ruleForm.unit">
              <el-radio v-for="item in unitArray" :key="item.key" :label="item.key">{{
                item.value
              }}</el-radio>
              <span v-if="ruleForm.unit === 'custom'" class="radio-input">
                <el-input v-model.trim="unit" maxlength="5"></el-input>
              </span>
            </el-radio-group>
          </el-form-item>
        </template>
        <el-form-item :label="$t('home.advancedCondition')">
          <filter-condition-list
            :condition-list.sync="ruleForm.filter"
            :label-border="true"
            :use-delete="true"
            :use-symbol="true"
            @deleteCondition="deleteCondition"
          >
          </filter-condition-list>
          <p>
            <el-dropdown placement="bottom-start" trigger="click">
              <el-button class="el-dropdown-link" icon="el-icon-plus" type="text">{{
                $t('nouns.addFilter')
              }}</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-scrollbar
                  :native="false"
                  :noresize="false"
                  tag="ul"
                  view-class="el-select-dropdown__list"
                >
                  <template v-for="item in selectAble">
                    <template v-if="item.subForm && item.subForm.items.length > 0">
                      <el-dropdown-item
                        v-for="sub in item.subForm.items"
                        v-show="fieldFilter(sub.fieldType)"
                        :key="item.attr + sub.attr"
                        :disabled="selectAttr.indexOf(item.attr + '-' + sub.attr) > -1"
                        :title="item.attrName + '.' + sub.attrName"
                        @click.native="addItemClick(item, sub)"
                      >
                        {{ item.attrName + '.' + sub.attrName }}
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        v-show="fieldFilter(item.fieldType)"
                        :key="item.attr"
                        :disabled="selectAttr.indexOf(item.attr) > -1"
                        :title="item.attrName"
                        @click.native="addItemClick(item)"
                      >
                        {{ item.attrName }}
                      </el-dropdown-item>
                    </template>
                  </template>
                </el-scrollbar>
              </el-dropdown-menu>
            </el-dropdown>
          </p>
        </el-form-item>
        <!-- 范围设置 -->
        <el-form-item :label="$t('otherSetting.rangeSet')">
          <span class="range-set" @click="openRangeSet">{{ $t('operation.set') }}</span>
        </el-form-item>
        <!-- 穿透表头字段 -->
        <el-form-item :label="$t('otherSetting.penetrateAttr')">
          <el-select
            v-model="ruleForm.penetrateAttr"
            collapse-tags
            multiple
            :placeholder="$t('multiUnit.pleaseChoose')"
            value-key="attr"
          >
            <template v-for="item in showAble">
              <template v-if="item.subForm && item.subForm.items.length > 0">
                <el-option
                  v-for="sub in item.subForm.items"
                  :key="item.attr + sub.attr"
                  :disabled="selectAttr.indexOf(item.attr + '-' + sub.attr) > -1"
                  :label="item.attrName + '.' + sub.attrName"
                  :value="sub"
                >
                </el-option>
              </template>
              <template v-else>
                <el-option
                  :key="item.attr"
                  :disabled="selectAttr.indexOf(item.attr) > -1"
                  :label="item.attrName"
                  :value="item"
                >
                </el-option>
              </template>
            </template>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $t('operation.cancel') }} </el-button>
      <el-button :loading="buttonLoading" type="primary" @click="submitData('ruleForm')"
        >{{ $t('operation.confirm') }}
      </el-button>
    </span>
    <!-- 范围设置 -->
    <RangeSet
      v-if="rangeSetVisible"
      :data="rangeSetParams"
      :visible.sync="rangeSetVisible"
      @saveRule="saveRule"
    ></RangeSet>
  </el-dialog>
</template>

<script>
import { getAllFormList, getDataSourceAttr } from '@/api/statistics.js'
import { cunstomPkSave, getPkEdit } from '@/api/pkSet'
import FilterConditionList from '@/components/filter/filter-condition-list.vue'
import RangeSet from './rangeSet'

export default {
  name: 'AddPKList',
  components: {
    FilterConditionList,
    RangeSet
  },
  props: {
    chartId: {
      type: Number,
      default: 0
    },
    // 编辑回显的范围设置
    rangeSet: {
      type: Object
    }
  },
  data() {
    return {
      dialogVisible: false,
      // 表单
      ruleForm: {
        name: '',
        memo: '',
        driverSources: [],
        sourceAttr: '',
        belongAttr: '',
        timeAttr: '',
        statisticsWay: '',
        unit: '',
        filter: [],
        penetrateAttr: []
      },
      // 规则
      rules: {
        name: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', { attr: this.$t('home.targetName') }),
            trigger: 'blur'
          },
          { min: 0, max: 20, message: this.$t('home.lengthLimit'), trigger: 'blur' }
        ],
        driverSources: [
          {
            required: true,
            type: 'array',
            message: this.$t('placeholder.choosePls', { attr: this.$t('nouns.dataSources') }),
            trigger: 'change'
          }
        ],
        sourceAttr: [
          {
            required: true,
            type: 'object',
            message: this.$t('placeholder.choosePls', { attr: this.$t('nouns.statisticalField') }),
            trigger: 'change'
          }
        ],
        belongAttr: [
          {
            required: true,
            type: 'object',
            message: this.$t('placeholder.choosePls', { attr: this.$t('nouns.belonger') }),
            trigger: 'change'
          }
        ],
        timeAttr: [
          {
            required: true,
            type: 'object',
            message: this.$t('placeholder.choosePls', { attr: this.$t('home.statsTime') }),
            trigger: 'change'
          }
        ],
        statisticsWay: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', { attr: this.$t('home.computeMode') }),
            trigger: 'change'
          }
        ],
        unit: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', { attr: this.$t('unit.unit') }),
            trigger: 'change'
          }
        ]
      },
      // 高级筛选
      selectAble: [],
      driverSourcesArray: [], // 数据来源
      summaryAble: [], // 统计字段
      belongAble: [], // 归属人选项
      timeAble: [], // 统计时间
      unitArray: [], // 单位
      statisticsWayArray: [], // 计算方式
      unit: '', // 自定义的单位
      allConditionMap: [], // 高级筛选的条件
      rangeSetVisible: false, // 范围设置弹窗显示
      rangeSetParams: {
        joinRule: [], // Pk榜范围设置参与的人员
        removeRule: [], // Pk榜范围设置不参与的人员
        scopeRule: [], // Pk榜可见权限范围
        powerType: 0
      },
      showAble: [], // 穿透表头字段
      formLoading: false, // 表单loading
      buttonLoading: false // 确定loading
    }
  },
  watch: {
    /**
     * 筛选条件不能重复
     */
    'ruleForm.filter': {
      handler(val) {
        this.selectAttr = []
        val.forEach((item) => {
          this.selectAttr.push(item.selectAttr)
        })
      },
      immediate: true
    },
    rangeSet: {
      handler(val) {
        if (JSON.stringify(val) !== '{}') {
          this.rangeSetParams = val
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.dialogVisible = true
    if (this.chartId) {
      this.editChartBase()
    }
    this.getAllFormList()
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    },
    submitData() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let isFormat = true
          this.ruleForm.filter.forEach((item) => {
            if (item.symbol !== 'empty' && item.symbol !== 'noempty') {
              if (!item.value) {
                isFormat = false
              } else if (
                item.symbol === 'in' ||
                item.symbol === 'noin' ||
                item.symbol === 'range' ||
                item.symbol === 'include' ||
                item.symbol === 'allinclude'
              ) {
                if (Array.isArray(item.value)) {
                  if (item.value.length < 1) isFormat = false
                } else {
                  isFormat = false
                }
              }
            }
          })
          if (!isFormat) {
            this.$message.error(this.$t('home.finishCondition'))
          } else {
            if (this.ruleForm.unit === 'custom') {
              if (!this.unit) {
                this.$message.error(this.$t('home.plsInputCustomUnit'))
                return
              }
              if (this.unit.length > 5) {
                this.$message.error(this.$t('home.unitLengthLimit'))
                return
              }
            }
            this.saveData()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 获取编辑时的数据
    editChartBase() {
      this.formLoading = true
      getPkEdit({ chartId: this.chartId })
        .then((data) => {
          const chart = data.result.chart
          this.ruleForm.name = chart.name
          this.ruleForm.memo = chart.memo
          const driverSources = JSON.stringify({
            menuId: chart.driverSources.menuId,
            formId: chart.driverSources.formId,
            saasMark: chart.driverSources.saasMark,
            businessType: chart.driverSources.businessType,
            distributorMark: chart.driverSources.distributorMark || 0
          })
          this.ruleForm.driverSources = [chart.driverSources.appId, driverSources]

          this.ruleForm.belongAttr = chart.belongAttr
          this.ruleForm.sourceAttr = chart.sourceAttr
          this.ruleForm.timeAttr = chart.timeAttr
          this.ruleForm.unit = chart.unit
          chart.unit === 'custom' ? (this.unit = chart.unitValue) : (this.unit = '')
          this.ruleForm.statisticsWay = chart.statisticsWay
          this.ruleForm.filter = chart.filter
          this.ruleForm.penetrateAttr = chart.penetrateAttr
          // 获取统计字段
          this.handleDriverSources(this.ruleForm.driverSources)
        })
        .catch(() => {
          this.formLoading = false
        })
    },
    // 保存指标
    saveData() {
      const driverSources1 = JSON.parse(this.ruleForm.driverSources[1])
      const driverSources = {
        appId: this.ruleForm.driverSources[0],
        menuId: driverSources1.menuId,
        formId: driverSources1.formId,
        saasMark: driverSources1.saasMark,
        businessType: driverSources1.businessType,
        distributorMark: driverSources1.distributorMark || 0
      }
      const chart = {
        id: this.chartId,
        memo: this.ruleForm.memo,
        name: this.ruleForm.name,
        statisticsType: 2,
        driverSources: driverSources,
        sourceAttr: this.ruleForm.sourceAttr,
        belongAttr: this.ruleForm.belongAttr,
        timeAttr: this.ruleForm.timeAttr,
        statisticsWay: this.ruleForm.statisticsWay,
        unit: this.ruleForm.unit,
        unitValue: this.ruleForm.unit === 'custom' ? this.unit : '',
        filter: this.ruleForm.filter,
        categoryId: 0,
        penetrateAttr: this.ruleForm.penetrateAttr, // 穿透表头字段
        joinRule: this.rangeSetParams.joinRule, // 范围设置-参与人
        scopeRule: this.rangeSetParams.scopeRule, // 范围设置-可见权限
        powerType: this.rangeSetParams.powerType, // 范围设置-可见数据范围：0 数据权限，1 全部数据
        removeRule: this.rangeSetParams.removeRule // 范围设置-不参与人
      }
      this.buttonLoading = true
      cunstomPkSave({ chart: chart })
        .then((data) => {
          this.$message.success(this.$t('message.saveSuccess'))
          this.handleClose()
          this.$emit('fresh', !this.chartId ? 1 : 0)
        })
        .catch(() => {})
        .finally(() => {
          this.buttonLoading = false
        })
    },
    // 获取数据来源
    getAllFormList() {
      getAllFormList({ isFromIndex: 1, isOtherFromIndex: 1 })
        .then((data) => {
          this.driverSourcesArray = data.result.appList.map((item) => {
            const formList = item.formList.map((obj) => {
              return {
                value: JSON.stringify({
                  menuId: obj.menuId,
                  formId: obj.formId,
                  saasMark: obj.saasMark,
                  businessType: obj.businessType,
                  distributorMark: obj.distributorMark || 0
                }),
                label: obj.name
              }
            })
            return {
              label: item.appName,
              value: item.appId,
              children: formList
            }
          })
        })
        .catch(() => {})
    },
    // 拿到数据来源后请求数据
    handleDriverSources(arr) {
      if (this.ruleForm.driverSources !== arr) {
        this.ruleForm.belongAttr = ''
        this.ruleForm.sourceAttr = ''
        this.ruleForm.timeAttr = ''
        this.ruleForm.filter = []
        this.ruleForm.unit = ''
        this.ruleForm.statisticsWay = ''
        this.selectAble = []
        this.summaryAble = []
        this.statisticsWayArray = []
        this.unitArray = []
        this.timeAble = []
        this.belongAble = []
        this.showAble = []
        this.ruleForm.penetrateAttr = []
      }
      this.ruleForm.driverSources = arr
      const arrOther = JSON.parse(arr[1])
      const params = {
        driverSources: {
          appId: arr[0],
          menuId: arrOther.menuId,
          formId: arrOther.formId,
          saasMark: arrOther.saasMark,
          businessType: arrOther.businessType,
          distributorMark: arrOther.distributorMark || 0
        },
        chartType: 22,
        single: 1
      }
      this.formLoading = true
      getDataSourceAttr(params)
        .then((data) => {
          this.selectAble = data.result.selectAble[0].fieldList.map((item) => {
            item.formId = data.result.selectAble[0].formId
            return item
          })
          this.summaryAble = data.result.summaryAble[0].fieldList
          this.summaryAble.forEach((item) => {
            if (item.attr === this.ruleForm.sourceAttr.attr) {
              this.statisticsWayArray = item.statisticsWayArray
              this.unitArray = item.unitArray
            }
          })
          this.timeAble = data.result.timeAble[0].fieldList
          this.belongAble = data.result.belongAble[0].fieldList
          this.showAble = data.result.showAble[0].fieldList
        })
        .catch(() => {})
        .finally(() => {
          this.formLoading = false
        })
    },
    // 选择统计方式后重新渲染计算方式和单位
    handleSourceAttr(item) {
      this.ruleForm.unit = ''
      this.ruleForm.statisticsWay = ''
      this.statisticsWayArray = item.statisticsWayArray
      this.unitArray = item.unitArray
    },
    // 删除选中的一项筛选条件
    deleteCondition(index) {
      this.ruleForm.filter.splice(index, 1)
    },
    // 添加筛选条件
    addItemClick(item, subForm) {
      this.ruleForm.filter.push({
        ...item,
        subForm,
        label: subForm ? item.attrName + '.' + subForm.attrName : item.attrName,
        selectAttr: subForm ? item.attr + '-' + subForm.attr : item.attr
      })
    },
    openRangeSet() {
      this.rangeSetVisible = true
    },
    saveRule(data) {
      this.rangeSetParams = data
      this.rangeSetVisible = false
    },
    // 高级筛选列表过滤
    fieldFilter(fieldType) {
      // 还需要过滤部门和员工单选多选相关字段
      return ![
        10006, 10002, 10004, 10005, 10007, 10008, 10018, 10009, 10010, 10011, 10012
      ].includes(+fieldType)
    }
  }
}
</script>

<style lang="scss" scoped>
.intercalate-dialog {
  .create-tips {
    padding: 10px 20px;
    margin: 0 0 10px 10px;
    line-height: 25px;
    background-color: $neutral-color-1;
  }
  .radio-input {
    display: inline-block;
    width: 120px;
    margin-left: 10px;
  }
  .range-set {
    font-size: 14px;
    color: #ff8e3c;
    cursor: pointer;
  }
}
</style>
