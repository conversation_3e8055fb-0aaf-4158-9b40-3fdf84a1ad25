<template>
  <div>
    <div class="alibaba-auth-set">
      <div class="alibaba-auth-set__header">1688授权</div>
      <div class="alibaba-auth-set__content">
        <div class="header">
          <div class="title">授权设置</div>
          <div class="status-tag" :class="{ auth: status }">{{ status ? '已授权' : '未授权' }}</div>
        </div>
        <div class="tips">请先进行店铺授权，授权后可进行历史数据同步操作</div>
        <div class="auth-status">
          <div><span></span>{{ status ? `授权店铺(${accountList.length}/5)` : '授权入口' }}</div>
          <p v-if="!status">
            授权完成后请<span @click="getRightBindList">刷新页面</span>更新授权状态
          </p>
          <p v-else>
            授权到期、修改密码、订购到期、取消授权后需要重新授权账号，授权完成后请<span
              @click="getRightBindList"
              >刷新页面</span
            >更新授权状态
            <span @click="openHelp"> <i class="icon-book-open-line t-iconfont"></i>绑定教程</span>
          </p>
        </div>
        <div v-if="status" class="account">
          <div v-for="item in accountList" :key="item.id" class="account__item">
            <div class="flex-wrap">
              <div class="flex-wrap__img" :class="item.status ? 'unbound' : ''">
                <i class="icon-store-2-fill t-iconfont"></i>
              </div>
              <div class="account-info">
                <div class="account-info__item flex">
                  <div class="account-info__item name">
                    <span v-if="item.isMain">主</span>
                    {{ item.loginName }}
                  </div>
                  <div class="tag" :class="item.status ? 'unbound-tag' : 'bound-tag'">
                    {{ item.status ? '审核中' : '已绑定' }}
                  </div>
                </div>
                <div class="account-info__item id">店铺账号：{{ item.loginId }}</div>
              </div>
            </div>
            <div class="authorization-flex-wrap">
              <div class="expire-time">
                <span>到期日期：</span>
                <span :class="isBeforeCurrentDate(item.expireTime) ? 'red-text' : ''">{{
                  item.expireTime
                }}</span>
              </div>
              <div
                v-if="isBeforeCurrentDate(item.expireTime) && adminOrBoss"
                class="reauthorization"
                @click="handleJump"
              >
                <i class="icon-user-follow-line t-iconfont"></i> <span>重新授权</span>
              </div>
            </div>
          </div>
          <div
            v-if="accountList.length < 5 && adminOrBoss"
            class="account__item add"
            @click="handleOpen"
          >
            <i class="icon-add-line t-iconfont"></i> <span>绑定其他店铺</span>
          </div>
        </div>
        <el-button v-else class="auth-button" type="primary" @click="jumpAlibabaLogin">
          <i class="icon-user-follow-line t-iconfont"></i>
          前往授权
        </el-button>
        <div v-if="status">
          <div class="auth-status">
            <div><span></span>历史数据同步</div>
            <p>产品同步数据范围为销售中的商品，订单同步数据时间范围默认为近 1 年</p>
          </div>
          <el-button
            v-if="contractStatus === -1 && adminOrBoss"
            class="auth-button"
            type="primary"
            @click="handleOpenSync(1)"
          >
            <i class="icon-upload-cloud-line t-iconfont"></i>
            数据同步
          </el-button>
          <div v-else class="block simple-steps">
            <div class="custom-title" :class="productStatus === 1 ? 'active-step' : ''">
              <span v-if="productStatus !== 2">1</span>
              <i v-if="productStatus === 2" class="icon-checkbox-circle-fill t-iconfont"></i>
              <p :class="productStatus === 1 ? 'is-success' : ''">产品同步</p>
              <v-tag class="tag" :class="productStatus === 2 ? 'success-tag' : ''">{{
                tagStatus(productStatus)
              }}</v-tag>
            </div>
            <el-divider direction="vertical"></el-divider>
            <div class="custom-title" :class="contractStatus === 1 ? 'active-step' : ''">
              <span v-if="contractStatus !== 2">2</span>
              <i v-if="contractStatus === 2" class="icon-checkbox-circle-fill t-iconfont"></i>
              <p :class="contractStatus === 1 ? 'special-color' : ''">订单同步</p>
              <v-tag class="tag" :class="contractStatus === 2 ? 'success-tag' : ''">{{
                tagStatus(contractStatus)
              }}</v-tag>
            </div>
            <p v-if="adminOrBoss">
              新增店铺数据同步/数据有遗漏？可点击
              <span @click="handleOpenSync(2)">
                <i class="icon-refresh-line t-iconfont"></i>重新同步</span
              >
            </p>
          </div>
          <template v-if="adminOrBoss">
            <div class="auth-status">
              <div><span></span>实时数据同步规则</div>
              <p>根据规则的设置，同步实时推送到系统的数据</p>
            </div>
            <el-button class="auth-button" @click="handleOpenSync(3)">
              <i class="icon-settings-4-line t-iconfont"></i>
              同步设置
            </el-button>
          </template>
          <template>
            <div class="auth-status">
              <div><span></span>询盘转客户同步规则</div>
              <p>询盘转客户时，系统将按此规则，自动同步询盘内相关字段信息至客户中</p>
            </div>
            <el-button
              class="auth-button"
              @click="handleConvertRules('alibaba', '询盘转客户同步设置')"
            >
              <i class="icon-settings-4-line t-iconfont"></i>
              同步设置
            </el-button>
          </template>
        </div>
      </div>
      <el-dialog
        :before-close="handleClose"
        title="绑定店铺"
        :visible.sync="dialogVisible"
        width="480px"
      >
        <div class="simple-steps">
          <div class="custom-title" :class="active === 0 ? 'active-step' : ''">
            <span v-if="active === 0">1</span>
            <i v-else class="icon-checkbox-circle-fill t-iconfont"></i>
            <p :class="active === 1 ? 'is-success' : ''">填写待绑定店铺旺旺账号</p>
          </div>
          <i class="icon-arrow-right-s-line t-iconfont"></i>
          <div class="custom-title" :class="active === 1 ? 'active-step' : ''">
            <span>2</span>
            <p :class="active === 1 ? 'special-color' : ''">1688 后台完成绑定和授权</p>
          </div>
        </div>
        <div v-if="active === 0">
          <p class="placeholder">请输入待绑定店铺旺旺账号</p>
          <el-input v-model="loginId" placeholder="请输入"></el-input>
        </div>
        <div v-else>
          <p class="placeholder">授权账号</p>
          <el-button class="auth-button" type="primary" @click="handleJump">
            <i class="icon-user-follow-line t-iconfont"></i>
            前往授权
          </el-button>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">{{ $t('operation.cancel') }}</el-button>
          <el-button v-if="active === 0" :disabled="!loginId" type="primary" @click="handleNext">{{
            $t('operation.nextStep')
          }}</el-button>
          <el-button v-else type="primary" @click="onSuccess">授权完成</el-button>
        </span>
      </el-dialog>
      <el-dialog
        :before-close="handleCloseSync"
        :title="syncDialogTitle"
        :visible.sync="syncVisible"
        width="600px"
      >
        <div v-if="syncType === 2" class="sync-data-block">
          <div class="sync-title"><span></span>同步范围</div>
          <el-checkbox v-model="syncProduct" label="产品同步"></el-checkbox>
          <el-checkbox v-model="syncContract" label="订单同步"></el-checkbox>
        </div>
        <template v-if="syncType !== 2 || syncContract">
          <div class="sync-data-block">
            <div class="sync-title"><span></span>订单信息同步</div>
            <div class="sync-info">
              <div class="head info">
                <div class="info-left">
                  <i class="icon-a-1688-line t-iconfont"></i>
                  <span>1688 订单</span>
                </div>
                <div class="info-middle">同步到</div>
                <div class="info-right">
                  <i class="icon-logo1 t-iconfont"></i>
                  <span>1688 合同</span>
                </div>
              </div>
              <div v-for="item in convertRules" :key="item.attr" class="data info">
                <div class="info-left">{{ item.aliName }}</div>
                <div class="info-middle"><i class="icon-arrow-right-line t-iconfont"></i></div>
                <div class="info-right">{{ item.attrName }}</div>
              </div>
            </div>
          </div>
          <div class="status-block sync-data-block">
            <div class="sync-title"><span></span>支付状态同步设置</div>
            <div class="sync-info">
              <div class="head info">
                <div class="info-left">
                  <i class="icon-a-1688-line t-iconfont"></i>
                  <span>支付状态</span>
                </div>
                <div class="info-middle">同步到</div>
                <div class="info-right">
                  <i class="icon-logo1 t-iconfont"></i>
                  <span>支付状态</span>
                </div>
              </div>
              <div v-for="aliItem in statusList" :key="aliItem.attr" class="data info">
                <div class="info-left">{{ aliItem.aliName }}</div>
                <div class="info-middle"><i class="icon-arrow-right-line t-iconfont"></i></div>
                <div class="info-right">
                  <el-select v-model="aliItem.value" placeholder="请选择">
                    <el-option
                      v-for="item in itemList"
                      :key="item.value"
                      :label="item.text"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </template>

        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseSync">{{ $t('operation.cancel') }}</el-button>
          <el-button type="primary" @click="handleSaveSync">{{
            $t('operation.confirm')
          }}</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 线索转换规则 -->
    <ClueConvertRulesDialog
      v-if="clueConversionRulesDialogShow"
      :convert-rule-info="convertRuleInfo"
      :form-id="formId"
      :form-name="formName"
      :fullscreen="false"
      :show.sync="clueConversionRulesDialogShow"
      @dialogConfirmCancel="clueConversionRulesDialogShow = false"
    />
  </div>
</template>

<script>
import {
  getUrl,
  getAuthStatus,
  getRightBindList,
  addRightBind,
  syncData,
  againSync,
  getAlibabaField,
  saveAlibabaField
} from '@/api/alibaba'
import VTag from '@/components/base/v-tag.vue'
import { mapGetters } from 'vuex'
import ClueConvertRulesDialog from '@/views/edit/new-form-design/components/other/clue-convert-rules/clue-convert-rules-dialog.vue'

export default {
  components: {
    VTag,
    ClueConvertRulesDialog
  },
  data() {
    return {
      status: true,
      accountList: [],
      dialogVisible: false,
      syncVisible: false,
      active: 0,
      loginId: '',
      syncProduct: false,
      syncContract: false,
      contractStatus: -1,
      productStatus: '',
      convertRules: [],
      statusList: [],
      itemList: [],
      itemCovertRuleId: null,
      syncDialogTitle: '',
      syncType: null,
      clueConversionRulesDialogShow: false,
      formName: '',
      formId: null
    }
  },
  async mounted() {
    await this.getAuthStatus()
    if (this.status) {
      this.getRightBindList()
    }
  },
  computed: {
    ...mapGetters(['adminOrBoss'])
  },
  methods: {
    tagStatus(val) {
      return val ? (val === 1 ? '同步中' : '同步完成') : '等待中'
    },
    getAuthStatus() {
      getAuthStatus()
        .then(({ result }) => {
          this.status = result?.status === 0
          this.account = result.loginId
          this.contractStatus = result.contractStatus
          this.productStatus = result.productStatus
          this.formId = result.formId
          this.formName = result.name
        })
        .catch(() => {})
    },
    handleClose() {
      this.dialogVisible = false
      this.active = 0
      this.loginId = ''
    },
    onSuccess() {
      this.handleClose()
      this.getRightBindList()
    },
    handleOpen() {
      this.dialogVisible = true
    },
    handleNext() {
      this.active = 1
      const params = {
        loginId: this.loginId
      }
      addRightBind(params).then((res) => {})
    },
    getRightBindList() {
      getRightBindList().then((res) => {
        this.accountList = res.result.list
      })
    },
    jumpAlibabaLogin() {
      const url = window.location.origin
      const state = 'alibaba'
      getUrl({ url, state })
        .then(({ result }) => {
          window.open(result.url, '_self')
        })
        .catch(() => {})
    },
    handleJump() {
      window.open('https://pc.1688.com/order/list/items.htm?type=paid')
    },
    openHelp() {
      window.open('https://alidocs.dingtalk.com/i/p/YOlnXR7JZbxmLpvg5pXBkdxerLjrBm7Z')
    },
    handleCloseSync() {
      this.syncVisible = false
      this.syncProduct = false
      this.syncContract = false
    },
    handleSaveSync() {
      this.convertRules.forEach((item) => {
        if (item.aliAttr === 'status') {
          item.convertRule.itemList = this.statusList
        }
      })
      const params = {
        itemCovertRuleId: this.itemCovertRuleId,
        convertRules: this.convertRules
      }
      let api
      switch (this.syncType) {
        case 1:
          api = syncData
          break
        case 2:
          params.syncProduct = this.syncProduct ? 1 : 0
          params.syncContract = this.syncContract ? 1 : 0
          api = againSync
          break
        case 3:
          api = saveAlibabaField
          break
      }
      api(params).then((res) => {
        this.getAuthStatus()
        this.handleCloseSync()
      })
    },
    handleOpenSync(type) {
      this.syncDialogTitle = type === 3 ? '实时数据同步设置' : '历史数据同步'
      this.syncVisible = true
      this.syncType = type

      getAlibabaField().then((res) => {
        this.convertRules = res.result.convertRules
        this.itemList = res.result.itemList
        const listItem = res.result.convertRules.find((item) => item.aliAttr === 'status')
        this.statusList = listItem.convertRule.itemList
        this.statusList.forEach((item) => {
          delete item['text']
        })
        this.itemCovertRuleId = res.result.itemCovertRuleId
      })
    },
    isBeforeCurrentDate(timeString) {
      const inputDate = new Date(timeString)
      const currentDate = new Date()
      return inputDate.getTime() < currentDate.getTime()
    },
    // 处理线索转换规则
    handleConvertRules(settingType, ruleSettingsTitle) {
      this.clueConversionRulesDialogShow = true
      this.convertRuleInfo = { ruleSettingsTitle, settingType }
    }
  }
}
</script>

<style lang="scss" scoped>
.alibaba-auth-set {
  .alibaba-auth-set__header {
    height: 48px;
    padding: 0 24px;
    font-size: 16px;
    font-weight: 600;
    line-height: 48px;
    color: $text-main;
    background-color: $base-white;
  }
  .alibaba-auth-set__content {
    height: 83vh;
    padding: 12px 16px;
    margin: 12px;
    background-color: $base-white;
    border-radius: 4px;
    .header {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: $text-main;
      .title {
        font-weight: 600;
      }
      .status-tag {
        padding: 4px 10px;
        margin-left: 8px;
        font-size: 12px;
        color: $text-main;
        background-color: $neutral-color-2;
        border-radius: 4px;
      }
      .auth {
        color: $success-base-color-6;
        background-color: $success-color-1;
      }
    }
    .tips {
      padding: 12px 0;
      font-size: 13px;
      color: $text-auxiliary;
      border-bottom: 1px solid $neutral-color-3;
    }
    .auth-status {
      padding: 12px 0;
      font-size: 14px;
      color: $text-main;
      div {
        height: 20px;
        margin-bottom: 4px;
        font-size: 14px;
        line-height: 20px;
        > span {
          display: inline-block;
          width: 4px;
          height: 12px;
          margin-right: 8px;
          background: $brand-base-color-6;
          border-radius: 2px;
        }
      }
      p {
        font-size: 12px;
        color: $text-auxiliary;
        span {
          color: #248df5;
          cursor: pointer;
          i {
            font-size: 14px;
          }
        }
      }
    }
    .account {
      display: flex;
      flex-wrap: wrap;
      &__item {
        box-sizing: border-box;
        flex: 0 0 calc((100% - 24px) / 3);
        height: 96px;
        padding: 12px;
        margin-right: 12px;
        margin-bottom: 12px;
        border: 1px solid #e5e6eb;
        border-radius: 8px;
      }
      &__item:nth-child(3n) {
        margin-right: 0;
      }
      .flex-wrap {
        display: flex;
        &__img {
          width: 44px;
          height: 44px;
          margin-bottom: 8px;
          background: linear-gradient(180deg, #ffa100 0%, #ff6a00 100%);
          border-radius: 8px;
          .icon-store-2-fill {
            margin-left: 10px;
            font-size: 24px;
            line-height: 44px;
            color: #fff;
          }
        }
        .unbound {
          background: #c8c9cc;
        }
        .account-info {
          flex: 1;
          margin-left: 8px;
          &__item {
            height: 20px;
            font-size: 14px;
            line-height: 20px;
          }
          .name {
            margin-bottom: 4px;
            font-weight: 600;
            span {
              display: inline-block;
              width: 20px;
              height: 20px;
              font-size: 12px;
              color: #ffffff;
              text-align: center;
              background: #ff6a00;
              border-radius: 4px;
            }
          }
          .id {
            color: #646566;
          }
          .flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .tag {
            padding: 2px 8px;
            font-size: 12px;
            line-height: 16px;
            border-radius: 4px;
          }
          .bound-tag {
            color: #00c241;
            background: #e8ffec;
          }
          .unbound-tag {
            color: $text-main;
            background: #f2f3f5;
          }
        }
      }
      .authorization-flex-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 20px;
        line-height: 20px;
        color: $brand-base-color-6;
        .expire-time {
          height: 20px;
          font-size: 14px;
          line-height: 20px;
          color: #969799;
          .red-text {
            color: red;
          }
        }
        .reauthorization {
          cursor: pointer;
        }
      }

      .add {
        font-size: 14px;
        line-height: 66px;
        color: $text-auxiliary;
        text-align: center;
        cursor: pointer;
        border: 1px dashed #e5e6eb;
      }
    }
  }
  :deep(.el-dialog) {
    border-radius: 12px;
    .el-dialog__body {
      padding: 16px;
    }
    .el-dialog__footer {
      height: 56px;
      padding: 0 24px;
      line-height: 56px;
    }
    .el-dialog__header {
      display: flex;
      align-items: center;
      height: 48px;
      padding: 13px 20px;
      font-size: 16px !important;
      line-height: 48px !important;
      color: $text-main;
      background: $base-white;
      border-bottom: 1px solid $neutral-color-2;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
      .el-dialog__title {
        font-size: 16px;
        line-height: 48px;
      }
      .el-dialog__headerbtn {
        top: 16px;
      }
    }
  }
}

.block {
  display: block !important;
  height: 72px;
  padding: 0px !important;
  .tag {
    width: unset !important;
    margin-left: 8px;
    border-radius: 4px !important;
  }
  .success-tag {
    color: $success-base-color-6 !important;
    background: #e8ffec !important;
  }
  > p {
    margin-top: 8px;
    font-size: 12px;
    color: #969799;
    span {
      color: #248df5;
      cursor: pointer;
      i {
        font-size: 14px;
      }
    }
  }
}
.simple-steps {
  display: flex;
  align-items: center;
  padding-left: 12px;
  margin-bottom: 16px;
  .custom-title {
    display: flex;
    span {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-right: 8px;
      font-size: 12px;
      line-height: 20px;
      color: #646566;
      text-align: center;
      background: #f7f8fa;
      border-radius: 50%;
    }
    p {
      font-weight: 600;
      line-height: 20px;
      color: $text-plain;
    }
    .icon-checkbox-circle-fill {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      font-size: 20px;
      color: #00c241;
    }
    .is-success {
      color: $text-main;
    }
  }
  .active-step {
    span {
      color: $brand-base-color-6;
      background: $brand-color-1;
    }
    p {
      color: $brand-base-color-6;
    }
  }
  .icon-arrow-right-s-line {
    width: 20px;
    height: 20px;
    margin: 0 8px;
    line-height: 20px;
    color: #c8c9cc;
  }
}
.placeholder {
  margin-bottom: 8px;
  color: $text-main;
}
.el-checkbox {
  display: block;
}
.sync-data-block {
  margin-bottom: 16px;
  .sync-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
    color: $text-main;
    > span {
      display: inline-block;
      width: 4px;
      height: 16px;
      margin-right: 8px;
      background: $brand-base-color-6;
      border-radius: 2px;
    }
  }
  .sync-info {
    padding: 8px 12px;
    background: $neutral-color-1;
    border-radius: 4px;
    .info {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      height: 20px;
      margin-bottom: 8px;
      .info-middle {
        width: 48px;
        margin-right: 48px;
        color: #9e9e9e;
      }
      .info-left,
      .info-right {
        flex: 1;
      }
    }
    .head {
      .icon-logo1,
      .icon-a-1688-line {
        width: 20px;
        height: 20px;
        line-height: 20px;
        color: $base-white;
        text-align: center;
        background: $brand-base-color-6;
        border-radius: 6px;
      }
      span {
        font-weight: 600;
        color: $text-main;
      }
    }
    .data {
      color: $text-plain;
      .icon-arrow-right-line {
        margin-left: 12px;
        color: $text-auxiliary;
      }
    }
  }
}
.status-block .sync-info .data {
  height: 32px !important;
}
</style>
