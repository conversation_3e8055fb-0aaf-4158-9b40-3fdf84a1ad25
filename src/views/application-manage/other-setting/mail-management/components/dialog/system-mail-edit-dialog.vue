<template>
  <el-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="$t('mail.bindSystemEmail')"
    :visible.sync="selfVisible"
    width="600px"
  >
    <ol class="tips">
      <li>
        {{ $t('mail.bindEmailDialogTip1')
        }}<a href="https://help.xbongbong.com/?p=20847" target="_blank">{{
          $t('mail.bindEmailDialogTip2')
        }}</a>
      </li>
      <!-- <li>
        {{ $t('mail.bindEmailDialogTip3') }}<a>{{ $t('mail.bindEmailDialogTip4') }}</a>
      </li> -->
    </ol>
    <el-form
      ref="form"
      label-position="left"
      label-width="110px"
      :model="selfFormData"
      :rules="rules"
    >
      <el-form-item prop="account">
        <template slot="label">
          <span>{{ $t('mail.mailAccount') }}：</span>
        </template>
        <el-input
          v-model="selfFormData.account"
          :disabled="isEdit"
          @blur="handleMailAccountInputBlur"
        />
      </el-form-item>

      <el-form-item prop="password">
        <template slot="label">
          <span>{{ $t('mail.mailPassword') }}：</span>
        </template>
        <el-input v-model="selfFormData.password" show-password />
      </el-form-item>

      <el-form-item prop="sendServer">
        <template slot="label">
          <span>{{ $t('mail.senderServer') }}：</span>
        </template>
        <div class="server-config-wrapper">
          <el-input
            v-model="selfFormData.sendServer.host"
            :placeholder="$t('mail.senderServerHost')"
          ></el-input>
          <span class="separator">:</span>
          <el-input
            v-model="selfFormData.sendServer.port"
            :placeholder="$t('mail.senderServerPort')"
          ></el-input>
          <el-checkbox v-model="selfFormData.sendServer.ssl" :false-label="0" :true-label="1"
            >SSL</el-checkbox
          >
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer">
      <el-button @click="handleCancel">{{ $t('operation.cancel') }}</el-button>
      <el-button :loading="confirmBtnLoading" type="primary" @click="handleConfirm">{{
        $t('operation.confirm')
      }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import dialogMixin from '@/mixin/editDialogMixin'
import { getMailConfigByMailAddress } from '@/views/application-manage/other-setting/mail-management/mail-config.js'

const formDataSchema = {
  account: '',
  password: '',

  sendServer: {
    host: '',
    port: '',
    ssl: 0
  }
}

export default {
  name: 'SystemMailEditDialog',

  components: {},

  mixins: [dialogMixin(formDataSchema)],

  props: {},

  data() {
    const checkServer = (rule, value, cb) => {
      if (!/^([0-9a-zA-Z-]{1,}\.)+([a-zA-Z]{2,})$/.test(value.host)) {
        cb(this.$t('mail.serverHostError'))

        return
      }
      if (
        !/^(?:[1-9]\d{0,4}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/.test(
          value.port
        )
      ) {
        cb(this.$t('mail.serverPortError'))
        return
      }

      cb()
    }
    return {
      mailConfig: null,

      rules: {
        account: [
          { required: true, trigger: 'change', message: this.$t('mail.mailAccountError1') },
          {
            pattern:
              /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            trigger: 'blur',
            message: this.$t('mail.mailAccountError2')
          }
        ],
        password: [
          { required: true, trigger: 'change', message: this.$t('mail.mailPasswordError') }
        ],
        sendServer: [
          { required: true },
          {
            trigger: 'blur',
            validator: checkServer
          }
        ]
      }
    }
  },

  computed: {},

  watch: {},

  created() {},

  beforeDestroy() {},

  methods: {
    handleMailAccountInputBlur() {
      this.mailConfig = getMailConfigByMailAddress(this.selfFormData.account)
      if (!this.mailConfig) {
        this.selfFormData.sendServer = {
          host: '',
          port: '',
          ssl: 0
        }
      } else {
        this.selfFormData.sendServer = xbb.deepClone(this.mailConfig.sendServer)
      }
    },
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.startConfirmBtnLoading()
          const finalFormData = {
            account: this.selfFormData.account,
            password: this.selfFormData.password,

            sendHost: this.selfFormData.sendServer.host,
            sendPort: this.selfFormData.sendServer.port,
            enableSendSsl: this.selfFormData.sendServer.ssl
          }
          if (this.selfFormData.id) {
            finalFormData.id = this.selfFormData.id
          }
          this.$emit('submit', finalFormData)
        }
      })
    },
    handleCancel() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.server-config-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
  .separator {
    font-size: 14px;
    color: $text-main;
  }
}

.tips {
  padding: 8px 16px;
  margin-bottom: 16px;
  background: $neutral-color-1;
  border-radius: 4px;

  li {
    font-size: 12px;
    line-height: 16px;
    color: $text-plain;
    // list-style: decimal inside;
    list-style: none;
  }
}
</style>
