<template>
  <el-dialog class="new-style-dialog" title="列表设置" :visible.sync="dialogShow" width="680px">
    <div>
      <span class="desc">合并字段规则</span>
      <v-table
        ref="tableRef"
        v-loading="loading"
        border
        :data="settingValue"
        height="290px"
        row-class-name="table-row--list"
        row-key="attr"
        style="margin-top: 10px"
      >
        <el-table-column label="合并字段名称" prop="mergeFieldName">
          <template slot-scope="scope">
            <div class="name-block">
              <el-input v-model="scope.row.mergeFieldName" placeholder="请输入" />
              <!-- <edit-cell
                :ref="`nameinput${scope.$index}`"
                v-model="scope.row.mergeFieldName"
                type="input"
              /> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="项目任务" width="150px">
          <template slot-scope="scope">
            <el-select v-model="scope.row.taskFieldAttr" placeholder="请选择">
              <el-option
                v-for="item in taskList"
                :key="item.attr"
                :label="item.attrName"
                :value="item.attr"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="项目风险" width="150px">
          <template slot-scope="scope">
            <el-select v-model="scope.row.riskFieldAttr" placeholder="请选择">
              <el-option
                v-for="item in riskList"
                :key="item.attr"
                :label="item.attrName"
                :value="item.attr"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="50px">
          <template slot-scope="scope">
            <el-button type="text" @click="deleteData(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('operation.sort')" width="100px">
          <template slot-scope="scope">
            <div class="sort">
              <el-button class="move sort-btn" icon="el-icon-rank" type="text"> </el-button>
              <el-button
                class="sort-btn"
                icon="el-icon-upload2"
                type="text"
                @click="sortList(scope, 'top')"
              >
              </el-button>
              <el-button
                class="sort-btn"
                icon="el-icon-download"
                type="text"
                @click="sortList(scope, 'bottom')"
              >
              </el-button>
            </div>
          </template>
        </el-table-column>
      </v-table>
      <el-button type="text" @click="handleAddFiled">+添加合并字段</el-button>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogShow = false">取 消</el-button>
      <el-button :loading="saveloading" type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getProjectSettingConfig,
  saveProjectSettingConfig
} from '@/api/work-order-v2/project-setting.js'

import Sortable from 'sortablejs'

import xbb from '@xbb/xbb-utils'
import VTable from '@/components/v-table/index.js'

export default {
  name: 'GanttSet',

  components: {
    VTable
  },

  filters: {},

  mixins: [],
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      loading: false,
      saveloading: false,
      settingValue: [],
      taskList: [],
      riskList: []
    }
  },

  computed: {
    dialogShow: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },

  watch: {},

  mounted() {
    this.getSet()
  },

  methods: {
    handleAddFiled() {
      this.settingValue.push({
        mergeFieldName: '默认合并字段',
        riskFieldAttr: '',
        taskFieldAttr: ''
      })
      // 滚动到新增行
      this.$nextTick(() => {
        const rowHeight = this.$refs.tableRef.$el.querySelector('.el-table__row').clientHeight
        this.$refs.tableRef.bodyWrapper.scrollTop += rowHeight * this.settingValue.length
      })
    },
    getSet() {
      this.loading = true
      getProjectSettingConfig({
        settingType: 4
      })
        .then(({ result }) => {
          this.settingValue = result.settingValue
          this.riskList = result.risk
          this.taskList = result.task
          this.$nextTick(() => {
            this.rowDrop()
          })
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    confirm() {
      this.saveloading = true
      saveProjectSettingConfig({
        settingType: 4,
        settingValue: this.settingValue
      })
        .then((res) => {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.dialogShow = false
        })
        .catch(() => {})
        .finally(() => {
          this.saveloading = false
        })
    },
    deleteData(index) {
      this.settingValue.splice(index, 1)
    },
    rowDrop() {
      const tbody = document.querySelectorAll('.el-table__body-wrapper tbody')[0]
      const _this = this
      this.sortable = Sortable.create(tbody, {
        handle: '.move',
        onEnd({ newIndex, oldIndex }) {
          _this.reorderItems(oldIndex, newIndex)
        }
      })
    },
    // 置顶、置底操作
    sortList(scope, type) {
      const currRow = this.settingValue.splice(scope.$index, 1)[0]
      if (type === 'top') {
        this.settingValue.splice(0, 0, currRow)
      } else {
        this.settingValue.push(currRow)
      }
    },
    reorderItems(oldIndex, newIndex) {
      const settingValue = xbb.deepClone(this.settingValue)
      const currRow = settingValue.splice(oldIndex, 1)[0]
      settingValue.splice(newIndex, 0, currRow)
      this.settingValue = []
      this.$nextTick(() => {
        this.settingValue = settingValue
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sort {
  display: flex;
  align-items: center;
  justify-content: space-around;
  &-btn {
    font-size: 16px;
    color: $text-auxiliary;
  }
}

.move {
  &:hover {
    cursor: move;
  }
}

.desc {
  margin-bottom: 10px;
}
</style>
