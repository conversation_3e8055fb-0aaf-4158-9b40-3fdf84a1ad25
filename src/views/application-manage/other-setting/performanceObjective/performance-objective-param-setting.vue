<!--
 * @Author: 金小瑞
 * @Date: 2019-01-15 10:23:04
 * @LastEditors: ning.chen
 * @LastEditTime: 2021-11-30 10:14:20
 * @Description: 组件关联页面：业绩目标的参数设置页
 -->

<!--
组件关联页面：业绩目标的参数设置页
-->
<template>
  <div class="performance-objective-param-setting">
    <el-dialog
      :before-close="fiscalYearCancel"
      :title="$t('otherSetting.changeYear')"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <span>{{ $t('otherSetting.isUpdate') }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="fiscalYearCancel">{{ $t('operation.cancel') }} </el-button>
        <el-button type="primary" @click="fiscalYearSave">{{ $t('operation.confirm') }} </el-button>
      </span>
    </el-dialog>
    <el-form
      ref="form"
      v-loading="loading"
      label-position="left"
      label-width="130px"
      :model="form"
      :rules="rules"
    >
      <h2 class="setting-title">{{ $t('otherSetting.yearSet') }}</h2>
      <el-form-item :label="$t('otherSetting.startMonth') + ':'">
        <el-select
          v-model="form.month"
          :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.month') })"
          @change="dialogVisible = true"
        >
          <el-option
            v-for="month in 12"
            :key="month"
            :label="month + $t('unit.month')"
            :value="month"
          >
          </el-option>
        </el-select>
        <span v-if="form.month === 1" class="fiscal-year-tips">{{
          $t('otherSetting.yearPeriod')
        }}</span>
        <span v-else class="fiscal-year-tips"
          >当前财年周期：本年{{ form.month }}月至次年{{ form.month - 1 }}月底</span
        >
      </el-form-item>
      <h2 class="other-setting setting-title">
        {{ $t('otherSetting.othersSet') }}
      </h2>
      <div class="work-days">
        <el-form-item
          :label="$t('otherSetting.workDaysMonth') + ':'"
          label-width="140px"
          prop="monthDays"
          type="number"
        >
          <el-input
            v-model.number="form.monthDays"
            :placeholder="$t('placeholder.contentPls')"
            @change="handlerSave(form.monthDays, 'workingDayMonth')"
          >
          </el-input>
          <span style="padding: 0 20px 0 6px">{{ $t('unit.day') }}</span>
        </el-form-item>
        <el-form-item
          :label="$t('otherSetting.workDaysWeek') + ':'"
          label-width="140px"
          prop="weekDays"
          type="number"
        >
          <el-input
            v-model.number="form.weekDays"
            :placeholder="$t('placeholder.contentPls')"
            @change="handlerSave(form.weekDays, 'workingDayWeek')"
          >
          </el-input>
          <span style="padding-left: 6px">{{ $t('unit.day') }}</span>
          <el-tooltip placement="top-start">
            <span slot="content">{{ $t('otherSetting.splitParams') }}</span>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form-item>
      </div>
      <div v-for="item in switchList" :key="item.value" class="switch-setting">
        <el-switch
          v-model="form[item.value]"
          active-color="#ff8c2e"
          :active-value="1"
          :inactive-value="0"
          @change="handlerSave(form[item.value], item.type)"
        >
        </el-switch>
        <span style="padding-left: 30px">
          {{ item.label }}
        </span>
      </div>
      <!-- <div class="switch-setting">
        <el-switch
          v-model="form.superiorSummary"
          active-color="#ff8c2e"
          inactive-color="#f1f4f8"
          :active-value="1"
          :inactive-value='0'
          @change="handlerSave(form.superiorSummary, 'switchAutoSend')"
          >
        </el-switch>
        <span style="padding-left: 30px">
          {{ $t('otherSetting.autoSummary') }}
        </span>
        <el-tooltip
          placement="top-start"
        >
          <span slot="content">{{ $t('otherSetting.autoCollect') }}</span>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div> -->
    </el-form>
  </div>
</template>

<script>
import {
  setWorkingDayMonth,
  setWorkingDayWeek,
  openCompanyConfig,
  setFiscal,
  getWorkingDayWeek,
  getWorkingDayMonth,
  getFiscal,
  hasBeenEnableBatch
} from '@/api/companyConfig'
import { Message } from 'element-ui'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'PerformanceObjectiveParamSetting',

  data() {
    const monthDays = (rule, value, callback) => {
      if (value < 1 || value > 30) {
        callback(new Error(this.$t('otherSetting.mustOn')))
      } else if (String(value).split('.')[1] && String(value).split('.')[1].length > 2) {
        callback(new Error(this.$t('otherSetting.toFixedTwo')))
      } else {
        callback()
      }
    }
    const weekDays = (rule, value, callback) => {
      if (value < 1 || value > 7) {
        callback(new Error(this.$t('otherSetting.mustOnWeek')))
      } else if (String(value).split('.')[1] && String(value).split('.')[1].length > 2) {
        callback(new Error(this.$t('otherSetting.toFixedTwo')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false, // 财年更改提示框
      form: {
        month: 3,
        monthDays: 0,
        weekDays: 5
      },
      loading: true,
      rules: {
        monthDays: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('nouns.workDaysMonth')
            }),
            trigger: 'blur'
          },
          {
            type: 'number',
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('nouns.number')
            }),
            trigger: 'blur'
          },
          { validator: monthDays, trigger: 'blur' }
        ],
        weekDays: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('nouns.workDaysWeek')
            }),
            trigger: 'blur'
          },
          {
            type: 'number',
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('nouns.number')
            }),
            trigger: 'blur'
          },
          { validator: weekDays, trigger: 'blur' }
        ]
      },
      switchList: [
        {
          value: 'allowGovernorSet',
          type: 'switchManage',
          label: this.$t('otherSetting.alowSet')
        },
        {
          value: 'allowStaffSet',
          type: 'switchStaff',
          label: this.$t('otherSetting.alowStaffSet')
        },
        {
          value: 'performanceLeave',
          type: 'switchLeave',
          label: this.$t('otherSetting.fixedLeave')
        }
      ]
    }
  },

  created() {
    this.getForm()
  },

  methods: {
    getForm() {
      getFiscal({})
        .then((data) => {
          this.form.month = data.result.startMonth
        })
        .catch(() => {})
      getWorkingDayMonth({})
        .then((data) => {
          this.form.monthDays = data.result.workingDayMonth
        })
        .catch(() => {})
      getWorkingDayWeek({})
        .then((data) => {
          this.form.weekDays = data.result.workingDayWeek
        })
        .catch(() => {})
      hasBeenEnableBatch({
        aliasList: [
          'performanceSet',
          'performanceManageSet',
          'aggDepartmentPerfor',
          'fixedLeaveMark',
          'performanceLeave'
        ]
      })
        .then((data) => {
          const list = data.result.list
          list.forEach((item) => {
            switch (item.alias) {
              case 'performanceSet':
                this.form.allowStaffSet = item.enable
                break
              case 'performanceManageSet':
                this.form.allowGovernorSet = item.enable
                break
              case 'aggDepartmentPerfor':
                this.form.superiorSummary = item.enable
                break
              case 'fixedLeaveMark':
              case 'performanceLeave':
                this.form[item.alias] = item.enable
                break
            }
          })
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    // 财年保存
    fiscalYearSave() {
      const params = {
        startMonth: this.form.month
      }
      setFiscal(params)
        .then((data) => {
          Message({
            type: 'success',
            message: this.$t('message.saveSuccess')
          })
          // LS.set('VUE-performance-fiscal-year', data.fiscalYear)
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    fiscalYearCancel() {
      getFiscal({})
        .then((data) => {
          this.form.month = data.startMonth
          this.dialogVisible = false
        })
        .catch(() => {})
    },
    // 保存数据，很难受，这种接口太差了，接口不愿意改，只能将就
    handlerSave(val, type) {
      if (val === '') {
        // 空值的时候不保存
        return false
      }
      const params = {}
      switch (type) {
        case 'workingDayMonth':
          if (String(val).split('.')[1] && String(val).split('.')[1].length > 2) {
            return false
          }
          params[type] = val
          setWorkingDayMonth(params)
            .then((data) => {
              Message({
                type: 'success',
                message: this.$t('message.saveSuccess')
              })
            })
            .catch(() => {})
          break
        case 'workingDayWeek':
          if (String(val).split('.')[1] && String(val).split('.')[1].length > 2) {
            return false
          }
          params[type] = val
          setWorkingDayWeek(params)
            .then((data) => {
              Message({
                type: 'success',
                message: this.$t('message.saveSuccess')
              })
            })
            .catch(() => {})
          break
        case 'switchManage':
          params.value = val
          params.alias = 'performanceManageSet'
          this.openCompanyConfig(params)
          break
        case 'switchStaff':
          params.value = val
          params.alias = 'performanceSet'
          this.openCompanyConfig(params)
          break
        case 'switchMark':
          params.value = val
          params.alias = 'fixedLeaveMark'
          this.openCompanyConfig(params)
          break
        case 'switchLeave':
          params.value = val
          params.alias = 'performanceLeave'
          this.openCompanyConfig(params)
          break
      }
    },
    // 设置防抖，减少重复提交的次数
    @xbb.debounceWrap(500)
    openCompanyConfig(params) {
      openCompanyConfig(params)
        .then((data) => {
          Message({
            type: 'success',
            message: this.$t('message.saveSuccess')
          })
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.performance-objective-param-setting {
  padding: 30px 40px;
  .setting-title {
    padding-left: 10px;
    margin-bottom: 30px;
    color: $brand-color-5;
    border-left: 2px solid $brand-color-5;
  }
  .other-setting {
    margin-top: 60px;
  }
  .work-days {
    display: flex;
    .el-input {
      width: 100px;
    }
  }
  .switch-setting {
    line-height: 50px;
  }
}
</style>
