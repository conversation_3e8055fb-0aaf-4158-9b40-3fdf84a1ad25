<!--
 * @Description: 组件关联页面：业绩目标首页查看详情
 -->

<!--
组件关联页面：业绩目标首页查看详情
-->
<template>
  <div v-loading="loading" class="component-performance-detail detail">
    <span class="detailClose" @click="closeDetail"><i class="el-icon-close"></i></span>
    <div class="table-content">
      <span class="title">{{ name }}的全部业绩目标预览</span>
      <template v-for="(tableInfo, i) in tables">
        <div :key="i" class="setting-table">
          <span class="unit"
            ><span class="name">{{ tableInfo.name }}</span
            >单位：{{ tableInfo.unit }}</span
          >
          <template v-if="tableInfo.assessType === 2">
            <div class="center-box">
              <h2 class="box-top">
                <span class="mark-black">{{ tableInfo.data }}</span>
              </h2>
              <p class="box-mid">
                {{ tableInfo.month }}
                <el-tooltip placement="top-start">
                  <span slot="content">{{ tableInfo.monthMemo }}</span>
                  <i class="el-icon-question"></i>
                </el-tooltip>
                {{ tableInfo.year }}
                <el-tooltip placement="top-start">
                  <span slot="content">{{ tableInfo.yearMemo }}</span>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </p>
              <p class="box-bottom">{{ $t('otherSetting.fixationWay') }}</p>
            </div>
          </template>
          <template v-else>
            <!-- 月目标值 -->
            <template v-if="tableInfo.minCycle === 1">
              <!-- 全年目标值 -->
              <ul class="year-table">
                <li class="year-table-li">
                  <el-row>
                    <el-col :span="24">
                      <span class="label">{{ tableInfo.data.year[0].title }}：</span>
                      <span class="value">{{ tableInfo.data.year[0].value }}</span>
                    </el-col>
                  </el-row>
                </li>
                <li class="year-table-li">
                  <el-row>
                    <el-col :span="12">
                      <span class="label">{{ tableInfo.data.yearHalfUp[0].title }}：</span>
                      <span class="value">{{ tableInfo.data.yearHalfUp[0].value }}</span>
                    </el-col>
                    <el-col :span="12">
                      <span class="label">{{ tableInfo.data.yearHalfDown[0].title }}：</span>
                      <span class="value">{{ tableInfo.data.yearHalfDown[0].value }}</span>
                    </el-col>
                  </el-row>
                </li>
                <li class="year-table-li">
                  <el-row>
                    <el-col v-for="(item, index) in tableInfo.data.season" :key="index" :span="6">
                      <span class="label">{{ item.title }}：</span>
                      <span class="value">{{ item.value }}</span>
                    </el-col>
                  </el-row>
                </li>
                <li class="year-table-li">
                  <el-row>
                    <template v-for="(item, index) in tableInfo.data.month">
                      <el-col v-if="[1, 4, 7, 10].indexOf(index + 1) > -1" :key="index" :span="6">
                        <span class="label">{{ item.title }}：</span>
                        <span class="value">{{ item.value }}</span>
                      </el-col>
                    </template>
                  </el-row>
                </li>
                <li class="year-table-li">
                  <el-row>
                    <template v-for="(item, index) in tableInfo.data.month">
                      <el-col v-if="[2, 5, 8, 11].includes(index + 1)" :key="index" :span="6">
                        <span class="label">{{ item.title }}：</span>
                        <span class="value">{{ item.value }}</span>
                      </el-col>
                    </template>
                  </el-row>
                </li>
                <li class="year-table-li">
                  <el-row>
                    <template v-for="(item, index) in tableInfo.data.month">
                      <el-col v-if="[3, 6, 9, 12].includes(index + 1)" :key="index" :span="6">
                        <span class="label">{{ item.title }}：</span>
                        <span class="value">{{ item.value }}</span>
                      </el-col>
                    </template>
                  </el-row>
                </li>
              </ul>
            </template>
            <!-- 周目标值设置 -->
            <ul v-if="tableInfo.minCycle === 2" class="week-table">
              <li class="week-table-li">
                <span class="everyweek">
                  <span class="label">{{ tableInfo.data.month[0].title }}：</span>
                  <span class="value">{{ tableInfo.data.month[0].value }}</span>
                </span>
              </li>
              <li class="week-table-li">
                <span v-for="(item, index) in tableInfo.data.week" :key="index" class="everyweek">
                  <span class="label">{{ item.title }}：</span>
                  <span class="value">{{ item.value }}</span>
                </span>
              </li>
            </ul>
          </template>
        </div>
      </template>
    </div>
    <v-pagination
      v-if="pageHelper.rowsCount"
      class="pagination-detail"
      :current-page="pageHelper.currentPageNum"
      layout="slot, prev, pager, next, jumper"
      :page-size="2"
      :total="pageHelper.rowsCount"
      @current-change="pageChange"
    >
    </v-pagination>
  </div>
</template>

<script>
import { getPerformanceDetail } from '@/api/performance'

export default {
  name: 'ComponentPerformanceDetail',

  props: {
    getParams: {
      type: Object,
      required: true
    },
    name: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      tables: [],
      loading: true,
      pageHelper: {},
      paging: {
        page: 1,
        pageSize: 2
      }
    }
  },

  created() {
    this.getTable()
  },

  methods: {
    closeDetail() {
      this.$emit('closeDetail')
    },
    getTable() {
      const data = {
        ...this.getParams,
        page: this.paging.page,
        pageSize: this.paging.pageSize
      }
      getPerformanceDetail(data)
        .then((data) => {
          this.tables = data.result.list
          this.pageHelper = data.result.pageHelper
          this.exchangeGetTable()
          this.loading = false
        })
        .catch(() => {})
    },
    // 将获取的数据携带-1的转成null
    exchangeGetTable() {
      this.tables.forEach((table) => {
        if (table.assessType === 1) {
          // 判断是否是表格,动态数值型，有data数组
          const data = table.data
          for (const item in data) {
            data[item] = data[item].map((value) => {
              if (value.value < 0) {
                // 判断值是否未设置，未设置的是-1
                value.value = '--'
              }
              return value
            })
          }
        }
      })
    },
    pageChange(val) {
      this.paging.page = val
      this.getTable()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../styles/saas/detail.scss';
.component-performance-detail {
  .el-col {
    padding-left: 10px;
    border: 1px solid $neutral-color-3;
    span {
      display: inline-block;
      min-width: 70px;
    }
  }
  .title {
    font-size: 20px;
    color: $brand-color-5;
  }
  .table-content {
    padding: 30px;
    overflow: auto;
  }
  .setting-table {
    flex: 1 1 auto;
    margin-top: 50px;
    .unit {
      display: block;
      margin-bottom: 20px;
      font-size: 14px;
      color: $text-plain;
      .name {
        margin-right: 20px;
        font-size: 20px;
        color: $text-main;
      }
    }
    .year-table {
      margin: 0 auto;
      border: 1px solid $neutral-color-3;
      .year-table-li {
        line-height: 45px;
      }
    }
    .week-table {
      margin: 0 auto;
      border: 1px solid $neutral-color-3;
      .week-table-li {
        display: flex;
        flex-wrap: wrap;
        line-height: 45px;
        border: 1px solid $neutral-color-3;
        .everyweek {
          display: inline-block;
          width: 300px;
          padding-left: 10px;
        }
      }
    }
    .label {
      color: $text-auxiliary;
    }
    .value {
      color: $text-main;
    }
  }
  .center-box {
    width: auto;
    padding: 0 46px;
    margin: 0 auto;
    border: 1px solid $neutral-color-3;
    .box-top {
      font-size: 18px;
      line-height: 100px;
      color: $text-main;
      .mark-black {
        margin-right: 220px;
      }
    }
    .box-mid {
      line-height: 50px;
    }
    .box-bottom {
      line-height: 50px;
      border-top: 1px dashed $neutral-color-3;
    }
  }
  .pagination-detail {
    position: absolute;
    right: 0;
    bottom: 15px;
    display: flex;
    justify-content: flex-end;
    padding-right: 30px;
  }
}
</style>
