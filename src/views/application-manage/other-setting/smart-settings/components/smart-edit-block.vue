<template>
  <div class="setting_item">
    <div class="setting_title">{{ title }}</div>
    <slot name="content"></slot>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.setting {
  &_item {
    margin-bottom: 54px;
  }
  &_title {
    padding: 12px 10px;
    margin-bottom: 30px;
    font-size: 16px;
    color: $text-main;
    border-bottom: 1px solid $neutral-color-3;
  }
}
</style>
