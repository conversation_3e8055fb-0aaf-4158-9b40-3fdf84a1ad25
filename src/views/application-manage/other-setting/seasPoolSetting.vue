<!--
 * @Description: 公海池规则设置新版
 -->

<template>
  <div class="publicRule-set">
    <div class="children-head">
      <div>
        <el-tabs v-if="tabList.length > 0" v-model="activeName" :before-leave="beforeLeave">
          <template v-for="tab in tabList">
            <el-tab-pane :key="tab.alias" :label="tab.name" :name="tab.alias"> </el-tab-pane>
          </template>
        </el-tabs>
      </div>
      <div class="option-remind-time">
        <el-button v-if="showChangeModeButton" @click="changeSeasPoolMode">{{
          $t('otherSetting.changeSeasPoolMode', { changeSeasPoolMode: modeText })
        }}</el-button>
        <el-button
          v-if="isTrailSetGroup"
          :disabled="isAddGroup"
          size="small"
          type="primary"
          @click="addNewTemp"
        >
          {{ $t('operation.addGroup') }}
        </el-button>
      </div>
    </div>
    <section class="trail-wrap">
      <!-- 标准模式下的分组用原来的trail-set-group组件 -->
      <trail-set-group
        v-if="isTrailSetGroup"
        ref="standardComponent"
        :active-name="activeName"
        :active-tab-index="activeTabIndex"
        :active-template-index="currentTemplateIndex"
        :active-template-list="currentTemplateList"
        :business-type="businessType"
        :click-left.sync="clickLeft"
        :is-add-group="isAddGroup"
        :is-edit-status.sync="isEditStatus"
        :is-standard="isStandard"
        :public-senior-mode="publicSeniorMode"
        @changeAddGroup="changeAddGroup"
        @chouldLeave="chouldLeave"
      ></trail-set-group>
      <!-- 另外的组件全部用动态组件即可 -->
      <!-- 这里的标准版的左侧列表模板的接口记得用后台重新写好的统一的接口，把分组其他以及退回等都封装成了一个接口了， -->
      <!--   -->
      <div v-else class="content-column">
        <div v-if="isShowSide" class="side">
          <h3 class="title">
            <span class="name">{{ $t('otherSetting.setOption') }}</span>
          </h3>
          <ul class="cate-list">
            <li
              v-for="(temp, index) in templateList"
              :key="index"
              class="item"
              :class="{
                active: publicSeniorMode
                  ? currentRule === temp.formId
                  : currentRule === temp.ruleType
              }"
              @click="showActiveComponent(temp, index)"
            >
              <span class="name">{{ temp.name }}</span>
              <span v-if="temp.status === 0 || !temp.status" class="closeStatus status">{{
                $t('otherSetting.isClose')
              }}</span>
              <span v-else class="status">{{ $t('otherSetting.isOpen') }}</span>
            </li>
            <li v-if="!templateList.length" class="no-data">{{ $t('message.noData') }}</li>
          </ul>
        </div>
        <div v-if="!showRightContent" v-loading="showRightContentLoading" class="public-main"></div>
        <div
          v-else
          class="public-main"
          :class="{ 'not-otherSetting': !isTrailSetting, isPublicSenior: publicSeniorMode }"
        >
          <component
            :is="activeComponent"
            :key="activeName"
            :ref="activeComponent"
            :active-name="activeName"
            :active-tab-index="activeTabIndex"
            :active-template-index="currentTemplateIndex"
            :active-template-list="currentTemplateList"
            :business-type="businessType"
            :click-left.sync="clickLeft"
            :form-list="formList"
            :is-edit-status.sync="isEditStatus"
            :is-standard="isStandard"
            :public-senior-mode="publicSeniorMode"
          ></component>
        </div>
      </div>
    </section>
    <!-- 切换高级模式和标准模式弹窗客户 -->
    <seas-pool-customer-change-mode
      :business-type="businessType"
      :public-senior-mode="publicSeniorMode"
      :seas-pool-customer-mode-dialog="seasPoolCustomerModeDialog"
      @changeSeasPoolCustomerModeDialog="changeSeasPoolCustomerModeDialog"
    ></seas-pool-customer-change-mode>
    <seas-pool-clue-change-mode
      :business-type="businessType"
      :public-senior-mode="publicSeniorMode"
      :seas-pool-clue-mode-dialog="seasPoolClueModeDialog"
      @changeSeasPoolClueModeDialog="changeSeasPoolClueModeDialog"
    ></seas-pool-clue-change-mode>

    <el-dialog
      :title="$t('seasPoolClueChange.switchMode', { publicSeniorModeText: $t('listTable.senior') })"
      :visible.sync="activityDialog"
      width="600px"
    >
      <section class="activity_wrap">
        <div class="change_question">{{ $t('seasPoolClueChange.standradquestion1') }}</div>
        <div class="question_answer_wrap">
          <ul>
            <li>
              <h3>{{ $t('seasPoolClueChange.activity_q1') }}</h3>
              <p>
                {{ $t('seasPoolClueChange.activity_q1answer') }}
              </p>
            </li>
            <li class="answer_list">
              <h3>{{ $t('seasPoolClueChange.activity_q2') }}</h3>
              <p>
                {{ $t('seasPoolClueChange.activity_q2answer') }}
              </p>
            </li>
            <li class="answer_list">
              <h3>{{ $t('seasPoolClueChange.activity_q3') }}</h3>
              <p>
                {{ $t('seasPoolClueChange.activity_q3answer') }}
              </p>
            </li>
          </ul>
        </div>
        <div class="change_question">{{ $t('seasPoolClueChange.activity_q4') }}</div>
        <div class="question_answer_wrap">
          <ul>
            <li>
              <p>
                {{ $t('seasPoolClueChange.activity_q4answer') }}
              </p>
            </li>
          </ul>
        </div>
      </section>
      <div slot="footer" class="change_pool_bottom_btn">
        <el-button @click="activityDialog = false">{{ $t('decisionTree.cancel') }}</el-button>
        <el-button type="primary" @click="concatAsk">{{
          $t('seasPoolClueChange.askefu')
        }}</el-button>
      </div>
    </el-dialog>

    <div class="contact-wrapper">
      <el-dialog
        :title="$t('appModule.lookDetail')"
        top="15vh"
        :visible.sync="concatDialog"
        width="30%"
      >
        <div class="seas_pool_concat_wrap">
          <img
            alt=""
            height="120"
            src="https://cloudcode-usercode.oss-cn-beijing.aliyuncs.com/module-center/static/test/core/img/trail_img.d5d148b1.jpg"
            width="154"
          />
          <h4>{{ $t('appModule.detailContent') }}</h4>
          <h4>0571-28834699</h4>
          <el-button class="confirm_pool" @click="concatDialog = false">{{
            $t('teamManage.confirm')
          }}</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// 右侧动态组件引入
import { mapState, mapGetters } from 'vuex'
import { Message } from 'element-ui'
import businessEnum from '@/constants/common/business-type.js'
// import customerSetDialog from './customerSetDialog.vue' // 高级模式和标准模式切换弹窗
// import trailSetDialog from './trailSetDialog.vue' // 高级模式和标准模式切换弹窗
import trailSetGroup from './trailSetGroup' // 分组标准
import publicGroupSenior from './seas-pool-components/publicGroupSenior.vue' //分组高级
import otherSettingClue from './otherSettingClue.vue' // 其他设置标准（高级和标准公用这一个）
import otherSettingCustomer from './otherSettingCustomer.vue' // 其他设置标准（高级和标准公用这一个）
import seasPoolCustomerChangeMode from './seas-pool-components/seasPoolCustomerChangeMode.vue' //切换模式弹窗(线索)
import seasPoolClueChangeMode from './seas-pool-components/seasPoolClueChangeMode.vue' //切换模式弹窗(线索)
import automaticRollbackStandard from './seas-pool-components/automatic-rollback-standard.vue' // 自动退回标准线索
import customerRollbackStandard from './seas-pool-components/customer-rollback-standard.vue' // 自动退回标准 客户
import automaticRollbackSenior from './seas-pool-components/automatic-rollback-senior.vue' // 自动退回高级线索
import {
  getPublicSeasPool,
  getPublicRuleList,
  getSeniorPublicRuleList,
  concatAsk
} from '@/api/market-manage'
import { getChangePublicPoolStatus } from '@/api/publicGroupSet'

export default {
  name: 'SeasPoolSetting',
  components: {
    PublicGroupSenior: publicGroupSenior,
    TrailSetGroup: trailSetGroup,
    AutomaticRollbackSenior: automaticRollbackSenior,
    AutomaticRollbackStandard: automaticRollbackStandard,
    OtherSettingClue: otherSettingClue,
    OtherSettingCustomer: otherSettingCustomer,
    SeasPoolClueChangeMode: seasPoolClueChangeMode,
    SeasPoolCustomerChangeMode: seasPoolCustomerChangeMode,
    CustomerRollbackStandard: customerRollbackStandard
  },
  beforeRouteLeave(to, from, next) {
    this.beforeLeave('leave')
      .then(() => next())
      .catch(() => {})
  },
  data() {
    return {
      showChangeModeButton: false,
      modeText: '',
      concatDialog: false,
      isAddGroup: false,
      clickLeft: false,
      currentRule: 1,
      showRightContentLoading: true,
      publicSeniorMode: false,
      isEdit: false,
      businessType: 100,
      showRightContent: false,
      activityDialog: false, //营销页弹窗
      seasPoolClueModeDialog: false, //切换弹窗线索
      seasPoolCustomerModeDialog: false, //切换弹窗客户
      formTree: [], // 分组
      formList: [],
      activeName: '', // 上方的tab名字
      activeTabIndex: 1, //上方选中的tab下标
      currentTemplateList: {}, // 当前选中的左侧列表的内容
      currentTemplateIndex: 1, // 当前选中的左侧列表的id
      activeComponent: '', //要显示的动态组件
      // activeComponent: 'publicGroupSenior',
      seniorFormList: [],
      tabList: [], //上方tab列表
      isEditStatus: false, // 分组编辑状态
      isStandard: false, //是否是高级模式
      templateList: [], // 左侧列表
      enumObj: {}
      // customerSetDialogVisible: false,
      // trailSetDialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['feeType']),
    ...mapState({
      isCluePublicSeniorMode: (state) => state.user.isCluePublicSeniorMode,
      isCustomerPublicSeniorMode: (state) => state.user.isCustomerPublicSeniorMode
    }),
    // 如果是标准模式并且是分组的话，那么就采用trail-set-group组件
    isTrailSetGroup() {
      return this.activeName === 'publicGroupRuleSet' && !this.publicSeniorMode
    },
    isTrailSetting() {
      return this.activeName === 'publicOtherRuleSet' || this.activeName === 'publicBackRuleSet'
    },
    // 没有左侧列表的：只有线索下需要区分一下，标准模式下线索的分配和退回以及高级模式下的分配是没有左侧列表的
    isShowSide() {
      if (this.publicSeniorMode) {
        if (
          this.activeName === 'publicDistributionRuleSet' ||
          this.activeName === 'publicOtherRuleSet'
        ) {
          return false
        }
      } else {
        if (
          this.activeName === 'publicDistributionRuleSet' ||
          (this.activeName === 'publicBackRuleSet' && this.businessType === 8000) ||
          this.activeName === 'publicOtherRuleSet'
        ) {
          return false
        }
      }
      return true
    }
  },
  watch: {
    $route: {
      handler: function (val, oldVal) {
        this.businessType = val.path === '/Setting/clueSeasPool' ? 8000 : 100
      },
      // 深度观察监听
      deep: true,
      immediate: true
    },
    activeName(newVal) {
      const seasPoolComponentStandard = {
        publicGroupRuleSet: trailSetGroup, //因为客户和线索标准模式下分组是一样的
        publicBackRuleSet: this.enumObj[this.businessType].publicBackRuleSet, //因为自动退回下客户和线索有很多不同，所以分开组件做
        publicDistributionRuleSet: automaticRollbackSenior,
        publicOtherRuleSet: this.enumObj[this.businessType].publicOtherRuleSet
      }
      const seasPoolComponentSenior = {
        publicGroupRuleSet: publicGroupSenior, //因为客户和线索高级模式下分组是一样的
        publicBackRuleSet: automaticRollbackSenior, //高级模式下用automaticRollbackSenior
        publicDistributionRuleSet: automaticRollbackSenior, //自动分配
        publicTransferRuleSet: automaticRollbackSenior, //自动转移
        publicOtherRuleSet: this.enumObj[this.businessType].publicOtherRuleSetSenior //其他设置
      }
      const seasComponents = this.publicSeniorMode
        ? seasPoolComponentSenior
        : seasPoolComponentStandard
      if (seasComponents[newVal]) {
        this.activeComponent = seasComponents[newVal]
      }
      // this.changeTab(newVal)
    }
  },
  created() {
    // 获取tab
    this.getPublicSeasPool()
    this.enumObj = {
      // 线索
      [businessEnum.SALES_LEADS]: {
        publicBackRuleSet: automaticRollbackStandard,
        publicOtherRuleSet: otherSettingClue,
        publicOtherRuleSetSenior: otherSettingClue,
        publicSeniorMode: this.isCluePublicSeniorMode
      },
      // 客户
      [businessEnum.CUSTOMER_MANAGEMENT]: {
        publicBackRuleSet: customerRollbackStandard,
        publicOtherRuleSet: otherSettingCustomer,
        publicOtherRuleSetSenior: otherSettingCustomer,
        publicSeniorMode: this.isCustomerPublicSeniorMode
      }
    }
    this.publicSeniorMode = this.enumObj[this.businessType].publicSeniorMode
    this.modeText = !this.publicSeniorMode
      ? this.$t('listTable.senior')
      : this.$t('chartEdit.standard')
    // 获取模板列表
    this.getLeftTemplateList()
  },
  methods: {
    concatAsk() {
      this.concatDialog = true
      concatAsk().then((res) => {
        console.log(res)
      })
    },
    changeAddGroup(val) {
      this.isAddGroup = val
    },
    // 分组为编辑状态时，禁止进行其他操作
    tempIsEdit() {
      if (this.isEditStatus) {
        Message({
          type: 'error',
          message: this.$t('otherSetting.savePls')
        })
        return true
      } else {
        return false
      }
    },
    // 新建分组
    addNewTemp() {
      // ref名用对象获取不到
      this.$refs['standardComponent'].addNewTemp()

      // this.$nextTick(() => {
      //   this.isEditStatus = true
      // })
    },
    changeSeasPoolMode() {
      // 如果是旗舰版，跳出切换弹窗
      if (this.$feeType.checkFeatureEnable('CRM.publicPoolHighMode')) {
        getChangePublicPoolStatus({ businessType: this.businessType }).then((res) => {
          if (res.result.status === 3 && !this.publicSeniorMode) {
            Message({
              type: 'error',
              message: this.$t('seasPoolClueChange.changeFrequence')
            })
            return
          }
          if (this.businessType === 8000) {
            this.seasPoolClueModeDialog = true
          } else {
            this.seasPoolCustomerModeDialog = true
          }
        })
      } else {
        this.activityDialog = true
      }

      // 如果是高级版和标准版,跳出营销页弹窗
    },
    // 切换模式线索弹窗显隐
    changeSeasPoolClueModeDialog(value) {
      this.seasPoolClueModeDialog = value
    },
    // 切换模式客户弹窗显隐
    changeSeasPoolCustomerModeDialog(value) {
      this.seasPoolCustomerModeDialog = value
    },
    changeTab(newVal) {
      this.currentTemplateIndex = 1
      const findItem = this.tabList.filter((item) => item.alias === newVal)
      this.activeTabIndex = findItem[0].tabType
      this.getLeftTemplateList()
      this.isAddGroup = false
    },
    // 点击左侧列表，显示右侧动态组件
    showActiveComponent(temp, index) {
      this.beforeLeave('leave')
        .then(() => {
          this.clickLeft = true
          this.formId = temp.formId
          this.currentRule = this.publicSeniorMode ? temp.formId : temp.ruleType
          this.currentTemplateList = temp
          this.currentTemplateIndex = index + 1
        })
        .catch(() => {})
    },
    // 获取tabs

    getPublicSeasPool() {
      const seasPoolData = {
        businessType: this.businessType
      }
      getPublicSeasPool(seasPoolData)
        .then(({ result }) => {
          this.showChangeModeButton = result.changePublicPoolPerm ? true : false
          this.tabList = result.groupSetItem
          this.activeName = this.tabList[0]['alias']
          this.activeIndex = this.tabList[0]['tabType']
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取左侧模板列表
    getLeftTemplateList(ruleType = 1) {
      // tabType 1 分组，2退回，3转移，4分配，5其他
      // 标准模式下
      if (!this.publicSeniorMode) {
        // 获取左侧列表信息
        getPublicRuleList({
          tabType: this.activeTabIndex,
          businessType: this.businessType
        })
          .then((data) => {
            const { ruleList, formList } = data.result
            if (ruleList) {
              this.templateList = ruleList
              this.currentTemplateList =
                this.templateList.length > 0 ? this.templateList[ruleType - 1] : null
            }
            this.formList = formList
            this.showRightContent = true
            this.showRightContentLoading = false
            this.currentRule =
              this.templateList.length > 0 ? this.templateList[ruleType - 1].ruleType : null
          })
          .catch((err) => {
            console.log(err)
          })
      } else {
        // 获取左侧列表信息
        getSeniorPublicRuleList({
          tabType: this.activeTabIndex,
          businessType: this.businessType
        })
          .then((data) => {
            this.showRightContent = true
            this.showRightContentLoading = false
            this.templateList = data.result.formList
            this.currentTemplateList =
              this.templateList.length > 0 ? this.templateList[ruleType - 1] : null
            this.currentRule = this.templateList[ruleType - 1].formId
          })
          .catch(() => {})
      }
    },
    chouldLeave(e) {
      console.log('监听', e)
      this.isEdit = !e
    },
    beforeLeave(activeName) {
      return new Promise((resolve, reject) => {
        // 是否是分配
        // 整合保存才提示，自动分配是操作后立马保存，所以不需要提示
        const isDistribution = this.$children.some((item) => item.isDistribution === true)
        // 是否被编辑过，saveHigh为false则表示可以点击保存按钮
        const idEdit = this.$children.some((item) => item.saveHigh === false)
        // 如果是离开当前页面则不需要请求切换tab的接口
        const activeIndex = this.tabList.findIndex((item) => item.alias === activeName)
        if ((idEdit && !isDistribution) || this.isAddGroup) {
          this.$confirm(this.$t('seasPool.leaveTip'), this.$t('message.confirmTitle'), {
            confirmButtonText: this.$t('formDataDetail.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          })
            .then((res) => {
              resolve()
              activeIndex !== -1 && this.changeTab(activeName)
            })
            .catch(() => {
              reject()
            })
        } else {
          resolve()
          activeIndex !== -1 && this.changeTab(activeName)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../styles/saas/item-select.scss';
.publicRule-set {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  height: 98%;
  .children-head {
    // 页面头部
    position: relative;
    display: flex;
    flex: 0 0 50px;
    justify-content: space-between;
    height: 50px;
    padding-left: 30px;
    font-size: 0;
    text-align: right;
    background-color: $base-white;
    .option-remind-time {
      display: inline-block;
      margin-top: 9px;
      margin-right: 25px;
    }
  }
}
</style>

<style lang="scss">
.publicRule-set {
  .children-head {
    // 页面头部
    .el-tabs__item {
      height: 50px;
      line-height: 50px;
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .staff-select {
    display: inline-block;
  }
}
.contact-wrapper {
  .el-dialog {
    > .el-dialog__header {
      background: $base-white;
      border-radius: 8px 8px 0 0;
    }
    border-radius: 8px;
    .seas_pool_concat_wrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        margin-bottom: 10px;
      }
      h4 {
        margin-top: 10px;
      }
      .confirm_pool {
        width: 160px;
        height: 38px;
        margin-top: 40px;
        color: $base-white;
        background-image: linear-gradient(to top right, #ffaa3d, $brand-color-5);
        box-shadow: 0 6px 18px -10px rgba(255, 145, 61, 0.5);
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.trail-wrap {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  height: calc(100% - 60px);
  .content-column {
    display: flex;
    flex: 1 1 auto;
    height: calc(100% - 60px);
    padding: 10px;
    .side {
      box-sizing: border-box;
      flex: 0 0 300px;
      width: 300px;
      padding: 15px;
      overflow: auto;
      background-color: $base-white;
      .cate-list {
        margin-top: 20px;
        .item {
          display: flex;
          justify-content: space-between;
          height: 38px;
          padding: 0 12px;
          margin-bottom: 5px;
          overflow: hidden;
          line-height: 38px;
          text-align: right;
          cursor: pointer;
          transition: all 0.3s;
          .name {
            max-width: 200px;
            overflow: hidden;
            font-size: 14px;
            color: $bg-menu;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .status {
            font-size: 12px;
            color: $text-plain;
            &.closeStatus {
              color: $text-grey;
            }
          }
          &.active,
          &.active:hover {
            background-color: $bg-blue;
            // .name,
            // .status {
            //   color: $base-white;
            // }
          }
          &:hover {
            background-color: $bg-primary;
          }
        }
      }
    }
    .public-main {
      box-sizing: border-box;
      flex: 1 1 auto;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .isPublicSenior {
      margin-left: 10px;
    }
    .not-otherSetting {
      background-color: $base-white;
    }
    .title {
      height: 30px;
      font-size: 15px;
      border-bottom: 2px solid $neutral-color-3;
      .name {
        display: inline-block;
        height: 30px;
        padding: 0 5px;
        line-height: 24px;
        border-bottom: 2px solid $brand-color-5;
      }
    }
  }
}

.activity_wrap {
  position: relative;
  height: 450px;
  overflow: auto;
  .change_question {
    margin-bottom: 10px;
    font-size: 16px;
    color: $text-main;
  }
  .question_answer_wrap {
    padding: 16px;
    margin-bottom: 24px;
    background: $bg-blue;
    .answer_list {
      margin-top: 24px;
    }
    li {
      h3 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: bold;
        color: $text-main;
      }
      p {
        font-size: 12px;
        line-height: 1.5;
        color: $text-plain;
      }
    }
  }

  .change_pool_bottom_btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 54px;
  }
}
</style>
