<!--
 * @Description: 新建数据集弹窗
-->
<template>
  <div>
    <el-dialog
      :before-close="closeDialog"
      custom-class="create-dataset-dialog"
      :title="`${isEdit ? $t('operation.edit') : $t('operation.add')}${$t('dataSet.name')}`"
      :visible.sync="dialogShow"
      width="50%"
    >
      <div class="reate-dataset-content">
        <el-form ref="dataSetForm" label-width="100px" :model="dataSetForm" :rules="rules">
          <el-form-item :label="$t('label.name')" prop="name">
            <el-input v-model.trim="dataSetForm.name" :maxlength="20"></el-input>
          </el-form-item>
          <el-form-item :label="$t('homeManage.describe')" prop="memo">
            <el-input
              v-model="dataSetForm.memo"
              :maxlength="100"
              :rows="5"
              type="textarea"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="!dataSetForm.id"
            :label="$t('chartManagement.viewDataRange')"
            prop="dataPermissionArray"
          >
            <el-radio-group v-model="dataSetForm.dataPermissionArray" size="small">
              <el-radio :label="0">{{ $t('chartManagement.allDataPermission') }}</el-radio>
              <el-radio :label="1">{{ $t('chartManagement.dataFormPermission') }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="reate-dataset-footer">
        <el-button @click="closeDialog">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('operation.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      originForm: {
        id: '',
        name: '',
        memo: ''
      },
      dataSetForm: {
        id: '',
        name: '',
        memo: '',
        dataPermissionArray: 0
      },
      dialogShow: this.show,
      rules: {
        name: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', { attr: this.$t('label.name') }),
            trigger: 'blur'
          },
          {
            min: 0,
            max: 20,
            message: this.$t('homeManage.nameLength', { num1: 0, num: 20 }),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    if (JSON.stringify(this.value) !== '{}') {
      this.dataSetForm = this.value
    }
    this.originForm = JSON.parse(JSON.stringify(this.dataSetForm))
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('close', false)
    },
    submit() {
      if (this.dataSetForm.name === '') {
        this.$message.error(this.$t('placeholder.inputPls', { attr: this.$t('label.name') }))
        return
      }
      if (
        this.dataSetForm.id &&
        this.dataSetForm.name === this.originForm.name &&
        this.dataSetForm.memo === this.originForm.memo
      ) {
        this.$message.error(this.$t('dataSet.sameName'))
        return
      }
      this.$emit('submit', this.dataSetForm)
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss">
.create-dataset-dialog {
  .el-input {
    width: 80%;
  }
  .el-form-item__label:before {
    position: absolute;
    left: 65px;
  }
}
</style>

<style lang="scss" scoped>
.create-dataset-dialog {
  .reate-dataset-content {
    min-height: 220px;
  }
  .reate-dataset-footer {
    padding-top: 15px;
    text-align: right;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
  }
  .inline-box {
    display: inline-block;
  }
  .block-padding {
    padding-top: 5px;
    padding-left: 67px;
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 390px;
    }
  }
  .mt-5 {
    margin-top: 5px;
  }
}
</style>
