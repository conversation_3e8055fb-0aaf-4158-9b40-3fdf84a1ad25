<!--
 * @Description: 自助数据集-过滤条件 -节点的过滤条件设置
-->
<template>
  <div class="dataset-filter-condition">
    <div v-if="node.type !== 'unpivot'" class="node-tab">
      <div
        v-for="tab in inputNodeInfos"
        :key="tab.id"
        class="node-tab-item"
        :class="{ 'node-tab-active': nodeInfo.id === tab.id }"
        @click="tabChange(tab)"
      >
        {{ tab.name }}
      </div>
    </div>
    <div class="set-box">
      <div class="node-box">
        <div class="title">
          {{ $t('form.formName') }}:
          <span>{{ nodeInfo.name }}</span>
        </div>
        <div ref="scrollBox" class="condition-box">
          <!-- 过滤条件 -->
          <div
            v-for="(filter, index) in node.filter[nodeInfo.id]"
            :key="nodeInfo.id + 'condition' + index"
            class="condition"
          >
            <!-- 字段选择 -->
            <el-select
              v-model="node.filter[nodeInfo.id][index]"
              :placeholder="$t('placeholder.choosePls', { attr: $t('nouns.field') })"
              value-key="attr"
            >
              <el-option
                v-for="items in getSelectAbleField"
                :key="items.attr"
                :disabled="items.disable"
                :label="items.attrName"
                :value="items"
              ></el-option>
            </el-select>
            <!-- 条件设置与选择 -->
            <filter-condition-list
              v-if="JSON.stringify(filter) !== '{}'"
              :chart-explain="chartExplain"
              :condition-list="[node.filter[nodeInfo.id][index]]"
              :hover-bg="true"
              :is-not-panel="false"
              layout="vertical"
              :restrict-width="true"
              :use-delete="true"
              :use-symbol="true"
              @conditionListChange="
                (val) => {
                  node.filter[nodeInfo.id][index] = val[0]
                }
              "
            ></filter-condition-list>
            <!-- 删除 -->
            <div class="condition-delete" @click="deleteCondition(nodeInfo.id, index)">
              <i class="el-icon-delete"></i>
            </div>
          </div>
        </div>
        <div v-if="addConditionShow" class="add-condition">
          <i class="el-icon-plus"></i>
          <span @click="addNewEmptyCondition(nodeInfo.id)">{{
            $t('formDesign.addFilterCondition')
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import FilterConditionList from '@/components/filter/filter-condition-list.vue'
import { findInputNodes } from './js/utils'
import { findBeforeNodes } from '@/views/application-manage/data-set/edit/components/node-data-set/js/utils.js'
import xbb from '@xbb/xbb-utils'

export default {
  components: { FilterConditionList },
  props: {
    node: {}, // 当前节点信息
    nodeObj: {}, // 全部的节点信息
    // 节点中字段选择的限制规则
    fieldsRule: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      nodeInfo: '', // 当前设置的节点
      inputNodeInfos: {}, // 接入点的信息
      // 虚拟的chartExplain，用于使用filter-condition-list需要
      chartExplain: {
        single: 2,
        dataSetId: 0,
        // 特殊：为获取FilterConditionList的下拉内容；取当前所操作过滤添加所属节点与其节点之前的节点
        beforeNodes: {},
        //  特殊：标记当前节点前的全部节点是否均配置正确，配置正确才可以发起接口获取下拉内容
        beforeNodesPass: false,
        // 特殊：当前所操作的过滤条件所属的节点id
        belongNodeId: ''
      }
    }
  },
  created() {
    this.inputNodeInfos = findInputNodes(this.nodeObj, this.node)
    this.initNewCondition()
    this.nodeInfo = Object.values(this.inputNodeInfos)[0]
    this.getBeforeNodes()
  },
  computed: {
    // 可供选择的字段
    getSelectAbleField() {
      if (!this.nodeInfo.field) {
        return []
      }
      // 已选的字段不可以再选择
      const selectedFields = this.node.filter[this.nodeInfo.id].map((field) => {
        return field.attr + field.formId
      })
      const fieldsCopy = xbb.deepClone(this.nodeInfo.field).filter((field) => {
        return this.filterFieldCondition(field)
      })
      // 对拷贝的字段列表进行处理
      const fieldsHandle = this.copyFieldsHandle(fieldsCopy)

      return fieldsHandle.map((field) => {
        if (selectedFields.includes(field.attr + field.formId)) {
          this.$set(field, 'disable', true)
        }
        return field
      })
    },
    // 不可在下拉框内选择的字段合集(unSelectAble用于数据集中的过滤条件设置)
    unSelectAbleMap() {
      return this.fieldsRule.unSelectAble || {}
    },
    selectAbleList() {
      return this.fieldsRule.selectAble || []
    },
    // 显示添加过滤条件
    addConditionShow() {
      return this.node.filter[this.nodeInfo.id].length < 15
    }
  },
  methods: {
    // 获取当前所选字段所属节点和其之前的节点，包括一些获取接口所必须的参数
    getBeforeNodes() {
      this.chartExplain.belongNodeId = this.nodeInfo.id
      const beforeNodes = JSON.parse(
        JSON.stringify(
          findBeforeNodes(
            this.nodeObj[this.chartExplain.belongNodeId],
            { [this.chartExplain.belongNodeId]: this.nodeObj[this.chartExplain.belongNodeId] },
            this.nodeObj
          )
        )
      )
      this.chartExplain.beforeNodes = beforeNodes
      this.chartExplain.beforeNodesPass = Object.values(beforeNodes).every((node) => {
        return node.passFlag === 1
      })
    },

    /**
     * 过滤条件的字段选择列表的过滤规则
     *
     */
    filterFieldCondition(field) {
      // ! 如果字段中存在originalAttr，则使用originalAttr判断其是否在unSelectAbleMap中
      // !screenTypeAble 满足screenType的字段可以进行选择
      return (
        this.selectAbleList.includes(field.fieldType) &&
        !(
          this.unSelectAbleMap[field.businessType] &&
          this.unSelectAbleMap[field.businessType].includes(
            field.originalAttr ? field.originalAttr : field.attr
          )
        ) &&
        field.status !== 2 &&
        (field.screenType === undefined
          ? true
          : this.fieldsRule.screenTypeAble.select.includes(field.screenType))
      )
    },

    /**
     * 拷贝字段的处理, 拷贝字段处理后返回一个新的列表
     * @param fieldList
     * @returns {*[]}
     */
    copyFieldsHandle(fieldList) {
      const fieldsHandle = []
      fieldList.forEach((field) => {
        // 将子表单字段拆分出子字段，并修改其attr与attrName
        if (
          field.attrType === 'subForm' &&
          field.subForm &&
          field.subForm.items &&
          field.subForm.items.length
        ) {
          field.subForm.items.forEach((subFormField) => {
            subFormField.rootAttr = field.rootAttr
            subFormField.attr = field.attr + (subFormField.splitKey || '.') + subFormField.attr
            subFormField.attrName = field.attrName + '--' + subFormField.attrName
            subFormField.attrNameEn = field.attrNameEn + '--' + subFormField.attrNameEn
            fieldsHandle.push(subFormField)
          })
        } else {
          fieldsHandle.push(field)
        }
      })
      return fieldsHandle
    },

    tabChange(tab) {
      this.nodeInfo = tab
      this.getBeforeNodes()
    },
    // 如果当前合并节点filterCondition还没有接入节点id对应的容器，则初始化
    initNewCondition() {
      for (const key in this.inputNodeInfos) {
        if (
          !this.node.filter[key] ||
          (this.node.filter[key] && this.node.filter[key].length === 0)
        ) {
          this.$set(this.node.filter, key, [{}])
        }
      }
    },
    // 新增过滤条件
    addNewEmptyCondition(id) {
      // 如果有未完成的过滤条件，需要先完成后才可以继续添加
      if (this.node.filter[id].length) {
        const lastCondition = this.node.filter[id][this.node.filter[id].length - 1]
        let pass = false
        switch (lastCondition.symbol) {
          // ?? 选择了为空或不为空，不会受此限制
          case 'empty':
          case 'noempty':
            pass = true
            break
          case 'custom':
            pass = lastCondition.dynamicDate
            break
          default:
            pass = lastCondition.value && lastCondition.value.length
            break
        }
        if (!pass) {
          this.$message.error(this.$t('dataSet.addFilterAgain'))
          return
        }
      }
      // 将滚动条移动到最右侧
      setTimeout(() => {
        this.$refs.scrollBox.scrollTop = this.$refs.scrollBox.scrollHeight
      }, 200)
      // eslint-disable-next-line
      this.node.filter[id].push({})
    },
    deleteCondition(nodeId, index) {
      // eslint-disable-next-line
      this.node.filter[nodeId].splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.dataset-filter-condition {
  .condition {
    .filter-condition {
      flex-direction: row !important;
    }
    .filter-condition__select {
      min-width: 182px;
    }
    .filter-condition__title {
      min-width: 140px;
      margin: 0 12px;
      margin-bottom: 0;
    }
    .filter-condition-list__content {
      margin-bottom: 0;
    }
    // .filter-condition-list {
    //   flex: 1;
    // }
    .filter-condition__select {
      flex: 1;
      .custom-box {
        position: fixed;
        top: 80px;
        left: 600px;
      }
    }
    .filter-condition__extra {
      margin-left: 5px;
      .extra-item {
        align-items: center;
        margin-top: 8px;
        white-space: nowrap;
      }
    }
    .multi-tag {
      height: auto;
    }
  }
}
</style>

<style lang="scss" scoped>
.dataset-filter-condition {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  .node-tab {
    box-sizing: border-box;
    margin: 10px 20px 0 20px;
    border-bottom: 1px solid $line-table;
    .node-tab-item {
      position: relative;
      box-sizing: border-box;
      display: inline-block;
      padding: 12px 15px;
      color: $text-plain;
      cursor: pointer;
      border: 1px solid $line-table;
      border-right: none;
      border-bottom: none;
      transition: all 0.3s;
    }
    .node-tab-item:nth-last-child(1) {
      border-right: 1px solid $line-table;
    }
    .node-tab-active {
      color: $brand-color-5;
    }
    .node-tab-active::after {
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 100%;
      height: 1px;
      content: '';
      background: $base-white;
    }
  }
  .set-box {
    width: 100%;
    height: calc(100% - 40px);
    .node-box {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 20px;
      border-right: 1px solid $line-table;
      .title {
        margin-bottom: 15px;
      }
      .condition-box {
        max-height: 225px;
        overflow-y: scroll;
        scroll-behavior: smooth;
      }
      .add-condition {
        width: 100%;
        font-size: 12px;
        color: $brand-color-5;
        span {
          cursor: pointer;
        }
      }
    }
    .node-box:nth-last-child(1) {
      border-right: none;
    }
    .condition {
      display: flex;
      flex-wrap: nowrap;
      margin-bottom: 12px;
      .condition-delete {
        display: flex;
        align-items: center;
        margin-left: 8px;

        .el-icon-delete {
          font-size: 16px;
          color: $brand-base-color-6;
          cursor: pointer;
        }
      }
    }
  }
  .tip-box {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: $text-plain;
  }
}
</style>
