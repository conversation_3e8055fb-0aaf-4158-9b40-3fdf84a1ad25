<!-- eslint-disable vue/no-mutating-props -->

<template>
  <div class="dynamic-add">
    <div class="dynamic-add__content" :style="contentStyle">
      <div v-for="(item, index) in value" :key="index" class="dynamic-add__item">
        <div class="dynamic-add__item-input">
          <el-input
            ref="input"
            v-model="value[index]"
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </div>
        <i class="dynamic-add__del-icon el-icon-delete" @click="handleItemDelete(index)" />
      </div>
    </div>
    <el-button :disabled="!canAdd" style="align-self: start" type="text" @click="handleItemAdd">
      <i class="el-icon-plus" />
      {{ addBtnText }}
    </el-button>
  </div>
</template>

<script>
/* eslint-disable vue/no-mutating-props */

export default {
  name: 'DynamicAdd',

  props: {
    value: {
      type: Array
    },
    addBtnText: {
      type: String,
      default: '添加'
    },
    max: {
      type: Number
    },
    maxHeight: {
      type: Number
    }
  },

  data() {
    return {}
  },

  computed: {
    noData() {
      return this.value.length === 0
    },
    canAdd() {
      return this.max ? this.value.length < this.max : true
    },
    contentStyle() {
      return {
        display: this.noData ? 'none' : 'block',
        maxHeight: this.maxHeight ? `${this.maxHeight}px` : ''
      }
    }
  },

  methods: {
    handleItemInput(index, $event) {
      const originalValue = [...this.value]
      originalValue[index] = $event
      this.$emit('input', originalValue)
    },
    handleItemDelete(index) {
      const originalValue = [...this.value]
      originalValue.splice(index, 1)
      this.$emit('input', originalValue)
    },
    handleItemAdd() {
      if (!this.canAdd) return

      const originalValue = [...this.value]
      originalValue.push('')
      this.$emit('input', originalValue)

      this.$nextTick(() => {
        this.focusLatestInput()
      })
    },
    focusLatestInput() {
      const input = this.$refs.input[this.$refs.input.length - 1]
      input.focus()
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-add {
  display: inline-flex;
  flex-direction: column;
  &__content {
    overflow-y: auto;
  }
  &__item {
    display: flex;
    align-items: center;
    height: 32px;
    margin-bottom: 16px;
  }
  &__item-input {
    width: 428px;
    height: 32px;
    margin-right: 12px;
  }
  &__del-icon {
    font-size: 16px;
    color: $error-base-color-6;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      color: mix($error-base-color-6, $base-white, 55%);
    }
  }
}
</style>
