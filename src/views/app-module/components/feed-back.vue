<!--
 * @Description: 问卷页
 -->
<template>
  <div class="feed-back">
    <template-header color="#f3f3f3" :is-recommend="true" position="fixed">
      <div class="header-left">
        {{ $t('appModule.leaveYourNeed') }}
      </div>
      <div class="header-right">
        <span @click="$parent.feedBack = false">{{ $t('operation.cancel') }}</span>
        <span class="confirm-btn" @click="submitForm('formBody')">{{
          $t('operation.confirm')
        }}</span>
      </div>
    </template-header>
    <div class="user-advice-container">
      <div class="tell-me">
        <img
          alt=""
          src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1577338717192&di=bce8a78d7738cc02886f421589d4c065&imgtype=0&src=http%3A%2F%2Fimg.mp.sohu.com%2Fupload%2F20170830%2Fc8e9eb8b5c2a4252bb1b1c5496f89f2d_th.png"
        />
      </div>
      <el-form
        ref="formBody"
        label-position="top"
        label-width="100px"
        :model="formData"
        :rules="opinionRules"
      >
        <el-form-item :label="$t('appModule.yourIndustry')" prop="job">
          <el-select
            v-model="formData.job"
            filterable
            :placeholder="$t('placeholder.choosePls', { attr: '' })"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('appModule.leaveYourNeed')" prop="advice">
          <p>{{ $t('appModule.leaveYourWish') }}</p>
          <el-input
            v-model="formData.advice"
            :autosize="{ minRows: 4, maxRows: 8 }"
            :placeholder="$t('placeholder.choosePls', { attr: '' })"
            resize="none"
            type="textarea"
          >
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('appModule.keyWords')" prop="tags">
          <p>{{ $t('appModule.keyWords') }}</p>
          <el-input
            v-model="formData.tags"
            autosize
            :placeholder="$t('placeholder.choosePls', { attr: '' })"
            resize="none"
            type="textarea"
          >
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('appModule.picture')" prop="images">
          <PictureUpload v-model="formData.images" :field-info="pictureFieldInfo" />
        </el-form-item>
        <el-form-item :label="$t('appModule.leaveTel')" prop="contact">
          <el-input
            v-model="formData.contact"
            autosize
            :placeholder="
              $t('placeholder.inputPls', {
                attr: $t('unit.yours', {
                  attr: $t('nouns.phoneNumber') + $t('unit.or') + $t('nouns.mail')
                })
              })
            "
            resize="none"
            type="textarea"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import templateHeader from '../components/template-header'
import PictureUpload from '@/components/all-fields/form-data-edit/PictureUpload/PictureUpload'

export default {
  name: 'FeedBack',

  components: {
    TemplateHeader: templateHeader,
    PictureUpload
  },

  data() {
    return {
      options: [],
      formData: {
        job: null,
        advice: '',
        tags: '',
        images: [],
        contact: ''
      },
      pictureFieldInfo: {
        editable: 1
      },
      opinionRules: {
        // job: [{ required: true, message: this.$t('appModule.subYourTrade'), trigger: 'change' }],
        advice: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('unit.yours') + this.$t('form.template') + this.$t('nouns.demand')
            }),
            trigger: 'blur'
          }
        ],
        tags: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('form.correspond', { attr: this.$t('label.label') })
            }),
            trigger: 'blur'
          }
        ],
        contact: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', { attr: this.$t('nouns.contactInformation') }),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return false
        }
        console.log(6666)
      })
    }
  }
}
</script>

<style lang="scss">
.user-advice-container {
  .el-form-item {
    max-width: 430px;
    margin-top: 30px;
    p {
      font-size: 13px;
      color: rgba(144, 147, 153, 1);
    }
  }
}
</style>

<style lang="scss" scoped>
div {
  box-sizing: border-box;
}
.feed-back {
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100vh;
  overflow: scroll;
  background: #f5f9fc;
}
.tell-me {
  width: 100%;
  max-width: 1100px;
  height: 360px;
  margin: 60px 0px 15px;
  img {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}
.header-left {
  margin-left: 13px;
  font-size: 24px;
  color: $text-main;
}
.header-right {
  display: flex;
  align-items: center;
  span {
    box-sizing: border-box;
    display: inline-block;
    min-width: 70px;
    height: 32px;
    padding: 10px;
    margin: 0px 6.5px;
    line-height: 10px;
    color: $text-plain;
    text-align: center;
    cursor: pointer;
    background: white;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
  }
  .confirm-btn {
    margin-left: 15px;
    color: white;
    background: $brand-color-5;
    border: 1px solid $brand-color-5;
  }
}
.user-advice-container {
  box-sizing: border-box;
  width: 1200px;
  padding: 60px 50px 30px;
  margin: 0 auto;
  overflow: scroll;
  background: white;
}
</style>
