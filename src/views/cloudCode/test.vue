<!--
 * @Description: cloudcode test
-->
<template>
  <div class="index">
    <div class="input-container" :style="{ height: showTips ? '130px' : '0px' }">
      <h3 class="tips">
        WARN:此页面仅供销帮帮开发者本地联调，非法使用可能导致数据异常，请谨慎使用
      </h3>

      <el-input v-model="url" clearable placeholder="输入调试 url">
        <template slot="prepend">
          <el-select v-model="pluginType" class="plugin-type" placeholder="请选择插件类型">
            <el-option label="菜单页" value="MENU"></el-option>
            <el-option label="详情页按钮" value="DETAIL_BTN"></el-option>
            <el-option label="详情页TAB" value="DETAIL_TAB"></el-option>
            <el-option label="列表页按钮" value="LIST"></el-option>
          </el-select>
          <el-select v-model="renderType" class="input-with-select" placeholder="请选择">
            <el-option label="qiankun" value="qiankun"></el-option>
            <el-option label="universe" value="universe"></el-option>
          </el-select>
        </template>

        <el-button slot="append" icon="el-icon-check" @click="checkLoad"></el-button>
      </el-input>
    </div>
    <el-button
      circle
      class="tips-button"
      icon="el-icon-warning"
      style="background-color: red"
      @click="() => (showTips = !showTips)"
    ></el-button>
    <pluginsContainer
      v-if="appLink"
      ref="pluginContainer"
      :entry="appLink"
      name="test"
      :on-message="onMessage"
      :props="props"
      :render="renderType"
    ></pluginsContainer>
  </div>
</template>

<script>
import pluginsContainer from '@/components/cloudcode/container'
import { mapMutations, mapActions } from 'vuex'
import utils from '@/utils'
import { getCallCenterType } from '@/api/yun-call-export.js'

export default {
  components: {
    PluginsContainer: pluginsContainer
  },
  data() {
    return {
      microApp: null,
      appLink: '',
      visible: false,
      renderType: 'qiankun',
      divDom: null,
      iframeDom: null,
      url: '',
      pluginType: 'MENU',
      showTips: true,
      props: {}
    }
  },
  mounted() {
    const devInfo = utils.LS.get('dev_cloudcode_info')
    if (devInfo) {
      this.renderType = devInfo.renderType
      this.pluginType = devInfo.pluginType
      const props = utils.LS.get('cloudCodeData')
      if (devInfo.pluginType.startsWith('DETAIL')) {
        this.props = props['DETAIL'] || {}
      } else if (devInfo.pluginType === 'LIST') {
        this.props = props['LIST'] || {}
      } else if (devInfo.pluginType === 'MENU') {
        this.props = props['MENU'] || {}
      }
      this.url = devInfo.url
      this.checkLoad()
    }
  },
  methods: {
    ...mapMutations(['SET_DETAIL_DIALOG_SHOW', 'SET_DETAIL_QUERY']),
    ...mapActions(['proExport/startExport']),
    checkLoad() {
      this.appLink = undefined
      this.$nextTick(() => {
        this.appLink = this.url
        utils.LS.set('dev_cloudcode_info', {
          url: this.url,
          renderType: this.renderType,
          pluginType: this.pluginType
        })
      })
    },
    // 测试云呼导出
    onMessage(e) {
      if (e.type === 'table-export' && e.data.flag) {
        const params = {
          promise: getCallCenterType(e.data.apiParams)
        }
        this['proExport/startExport'](params)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.index {
  width: 100%;
  height: calc(100vh - 60px);
  overflow-y: scroll;
}
.tips {
  margin-top: 30px;
  margin-bottom: 30px;
  color: red;
  text-align: center;
}
.input-container {
  width: 500px;
  margin: 0 auto;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
}
.plugin-type {
  width: 140px;
  margin-right: 5px;
}
.input-with-select {
  width: 100px;
}
.tips-button {
  position: absolute;
  top: 20px;
  right: 30px;
}
</style>
