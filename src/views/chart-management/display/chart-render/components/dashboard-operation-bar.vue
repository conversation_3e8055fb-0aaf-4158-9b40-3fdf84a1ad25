<template>
  <div class="operation-bar__wrap" :style="indexTopBackground">
    <template v-for="(item, index) of chartOptionListCopy">
      <div
        v-if="`${parentIndex}|${index}` === selectedIndex"
        :key="`${item.id}-${index}`"
        class="operation-bar"
      >
        <div class="chart-render__title" :style="indexTopBackground">
          <span class="chart-render__text" :style="indexTopTitle">{{ item.name }}</span>
        </div>
        <div class="right-operation" :style="indexTopBackground">
          <div v-if="isCustomDashboard && globalList.length > 0" class="chart-render_select">
            <!-- 常用筛选范围和时间选择组件 当没有选择全局查询字段和不是自定义仪表盘的时候 不显示该组件  -->
            <!-- 重置图标 -->
            <el-button v-if="isShowReset" class="clear-icon" @click="resetNomalSelect">
              <i class="web-icon-qingliclear web-iconfont" :style="indexTopTitle"></i>
            </el-button>
            <div class="select-item_box" :style="indexTopTitle">
              <!--              <span class="select-form__label">{{ $t('form.chooseRange') }}</span>-->
              <multi-tag
                ref="multiTag"
                class="select-form__multi-select"
                :is-bi="true"
                :prop-format="mulitFormat"
                :selected-tag="checkId"
                :style="{
                  ...indexTopBackground,
                  ...indexTopTitle
                }"
                @click.native="personSelectVisitOpen"
                @tagDelete="tagDelete"
              >
              </multi-tag>
            </div>
            <div class="select-item_box" :style="indexTopTitle">
              <!--              <span class="select-item__label">{{ $t('form.chooseTime') }}</span>-->
              <time-selector
                ref="timeSelector"
                class="select-item__time"
                :color="indexTopTitle"
                :is-bi="true"
                :is-fiscal-year="true"
                :need-bottom-end="true"
                :set-default="false"
                :style="indexTopTitle"
                @deleteParam="deleteParam"
                @saveFilter="saveFilter"
              >
              </time-selector>
            </div>
          </div>
          <div class="chart-render__button-box" :style="indexTopTitle">
            <!-- 分享按钮 -->
            <div
              v-if="showShare && !isFullScreen && !chartListLoading"
              class="bi-colorful-btn chart-render__button operate_editbutton"
              :style="indexTopTitle"
              @click="shareClick"
            >
              <i class="icon-share-line t-iconfont" :style="indexTopTitle" />
              <span :style="indexTopTitle"> 分享 </span>
            </div>
            <!-- 订阅按钮 -->
            <div
              v-if="showSubscription && !isFullScreen && !chartListLoading"
              v-tooltip="subscriptionCompute.tooltip"
              class="bi-colorful-btn chart-render__button operate_editbutton"
              :style="indexTopTitle"
              @click="subscriptionClick"
            >
              <i :class="subscriptionCompute.icon" :style="indexTopTitle" />
              <span :style="indexTopTitle"> {{ subscriptionCompute.label }} </span>
            </div>
            <!-- 取消联动 -->
            <div
              v-if="!isFullScreen && linkageState"
              class="bi-colorful-btn chart-render__button operate_editbutton"
              :style="indexTopTitle"
              @click="cancelLinkage"
            >
              <span :style="indexTopTitle">{{ $t('display.chartCustom.cancelLinkage') }}</span>
            </div>
            <!-- 编辑 -->
            <div
              v-if="
                ['chart-panel', 'chart-custom'].includes(componentsName(item.type)) &&
                updatePower &&
                chartUpdate &&
                !isFullScreen
              "
              v-feeHide="1"
              class="bi-colorful-btn chart-render__button operate_editbutton"
              :style="indexTopTitle"
              @click="chartEdit"
            >
              <i class="web-icon-edit web-iconfont" :style="indexTopTitle" />
              <span :style="indexTopTitle">{{ $t('operation.edit') }}</span>
            </div>

            <template v-if="!isFullScreen">
              <!-- 发布首页 -->
              <div
                v-if="isDashBoard && publishPower && chartPublish"
                class="bi-colorful-btn chart-render__button operate_button"
                :style="indexTopTitle"
                @click="publishToIndex"
              >
                <i
                  class="web-iconfont"
                  :class="publishStatus ? 'web-icon-chexiao' : 'web-icon-publish'"
                  :style="indexTopTitle"
                ></i>
              </div>
            </template>
            <!-- 全屏 -->
            <div
              class="bi-colorful-btn chart-render__button operate_button"
              :style="indexTopTitle"
              @click="fullScreen"
            >
              <i
                v-if="!isFullScreen"
                class="web-icon-quanping1 web-iconfont"
                :style="indexTopTitle"
              ></i>
              <i v-else class="web-icon-tuichuquanping web-iconfont" :style="indexTopTitle"></i>
            </div>
          </div>
        </div>
      </div>
    </template>
    <subscription-dialog
      :current-subscription-form="currentSubscriptionForm"
      :data-id-obj="currentSubscriptionDataIdObj"
      :data-type="currentSubscriptionDataType"
      :mode="subscriptionMode"
      :show.sync="subscriptionDialogShow"
      @dialog-closed="dialogClosed"
      @open-setting-share="openSettingShare"
      @reset-dialog="resetDialog"
    ></subscription-dialog>

    <subscription-preview-dialog
      :data-id-obj="currentSubscriptionDataIdObj"
      :data-type="currentSubscriptionDataType"
      :show.sync="subscriptionPreviewDialogShow"
      @dialog-closed="dialogClosed"
      @edit-subscription="editSubscription"
      @reset-dialog="resetDialog"
    />
    <share-dialog
      :current-data-obj="currentShareDataObj"
      :data-type="currentShareDataType"
      :dialog-closed="shareDialogClosed"
      :show.sync="shareDialogShow"
      @reset-dialog="resetShareDialog"
    />
  </div>
</template>

<script>
// api

// utils

// constants

// components
import MultiTag from '@/components/select-tree/multi-tag.vue'
import TimeSelector from '@/components/date-time/time-selector.vue'
import SubscriptionDialog from '@/components/chart-management/subscription-dialog.vue'
import SubscriptionPreviewDialog from '@/components/chart-management/subscription-preview-dialog.vue'
import { ShareDialog } from '@/components/chart-management'

import { mapGetters } from 'vuex'

// mixins
import { chartShareMixin, chartSubscriptionMixin } from '@/mixin/bi-chart/index.js'

// others
export default {
  name: 'DashboardOperationBar',
  components: {
    ShareDialog,
    TimeSelector,
    MultiTag,
    SubscriptionDialog,
    SubscriptionPreviewDialog
  },
  mixins: [chartSubscriptionMixin, chartShareMixin],
  props: {
    chartOptionListCopy: {
      type: Array,
      default: () => []
    },
    categoryId: {
      type: [String, Number],
      default: ''
    },
    categoryAlias: {
      type: String,
      default: ''
    },
    categoryName: {
      type: String,
      default: ''
    },
    parentIndex: {
      type: Number,
      default: 0
    },
    selectedIndex: {
      type: String,
      default: ''
    },
    indexTopBackground: {
      type: Object,
      default: () => ({})
    },
    indexTopTitle: {
      type: Object,
      default: () => ({})
    },
    chartType: {
      type: Boolean,
      default: false
    },
    globalList: {
      type: Array,
      default: () => []
    },
    mulitFormat: {
      type: Object
    },
    checkId: {
      type: Array,
      default: () => []
    },
    isFullScreen: {
      type: Boolean,
      default: false
    },
    linkageState: {
      type: Boolean,
      default: false
    },
    isDashBoard: {
      type: Boolean,
      default: false
    },
    publishPower: {
      type: Boolean,
      default: false
    },
    publishInfo: {
      type: String,
      default: ''
    },
    publishStatus: {
      type: Number,
      default: 0
    },
    updatePower: {
      type: Boolean,
      default: false
    },
    isCustomDashboard: {
      type: Boolean,
      default: false
    },
    categoryType: {
      type: Number,
      default: 0
    },
    chartPermissions: {
      type: Object,
      default: () => ({})
    },
    chartListLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectTime: false,
      tempSubscriptionStatus: true
    }
  },
  watch: {
    categoryId: {
      handler() {
        this.tempSubscriptionStatus = true
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['feeType']),

    subscriptionStatus() {
      return (
        this.chartPermissions.hasOwnProperty('chartSubscription') &&
        this.chartPermissions.chartSubscription &&
        this.tempSubscriptionStatus
      )
    },
    ckFlag() {
      return utils.SS.get('biCkFlag') === 'true'
    },

    showSubscription() {
      return (
        this.ckFlag &&
        this.chartPermissions.hasOwnProperty('chartSubscription') &&
        this.chartPermissions.chartSubscription !== null &&
        this.$feeType.checkFeatureEnable('CHART.Subscription')
      )
    },

    showShare() {
      return (
        this.ckFlag &&
        this.chartPermissions.hasOwnProperty('chartShare') &&
        this.chartPermissions.chartShare !== null &&
        this.$feeType.checkFeatureEnable('CHART.Subscription')
      )
    },

    subscriptionCompute() {
      return this.subscriptionStatus
        ? {
            icon: 't-iconfont icon-notification-3-line',
            label: this.$t('chartManagement.subscription'),
            tooltip: ''
          }
        : {
            icon: 't-iconfont icon-notification-3-fill',
            label: this.$t('chartManagement.subscribed'),
            tooltip: this.$t('chartManagement.viewSubscription')
          }
    },

    isShowReset: {
      get() {
        return this.checkId.length || this.selectTime
      },
      set() {}
    },
    chartUpdate() {
      if (!this.$feeType.checkFeatureEnable('CHART.CustomChart')) return false

      return this.chartPermissions?.chartUpdate
    },
    chartPublish() {
      if (!this.$feeType.checkFeatureEnable('CHART.CustomChart')) return false

      return this.chartPermissions?.chartPublish
    }
  },

  methods: {
    openSettingShare() {
      this.shareClick()
    },
    dialogClosed(status = true) {
      if (status) {
        this.$set(this.chartPermissions, 'chartSubscription', true)
        this.$emit('refresh-chart-permissions', {
          ...this.chartPermissions,
          chartSubscription: true
        })
      } else {
        this.$emit('refresh-chart-permissions', {
          ...this.chartPermissions,
          chartSubscription: false
        })
      }
      this.tempSubscriptionStatus = status
    },
    subscriptionClick() {
      this.subscriptionBtnClick(
        this.subscriptionStatus ? 'create' : 'preview',
        {
          type: this.categoryId ? 'id' : 'alias',
          id: this.categoryId,
          alias: this.categoryAlias,
          objectName: this.categoryName
        },
        this.categoryType
      )
    },

    shareClick() {
      this.editShareDialog(
        {
          id: this.categoryId,
          alias: this.categoryAlias,
          objectName: this.categoryName
        },
        this.categoryType
      )
    },

    deleteParam() {
      this.$emit('delete-param')
    },
    componentsName(type) {
      let componentsName = ''
      switch (type) {
        case 4:
        case 8:
          componentsName = 'chart-system' /* 常规系统表 */
          break
        case 5:
          componentsName = 'chart-custom' /* 旧的自定义图表 */
          break
        case 6:
        case 61:
        case 62:
          componentsName = 'chart-compare' /* PK榜 */
          break
        case 7:
        case 71:
          componentsName = 'chart-achievement' /* 目标完成情况 */
          break
        case 9:
          componentsName = 'chart-panel' /* 新仪表盘 */
          break
        default:
          break
      }
      return componentsName
    },
    saveFilter(param, isSave, text) {
      this.selectTime = true
      this.$emit('save-filter', param, isSave, text)
    },
    personSelectVisitOpen() {
      this.$emit('person-select-visit-open')
    },
    tagDelete(tag) {
      this.$emit('tag-delete', tag)
    },
    cancelLinkage() {
      this.$emit('cancel-linkage')
    },
    fullScreen() {
      this.$emit('full-screen')
    },
    publishToIndex() {
      this.$emit('publish-to-index')
    },

    chartEdit() {
      this.$emit('chart-edit')
    },
    resetNomalSelect() {
      this.selectTime = false
      this.$refs['timeSelector'][0].activeText = '全部'
      this.$refs['multiTag'][0].selectedTag = []
      this.$emit('delete-param')
    }
  }
}
</script>

<style lang="scss" scoped>
.operation-bar__wrap {
  position: relative;
  left: 10px;
  //border: 1px solid red;
  width: calc(100% - 20px);
  height: 44px;
  margin-bottom: 10px;
  background: $base-white;
  border-radius: 4px;

  .operation-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    line-height: 44px;

    .right-operation {
      display: flex;
      justify-content: center;

      .chart-render_select {
        display: flex;
        align-items: center;
        height: 44px;
        line-height: 44px;

        .select-item_box {
          display: flex;
          justify-content: center;
          margin-right: 12px;

          .select-form__multi-select {
            display: flex;
            justify-content: center;
            height: 24px;
            min-height: 24px;
            line-height: 24px;
            color: $text-auxiliary;
            cursor: pointer;
            border: 1px solid $neutral-color-3;
            border-radius: 4px;
            :deep(.el-tag) {
              height: 18px !important;
              line-height: 18px;
            }
            :deep(.tagsNum) {
              height: 18px !important;
              line-height: 18px;
            }
            :deep(.placeholder) {
              height: 24px;
              line-height: 24px;
            }

            &:hover {
              color: $brand-color-5;
            }
          }

          .select-item__time {
            height: 24px;
            line-height: 24px;
            color: $text-auxiliary;
            cursor: pointer;
            border: 1px solid $neutral-color-3;
            border-radius: 4px;
            :deep(.detail) {
              color: $text-auxiliary !important;
            }
            :deep(.arrow-icon) {
              vertical-align: text-top;
            }

            &:hover {
              color: $brand-color-5;
            }

            &:active {
              :deep(.detail) {
                color: $brand-base-color-6 !important;
              }
              :deep(.arrow-icon) {
                color: $brand-base-color-6;
                vertical-align: text-top;
              }
              :deep(.el-icon-circle-close) {
                color: $brand-base-color-6 !important;
              }
            }

            &:focus-within {
              border: 1px solid $brand-base-color-6;
            }
          }
        }
      }

      .chart-render__button-box {
        display: flex;
        align-items: center;
        height: 44px;
        line-height: 44px;

        .chart-render__button {
          height: 24px;
          margin-right: 15px;
          font-size: 12px;
          font-weight: 400;
          line-height: 24px;
          color: $neutral-color-8;
          text-align: center;
          border: 1px solid $neutral-color-3;
          border-radius: 4px;

          span {
            color: $text-plain;
          }

          i {
            width: 14px;
            height: 14px;
            // margin-right: 5px;
            font-size: 14px;
            color: $text-plain;
          }
        }
      }
    }
  }

  .operate_button {
    display: inline-block;
    width: 24px;
    cursor: pointer;

    .web-icon-publish {
      width: 100% !important;
      font-size: 13px;
    }
  }

  .operate_editbutton {
    min-width: 51px;
    padding: 0 6px;
    font-size: 12px;
  }

  .chart-render__title {
    height: 44px;
    padding-left: 20px;
    font-size: 16px;
    font-weight: 400;
    line-height: 44px;
    color: $text-main;
  }

  .clear-icon {
    width: 24px;
    height: 24px;
    padding: 0;
    margin-right: 5px;
    // margin-top: px;
    color: $text-auxiliary;

    :hover {
      color: $brand-base-color-6;
    }
  }
}
</style>
