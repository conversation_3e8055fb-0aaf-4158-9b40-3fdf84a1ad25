/**
 * @Description: 员工客户资产 Mock 数据使用示例
 * @Author: AI Assistant
 * @Date: 2024
 */

import {
  ASSET_STATUS_MAP,
  fetchEmployeeAssetById,
  fetchEmployeeAssetsData,
  FOLLOW_UP_STATUS,
  getRedLightAssets,
  getWarningAssets,
  mockAssetsByDepartment,
  mockAssetsByRiskLevel,
  mockAssetsStatistics,
  sortByAssetValue
} from './customer-assets-data.js'

// 使用示例1: 在组件中获取所有员工资产数据
export const exampleGetAllEmployeeAssets = async () => {
  try {
    const response = await fetchEmployeeAssetsData()
    console.log('所有员工资产数据:', response.data)
    return response.data
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 使用示例2: 获取特定员工的资产信息
export const exampleGetEmployeeAssets = async (employeeId = 'EMP001') => {
  try {
    const response = await fetchEmployeeAssetById(employeeId)
    console.log(`员工 ${employeeId} 的资产信息:`, response.data)
    return response.data
  } catch (error) {
    console.error('获取员工资产失败:', error)
  }
}

// 使用示例3: 获取红灯客户列表
export const exampleGetRedLightCustomers = () => {
  const redLightAssets = getRedLightAssets()
  console.log('红灯客户列表:', redLightAssets)

  // 统计红灯客户总数
  const totalRedLightCount = redLightAssets.reduce((sum, emp) => sum + emp.assets.length, 0)
  console.log('红灯客户总数:', totalRedLightCount)

  return redLightAssets
}

// 使用示例4: 获取预警客户列表
export const exampleGetWarningCustomers = () => {
  const warningAssets = getWarningAssets()
  console.log('预警客户列表:', warningAssets)
  return warningAssets
}

// 使用示例5: 按部门查看资产分布
export const exampleGetAssetsByDepartment = () => {
  console.log('按部门分组的资产:', mockAssetsByDepartment)

  // 计算各部门红灯客户占比
  Object.keys(mockAssetsByDepartment).forEach((dept) => {
    const employees = mockAssetsByDepartment[dept]
    const totalCustomers = employees.reduce((sum, emp) => sum + emp.totalCustomers, 0)
    const redLightCustomers = employees.reduce((sum, emp) => sum + emp.redLightCustomers, 0)
    const ratio = totalCustomers > 0 ? ((redLightCustomers / totalCustomers) * 100).toFixed(2) : 0

    console.log(`${dept}: 总客户 ${totalCustomers}, 红灯客户 ${redLightCustomers}, 占比 ${ratio}%`)
  })

  return mockAssetsByDepartment
}

// 使用示例6: 按风险等级查看资产
export const exampleGetAssetsByRiskLevel = () => {
  console.log('按风险等级分组的资产:', mockAssetsByRiskLevel)

  // 统计各风险等级的资产价值
  Object.keys(mockAssetsByRiskLevel).forEach((level) => {
    const assets = mockAssetsByRiskLevel[level]
    const totalValue = assets.reduce((sum, asset) => sum + asset.assetValue, 0)
    const avgValue = assets.length > 0 ? (totalValue / assets.length).toFixed(0) : 0

    console.log(
      `${level}风险等级: 资产数量 ${assets.length}, 总价值 ${totalValue}, 平均价值 ${avgValue}`
    )
  })

  return mockAssetsByRiskLevel
}

// 使用示例7: 获取统计数据
export const exampleGetStatistics = () => {
  console.log('资产统计数据:', mockAssetsStatistics)

  // 格式化显示统计信息
  const formattedStats = {
    员工总数: mockAssetsStatistics.totalEmployees,
    客户总数: mockAssetsStatistics.totalCustomers,
    红灯客户数: mockAssetsStatistics.totalRedLightCustomers,
    红灯客户平均占比: `${mockAssetsStatistics.averageRedLightRatio.toFixed(2)}%`,
    总资产价值: `¥${(mockAssetsStatistics.totalAssetValue / 10000).toFixed(1)}万`,
    总潜在价值: `¥${(mockAssetsStatistics.totalPotentialValue / 10000).toFixed(1)}万`,
    高风险资产数: mockAssetsStatistics.highRiskAssetsCount,
    中风险资产数: mockAssetsStatistics.mediumRiskAssetsCount,
    低风险资产数: mockAssetsStatistics.lowRiskAssetsCount
  }

  console.log('格式化统计信息:', formattedStats)
  return formattedStats
}

// 使用示例8: 按资产价值排序
export const exampleSortByAssetValue = () => {
  const sortedData = sortByAssetValue(true) // true为降序
  console.log('按资产价值降序排列:', sortedData)
  return sortedData
}

// 使用示例9: 获取跟进状态映射信息
export const exampleGetStatusMapping = () => {
  console.log('跟进状态枚举:', FOLLOW_UP_STATUS)
  console.log('状态样式映射:', ASSET_STATUS_MAP)

  // 示例：根据状态获取样式
  const dangerStyle = ASSET_STATUS_MAP[FOLLOW_UP_STATUS.DANGER]
  console.log('红灯状态样式:', dangerStyle)

  return { FOLLOW_UP_STATUS, ASSET_STATUS_MAP }
}

// 使用示例10: 模拟组件中的完整使用流程
export const exampleComponentUsage = async () => {
  console.log('=== 组件使用示例开始 ===')

  // 1. 获取数据
  const data = await exampleGetAllEmployeeAssets()

  // 2. 获取统计信息
  const stats = exampleGetStatistics()

  // 3. 获取红灯客户
  const redLightCustomers = exampleGetRedLightCustomers()

  // 4. 按部门分组
  const departmentData = exampleGetAssetsByDepartment()

  // 5. 获取状态映射
  const statusMapping = exampleGetStatusMapping()

  console.log('=== 组件使用示例结束 ===')

  return {
    data,
    stats,
    redLightCustomers,
    departmentData,
    statusMapping
  }
}

// 导出所有示例函数
export default {
  exampleGetAllEmployeeAssets,
  exampleGetEmployeeAssets,
  exampleGetRedLightCustomers,
  exampleGetWarningCustomers,
  exampleGetAssetsByDepartment,
  exampleGetAssetsByRiskLevel,
  exampleGetStatistics,
  exampleSortByAssetValue,
  exampleGetStatusMapping,
  exampleComponentUsage
}
