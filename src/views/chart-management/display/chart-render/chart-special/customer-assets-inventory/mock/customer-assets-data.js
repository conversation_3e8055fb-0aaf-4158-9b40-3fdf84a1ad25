/**
 * @Description: 员工客户资产 Mock 数据
 * @Author: AI Assistant
 * @Date: 2024
 */

// 跟进状态枚举
export const FOLLOW_UP_STATUS = {
  NORMAL: 'normal', // 正常跟进
  WARNING: 'warning', // 预警（3-7天未跟进）
  DANGER: 'danger' // 红灯（7天以上未跟进）
}

// 客户资产状态映射
export const ASSET_STATUS_MAP = {
  [FOLLOW_UP_STATUS.NORMAL]: {
    label: '正常跟进',
    color: '#67C23A',
    bgColor: '#F0F9FF'
  },
  [FOLLOW_UP_STATUS.WARNING]: {
    label: '预警状态',
    color: '#E6A23C',
    bgColor: '#FDF6EC'
  },
  [FOLLOW_UP_STATUS.DANGER]: {
    label: '红灯客户',
    color: '#F56C6C',
    bgColor: '#FEF0F0'
  }
}

// 基础员工客户资产数据
export const mockEmployeeAssetsData = [
  {
    employeeId: 'EMP001',
    employeeName: '张三',
    department: '销售一部',
    position: '高级销售经理',
    totalCustomers: 45,
    redLightCustomers: 8,
    redLightRatio: 17.78,
    lastUpdateTime: '2024-01-15 14:30:00',
    assets: [
      {
        assetId: 'ASSET001',
        companyName: '北京科技有限公司',
        contactPerson: '李经理',
        contactPhone: '138****8888',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 12,
        lastFollowUpDate: '2024-01-03',
        customerLevel: 'A',
        assetValue: 850000,
        potentialValue: 1200000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET002',
        companyName: '上海贸易集团',
        contactPerson: '王总',
        contactPhone: '139****9999',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 5,
        lastFollowUpDate: '2024-01-10',
        customerLevel: 'B',
        assetValue: 420000,
        potentialValue: 600000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET003',
        companyName: '深圳创新科技',
        contactPerson: '陈主任',
        contactPhone: '137****7777',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 15,
        lastFollowUpDate: '2023-12-31',
        customerLevel: 'A',
        assetValue: 950000,
        potentialValue: 1500000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET001',
        companyName: '北京科技有限公司',
        contactPerson: '李经理',
        contactPhone: '138****8888',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 12,
        lastFollowUpDate: '2024-01-03',
        customerLevel: 'A',
        assetValue: 850000,
        potentialValue: 1200000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET002',
        companyName: '上海贸易集团',
        contactPerson: '王总',
        contactPhone: '139****9999',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 5,
        lastFollowUpDate: '2024-01-10',
        customerLevel: 'B',
        assetValue: 420000,
        potentialValue: 600000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET003',
        companyName: '深圳创新科技',
        contactPerson: '陈主任',
        contactPhone: '137****7777',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 15,
        lastFollowUpDate: '2023-12-31',
        customerLevel: 'A',
        assetValue: 950000,
        potentialValue: 1500000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET001',
        companyName: '北京科技有限公司',
        contactPerson: '李经理',
        contactPhone: '138****8888',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 12,
        lastFollowUpDate: '2024-01-03',
        customerLevel: 'A',
        assetValue: 850000,
        potentialValue: 1200000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET002',
        companyName: '上海贸易集团',
        contactPerson: '王总',
        contactPhone: '139****9999',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 5,
        lastFollowUpDate: '2024-01-10',
        customerLevel: 'B',
        assetValue: 420000,
        potentialValue: 600000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET003',
        companyName: '深圳创新科技',
        contactPerson: '陈主任',
        contactPhone: '137****7777',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 15,
        lastFollowUpDate: '2023-12-31',
        customerLevel: 'A',
        assetValue: 950000,
        potentialValue: 1500000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET001',
        companyName: '北京科技有限公司',
        contactPerson: '李经理',
        contactPhone: '138****8888',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 12,
        lastFollowUpDate: '2024-01-03',
        customerLevel: 'A',
        assetValue: 850000,
        potentialValue: 1200000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET002',
        companyName: '上海贸易集团',
        contactPerson: '王总',
        contactPhone: '139****9999',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 5,
        lastFollowUpDate: '2024-01-10',
        customerLevel: 'B',
        assetValue: 420000,
        potentialValue: 600000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET003',
        companyName: '深圳创新科技',
        contactPerson: '陈主任',
        contactPhone: '137****7777',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 15,
        lastFollowUpDate: '2023-12-31',
        customerLevel: 'A',
        assetValue: 950000,
        potentialValue: 1500000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET001',
        companyName: '北京科技有限公司',
        contactPerson: '李经理',
        contactPhone: '138****8888',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 12,
        lastFollowUpDate: '2024-01-03',
        customerLevel: 'A',
        assetValue: 850000,
        potentialValue: 1200000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET002',
        companyName: '上海贸易集团',
        contactPerson: '王总',
        contactPhone: '139****9999',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 5,
        lastFollowUpDate: '2024-01-10',
        customerLevel: 'B',
        assetValue: 420000,
        potentialValue: 600000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET003',
        companyName: '深圳创新科技',
        contactPerson: '陈主任',
        contactPhone: '137****7777',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 15,
        lastFollowUpDate: '2023-12-31',
        customerLevel: 'A',
        assetValue: 950000,
        potentialValue: 1500000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET001',
        companyName: '北京科技有限公司',
        contactPerson: '李经理',
        contactPhone: '138****8888',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 12,
        lastFollowUpDate: '2024-01-03',
        customerLevel: 'A',
        assetValue: 850000,
        potentialValue: 1200000,
        riskLevel: 'high'
      },
      {
        assetId: 'ASSET002',
        companyName: '上海贸易集团',
        contactPerson: '王总',
        contactPhone: '139****9999',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 5,
        lastFollowUpDate: '2024-01-10',
        customerLevel: 'B',
        assetValue: 420000,
        potentialValue: 600000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET003',
        companyName: '深圳创新科技',
        contactPerson: '陈主任',
        contactPhone: '137****7777',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 15,
        lastFollowUpDate: '2023-12-31',
        customerLevel: 'A',
        assetValue: 950000,
        potentialValue: 1500000,
        riskLevel: 'high'
      }
    ]
  },
  {
    employeeId: 'EMP002',
    employeeName: '李四',
    department: '销售二部',
    position: '销售经理',
    totalCustomers: 32,
    redLightCustomers: 5,
    redLightRatio: 15.63,
    lastUpdateTime: '2024-01-15 16:20:00',
    assets: [
      {
        assetId: 'ASSET004',
        companyName: '广州制造有限公司',
        contactPerson: '刘总监',
        contactPhone: '135****5555',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 9,
        lastFollowUpDate: '2024-01-06',
        customerLevel: 'B',
        assetValue: 320000,
        potentialValue: 480000,
        riskLevel: 'medium'
      },
      {
        assetId: 'ASSET005',
        companyName: '杭州电子商务',
        contactPerson: '周经理',
        contactPhone: '136****6666',
        followUpStatus: FOLLOW_UP_STATUS.WARNING,
        daysNotFollowed: 4,
        lastFollowUpDate: '2024-01-11',
        customerLevel: 'C',
        assetValue: 180000,
        potentialValue: 250000,
        riskLevel: 'low'
      }
    ]
  },
  {
    employeeId: 'EMP003',
    employeeName: '王五',
    department: '销售三部',
    position: '销售专员',
    totalCustomers: 28,
    redLightCustomers: 3,
    redLightRatio: 10.71,
    lastUpdateTime: '2024-01-15 11:45:00',
    assets: [
      {
        assetId: 'ASSET006',
        companyName: '成都软件开发',
        contactPerson: '赵工程师',
        contactPhone: '134****4444',
        followUpStatus: FOLLOW_UP_STATUS.DANGER,
        daysNotFollowed: 8,
        lastFollowUpDate: '2024-01-07',
        customerLevel: 'A',
        assetValue: 680000,
        potentialValue: 900000,
        riskLevel: 'high'
      }
    ]
  }
]

// 按部门分组的数据
export const mockAssetsByDepartment = {
  销售一部: mockEmployeeAssetsData.filter((emp) => emp.department === '销售一部'),
  销售二部: mockEmployeeAssetsData.filter((emp) => emp.department === '销售二部'),
  销售三部: mockEmployeeAssetsData.filter((emp) => emp.department === '销售三部')
}

// 按风险等级分组的资产
export const mockAssetsByRiskLevel = {
  high: [],
  medium: [],
  low: []
}

// 填充风险等级分组数据
mockEmployeeAssetsData.forEach((employee) => {
  employee.assets.forEach((asset) => {
    mockAssetsByRiskLevel[asset.riskLevel].push({
      ...asset,
      employeeName: employee.employeeName,
      employeeId: employee.employeeId
    })
  })
})

// 统计数据
export const mockAssetsStatistics = {
  totalEmployees: mockEmployeeAssetsData.length,
  totalCustomers: mockEmployeeAssetsData.reduce((sum, emp) => sum + emp.totalCustomers, 0),
  totalRedLightCustomers: mockEmployeeAssetsData.reduce(
    (sum, emp) => sum + emp.redLightCustomers,
    0
  ),
  averageRedLightRatio:
    mockEmployeeAssetsData.reduce((sum, emp) => sum + emp.redLightRatio, 0) /
    mockEmployeeAssetsData.length,
  totalAssetValue: mockEmployeeAssetsData.reduce(
    (sum, emp) => sum + emp.assets.reduce((assetSum, asset) => assetSum + asset.assetValue, 0),
    0
  ),
  totalPotentialValue: mockEmployeeAssetsData.reduce(
    (sum, emp) => sum + emp.assets.reduce((assetSum, asset) => assetSum + asset.potentialValue, 0),
    0
  ),
  highRiskAssetsCount: mockAssetsByRiskLevel.high.length,
  mediumRiskAssetsCount: mockAssetsByRiskLevel.medium.length,
  lowRiskAssetsCount: mockAssetsByRiskLevel.low.length
}

// 模拟 API 响应格式
export const mockApiResponse = {
  code: 200,
  message: 'success',
  data: {
    employees: mockEmployeeAssetsData.concat(mockEmployeeAssetsData),
    statistics: mockAssetsStatistics,
    timestamp: Date.now()
  }
}

// 工具函数：根据员工ID获取数据
export const getEmployeeById = (employeeId) => {
  return mockEmployeeAssetsData.find((emp) => emp.employeeId === employeeId)
}

// 工具函数：根据跟进状态筛选资产
export const filterAssetsByStatus = (status) => {
  const result = []
  mockEmployeeAssetsData.forEach((employee) => {
    const filteredAssets = employee.assets.filter((asset) => asset.followUpStatus === status)
    if (filteredAssets.length > 0) {
      result.push({
        ...employee,
        assets: filteredAssets
      })
    }
  })
  return result
}

// 工具函数：获取红灯客户（7天以上未跟进）
export const getRedLightAssets = () => {
  return filterAssetsByStatus(FOLLOW_UP_STATUS.DANGER)
}

// 工具函数：获取预警客户（3-7天未跟进）
export const getWarningAssets = () => {
  return filterAssetsByStatus(FOLLOW_UP_STATUS.WARNING)
}

// 工具函数：按资产价值排序
export const sortByAssetValue = (desc = true) => {
  return mockEmployeeAssetsData.map((employee) => ({
    ...employee,
    assets: employee.assets.sort((a, b) =>
      desc ? b.assetValue - a.assetValue : a.assetValue - b.assetValue
    )
  }))
}

// 模拟异步获取数据的函数
export const fetchEmployeeAssetsData = (delay = 800) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockApiResponse)
    }, delay)
  })
}

// 模拟获取单个员工资产数据
export const fetchEmployeeAssetById = (employeeId, delay = 300) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const employee = getEmployeeById(employeeId)
      if (employee) {
        resolve({
          code: 200,
          message: 'success',
          data: employee
        })
      } else {
        reject({
          code: 404,
          message: '员工不存在'
        })
      }
    }, delay)
  })
}
