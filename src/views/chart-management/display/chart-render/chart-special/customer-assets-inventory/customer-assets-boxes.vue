<script setup>
import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { getSpecialPerformance } from '@/api/statistics'
import store from '@/store/index'


defineOptions({
  name: 'CustomerAssetsBoxes'
})

const props = defineProps({
  filterForm: {
    type: Object,
    default: () => ({})
  },
  chartExplain: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const employeesData = ref([])
const loading = ref(false)
const expandedEmployees = ref([]) // 存储展开的员工ID数组
const stickyEmployee = ref(null) // 当前吸顶的员工数据
const scrollContainer = ref(null) // 滚动容器引用
const assetsContainer = ref(null) // 资产容器引用
const itemsPerRow = ref(6) // 每行可显示的资产数量，默认6个
let throttleTimer = null // 节流定时器
let resizeObserver = null // 尺寸监听器

// 滚动加载配置项
const pageOption = reactive({
  pageTotal: 0,
  leftPageNums: [],
  rightPageNums: [],
  hasLeft: false,
  hasRight: true,
  currentPageNum: 1, // 当前页的页码
  pageSize: 10, // 每页的大小，改为10条
  pageSizes: [10, 20, 30, 40, 50, 100],
  rowsCount: 0 // 总的条数
})

// 滚动加载相关状态
const isLoadingMore = ref(false) // 是否正在加载更多
const hasMoreData = ref(true) // 是否还有更多数据
const scrollLoadContainer = ref(null) // 滚动加载容器引用

// 方法：切换员工资产展开/收起状态
const toggleEmployeeAssets = (employeeId) => {
  const index = expandedEmployees.value.indexOf(employeeId)
  if (index > -1) {
    // 如果已展开，则收起
    expandedEmployees.value.splice(index, 1)
  } else {
    // 如果未展开，则展开
    expandedEmployees.value.push(employeeId)
  }
}

// 方法：计算每行可显示的资产数量
const calculateItemsPerRow = () => {
  if (!assetsContainer.value) return

  const containerWidth = assetsContainer.value.offsetWidth
  const itemWidth = 168 // asset-box-item 宽度
  const gap = 8 // gap 间距
  const totalItemWidth = itemWidth + gap

  // 计算可以放置多少个完整的item（至少1个）
  const calculatedItems = Math.max(1, Math.floor(containerWidth / totalItemWidth))
  itemsPerRow.value = calculatedItems
}

// 方法：处理滚动事件，计算吸顶元素
const handleScroll = () => {
  if (!scrollContainer.value) return

  const containerRect = scrollContainer.value.getBoundingClientRect()
  const containerTop = containerRect.top

  // 找到当前应该吸顶的员工
  let targetEmployee = null
  const employeeBoxes = scrollContainer.value.querySelectorAll('.customer-assets-box')

  // 检查第一个员工是否完全可见
  const firstBox = employeeBoxes[0]
  if (firstBox) {
    const firstRect = firstBox.getBoundingClientRect()
    const firstStaffInfoBox = firstBox.querySelector('.staff-info-box')

    // 如果第一个员工的信息框完全在容器内（顶部可见），则不显示吸顶
    if (firstStaffInfoBox && firstRect.top >= containerTop - 5) {
      if (stickyEmployee.value) {
        stickyEmployee.value = null
      }
      return
    }
  }

  for (let i = 0; i < employeeBoxes.length; i++) {
    const box = employeeBoxes[i]
    const rect = box.getBoundingClientRect()
    const staffInfoBox = box.querySelector('.staff-info-box')

    if (staffInfoBox && rect.top <= containerTop && rect.bottom > containerTop + 40) {
      const employeeId = staffInfoBox.getAttribute('data-employee-id')
      // 找到对应的员工数据
      targetEmployee = employeesData.value.find((emp) => emp.userId.toString() === employeeId)

      break
    }
  }

  // 只有当目标员工发生变化时才更新
  const currentEmployeeId = stickyEmployee.value?.userId
  const targetEmployeeId = targetEmployee?.userId

  if (currentEmployeeId !== targetEmployeeId) {
    stickyEmployee.value = targetEmployee

    // 设置吸顶元素的位置和宽度
    nextTick(() => {
      if (targetEmployee) {
        const stickyElement = document.querySelector('.temp-staff-info-box')
        if (stickyElement) {
          stickyElement.style.top = containerRect.top + 'px'
          stickyElement.style.left = containerRect.left + 'px'
          stickyElement.style.width = containerRect.width + 'px'
        }
      }
    })
  }
}


function goDetail(data) {
  console.log('data,', data)
  if (data.id || data.dataId) {
    const dataId = Number(data.id || data.dataId) // 数据id
    // 兼容之前 Number(0 || undefined)的情况
    const businessType = Number(data.businessType || data.businesstype) || 0 // 业务类型
    const saasMark = Number(data.saasMark) || 1
    store.commit('SET_DATA_ID_LIST', [])
    store.commit('SET_DETAIL_DIALOG_SHOW',true)
    store.commit('SET_DETAIL_QUERY', {
      dataId,
      saasMark,
      businessType
    })
  }
}

// 方法：节流处理滚动事件
const throttledHandleScroll = () => {
  if (throttleTimer) return

  throttleTimer = setTimeout(() => {
    handleScroll()
    throttleTimer = null
  }, 16) // 约60fps
}

/**
 * 生成API请求参数
 * @param {boolean} isExport - 是否用于导出
 * @returns {Object} 请求参数对象
 */
const genCustomerAssetsParams = (isExport = false) => {
  const { filterForm, chartExplain } = props
  const params = {
    page: pageOption.currentPageNum,
    pageSize: pageOption.pageSize,
    companyStructType: 2,
    checkSubDep: filterForm.checkSubDep,
    timeFilter: filterForm.timeRange,
    checkedId: filterForm.checkedId,
    checkedDepId: filterForm.checkedDepId || '',
    statisticsType: chartExplain.statisticsType,
    argStrIn: [filterForm.argStrIn],
    argTwoId: filterForm.argTwoId,
    refId: filterForm.refId,
    chartIdIn: [
      {
        id: chartExplain.id,
        statisticsType: chartExplain.statisticsType,
        systemCode: chartExplain.systemCode
      }
    ]
  }

  // 选择部门目标时，部门id必传，默认组织架构的第一个部门id
  if (params.companyStructType === 2 && params.checkedDepId === '') {
    params.checkedDepId = 1
  }

  return params
}

/**
 * @description 设置分页组件
 * @param pageHelper
 */
const setPageOption = (pageHelper) => {
  Object.keys(pageOption).forEach((key) => {
    pageOption[key] = pageHelper[key]
  })
}

/**
 * @description 获取客户资产盘点数据
 * @param {boolean} isLoadMore - 是否为加载更多
 * @return {Promise<void>}
 */
const loadCustomerAssets = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true
  } else {
    loading.value = true
    // 重置数据
    pageOption.currentPageNum = 1
    employeesData.value = []
    hasMoreData.value = true
    // 清空吸顶员工信息
    stickyEmployee.value = null
    // 清空展开状态
    expandedEmployees.value = []
  }

  try {
    const params = genCustomerAssetsParams()
    const { result, code } = await getSpecialPerformance(params)
    if (code === 1) {
      const newData = result.chartList[0].customerAsset.userList || []

      if (isLoadMore) {
        // 追加数据
        employeesData.value.push(...newData)
      } else {
        // 替换数据
        employeesData.value = newData
      }

      setPageOption(result.chartList[0].customerAsset.pageHelper)

      // 检查是否还有更多数据
      const totalPages = Math.ceil(pageOption.rowsCount / pageOption.pageSize)
      hasMoreData.value = pageOption.currentPageNum < totalPages

      // 数据加载完成后，检查是否需要自动加载更多
      nextTick(() => {
        setTimeout(() => {
          checkAndAutoLoad()
        }, 100) // 延迟一点确保DOM更新完成
      })
    }
  } catch (error) {
    console.error('获取员工资产数据失败:', error)
  } finally {
    if (isLoadMore) {
      isLoadingMore.value = false
    } else {
      loading.value = false
    }
  }
}

/**
 * @description 加载更多数据
 */
const loadMoreData = async () => {
  if (isLoadingMore.value || !hasMoreData.value) return

  pageOption.currentPageNum += 1
  await loadCustomerAssets(true)
}

/**
 * @description 检查是否需要自动加载更多数据（当内容不足以产生滚动条时）
 */
function checkAndAutoLoad() {
  if (!scrollLoadContainer.value || isLoadingMore.value || !hasMoreData.value) return

  const { scrollHeight, clientHeight } = scrollLoadContainer.value
  console.log('检查是否需要自动加载:', {
    scrollHeight,
    clientHeight,
    needAutoLoad: scrollHeight <= clientHeight
  })

  // 如果内容高度小于等于容器高度，说明没有滚动条，需要自动加载
  if (scrollHeight <= clientHeight) {
    console.log('内容不足，自动加载更多数据')
    loadMoreData()
  }
}

// 方法：处理滚动加载
const handleScrollLoad = () => {
  console.log('handleScrollLoad 被调用')
  if (!scrollLoadContainer.value || isLoadingMore.value || !hasMoreData.value) {
    console.log('滚动加载条件不满足:', {
      hasContainer: !!scrollLoadContainer.value,
      isLoadingMore: isLoadingMore.value,
      hasMoreData: hasMoreData.value
    })
    return
  }

  const { scrollTop, scrollHeight, clientHeight } = scrollLoadContainer.value
  console.log('滚动位置:', {
    scrollTop,
    scrollHeight,
    clientHeight,
    trigger: scrollTop + clientHeight >= scrollHeight - 100
  })

  // 当滚动到距离底部100px时触发加载
  if (scrollTop + clientHeight >= scrollHeight - 100) {
    console.log('触发加载更多')
    loadMoreData()
  }
}

// 滚动加载节流定时器
let scrollLoadTimer = null

// 方法：节流处理滚动加载
const throttledScrollLoad = () => {
  console.log('throttledScrollLoad 被调用')
  if (scrollLoadTimer) return

  scrollLoadTimer = setTimeout(() => {
    handleScrollLoad()
    scrollLoadTimer = null
  }, 200) // 200ms节流
}

// 方法：初始化滚动监听
const initScrollListener = () => {
  // 查找滚动容器（父组件中的 .customer-assets-boxes__wrapper）
  nextTick(() => {
    // 尝试多个可能的滚动容器
    let element = document.querySelector('.customer-assets-boxes__wrapper')

    if (!element) {
      // 如果找不到包装器，尝试查找父级滚动容器
      element = document.querySelector('.chart-container')
    }

    if (!element) {
      // 最后尝试使用 window 作为滚动容器
      element = window
      scrollLoadContainer.value = document.documentElement
    }

    console.log('查找滚动容器:', element)

    if (element) {
      scrollContainer.value = element === window ? document.documentElement : element
      if (!scrollLoadContainer.value) {
        scrollLoadContainer.value = element === window ? document.documentElement : element
      }

      console.log('添加滚动监听器')
      // 添加吸顶滚动监听
      element.addEventListener('scroll', throttledHandleScroll, { passive: true })
      // 添加滚动加载监听
      element.addEventListener('scroll', throttledScrollLoad, { passive: true })

      // 测试滚动事件是否正常
      element.addEventListener(
        'scroll',
        () => {
          console.log('原始滚动事件触发')
        },
        { passive: true }
      )

      // 初始化时也执行一次
      if (element !== window) {
        handleScroll()
      }

      // 初始化后检查是否需要自动加载
      setTimeout(() => {
        checkAndAutoLoad()
      }, 500) // 延迟一点确保所有初始化完成
    } else {
      console.error('未找到任何滚动容器')
    }
  })
}

// 方法：初始化尺寸监听
const initResizeObserver = () => {
  nextTick(() => {
    const element = document.querySelector('.customer-assets-boxes')
    if (element && window.ResizeObserver) {
      assetsContainer.value = element

      resizeObserver = new ResizeObserver(() => {
        calculateItemsPerRow()
      })

      resizeObserver.observe(element)
      // 初始化时计算一次
      calculateItemsPerRow()
    }
  })
}

// 方法：清理滚动监听
const cleanupScrollListener = () => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', throttledHandleScroll)
    scrollContainer.value.removeEventListener('scroll', throttledScrollLoad)
  }
  if (throttleTimer) {
    clearTimeout(throttleTimer)
    throttleTimer = null
  }
}

// 方法：清理尺寸监听
const cleanupResizeObserver = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}

// 监听 filterForm对象如果变更要重新加载数据
watch(
  () => props.filterForm,
  (newVal, oldVal) => {
    expandedEmployees.value = []
    loadCustomerAssets()
  },
  { deep: true }
)

// 生命周期：组件挂载时获取数据和初始化滚动监听
onMounted(() => {
  loadCustomerAssets()
  initScrollListener()
  initResizeObserver()
})

// 监听筛选条件变化
watch(
  () => props.filterForm,
  (newFilterForm, oldFilterForm) => {
    // 深度比较筛选条件是否真的发生了变化
    if (JSON.stringify(newFilterForm) !== JSON.stringify(oldFilterForm)) {
      console.log('筛选条件发生变化，重新加载数据')
      loadCustomerAssets()
    }
  },
  { deep: true }
)

// 生命周期：组件卸载时清理监听
onUnmounted(() => {
  cleanupScrollListener()
  cleanupResizeObserver()
})
</script>

<template>
  <div class="customer-assets-boxes">
    <!-- 吸顶员工信息组件 -->
    <div
      v-if="stickyEmployee"
      class="temp-staff-info-box"
      @click="toggleEmployeeAssets(stickyEmployee.userId)"
    >
      <div class="staff-info-left">
        <img class="staff-info-box__avtar" src="@/assets/user-default.png" />
        <span class="staff-info-box__name">{{ stickyEmployee.userName }}</span>
        <span class="staff-info-box__department">{{ stickyEmployee.userId }}</span>
        <span class="staff-info-box__red-count">
          红灯客户：{{ stickyEmployee.redCustomerCount }}
        </span>
      </div>
      <div class="staff-info-right">
        <span
          v-if="stickyEmployee.customerList && stickyEmployee.customerList.length > itemsPerRow"
          class="expand-icon"
          :class="{ expanded: expandedEmployees.includes(stickyEmployee.userId) }"
        >
          <i class="icon-arrow-down-s-line t-iconfont" />
        </span>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <div v-else-if="employeesData.length === 0" class="empty-container">
      <div class="empty-text">暂无红灯客户数据</div>
    </div>

    <template v-else>
      <!-- 遍历有红灯客户的员工 -->
      <div
        v-for="(employee, idx) in employeesData"
        :key="employee.userId"
        class="customer-assets-box"
      >
        <!-- 员工信息 点击展开 assets-box -->
        <div
          class="staff-info-box"
          :data-employee-id="employee.userId"
          @click="toggleEmployeeAssets(employee.userId)"
        >
          <div class="staff-info-left">
            <img class="staff-info-box__avtar" src="@/assets/user-default.png" />
            <span class="staff-info-box__name">{{ employee.userName }}</span>
            <span class="staff-info-box__department">{{ employee.userId }}</span>
            <span class="staff-info-box__red-count">
              红灯客户：{{ employee.redCustomerCount }}
            </span>
          </div>
          <div class="staff-info-right">
            <span
              v-if="employee.customerList && employee.customerList.length > itemsPerRow"
              class="expand-icon"
              :class="{ expanded: expandedEmployees.includes(employee.userId) }"
            >
              <i class="icon-arrow-down-s-line t-iconfont" />
            </span>
          </div>
        </div>

        <!-- 可折叠的资产列表， 只有员工所拥有的客户数量大于一行才可以折叠 -->
        <div v-if="employee?.customerList?.length" class="assets-boxes">
          <div
            v-for="(asset, index) in employee.customerList"
            v-show="
              expandedEmployees.includes(employee.userId) ||
              employee.customerList.length <= itemsPerRow ||
              index < itemsPerRow
            "
            :key="asset.id"
            class="asset-box-item"
            :class="asset.isRed ? 'red-asset-box-item' : ''"
            @click="goDetail(asset)"
          >
            <div class="asset-info">
              <span v-tooltip="asset.name" class="asset-name">
                {{ asset.name }}
              </span>
            </div>
            <div class="asset-status-info">
              <span class="asset-status">
                {{ asset.lastConnectTimeStr }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多提示 -->
      <div v-if="isLoadingMore" class="loading-more">
        <div class="loading-more-text">加载中...</div>
      </div>

      <!-- 手动加载更多按钮 -->
      <div v-else-if="hasMoreData && employeesData.length > 0" class="load-more-btn-container">
        <button class="load-more-btn" @click="loadMoreData">加载更多</button>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-else-if="!hasMoreData && employeesData.length > 0" class="no-more-data">
        <div class="no-more-data-text">没有更多数据了</div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.customer-assets-boxes {
  background: #fff;
  border-radius: 8px;
  //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  // 吸顶员工信息组件样式
  .temp-staff-info-box {
    position: fixed;
    z-index: 1000;
    box-sizing: border-box;
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 8px 16px 8px 8px;
    cursor: pointer;
    background: $neutral-color-1;
    border-radius: 0 0 4px 4px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f0f0;
    }

    .staff-info-left {
      display: flex;
      flex-direction: row;
      gap: 4px;
      align-items: center;
      height: 24px;
      line-height: 24px;
      .staff-info-box__avtar {
        width: 24px;
        height: 24px;
        border: 1px solid $neutral-color-2;
        border-radius: 4px;
      }

      .staff-info-box__name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .staff-info-box__department {
        font-size: 12px;
        color: #666;
      }

      .staff-info-box__red-count {
        box-sizing: border-box;
        height: 20px;
        padding: 2px 8px;
        font-size: 12px;
        font-weight: normal;
        line-height: 16px;
        color: $error-base-color-6;
        /* 错误色/Error-1 浅色白底悬浮 */
        background: #ffece8;
        border-radius: 4px;
      }
    }

    .staff-info-right {
      display: flex;
      gap: 12px;
      align-items: center;

      .expand-icon {
        font-size: 12px;
        color: #999;
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .loading-container,
  .empty-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 120px;
    font-size: 14px;
    color: #999;
  }

  // 加载更多样式
  .loading-more,
  .no-more-data,
  .load-more-btn-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-size: 14px;
    color: #999;

    .loading-more-text,
    .no-more-data-text {
      position: relative;

      &::before {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 8px;
        vertical-align: middle;
        content: '';
        border: 2px solid #e0e0e0;
        border-top-color: #999;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    .no-more-data-text::before {
      display: none;
    }
  }

  .load-more-btn {
    padding: 8px 24px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      color: #1890ff;
      background: #e6f7ff;
      border-color: #91d5ff;
    }

    &:active {
      background: #bae7ff;
      border-color: #40a9ff;
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .customer-assets-box {
    margin-bottom: 16px;
    overflow: hidden;
    border-radius: 6px;

    &:last-child {
      margin-bottom: 0;
    }

    .staff-info-box {
      box-sizing: border-box;
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      padding: 8px 16px 8px 8px;
      cursor: pointer;
      background: $neutral-color-1;
      border-radius: 4px;
      transition: background-color 0.3s ease;
      &:hover {
        background: #f0f0f0;
      }

      // 吸顶样式
      &--sticky {
        position: fixed !important;
        z-index: 1000;
        margin: 0;
        background: $neutral-color-1;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        &:hover {
          background: #f0f0f0;
        }
      }

      .staff-info-left {
        display: flex;
        flex-direction: row;
        gap: 4px;
        align-items: center;
        height: 24px;
        line-height: 24px;
        .staff-info-box__avtar {
          width: 24px;
          height: 24px;
          border: 1px solid $neutral-color-2;
          border-radius: 4px;
        }

        .staff-info-box__name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .staff-info-box__department {
          font-size: 12px;
          color: #666;
        }

        .staff-info-box__red-count {
          box-sizing: border-box;
          height: 20px;
          padding: 2px 8px;
          font-size: 12px;
          font-weight: normal;
          line-height: 16px;
          color: $error-base-color-6;
          /* 错误色/Error-1 浅色白底悬浮 */
          background: #ffece8;
          border-radius: 4px;
        }
      }

      .staff-info-right {
        display: flex;
        gap: 12px;
        align-items: center;

        .expand-icon {
          font-size: 12px;
          color: #999;
          transition: transform 0.3s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }

    .assets-boxes {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      min-height: 60px;
      max-height: 300px;
      padding-top: 8px;
      background: #fff;
      .asset-box-item {
        z-index: 0;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 2px;
        align-items: flex-start;
        justify-content: center;
        width: 168px;
        height: 52px;
        padding: 6px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: all .3s ease;
        //box-shadow: 0px 2px 30px 2px rgba(0, 0, 0, 0.05);
        background: #0FBB54;
        &:last-child {
          border-bottom: none;
        }
        &:hover {
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }


        .asset-info {
          display: flex;
          flex: 1;
          flex-direction: column;
          height: 20px;
          line-height: 20px;

          .asset-name {
            max-width: 144px;
            overflow: hidden;
            font-size: 14px;
            font-weight: 500;
            color: $base-white;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .asset-contact {
            font-size: 12px;
            color: $base-white;
          }
        }

        .asset-status-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          height: 18px;
          line-height: 18px;
          color: $base-white;
        }
      }

      .red-asset-box-item {
        background: #fd5555;
      }
    }
  }
}

// 折叠动画
.slide-down-enter-active,
.slide-down-leave-active {
  max-height: 500px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>
