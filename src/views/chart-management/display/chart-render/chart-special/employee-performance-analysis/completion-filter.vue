<!--
 * @Description: 目标完成情况的查询条件
 -->

<template>
  <div class="completion-filter">
    <div class="filter__item">
      <span class="filter__label">{{ $t('form.chooseTime') }}</span>
      <time-selector
        class="filter__time"
        :filter-param="filterForm.timeRange"
        :is-fiscal-year="true"
        :set-default="false"
        :special-param="specialParam"
        @saveFilter="saveTime"
      >
      </time-selector>
    </div>

    <div class="filter__item">
      <span class="filter__label">{{ $t('form.chooseRange') }}</span>
      <multi-tag
        class="filter__multi-select"
        :prop-format="multiFormat"
        :selected-tag="chooseName"
        @click.native="personSelectVisitOpen"
        @tagDelete="tagDelete"
      ></multi-tag>
    </div>

    <div class="filter__item">
      <span class="filter__label">{{ $t('display.chartAchievement.colorSet') + '：' }}</span>
      <el-popover
        v-model="colorSetBox"
        placement="top"
        :popper-class="'completion-popover'"
        @show="getFinishColor"
      >
        <p class="color-setting">
          0%
          <span class="color-setting__preview color-setting__preview--red"></span>
          <el-input
            v-model.number="colorRange.performanceFinishDown"
            @keyup.native="exchangeNumber('performanceFinishDown')"
          >
          </el-input
          >%
          <span class="color-setting__preview color-setting__preview--orange"></span>
          <el-input
            v-model.number="colorRange.performanceFinishUp"
            @keyup.native="exchangeNumber('performanceFinishUp')"
          >
          </el-input
          >%
          <span class="color-setting__preview color-setting__preview--green"></span>
          100%
        </p>
        <div class="setting-button">
          <el-button type="cancle" @click="colorSetBox = false">{{
            $t('operation.cancel')
          }}</el-button>
          <el-button type="primary" @click="setFinishColor">{{ $t('operation.save') }}</el-button>
        </div>
        <!-- 回显 -->
        <span slot="reference" class="color-completion">
          <span class="color-completion__box"></span>
          <span class="color-completion__text"
            >{{ filterForm.colorPreview.performanceFinishDown }}%~{{
              filterForm.colorPreview.performanceFinishUp
            }}%</span
          >
          <i class="web-icon-shezhi2 web-iconfont"></i>
        </span>
      </el-popover>
    </div>
    <div v-if="companyStructType === 2" class="filter__item last__item">
      <span class="check-dep-sub">
        <el-checkbox v-model="filterForm.checkSubDep" label="是否包含下属部门"></el-checkbox>
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, reactive, ref, watch } from 'vue'
import TimeSelector from '@/components/date-time/time-selector.vue'
import MultiTag from '@/components/select-tree/multi-tag.vue'
import { getPerformanceFinish, setPerformanceFinish } from '@/api/companyConfig'
import { Message } from 'element-ui'
import i18n from '@/lang'
import utils from '@/utils'
import PersonSelect from '@/components/person-select' // 人员和部门多选

// 定义组件名称
defineOptions({
  name: 'CompletionFilter'
})
// 定义props
const props = defineProps({
  companyStructType: {
    type: Number,
    default: 1
  },
  specialParam: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(['sendFilter'])

// 将data转换为reactive和ref
const filterForm = reactive({
  // 时间选择
  timeRange: {
    timeType: 3 // 默认本月
  },
  object: '',
  checkedId: [],
  checkedUserId: '', // 筛选员工id
  checkedDepId: '', // 筛选部门id
  colorPreview: {}, // 颜色显示在筛选框中
  checkSubDep: true
})

const chooseName = ref([]) // 选中员工或者部门的回显
const colorRange = reactive({}) // 颜色设置区间
const colorSetBox = ref(false)
// 多选回显值
const multiFormat = reactive({
  label: 'name'
})

/**
 * 根据公司结构类型决定显示哪些选项卡
 * 当公司结构类型为1时，显示部门和用户选项卡
 * 当公司结构类型为2时，只显示部门选项卡
 * @returns {Array} 要显示的选项卡名称数组
 */
const showTabsName = computed(() => {
  return props.companyStructType === 1 ? ['dept', 'user'] : ['dept']
})

/**
 * 监听筛选表单的变化
 * 当筛选条件发生变化时，将最新的筛选条件发送给父组件
 * deep: true - 深度监听对象内部属性变化
 * immediate: true - 组件创建时立即触发一次
 */
watch(
  filterForm,
  (val) => {
    emit('sendFilter', val)
  },
  {
    deep: true,
    immediate: true
  }
)

/**
 * 处理完成率颜色数字输入
 * 验证输入值是否在0-100之间，并确保是整数
 * @param {string} attr - 要修改的属性名（performanceFinishDown或performanceFinishUp）
 */
function exchangeNumber(attr) {
  // 输入0-100的整数
  const value = Number(colorRange[attr])
  if (value > 0 && value < 100) {
    // 确保是整数
    colorRange[attr] = Math.round(value)
  } else {
    colorRange[attr] = null
    Message.error(i18n.t('rule.between', { attr: i18n.t('nouns.number'), num1: 0, num2: 100 }))
  }
}

/**
 * 获取完成率颜色的上限和下限
 * 从API获取颜色设置并更新本地状态
 * @param {string} isSend - 如果为'sendColor'，则将颜色信息传递到筛选对象
 */
function getFinishColor(isSend) {
  getPerformanceFinish({})
    .then((res) => {
      if (isSend) {
        filterForm.colorPreview = res.result // 初始化，第一次加载的时候获取颜色信息传递到筛选对象，从而传递给父组件
      }
      Object.assign(colorRange, res.result)
    })
    .catch((error) => {
      console.error('Failed to get performance finish colors:', error)
    })
}

/**
 * 设置完成率颜色的上限和下限
 * 将用户设置的颜色范围保存到后端并更新本地状态
 */
function setFinishColor() {
  const params = {
    performanceFinishDown: colorRange.performanceFinishDown,
    performanceFinishUp: colorRange.performanceFinishUp
  }
  setPerformanceFinish(params)
    .then(() => {
      filterForm.colorPreview = { ...colorRange } // 设置完成立即传递到父组件
      colorSetBox.value = false
    })
    .catch((error) => {
      console.error('Failed to set performance finish colors:', error)
    })
}

/**
 * 保存用户选择的时间范围
 * @param {Object} params - 时间选择器返回的时间参数
 */
function saveTime(params) {
  filterForm.timeRange = { ...params }
}

/**
 * 从选中的组织架构标签中删除一个标签
 * @param {Object} tag - 要删除的标签对象
 */
function tagDelete(tag) {
  const deleteTagIndex = chooseName.value.findIndex((item) => item.id === tag.id)
  if (deleteTagIndex !== -1) {
    chooseName.value.splice(deleteTagIndex, 1)
  }
  // 重置选择
  filterForm.checkedDepId = ''
  filterForm.checkedUserId = ''
}

/**
 * 处理组织架构选择弹窗的提交事件
 * 更新选中的部门或用户信息
 * @param {Array} val - 选中的部门或用户数组
 */
function personSubmit(val) {
  chooseName.value = val
  filterForm.checkedId = val

  // 重置选择
  filterForm.checkedDepId = ''
  filterForm.checkedUserId = ''

  // 只处理最后一个选择项
  if (val.length > 0) {
    const lastItem = val[val.length - 1]
    if (lastItem.property === 'dept') {
      filterForm.checkedDepId = lastItem.id
    } else if (lastItem.property === 'user') {
      filterForm.checkedUserId = lastItem.id
    }
  }
}

/**
 * 打开组织架构选择弹窗
 * 如果仪表盘处于全屏状态，则不打开弹窗
 */

function personSelectVisitOpen() {
  // 仪表盘全屏时禁止弹出
  if (+utils.SS.get('chart-panel-fullscreen') === 1) {
    return
  }
  const defaultVal = filterForm?.checkedId || []
  PersonSelect({
    showTabsName: showTabsName,
    defaultVal: defaultVal,
    tabMultiple: true,
    otherParams: {
      // 是否控制组织架构权限范围
      deptVisibleRule: true,
      parentStrictly: true
    }
  }).then((res) => {
    if (res.data) {
      personSubmit(res.data)
    }
  })
}

// 组件创建时的生命周期钩子
// 初始化时获取完成率颜色设置并传递给父组件
onMounted(() => {
  getFinishColor('sendColor')
})
</script>

<style lang="scss" scoped>
.completion-filter {
  .last__item {
    float: right;
  }
  .filter {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid $line-table;
    &__item {
      display: inline-flex;
      padding-right: 18px;
      padding-left: 12px;
      justify-content: center;
      align-items: baseline;
    }
    &__label {
      font-size: 14px;
    }
    &__time {
      height: 32px;
      line-height: 32px;
      border: 1px solid $line-filter;
      border-radius: 3px;
    }
    &__multi-select {
      display: inline-block;
      width: 240px;
      height: 32px;
      line-height: 32px;
    }
  }

  // 颜色设置回显
  .color-completion {
    display: inline-flex;
    align-items: center;
    height: 32px;
    padding: 0px 14px;
    cursor: pointer;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
    &__box {
      display: block;
      width: 15px;
      height: 15px;
      background: $text-plain;
    }
    &__text {
      display: block;
      padding: 0 10px;
      line-height: 15px;
      border-right: 1px solid $neutral-color-3;
    }
    .web-icon-shezhi2 {
      margin-left: 5px;
      color: $text-plain;
    }
  }
}
</style>

<style lang="scss">
// 完成率颜色设置
.completion-popover {
  display: flex;
  flex-direction: column;
  align-items: center;
  // 颜色设置
  .color-setting {
    &__preview {
      display: inline-flex;
      width: 30px;
      height: 6px;
      margin: 0px 10px;
      border-radius: 3px;

      &--red {
        background-color: #fd6865;
      }
      &--orange {
        background-color: #fec069;
      }
      &--green {
        background-color: #25ceba;
      }
    }
    .el-input {
      width: 40px;
      padding-right: 5px;
      input {
        padding: 0 6px;
      }
    }
  }
  // 设置按钮
  .setting-button {
    width: 230px;
    padding: 10px 0px;
    padding-bottom: 0px;
    margin-top: 12px;
    text-align: center;
    border-top: 1px solid $line-input;
  }
}

.completion-filter {
  .filter-form {
    .time-selector {
      .btn-text {
        border: none;
      }
      .el-select-dropdown.el-popper.is-multiple {
        display: none;
      }
    }
  }
}
</style>
