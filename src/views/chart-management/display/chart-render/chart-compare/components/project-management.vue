<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 方案管理弹窗
 -->
<template>
  <div class="project-management">
    <el-dialog
      append-to-body
      :before-close="closeDialog"
      class="project_manage"
      :title="$t('display.chartCompare.planManage')"
      :visible.sync="projectVisible"
    >
      <!-- 方案总数 -->
      <div class="project-show">
        <div class="project-show__total">
          {{ $t('display.chartCompare.planTotal') + ':'
          }}<span class="project-show__num">{{ projects.length }}</span
          >/10
          <el-tooltip
            class="item"
            :content="$t('display.chartCompare.planPK')"
            effect="dark"
            placement="top-start"
          >
            <i class="web-icon-questMark web-iconfont"></i>
          </el-tooltip>
        </div>

        <div
          class="project-show__add"
          :class="{ 'project-show__add--disabled': projectList.length >= 10 }"
          @click="addProject"
        >
          {{ $t('display.chartCompare.addPlan') }}
        </div>
      </div>
      <!-- 方案排序、编辑、删除 -->
      <div class="project-sort">
        <div class="project-sort__title">
          <div class="project-sort__left">{{ $t('display.chartCompare.classify') }}</div>
          <div class="project-sort__right">{{ $t('display.chartCompare.editOperate') }}</div>
        </div>
        <VueDraggable v-model="projects" v-draggable handle=".project-sort__move">
          <div v-for="(project, index) of projects" :key="index" class="project-sort__data">
            <div class="project-sort__move"><i class="el-icon-rank web-iconfont"></i></div>
            <div class="project-sort__left">{{ project.name }}</div>
            <div class="project-sort__right">
              <span class="project-sort__edit" @click="editProject(project)">{{
                $t('operation.edit')
              }}</span
              ><span class="project-sort__delete" @click="deleteProject(index)">{{
                $t('operation.delete')
              }}</span>
            </div>
          </div>
        </VueDraggable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">{{ $t('operation.cancel') }} </el-button>
        <el-button type="primary" @click="submit">{{ $t('operation.confirm') }} </el-button>
      </span>
    </el-dialog>

    <!-- 编辑pk方案 -->
    <edit-project
      v-if="editProjectVisit"
      :edit-project-visit.sync="editProjectVisit"
      :is-add="isAddProject"
      :project="project"
      @projectAdd="projectAdd"
      @projectSubmit="projectSubmit"
    ></edit-project>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import EditProject from './edit-project.vue'
import { deleteSortPKProject } from '@/api/statistics.js'

const MAX_PROJECT_NUM = 10
export default {
  name: 'ProjectManagement',

  components: {
    EditProject
  },

  props: {
    projectVisible: {
      type: Boolean,
      default: false
    },
    projectList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      // pk方案列表
      projects: [],
      // 新建、编辑pk方案弹窗是否显示
      editProjectVisit: false,
      // 是否为新增pk方案
      isAddProject: '',
      // 方案对象
      project: {}
    }
  },

  mounted() {
    this.projects = JSON.parse(JSON.stringify(this.projectList))
  },

  methods: {
    // 关闭方案管理弹窗
    closeDialog() {
      this.$emit('update:projectVisible', false)
    },
    // 打开新建pk方案弹窗
    addProject() {
      if (this.projectList.length < MAX_PROJECT_NUM) {
        this.editProjectVisit = true
        this.isAddProject = true
      } else {
        if (this.projects.length < MAX_PROJECT_NUM) {
          this.$message.warning(this.$t('display.chartCompare.saveFirst'))
        } else {
          this.$message.warning(this.$t('display.chartCompare.maxTenPlan'))
        }
      }
    },
    // 打开编辑pk方案弹窗
    editProject(project) {
      this.editProjectVisit = true
      this.isAddProject = false
      this.project = project
    },
    // 编辑pk方案确定
    projectSubmit(project) {
      // 若直接修改成功
      for (const index in this.projects) {
        if (this.projects[index].id === project.id) {
          this.$set(this.projects, index, project)
        }
      }
      this.$emit('projectSubmit', project)
    },
    // 新增pk方案确定
    projectAdd(project) {
      this.projects.push(project)
      this.$emit('projectAdd', project)
    },
    // 删除pk方案
    deleteProject(index) {
      this.projects.splice(index, 1)
    },
    // 保存方案管理
    submit() {
      this.deleteSortPKProject()
      this.$emit('update:projectList', this.projects)
      this.$emit('update:projectVisible', false)
    },
    // 删除、排序后请求接口
    deleteSortPKProject() {
      this.projects.forEach((item, index) => {
        this.$set(item, 'sort', this.projects.length - index)
      })
      return new Promise((resolve) => {
        deleteSortPKProject({ list: this.projects })
          .then((res) => {
            resolve()
          })
          .catch(() => {})
      })
    }
  }
}
</script>

<style lang="scss">
.project_manage {
  // 方案展示
  .project-show {
    display: flex;
    justify-content: space-between;
    height: 22px;
    margin-bottom: 26px;
    font-size: 13px;
    line-height: 22px;
    &__total {
      display: inline-block;
    }
    &__num {
      font-size: 14px;
    }
    &__add {
      display: inline-block;
      color: $link-base-color-6;
      cursor: pointer;
      &--disabled {
        color: $text-grey;
        cursor: not-allowed;
      }
    }
    .web-icon-questMark {
      padding-left: 5px;
      color: $brand-color-5;
    }
  }
  // 方案排序
  .project-sort {
    font-size: 14px;
    border: 1px solid $line-input;
    &__title {
      height: 44px;
      line-height: 44px;
      background-color: $line-cut-light;
    }
    &__data {
      position: relative;
      height: 44px;
      line-height: 44px;
      border-bottom: 1px solid $line-input;
      &:last-child {
        border-bottom: none;
      }
      &:first-child {
        border-top: 1px solid $line-input;
      }
    }
    &__left {
      box-sizing: border-box;
      display: inline-block;
      width: 70%;
      padding-left: 35px;
    }
    &__right {
      display: inline-block;
      text-align: right;
    }
    &__move {
      position: absolute;
      left: 10px;
      cursor: move;
    }
    &__edit {
      padding-right: 12px;
      margin: 0;
      font-size: 13px;
      color: $link-base-color-6;
      cursor: pointer;
      border-right: 1px solid $line-input;
    }
    &__delete {
      padding-left: 12px;
      margin: 0;
      font-size: 13px;
      color: $error-base-color-6;
      cursor: pointer;
    }
  }
}
</style>

<style lang="scss" scoped>
.project-management {
  width: 100%;
}
</style>
