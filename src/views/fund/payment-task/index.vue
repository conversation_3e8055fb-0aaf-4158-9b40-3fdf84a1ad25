<!--
 * @Description: 回款计划的列表页
 -->
<!--
组件关联页面：回款计划
-->
<template>
  <div class="communicate-plan list">
    <v-top-bottom-page :template-empty="templateEmpty">
      <!-- 头部 -->
      <list-head
        ref="listHead"
        slot="header"
        :is-show-update.sync="isShowUpdate"
        :show-search="false"
        :top-permissions="topPermissions"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <!-- 内容 -->
      <div slot="layout-column" class="communicatePlay-body content">
        <!-- 上方筛选 -->
        <list-panel-special
          :common-filter-list="commonFilter"
          :show-filter-list="showFilterList"
          @getFormData="panelFilterParams"
        >
        </list-panel-special>
        <div class="body-content">
          <div class="content-left">
            <div class="calendar">
              <calendar ref="calendar" :params="commonParams" @chooseDay="calendarClick"></calendar>
            </div>
            <div v-if="statisticsListShow" class="program">
              <p class="title">{{ date }} 下属员工的{{ pageInfo.pageTitle }}</p>
              <el-table
                :data="statisticsList"
                style="z-index: 0; width: 100%; margin-top: 25px"
                @row-click="gotoSelect"
              >
                <el-table-column align="center" label="执行人" prop="name"> </el-table-column>
                <el-table-column align="center" label="计划个数" prop="planCount">
                </el-table-column>
                <el-table-column align="center" label="完成个数" prop="finishPlanCount">
                </el-table-column>
                <el-table-column align="center" :label="$t('CRM.finishRatio')" prop="finishRatio">
                </el-table-column>
              </el-table>
              <div
                class="block"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-top: 27px;
                "
              >
                <!-- <div>{{ $t('CRM.totalHave') }}<span>{{statisticPageHelper.total}}个</span></div> -->
                <v-pagination
                  :current-page="statisticPageHelper.currentPage"
                  layout="slot, prev, pager, next"
                  :page-size="statisticPageHelper.pageSize"
                  :placeholder="$t('unit.total') + statisticPageHelper.total + '个员工'"
                  :total="statisticPageHelper.total"
                  @current-change="handleCurrentChange"
                >
                </v-pagination>
              </div>
            </div>
            <div v-else class="program">
              <div class="program-title">
                <p class="title">
                  <span>{{ selectName }}</span>
                  {{ pageInfo.pageTitle }}
                </p>
                <span v-if="dataPermission" class="back" @click="goBack">{{
                  $t('operation.back')
                }}</span>
              </div>
              <ul class="plan-list">
                <li class="list-detail">
                  <span>
                    <b class="disc" style="background: #74c3ff"></b>
                    {{ $t('nouns.attrOf', { attr: $t('nouns.unfinish') })
                    }}{{ pageInfo.pageTitle }}</span
                  >
                  <span class="noFollow number">{{ notFinishPlanCount }}</span>
                </li>
                <li class="list-detail">
                  <span>
                    <b class="disc" style="background: #48cc6d"></b>
                    {{ $t('nouns.attrOf', { attr: $t('nouns.finished') })
                    }}{{ pageInfo.pageTitle }}</span
                  >
                  <span class="finish number">{{ finishPlanCount }}</span>
                </li>
                <li class="list-detail">
                  <span>
                    <b class="disc" style="background: #74c3ff"></b>
                    未完成的计划金额
                  </span>
                  <span class="delaied number">{{ notFinishMoney }}</span>
                </li>
                <li class="list-detail">
                  <span>
                    <b class="disc" style="background: #48cc6d"></b>
                    已完成的计划金额
                  </span>
                  <span class="cancel number">{{ finishMoney }}</span>
                </li>
              </ul>
              <div class="finish-state">
                <div class="state">
                  <span class="title"
                    >{{ pageInfo.pageTitle }}{{ $t('constants.globalConfig.completeRate') }}</span
                  >
                  <span class="rate">{{ finishRatio }}%</span>
                </div>
                <el-progress
                  :percentage="finishRatio"
                  status="success"
                  :stroke-width="18"
                  :text-inside="true"
                ></el-progress>
              </div>
            </div>
          </div>

          <ul
            v-if="planList.length"
            id="planList"
            v-loading="loading"
            class="content-right"
            @scroll="handleScroll"
          >
            <template v-for="plan in planList">
              <li :key="plan.id" class="list-item" @click="showPlanDetail(plan)">
                <div class="left-info">
                  <div class="left-time">{{ formatHHMM(plan.communicateTime) }}</div>
                  <div class="middle-detail">
                    <div class="detail-top">
                      <span class="plan">{{ plan.serialNo }}</span>
                      <el-tag class="executor" type="info">{{ plan.userName }}</el-tag>
                      <span
                        class="company"
                        @click="showCustomerDetail(plan.linkCustomer, $event)"
                        >{{ plan.customerName }}</span
                      >
                    </div>
                    <p class="memo">
                      <span>计划收款金额:{{ plan.paymentTaskNum }}</span>
                    </p>
                    <el-tooltip
                      :content="getConverUserStr(userMap, plan.id)"
                      :disabled="converUser(userMap, plan.id).length < 4"
                      placement="top"
                    >
                      <ul class="userArr">
                        <li
                          v-for="(user, index) in converUser(userMap, plan.id)"
                          :key="user.userName + '' + user.id"
                          class="user"
                        >
                          <template v-if="index < 3">
                            <avatar-img :alt="user.userName" :size="24" :src="user.userAvatar" />
                            <span class="name">{{ user.userName }}</span>
                          </template>
                          <template v-else-if="index === 3">
                            <span>...</span>
                          </template>
                        </li>
                      </ul>
                    </el-tooltip>
                  </div>
                </div>
                <div
                  v-clickoutside="isStatusCloseFn"
                  class="right-prompt"
                  :class="{
                    noFollow: plan.isFinish === 1,
                    finish: plan.isFinish === 2
                  }"
                >
                  <span @click.stop="isStatusOpenFn(plan)">
                    {{ statusText(plan) }}
                    <span
                      class="rightIcon"
                      :class="[plan.isStatusOpen ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"
                    ></span>
                  </span>
                </div>
                <transition name="slide">
                  <ul v-if="plan.isStatusOpen" class="statusSelect" @click.stop>
                    <li
                      v-for="operator in contentPermissions"
                      :key="operator.id"
                      class="statusItem"
                      @click.stop="clickHandle(operator, plan)"
                    >
                      <b class="disc" style="background: #ff6161"></b
                      ><span>{{ operator.value }}计划</span>
                    </li>
                  </ul>
                </transition>
              </li>
            </template>
          </ul>
          <v-empty v-else :notice="$t('CRM.todayNo') + pageInfo.pageTitle"></v-empty>
        </div>
      </div>
      <template slot="layout-dialog">
        <exportDialog
          :dialog-form-visible.sync="dialogFormVisible"
          @submit="exportSubmit"
        ></exportDialog>
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'
import calendar from './components/calendar'
import vEmpty from '@/components/page-empty'
import {
  getFilterPaymentTaskList,
  getPaymentTaskList,
  getPaymentTaskStatisticsList
} from '@/api/form-special-list.js'
import { exportPaymentTaskFormData } from '@/api/export.js'
import exportDialog from './components/export-dialog'
import { mapGetters } from 'vuex'

export default {
  name: 'PaymentTask',

  components: {
    Calendar: calendar,
    ExportDialog: exportDialog,
    VEmpty: vEmpty
  },

  mixins: [listMixin, listEditMixin],

  data() {
    return {
      statisticPageHelper: {
        // 统计接口的分页信息
        total: 0,
        pageSize: 5,
        currentPage: 1
      },
      planListPageHelper: {
        // 访客计划列表的分页信息
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      commonParams: {
        // 公共的查询条件
        listGroupId: ''
      },
      exportParams: {}, // 导出的查询条件
      showFilterList: {
        // 前端控制的固定筛选条件
        range: {
          placeholder: ''
        }
      },
      disableToolTip: true, // 是否显示tooltip
      dialogFormVisible: false, // 导出的弹出框

      planList: [], // 右侧访客计划列表
      statisticsList: [], // 下方统计接口
      delayCauseOptions: [],
      statisticsListShow: false,
      isScroll: 0,
      userMap: [],
      isBoss: false,
      date: '',
      selectName: '',
      dataPermission: '',
      pid: '',
      treeType: '',
      finishPlanCount: 0,
      notFinishPlanCount: 0,
      notFinishMoney: 0,
      finishMoney: 0,
      planCount: 0,
      finishRatio: 0,
      contentPermissions: [], // 删除计划相关信息
      isShowUpdate: true
    }
  },

  computed: {
    ...mapGetters({
      appInfo: 'proList/getAppInfo' // appInfo的信息
    })
  },

  created() {
    if (localStorage.getItem('isShowUpdate')) {
      this.isShowUpdate = false
    }
  },

  methods: {
    /**
     * @description: 鼠标进入备注内容，判断是否显示tooltip
     * @param {type}
     * @return:
     */
    mouseEnter(el) {
      if (el.currentTarget.parentElement.offsetWidth < el.target.offsetWidth) {
        this.disableToolTip = false
      } else {
        this.disableToolTip = true
      }
    },
    // 获取列表数据
    getSpecialFormList(params, refer) {
      this.commonParams.listGroupId = params.listGroupId
      this.commonParams.status = params.commonFilter.status
      this.commonParams.nameLike = params.commonFilter.nameLike
      this.commonParams.rangeScreen = params.commonFilter.rangeScreen
      this.commonParams.pid = this.pid
      this.commonParams.treeType = this.treeType
      this.commonParams.formId = params.formId
      this.commonParams.distributorMark = params.distributorMark
      // 没有获取到筛选项的话，会去获取
      getFilterPaymentTaskList(params)
        .then((data) => {
          this.commonFilter = data.result.fieldList
        })
        .catch(() => {})
      // 右侧列表请求
      this.planList = []
      this.userMap = []
      this.planListPageHelper.currentPage = 1
      this.statisticPageHelper.currentPage = 1
      this.getPaymentTaskList()
      // 左下角统计请求
      this.getPaymentTaskStatisticsList()
    },
    // 获取回款计划列表
    getPaymentTaskList() {
      // const { listGroupId, status, nameLike, rangeScreen } = this.commonParams
      const { formId, distributorMark } = this.appInfo // 需要传formId和经销商标识
      const params = {
        ...this.commonParams,
        date: this.date,
        pid: this.pid,
        treeType: this.treeType,
        page: this.planListPageHelper.currentPage,
        pageSize: this.planListPageHelper.pageSize,
        formId,
        distributorMark
      }
      // 导出的查询参数存储
      this.exportParams = params
      getPaymentTaskList(params)
        .then((data) => {
          this.contentPermissions = data.result.contentPermissions || []
          this.disposeCommonFormListData(data)
          if (this.isScroll === 0) {
            this.planList = []
            this.userMap = []
          }
          data.result.planList.forEach((item) => {
            this.planList.push(item)
          })
          this.userMap.push(data.result.userMap)
          this.$nextTick(() => {
            this.$refs.calendar.loadPageCalendar('plan')
          })
          this.planListPageHelper.total = data.result.pageHelper.rowsCount
          this.showDetailVisible = false
          setTimeout(() => {
            this.loading = false
          }, 300)
          this.planList = this.planList.map((item) => {
            item.isStatusOpen = false
            return item
          })
        })
        .catch(() => {})
    },
    // 获取统计列表
    getPaymentTaskStatisticsList(page) {
      this.statisticPageHelper.currentPage = page || 1
      const { formId, distributorMark } = this.appInfo // 需要传formId和经销商标识
      const params = {
        ...this.commonParams,
        date: this.date,
        pid: this.pid,
        treeType: this.treeType,
        page: this.statisticPageHelper.currentPage,
        pageSize: this.statisticPageHelper.pageSize,
        formId,
        distributorMark
      }
      getPaymentTaskStatisticsList(params)
        .then((data) => {
          this.statisticsArray = data.result.statisticsArray
          this.statisticPageHelper.total = data.result.pageHelper.rowsCount
          this.dataPermission = data.result.dataPermission
          this.statisticsListShow = data.result.dataPermission
          // if (typeof (refer) === 'undefined') {
          //   this.statisticsListShow = data.result.dataPermission
          // }
          this.selectName = data.result.loginUserName
          this.isBoss = data.result.isBoss
          this.exchangeStatisticsList()
          if (!data.result.dataPermission) {
            data.result.statisticsArray.length && this.statisticsOne(data.result.statisticsArray[0])
          }
        })
        .catch(() => {})
    },

    /**
     * @description: 打开客户的详情
     * @param {type}
     * @return:
     */
    showCustomerDetail(info, e) {
      const { dataId, businessType, saasMark } = info
      this.beforeOpenDetail({ dataId, businessType, saasMark })
      e.stopPropagation()
    },
    // 统计列表 数据转化
    exchangeStatisticsList() {
      this.statisticsList = []
      let finishPlanCount = 0
      let planCount = 0
      for (let i = 0; i < this.statisticsArray.length; i++) {
        const statisticsInfo = this.statisticsArray[i]
        finishPlanCount = this.formatNum(statisticsInfo.finishPlanCount)
        planCount = this.formatNum(statisticsInfo.planCount)
        const planJson = {
          name: statisticsInfo.userName,
          planCount: planCount,
          finishPlanCount: finishPlanCount,
          finishRatio: this.formatPercent(finishPlanCount / planCount) + '%',
          index: i
        }
        this.statisticsList.push(planJson)
      }
    },

    /**
     * @description: 导出确认
     * @param {type}
     * @return:
     */
    exportSubmit(exportForm) {
      const nearbyCustomerExportParams = {
        ...this.exportParams,
        planDateArr: exportForm.date
      }
      const params = {
        promise: exportPaymentTaskFormData(nearbyCustomerExportParams)
      }
      this['proExport/startExport'](params)
    },

    /**
     * @description: 回款计划导出前的数据处理
     * @param {type}
     * @return:
     */
    beforeExport() {
      this.dialogFormVisible = true
    },
    // 选中某个
    gotoSelect(row) {
      this.loading = true
      const oneData = this.statisticsArray[row.index]
      this.statisticsOne(oneData)
      this.statisticsListShow = false
      this.pid = oneData.userId
      this.treeType = 'staff'
      this.isScroll = 0
      this.planListPageHelper.currentPage = 1
      this.getPaymentTaskList()
    },
    refreshAll() {
      setTimeout(() => {
        this.showDetailVisible = false
        this.loading = true
        this.getPaymentTaskList()
        this.getPaymentTaskStatisticsList()
      }, globalConst.DELAY_TIME)
    },
    // 打开详情
    showPlanDetail(plan) {
      const dataId = plan.id
      const businessType = this.catchAppInfo.businessType
      const appId = this.catchAppInfo.appId
      const saasMark = 1
      this.beforeOpenDetail({ appId, dataId, businessType, saasMark })
    },

    // 统计返回
    goBack() {
      this.statisticsListShow = true
      this.treeType = ''
      this.pid = ''
      this.isScroll = 0
      this.page1 = 1
      this.userName = ''
      this.loading = true
      this.getPaymentTaskStatisticsList()
      this.getPaymentTaskList()
    },
    handleCurrentChange(currentPage) {
      this.getPaymentTaskStatisticsList(currentPage)
    },
    // 单条统计 数据
    statisticsOne(statisticsInfo) {
      this.finishPlanCount = this.formatNum(statisticsInfo.finishPlanCount)
      this.notFinishPlanCount = this.formatNum(statisticsInfo.notFinishPlanCount)
      this.notFinishMoney = this.formatNum(statisticsInfo.notFinishMoney)
      this.finishMoney = this.formatNum(statisticsInfo.finishMoney)
      this.planCount = this.finishPlanCount + this.notFinishPlanCount
      this.selectName = statisticsInfo.userName
      this.userName = statisticsInfo.userName
      this.finishRatio = this.formatPercent(this.finishPlanCount / this.planCount)
    },
    formatNum(num) {
      if (typeof num === 'undefined') {
        return 0
      } else {
        return num
      }
    },
    formatPercent(num) {
      if (isNaN(num)) {
        return 0
      } else {
        return Number((num * 100).toFixed(0))
      }
    },
    // 日期的点击
    calendarClick(dateSelect) {
      if (!this.statisticsListShow) {
        this.userName = ''
        this.pid = ''
        this.treeType = ''
      }
      this.isScroll = 0
      this.planListPageHelper.currentPage = 1
      this.date = dateSelect
      this.getFormList()
    },
    // 滚动加载
    handleScroll() {
      const scrollEle = document.getElementById('planList')
      // 滚动条上边滑动的高度
      const scrolled = scrollEle.scrollTop
      // 总的滚动高度
      const scrollHeight = scrollEle.scrollHeight
      // 列表高度
      const bodyHeight = scrollEle.clientHeight
      const { currentPage, total } = this.planListPageHelper
      if (scrolled + bodyHeight > scrollHeight * 0.9 && currentPage * 10 < total) {
        this.planListPageHelper.currentPage++
        this.isScroll = 1
        this.loading = true
        this.getPaymentTaskList()
      }
    },
    // 将执行人信息转化成数组
    converUser(userMap, planId) {
      let userArr = []
      for (let i = 0; i < userMap.length; i++) {
        const oneMap = userMap[i]
        if (typeof oneMap[planId] !== 'undefined') {
          userArr = oneMap[planId]
        }
      }
      if (userArr.length < 1) {
        // 没有执行人的不显示，不统计
        return []
      } else {
        return userArr
      }
    },
    // 访客计划状态
    statusText(plan) {
      if (plan.isFinish === 1) {
        return '未完成'
      } else {
        return '已完成'
      }
    },
    isStatusOpenFn(plan) {
      if (plan.isStatusOpen) {
        plan.isStatusOpen = false
      } else {
        this.planList = this.planList.map((item) => {
          item.isStatusOpen = false
          return item
        })
        plan.isStatusOpen = true
      }
    },
    isStatusCloseFn() {
      this.planList = this.planList.map((item) => {
        item.isStatusOpen = false
        return item
      })
    },
    // 删除计划
    deletePlan(plan) {
      console.log(plan)
      this.deleteSingle({ dataIdList: [plan.id] })
    },
    clickHandle(operator, plan) {
      if (operator.attr === 'del') {
        // 删除计划
        this.deletePlan(plan)
      } else {
        console.log(`${plan.value}逻辑未添加！`)
      }
    },
    getConverUserStr(userMap, planId) {
      const userArr = this.converUser(userMap, planId)
      let str = ''
      userArr.forEach((item, i) => {
        if (i !== userArr.length - 1) {
          str += item.userName + ','
        } else {
          str += item.userName
        }
      })
      return str
    },
    formatHHMM(date) {
      const time = new Date(date * 1000)
      let h = time.getHours()
      let mm = time.getMinutes()
      h = h < 10 ? '0' + h : h
      mm = mm < 10 ? '0' + mm : mm
      return h + '：' + mm
    }
  }
}
</script>

<style lang="scss" scoped>
.communicate-plan {
  height: 100%;
}
.content {
  display: flex;
  flex: auto;
  flex-direction: column;
  width: 100%;
}
.communicatePlay-body {
  padding: 10px;
  & :deep(.input-text) {
    width: 165px;
  }
  .body-content {
    display: flex;
    flex: auto;
    padding: 10px;
    overflow-y: auto;
    background: #f5f8fd;
    .content-left {
      flex: none;
      width: 460px;
      margin-left: 10px;
      overflow-y: auto;
    }
    .content-right {
      flex: auto;
      margin-left: 10px;
      overflow-y: auto;
    }
  }
}
.body-content .content-left .calendar {
  box-sizing: border-box;
  width: 100%;
  height: 300px;
  padding-top: 10px;
  background: $base-white;
  border-radius: 4px;
}
.body-content .content-left .program {
  box-sizing: border-box;
  width: 100%;
  padding: 28px 30px;
  margin-top: 10px;
  background: $base-white;
  border-radius: 4px;
}
.body-content .content-left .program .title {
  font-size: 16px;
  color: $text-main;
}
.body-content .content-left .program .list-detail {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}
.body-content .content-left .program .plan-list {
  margin: 0px 0 20px 0;
}
.body-content .content-left .program .list-detail .disc {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 10px;
  border-radius: 100%;
}
.body-content .content-left .program .list-detail .number {
  font-size: 20px;
}
.body-content .content-left .program .finish-state {
  padding-top: 24px;
  border-top: 1px dotted $neutral-color-3;
}
.body-content .content-left .program .finish-state .state {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.body-content .content-left .program .finish-state .state .title {
  font-size: 16px;
  color: #444c63;
}
.body-content .content-left .program .finish-state .state .rate {
  font-size: 20px;
  color: $text-plain;
}
.body-content .content-right .list-item {
  .statusSelect {
    position: absolute;
    top: 58%;
    right: 0;
    z-index: 10;
    width: 92px;
    background: $base-white;
    border-radius: 1px;
    box-shadow: 0 0 2px 0 rgba(114, 114, 114, 0.5);
    .statusItem {
      padding: 10px 10px;
      font-size: 13px;
      line-height: 30px;
      color: $text-plain;
      .disc {
        display: inline-block;
        width: 7px;
        height: 7px;
        margin-right: 10px;
        border-radius: 100%;
      }
    }
  }
}
// 未完成
.body-content .content-right .noFollow {
  background: $link-color-4;
}
// 已延期
.body-content .content-right .delaied {
  background: $violets-color-4;
}
// 取消
.body-content .content-right .cancel {
  background: $neutral-color-4;
}
// 已完成
.body-content .content-right .finish {
  background: $success-color-4;
}
// 未完成
.body-content .content-left .noFollow {
  color: $link-color-4;
}
// 已延期
.body-content .content-left .delaied {
  color: $violets-color-4;
}
// 取消
.body-content .content-left .cancel {
  color: $neutral-color-4;
}
// 已完成
.body-content .content-left .finish {
  color: $success-color-4;
}
.content-right {
  .list-item {
    position: relative;
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 120px;
    margin: 0 0 10px 0;
    cursor: pointer;
    background: $base-white;
    border-radius: 4px;
    .left-info {
      display: flex;
      flex: auto;
      padding: 14px 0 12px 15px;
      overflow: hidden;
      .left-time {
        flex: none;
        width: 94px;
        font-size: 24px;
        line-height: 90px;
        color: $text-plain;
        border-right: 2px dotted $neutral-color-3;
      }
      .middle-detail {
        flex: auto;
        padding: 0 14px 0 14px;
        overflow: hidden;
      }
    }
    .right-prompt {
      // position: absolute;
      // top: 0;
      // bottom: 0;
      // right: 0;
      flex: none;
      width: 92px;
      height: 100%;
      font-size: 15px;
      line-height: 120px;
      color: $base-white;
      text-align: center;
      .rightIcon {
        display: inline-block;
        margin-left: 5px;
        font-size: 12px;
        opacity: 0.6;
      }
    }
  }
}
.content-right .middle-detail .detail-top {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
}
.content-right .middle-detail .detail-top .plan {
  display: inline-block;
  width: 30%;
  margin-right: 15px;
  overflow: hidden;
  font-size: 15px;
  line-height: initial;
  color: $text-plain;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.content-right .middle-detail .detail-top .executor {
  margin-right: 25px;
}
.content-right .middle-detail .detail-top .company {
  display: inline-block;
  padding-left: 20px;
  font-size: 13px;
  color: $link-base-color-6;
  border-left: 1px solid $bg-blue;
}
.content-right .middle-detail .memo {
  width: 100%;
  margin: 15px 0;
  overflow: hidden;
  font-size: 13px;
  color: $text-plain;
  text-overflow: ellipsis;
  word-break: keep-all;
  white-space: nowrap;
}
.content-right .middle-detail .userArr {
  display: flex;
  align-items: center;
  overflow: hidden;
}
.content-right .middle-detail .userArr .user {
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  margin-right: 20px;
}
.content-right .middle-detail .userArr .user .img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  font-size: 0px;
  background: #5ec9f6;
  border-radius: 50%;
}
.content-right .middle-detail .userArr .user .name {
  padding-left: 5px;
  font-size: 13px;
  color: $text-plain;
}
.mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: $text-main;
  opacity: 0.2;
}
.communicate-plan .content-right .noVisitorPlan {
  position: absolute;
  top: 35%;
  right: 40%;
  margin: 0 auto;
  font-size: 20px;
  color: $text-plain;
  text-align: center;
}

.communicate-plan .program-title {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}
.communicate-plan .program-title .back {
  display: inline-block;
  width: 40px;
  height: 26px;
  font-size: 14px;
  line-height: 26px;
  color: $text-grey;
  text-align: center;
  cursor: pointer;
  border: 1px solid $neutral-color-3;
  border-radius: 20px;
}
.communicate-plan .program-title .back:hover {
  color: #5ec9f6;
  border: 1px solid #5ec9f6;
}
</style>
