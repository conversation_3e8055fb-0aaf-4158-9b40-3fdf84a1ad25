<!--
 * @Description: 预收款列表
 -->
<template>
  <div class="advance-collection">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :data-list="parSubFieldList"
          :show-filter-list="specialFilter"
          @getDataSummary="getTableSummary"
          @getFormData="panelFilterParams"
        >
        </list-panel>
        <list-table
          ref="table"
          :column-invisible-field="columnInvisibleField"
          :data-list="parSubFieldList"
          :loading="loading"
          :summary="summary"
          :table-data="tableData"
          :table-head="tableHead"
          @editClick="editTable"
          @editForm="editFormDispatch"
        ></list-table>
        <list-footer
          :archived="archived"
          :page-helper="pageHelper"
          :permission="batchEditButtons"
          @batch-handle="batchHandle"
          @getFormData="footerFilterParams"
        >
          <!-- 批量操作按钮 -->
          <template slot="permission" slot-scope="props">
            <el-button
              :disabled="!!props.type.disabled"
              plain
              size="mini"
              :type="buttonType(props.type.attr)"
              @click="batchHandle(props.type)"
            >
              <div class="list-button-icon">
                <i :class="props.type.icon + ' iconfont'"></i>
                <span>{{ props.text }}</span>
              </div>
            </el-button>
          </template>
        </list-footer>
      </div>
      <template slot="layout-dialog"> </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'

export default {
  name: 'AdvanceCollection',
  mixins: [listMixin, listEditMixin],

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.formListApi
        .getAdvanceCollectionList(params)
        .then((data) => {
          // 处理请求数据格式
          this.disposeFormListData(data)
        })
        .catch(() => {})
    },

    // 添加回款单
    addCollection({ dataIdList, data }) {
      const { businessType, saasMark, distributorMark } = this['proList/getAppInfo']
      const params = {
        linkBusinessType: businessType,
        businessType: Number(data.businessType),
        saasMark,
        distributorMark,
        dataIdList,
        saveUrl: 'linkAddCollectionSave'
      }
      this.formDetailLinkAdd(params)
    },
    // 核销操作方法
    openWriteOff({ dataIdList, data }) {
      const { saasMark, distributorMark } = this['proList/getAppInfo']
      const params = {
        distributorMark,
        linkBusinessType: Number(data.linkBusinessType),
        businessType: Number(data.businessType),
        saasMark,
        dataIdList: [data.customerId]
      }
      this.formDetailLinkAdd(params)
    },
    // 供应商的删除提示
    specialNotice(data) {
      // 有些情况下，供应商不允许被删除
      const memo = data.result.errorDataMemo
      const errorList = data.result.errorDataList
      return new Promise((resolve, reject) => {
        if (memo && errorList.length) {
          const h = this.$createElement
          const title = this.$t('message.delRemind')
          const message = h('div', { class: 'confirm-con' }, [
            h('h4', { class: 'tit' }, `${memo}`),
            h(
              'ul',
              { class: 'c-list' },
              errorList.map((item) => {
                return h('li', { class: 'item' }, item)
              })
            )
          ])
          const options = {
            confirmButtonText: this.$t('message.know')
          }
          this.$alert(message, title, options)
        } else {
          resolve(data)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.confirm-con {
  .tit {
    font-size: 14px;
    line-height: 1.5;
    color: $brand-color-5;
  }
  .msg {
    margin-top: 12px;
  }
}
</style>

<style lang="scss" scoped>
.advance-collection {
  height: 100%;
}
</style>
