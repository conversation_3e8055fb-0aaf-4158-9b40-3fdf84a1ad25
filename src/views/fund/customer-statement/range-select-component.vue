<!--
 * @Description:
-->
<template>
  <div class="range-select-component">
    <rangeSelect ref="selectRange" :field-info="fieldInfo" @valueChange="valueChange">
    </rangeSelect>
  </div>
</template>

<script>
import rangeSelect from '@/components/independentFields/range-select'
import bus from '@/utils/temp-vue'

export default {
  name: 'RangeSelectComponent',

  components: {
    RangeSelect: rangeSelect
  },
  model: {
    prop: 'value',
    event: 'modelChange'
  },

  props: {
    value: {
      type: Object
    },
    fieldInfo: {
      type: Object
    }
  },

  created() {
    bus.$on('clearData', this.clearData)
  },
  destroyed() {
    bus.$off('clearData')
  },
  mounted() {},

  methods: {
    init() {},
    valueChange(val) {
      this.$emit('modelChange', val)
    },
    clearData() {
      this.$refs.selectRange.clearSelect && this.$refs.selectRange.clearSelect()
    }
  }
}
</script>

<style lang="scss" scoped>
.range-select-component {
}
</style>
