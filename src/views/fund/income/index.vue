<!--
 * @Description: 其他收入单
-->
<template>
  <div class="collection list">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          ref="listPanel"
          :custom-mode-config="{ viewConfig, viewButton }"
          :gantt-head-list="ganttViewInfo.ganttHeadList"
          :show-filter-list="specialFilter"
          :stage-head-list="stageViewInfo.stageHeadList"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @changeGroupLaneVersion="changeGroupLaneVersion"
          @getFormData="panelFilterParams"
          @getLaneVersionData="getLaneVersionData"
          @getShowSwitchPhase="getShowSwitchPhase"
          @onViewChange="onViewChange"
        >
        </list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams,
            ...stageProps
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onStageView="onStageView"
          @onTableView="onTableView"
          @oncalendarView="oncalendarView"
        >
          <template #table-view-footer="props">
            <el-tooltip
              v-if="props.type.needRemind"
              class="item"
              :content="props.type.remindMsg"
              effect="dark"
              placement="top-start"
            >
              <div>
                <el-button
                  :disabled="!!props.type.disabled"
                  plain
                  size="mini"
                  @click="batchHandle(props.type)"
                >
                  <div class="list-button-icon">
                    <span>{{ props.text }}</span>
                  </div>
                </el-button>
              </div>
            </el-tooltip>
            <el-button
              v-else-if="!props.type.needRemind"
              :disabled="!!props.type.disabled"
              size="mini"
              @click="batchHandle(props.type)"
            >
              {{ props.text }}
            </el-button>
          </template>
        </component>
      </div>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'

export default {
  name: 'Collection',

  mixins: [listMixin, listEditMixin],

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      const { menuId } = this.catchAppInfo
      params.menuId = menuId
      this.initViewType({ params, api: this.formListApi.getOtherIncome })
    }
  }
}
</script>

<style lang="scss" scoped>
.collection {
  height: 100%;
}
</style>
