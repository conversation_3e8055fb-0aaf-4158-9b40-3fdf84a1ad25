<template>
  <div class="cost-adjust">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :data-list="parSubFieldList"
          :show-filter-list="specialFilter"
          @getDataSummary="getTableSummary"
          @getFormData="panelFilterParams"
        >
        </list-panel>
        <list-table
          ref="table"
          :column-invisible-field="columnInvisibleField"
          :data-list="parSubFieldList"
          :loading="loading"
          :summary="summary"
          :table-data="tableData"
          :table-head="tableHead"
          @editClick="editTable"
          @editForm="editFormDispatch"
        ></list-table>
        <list-footer
          :archived="archived"
          :page-helper="pageHelper"
          :permission="batchEditButtons"
          @batch-handle="batchHandle"
          @getFormData="footerFilterParams"
        >
          <!-- 批量操作按钮 -->
          <template slot="permission" slot-scope="props">
            <el-button
              plain
              size="mini"
              :type="buttonType(props.type.attr)"
              @click="batchHandle(props.type)"
            >
              <div class="list-button-icon">
                <i :class="props.type.icon + ' iconfont'"></i>
                <span>{{ props.text }}</span>
              </div>
            </el-button>
          </template>
        </list-footer>
      </div>
      <template slot="layout-dialog">
        <!-- 成员选择的弹窗 -->
        <organization-select
          v-if="showOrganizationSelect"
          :active-tab-name="organizationSelectActiveName"
          :default-val.sync="selectPersonsDefault"
          :dialog-visible.sync="showOrganizationSelect"
          :multiple="tabOption"
          :range-params="rangeParams"
          :show-tabs-name="organizationSelectShowTab"
          @dialogSubmit="SubmitSelectPersons"
        >
        </organization-select>
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'

export default {
  name: 'CostAdjust',

  mixins: [listMixin, listEditMixin],

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.formListApi
        .getCostAdjustList(params)
        .then((data) => {
          // 处理请求数据格式
          this.disposeFormListData(data)
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.cost-adjust {
  height: 100%;
}
</style>
