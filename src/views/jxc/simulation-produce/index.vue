<!--
 * @Description: 模拟生产页面
 -->

<!--
  模拟生产
 -->
<template>
  <div class="list simulation-produce">
    <div class="children-head">
      <h1 class="title">
        <span slot="title">{{ $t('jxc.simulatedProduction') }}</span>
        <span class="version">{{ $t('jxc.betaVersion') }}</span>
        <span class="hint">{{ $t('jxc.productTip1') }}</span>
      </h1>
    </div>
    <div
      v-loading="loading"
      class="commun-wrap"
      :element-loading-text="$t('jxc.startSimulation') + '...'"
    >
      <div class="field-out">
        <label class="el-form-item__label" style="width: 124px">{{ $t('jxc.selectBOM') }}</label>
        <el-form>
          <link-table
            v-model="formData[fieldInfo.attr]"
            :base-form-data="formData"
            class="simulation-link-table"
            :field-info="fieldInfo"
            :form-data="formData"
            :is-see="isSee"
            @modelChange="bomChange"
          >
          </link-table>
          <el-button class="save" :disabled="!bomObj.id" type="primary" @click="startSimulation">
            {{ $t('jxc.startSimulation') }}
          </el-button>
        </el-form>
      </div>
      <div class="simulation-table">
        <table border="0" cellpadding="0" cellspacing="0" class="tableclass">
          <tbody>
            <tr>
              <td>BOM</td>
              <td>
                <template v-if="bomName">
                  <span>关联BOM清单：{{ bomName }}</span>
                  <span class="bom-text">BOM版本：{{ bomVersion }}</span>
                </template>
                <span v-else class="no-bom">{{ $t('jxc.simulateProduction') }}</span>
              </td>
            </tr>
            <tr>
              <td>{{ $t('jxc.materiel') }}</td>
              <td>
                <div style="width: 99%">
                  <el-table
                    v-if="bomName"
                    border
                    :data="materielTableData"
                    max-height="400"
                    style="width: 100%"
                  >
                    <template v-for="(h, index) in materielExplains">
                      <!-- 兼容一下fieldType === 20009库存数量为数字类型 -->
                      <el-table-column
                        v-if="h.attr !== 'unitNum'"
                        :key="h.attr"
                        :align="h.fieldType === 2 || h.fieldType === 20009 ? 'right' : 'left'"
                        header-align="left"
                        :label="h.attrName"
                        :prop="h.attr"
                      >
                        <template slot="header" slot-scope="scope">
                          <div v-if="scope.column.property === 'warehouse'" class="batch-set">
                            {{ scope.column.label }}
                            <span
                              class="tag-batch"
                              @click="
                                (event) => {
                                  warehouseBatchHandle(scope.$index, event)
                                }
                              "
                              >{{ $t('form.batch') }}</span
                            >
                          </div>
                          <span v-else>{{ scope.column.label }}</span>
                        </template>
                        <template slot-scope="scope">
                          <template v-if="h.attr === 'warehouse'">
                            <el-select
                              v-model="scope.row.result[index].attrValue"
                              :placeholder="$t('placeholder.choosePls', { attr: '' })"
                              size="small"
                              value-key="id"
                              @change="
                                changeWareHouse(scope.$index, scope.row.result[index].attrValue)
                              "
                            >
                              <el-option
                                v-for="op in scope.row.result[index].warehouseArray"
                                :key="op.id"
                                :label="op.value"
                                :value="op"
                              >
                              </el-option>
                            </el-select>
                          </template>
                          <!-- 库存 -->
                          <template v-else-if="h.attr === 'stock'">
                            <div class="item-unit">
                              {{ scope.row.result[index].attrValue || 0 }}
                              <p
                                v-if="
                                  scope.row.result[index].attrValue && scope.row.transformUnitRate
                                "
                                class="item-unit__desc"
                              >
                                {{
                                  fetchUnitDesc(
                                    scope.row.transformUnitRate,
                                    scope.row.result[index].attrValue
                                  )
                                }}
                              </p>
                            </div>
                          </template>
                          <template v-else-if="[5, 6, 8, 10].indexOf(h.fieldType) === -1">
                            <span>{{ scope.row.result[index].attrValue }}</span>
                          </template>
                        </template>
                      </el-table-column>
                    </template>
                  </el-table>
                  <span v-else class="no-bom">{{ $t('jxc.simulateProduction') }}</span>
                </div>
              </td>
            </tr>
            <tr>
              <td>{{ $t('jxc.finishedProduct') }}</td>
              <td>
                <div v-if="bomName" class="ed-product">
                  <img
                    v-lazy="thumbnail(finishedProduct.thumbnail, 80)"
                    @click="openLightbox([finishedProduct.thumbnail])"
                  />
                  <div class="product-Info">
                    <p>产品名称：{{ finishedProduct.productName || '--' }}</p>
                    <p>产品编号：{{ finishedProduct.productNo || '--' }}</p>
                    <p>规格：{{ finishedProduct.specification || '--' }}</p>
                  </div>
                  <div class="product-Num">
                    <p>
                      库存总数量：{{ finishedProduct.stock
                      }}<span
                        v-if="finishedProduct.stock && finishedProduct.transformUnitRate"
                        class="unit-item__span"
                        >({{
                          fetchUnitDesc(finishedProduct.transformUnitRate, finishedProduct.stock)
                        }})</span
                      >
                    </p>
                    <p>
                      可生产数量：{{ simulationForm.canNum }}
                      <el-tooltip :content="$t('jxc.quantityTip')" effect="light" placement="top">
                        <span class="el-icon-question icon"></span>
                      </el-tooltip>
                      <span
                        v-if="simulationForm.canNum && finishedProduct.transformUnitRate"
                        class="unit-item__span"
                        >({{
                          fetchUnitDesc(finishedProduct.transformUnitRate, simulationForm.canNum)
                        }})</span
                      >
                    </p>
                  </div>
                </div>
                <span v-else class="no-bom">{{ $t('jxc.simulateProduction') }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="btnGroup">
        <el-button class="save" :disabled="afterSimulation" type="primary" @click="oneKey">{{
          $t('constants.globalConfig.oneButtonProduction')
        }}</el-button>
      </div>
    </div>
    <div class="set-form" :style="setFormStyle">
      <el-select
        v-model="batchWarsehouse"
        :placeholder="$t('placeholder.choosePls', { attr: '' })"
        size="small"
        value-key="id"
      >
        <el-option v-for="item in warsehouseMax" :key="item.id" :label="item.value" :value="item">
        </el-option>
      </el-select>
      <i class="blue el-icon-check set-btn" @click="setBatch"></i>
      <i class="el-icon-close red set-btn" @click="cancelBatch"></i>
    </div>
    <!-- 图片预览 -->
    <lg-preview
      v-if="isPreviewShow"
      :index="previewIndex"
      :list="imageList"
      @close="isPreviewShow = false"
    ></lg-preview>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { Message } from 'element-ui'
import { simulationProduceBomBillProductList } from '@/api/simulationProduce'
import linkTable from '@/components/all-fields/form-data-edit/sys-saas/link-table/link-table' // BOM选择
import tempVue from '@/utils/temp-vue'
import { getUnitNumStr } from '@/utils/unit'
// import bomField from 'components/fieldtype/productManage/bom-field.vue' // bom
import xbb from '@xbb/xbb-utils'

export default {
  provide: function (params) {
    return {
      ...this.formQuery
    }
  },
  props: {
    barcodeParams: {
      type: Object,
      default: () => ({})
    },
    isSee: {
      // 是否是查看模式
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {},
      formQuery: {
        appId: +this.$route.query.appId,
        menuId: +this.$route.query.menuId,
        formId: +this.$route.query.formId,
        // subBusinessType: this.$route.query.subBusinessType,
        businessType: +this.$route.query.subBusinessType,
        saasMark: +this.$route.query.saasMark
      },
      fieldInfo: {
        attr: 'bomArry',
        editable: 1,
        fieldType: 20001,
        isOpen: 1,
        linkedType: 2601,
        parentAttr: '',
        required: 0,
        saasAttr: 'bomId',
        saasParticularAttributePoJo: {
          multiCheck: false,
          relyMode: 'bom',
          relyType: 'base'
        },
        screenType: 1,
        setType: 1,
        showDetailAddress: 1,
        showType: 0,
        visible: 1
      },
      isPreviewShow: false, // 查看图片弹窗
      imageList: [], //  图片
      previewIndex: 0, // 显示第几个图片文件
      // 要提交的数据
      simulationForm: {
        bomId: {}, // bomId
        materiel: [], // 提交物料产品
        canNum: 0, // 可生产数量
        type: 'simulationProduce' // 一键生产类型
      },
      bomName: '', // bom名称(编号)
      bomVersion: '', // bom版本号
      bomObj: {}, // BOM相关
      loading: false,
      materielExplains: [], // 物料产品字段
      materielTableData: [], // 物料产品
      finishedProduct: {}, // 成品产品
      compareArr: [], // 用来比较大小
      checkout: false, // 校验
      afterSimulation: true, //  判断是否可点击一键生产
      setFormStyle: {}, // 仓库批量处理选择框样式
      warsehouseMax: [], // 所有物料仓库的最大集
      batchWarsehouse: null, // 批量选中的仓库
      materielWarehouse: [] // 存储所有物料的仓库信息
    }
  },

  mounted() {
    // this.initList('simulationProduce')
    tempVue.$on('formDataEditSuccess', () => {
      // 刷新
      this.init()
    })
  },

  beforeDestroy() {
    tempVue.$off('formDataEditSuccess')
  },

  computed: {
    ...mapGetters(['moduleName'])
  },
  methods: {
    ...mapActions([
      'formSimulationProduce'
      // 'initList'
    ]),

    fetchUnitDesc(unit, num) {
      const baseObj = unit.find((item) => item.isBase === 1)
      return getUnitNumStr(baseObj, unit, num)
    },

    thumbnail(img, size) {
      return xbb.thumbnail(img, size)
    },

    // 初始化参数
    init() {
      this.formData = {}
      this.afterSimulation = true
      this.bomObj = {}
      this.bomName = ''
    },
    // 图片预览
    openLightbox(urlList, previewIndex) {
      console.log(432, urlList, previewIndex)
      this.imageList = urlList
      this.previewIndex = previewIndex
      this.isPreviewShow = true
    },
    // 渲染物料项表头标题
    renderMaterialHead(h, { column, $index }) {
      // 仓库项添加批量处理
      if (column.property === 'warehouse') {
        return h(
          'div',
          {
            class: 'batch-set'
          },
          [
            column.label,
            h(
              'span',
              {
                class: 'tag-batch',
                on: {
                  click: (event) => this.warehouseBatchHandle($index, event)
                }
              },
              this.$t('form.batch')
            )
          ]
        )
      }
      return h('span', column.label)
    },
    // 点击仓库批量处理
    warehouseBatchHandle(index, event) {
      const position = event.target.getBoundingClientRect()
      this.setFormStyle = {
        left: `${position.left - 113}px`,
        top: `${position.top + 30}px`,
        display: 'inline-block'
      }
    },
    // 批量设置处理仓库
    setBatch() {
      if (this.batchWarsehouse) {
        this.materielWarehouse.forEach((warehouse, index) => {
          let flag = false
          let realWarehouse = null
          // 判断当前物料的仓库是否包含所选择批量处理的仓库
          for (let i = 0; i < warehouse.length; i++) {
            if (warehouse[i].id === this.batchWarsehouse.id) {
              realWarehouse = warehouse[i]
              flag = true
              break
            }
          }
          // 如果存在，更改页面对应显示信息
          if (flag) {
            const warsehouseInfo = this.materielTableData[index].result.filter((obj) => {
              return obj.attr === 'warehouse'
            })[0]
            this.$set(warsehouseInfo, 'attrValue', realWarehouse)
            this.changeWareHouse(index, realWarehouse)
          }
        })
      }
      this.cancelBatch()
    },
    // 关闭批量选择仓库
    cancelBatch() {
      this.setFormStyle = {}
    },
    // 选择bom后获得成品和物料
    bomChange(arr, headList) {
      console.log(878, headList)
      console.log(212, arr)
      this.bomObj = {}
      this.bomName = ''
      this.finishedProduct = {}
      this.afterSimulation = true
      if (headList) {
        headList.forEach((headItem) => {
          if (['sheetNo'].indexOf(headItem.saasAttr) > -1) {
            this.bomObj.name = arr[0][headItem.attr]
          } else if (['version'].indexOf(headItem.saasAttr) > -1) {
            this.bomObj.version = arr[0][headItem.attr]
          }
        })
        this.bomObj.id = arr[0].dataId
      }
    },

    // 仓库改变时清空批次信息和改变库
    changeWareHouse(index, val) {
      let unitNum = 1
      let attritionRate = 0
      let stock = 0
      let accuracy = 6
      this.materielTableData[index].result.forEach((item) => {
        if (item.attr === 'stock' && (val.num === 0 || val.num)) {
          item.attrValue = val.num
          stock = item.attrValue
        }
        if (item.attr === 'unitNum') {
          unitNum = item.attrValue // 标准用量
          accuracy = item.accuracy || 6
        }
        if (item.attr === 'attritionRate') {
          attritionRate = item.attrValue // 损耗率
        }
      })
      //  计算可生产数量
      const itemCanNum = (stock * (1 - attritionRate / 100)) / unitNum
      let plusNum = Number(itemCanNum.toFixed(accuracy))
      if (plusNum < 0) {
        plusNum = 0
      }
      this.compareArr[index] = plusNum
      this.simulationForm.canNum = Math.min.apply(null, this.compareArr)
    },

    // 开始模拟
    startSimulation() {
      this.loading = true
      this.afterSimulation = false
      this.simulationForm.canNum = 0 // 可生产数量初始为0
      this.compareArr = [] // 清空比较大小
      if (this.bomObj.id) {
        const params = {
          dataId: this.bomObj.id,
          saasMark: 1,
          businessType: 2601, // BOM单
          sourceBusinessType: 2701 // 模拟生产
        }
        simulationProduceBomBillProductList(params)
          .then((data) => {
            this.loading = false
            this.materielExplains = data.result.materiel.explains
            this.materielTableData = data.result.materiel.productList
            this.finishedProduct = data.result.finishedProduct.productList[0]
            this.warsehouseReduce()
            this.simulationForm.bomId.id = this.bomObj.id
            this.bomVersion = this.bomObj.version
            this.bomName = this.bomObj.name
            // 初始化比较数据
            this.compareArr = this.materielTableData.map((marItem) => {
              return 0
            })
          })
          .catch(() => {
            this.loading = false
          })
      }
    },
    // 获取所有物料的仓库的总集，并做去重，用于批量选择仓库的选项
    warsehouseReduce() {
      this.batchWarsehouse = null
      this.materielWarehouse = []
      let warsehouseMax = []
      // 将物料所有的仓库组合成一个数组
      for (let i = 0; i < this.materielTableData.length; i++) {
        const data = this.materielTableData[i]
        let materielWarehouse = []
        if (data.result) {
          for (let j = 0; j < data.result.length; j++) {
            const obj = data.result[j]
            if (obj.attr === 'warehouse') {
              this.materielWarehouse.push(obj.warehouseArray)
              materielWarehouse = obj.warehouseArray
              break
            }
          }
        }
        if (materielWarehouse.length > 0) {
          warsehouseMax = warsehouseMax.concat(materielWarehouse)
        }
      }
      // 以下根据仓库的id进行去重
      const mobj = {}
      warsehouseMax = warsehouseMax.reduce((item, next) => {
        ;(mobj[next.id] ? '' : (mobj[next.id] = true)) && item.push(next)
        return item
      }, [])
      this.warsehouseMax = warsehouseMax
    },
    // 一键生产
    oneKey() {
      this.simulationForm.materiel = this.proformData(this.materielTableData)
      if (!this.checkout) {
        this.checkout = false
        // let query = {
        //   moduleName: 'productionOrder'
        // }
        // let params = this.simulationForm
        // this.$router.push({
        //   name: 'jxcAddPage',
        //   query: query,
        //   params: params
        // })
        if (!this.simulationForm.canNum) {
          Message({
            type: 'error',
            message: this.$t('jxc.canNotProduct')
          })
          return false
        }
        const params = {
          dataId: this.simulationForm.bomId.id,
          materiel: this.simulationForm.materiel,
          canNum: this.simulationForm.canNum,
          businessType: 2701, // 模拟生产
          linkBusinessType: 2801 // 生产单
        }
        this.formSimulationProduce(params)
          .then(() => {
            // this.afterSimulation = true
            // this.bomName = ''
          })
          .catch(() => {})
      }
    },

    // 格式物料
    proformData(arrPro) {
      const productList = []
      let count = 0 // 判断仓库是否全选了
      let noWarehouseflag = false // 仓库未选提示
      for (let i = 0, len = arrPro.length; i < len; i++) {
        const proObj = {
          id: arrPro[i].id
        }
        arrPro[i].result.forEach((item) => {
          if (item.attr === 'stock') {
            proObj[item.attr] = item.attrValue
          }
          if (item.attr === 'warehouse') {
            if (item.attrValue) {
              proObj[item.attr] = item.attrValue
              count++
            } else {
              Message({
                type: 'error',
                message: this.$t('jxc.warehouse')
              })
              this.checkout = true
              noWarehouseflag = true
            }
          }
        })
        if (noWarehouseflag) {
          break
        }
        productList.push(proObj)
      }
      if (count === arrPro.length) {
        this.checkout = false
      }
      return productList
    }
  },
  components: {
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      ),
    LinkTable: linkTable
  },
  watch: {
    simulationForm: {
      handler() {
        console.log(88, this.simulationForm)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.item-unit {
  line-height: 14px;
  &__desc {
    font-size: 12px;
    color: $text-auxiliary;
  }
}
.simulation-produce {
  display: flex;
  flex-direction: column;
  height: 100%;
  .simulation-link-table {
    display: inline-block;
    width: $field-width;
    vertical-align: bottom;
  }
  .version {
    display: inline-block;
    width: 50px;
    height: 20px;
    margin: 0 8px 0 8px;
    font-size: 12px;
    line-height: 20px;
    color: rgba(254, 157, 79, 1);
    text-align: center;
    background-color: rgba(252, 242, 222, 1);
  }
  .hint {
    font-size: 12px;
    line-height: 16px;
    color: rgba(255, 124, 53, 1);
  }
  .children-head {
    position: relative;
    flex: 0 0 60px;
    height: 60px;
    font-size: 0;
    text-align: right;
    background-color: $base-white;
    .title {
      position: absolute;
      left: 20px;
      margin-left: 0px;
      font-size: 16px;
      line-height: 60px;
      color: $text-main;
    }
  }
  .commun-wrap {
    position: relative;
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    padding: 15px;
    margin: 10px;
    overflow: auto;
    background-color: $base-white;
    .field-out {
      .item-select {
        display: inline-block;
        width: 300px;
      }
      .simulated {
        display: inline-block;
      }
    }
    .simulation-table {
      flex: auto;
      overflow: auto;
      .tableclass {
        width: 100%;
        margin-top: 10px;
        border: 1px solid $neutral-color-3;
        .bom-text {
          padding-left: 10px;
        }
      }
      .tableclass tr td {
        padding: 3px 10px;
        padding: 10px;
        line-height: 30px;
        text-align: left;
        vertical-align: middle;
        border-right: 1px solid $neutral-color-3;
        border-bottom: 1px solid $neutral-color-3;
      }
      .tableclass tr td:first-of-type {
        width: 100px;
        background: $bg-blue;
      }
      .tableclass tr td:last-of-type {
        border-right: none;
      }
      .tableclass tr:last-of-type td {
        border-bottom: none;
      }
      .ed-product {
        display: flex;
        img {
          width: 80px;
          height: 80px;
          cursor: pointer;
        }
        .product-Info {
          width: 35%;
          padding-left: 15px;
        }
        .product-Num {
          color: $brand-color-5;
          .icon {
            color: $text-plain;
          }
          .unit-item__span {
            margin-left: 10px;
            font-size: 12px;
            color: $text-auxiliary;
          }
        }
      }
      .no-bom {
        color: $text-grey;
      }
    }
    //保存
    .btnGroup {
      padding: 34px 0 14px;
      text-align: center;
      // position: absolute;
      // bottom: 0;
      // left: 20px;
      // right: 20px;
      .save {
        padding: 8px 24px;
        margin-right: 15px;
        background: $brand-color-5;
      }
    }
  }
  .set-form {
    position: fixed;
    display: none;
    padding: 2px 8px;
    background-color: $base-white;
    border: solid 1px $neutral-color-3;
    border-radius: 2px;
    .el-input {
      display: inline-block;
      width: 120px;
    }
    .set-btn {
      display: inline-block;
      padding: 0 2px;
      font-size: 15px;
      cursor: pointer;
      &.blue {
        color: $link-base-color-6;
      }
      &.red {
        color: $error-base-color-6;
      }
    }
  }
}
// 设置批量样式
.el-table :deep(.tag-batch) {
  position: absolute;
  right: 20px;
  padding: 3px 6px;
  font-size: 12px;
  line-height: 1;
  color: $base-white;
  // top: 12px;
  cursor: pointer;
  background-color: $link-base-color-6;
  border: solid 1px $link-base-color-6;
  border-radius: 4px;
  &:hover {
    background-color: #4db3ff;
    border-color: #4db3ff;
  }
}
</style>
