<!--
 * @Description: 入库单
 -->
<template>
  <div class="in-warehouse list">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :custom-mode-config="{ viewConfig, viewButton }"
          :show-filter-list="specialFilter"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @getFormData="panelFilterParams"
        >
        </list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onTableView="onTableView"
          @oncalendarView="oncalendarView"
        >
        </component>
      </div>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'

export default {
  name: 'InWarehouse',

  mixins: [listMixin, listEditMixin],

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.initViewType({ params, api: this.formListApi.getInstockList })
    }
  }
}
</script>

<style lang="scss" scoped>
.in-warehouse {
  height: 100%;
}
</style>
