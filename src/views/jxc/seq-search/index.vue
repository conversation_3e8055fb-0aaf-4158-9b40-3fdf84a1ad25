<!--
 * @Description: 序列号查询页面
-->
<template>
  <div class="list seq-search">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :show-search="false"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      ></list-head>
      <div slot="layout-column" class="content">
        <list-panel
          ref="listPanel"
          :custom-mode-config="{ viewConfig, viewButton }"
          :show-filter-list="specialFilter"
          :show-filter-setting="false"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @getFormData="panelFilterParams"
        ></list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams,
            chooseCheckBox: false
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onTableView="onTableView"
          @oncalendarView="oncalendarView"
        >
        </component>
      </div>
    </v-top-bottom-page>
    <TrackLine ref="trackLine" :show.sync="visibleTrack" />
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'
import TrackLine from './components/track-line'

export default {
  name: 'SeqSearchList',

  components: {
    TrackLine
  },

  mixins: [listMixin, listEditMixin],

  data() {
    return {
      visibleTrack: false
    }
  },

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.initViewType({ params, api: this.formListApi.getProductSerialList })
    },
    // 跟踪记录
    previewLastFollow({ data }) {
      this.visibleTrack = true
      this.$refs.trackLine.init({
        dataId: data.id
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.seq-search {
  height: 100%;
}
</style>
