<!-- 文章预览页面 -->
<template>
  <page-layout class="material-lib-essay-preview">
    <template #header>
      <div class="material-lib-essay-preview__header">
        <h3>预览文章</h3>
        <el-button size="small" @click="changePreview"
          >切换{{ previewEnum.pc === pageRender.currentPreview ? '手机' : '电脑' }}模式</el-button
        >
      </div>
    </template>
    <template #content-body>
      <!-- 文章预览 -->
      <essay-preview :content="content" :type="pageRender.currentPreview"></essay-preview>
    </template>
  </page-layout>
</template>

<script>
import pageLayout from '@/views/scrm/components/layout/page-layout.vue'
import essayPreview from '../components/essay-preview'
// API
import { getEditEssay } from '@/api/scrm/customer-operation/material-lib.js'
// Enum
// import routerEnum from '@/constants/router-enum/index.js'
import { previewEnum } from '@/views/scrm/constants/common'

export default {
  name: 'MaterialLibEssayPreview',

  components: {
    PageLayout: pageLayout,
    EssayPreview: essayPreview
  },

  beforeRouteEnter(to, from, next) {
    next((vm) => Reflect.has(to.query, 'id') && vm.getEssayInfo(to.query.id))
  },

  data() {
    return {
      content: '',
      previewEnum,
      pageRender: {
        isPreview: false, // 是否开启预览
        currentPreview: previewEnum.pc
      }
    }
  },

  created() {},

  methods: {
    /**
     * @description: 改变预览
     */
    changePreview() {
      if (this.pageRender.currentPreview === previewEnum.pc) {
        this.pageRender.currentPreview = previewEnum.phone
      } else {
        this.pageRender.currentPreview = previewEnum.pc
      }
    },
    /**
     * @description: 获取个活码信息
     * @param {*}
     * @return {*}
     */
    getEssayInfo(id) {
      this.loading = true
      getEditEssay({ id })
        .then((res) => {
          // 格式化数据，回显
          const { essayMaterialPojo } = res
          this.content = essayMaterialPojo.content || ''
        })
        .catch((_) => {
          this.goBack()
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.material-lib-essay-preview {
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
  }
}
</style>
