<!-- 上传封面 -->
<template>
  <div v-loading="loading" class="upload-cover">
    <!-- 未选择图片 -->
    <template v-if="valueControl">
      <div class="upload-cover__preview">
        <img alt="" class="upload-cover__preview--img" :src="valueControl" />
        <div class="upload-cover__preview--del" @click="valueControl = ''">
          <i class="iconfont iconguanbi"></i>
          <span>删除</span>
        </div>
      </div>
    </template>
    <!-- 已选择图片 -->
    <template v-else>
      <div class="upload-cover__selector" @click="toSelectFile">
        <i class="web-icon-plus web-iconfont"></i>
        <span>上传图片</span>
      </div>
    </template>
    <!-- file -->
    <input
      ref="file"
      style="display: none; width: 0; height: 0"
      type="file"
      @change="toUploadFile"
    />
  </div>
</template>

<script>
// 引入oss上传utils
import { uploadImg, selectLocalFile } from '@/views/scrm/utils/upload.js'

export default {
  name: 'UploadCover',

  props: {
    value: {
      type: [String, Array, Object],
      required: true
    }
  },

  data() {
    return {
      loading: false
    }
  },

  computed: {
    valueControl: {
      set(val) {
        this.$emit('update:value', val)
      },
      get() {
        return this.value
      }
    }
  },

  created() {},

  methods: {
    /**
     * @description: 本地文件上传---统一处理
     */
    toSelectFile() {
      selectLocalFile({
        accept: '.png,.jpg,.jpeg',
        callback: (e) => {
          this.toUploadFile(e)
        }
      })
    },
    /**
     * @description: 本地上传图片
     * @param {Object} e
     */
    async toUploadFile(e) {
      this.loading = true
      // 获取本地文件
      const files = e.target.files
      const file = (files.length && files[0]) || ''
      // 文件is存在
      if (!file) return
      // 获取oss回调
      const fileTypeList = ['jpg', 'jpeg', 'png']
      const { status, data, description } = await uploadImg({ file, fileTypeList }).finally(() => {
        this.$refs.file.value = null
        this.loading = false
      })
      console.log('status, data, description', status, data, description)
      // 提示
      // 回调is成功
      // if (!status) return
      if (!status) {
        this.$message({
          type: 'error',
          message: description
        })
        return
      }
      //
      this.valueControl = data.filepath
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-cover {
  //   height: 172px;
  border: 1px dotted $neutral-color-3;
  @mixin makeCenter {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  &__preview,
  &__selector {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  &__preview {
    position: relative;
    overflow: hidden;
    text-align: center;
    background: $neutral-color-9;
    &--img {
      object-fit: cover;
      height: 100%;
    }
    & > img {
      // max-width: 323px;
      max-width: 100%;
      // max-width: 306px;
      // max-height: 172px;
    }
    &:hover {
      .upload-cover__preview--del {
        opacity: 0.75;
      }
    }
    &--del {
      @include makeCenter();
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      width: 100%;
      height: 100%;
      background: $neutral-color-9;
      opacity: 0;
      transition: all 0.25s ease-in;
      & > i,
      span {
        font-size: 22px;
        color: #ccc;
      }
      & > span {
        font-size: 12px;
      }
    }
  }
  &__selector {
    @include makeCenter();
    &:hover {
      opacity: 0.75;
    }
  }
}
</style>
