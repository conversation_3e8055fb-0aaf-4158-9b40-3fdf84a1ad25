<!-- 预览分享到朋友-->
<template>
  <div class="preview-friend">
    <div class="preview-friend__img">
      <img alt="当前无预览图" class="preview-friend__img--fill" :src="value.cover" />
    </div>
    <div class="preview-friend__text">
      <b class="preview-friend__text--title">{{ value.title }}</b>
      <section class="preview-friend__text--content">{{ value.summary }}</section>
    </div>
  </div>
</template>

<script>
import previewMixin from './preview.mixin'

export default {
  name: 'PreviewFriend',

  mixins: [previewMixin]
}
</script>

<style lang="scss" scoped>
.preview-friend {
  box-sizing: border-box;
  display: flex;
  padding: 12px;
  &__img {
    position: relative;
    box-sizing: border-box;
    flex: 0 0 auto;
    width: 67px;
    height: 67px;
    margin-right: 8px;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    &--fill {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
    img {
      max-width: 67px;
      // height: 67px;
    }
  }
  &__text {
    box-sizing: border-box;
    flex-grow: 1;
    width: 0;
    max-height: 148px;
    padding: 8px;
    overflow: hidden;
    background: $neutral-color-2;
    &--title {
      display: block;
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 17px;
      color: #666;
      word-break: break-all;
      @include singleline-ellipsis();
    }
    &--content {
      font-size: 12px;
      color: #999;
      word-break: break-all;
      @include multiline-ellipsis(2);
    }
  }
}
</style>
