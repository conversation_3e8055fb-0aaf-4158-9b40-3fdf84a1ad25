<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 导入员工信息
-->
<template>
  <div class="mobile-card-info-dialog">
    <el-dialog
      :title="$t('scrm.mobileCard.importTitle')"
      :visible="infoVisible"
      @close="closeDialog"
    >
      <div class="mobile-card-info-dialog__contant">
        <div>{{ $t('scrm.mobileCard.import') }}：</div>
        <file-upload
          :button-name="$t('scrm.mobileCard.uopload')"
          :xls-type="true"
          @fileAdd="fileAdd"
        ></file-upload>
        <span
          class="mobile-card-info-dialog__contant-upload-look"
          :title="$t('scrm.mobileCard.mode')"
          @click="imgDialogVisible = true"
        >
          {{ $t('scrm.mobileCard.exampleImg') }}
        </span>
        <span class="mobile-card-info-dialog__contant-upload-temp" @click="fileDownload">{{
          $t('scrm.mobileCard.downloadMode')
        }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="mobile-card-info-dialog__button" @click="closeDialog">{{
          $t('operation.cancel')
        }}</el-button>
        <el-button class="mobile-card-info-dialog__button" type="primary" @click="updateInfo">{{
          $t('operation.confirm')
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 查看示例图 -->
    <el-dialog :title="$t('scrm.mobileCard.mode')" :visible.sync="imgDialogVisible">
      <img class="mobile-card-info-dialog__img" src="@/assets/scrm/mobile-card/mobile-card.png" />
      <span slot="footer" class="dialog-footer">
        <el-button
          class="mobile-card-info-dialog__button"
          type="primary"
          @click="imgDialogVisible = false"
          >{{ $t('operation.iknow') }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import FileUpload from '@/views/scrm/components/img/file-upload'
import { scrmDownloadTemplate, importExcel } from '@/api/scrm/customer-getting/mobile-card.js'

export default {
  name: 'MobileCardInfoDialog',
  components: {
    FileUpload
  },
  props: {
    infoVisible: {
      type: Boolean,
      default: false
    },
    steps: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imgDialogVisible: false,
      userInfoExcelUrl: '',
      fileList: []
    }
  },
  created() {},
  methods: {
    // 上传文件回调
    fileAdd(e) {
      this.userInfoExcelUrl = e[e.length - 1].url
      this.fileList.push(e.pop())
    },
    // 名片文件下载
    fileDownload() {
      const fileName = '移动名片模板.xlsx'
      if (utils.isMacWX()) {
        utils.WXLoadFile(fileName, 'scrmDownloadTemplate')
        return
      }
      scrmDownloadTemplate().then((res) => {
        utils.filePostDownload(fileName, res)
      })
    },
    // 提交文件
    updateInfo() {
      const data = {
        userInfoExcelUrl: this.userInfoExcelUrl
      }
      importExcel(data).then((res) => {
        this.$message({
          message: this.$t('scrm.mobileCard.importSuccess'),
          type: 'success'
        })
        this.closeDialog()
      })
    },
    closeDialog() {
      this.$emit('closeInfo')
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-card-info-dialog {
  background: white;
  &__contant {
    position: relative;
    display: flex;
    width: 420px;
    height: 68px;
    margin: 0 auto;
    line-height: 32px;
    &-upload {
      display: flex;
      &-look,
      &-temp {
        position: absolute;
        // display: inline-block;
        margin-left: 20px;
        color: $brand-color-5;
        cursor: pointer;
      }
      &-look {
        left: 210px;
      }
      &-temp {
        left: 300px;
      }
    }
  }
  &__foot {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
  }
  &__button {
    height: 32px;
    padding: 5px 16px;
  }
  &__img {
    display: block;
    width: 100%;
    margin: 0 auto;
  }
}
</style>
