<template>
  <div class="frame-drawer">
    <el-drawer :show-close="false" :size="size" :visible.sync="visibleControl" :with-header="false">
      <div class="drawer-close" @click="visibleControl = false">
        <i class="el-icon-close"></i>
      </div>
      <!-- 滚动盒子 -->
      <el-scrollbar v-if="$slots['content-scroll']" v-loading="loading" :style="{ width: size }">
        <slot v-show="!loading" name="content-scroll"></slot>
      </el-scrollbar>
      <!-- 非滚动盒子 -->
      <div v-else v-loading="loading" class="frame-drawer__normal" :style="{ width: size }">
        <slot v-show="!loading" name="content"></slot>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'DrawerFrame',

  props: {
    // 组件是否可见
    visible: {
      type: Boolean,
      required: true,
      default: false
    },
    size: {
      type: String,
      default: '970px'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    visibleControl: {
      set(val) {
        this.$emit('update:visible', val)
      },
      get() {
        return this.visible
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.frame-drawer {
  position: relative;
  // close
  .drawer-close {
    // top: 60px;
    // right: 970px;
    position: absolute;
    top: 0px;
    left: -51px;
    box-sizing: border-box;
    width: 50px;
    height: 50px;
    font-size: 20px;
    line-height: 50px;
    text-align: center;
    cursor: pointer;
    background: #fff;

    &:hover {
      color: $brand-color-5;
    }
  }

  // 滚动盒子
  .el-scrollbar {
    width: 970px;
    // height: calc(100% - 60px);
    // height: calc(100% + 5px);
    height: 100%;
    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }
  }
  // 非滚动盒子
  &__normal {
    position: relative;
    // height: 100vh;
    height: calc(100% - 60px);
    // height: 100%;
  }
}
</style>

<style lang="scss">
.frame-drawer {
  .el-drawer__container {
    top: 60px !important;
    height: calc(100vh - 60px) !important;
    .el-drawer__body {
      height: 100%;
    }
    .el-drawer {
      overflow: unset !important;
    }
  }
}
</style>
