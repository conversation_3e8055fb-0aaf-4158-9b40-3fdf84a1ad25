<template>
  <div class="operation-function-setting">
    <el-form ref="form" label-width="152px" :model="form" size="small">
      <!-- 1.自动通过好友 -->
      <!-- <el-form-item label="自动通过好友:">
        <el-switch v-model="form.skipVerifyEnable"></el-switch>
      </el-form-item> -->
      <!-- 1.1.开启时间： -->
      <el-form-item v-show="form.skipVerifyEnable" :label="$t('scrm.selfCode.autoSupport') + ':'">
        <!-- 选择【skipTimeType】 -->
        <el-radio-group v-model="form.skipTimeType">
          <el-radio
            v-for="radio in formRender.skipTimeType"
            :key="radio.value"
            :label="radio.value"
          >
            {{ radio.label }}
          </el-radio>
        </el-radio-group>
        <!-- 选择时间段 -->
        <section
          v-show="form.skipTimeType === FunctionSettingOpenTime.during"
          class="form-item-padding"
        >
          <el-time-picker
            v-model="form.skipTime"
            :end-placeholder="$t('label.endWhen')"
            is-range
            :picker-options="{
              format: 'HH:mm'
            }"
            :placeholder="$t('otherSetting.chooseTimeRange')"
            :range-separator="$t('unit.to')"
            :start-placeholder="$t('label.startWhen')"
            value-format="timestamp"
          >
          </el-time-picker>
          {{ $t('scrm.selfCode.noticedPermitAutoAdd') }}
          <!-- 若不在开启「自动通过好友」时间段添加的好友，需要客服手动通过好友验证后才可添加 -->
        </section>
      </el-form-item>
      <!-- 2.二维码预览 -->
      <el-form-item :label="$t('scrm.selfCode.codePreview')">
        <section class="code-prereview">
          <div class="code-prereview__left">
            <img v-show="form.avatar" alt="" :src="form.avatar" />
          </div>
          <div class="code-prereview__right">
            <span class="code-prereview__right--noticed">{{
              $t('scrm.selfCode.noticedPreview')
            }}</span>
            <span class="code-prereview__right--ext"
              >{{ $t('scrm.selfCode.supportedExt') }}：jpg、png</span
            >
            <el-button @click="selectLogo">{{ $t('scrm.selfCode.uploadAvatar') }}</el-button>
          </div>
        </section>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
// import imgUpload from '@/components/img/img-upload'
import uploadMix from '@/mixin/uploadMixin'
import { FunctionSettingOpenTime } from '../constants.js'
import operationForMxixin from '../operation.form.mixin.js'
import { selectLocalFile, uploadImg } from '@/views/scrm/utils/upload.js'

export default {
  name: 'OperationFunctionSetting',

  components: {
    // imgUpload
  },

  mixins: [operationForMxixin, uploadMix],

  data() {
    return {
      imgAccept: 'image/jpeg, image/png',
      images: [],
      // 枚举
      FunctionSettingOpenTime,
      // 页面渲染
      formRender: {
        skipTimeType: [
          { label: this.$t('scrm.selfCode.openAllDay'), value: FunctionSettingOpenTime.all },
          {
            label: this.$t('scrm.selfCode.selectDuringTime'),
            value: FunctionSettingOpenTime.during
          }
        ]
      }
    }
  },

  methods: {
    selectLogo() {
      selectLocalFile({
        accept: '.jpg,.png',
        callback: (e) => {
          this.onFileUpload(e)
        }
      })
    },
    /**
     * @description: 本地上传视频
     * @param {Object} e
     */
    async onFileUpload(e) {
      // 获取本地文件
      const files = e.target.files
      // 文件is存在
      if (!files.length) return
      const promiseArr = []
      for (let i = 0; i < files.length; i++) {
        promiseArr.push(uploadImg({ file: files[i], sizeLimit: 2, fileTypeList: ['jpg', 'png'] }))
      }
      const dataArr = await Promise.all(promiseArr)
      const dataList = dataArr
        .filter((item) => {
          if (!item.status) {
            this.$message.error(item.description)
          }
          return item.status
        })
        .map((item) => {
          return {
            url: item.data.filepath,
            content: item.data.filepath,
            // url: 'https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/7a2e31bf18e8fb5abd2a32ea93f0d46b.mp4',
            // content: 'https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/7a2e31bf18e8fb5abd2a32ea93f0d46b.mp4',
            name: item.data.fileName,
            size: item.data.fileSize,
            ext: item.data.suffix
          }
        })
      if (!dataList.length) return
      console.log('🚀 ~ file: index.vue:140 ~ onFileUpload ~ dataList:', dataList)
      this.imgAdd(dataList)
    },
    // 移除图片
    imgRemove(file) {
      if (file) {
        this.form.avatar = ''
      }
    },
    imgAdd(fileList) {
      const path = `${fileList[0].url}?x-oss-process=image/resize,m_fixed,h_80,w_80`
      this.form.avatar = path
    }
  }
}
</script>

<style lang="scss" scoped>
.operation-function-setting {
  padding: 24px 24px 24px 0;
  color: $text-main;
  // 行间距
  .form-item-padding {
    padding-top: 13px;
  }
  // :deep(.el-form-item__label) {
  //   box-sizing: border-box;
  //   // padding-left: 32px;
  //   text-align: left !important;
  //   padding-right: 32px;
  //   // text-align: right;
  // }
  // 二维码预览
  .code-prereview {
    display: flex;
    &__left {
      position: relative;
      box-sizing: border-box;
      width: 128px;
      height: 128px;
      margin-right: 12px;
      background-image: url('../../../../../../../assets/scrm/dynamic-code/default-code.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border: 1px solid $neutral-color-3;
      // 头像预览
      & > img {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 40px;
        height: 40px;
        transform: translate(-50%, -50%);
      }
    }
    &__right {
      display: flex;
      flex-direction: column-reverse;

      &--noticed,
      &--ext {
        font-size: 14px;
        line-height: 22px;
      }

      &--noticed {
        color: $text-main;
      }

      &--ext {
        margin: 8px 0 14px;
        color: $text-main;
      }
    }
  }
}
</style>

<style lang="scss">
.operation-function-setting {
  .el-form-item__label {
    box-sizing: border-box;
    padding: 0 12px 0 24px;
    text-align: left !important;
  }
}
</style>
