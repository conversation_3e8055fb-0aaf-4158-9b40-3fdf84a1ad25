<!--
 * @Description: 基础组件-radio-box
-->
<template>
  <div class="basic-checkbox">
    {{ label }}：<el-checkbox v-model="valueController"></el-checkbox>
  </div>
</template>

<script>
export default {
  name: 'BasicCheckbox',

  props: {
    value: {
      type: Boolean,
      required: true,
      default: false
    },
    label: {
      type: String,
      required: true,
      default: '请选择'
    }
  },

  computed: {
    valueController: {
      set(val) {
        this.$emit('update:value', val)
      },
      get() {
        return this.value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-checkbox {
  margin-bottom: 20px;
}
</style>
