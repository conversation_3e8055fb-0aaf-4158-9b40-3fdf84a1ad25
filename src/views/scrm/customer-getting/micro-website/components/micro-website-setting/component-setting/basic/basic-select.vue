<!--
 * @Description: 基础组件-
-->
<template>
  <el-select v-model="valueController" class="basic-select" placeholder="请选择">
    <el-option
      v-for="item in list"
      :key="item[valueAlias]"
      :label="item[textAlias]"
      :value="item[valueAlias]"
    >
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'BasicSelect',

  props: {
    // 选中项
    value: {
      type: [String, Number],
      required: true
    },
    valueAlias: {
      type: String,
      default: 'value'
    },
    textAlias: {
      type: String,
      default: 'text'
    },
    list: {
      type: Array,
      required: true,
      default: () => [
        // { text:'text', value: 'value'}
      ]
    }
  },

  data() {
    return {}
  },

  computed: {
    valueController: {
      set(val) {
        this.$emit('update:value', val)
      },
      get() {
        return this.value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-select {
  width: 100%;
  padding-bottom: 24px;
}
</style>
