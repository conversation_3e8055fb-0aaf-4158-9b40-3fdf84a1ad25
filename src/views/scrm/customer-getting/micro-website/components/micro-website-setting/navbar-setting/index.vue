<!--
 * @Description: 微官网编辑页面-右侧底部导航切换组件
-->
<template>
  <div class="navbar-setting">
    <div class="navbar-setting__title">底部导航</div>
    <div class="navbar-setting__list">
      <el-radio-group :value="currentIndexController">
        <el-radio
          v-for="(item, index) in tabListController"
          :key="index"
          :label="item.index"
          @change="changeIndex($event, item.index)"
        >
          <i :class="`iconfont ${item.icon}`"></i>
          <p>{{ item.name }}</p>
          <el-dropdown
            class="operate"
            placement="bottom"
            @command="handleCommand($event, index)"
            @visible-change="dropdownChange($event, index)"
          >
            <span>
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="edit">编辑</el-dropdown-item>
              <el-dropdown-item v-if="tabListController.length !== 1" command="delete"
                >删除</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </el-radio>
      </el-radio-group>
    </div>
    <div :class="'navbar-setting__footer ' + addDisbled" @click="addNav">
      <i class="el-icon-plus"></i> 新建({{ tabListController.length }}/{{ TAB_MAX_LENGTH }})
    </div>
    <AddNavDialog
      v-if="addNavVisible"
      :is-active="addNavVisible"
      :nav-item="navItem"
      :title-name="operateTitle + '页面'"
      @add-nav-confirm="addNavConfirm"
      @dialog-cancel="addNavVisible = false"
    ></AddNavDialog>
  </div>
</template>

<script>
import AddNavDialog from './add-nav-dialog.vue'
import mixinValidate from '../mixin-validate'
import { ENUM_OPERATE_TYPE } from '@/views/scrm/constants/enum.operation'
import { TAB_MAX_LENGTH } from '../../../enum.micro-website.js'

export default {
  name: 'NavbarSetting',
  components: {
    AddNavDialog
  },

  mixins: [mixinValidate],

  props: {
    tabList: {
      type: Array
    },
    pageList: {
      type: Array
    },
    currentIndex: {
      type: Number,
      default: 0
    },
    previewSelectInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },

  data() {
    return {
      TAB_MAX_LENGTH,
      operateIndex: 0, // 当前操作的导航
      addNavVisible: false, // 新增nav弹框
      operateTitle: '添加'
    }
  },

  computed: {
    // 底部导航等于4个禁用添加按钮
    addDisbled() {
      return this.tabListController.length === TAB_MAX_LENGTH ? 'add-disabled' : ''
    },
    // 新建/编辑时addNavVisible的回显
    navItem() {
      return this.operateIndex === -1
        ? { name: '', icon: '' }
        : this.tabListController[this.operateIndex]
    },
    tabListController: {
      set(val) {
        this.$emit('update:tabList', val)
      },
      get() {
        return this.tabList
      }
    },
    pageListController: {
      set(val) {
        this.$emit('update:pageList', val)
      },
      get() {
        return this.pageList
      }
    },
    currentIndexController: {
      set(val) {
        this.$emit('update:currentIndex', val)
      },
      get() {
        return this.currentIndex
      }
    }
  },

  watch: {
    tabListController(newVal) {
      this.tabListController.forEach((item, index) => {
        item.index = index
      })
    }
  },

  methods: {
    // 添加nav
    addNav() {
      if (this.tabListController.length === 4) return
      this.operateIndex = -1
      this.addNavVisible = true
      this.operateTitle = '添加'
    },
    // 确认新增
    addNavConfirm(data) {
      const { name, icon } = data
      if (this.operateIndex === -1) {
        // 新增
        const item = { name, icon, index: this.tabListController.length - 1 }
        this.tabListController.push(item)
        this.pageListController.push([])
      } else {
        // 编辑
        this.tabListController[this.operateIndex].name = name
        this.tabListController[this.operateIndex].icon = icon
      }
      this.addNavVisible = false
    },
    // 操作按钮下拉菜单显隐控制操作按钮显隐
    dropdownChange(val, index) {
      const operateBtn = document.getElementsByClassName('operate')[index]
      operateBtn.style.display = val ? 'block' : ''
    },
    // 执行nav的编辑/删除指令的分发入口
    handleCommand(val, index) {
      if (val === ENUM_OPERATE_TYPE.EDIT) {
        // 编辑
        this.operateIndex = index
        this.addNavVisible = true
        this.operateTitle = '编辑'
      } else {
        // 删除
        // })
        const name = this.tabListController[index].name
        const h = this.$createElement
        this.$msgbox({
          title: '删除页面',
          message: h('p', null, [
            h(
              'i',
              {
                class: 'el-icon-warning',
                style: 'color: rgb(250, 173, 20);font-size: 22px;margin-right:6px'
              },
              ''
            ),
            h('span', { style: 'font-weight: 800' }, `确认删除页面"${name}"吗?`),
            h(
              'p',
              { style: 'color: #999;margin-left: 28px' },
              '删除后，页面配置的所有内容将被清空，且无法恢复'
            )
          ]),
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              this.tabListController.splice(index, 1)
              this.pageListController.splice(index, 1)
              // 如果删除的是当前高亮的 删除后进入第一个页面的编辑状态
              if (index < this.currentIndexController) {
                this.currentIndexController -= 1
              } else if (index === this.currentIndexController) {
                this.$emit('callback')
                this.currentIndexController = 0
              }
              done()
            } else {
              done()
            }
          }
        })
      }
    },
    //切换时，校验
    changeIndex(e, value) {
      if (this.commonValidate(this.previewSelectInfo.data)) {
        return
      } else {
        this.$emit('callback')
        this.currentIndexController = value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar-setting {
  @mixin lineHeight($lineHeight: 64px) {
    width: 100%;
    height: $lineHeight;
    line-height: $lineHeight;
    text-align: center;
  }
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  &__title {
    @include lineHeight;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 1px solid $neutral-color-3;
  }
  &__list {
    .el-radio__inner {
      display: none;
    }
  }
  &__footer {
    @include lineHeight(70px);
    position: absolute;
    bottom: 0;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    border-top: 1px solid $neutral-color-3;
    &:hover {
      color: $brand-color-5;
    }
  }
  .add-disabled {
    color: rgba(102, 102, 102, 0.5);
    cursor: not-allowed;
  }
}
</style>

<style lang="scss">
.navbar-setting {
  &__list {
    .el-radio-group {
      width: 100%;
      .el-radio__label {
        width: 100%;
        padding: 0;
      }
      .el-radio__inner {
        display: none;
      }
      .el-radio {
        position: relative;
        display: flex;
        flex-direction: columns;
        align-items: center;
        width: 100%;
        height: 90px;
        margin: 0;
        font-size: 14px;
        text-align: center;
        .el-dropdown {
          position: absolute;
          top: 34px;
          right: 10px;
          display: none;
          font-size: 12px;
          transform: rotate(90deg);
        }
        i {
          font-size: 12px;
        }
        p {
          margin-top: 6px;
        }
        &:hover {
          .el-dropdown {
            display: block;
          }
          background: $neutral-color-1;
        }
      }
      .is-checked {
        background: rgba(255, 142, 61, 0.1) !important;
      }
    }
  }
}
</style>
