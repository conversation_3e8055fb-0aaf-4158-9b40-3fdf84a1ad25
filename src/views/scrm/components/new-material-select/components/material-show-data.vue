<template>
  <div class="material-show-data">
    <div
      v-for="(items, indexs) in dataList"
      :key="'type' + indexs"
      class="material-show-data__list"
    >
      <span class="material-show-data__list--label">
        <span class="material-show-data__label--name"> {{ items.name }}</span>
        <span class="material-show-data__label--number"> ({{ items.length }}) </span>
        <span>:</span>
      </span>
      <div class="material-show-data__list--tags" @click="deleteData(items.type, $event)">
        <div
          v-for="(item, index) in items.list"
          :key="items.type + index"
          class="material-show-data__item"
        >
          <el-tooltip :content="item.name" :disabled="nameShow" effect="dark" placement="top-start">
            <div class="material-show-data__item--name">
              <span @mouseenter="nameWidth">
                {{ item.name }}
              </span>
            </div>
          </el-tooltip>
          <i
            class="material-show-data__item--icon web-icon-tishi_guanbi web-iconfont"
            :data-index="index"
          ></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MaterialShowData',
  components: {},
  props: {
    dataList: {
      // 渲染数据
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      nameShow: true
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 插槽与父组件交互事件，type:删除的类型，index：删除的索引值，type由父组件传入，直接用父组件传入的type就行
    deleteData(type, event) {
      if (event.target.dataset.index) {
        this.$emit('deleteData', type, parseInt(event.target.dataset.index))
      }
    },
    // 动态展示文字提示
    nameWidth(event) {
      const e = event || window.event
      this.nameShow = e.target.offsetWidth <= e.target.parentNode.offsetWidth
    }
  }
}
</script>

<style scoped lang="scss">
.material-show-data {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 17px 69px 17px 24px;
  overflow: auto;
  &__list {
    display: flex;
    align-items: flex-start;
    width: 100%;
    overflow-x: hidden;
    &--label {
      flex-shrink: 0;
      width: 85px;
      height: 22px;
      font-size: 14px;
      line-height: 22px;
      color: $text-plain;
    }
    &--tags {
      display: flex;
      flex-grow: 1;
      flex-wrap: wrap;
    }
  }
  &__label {
    &--number {
      margin: 0 3px;
    }
  }
  &__item {
    box-sizing: border-box;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    max-width: 150px;
    height: 22px;
    padding: 0 8px;
    margin-right: 8px;
    margin-bottom: 8px;
    overflow: hidden;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    &--name {
      flex-grow: 1;
      height: 20px;
      overflow: hidden;
      line-height: 20px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &--icon {
      height: 20px;
      margin-left: 5px;
      font-size: 12px;
      line-height: 20px;
      cursor: pointer;
      transform: scale(0.8);
      &:hover {
        // background-color: rgba($color: #000000, $alpha: 0.4);
        border-radius: 50%;
      }
    }
  }
}
</style>
