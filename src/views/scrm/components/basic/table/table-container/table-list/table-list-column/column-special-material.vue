<template>
  <div class="column-special-material">
    <template v-if="[1, 5].includes(dataInfo.type)">
      <el-image
        alt=""
        class="column-special-material__image"
        :preview-src-list="[dataInfo.content]"
        size="24"
        :src="dataInfo.content"
      ></el-image>
    </template>
    <template v-else>
      {{ dataInfo.content }}
    </template>
  </div>
</template>

<script>
import columnPublicMixin from './column-public-mixin'

export default {
  name: 'ColumnSpecialMaterial',

  mixins: [columnPublicMixin],

  data() {
    return {}
  },

  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.column-special-material {
  &__image {
    width: 24px;
    height: 24px;
  }
}
</style>
