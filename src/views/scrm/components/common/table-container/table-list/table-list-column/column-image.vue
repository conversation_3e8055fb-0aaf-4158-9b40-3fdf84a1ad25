<template>
  <!-- TODO: 考虑重构此组件 宗宝 浩浩 -->
  <div class="column-image">
    <!-- 图像type标识(头像右下角企微和微信图标) -->
    <div v-if="typeof dataInfo !== 'number' && !isStrDataInfo" class="type-icon">
      <img
        v-if="dataInfo.statusCode === 0"
        class="type-icon__style"
        :src="getImgTypeIcon(dataInfo.type)"
      />
      <i v-else class="type-icon__style" :class="getImgTypeIcon(dataInfo.type, 'icon')" />
    </div>
    <!-- dataInfo是type 渲染企微/微信图标 点击打开对话 -->
    <div v-if="dataInfoIsType">
      <i
        v-if="dataRowInfo.showType === 0"
        class="disabled-icon"
        :class="getDisabledIcon(dataInfo)"
      ></i>
      <el-image v-else :src="getImgTypeIcon(dataInfo)" @click="openChat"> </el-image>
    </div>
    <!-- 图片渲染 -->
    <el-image v-else alt="" :src="imageMini" @click="isPreviewShow = true">
      <div slot="error" class="image-error-slot">
        <i class="el-icon-picture-outline"></i>
      </div>
    </el-image>
    <!-- xbb图片预览组件 -->
    <lg-preview
      v-if="isPreviewShow"
      :list="imagePreview"
      @close="isPreviewShow = false"
    ></lg-preview>
  </div>
</template>

<script>
import columnPublicMixin from './column-public-mixin'
// utils
import xbb from '@xbb/xbb-utils'
import {
  getIconByIdentity as getImgTypeIcon,
  getDisabledIconByIdentity as getDisabledIcon
} from '@/views/scrm/utils/business.js'

export default {
  name: 'ColumnImage',

  components: {
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },

  mixins: [columnPublicMixin],

  data() {
    return {
      isPreviewShow: false
    }
  },

  computed: {
    // 判断图片是普通字符串or对象格式{ url: '', type: '' }
    isStrDataInfo() {
      return typeof this.dataInfo === 'string'
    },
    // 判断dataInfo是否是type
    dataInfoIsType() {
      return typeof this.dataInfo === 'number'
    },
    // 图片可能是字符串or对象格式{ url: '', type: '' }
    dataInfoComputed() {
      return this.isStrDataInfo ? this.dataInfo : this.dataInfo.url
    },
    // 判断图片有没有后缀(企微图片没有后缀)
    hasImageSuffix() {
      return /\S{1,}\/\S{1,}\.\w{1,}$/g.test(this.dataInfoComputed)
    },
    // 预览原图片
    imagePreview() {
      // 兼容微信图片（没有后缀）
      return this.hasImageSuffix ? [this.dataInfoComputed] : [`${this.dataInfoComputed}.png`]
    },
    // 列表缩略图片
    imageMini() {
      // 兼容微信图片（没有后缀）
      return this.hasImageSuffix ? xbb.thumbnail(this.dataInfoComputed, 24) : this.dataInfoComputed
    }
  },

  methods: {
    getImgTypeIcon,
    getDisabledIcon,
    openChat() {
      console.log('打开对话', this.dataInfo, this.dataRowInfo.externalUserId, 'xx')
      const externalUserId = this.dataRowInfo.externalUserId || ''
      if (!externalUserId) {
        this.$message.warning('对话打开失败，未查询到好友信息')
        return
      }
      const data = {
        userIds: '',
        externalUserIds: externalUserId, // 参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
        groupName: '', // 会话名称。单聊时该参数传入空字符串""即可。
        chatId: ''
      }
      utils.openEnterpriseChat(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.column-image {
  position: relative;
  width: 24px;
  height: 24px;
  // margin: 0 auto;
  // 图像type标识
  .type-icon {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 14px;
    height: 14px;
    line-height: 14px;
    transform: translateX(50%);
    &__style {
      width: 100%;
      color: $neutral-color-4;
      background: $base-white;
      border: none;
      border-radius: 50%;
    }
    .web-iconfont {
      width: auto !important;
      font-size: 15px;
    }
  }
  .el-image {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .disabled-icon {
    font-size: 26px;
    color: $neutral-color-4;
    cursor: not-allowed;
  }
}
</style>
