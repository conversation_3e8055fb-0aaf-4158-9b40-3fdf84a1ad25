<template>
  <span class="column-combo">
    {{ dataInfoCompt }}
  </span>
</template>

<script>
import columnPublicMixin from './column-public-mixin'

export default {
  name: 'ColumnCombo',

  mixins: [columnPublicMixin],

  computed: {
    dataInfoCompt() {
      if (!this.dataInfo || !this.headColumnInfo.items) return
      const dataInfo = this.headColumnInfo.items.find((item) => item.value === this.dataInfo) || {}
      return dataInfo.text || '-'
    }
  }
}
</script>

<style lang="scss" scoped></style>
