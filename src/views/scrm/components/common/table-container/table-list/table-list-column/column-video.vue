<template>
  <img
    class="column-video"
    :src="dataRowInfo.data.videoThumbUrl"
    @click="put(dataRowInfo, headColumnInfo)"
  />
</template>

<script>
import columnPublicMixin from './column-public-mixin'
// 列表项操作枚举
import { OperationEnum } from './table-list-column.enum.js'

export default {
  name: 'ColumnVideo',

  mixins: [columnPublicMixin],

  data() {
    return {}
  },

  methods: {
    /**
     * @description: 列表行的操作
     * @param {Object} data: 当前一行数据
     * @param {Object} head: 当前操作表头
     */
    put(data, head) {
      this.$emit('onColumnOperation', OperationEnum.dialog, data, head)
    }
  }
}
</script>

<style lang="scss" scoped>
.column-video {
  width: 24px;
  height: 24px;
  cursor: pointer;
}
</style>
