<template>
  <div class="tag-selection-box">
    <!-- 标签容器加入插槽功能方便展示不同样式，在setBoxHeight函数加了兼容 -->
    <slot name="container">
      <!-- 标签容器默认样式 -->
      <div ref="tagContainer" class="tag-selection-box__start-tags">
        <el-scrollbar :style="{ height: tagContainerHeight }" view-style="height: 100%">
          <span v-show="!data.length" class="tag-selection-box__start-tags--placeholder">{{
            tagContainerText
          }}</span>
          <section v-show="data.length" ref="tagList" class="tag-selection-box__start-tags--list">
            <v-tag
              v-for="tag in data"
              :key="tag.id"
              closable
              :color="tag.color"
              disable-transitions
              @close="cancelSelectedTags(tag)"
            >
              <span v-html="addNodeByWX(tag.name)"></span>
            </v-tag>
          </section>
        </el-scrollbar>
      </div>
    </slot>
    <!-- 按钮样式加入插槽，切记使用插槽时给该组件一个ref调用openSelectTagDialog方法 -->
    <slot name="button">
      <!-- 选择标签按钮 -->
      <el-button
        class="tag-selection-box__start-tags--select-btn"
        icon="el-icon-plus"
        type="text"
        @click="openSelectTagDialog"
      >
        {{ btnName }}
      </el-button>
    </slot>
    <!-- 选择启动标签 -->
    <!-- <person-select
      v-if="selectTagDialog && type === 'person'"
      :active-tab-name="'user'"
      :business-type="businessType"
      :default-val.sync="dataControl"
      :dialog-visible.sync="selectTagDialog"
      :is-contact-user="isContactUser"
      :multiple="multiple"
      :show-tabs-name="showTabsName"
      :title="$t('appLayout.selectStaff')"
    >
    </person-select> -->
    <!--  -->
    <tag-data-dialog
      v-if="selectTagDialog"
      :business-type="businessType"
      :extra-params="extraParams"
      :is-scrm="true"
      :select-tag-list="dataControl"
      :show.sync="selectTagDialog"
      @dialogSubmit="getTagSelect"
    ></tag-data-dialog>
    <!-- @dialogSubmit= -->
  </div>
</template>

<script>
// import tagSelect from './index'
// import personSelect from '@/components/person/person-select.vue'
import PersonSelect from '@/components/person-select/index'
import tagDataDialog from '@/components/all-fields/form-data-edit/tag-tabs/tag-data-dialog'
// utils
import VTag from '@/components/base/v-tag.vue'

export default {
  name: 'TagSelectionBox',

  components: {
    // tagSelect
    TagDataDialog: tagDataDialog,
    VTag
  },

  props: {
    // 弹框dialog
    dialogTitle: {
      type: String,
      default: '请选择标签'
    },
    // 添加标签的btn名称
    btnName: {
      type: String,
      default: '选择'
    },
    // 标签容器未添加tags的提示文案
    tagContainerText: {
      type: String,
      default: '添加标签'
    },
    // 渲染的类别tag/person
    type: {
      type: String,
      default: 'person'
    },
    // tag 业务
    businessType: {
      type: [String, Number],
      default: ''
    },
    data: {
      type: Array,
      required: true,
      default: function () {
        return []
      }
    },
    // 限制容器高度
    container: {
      type: Object,
      default: () => {
        return {
          maxNum: 3,
          minNum: 3
        }
      }
    },
    multiple: {
      type: Object,
      default: () => ({ prd: true })
    },
    showTabsName: {
      type: Array,
      // required: true,
      default: function () {
        return ['user']
      }
    },
    // isContactUser 是否企微收费的
    isContactUser: {
      type: Boolean,
      default: false
    },
    // extra params 额外参数
    extraParams: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      selectTagDialog: false, // 选择标签dialog
      tagContainerHeight: 0
    }
  },

  computed: {
    PersonSelect() {
      let res = []
      const arr = this.dataControl || []
      res = arr.map((item) => {
        return {
          id: item.id, // id
          name: item.name, // 显示的label
          property: 'user' // 类型:user、dept、role、manager
        }
      })
      return res
    },
    dataControl: {
      set(val) {
        this.$emit('update:data', val)
      },
      get() {
        return this.data
      }
    }
  },

  watch: {
    data: {
      handler(newVal) {
        this.$nextTick(() => {
          this.setBoxHeight(this.container.minNum, this.container.maxNum, newVal)
        })
      },
      deep: true
    }
  },

  mounted() {
    this.setBoxHeight(this.container.minNum, this.container.maxNum, this.data.data)
  },

  methods: {
    // 获取标签
    getTagSelect(tags) {
      console.log('val', tags)
      this.dataControl = tags
    },
    /**
     * @description: 点击选择标签
     * @param {*}
     * @return {*}
     */
    openSelectTagDialog() {
      // 打开dialog
      if (this.type === 'tag') {
        this.selectTagDialog = true
        return
      }
      PersonSelect({
        activeTabName: 'user',
        showTabsName: this.showTabsName,
        isContactUser: this.isContactUser,
        defaultVal: this.dataControl,
        multiple: {
          user: true,
          dept: true
        },
        tabMultiple: true
      }).then((res) => {
        if (res.data) {
          this.dataControl = res.data
        }
      })
    },

    /**
     * @description:取消选择的启动标签
     * @param {*}
     * @return {*}
     */
    cancelSelectedTags(del) {
      const index = this.dataControl.findIndex((item) => {
        // return item.value === del.value && item.target === del.target
        return item.id === del.id
      })
      this.dataControl.splice(index, 1)
    },

    /**
     * @description:获取添加的标签id(value)
     * @param {*}
     * @return {*}
     */
    getSelectedTags(val) {
      this.dataControl = val
    },

    /**
     * @description: 自适应高度
     * @param {*} minNum
     * @param {*} maxNum
     * @param {*}
     * @return {*}
     */
    setBoxHeight(minNum, maxNum, renderData = []) {
      if (!this.$refs.tagContainer) {
        return
      } // 兼容插槽
      const tagTopDom = this.$refs.tagContainer // 最外部容器
      const tagListDom = this.$refs.tagList // 标签列表容器
      const containerDistance = 6 // 【最外部容器 && 标签列表容器】 的差别
      // const tagListDomHeight = tagListDom.offsetHeight - (parseInt(this.getCurrentDomStyle(tagListDom, 'border-top')) + parseInt(this.getCurrentDomStyle(tagListDom, 'border-bottom')) + parseInt(this.getCurrentDomStyle(tagListDom, 'padding-bottom')))// 标签列表容器的宽度
      const tagListDomHeight =
        tagListDom.offsetHeight -
        this.getDomStylePropertyValue(tagListDom, ['border-top', 'padding-bottom']) // 标签列表容器的宽度
      const tagListAllDom = Array.from(tagListDom.children) // 获取所有标签（Array）
      // 单个标签的高度(标签的高度是不变的)
      // situcation01:如果外部数据为空,tags数量为0,初始化高度 31
      let tagHeight = 32 // （28 + 3）
      // situcation02:如果外部数据不为空，计算
      if (renderData.length) {
        // tagHeight = tagListAllDom[0].offsetHeight + parseInt(this.getCurrentDomStyle(tagListAllDom[0], 'margin-top')) + parseInt(this.getCurrentDomStyle(tagListAllDom[0], 'margin-bottom'))
        tagHeight =
          tagListAllDom[0].offsetHeight +
          this.getDomStylePropertyValue(tagListAllDom[0], ['margin-top', 'margin-bottom'])
      }
      // 获取子节点数据占据了多少行【父盒子高度/单个子盒子高度】
      const tagListLine = this.getChildElementLine(tagListDomHeight, tagHeight)
      // 保存当前行数
      let tagTopDomHeight = 0
      // 当前行数是否大于最大行数
      if (tagListLine >= maxNum) {
        tagTopDomHeight = tagHeight * maxNum
      } else if (tagListLine <= minNum) {
        // 是否小于最小行数
        tagTopDomHeight = tagHeight * minNum
      } else {
        // 当前
        tagTopDomHeight = tagHeight * tagListLine
      }
      // 保存当前高度:
      this.tagContainerHeight = `${tagTopDomHeight + containerDistance}px`
      // 设置容器高度:
      tagTopDom.style.height = `${tagTopDomHeight + containerDistance}px`
    },

    /**
     * @description: 获取当前节点的样式
     * @param {*}
     * @return {*}
     */
    getCurrentDomStyle(element, attr) {
      // 兼容
      if (element.currentStyle) {
        return element.currentStyle[attr]
      } else {
        // false:不获取伪类
        return getComputedStyle(element, false)[attr]
      }
    },
    /**
     * @description: 获取dom常用的样式属性值之和【】
     * @param {*}
     * @param {Array} type:属性值
     * @return {*}
     */
    getDomStylePropertyValue(dom, type = []) {
      let altogether = 0
      type.forEach((item) => {
        altogether += parseInt(this.getCurrentDomStyle(dom, item), 10)
      })
      return altogether
    },
    /**
     * @description: 获取子节点在父节点中，占用的行数 【父盒子高度/单个子盒子高度】
     * @param {*}
     * @param {*}
     * @return {*}
     */
    getChildElementLine(parentHeight, sonHegiht) {
      // ps: 这里的加1，因为父容器的高度每行会多出来1px
      return parentHeight / (sonHegiht + 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-selection-box {
  // 启动标签
  .tag-selection-box__start-tags {
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    // padding: 0 10px;
    // height: 77px;
    border: 1px solid rgba($tag-gray, 0.3);
    border-radius: 5px;
    transition: height 0.4s ease;
    .tag-selection-box__start-tags--placeholder {
      padding: 0 10px;
      font-size: 14px !important;
      line-height: 31px !important;
      color: $tag-gray;
    }
    .tag-selection-box__start-tags--select-btn {
      font-size: 14px;
    }
    .tag-selection-box__start-tags--list {
      box-sizing: border-box;
      // padding: 0 0 3px 0 !important;
      padding: 0 0 3px 10px !important;
      margin: 0 !important;
      line-height: 32px !important;
      border: none !important;
    }
  }
}
</style>

<style lang="scss">
.tag-selection-box {
  .el-icon-question {
    font-size: 14px;
    color: $brand-color-5;
  }
  .el-form {
    width: 92%;
    .el-form-item__label {
      box-sizing: border-box;
      padding-top: 12px;
      line-height: 16px;
      line-height: auto !important;
      text-align: right;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
      .el-scrollbar__view {
        height: auto !important;
        line-height: 32px !important;
      }
      .is-horizontal {
        display: none;
      }
    }
  }
}
</style>
