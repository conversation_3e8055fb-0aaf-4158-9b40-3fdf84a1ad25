<!--
 * @Author: jing.chen
 * @Date: 2020-09-09 09:40:20
 * @LastEditors: zongbao.yao
 * @LastEditTime: 2021-09-18 12:25:47
 * @Description: 二维码
-->
<template>
  <div :class="[moveFlg ? 'qr-code-move' : 'qr-code']">
    <!-- 如果不需要放大预览二维码 -->
    <!-- <div @click="downLoadImg">下载二维码</div> -->
    <img
      v-if="qrFlg"
      ref="qrImg"
      class="img"
      :src="url"
      style="position: absolute; width: 70px"
      @mousedown="move"
    />
    <canvas v-else ref="qrCode" class="qr" @mousedown="move"></canvas>
    <!-- 预览二维码 -->
    <template v-if="ifPreview">
      <el-image :preview-src-list="urlPreview" :src="urlImg" :style="{ width: size, height: size }">
      </el-image>
    </template>
  </div>
</template>

<script>
import QRCode from 'qrcode'

export default {
  name: 'QrCode',

  components: {},

  filters: {},

  mixins: [],
  props: {
    url: {
      type: String,
      required: true
    },
    // 二维码展示大小
    size: {
      type: String,
      default: '80px'
    },
    // 是否预览？
    ifPreview: {
      type: Boolean,
      default: false
    },
    // 是否拖拽
    moveFlg: {
      type: Boolean,
      default: false
    },
    // 是群活码还是个活码
    qrFlg: {
      type: Boolean,
      default: false
    },
    // 起始位置
    codeLocation: {
      type: Object,
      default: () => ({})
    },
    styleInit: {
      type: Boolean,
      default: false
    },
    // 仅在素材库使用，生成并替换图片
    imgPreview: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      urlImg: '',
      // 预览的图片url
      urlPreview: []
    }
  },
  mounted() {
    console.log(this.url)
    if (!this.url) return
    let canvas
    if (this.qrFlg) {
      console.log('qrFlg', this.qrFlg)
      // 个活码
      canvas = this.$refs.qrImg
      console.log('个活码', canvas.style)
    } else {
      // 群活码
      // console.log('群活码')
      this.createQRCode()
      canvas = this.$refs.qrCode
    }
    if (!this.styleInit) return
    if (!this.codeLocation) return
    canvas.style.left = this.codeLocation.left + 'px'
    canvas.style.top = this.codeLocation.top + 'px'
  },
  methods: {
    /**
     * @description 生成二维码
     */
    createQRCode() {
      // 先用 QRCode 生成二维码 canvas
      const canvas = this.$refs.qrCode
      QRCode.toCanvas(canvas, this.url, (error) => {
        if (error) {
          console.log(error)
        } else {
          // qrcode 生成的二维码会带有一些默认样式，需要调整下
          // canvas.style.width = '80px'
          // canvas.style.height = '80px'
          canvas.style.width = this.size
          canvas.style.height = this.size
        }
      })
      // 是否开启预览模式
      // this.$nextTick(() => {
      //   if (this.ifPreview) {
      //     canvas.style.width = '0px'
      //     canvas.style.height = '0px'
      //     const img = canvas.toDataURL('image/png')
      //     this.urlImg = img
      //     this.urlPreview.push(img)
      //     this.$emit('canvasUrl', this.urlImg)
      //   } else {
      //     canvas.style.width = this.size
      //     canvas.style.height = this.size
      //   }
      // })
      // 在企微工作台环境el-image接收转换成base64的图片会因为转换速度无法渲染，因此换成setTimeout

      if (this.ifPreview) {
        canvas.style.width = '0px'
        canvas.style.height = '0px'
        setTimeout(() => {
          const img = canvas.toDataURL('image/png')
          this.urlImg = img
          this.urlPreview.push(img)
          this.$emit('canvasUrl', this.urlImg)
        }, 100)
      } else {
        canvas.style.width = this.size
        canvas.style.height = this.size
      }
      if (this.imgPreview) {
        const img = canvas.toDataURL('image/png')
        this.$emit('canvasUrl', img)
      }
    },
    // 下载二维码
    downLoadImg(name) {
      const canvas = this.$refs.qrCode
      const img = canvas.toDataURL('image/png')
      const element = document.createElement('a')
      element.href = img
      element.download = name
      element.click()
    },
    // 下载二维码
    batchDownloadQrcode(url, imgName, flag) {
      const canvas = this.$refs.qrCode
      QRCode.toCanvas(canvas, url, (error) => {
        if (error) {
          console.log(error)
        } else {
          // qrcode 生成的二维码会带有一些默认样式，需要调整下
          if (flag) {
            canvas.style.width = '48px'
            canvas.style.height = '48px'
          } else {
            canvas.style.width = '0px'
            canvas.style.height = '0px'
          }
          // 下载后置操作
          const img = canvas.toDataURL('image/png')
          const element = document.createElement('a')
          console.log(img, 'img')
          element.href = img
          element.download = imgName
          element.click()
        }
      })
    },
    /**
     * @description 二维码拖拽
     * @param {object} [e] 当前二维码元素
     */
    move(e) {
      const odiv = e.target // 获取目标元素
      // 阻止默认事件的方法,如果不阻止默认事件onmouseup会无法触发
      e.preventDefault()
      // 算出鼠标相对元素的位置
      const disX = e.clientX - odiv.offsetLeft
      const disY = e.clientY - odiv.offsetTop
      let left, top
      document.onmousemove = (e) => {
        console.log('移动')
        // 鼠标按下并移动的事件
        // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        left = e.clientX - disX
        top = e.clientY - disY
        // 移动当前元素
        odiv.style.left = left + 'px'
        odiv.style.top = top + 'px'
      }
      document.onmouseup = (e) => {
        console.log('停止')
        const codeLocation = {
          left: left,
          top: top
        }
        this.$emit('onMouseUp', codeLocation)
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.qr-code {
  display: flex;
  justify-content: center;
}
.qr-code-move {
  position: relative;
  width: 100%;
  height: 100%;
  .qr,
  .img {
    position: absolute;
    top: 0px;
    left: 0px;
  }
}
</style>
