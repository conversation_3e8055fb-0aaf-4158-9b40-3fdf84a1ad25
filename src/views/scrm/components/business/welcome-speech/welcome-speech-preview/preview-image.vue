<template>
  <div class="preview-image">
    <el-image v-if="data.url" class="preview-image__img" :src="data.url"></el-image>
  </div>
</template>

<script>
export default {
  name: 'PreviewImage',

  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({})
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-image {
  line-height: 0 !important;
  &__img {
    width: 128px;
    height: 128px;
    border-radius: 1px;
  }
}
</style>
