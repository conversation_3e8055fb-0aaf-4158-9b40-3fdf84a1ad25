<template>
  <div class="toolbar">
    <ul id="pos_btns" class="pos_btns">
      <li code="back">
        <i class="web-icon-field-address web-iconfont" @click="onOperate(ENUM_TOOL_TYPE.BACK)"></i>
      </li>
      <li><span class="gap"></span></li>
      <li>
        <i
          v-if="fullscreenVisible"
          class="web-icon-tuichuquanping web-iconfont"
          @click="onOperate(ENUM_TOOL_TYPE.EXIT_FULL)"
        ></i
        ><i
          v-else
          class="web-icon-quanping1 web-iconfont"
          @click="onOperate(ENUM_TOOL_TYPE.FULL)"
        ></i>
      </li>
      <li><span class="gap"></span></li>
      <li><i class="icon-suoxiao iconfont" @click="onOperate(ENUM_TOOL_TYPE.CUT)"></i></li>
      <li>{{ scaleNum }}%</li>
      <li code="add" @click="onOperate(ENUM_TOOL_TYPE.ADD)">
        <i class="icon-fangdajing iconfont"></i>
      </li>
    </ul>
  </div>
</template>

<script>
// TODO: 枚举建议同时维护相关的配置，描述，语义化,党浩浩
import { ENUM_TOOL_TYPE } from '@/views/scrm/constants/enum.contacts.js'

export default {
  name: 'Toolbar',

  components: {},

  filters: {},

  mixins: [],

  props: {
    fullscreenVisible: {
      type: Boolean,
      default: false
    }
    // scaleNum: {
    //   type: Number,
    //   default: 100
    // }
  },

  data() {
    return {
      ENUM_TOOL_TYPE,
      scaleNum: 100
    }
  },

  computed: {},

  watch: {},

  mounted() {},

  methods: {
    // TODO: 枚举建议同时维护相关的配置，描述，语义化,党浩浩
    onOperate(type) {
      if (type === ENUM_TOOL_TYPE.ADD && this.scaleNum !== 150) {
        this.scaleNum += 10
        this.$emit('on-operate', type)
      } else if (type === ENUM_TOOL_TYPE.CUT && this.scaleNum !== 50) {
        this.scaleNum -= 10
        this.$emit('on-operate', type)
      } else if (type === ENUM_TOOL_TYPE.BACK) {
        this.$emit('on-operate', type)
        this.scaleNum = 100
      } else if (type === ENUM_TOOL_TYPE.FULL || type === ENUM_TOOL_TYPE.EXIT_FULL)
        this.$emit('on-operate', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.toolbar {
  #pos_btns {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 198px;
    height: 42px;
    padding: 0 10px;
    margin: 0;
    font-size: 12px;
    color: #545454;
    list-style-type: none;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    -webkit-box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
    // TODO: 建议不要使用dom选择，改class，浩浩
    i {
      cursor: pointer;
    }
    i:hover {
      color: $brand-color-5;
    }
    .gap {
      display: inline-block;
      width: 1px;
      height: 20px;
      margin: 0 12px;
      background-color: $neutral-color-4;
    }
  }
}
</style>
