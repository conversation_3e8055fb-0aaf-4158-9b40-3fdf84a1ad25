/* 智能标签公用方法 */
import selectTag from './select-tag.vue'
import { colorRgba } from '@/views/scrm/utils'

export default {
  components: {
    selectTag
  },
  data() {
    return {
      visible: false
    }
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    invalids: {
      type: Array,
      default: () => []
    },
    businessType: {
      type: Number,
      default: 0
    }
  },
  methods: {
    colorRgba,
    // 数据无需处理的规则，初始化
    init() {
      if (this.list.length === 0) return
      this.clueList = this.list
    },
    // 格式化输入的数字
    numberInput(e, index) {
      const number = Number(e)
      if (number === 0) {
        this.clueList[index].ruleCondition.num = ''
        return
      }
      if (number > 9999) {
        this.clueList[index].ruleCondition.num = 9999
        return
      }
      this.clueList[index].ruleCondition.num =
        e.replace(/[^0-9]/g, '') && Number(e.replace(/[^0-9]/g, ''))
    },
    // 删除一行子规则
    minusRule(index) {
      this.clueList.splice(index, 1)
    },
    // 素材选择返回
    submitChoose(data, index) {
      this.clueList[index].ruleCondition.materialPojos = data
      const obj = Object.assign(this.clueList[index].ruleCondition, { materialPojos: data })
      const obj1 = Object.assign(this.clueList[index], { ruleCondition: obj })

      this.clueList.splice(index, 1, obj1)
    },
    // 选标签
    getSelectedTags(data, index) {
      const list = this.clueList[index].tag
      list.splice(0, list.length, ...data)
    },
    deleteTag(data, index) {
      const list = this.clueList[index].tag
      list.splice(data, 1)
    }
  }
}
