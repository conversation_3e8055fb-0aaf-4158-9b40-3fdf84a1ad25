<!-- 线索智能标签-首末次触发行为 -->
<template>
  <div class="second-clue-first">
    <div
      v-for="(element, index) in clueList"
      :key="index"
      class="rule"
      :class="[element.ruleCondition.contentCode === dingTalk[0].value && 'dip']"
    >
      <div class="second-clue-first__top" style="display: flex">
        <span>用户首次</span>
        <el-select
          v-if="element.ruleCondition.contentCode === TAG_RULES.rules_type"
          v-model="element.ruleCondition.code"
          placeholder="请选择"
          @change="onChange"
        >
          <el-option
            v-for="item in dingtalkType"
            :key="item.value"
            :disabled="item.disabled"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select v-else v-model="element.ruleCondition.code" placeholder="请选择">
          <el-option
            v-for="item in lastType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="element.ruleCondition.contentCode"
          placeholder="请选择"
          @change="onChange"
        >
          <el-option
            v-for="item in dingTalk"
            :key="item.value"
            :disabled="item.disabled"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <auto-tag-select
          v-if="element.ruleCondition.contentCode === dingTalk[0].value"
          :invalids="invalids"
          :list.sync="element.ruleCondition.materialPojos"
          @submitChoose="
            (data) => {
              submitChoose(data, index)
            }
          "
        ></auto-tag-select>
        <span>时，</span>
      </div>
      <div class="second-clue-first__info">
        自动打标签：
        <select-tag
          :business-type="businessType"
          :is-scrm="true"
          :limit-number="1"
          :tag-list="element.tag"
          :visible.sync="visible"
          @deleteTag="
            (data) => {
              deleteTag(data, index)
            }
          "
          @getSelectedTags="(list) => getSelectedTags(list, index)"
        ></select-tag>
      </div>
      <div v-if="clueList.length > 1" class="minus" @click="minusRuleFirst(index)">
        <i class="web-icon-shanchu_xian web-iconfont"></i>
      </div>
    </div>

    <div v-if="clueList.length < 5" class="add" @click="addRule">
      <i class="web-icon-plus web-iconfont"></i>{{ $t('scrm.autoTag.newRule') }}
    </div>
  </div>
</template>

<script>
import { lastType, dingTalk, dingtalkType, TAG_RULES } from './contants.js'
import autoTagSelect from './auto-tag-select.vue'
import autoTagMixin from './auto-tag-mixin'

export default {
  name: 'SecondClueFirst',
  components: {
    AutoTagSelect: autoTagSelect
  },
  mixins: [autoTagMixin],
  data() {
    return {
      clueList: [
        {
          subRule: 2,
          ruleCondition: {
            code: 1,
            contentCode: 7, // 6小程序，7具体素材
            materialPojos: []
          },
          tag: [] // 线索标签
        }
      ],
      codeFlg: false,
      lastType,
      dingTalk,
      dingtalkType,
      TAG_RULES,
      number: 0,
      accessFlg: false,
      shareFlg: false
    }
  },
  created() {
    const isHasSub = !!this.lastType.find((item) => {
      return item.value === 6
    })
    if (isHasSub) {
      lastType.pop()
    }
    this.initFirst()
  },

  methods: {
    initFirst() {
      if (this.list.length === 0) return
      this.clueList = this.list
    },
    // 新增规则
    addRule() {
      console.log('this.dingTalk:', JSON.stringify(this.dingtalkType))
      this.clueList.push({
        subRule: 2,
        ruleCondition: {
          code: 1,
          contentCode: 7, // 小程序
          materialPojos: []
        },
        tag: [] // 线索标签
      })
    },
    // 删除规则
    minusRuleFirst(index) {
      this.clueList.splice(index, 1)
    },
    onChange(e) {
      console.log('%c选择条件：', 'color: blue', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.second-clue-first {
  align-items: center;
  &__top {
    margin-top: 3px;
    margin-bottom: 8px;
  }
  &__info {
    display: flex;
    line-height: 40px;
    :deep(.el-input__inner) {
      height: 32px;
    }
  }
  :deep(.el-select) {
    width: 100px;
    margin: 0 10px;
  }
  :deep(.tag-selection-box) {
    display: flex;
    align-items: center;
    .tag-selection-box__start-tags {
      width: 200px;
      height: 32px !important;
    }
  }
  :deep(.el-input__inner) {
    height: 32px;
  }
  :deep(.new-material-select) {
    width: 0;
    height: 0;
  }
  .dip {
    flex-direction: column !important;
    align-items: start !important;
  }
}
</style>
