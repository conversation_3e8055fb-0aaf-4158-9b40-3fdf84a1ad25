<template>
  <!-- 弹框 -->
  <DialogLayout
    :append-to-body="true"
    :dialog-visiable.sync="visibleCompt"
    :loading-submit="btnLoading"
    title="绑定好友"
    width="600px"
    @access="onSubmit"
    @close="onClose"
  >
    <template #content>
      <div v-loading="loading" class="bind-friend">
        <div class="bind-friend__field">
          <span class="bind-friend__label"> 选择绑定类型： </span>
          <el-radio-group v-model="bindRadioIdx">
            <el-radio
              v-for="(item, index) in bindRadioList"
              :key="index"
              :disabled="item.disabled"
              :label="index"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </div>
        <div class="bind-friend__field">
          <span class="bind-friend__label"> 选择绑定对象：</span>
          <!-- 选择人员 -->
          <TableSelectorInput
            ref="tableSelectorInput"
            v-model="userList"
            alias-key="id"
            alias-label="text_1"
            :editable="true"
            :prop-selector="propSelector"
            style="width: 300px"
          >
            <template #btns>
              <div class="operation-box">
                <el-select v-model="formId">
                  <el-option
                    v-for="item in formList"
                    :key="item.formId"
                    :label="item.formName"
                    :value="item.formId"
                  ></el-option>
                </el-select>
                <span class="operation-box__add" @click="openAddDialog"
                  >+ {{ $t('operation.add') }}</span
                >
              </div>
            </template>
          </TableSelectorInput>
        </div>
      </div>
    </template>
  </DialogLayout>
</template>

<script>
// common
import DialogLayout from '@/views/scrm/components/basic/dialog/dialog-layout'
import TableSelectorInput from '@/views/scrm/components/business/wechat-friend/table-selector/table-selector-input.vue'
// api
import {
  getWechatFriendBindTemplateList,
  getBindBusinessTypeList,
  getBindBusinessDataList,
  bindWechatFriend
} from '@/api/scrm/crm/wechat-friend.js'
// enum
import { ENUM_BIND_TEMP } from '@/views/scrm/constants/enum.contacts.js'
// vuex
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'BindFriend',

  components: {
    DialogLayout,
    TableSelectorInput
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => ({
        // id: 0 // 好友关系id
        // externalUserId: ''
      })
    }
  },

  computed: {
    ...mapGetters('formDataDialogChild', ['isShowDialog']),
    // 绑定组件---radio-list渲染
    bindRadioList() {
      return Object.values(ENUM_BIND_TEMP)
        .filter((item) => {
          return this.bindBusinessTypeList.includes(item.businessType)
        })
        .map((item) => {
          return {
            ...item,
            disabled: this.forbiddenBindBusinessTypeList.includes(item.businessType)
          }
        })
    },
    // 当前选择的业务info
    radioInfoCompt() {
      return this.bindRadioList[this.bindRadioIdx] || {}
    },
    visibleCompt: {
      set(val) {
        this.$emit('update:visible', val)
      },
      get() {
        return this.visible
      }
    },
    // 当前表单info
    formInfo() {
      return this.formList.find((item) => item.formId === this.formId) || {}
    },
    // 档期那table-selector参数
    propSelector() {
      return {
        params: {
          // formId: '28133', // 表单id
          // menuId: '33574', // 菜单id
          // businessType: '',
          ...this.formInfo,
          bindDataInfo: this.bindDataInfo,
          bindCmd: null // TODO: 暂时传 null 值， 1 绑定操作，0 解绑操作
        },
        api: getBindBusinessDataList,
        tableListAlias: 'paasFormDataESList',
        selectLimit: 1
      }
    }
  },

  data() {
    return {
      bindDataInfo: {}, // 绑定数据信息，用于参数，获取绑定业务类型、获取模板列表
      bindBusinessTypeList: [], // 绑定类型的 businessType
      forbiddenBindBusinessTypeList: [], // 绑定类型的 businessType (禁用)
      bindRadioIdx: -1, // 当前选中的下标(绑定类型的 businessType)
      formList: [], // form list
      formId: 0, // form id
      userList: [], // 选中的user数据
      userSelectVisible: false,
      btnLoading: false, // 按钮loading
      loading: false // 加载loading
    }
  },

  watch: {
    isShowDialog(newVal, oldVal) {
      if (oldVal) {
        this.$refs.tableSelectorInput.onRefreshTable()
      }
    }
  },

  created() {
    this.init()
  },

  methods: {
    ...mapActions('formDataDialogChild', ['formDataAdd']),
    // 新建模板
    openAddDialog() {
      const { appId, businessType, menuId, formId } = this.formInfo
      const params = {
        appId,
        businessType,
        formId,
        menuId,
        saasMark: 1,
        subBusinessType: businessType || ''
      }
      this.formDataAdd(params)
    },
    async init() {
      this.loading = true
      // 监听 list初始化 bindRadioIdx
      // const unWatch = this.$watch(
      this.$watch(
        'bindRadioList',
        (newVal) => {
          if (!newVal.length) return
          // 初始化选中未被禁用的第一个
          const canSelectIdx = newVal.findIndex((item) => !item.disabled)
          this.bindRadioIdx = canSelectIdx
          // unWatch()
        },
        { deep: true }
      )
      // 监听bindRadioIdx 选择后改变table-selector必要参数
      this.$watch('bindRadioIdx', (newVal) => {
        if (newVal !== -1) {
          this.getWechatFriendBindTemplateList({
            businessType: this.radioInfoCompt.businessType,
            bindDataInfo: this.bindDataInfo
          })
          // 清空【选择绑定对象】
          this.userList.splice(0)
        }
      })
      // 获取 绑定类型的 businessType
      const res = await getBindBusinessTypeList({ id: this.params.id })
      this.bindBusinessTypeList = res.result.bindBusinessTypeList
      this.forbiddenBindBusinessTypeList = res.result.forbiddenBindBusinessTypeList
      this.bindDataInfo = res.result.bindDataInfo
      this.loading = false
    },
    // 获取form list
    getWechatFriendBindTemplateList({ businessType, bindDataInfo }) {
      this.formList.splice(0)
      this.formId = ''
      // 初始化formList和formId
      getWechatFriendBindTemplateList({
        businessType,
        bindDataInfo
      }).then((res) => {
        const formList = res.result.formList
        this.formList = formList
        this.formId = formList[0].formId
      })
    },
    onSubmit() {
      const radioInfo = this.radioInfoCompt
      if (!radioInfo.businessType) return this.$message.warning('请选择绑定类型！')
      if (!this.userList.length) return this.$message.warning('请选择绑定对象！')
      this.btnLoading = true
      bindWechatFriend({
        businessType: radioInfo.businessType, // 绑定的业务类型
        dataId: this.userList[0].dataId, // 绑定客户或线索或联系人数据id
        formId: this.userList[0].formId,
        friendshipId: this.params.id, // 好友id
        externalUserId: this.params.externalUserId, //
        bind: 1 // 1 绑定数据 ; 0 取消绑定
      })
        .then(({ result }) => {
          // 特殊逻辑
          if (result.msg && result.msg.code !== 200) {
            return this.$confirm(result.msg.msg, '无法绑定', {
              confirmButtonText: this.$t('operation.confirm'),
              cancelButtonText: this.$t('operation.cancel'),
              type: 'warning'
            })
          }
          this.$emit('callback', { type: 'bind' })
          this.$message.success('绑定成功！')
          this.onClose()
        })
        .finally(() => {
          this.btnLoading = false
        })
    },
    onClose() {
      this.userList.splice(0)
      this.visibleCompt = false
    }
  }
}
</script>

<style lang="scss" scoped>
.bind-friend {
  display: flex;
  flex-direction: column;
  width: 100%;
  &__field {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 12px;
  }
  &__label {
    font-size: 14px;
    color: $text-auxiliary;
    text-align: left;
  }
}
</style>

<style lang="scss">
.operation-box {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 10px;
  &__add {
    color: $brand-base-color-6;
    cursor: pointer;
  }
}
</style>
