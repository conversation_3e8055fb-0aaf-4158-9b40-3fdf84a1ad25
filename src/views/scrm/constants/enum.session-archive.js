// 会话存档相关枚举

// 会话存档列表-tab维护 (不同会话来源)
const ENUM_SESSION_SOURCE_TYPE = {
  EMPLOYEE: { type: 'employee', memo: '员工' },
  FRIEND: { type: 'customer', memo: '好友' },
  QWX_GROUP: { type: 'groupChat', memo: '企微群' }
}

const NEW_TIPS = [
  '1. 为保障客户服务质量、提高内部协作效率和监管合规等原因，企业微信提供会话内容存档功能。企业可以统一设置存档的员工范围，并通过API获取开启存档员工的工作沟通内容，满足企业的外部监管合规和内部管理需求。',
  '2. 会话存档，需要员工和客户许可；企业仅可获得席位员工的所有相关会话，不能获取未开启员工之间的对话。',
  '3. 在付费有效期内，存储企业自授权日起最近 90 天的会话内容过期的会话内容不存储。',
  '4. 正式开通到期后，超过 30 天未续期，则之前付费期间存储的会话内容将不再存储，前台展示组件不展示。',
  '5. 试用到期后，若超过 30 天未付费，则之前试用期间存储的会话内容将不再存储，前台展示组件不展示。'
]

export { ENUM_SESSION_SOURCE_TYPE, NEW_TIPS }
