<template>
  <div class="session-archive-detail">
    <div class="session-archive-detail__head">
      <span class="session-archive-detail__head--title" @click="routerBack">
        <i class="el-icon-arrow-left"></i>
        {{ $route.query.title }} 群聊会话详情
      </span>
    </div>

    <div class="session-box__content">
      <header class="session-box__header">
        <el-date-picker
          v-model="dateRange"
          :clearable="false"
          :editable="false"
          :end-placeholder="$t('label.endTime')"
          format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :range-separator="$t('unit.to')"
          :start-placeholder="$t('label.startTime')"
          type="daterange"
          unlink-panels
          value-format="timestamp"
          @change="handleDateRangeChange"
        >
        </el-date-picker>
        <div ref="btnBox" class="export-btn"></div>
      </header>

      <!-- <div v-if="hasData" class="session-box__column"> -->
      <SessionScrollList
        :params="thirdMenuParams"
        :select-active.sync="selectActive"
        :session-type-select="sessionTypeTabs"
        @initEnd="initEnd"
        @onTypeChange="onTypeChange"
        @updateSelect="updateSelect"
      ></SessionScrollList>
      <!-- </div> -->

      <!-- <div v-else class="no-data">暂无会话内容</div> -->
    </div>

    <div v-show="exportBtn" class="export">
      <div ref="export" class="export-text"></div>
      <i class="el-icon-close" @click="exportBtn = false"></i>
    </div>

    <!-- <session-archive-detail
      v-if="showDetail"
      :session-archive-params="sessionArchiveParams"
      style="flex: 1"
      @back="handleBackToList"
    ></session-archive-detail> -->
  </div>
</template>

<script>
import SessionScrollList from '@/views/scrm/sales-manage/session-archive-detail/session-scroll-list-sdk/index.vue'
import { exportV2, exportV2Result } from '@/api/scrm/sales-manage/session-archive.js'

export default {
  name: 'CustomerSessionArchiveDetail',

  components: {
    SessionScrollList
  },

  data() {
    return {
      searchParams: {
        // keyWord: '',
        startTime: undefined,
        endTime: undefined
      },
      dateRange: [],
      showDetail: false,
      selectedSessionArchive: {},
      canEditMainPerson: false,
      dataList: [],

      //
      sessionTypeTabs: [],
      selectActive: '',
      thirdMenuParams: {},
      friendMark: {},
      activeDom: {},
      exportBtn: false
    }
  },

  computed: {
    hasData() {
      return this.dataList.length > 0
    },
    pickerOptions() {
      const _this = this
      const pickerShortCutFn = (picker, month) => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * month)
        picker.$emit('pick', [start, end])
      }
      return {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              pickerShortCutFn(picker, 7)
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              pickerShortCutFn(picker, 30)
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              pickerShortCutFn(picker, 90)
            }
          }
        ],
        disabledDate(time) {
          const currentTime = Date.now() // 当前时间
          if (_this.dateRange[0]) {
            const times = 86400000 * 30 * 3 // 三个月的毫秒数
            const curSelectTime = _this.dateRange[0] // 选择的起始时间
            const before = curSelectTime - times
            const after = curSelectTime + times
            // 选择的时间 必须小于当前 前后三个月
            return time.getTime() > currentTime || time.getTime() > after || time.getTime() < before
          }
          return time.getTime() > currentTime
        }
      }
    }
    // sessionArchiveParams() {
    //   return {
    //     sessionType: 'externalSession',
    //     weChatMsgId: this.selectedSessionArchive.weChatMsgId || '',
    //     sessionUserId: this.selectedSessionArchive.userId
    //   }
    // }
  },

  watch: {
    searchParams: {
      deep: true,
      handler() {
        this.getDataList()
      }
    }
  },

  created() {
    window.ww.initOpenData()
    this.initDateRange()
    this.getDataList()
  },

  mounted() {
    setTimeout(() => {
      this.initBtn()
    }, 100)
    this.getExportResult()
  },

  methods: {
    initDateRange() {
      const currentTime = new Date().getTime()
      const startTime = currentTime - 3600 * 1000 * 24 * 90
      const endTime = currentTime
      this.dateRange = [startTime, endTime]

      this.handleDateRangeChange(this.dateRange)
    },
    getDataList(concat = false) {
      this.thirdMenuParams = {
        sessionUserId: utils.LS.get('userId') || '1',
        weChatMsgId: this.$route.query.weChatMsgId,
        sessionType: 'externalGroupSession',
        msgType: 'all',
        chatId: this.$route.query.chatId
      }
      // const params = {
      //   id: this.$route.query.id,
      //   sessionUserId: utils.LS.get('userId') || '1',
      //   version: 'v2',
      //   sessionType: 'externalGroupSession',
      //   chatId: this.$route.query.chatId,
      //   ...this.searchParams
      // }

      // getSessionList4WechatFriendship(params).then(({ result }) => {
      //   this.friendMark = result.friendMark
      //   this.canEditMainPerson = result.editFlag
      //   this.dataList = concat ? [...this.dataList, ...result.pojoList] : result.pojoList
      //   // this.activeDom = this.dataList[0]
      // })
    },

    handleDateRangeChange(val) {
      if (val) {
        this.searchParams.startTime = Math.ceil(val[0] / 1000)
        this.searchParams.endTime = Math.ceil(val[1] / 1000)
      } else {
        this.searchParams.startTime = this.searchParams.endTime = undefined
      }
    },
    getMessage(card) {
      console.log('getMessage', card)
      this.thirdMenuParams = {
        sessionUserId: card.userId,
        weChatMsgId: card.weChatMsgId,
        sessionType: 'externalSession',
        msgType: 'all',
        externalUserId: card.externalUserId
      }
      this.activeDom = card
      // this.showDetail = true
      // this.$nextTick(() => {
      //   this.selectedSessionArchive = card
      // })
    },
    handleBackToList() {
      this.selectedSessionArchive = {}
      this.showDetail = false
    },
    routerBack() {
      this.$router.replace({
        path: '/scrm/sales-manage/session-archive',
        query: { tabIdTwo: this.$route.query.tabIdTwo, tabId: this.$route.query.tabId }
      })
    },
    onTypeChange(val) {
      console.log('三级菜单-会话内容-tab切换筛选', val)
      const params = {
        ...this.thirdMenuParams,
        msgType: val
      }
      this.thirdMenuParams = params
    },
    updateSelect(list, val) {
      this.sessionTypeTabs = list
      this.selectActive = val || 'all'
    },
    initEnd() {
      this.thirdMenuParams = {
        sessionUserId: utils.LS.get('userId') || '1',
        weChatMsgId: this.$route.query.weChatMsgId,
        sessionType: 'externalGroupSession',
        msgType: 'all',
        chatId: this.$route.query.chatId
      }
    },
    initBtn() {
      console.log('初始化容器')
      const box = this.$refs.btnBox
      const _that = this
      const factory = window.ww.createOpenDataFrameFactory()
      const instance = factory.createOpenDataFrame({
        el: box, // “容器”元素，用于挂载展示组件
        template: `
          <view>
            <span class="btn">
              <ww-open-button open-type="getExportCode" bindgetexportcode="onExportCodeReady"><i class="el-icon-arrow-left"></i>导出</ww-open-button>
            </span>
          </view>
        `,
        style: `
          .text {
            font-size: 14px;
          }
        `,
        data: {
          // resultId: '',
          // messageId: '',
          // secretKey: ''
        },
        methods: {
          // 响应按钮点击
          onExportCodeReady(e) {
            console.log('响应消息点击事件', e, e.detail.exportCode)
            _that.exportHandle(e.detail.exportCode)
          }
        }
      })
      this.instance = instance
      // iframe高度
      box.children[0].style = 'width: 46px;height: 25px;'
      console.log('初始化容器-导出结果')
      const boxExport = this.$refs.export
      const factoryExport = window.ww.createOpenDataFrameFactory()
      const instanceExport = factoryExport.createOpenDataFrame({
        el: boxExport, // “容器”元素，用于挂载展示组件
        template: `
          <view class="text"> 
            <ww-open-result-link result-id="{{data.resultId}}" message-id="{{data.messageId}}" secret-key="{{data.secretKey}}">点击打开导出结果</ww-open-result-link>
          </view>
        `,
        style: `
          .text {
            font-size: 14px;
            width: 60px;
            height: 20px;
            width: 120px;
            line-height: 42px; 
            margin-left: 10px;
          }
        `,
        data: {
          resultId: '',
          messageId: '',
          secretKey: ''
        },
        methods: {
          // 响应按钮点击
          // onExportCodeReady(e) {
          //   console.log('响应消息点击事件', e, e.detail.exportCode)
          //   _that.exportHandle(e.detail.exportCode)
          // }
        }
      })
      this.instanceExport = instanceExport
      // iframe高度
      box.children[0].style = 'width: 46px;height: 25px;'
    },
    // 导出
    exportHandle(code) {
      const params = {
        exportCode: code,
        sessionUserId: utils.LS.get('userId') || '1',
        chatIdList: [this.$route.query.chatId],
        startTime: this.searchParams.startTime,
        endTime: this.searchParams.endTime,
        sessionType: 'externalGroupSession'
      }

      exportV2(params).then((res) => {
        console.log('导出')
        if (this.timer) clearInterval(this.timer)
        this.getExportResult()
      })
    },
    // 获取导出结果
    getExportResult() {
      try {
        exportV2Result().then(({ result }) => {
          console.log('this.timer:', this.timer)
          if (!result.status && !this.timer) {
            this.timer = setInterval(() => {
              this.getExportResult()
            }, 5000)
          } else if (result.status) {
            this.timer && clearInterval(this.timer)
            this.timer = null
            if (result.status === 1) {
              this.exportBtn = true
              this.instanceExport.setData({
                resultId: result.resultId,
                messageId: result.messageId,
                secretKey: result.secretKey
              })
            } else if (result.status === 2) {
              this.$message.error(result.errorMsg)
            }
          }
        })
      } catch (error) {
        console.log('获取ai结果-exportV2Result:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.session-archive-detail {
  display: flex;
  flex-direction: column;
  width: 100%;
  // min-width: 1250px;
  height: 100%;
  &__head {
    box-sizing: border-box;
    width: 100%;
    height: 50px;
    padding: 0 16px;
    line-height: 50px;
    background-color: $base-white;
    &--title {
      font-size: 16px;
      color: $text-main;
      cursor: pointer;
      i {
        margin-right: 5px;
      }
      &:hover {
        opacity: 0.9;
      }
      .title-style {
        margin: 0 4px;
        font-size: 12px;
      }
      .wx {
        color: $success-base-color-6;
      }
      .other {
        color: $brand-base-color-6;
      }
    }
  }
  .session-box {
    box-sizing: border-box;
    flex: 1;
    padding: 10px;
    background: $neutral-color-1;
    &__content {
      height: 100%;
      background-color: $neutral-color-1;
      border-radius: 4px;
    }
    &__header {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 16px;
      margin-top: 12px;
      font-size: 14px;
      background-color: $base-white;
      border-bottom: 1px solid rgba($line-table, 0.7);
    }
    &__column {
      display: flex;
      width: 100%;
      height: calc(100% - 65px);
    }
    &__left {
      box-sizing: border-box;
      flex-shrink: 0;
      width: 400px;
      padding: 16px 16px 0 16px;
      // height: calc(100% - 80px);
      // padding: 0 24px;
      overflow-y: auto;
      border-right: 1px solid #e5e6eb;
    }
    &__right {
      flex: 1;
    }
  }
  .export {
    position: fixed;
    right: 10px;
    bottom: 100px;
    z-index: 100;
    display: flex;
    width: 160px;
    // align-items: center;
    height: 42px;
    padding: 0 3px;
    margin: 10px 20px 0 0;
    white-space: nowrap;
    background-color: $base-white;
    border-radius: 21px;
    box-shadow: 0px 0px 5px $text-main;
    .el-icon-close {
      position: absolute;
      top: 14px;
      right: 10px;
    }
  }
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100% - 80px);
  font-size: 14px;
  color: #999999;
}
</style>
