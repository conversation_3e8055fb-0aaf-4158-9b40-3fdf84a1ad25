<!--
 * @Author: zongbao.yao
 * @Date: 2021-04-25 16:59:24
 * @LastEditors: zongbao.yao
 * @LastEditTime: 2021-06-02 16:50:56
 * @Description: 音频文件处理
-->
<template>
  <div :class="{ 'chat-withdraw': revoke }">
    <div v-if="revoke" class="chat-withdraw__head">
      <div class="chat-withdraw__headl iconchehui iconfont"></div>
      <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
    </div>
    <div :class="{ 'chat-withdraw__main': revoke }">
      <!-- 撤回消息内容 -->
      <div class="chat-voice">
        <!-- 样式 -->
        <!-- :class="{ left: direction == 1, right: direction == 2 }" -->
        <!-- :class="{ left: !direction, right: direction }" -->
        <div
          class="chat-voice__button"
          :class="direction ? 'left' : 'right'"
          :style="{ width: audioInfo.width }"
          @click="toPlaying"
        >
          <!-- 语音时长 -->
          <div class="chat-voice__button--time">{{ audioInfo.duration }}"</div>
          <!-- 语音动画 -->
          <!--
            <div ref="icon" class="chat-voice__button--icon">
              <div class="icon-wifi first"></div>
              <div class="icon-wifi second"></div>
              <div class="icon-wifi third"></div>
            </div>
            -->
          <!-- 兼容mac企业微信包裹一层 -->
          <div class="chat-voice__button--icon">
            <div ref="icon" class="icon-animate">
              <div class="first icon-wifi"></div>
              <div class="icon-wifi second"></div>
              <div class="icon-wifi third"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const BenzAMRRecorder = require('benz-amr-recorder')

export default {
  name: 'ChatVoice',

  props: {
    // 控制文字方向
    // direction: {
    //   type: [Number, String],
    //   default: '1'
    // },
    direction: {
      type: Boolean,
      default: true
    },
    // 内容
    content: {
      type: Object,
      default: () => ({})
    },
    // 名字
    name: {
      type: String,
      default: ''
    },
    // 判断是否是撤回消息
    revoke: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      // props: left(1) right(2)
      // direction: 1,
      // 音频节点
      audioDOM: null,
      // 音频信息
      audioInfo: {
        duration: 0, // 播放时长
        width: '0' // 容器宽度
      }
      // props:url地址
      // src: 'http://account.tanmarket.cn/fdfsServer/group3/M00/09/7D/rBHTw2BiUQuAX0FzAAAHxQP4Akg8976435'
      // src: 'https://cdn.cnbj1.fds.api.mi-img.com/mi-mall/11c70c96529b6e6938567ec1aa0910e0.mp4'
      // src: 'https://cdn.xbongbong.com/harlooCloud/1_423811ec65927d792016ace780ee731a'
    }
  },
  created() {
    this.audioArm = new BenzAMRRecorder()
    this.init()
  },
  mounted() {
    this.$root.eventHub.$on('stopVoicePlaying', this.stopPlaying)
  },
  beforeDestroy() {
    this.$root.eventHub.$off('stopVoicePlaying')
  },
  methods: {
    /**
     * @description: 点击事件
     * @param {*}
     * @return {*}
     */
    toPlaying() {
      if (this.audioArm.isPlaying()) {
        this.audioArm.stop()
      } else {
        this.$root.eventHub.$emit('stopVoicePlaying')
        // 初始化
        this.audioArm.play()
      }
    },
    stopPlaying() {
      this.audioArm.stop()
    },
    /**
     * @description: 渲染聊天框内容
     * @param {*}
     * @return {*}
     */
    playShow(duration) {
      // 取秒(四舍五入)：
      // 取宽:最小70px = width(60) + padding(10)
      const width = `${duration * 10}px`
      // 设置展示info
      this.audioInfo = {
        duration,
        width
      }
    },
    /**
     * @description: 开启播放动画
     * @param {*}
     * @return {*}
     */
    startAnimate() {
      this.$refs.icon.classList.add('playing')
    },
    /**
     * @description: 停止播放动画
     * @param {*}
     * @return {*}
     */
    stopAnimate() {
      this.$refs.icon.classList.remove('playing')
    },
    /**
     * @description: 添加音频播放的监听
     * @param {*}
     * @return {*}
     */
    addVoiceEventListener() {
      /**
       * 播放
       * @param {Function} fn
       */
      this.audioArm.onPlay(() => {
        console.log('开始播放')
        this.startAnimate()
      })
      /**
       * 停止（包括播放结束）
       * @param {Function} fn
       */
      this.audioArm.onStop(() => {
        console.log('停止播放')
        this.stopAnimate()
      })
      /**
       * 暂停
       * @param {Function} fn
       */
      this.audioArm.onPause(() => {
        console.log('暂停')
        this.stopAnimate()
      })
      /**
       * （暂停状态中）继续播放
       * @param {Function} fn
       */
      this.audioArm.onResume(() => {
        console.log('继续播放')
        this.startAnimate()
      })
      /**
       * 播放结束
       * @param {Function} fn
       */
      this.audioArm.onEnded(() => {
        console.log('播放结束')
        this.stopAnimate()
      })
    },
    /**
     * @description: 初始化监听
     * @param {*}
     * @return {*}
     */
    init() {
      this.audioArm.initWithUrl(this.content.url)
      this.addVoiceEventListener()
      this.playShow(this.content.play_length)
    }
  }
}
</script>

<style lang="scss" scoped>
// 组件样式
.chat-voice {
  // background: pink;
  position: relative;
  display: flex;
  flex-direction: column;
  // width: 100%;
  max-width: 432px;
  margin-right: 5px;
  &__button {
    position: relative;
    display: flex;
    align-items: center;
    // 宽度限制
    min-width: 60px !important;
    // max-width: 200px !important;
    // max-width: 80% !important;
    max-width: 95% !important;
    // flex-wrap: nowrap;
    // padding: 4px 0 4px 20px;
    padding: 4px 0;
    font-size: 12px;
    cursor: pointer;
    background-color: $neutral-color-2;
    border-radius: 4px;
    //
    &--time {
      display: block !important;
      padding: 4px;
    }
    // icon
    &--icon {
      position: relative;
      box-sizing: border-box;
      overflow: hidden !important;
      .icon-animate {
        position: relative;
        width: 18px;
        height: 18px;
        overflow: hidden !important;
        & > .icon-wifi {
          position: absolute;
          border: 2px solid #6a82a4;
          border-radius: 50%;
          // transform: translate(-50%, -50%);
        }
        & > .first {
          top: -2.2px;
          left: -2.2px;
          width: 2.2px;
          height: 2.2px;
          background-color: #6a82a4;
        }
        & > .second {
          top: -5.5px;
          left: -5.5px;
          width: 10px;
          height: 10px;
        }
        & > .third {
          top: -9px;
          left: -9px;
          width: 18px;
          height: 18px;
        }
      }

      .playing {
        .second {
          animation: fadeInOut 1s infinite 0.2s;
        }

        .third {
          animation: fadeInOut 1s infinite 0.4s;
        }
      }
    }

    @keyframes fadeInOut {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }
  }

  & > .left,
  & > .right {
    &::before {
      position: absolute;
      top: 50%;
      width: 6px;
      height: 6px;
      content: '';
      background-color: $neutral-color-2;
    }
  }

  & > .left {
    align-self: flex-start;
    padding-left: 20px;
    & > .chat-voice__button {
      &--time {
        order: 2;
        color: $text-main;
      }

      &--icon {
        order: 1;
        transform: rotate(-45deg);
      }
    }

    &::before {
      position: absolute;
      left: 0;
      content: '';
      transform: rotate(45deg) translate(-75%, 0%);
    }
  }

  & > .right {
    flex-direction: row-reverse;
    padding-right: 20px;
    & > .chat-voice__button {
      &--time {
        order: 2;
        color: $text-main;
      }

      &--icon {
        order: 1;
        transform: rotate(135deg);
      }
    }

    &::before {
      position: absolute;
      right: 0;
      content: '';
      transform: rotate(45deg) translate(0%, -75%);
    }
  }
}
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  border-radius: 2px;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
</style>
