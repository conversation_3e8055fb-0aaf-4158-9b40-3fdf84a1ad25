<template>
  <div class="session-sdk-page">
    <div class="right-head">
      <div class="right-head__search">
        <el-select
          v-model="selectActiveData"
          class="block-select"
          :disabled="!params.msgType"
          placeholder="请选择"
          @change="onTypeChange"
        >
          <el-option
            v-for="item in sessionTypeSelect"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-tooltip content="仅支持搜索筛选时间内近一个月的会话内容" placement="top">
          <el-input
            v-model="nameLike"
            class="block-search"
            clearable
            :disabled="!params.msgType || !['all'].includes(selectActiveData)"
            placeholder="请输入2个字及以上的会话内容"
            size="mini"
            @clear="clearSearch"
            @keydown.native.13="searchSession"
          ></el-input>
        </el-tooltip>
      </div>
      <div
        v-if="enable && isAi"
        :class="aiParams.status === 1 ? 'right-head__aing' : 'right-head__ai'"
        @click="getAi"
      >
        <i class="icon-zhinengzhaiyao t-iconfont"></i>
        {{ aiParams.status === 1 ? '提炼中...' : 'AI 智能摘要提炼' }}
      </div>
    </div>
    <div v-if="aiParams.status === 2 && enable && isAi" class="session-sdk-page__ai">
      <AiWindow :params="aiParams"></AiWindow>
    </div>
    <div class="session-scroll-list-sdk">
      <div ref="scrollBox" class="scroll-box"></div>
    </div>
  </div>
</template>

<script>
import {
  getSessionArchiveV2,
  getQueryWord,
  getAiSummary,
  getAiSummaryResult,
  enableAiSummary
} from '@/api/scrm/sales-manage/session-archive.js'
import AiWindow from '@/views/scrm/sales-manage/session-archive-detail/components/ai-window.vue'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'SessionScrollListSdk',

  components: {
    AiWindow
  },

  props: {
    // 外部传入参数
    params: {
      type: Object,
      required: true,
      default: () => ({
        sessionUserId: '',
        weChatMsgId: '',
        sessionType: '',
        msgType: ''
      })
    },
    sessionTypeSelect: {
      type: Array,
      default: () => [{ text: '全部', value: 'all' }]
    },
    selectActive: {
      type: String,
      default: 'all'
    },
    isAi: {
      type: Boolean,
      default: true
    },
    firstTabActive: {
      type: String,
      default: ''
    },
    searchEndTime: {
      type: [String, Number],
      default: ''
    },
    // from
    from: {
      type: Number,
      default: undefined
    }
  },

  data() {
    return {
      // 滚动加载方向
      pageNext: 'all',
      // 滚动加载临界数据msgId
      weChatMsgId: '',
      // 会话列表
      sessionList: [],
      // 加载lock
      topLoading: false, // 加载中
      bottomLoading: false, // 加载中
      // 数据是否加载完毕
      hasLeft: true,
      hasRight: true,
      // sdk实例
      instance: null,
      idGt: '',
      idLt: '',
      nameLike: '',
      aiParams: {
        status: 0, //0无任务，1进行中，2已完成
        resultId: '', //status=2时返回,用会话展示组件的 ww-open-result-text 模板组件进行展示
        secretKey: '', //status=2时返回,用会话展示组件的 ww-open-result-text 模板组件进行展示
        time: 0, //任务开始时间 时间戳
        ttl: 0 //任务剩余有效时间 秒
      },
      timer: null,
      enable: false,
      searchList: [],
      searchPosition: false,
      isSearch: false // 是否在搜索状态
      // sessionImg: require('../../../../../assets/session-no-search.png')
    }
  },

  computed: {
    searchData() {
      let params = {
        ...this.params,
        pageNext: this.pageNext,
        hasLeft: this.hasLeft,
        hasRight: this.hasRight
      }
      // 如果页面内部触发了滚动加载，则启用内部的msgId
      if (this.weChatMsgId) {
        params = { ...params, weChatMsgId: this.weChatMsgId }
      }
      return params
    },
    selectActiveData: {
      get() {
        return this.selectActive
      },
      set(val) {
        this.$emit('update:selectActive', val)
      }
    }
  },

  watch: {
    // 二级菜单切换时触发更新
    params: {
      async handler(newVal) {
        console.log('监听到sdk入参变动了：', newVal)
        if (newVal && newVal.msgType) {
          await this.refresh()
          this.isAi && this.getAiResult()
        } else {
          this.sessionList = []
          this.instance.setData({
            msgList: []
          })
        }
      },
      deep: true
    },
    nameLike(newVal, oldVal) {
      console.log('nameLike:', newVal, oldVal, newVal.length < 2)
      // 如果没有选择二级菜单，不可搜索
      if (!this.params.sessionType) return
      // 如果新搜索值为空，需要请求详情接口而非搜索接口
      if (!newVal) {
        this.refresh(this.weChatMsgId)
      } else if (newVal && newVal !== oldVal) {
        if (newVal.length < 2) return
        this.searchSession()
      }
    }
  },

  created() {
    window.ww.initOpenData()
  },
  mounted() {
    setTimeout(() => {
      this.init()
    }, 100)
  },

  methods: {
    // 获取会话消息list
    getSessionArchive(load = false, from) {
      console.log('获取会话消息list:', this.searchData, this.sensitiveWord)
      // 清空列表的提示
      this.instance.setData({
        noData: false,
        isMore: false
      })
      const params = {
        ...this.searchData,
        idLt: this.idLt,
        idGt: this.idGt
      }
      // 如果是点击搜索的定位
      // if(this.searchPosition) {
      //   params.idLt = ''
      //   params.idGt = ''
      //   params.msgType = 'all'
      //   params.hasLeft = true
      //   params.hasRight = true
      //   params.from = 1
      // } else if (from && )
      if (this.searchPosition) {
        params.idLt = ''
        params.idGt = ''
        params.msgType = 'all'
        params.pageNext = 'all'
        params.hasLeft = true
        params.hasRight = true
        params.from = this.from || (this.firstTabActive === 'sensitiveWord' ? 2 : 1)
      } else {
        params.idLt = this.idLt
        params.idGt = this.idGt
        params.from = this.from
        if (this.firstTabActive === 'sensitiveWord') params.from = 2
      }

      try {
        getSessionArchiveV2(params).then((res) => {
          // 关闭锁
          const {
            pageHelper: { hasLeft, hasRight },
            idGt,
            idLt
          } = res.result

          this.$emit('updateSelect', res.result.menuList, this.selectActive)
          this.searchPosition = false

          const messageList = res.result.messageList.map((item) => {
            const timeFormate = this.fomatDateSDK(item.time)
            return {
              ...item,
              time: timeFormate
            }
          })

          this.idGt = idGt
          this.idLt = idLt
          // 滚动加载完毕状态
          !hasLeft && (this.hasLeft = hasLeft)
          !hasRight && (this.hasRight = hasRight)
          console.log('messageList', messageList, hasLeft, hasRight)

          if (load) {
            console.log('滚动加载开启')
            // 滚动加载开启
            if (this.pageNext === 'left') {
              // 向上滚动加载
              this.sessionList.unshift(...messageList)
              this.topLoading = false
              // 数据加载成功后，将滚动条腾出top位置
              // this.$nextTick(() => {
              //   const scrollbar = this.$refs.scrollbar.wrap
              //   // 根据当前返回的数据条数，定位dom，设置滚动高度
              //   const lastFirstDOM =
              //     messageList.length && scrollbar.children[0].children[messageList.length + 1]
              //   messageList.length &&
              //     (this.$refs.scrollbar.wrap.scrollTop =
              //       lastFirstDOM.offsetTop + lastFirstDOM.offsetHeight)
              // this.autoFillScrollBox()
              // })
            } else if (this.pageNext === 'right') {
              // 向下滚动加载
              this.sessionList.push(...messageList)
              this.bottomLoading = false
              // this.autoFillScrollBox()
            }
          } else {
            console.log('未开启滚动加载')
            // 未开启滚动加载
            this.sessionList = messageList
            this.topLoading = false
            this.bottomLoading = false
            if (!messageList.length) {
              this.instance.setData({
                noData: true
              })
            }
          }
          // 判断有没有更多数据
          const isMore = !hasLeft && !hasRight
          // 解除搜索状态
          this.isSearch = false
          // 注入数据到sdk
          console.log('messageList:', messageList)
          this.instance.setData({
            sessionUserId: this.params.sessionUserId,
            msgList: this.sessionList,
            isMore: isMore
          })

          // if (messageList.length < 20) {
          //   if ((this.hasRight && this.hasLeft) || (this.hasRight && !this.hasLeft)) {
          //     this.onScrollLoadingBottom()
          //   } else if (!this.hasRight && this.hasLeft) {
          //     this.onScrollLoadingTop()
          //   }
          //   // this.hasRight ? this.onScrollLoadingBottom() : this.onScrollLoadingTop()
          // }
        })
      } catch (error) {
        console.log('获取会话消息list-getSessionArchive:', error)
      }
    },
    // 滚动加载处理[上滚动]
    @xbb.debounceWrap(500)
    onScrollLoadingTop() {
      console.log('left------------', this.hasLeft)
      if (!this.hasLeft || this.nameLike) return
      // 将当前状态标识为加载中
      this.topLoading = true
      // 向上滚动
      this.pageNext = 'left'
      // 查找顶部数据msgId
      this.weChatMsgId = this.sessionList[0].weChatMsgId
      // 请求
      this.getSessionArchive(true)
    },
    // 滚动加载处理[下滚动]
    @xbb.debounceWrap(500)
    onScrollLoadingBottom() {
      console.log('right++++++', this.hasRight)
      if (!this.hasRight || this.nameLike) return
      // 将当前状态标识为加载中
      this.bottomLoading = true
      // 向下滚动
      this.pageNext = 'right'
      // 查找顶部数据msgId
      this.weChatMsgId = this.sessionList.slice(-1)[0].weChatMsgId
      // 请求
      this.getSessionArchive(true)
    },
    // 搜索会话详情
    @xbb.debounceWrap(200)
    searchSession(isGetMore) {
      console.log(
        '搜索会话详情-searchSession:',
        isGetMore,
        typeof isGetMore === 'string',
        typeof isGetMore
      )
      if (this.nameLike.length < 2 || !this.nameLike) {
        this.$message({
          message: '搜索请输入 2 个字及以上的会话内容'
        })
        return
      }
      this.instance.setData({
        noData: false,
        isMore: false
      })
      const params = {
        ...this.params,
        queryWord: this.nameLike
      }
      if (this.searchEndTime) params.endTime = this.searchEndTime
      if (isGetMore && typeof isGetMore === 'string') params.cursor = isGetMore
      getQueryWord(params).then(({ result }) => {
        if (!result.messageList.length && !(isGetMore && typeof isGetMore === 'string')) {
          this.instance.setData({
            noData: true
          })
        }
        this.isSearch = true
        // 搜索后清空详情的搜索游标
        this.idLt = ''
        this.idGt = ''
        console.log('isGetMore：', isGetMore)
        if (isGetMore && typeof isGetMore === 'string') {
          this.searchList.push(...result.messageList)
        } else {
          this.searchList = [...result.messageList]
        }
        //记录 || 清空游标
        console.log('还有更多搜索内容，记录游标:', result.messageList, this.searchList)
        this.instance.setData({
          msgList: this.searchList,
          isGetMore: result.hasMore ? result.nextCursor : ''
        })
      })
    },
    // 点击ai创建
    getAi() {
      const { visitId, type } = this.getVisit()
      getAiSummary({ seatId: this.params.sessionUserId, visitId, type }).then((res) => {
        // this.$message.success('创建ai成功')
        this.aiParams.status = 1
        this.getAiResult()
      })
    },
    // 获取ai结果
    getAiResult() {
      const { visitId } = this.getVisit()
      getAiSummaryResult({ seatId: this.params.sessionUserId, visitId }).then(({ result }) => {
        if (result.status === 1 && !this.timer) {
          this.timer = setInterval(() => {
            this.getAiResult({ seatId: this.params.sessionUserId, visitId })
          }, 5000)
        } else if (result.status !== 1) {
          this.timer && clearInterval(this.timer)
          this.timer = null
        }
        // this.aiParams = { ...result }
        this.$set(this.aiParams, 'status', result.status)
        result.resultId && this.$set(this.aiParams, 'resultId', result.resultId)
        result.secretKey && this.$set(this.aiParams, 'secretKey', result.secretKey)
        result.isSpec && this.$set(this.aiParams, 'isSpec', result.isSpec)
        result.specResult && this.$set(this.aiParams, 'specResult', result.specResult)
      })
    },
    // 选择会话类型
    onTypeChange(val) {
      this.selectActiveData = val
      this.$emit('onTypeChange', val)
    },
    // 初始化容器
    init() {
      console.log('初始化容器')
      const box = this.$refs.scrollBox
      const _that = this
      const factory = window.ww.createOpenDataFrameFactory()
      const instance = factory.createOpenDataFrame({
        el: box, // “容器”元素，用于挂载展示组件
        template: `
          <scroll-view
            bindscrolltoupper="onScrollToupper"
            bindscrolltolower="onScrollTolower"
            scroll-y="{{true}}"
            class="scroll-box"
            style="height: 100%;"
            wx:if="{{data.msgList.length}}"
          >
            <view
              wx:for="{{data.msgList}}"
              wx:key="msg_id"
              class="msg"
              data-index="{{index}}"
              data-info="item"
              data-msg="{{item}}"
              bindclick="handleClickEvent"
              scroll-y="{{ true }}"
            >
              <view class="{{ !item.position ? 'scroll-item left': 'scroll-item right' }}">
                <span class="scroll-item__avatar">
                  <ww-open-data type="{{item.sessionIdAvatar}}" openid="{{item.sessionId}}"></ww-open-data>
                </span>
                <view class="scroll-item__box">
                  <view class="scroll-item__box-title">
                    <view wx:if="{{item.sessionRemarkName}}">{{item.sessionRemarkName}}</view>
                    <span wx:if="{{!item.sessionRemarkName}}">
                      <ww-open-data type="{{item.sessionIdName}}" openid="{{item.sessionId}}"></ww-open-data>
                    </span>
                    <span class="{{ item.type === 1 ? 'name-wx' : item.type === 2 ? 'name-qw' : 'name-other' }}" wx:if="{{item.nameSuffix}}">{{ item.nameSuffix }}</span>
                    <span class="scroll-item__box-title--time">{{ item.time }}</span>
                  </view>
                  <view class="scroll-item__box-content">
                    <span class="scroll-item__box-info"><ww-open-message message-id="{{item.msgId}}" secret-key="{{item.decryptedKey}}" open-type="viewMessage" highlight-text="{{data.hightMsg}}"/></span>
                  </view>
                  <view class="tag-list" wx:if="{{item.extInfo}}">
                    <view class="spam" wx:if="{{item.extInfo.spamResult}}">{{item.extInfo.spamResult}}</view>
                    <view class="sentiment" wx:if="{{item.extInfo.sentimentResult}}">{{item.extInfo.sentimentResult}}</view>
                    <view class="sensitive" wx:if="{{item.extInfo.sensitiveResult}}">{{item.extInfo.sensitiveResult}}</view>
                  </view>
                </view>
              </view>
            </view>
            <view wx:if="{{data.isGetMore}}" bindclick="getMore" class="get-more">加载更多</view>
            <view wx:if="{{data.isMore}}" class="is-more">没有更多了</view>
          </scroll-view>
          <view wx:if="{{data.noData && !data.msgList.length}}" class="no-data">
            <image src="data:image/png;base64,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" width="350px" class="no-data-img"></image>
          </view>
        `,
        style: `
          .no-data {
            display: flex; justify-content: center;
          }
          .no-data-img { width: 450px; }
          .get-more, .is-more {
            text-align: center;
            color: #646566;
            padding-bottom: 12px;
            background-color: #fff;
            font-size: 12px;
          }
          .scroll-box { height: 100%; min-height: 400px; }
          .msg { margin-bottom: 12px; }

          /* item 会话结构 */
          .scroll-item { box-sizing: border-box; display: flex; width: 100%; padding: 16px 24px; }
          .left { flex-direction: row !important; }
          .right { flex-direction: row-reverse !important; }

          /* item 会话结构-头像 */
          .scroll-item__avatar {
            diplay: inline-block;
            width: 36px;
            height: 36px;
            background: pink;
            border-radius: 4px;
            overflow: hidden;
            flex-shrink: 0;
          }

          /* item 会话结构-title第一行样式 */
          .scroll-item__box {
            margin: 0 12px;
          }
          .scroll-item__box-title {
            display: flex;
            padding-bottom: 8px;
            color: #969799;
            font-size: 14px;
          }
          .right .scroll-item__box .scroll-item__box-title {
            justify-content: flex-end;
          }

          /* item 会话结构-title-公用样式 */
          .name-wx, .name-qw, .name-other, .scroll-item__box-title--time {
            margin-left: 8px;
          }
          .scroll-item__box-title--time {
            display: inline-block;
          }
          .scroll-item__box-title .name-wx {
            color: #00C241;
          }
          .scroll-item__box-title .name-qw {
            color: #FF6A00;
          }
          .scroll-item__box-title .name-other {
            color: #186FD2;
          }

          /* 会话内容 */
          .scroll-item .scroll-item__box .scroll-item__box-content {
            margin-bottom: 8px;
          }
          .scroll-item__box .scroll-item__box-info img { max-width: 150px; }
          .scroll-item__box .scroll-item__box-info video { max-width: 200px; }
          .left .scroll-item__box .scroll-item__box-content {
            display: flex;
          }
          .left .scroll-item__box .scroll-item__box-info {
            background: #F2F3F5;
            padding: 8px 12px;
            border-radius: 4px;
            overflow: hidden;
            display: inline-block;
          }
          .right .scroll-item__box .scroll-item__box-content {
            display: flex;
            flex-direction: row-reverse;
          }
          .right .scroll-item__box .scroll-item__box-info {
            background: #E8F7FF;
            padding: 8px 12px;
            border-radius: 4px;
            overflow: hidden;
            display: inline-block;
          }

          /* tag */
          .tag-list { display: flex; align-items: center;}
          .tag-list .spam, .tag-list .sentiment, .tag-list .sensitive {
            font-size: 12px;
            padding: 4px 10px;
            border-radius: 4px;
            text-align: center;
            flex-shrink: 0;
            box-sizing: border-box;
          }
          .left .tag-list .spam, .left .tag-list .sentiment, .left .tag-list .sensitive {
            margin-right: 4px;
          }
          .right .tag-list .spam, .right .tag-list .sentiment, .right .tag-list .sensitive {
            margin-left: 4px;
          }
          .right .tag-list { justify-content: flex-end; }
          .tag-list .spam { color: #F7716C; background: #FFECE8; }
          .tag-list .sentiment {
            color: #FFC849;
            background: #FFFBE8;
          }
          .tag-list .sensitive {
            color: #FF8C2E;
            background: #FFF5E8;
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        `,
        data: {
          msgList: [],
          sessionUserId: '',
          isGetMore: '',
          // sessionNoData: _that.sessionImg,
          noData: false,
          isMore: false,
          hightMsg: '',
          weChatMsgId: ''
        },
        methods: {
          // 响应消息点击事件
          handleClickEvent(e) {
            console.log('响应消息点击事件', e, this.data.msgList)
            if (
              _that.nameLike &&
              _that.nameLike.length >= 2 &&
              this.data.msgList.length &&
              _that.isSearch
            ) {
              _that.weChatMsgId = e.currentTarget.dataset.msg.weChatMsgId
              this.data.hightMsg = _that.nameLike
              _that.searchPosition = true
              _that.nameLike = ''
              this.data.weChatMsgId = e.currentTarget.dataset.msg.weChatMsgId
            }
          },
          // 向上滚动加载
          onScrollToupper() {
            console.log('向上滚动加载')
            _that.onScrollLoadingTop()
          },
          // 向下滚动加载
          onScrollTolower() {
            console.log('向下滚动加载')
            _that.onScrollLoadingBottom()
          },
          // 搜索加载更多
          getMore() {
            _that.searchSession(this.data.isGetMore)
          }
        }
      })
      this.instance = instance
      box.children[0].style = 'width: 100%;height: 100%;' // iframe充满
      this.$emit('initEnd')
      this.isAi &&
        enableAiSummary().then(({ result }) => {
          this.enable = result.enable
        })
    },
    refresh(msgId) {
      if (!this.params.msgType) return
      // 清空滚动加载的msgId
      this.weChatMsgId = msgId || ''
      this.pageNext = 'all'
      this.nameLike = ''
      // 重置滚动加载lock
      this.topLoading = true
      this.bottomLoading = true
      // 重置滚动加载完成状态
      this.hasLeft = true
      this.hasRight = true
      // 清空滚动加载计数器
      this.idGt = ''
      this.idLt = ''
      // 解除搜索状态
      this.isSearch = false
      // 清空
      this.sessionList = []
      this.instance.setData({
        msgList: [],
        noData: false,
        isMore: false,
        isGetMore: '',
        sessionUserId: ''
      })
      this.aiParams = {
        tatus: 0,
        resultId: '',
        secretKey: '',
        time: 0,
        ttl: 0
      }
      if (this.timer) clearInterval(this.timer)
      this.getSessionArchive()
    },
    getVisit() {
      let visitId = ''
      let type = 0
      if (this.params.sessionType === 'externalSession') {
        visitId = this.params.externalUserId
        type = 1
      } else if (this.params.sessionType === 'internalSession') {
        visitId = this.params.otherUserId
        type = 2
      } else {
        visitId = this.params.chatId
        type = 3
      }
      return { visitId, type }
    },
    fomatDateSDK(val) {
      return xbb.formatTime(val, 'yyyy-MM-dd HH:mm:ss')
    },
    clearSearch() {}
  },
  beforeDestroy() {
    if (this.timer) clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
.session-sdk-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: $base-white;
  &__ai {
    margin: 16px;
  }
  .right-head {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 550px;
    height: 47px;
    padding: 8px 16px;
    border-bottom: 1px solid $neutral-color-3;
    &__search {
      display: flex;
      align-items: center;
      :deep(.block-select) {
        width: 104px;
        margin-right: 16px;
      }
      :deep(.block-search) {
        width: 240px;
        height: 32px;
      }
      :deep(.el-input--mini .el-input__inner) {
        height: 32px !important;
        line-height: 32px !important;
      }
    }
    &__ai,
    &__aing {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      font-size: 12px;
      color: $base-white;
      cursor: pointer;
      border-radius: 8px;
      .icon-zhinengzhaiyao {
        margin-right: 4px;
      }
    }
    &__ai {
      background: linear-gradient(103deg, rgba(0, 115, 255, 0.8) 0%, rgba(0, 166, 255, 0.8) 100%);
    }
    &__aing {
      background: linear-gradient(109deg, rgba(0, 115, 255, 0.5) 0%, rgba(0, 166, 255, 0.5) 100%);
    }
    .right-head__ai:hover {
      background: linear-gradient(103deg, #0073ff 0%, #00a6ff 100%);
    }
    .ai-active {
      background: linear-gradient(109deg, rgba(0, 115, 255, 0.5) 0%, rgba(0, 166, 255, 0.5) 100%);
    }
  }
  .session-scroll-list-sdk {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
    .scroll-box {
      position: absolute;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
