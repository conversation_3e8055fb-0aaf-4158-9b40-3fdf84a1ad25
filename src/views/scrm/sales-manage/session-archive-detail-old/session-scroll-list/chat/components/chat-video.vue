<!--
 * @Author: qian<PERSON><PERSON>.<PERSON><PERSON>
 * @Date: 2021-04-25 16:32:59
 * @LastEditors: qianqian.zhao
 * @LastEditTime: 2021-04-28 16:30:54
 * @Description:
-->
<template>
  <div :class="{ 'chat-withdraw': revoke }">
    <div v-if="revoke" class="chat-withdraw__head">
      <div class="chat-withdraw__headl iconchehui iconfont"></div>
      <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
    </div>
    <div :class="{ 'chat-withdraw__main': revoke }">
      <!-- 撤回消息内容 -->
      <div class="chat-video" @click="videoBtn">
        <div class="chat-video__head">
          <div class="chat-video__headl iconfont iconshipin"></div>
          <div class="chat-video__headr">{{ content.url }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatVideo',
  props: {
    // 内容
    content: {
      type: Object,
      default: () => ({})
    },
    // 名字
    name: {
      type: String,
      default: ''
    },
    // 判断是否是撤回消息
    revoke: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    // 点击跳转视频
    videoBtn() {
      window.open(this.content.url)
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-video {
  box-sizing: border-box;
  padding: 8px;
  text-align: left;
  cursor: pointer;
  background-color: $neutral-color-2;
  &__head {
    display: flex;
  }
  &__headl {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: $link-base-color-6;
  }
  &__headr {
    max-width: 260px;
    overflow: hidden;
    color: $link-base-color-6;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  border-radius: 2px;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
</style>
