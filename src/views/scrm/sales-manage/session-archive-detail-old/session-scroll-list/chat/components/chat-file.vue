<!--
 * @Author: qianqi<PERSON>.zhao
 * @Date: 2021-05-06 08:44:40
 * @LastEditors: qianqian.zhao
 * @LastEditTime: 2021-05-06 10:32:45
 * @Description:
-->
<template>
  <div :class="{ 'chat-withdraw': revoke }">
    <div v-if="revoke" class="chat-withdraw__head">
      <div class="chat-withdraw__headl iconchehui iconfont"></div>
      <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
    </div>
    <div :class="{ 'chat-withdraw__main': revoke }">
      <!-- 撤回消息内容 -->
      <div class="chat-file" @click="btnFile">
        <div class="chat-file__img">
          <div v-if="content.fileext === 'jpg'" class="iconfont icontupian1"></div>
          <div v-else-if="content.fileext === 'pdf'" class="iconfont iconmorenwenjian"></div>
          <div v-else-if="content.fileext === 'mp4'" class="iconfont iconshipin1"></div>
          <div v-else-if="content.fileext === 'mp3'" class="iconfont iconyinpin"></div>
          <div v-else class="iconfont iconwendang"></div>
        </div>
        <div class="chat-file__content">
          <div class="chat-file__text">{{ content.filename }}</div>
          <div class="chat-file__size">{{ (content.filesize / 1024 / 1024).toFixed(1) }}M</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatFile',
  props: {
    // 内容
    content: {
      type: Object,
      default: () => ({})
    },
    // 判断是否是撤回信息
    revoke: {
      type: Boolean,
      default: false
    },
    // 名称
    name: {
      type: String,
      default: ''
    }
  },
  methods: {
    // 跳转
    btnFile() {
      window.open(this.content.url)
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-file {
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 12px;
  cursor: pointer;
  background-color: $neutral-color-2;
  &__text {
    display: -webkit-box;
    max-width: 214px;
    margin-left: 8px;
    overflow: hidden;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  &__size {
    margin-top: 4px;
    margin-left: 8px;
  }
}
.icontupian1 {
  font-size: 36px;
  color: $warning-base-color-6;
}
.iconwendang {
  font-size: 36px;
  color: $success-color-5;
}
.iconmorenwenjian {
  font-size: 36px;
  color: $text-grey;
}
.iconyinpin {
  font-size: 36px;
  color: $error-color-5;
}
.iconshipin1 {
  font-size: 36px;
  color: $link-base-color-6;
}
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  border-radius: 2px;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
</style>
