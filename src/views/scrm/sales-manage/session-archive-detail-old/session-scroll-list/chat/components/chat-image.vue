<!--
 * @Author: q<PERSON><PERSON><PERSON>.z<PERSON>
 * @Date: 2021-04-22 17:01:55
 * @LastEditors: zongbao.yao
 * @LastEditTime: 2021-11-03 15:57:47
 * @Description:
-->
<template>
  <div class="chat-image">
    <div class="chat-text">
      <div :class="{ 'chat-withdraw': revoke }">
        <div v-if="revoke" class="chat-withdraw__head">
          <div class="chat-withdraw__headl iconchehui iconfont"></div>
          <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
        </div>
        <div :class="{ 'chat-withdraw__main': revoke }">
          <!-- 撤回消息内容 -->
          <div class="demo-image__preview">
            <el-image class="image-preview" :src="content.url" @click="imagePreview">
              <div slot="error" class="image-slot">
                <img :src="defaultImg" />
              </div>
            </el-image>
            <lg-preview
              v-if="isPreviewShow"
              :list="[content.url]"
              @close="isPreviewShow = false"
            ></lg-preview>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="demo-image__preview">
      <el-image
        style="width: 100px; height: 100px"
        :src="content.url"
        :preview-src-list="[content.url]">
      </el-image>
    </div> -->
  </div>
</template>

<script>
export default {
  name: 'ChatImage',
  components: {
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },
  props: {
    // 内容
    content: {
      type: Object,
      default: () => ({})
    },
    // 名字
    name: {
      type: String,
      default: ''
    },
    // 判断
    revoke: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isPreviewShow: false,
      defaultImg: require('@/assets/scrm/default-image.jpg')
    }
  },
  methods: {
    imagePreview() {
      this.isPreviewShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .image-preview {
    width: 200px;
    cursor: pointer;
  }
}
</style>
