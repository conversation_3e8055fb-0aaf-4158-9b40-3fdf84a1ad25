<!--
 * @Author: qianqi<PERSON>.<PERSON><PERSON>
 * @Date: 2021-04-25 14:53:15
 * @LastEditors: qianqian.zhao
 * @LastEditTime: 2021-05-07 16:49:08
 * @Description:
-->
<template>
  <div class="chat-link" @click="chatLinkBtn">
    <div :class="{ 'chat-withdraw': revoke }">
      <div v-if="revoke" class="chat-withdraw__head">
        <div class="chat-withdraw__headl iconchehui iconfont"></div>
        <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
      </div>
      <div :class="{ 'chat-withdraw__main': revoke }">
        <!-- 撤回消息内容 -->
        <div class="chat-link__header">{{ content.title }}</div>
        <div class="chat-link__main">
          <div class="chat-link__mainl">{{ content.description }}</div>
          <div class="chat-link__mainr">
            <img alt="" :src="content.image_url" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatLink',

  props: {
    // 内容
    content: {
      type: Object,
      default: () => ({})
    },
    // 名字
    name: {
      type: String,
      default: ''
    },
    // 判断是否撤回消息
    revoke: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 点击跳转链接
    chatLinkBtn() {
      if (this.content.link_url.includes('http')) {
        window.open(this.content.link_url)
      } else {
        const windowHttp = 'https://' + this.content.link_url
        window.open(windowHttp)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-link {
  box-sizing: border-box;
  max-width: 283px;
  padding: 12px;
  text-align: left;
  cursor: pointer;
  background-color: $neutral-color-2;
  &__header {
    overflow: hidden;
    font-size: 14px;
    color: $text-plain;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__main {
    display: flex;
    margin-top: 8px;
  }
  &__mainl {
    display: -webkit-box;
    width: 207px;
    height: 32px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  &__mainr {
    width: 40px;
    height: 40px;
    margin-left: 12px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  border-radius: 2px;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
</style>
