<!--
 * @Author: q<PERSON><PERSON><PERSON>.<PERSON><PERSON>
 * @Date: 2021-04-22 14:08:53
 * @LastEditors: zongbao.yao
 * @LastEditTime: 2021-06-02 16:53:18
 * @Description:
-->
<template>
  <div :class="{ 'chat-withdraw': revoke }">
    <div v-if="revoke" class="chat-withdraw__head">
      <div class="chat-withdraw__headl iconchehui iconfont"></div>
      <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
    </div>
    <div :class="{ 'chat-withdraw__main': revoke }">
      <!-- 撤回消息内容 -->
      <div class="chat-text">
        <div
          class="chat-text__paragraph"
          :class="direction ? 'chat-text__left' : 'chat-text__right'"
        >
          {{ content.content }}
        </div>
        <!-- :class="{'chat-text__left':direction == 1,'chat-text__right':direction == 2}">{{content.content}}</div> -->
      </div>
    </div>
  </div>
  <!-- 字段左 -->
  <!-- <div
      class="chat-text__paragraph "
      :class="{'chat-text__left':direction == 1,'chat-text__right':direction == 2}">{{content.content}}</div>
    </div> -->
</template>

<script>
export default {
  name: 'ChatText',

  props: {
    // 控制文字方向
    // direction: {
    //   type: [Number, String],
    //   default: '1'
    // },
    direction: {
      type: Boolean,
      default: true
    },
    // 内容
    content: {
      type: Object,
      default: () => ({})
    },
    // 撤回判断  revoke
    revoke: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-text {
  &__paragraph {
    box-sizing: border-box;
    display: inline-block;
    max-width: 432px;
    padding: 10px 12px 10px 14px;
    font-size: 14px;
    // color: $text-main;
    color: #6a82a4;
    word-break: break-all;
    // background-color: rgba($link-base-color-6, .2);
    background-color: $neutral-color-2;
  }
  &__left {
    text-align: left;
    border-radius: 0 6px 6px 6px;
  }
  &__right {
    // text-align: right;
    border-radius: 6px 0 6px 6px;
  }
}
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  border-radius: 2px;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
</style>
