<!--
 * @Author: qianqi<PERSON>.<PERSON><PERSON>
 * @Date: 2021-04-25 15:51:39
 * @LastEditors: qianqian.zhao
 * @LastEditTime: 2021-04-27 15:34:35
 * @Description:
-->
<template>
  <div :class="{ 'chat-withdraw': revoke }">
    <div v-if="revoke" class="chat-withdraw__head">
      <div class="chat-withdraw__headl iconchehui iconfont"></div>
      <div class="chat-withdraw__headr">{{ name }} 撤回了一条消息</div>
    </div>
    <div :class="{ 'chat-withdraw__main': revoke }">
      <!-- 撤回消息内容 -->
      <div class="chat-program">
        <div class="chat-program__head">
          <div class="chat-program__headl iconfont iconxiaochengxu"></div>
          <div class="chat-program__headr">{{ content.title }}</div>
        </div>
        <div class="chat-program__main">{{ content.displayname }}（小程序）</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatProgram',
  props: {
    content: {
      type: Object,
      default: () => ({})
    },
    // 名字
    name: {
      type: String,
      default: ''
    },
    // 判断是否是撤回消息
    revoke: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.chat-program {
  width: 277px;
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: $link-base-color-6;
  }
  &__headr {
    max-width: 238px;
    margin-right: 7px;
    overflow: hidden;
    font-size: 14px;
    color: $text-plain;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__main {
    display: -webkit-box;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
.chat-withdraw {
  padding: 8px 8px 8px 8px;
  text-align: left;
  background-color: $neutral-color-2;
  border-radius: 2px;
  &__head {
    display: flex;
  }
  &__headl {
    margin-right: 8px;
    line-height: 20px;
    color: #ccc;
  }
  &__headr {
    margin-right: 7px;
    font-size: 14px;
    color: $text-plain;
  }
  &__main {
    display: -webkit-box;
    margin-top: 6px;
    margin-left: 24px;
    overflow: hidden;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: left;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
</style>
