<!--
 * @Author: xiao.feng
 * @Date: 2021-08-23 15:40:37
 * @LastEditors: xiao.feng
 * @LastEditTime: 2021-11-10 14:20:38
 * @Description:筛选条件和按钮
-->
<template>
  <div class="moment-operation">
    <div class="moment-operation__left">
      <el-date-picker
        v-model="selectTime"
        end-placeholder="结束日期"
        range-separator="至"
        size="small"
        start-placeholder="开始日期"
        style="margin-right: 24px"
        type="datetimerange"
        value-format="timestamp"
        @change="getList"
      >
        <span slot="range-separator" class="moment-operation__date">至</span>
      </el-date-picker>
      <el-select
        v-model="creator"
        clearable
        default-first-option
        filterable
        :loading="isLoading"
        :placeholder="businessType === 101401 ? '搜索创建人' : '搜索发布人'"
        remote
        :remote-method="searchMember"
        reserve-keyword
        size="small"
        style="width: 240px; margin: 0"
        value-key="userId"
        @change="getList"
        @clear="searchMember"
      >
        <el-option
          v-for="item in userList"
          :key="item.userId"
          :label="item.name"
          :value="item.userId"
        >
        </el-option>
      </el-select>
    </div>
    <div class="moment-operation__right">
      <div v-show="activeName === 'releaseRecord'" class="moment-operation__right-question">
        <el-popover placement="top-start" trigger="hover" width="309">
          <div>
            <div>
              <i class="el-icon-warning-outline" style="color: #e6a23c"></i>
              无互通账号的成员完成任务的记录无法被同步到列表中,可联系管理员处理
            </div>
            <div style="float: right"><a @click="toMore">了解更多</a></div>
          </div>
          <i slot="reference" class="web-icon-question-circle web-iconfont"></i>
        </el-popover>
      </div>
      <el-button
        v-for="item in buttonEnum.filter((item) => {
          return item.permission
        })"
        :key="item.name"
        class="moment-operation__button"
        size="small"
        :type="item.type"
        @click="item.fun"
      >
        {{ item.text }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { getCommonUserList, getCommonButtonList } from '@/api/scrm/common.js'
import { ENUM_GUIDE_LICENSE } from '@/views/scrm/constants/enum.license.js'

export default {
  name: 'MomentOperation',
  components: {},
  props: {
    businessType: {
      type: Number
    },
    activeName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      buttonEnum: [
        {
          name: 'refresh',
          text: '刷新列表',
          type: 'plain',
          fun: this.getList,
          permission: false
        },
        {
          name: 'export',
          text: '导出',
          permission: false,
          type: 'plain',
          fun: this.exportList
        },
        {
          name: 'add',
          text: '添加发布任务',
          permission: false,
          type: 'primary',
          fun: this.addMonent
        }
      ],
      selectTime: [],
      creator: '',
      isLoading: false,
      userList: [] // 员工列表
    }
  },
  computed: {
    startTime() {
      return this.selectTime ? Math.floor(this.selectTime[0] / 1000) : ''
    },
    endTime() {
      return this.selectTime ? Math.floor(this.selectTime[1] / 1000) : ''
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.searchMember()
    this.getCommonButtonList()
  },
  methods: {
    // 获取员工列表
    searchMember(nameLike = '') {
      this.isLoading = true
      getCommonUserList({ nameLike: nameLike }).then((res) => {
        this.userList = res.result.userList
        this.isLoading = false
      })
    },
    // 获取按钮权限
    getCommonButtonList() {
      getCommonButtonList({ businessType: this.businessType }).then((res) => {
        const buttonList = res.result.topList.map((item) => {
          return item.key
        })
        this.buttonEnum.forEach((item) => {
          if (buttonList.includes(item.name)) {
            item.permission = true
          }
        })
      })
    },
    // 筛选条件点击确定
    getList() {
      const data = {
        startTime: this.startTime,
        endTime: this.endTime,
        creator: this.creator
      }
      this.$emit('getList', data)
    },
    // 导出表格
    exportList() {
      this.$emit('exportList')
    },
    // 添加发布任务
    addMonent() {
      this.$emit('addMonent')
    },
    // 重制筛选条件
    refreshSelect() {
      this.selectTime = []
      this.creator = ''
    },
    //了解更多
    toMore() {
      window.open(ENUM_GUIDE_LICENSE.ACCOUNT)
    }
  }
}
</script>

<style scoped lang="scss">
.moment-operation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  &__button {
    height: 32px;
    padding: 0 16px;
    margin-left: 16px;
    font-size: 14px;
  }
  &__left,
  &__right {
    white-space: nowrap;
    .moment-operation__right-question {
      display: inline-block;
    }
  }
}
</style>

<style scoped lang="scss">
.moment-operation {
  &__date {
    font-size: 14px;
    color: $text-auxiliary;
  }
}
</style>
