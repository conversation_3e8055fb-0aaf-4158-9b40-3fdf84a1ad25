<!--
 * @Description:企业微信朋友圈
-->
<template>
  <PageLayout class="moment">
    <template #layout-title>
      <!-- tab -->
      <VTab :is-active.sync="activeName" :tabs="tabList"></VTab>
      <!-- <el-tabs v-model="activeName" class="moment__tabs">
      <el-tab-pane
        v-for="(item, index) in tabList"
        :key="index"

        :label="item.text"
        :name="item.value"
      >

      </el-tab-pane>
    </el-tabs> -->
    </template>
    <!-- content-body -->
    <template #layout-column>
      <div class="moment-content">
        <!-- TODO: 此处的渲染逻辑可以优化 -->
        <moment-plane
          v-show="activeName === 'publishTask'"
          ref="publishTask"
          :active-name="activeName"
          detail-type="enterprise"
          @addMonent="toAddTask"
          @throughDetail="throughDetail"
        ></moment-plane>
        <moment-plane
          v-show="activeName === 'releaseRecord'"
          ref="releaseRecord"
          :active-name="activeName"
          detail-type="personal"
          @throughDetail="throughDetail"
        ></moment-plane>
      </div>
      <!-- 详情 -->
      <DrawerLayout :title="detailName" :visible.sync="detailVisiable">
        <template #default>
          <moment-detail :id="detailId" :type="detailType"></moment-detail>
        </template>
        <!-- <transition name="detail-dialog"> -->
        <!-- <div v-if="detailVisiable" class="moment__detail">
          <div class="moment__detail--iconbox" @click="detailVisiable = false">
            <i class="el-icon-close moment__detail--icon"></i>
          </div>
        </div> -->
        <!-- </transition> -->
        <!-- <transition name="dialog-bc">
        <div v-if="detailVisiable" class="moment__background" @click="detailVisiable = false"></div>
      </transition> -->
      </DrawerLayout>
    </template>
    <!-- 新建任务 -->
  </PageLayout>
</template>

<script>
import momentDetail from './components/moment-detail'
import momentPlane from './components/moment-plane.vue'
import { getTabList } from '@/api/scrm/sales-manage/moment.js'
// common
import PageLayout from '@/components/layout-page/v-layout'
import VTab from '@/components/common/v-tab'
import DrawerLayout from '@/views/scrm/components/basic/drawer/drawer-layout.vue'

export default {
  name: 'Moment',
  components: {
    VTab,
    PageLayout,
    DrawerLayout,
    MomentDetail: momentDetail,
    MomentPlane: momentPlane
  },
  props: {},
  data() {
    return {
      activeName: 'publishTask',
      detailId: '',
      detailName: '',
      detailVisiable: false,
      detailType: '',
      tabList: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getTabList()
  },
  mounted() {},
  methods: {
    // 查看详情
    throughDetail(data) {
      this.detailVisiable = false
      const { detailType, detailId, topic } = data
      this.detailId = detailId
      this.detailType = detailType
      this.detailName = topic
      this.detailVisiable = true
    },
    // 点击新建任务
    toAddTask() {
      this.$router.push('/scrm/sales-manage/moment-add-task')
    },
    // 获取tab
    async getTabList() {
      const res = await getTabList({ businessType: 101400 })
      this.tabList = res.result.tabList.map((item) => {
        return {
          id: item.value,
          name: item.text
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.moment {
  // &__tabs {
  //   display: flex;
  //   flex-direction: column;
  //   height: 100%;
  //   background-color: #fff;
  //   &--content {
  //     height: 100%;
  //   }
  // }
  &-content {
    position: relative;
    width: 100%;
    height: 100%;
  }
  &__detail {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;
    box-sizing: border-box;
    width: 600px;
    height: 100%;
    padding: 24px 16px;
    background-color: $neutral-color-1;
    // border-radius: 4px;
    box-shadow: -2px 0px 12px 0px rgba(0, 0, 0, 0.12);
    &--iconbox {
      position: absolute;
      top: 0px;
      left: -51px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      width: 50px;
      height: 50px;
      font-size: 20px;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
      background: #fff;
    }
    &--icon {
      font-size: 18px;
      font-weight: bold;
      color: black;
    }
  }
  &__background {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
  }
  &__addtask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }
}
</style>

<style lang="scss">
.moment {
  .moment-plane__table {
    display: flex;
    flex-direction: column;
  }

  .detail-dialog-enter-active {
    transition: all 0.6s ease;
  }
  .detail-dialog-leave-active {
    transition: all 0.6s ease;
  }
  .detail-dialog-enter,
  .detail-dialog-leave-to {
    transform: translateX(600px);
    // opacity: 0;
  }
  .dialog-bc-enter-active {
    transition: all 0.2s ease;
  }
  .dialog-bc-leave-active {
    transition: all 0.2s ease;
  }
  .dialog-bc-enter,
  .dialog-bc-leave-to {
    // transform: translateX(600px);
    opacity: 0;
  }
  &__tabs {
    .table-list {
      flex-grow: 1;
    }
    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__nav-wrap {
      box-sizing: border-box;
      display: flex;
      align-items: flex-end;
      height: 50px;
      padding: 0 20px;
      line-height: 50px;
      &::after {
        background-color: #fff;
      }
    }
    .el-tabs__content {
      box-sizing: border-box;
      flex-grow: 1;
      height: 100%;
      padding: 10px;
      background-color: $neutral-color-2;
    }
  }
  &__dialog {
    .el-dialog__body {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
