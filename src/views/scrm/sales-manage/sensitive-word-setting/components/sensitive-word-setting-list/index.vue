<template>
  <div class="sensitive-word-list">
    <div class="sensitive-word-list__words">
      <div class="sensitive-word-list__title">敏感词设置</div>
      <div class="sensitive-word-list__tip">
        自动捕捉对话中敏感词，提高会话查阅效率；同时，统计员工触发敏感词的情况，提高服务质量
        <span class="popper-click" @click="tipsDialog = true">查看示例</span>
      </div>
      <div class="sensitive-word-list__btn">
        <div class="new-btn" type="primary" @click="addBtn">
          <i class="icon-add-line t-iconfont"></i> 新建
        </div>
        <div class="user-btn" @click="openSettingDialog">通知人设置</div>
      </div>
      <div class="sensitive-word-list__table">
        <list-table
          ref="tableRef"
          :api="getSensitiveWordList"
          head-list-alias="headList"
          :render-module="[]"
          :select-limit="0"
          table-list-alias="formDataList"
          @operate="operate"
        >
        </list-table>
      </div>
    </div>
    <sensitive-word-setting-dialog
      v-if="dialogVisible"
      :id="id"
      :dialog-visible="dialogVisible"
      :edit-btn="editBtn"
      @dialogAchieve="dialogAchieve"
      @dialogClose="dialogClose"
    ></sensitive-word-setting-dialog>
    <el-dialog append-to-body title="功能示例" :visible.sync="tipsDialog" width="800px">
      <div class="dialog-gongneng">
        <div class="dialog-title">效果如下：</div>
        <div class="dialog-column">
          <img class="dialog-column__image image-style" src="@/assets/qiwei-06.png" />
          <img class="dialog-column__image" src="@/assets/qiwei-07.png" />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="tipsDialog = false">知道了</el-button>
      </span>
    </el-dialog>
    <!-- @refresh="getDetailList" -->
    <dialog-push-set
      v-if="settingDialog"
      :enable="enable"
      :get-query="sendQuery"
      :is-account-authorize="true"
      :push-object="pushObject"
      :push-time="pushTime"
      @close="closeDialog"
      @submit="submitDialog"
    >
      <div slot="account-authorize-slot" class="dialog-info">
        当对话出现敏感词汇时，如需向特定人员推送通知，请先在敏感词设置规则中启用消息推送功能
      </div>
    </dialog-push-set>
    <!-- <user-setting-dialog title="敏感词消息" :visible.sync="settingDialog"></user-setting-dialog> -->
  </div>
</template>

<script>
import sensitiveWordSettingDialog from '../sensitive-word-setting-dialog'
import userSettingDialog from '@/views/scrm/sales-manage/sensitive-word-setting/components/user-setting-dialog.vue'
import listTable from '@/views/scrm/components/common/table-container'
import { tableOperationLine } from '@/views/scrm/constants/common'
import DialogPushSet from '@/views/application-manage/notification-push/components/dialog-push-set'
import {
  getSensitiveWordList,
  getSensitiveWordDelete,
  getPushSet,
  savePushSet
} from '@/api/scrm/sales-manage/session-archive.js'
import { Message } from 'element-ui'

export default {
  name: 'SensitiveWordSettingList',
  components: {
    SensitiveWordSettingDialog: sensitiveWordSettingDialog,
    ListTable: listTable,
    UserSettingDialog: userSettingDialog,
    DialogPushSet
  },
  data() {
    return {
      tipsDialog: false,
      settingDialog: false,
      getSensitiveWordList,
      headList: [],
      wordList: [],
      dialogVisible: false,
      editBtn: '',
      id: '',
      // 消息设置
      enable: 0,
      pushTime: '',
      pushObject: '',
      sendQuery: {}
    }
  },
  created() {},
  methods: {
    // 操作
    operate(e, type) {
      console.log('操作', e, type)
      if (type === 'option_edit') {
        this.wordEdit(e)
      } else if (type === 'option_del') {
        this.wordDelete(e)
      }
    },
    openSettingDialog() {
      getPushSet({ subType: 0, type: 101204 }).then(({ result }) => {
        this.pushObject = result.push.pushObject
        this.enable = result.push.enable
        this.sendQuery = {
          id: result.push.id,
          title: result.push.title,
          subType: result.push.subType
        }
        this.settingDialog = true
      })
    },
    submitDialog(data) {
      console.log('submitDialog:', data)
      const params = {
        id: data.getQuery.id,
        enable: data.params.enable,
        subType: data.getQuery.subType,
        pushObject: data.params.pushObject,
        type: 101204
      }
      savePushSet({ push: params }).then((res) => {
        Message({
          showClose: true,
          message: '保存成功',
          type: 'success'
        })
        this.settingDialog = false
      })
    },
    wordEdit(scope) {
      console.log('scope', scope)
      this.editBtn = tableOperationLine['edit']
      this.id = scope.id
      this.dialogVisible = true
    },
    wordDelete(scope) {
      this.$confirm('此操作将永久删除该敏感词, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          getSensitiveWordDelete({ id: scope.id }).then((res) => {
            console.log(res)
            this.$refs.tableRef.refresh()
          })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    addBtn() {
      this.dialogVisible = true
      this.editBtn = tableOperationLine['add']
    },
    dialogClose() {
      this.dialogVisible = false
      console.log('this.$refs.tableRef.refresh()', this.$refs.tableRef.refresh())
    },
    dialogAchieve() {
      this.dialogVisible = false
      console.log('this.$refs.tableRef.refresh()', this.$refs.tableRef.refresh())
      this.$refs.tableRef.refresh()
    },
    closeDialog() {
      this.settingDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.sensitive-word-list {
  box-sizing: border-box;
  height: 100%;
  padding: 12px 16px;
  margin: 0 16px;
  background-color: $base-white;
  &__words {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }
  &__title {
    font-size: 16px;
    font-weight: 600;
    color: $text-main;
  }
  &__tip {
    box-sizing: border-box;
    padding: 10px 0 12px 0;
    margin-bottom: 20px;
    font-size: 13px;
    color: $text-auxiliary;
    border-bottom: 1px solid $neutral-color-3;
    .popper-click {
      margin-left: 8px;
      color: $link-base-color-6;
      cursor: pointer;
    }
  }
  &__btn {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .new-btn,
    .user-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
      border-radius: 4px;
    }
    .new-btn {
      width: 76px;
      height: 32px;
      margin-right: 16px;
      color: $base-white;
      background: $brand-base-color-6;
      .icon-add-line {
        margin-right: 6px;
      }
    }
    .user-btn {
      width: 94px;
      height: 30px;
      color: $text-plain;
      background: $base-white;
      border: 1px solid $neutral-color-3;
    }
  }
  &__table {
    flex: 1;
    :deep(.el-table) {
      thead {
        color: $text-main;
        background-color: $brand-color-1;
      }
    }
    :deep(.table-layout__main) {
      padding: 0 !important;
    }
  }
  .web-icon-plus {
    font-size: 13px;
  }
}
.dialog-info,
.dialog-column {
  box-sizing: border-box;
  width: 100%;
  background-color: $neutral-color-2;
  border-radius: 8px;
}
.dialog-info {
  padding: 20px;
  margin-bottom: 24px;
}

.dialog-gongneng {
  box-sizing: border-box;
  width: 100%;
  padding: 20px;
  background-color: $neutral-color-2;
  border-radius: 8px;
  .dialog-title {
    margin-bottom: 12px;
    font-family: PingFang SC;
    font-size: 16px;
    font-weight: 600;
    color: $text-main;
  }
  .dialog-column {
    display: flex;
    align-items: center;
    &__image {
      width: 350px;
    }
    .image-style {
      margin-right: 12px;
    }
  }
}
</style>
