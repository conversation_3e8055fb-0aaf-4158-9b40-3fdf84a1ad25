<template>
  <div class="material-score">
    <!-- score -->
    <div class="material-score__title">素材评分</div>
    <!-- main -->
    <div class="material-score__main">
      <span class="material-score__main--score">{{ valuePojo.value }}分</span>
      <span class="material-score__main--average">平均{{ valuePojo.avgValue }}分</span>
      <span class="material-score__main--compare">{{ compare }}</span>
      <span class="material-score__main--percent">
        {{ valuePojo.compareRate }}%
        <i class="iconfont" :class="materialScoreIcon"></i>
      </span>
    </div>
    <!-- footer -->
    <div class="material-score__footer">
      <div class="collects-box">
        <div>访问</div>
        <div class="collects-box__value">
          {{ userInfo.views }}
        </div>
      </div>
      <div class="collects-box">
        <div>转发</div>
        <div class="collects-box__value">
          {{ userInfo.forwards }}
        </div>
      </div>
      <div class="collects-box">
        <div>分享</div>
        <div class="collects-box__value">
          {{ userInfo.shares }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MaterialScore',

  props: {
    valuePojo: {
      type: Object,
      default: () => ({})
    },
    userInfo: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {}
  },

  computed: {
    // 判断
    compare() {
      if (this.valuePojo.compareRate > 0) {
        return '高于公司平均分'
      } else if (this.valuePojo.compareRate < 0) {
        return '低于公司平均分'
      } else {
        return '等于公司平均分'
      }
    },
    // 素材评分 涨跌icon
    materialScoreIcon() {
      const score = this.valuePojo.compareRate
      if (score < 0) {
        return 'green iconyoufanye1-copy-copy'
      } else if (score > 0) {
        return 'red iconyoufanye1-copy'
      } else {
        return 'gray iconyoufanye1-copy'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.material-score {
  box-sizing: border-box;
  width: 452px;
  padding: 19px 24px;
  background: $base-white;
  &__title {
    font-size: 14px;
    color: rgba($base-black, 0.45);
  }
  &__main {
    height: 60px;
    font-size: 14px;
    line-height: 60px;
    color: $text-auxiliary;
    border-bottom: 1px solid $line-cut-deep;
    &--score {
      margin-right: 12px;
      font-size: 24px !important;
      color: $text-main;
    }
    &--average {
      margin-right: 12px;
    }
    &--compare {
      margin-right: 8px;
    }
    &--percent {
      margin-top: 2px;
    }
    .iconfont {
      font-size: 12px;
      color: rgba($base-black, 0.45);
    }

    .gray {
      font-size: 12px;
      color: rgba($base-black, 0.45);
    }
    .green {
      font-size: 12px;
      color: $success-base-color-6;
    }
    .red {
      font-size: 12px;
      color: $error-base-color-6;
    }
  }
  &__footer {
    display: flex;
    margin-top: 15px;
    .collects-box {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      justify-content: space-between;
      height: 55px;
      margin-left: 40px;
      font-size: 14px;
      color: $text-auxiliary;
      &:nth-child(1) {
        margin-left: 0;
      }
      &:not(:last-child) {
        border-right: 1px solid $line-cut-deep;
      }
      &__value {
        font-size: 24px;
        color: $text-main;
      }
    }
  }
}
</style>
