<template>
  <div class="send-time">
    <!-- 生日 -->
    <div v-if="[ENUM_SOP_TEMPLATE.BIRTH.type].includes(sopType)" class="send-time__birth">
      <div class="send-time__both--line">
        <el-select v-model="dataInfoControl.executeOffsetMark" placeholder="">
          <el-option
            v-for="item in formRender.birth.executeOffsetMarkList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <template v-if="!isCurrentBirth">
          <el-input
            v-model.number="dataInfoControl.addDay"
            placeholder="请输入"
            @blur="numberLimitChange($event, timerExtraConfig.addDay, 'addDay')"
          ></el-input>
          天的
        </template>
        <el-time-picker v-model="dataInfoControl.time" format="HH:mm" placeholder="选择时间">
        </el-time-picker>
      </div>
    </div>
    <!-- 节日 -->
    <div
      v-else-if="[ENUM_SOP_TEMPLATE.FESTIVAL.type].includes(sopType)"
      class="send-time__festival"
    >
      <el-radio-group v-model="dataInfoControl.festivalType">
        <el-radio :label="ENUM_SEND_TIME_FESTIVAL.NORMAL.value">常用</el-radio>
        <el-radio :label="ENUM_SEND_TIME_FESTIVAL.CUSTOM.value">自定义</el-radio>
      </el-radio-group>
      <div class="send-time__both--line">
        <!-- 常用节日 -->
        <el-select
          v-if="dataInfoControl.festivalType === ENUM_SEND_TIME_FESTIVAL.NORMAL.value"
          v-model="dataInfoControl.festivalCode"
          :clearable="false"
          placeholder=""
        >
          <el-option
            v-for="item in formRender.festival.festivalCodeList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          ></el-option>
        </el-select>
        <!-- 自定义日期 -->
        <el-date-picker
          v-else
          v-model="dataInfoControl.festivalTime"
          :clearable="false"
          format="MM-dd"
          placeholder="选择日期"
          popper-class="festival-custom"
          type="date"
          value-format="MM-dd"
        >
        </el-date-picker>
        <!-- 公共部分选择小时 -->
        <el-time-picker
          v-model="dataInfoControl.time"
          :clearable="false"
          format="HH:mm"
          placeholder="选择时间"
        >
        </el-time-picker>
      </div>
    </div>
    <!-- 沉默客户 -->
    <div
      v-else-if="[ENUM_SOP_TEMPLATE.SILENCE_CUSTOMER.type].includes(sopType)"
      class="send-time__blank send-time__both"
    >
      <!-- 触发n天xxx后 -->
      <div class="send-time__both--line">
        触发第
        <el-input
          v-model.number="dataInfoControl.addDay"
          :disabled="disabledSendTimeControl.after"
          placeholder="请输入"
          @blur="numberLimitChange($event, timerExtraConfig.addDay, 'addDay')"
        ></el-input>
        天的
        <el-time-picker
          v-model="dataInfoControl.time"
          :clearable="false"
          :disabled="disabledSendTimeControl.after"
          format="HH:mm"
          placeholder="选择时间"
        >
        </el-time-picker>
        后，执行任务
      </div>
    </div>
    <!-- 空白模板 -->
    <div v-else class="send-time__blank send-time__both">
      <div class="send-time__both--line">
        <!-- 触发当日 -->
        <el-radio
          v-model="dataInfoControl.addDayType"
          :disabled="disabledSendTimeControl.current"
          :label="ENUM_SEND_TIME_DEFAULT.CURRENT_DAY.value"
        >
          触发当日
          <el-input
            v-model.number="dataInfoControl.minute"
            :disabled="disabledSendTimeControl.current"
            placeholder="请输入"
            @blur="numberLimitChange($event, timerExtraConfig.minute, 'minute')"
          ></el-input>
          分钟后，执行任务
        </el-radio>
      </div>
      <!-- 触发n天xxx后 -->
      <div class="send-time__both--line">
        <el-radio
          v-model="dataInfoControl.addDayType"
          :disabled="disabledSendTimeControl.after"
          :label="ENUM_SEND_TIME_DEFAULT.AFTER_DAY.value"
        >
          触发第
          <el-input
            v-model.number="dataInfoControl.addDay"
            :disabled="disabledSendTimeControl.after"
            placeholder="请输入"
            @blur="numberLimitChange($event, timerExtraConfig.addDay, 'addDay')"
          ></el-input>
          天的
          <el-time-picker
            v-model="dataInfoControl.time"
            :clearable="false"
            :disabled="disabledSendTimeControl.after"
            format="HH:mm"
            placeholder="选择时间"
          >
          </el-time-picker>
          后，执行任务
        </el-radio>
      </div>
    </div>
  </div>
</template>

<script>
// import xbb from '@xbb/xbb-utils'
// api
import { getSopFestivalList } from '@/api/scrm/customer-operation/automated-operation'
// enum
import { ENUM_SOP_TEMPLATE, ENUM_EXECUTE_OFFSET_MARK } from '@/views/scrm/constants/enum.sop.js'
// 发送时间---【空白SOP】
const ENUM_SEND_TIME_DEFAULT = {
  CURRENT_DAY: { label: 'current', value: 0 },
  AFTER_DAY: { label: 'after', value: 1 }
}
// 发送时间---【节日SOP】
const ENUM_SEND_TIME_FESTIVAL = {
  NORMAL: { label: 'normal', value: 0 },
  CUSTOM: { label: 'custom', value: 1 }
}
// form extra limit
const ENUM_EXTRA_LIMIT = {
  [ENUM_SOP_TEMPLATE.BLANK.type]: {
    minute: { min: 5, max: 60 },
    addDay: { min: 1, max: 365 }
  },
  [ENUM_SOP_TEMPLATE.BIRTH.type]: {
    addDay: { min: 1, max: 100 }
  }
}

export default {
  name: 'SendTime',

  props: {
    dataInfo: {
      type: Object,
      required: true,
      default: () => {}
    },
    sopType: {
      type: Number,
      default: 0
    },
    isSopEdit: {
      type: Boolean,
      default: false
    },
    isTaskEdit: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      ENUM_SOP_TEMPLATE,
      ENUM_SEND_TIME_DEFAULT,
      ENUM_SEND_TIME_FESTIVAL,
      // 生日SOP
      ENUM_EXECUTE_OFFSET_MARK,
      formRender: {
        // 生日SOP
        birth: {
          executeOffsetMarkList: Object.values(ENUM_EXECUTE_OFFSET_MARK)
        },
        // 节日SOP
        festival: {
          festivalCodeList: []
        }
      }
    }
  },

  computed: {
    dataInfoControl: {
      set(val) {
        this.$emit('update:data-info', val)
      },
      get() {
        return this.dataInfo
      }
    },
    disabledSendTimeControl() {
      // TODO: 需要优化
      if ([ENUM_SOP_TEMPLATE.BIRTH.type].includes(this.sopType)) {
        return {}
      } else if ([ENUM_SOP_TEMPLATE.FESTIVAL.type].includes(this.sopType)) {
        return {}
      } else {
        return {
          [ENUM_SEND_TIME_DEFAULT.CURRENT_DAY.label]: this.disabledSendTime(
            ENUM_SEND_TIME_DEFAULT.CURRENT_DAY.value
          ),
          [ENUM_SEND_TIME_DEFAULT.AFTER_DAY.label]: this.disabledSendTime(
            ENUM_SEND_TIME_DEFAULT.AFTER_DAY.value
          )
        }
      }
    },
    // 生日SOP
    // 是否选择生日当天
    isCurrentBirth() {
      return [ENUM_EXECUTE_OFFSET_MARK.CURRENT.value].includes(
        this.dataInfoControl.executeOffsetMark
      )
    },
    // form额外的数值限制
    timerExtraConfig() {
      return ENUM_EXTRA_LIMIT[this.sopType] || ENUM_EXTRA_LIMIT[ENUM_SOP_TEMPLATE.BLANK.type]
    }
  },

  created() {
    this.init()
  },

  methods: {
    init() {
      // 节日SOP初始化
      if ([ENUM_SOP_TEMPLATE.FESTIVAL.type].includes(this.sopType)) {
        this.getSopFestivalList()
        // 兼容api
        // 如果从【自定义时间】切换到【节日】,初始化节日code
        if (this.dataInfoControl.festivalType === ENUM_SEND_TIME_FESTIVAL.CUSTOM.value) {
          const unWatch = this.$watch('dataInfoControl.festivalType', (newVal, oldVal) => {
            console.log('dataInfoControl.festivalType', newVal, oldVal)
            this.dataInfoControl.festivalCode = this.formRender.festival.festivalCodeList[0].code
            // 销毁监听
            unWatch()
          })
        }
      }
    },
    //
    disabledSendTime(type) {
      const isCurr = type !== this.dataInfoControl.addDayType
      return this.isTaskEdit && isCurr
    },
    // input数量限制范围
    // @xbb.debounceWrap(200)
    numberLimitChange(e, { min = 0, max = 99999999 }, attr) {
      if (Number(e.target.value) <= min) {
        e.target.value = min
      } else if (Number(e.target.value) >= max) {
        e.target.value = max
      }
      this.dataInfoControl[attr] = e.target.value
      console.log('e.target.value', e.target.value)
    },
    // ************************************节日SOP****************************************
    getSopFestivalList() {
      getSopFestivalList().then((res) => {
        const list = res.result.festivals
        if (list.length) {
          this.dataInfoControl.festivalCode = this.dataInfoControl.festivalCode || list[0].code
          this.formRender.festival.festivalCodeList = list.map((item) => {
            return {
              ...item,
              name: `${item.name}(${item.time})`
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.send-time {
  &__both {
    display: flex;
    flex-direction: column;
    &--line {
      display: flex;
      align-items: center;
      &:not(:last-child) {
        margin-bottom: 12px;
      }
    }
  }

  // 空白模板
  &__blank {
    .el-input {
      width: 80px;
      margin: 0 8px;
    }
    .el-radio {
      margin-right: 0 !important;
    }
    .el-date-editor {
      width: 120px;
      margin: 0 8px;
    }
  }
  &__birth {
    .el-select,
    .el-input,
    .el-date-editor {
      width: 132px !important;
      margin-right: 8px;
    }
    .el-date-editor {
      margin: 0 8px 0;
    }
  }
  &__festival {
    .el-select,
    .el-date-editor {
      width: 210px !important;
      margin-right: 8px;
    }
  }
}
</style>

<style lang="scss">
.festival-custom {
  .el-icon-d-arrow-left,
  .el-icon-d-arrow-right {
    display: none !important;
  }
  .el-date-picker__header {
    .el-date-picker__header-label:nth-child(3) {
      display: none !important;
    }
  }
}
</style>
