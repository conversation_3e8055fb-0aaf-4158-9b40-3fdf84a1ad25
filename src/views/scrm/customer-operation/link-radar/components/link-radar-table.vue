<template>
  <div class="link-radar-table">
    <div class="link-radar-table__header">
      <span class="link-radar-table__cascader--text">{{ $t('linkRadar.tagFilter') }}</span>
      <el-cascader
        class="link-radar-table__cascader"
        clearable
        :options="tagList"
        :props="props"
        :show-all-levels="false"
        size="small"
        @change="onTagsFilter"
      >
      </el-cascader>
    </div>
    <!-- table -->
    <!--
      head-list-alias="headList"
      :business-type="businessType"
      table-list-alias="formDataList"
    -->
    <TableContainer
      ref="tableContainer"
      :api-data-alias="{ headList: 'headList', dataList: 'formDataList' }"
      :footer-button-list="tableOperationList"
      :render-module="['batchOperation']"
      :search="searchData"
      style="padding: 0 10px"
      @batchOperate="operationClick"
      @operate="operationRow"
      @select="tableSelection"
      @through="throughDetail"
    >
    </TableContainer>
  </div>
</template>

<script>
import materialMixin from './material.mixin.js'
// import tableContainer from '@/views/scrm/components/common/table-container'
import TableContainer from '@/views/scrm/components/basic/table/table-container/index'
import { tagScreenList } from '@/api/scrm/common.js'

export default {
  name: 'LinkRadarTable',
  components: {
    TableContainer
  },
  mixins: [materialMixin],
  props: {
    dataFilter: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tagCondition: {
        symbol: 'include',
        value: []
      },
      props: {
        value: 'id',
        label: 'name',
        children: 'list',
        multiple: true,
        emitPath: false
      },
      tagList: [],
      dataSelected: [] // 选中数据
    }
  },
  computed: {
    searchData() {
      return JSON.parse(
        JSON.stringify({
          ...this.dataFilter,
          tagCondition: this.tagCondition
        })
      )
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getCommonButtonList()
    tagScreenList({ businessType: this.businessType }).then((res) => {
      this.tagList = res.result.list
    })
  },
  methods: {
    // 标签筛选
    onTagsFilter(selectData) {
      this.tagCondition.value = selectData
    },
    // 行内点击操作
    operationRow(obj, operation) {
      console.log('我进来了')
      this.$emit('operationRow', obj, operation)
    },
    // 选择行内数据
    tableSelection(val) {
      this.dataSelected = val
    },
    // 点击底部操作
    operationClick(e) {
      if (this.dataSelected.length < 1) {
        this.$message({
          type: 'warning',
          message: '您未勾选操作数据'
        })
        return
      }
      this.$emit('batchOperation', this.dataSelected, e)
    },
    // 点击穿透
    throughDetail(data) {
      this.$emit('throughDetail', data)
    },
    /**
     * @description: 清空列表选中的数据
     * @param {*} vuexKey：标识数据
     * @return {*}
     */
    clearDataSelected() {
      this.dataSelected = []
      // 清空el-table视图选中效果
      this.$refs.tableContainer && this.$refs.tableContainer.refresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.link-radar-table {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  &__header {
    flex-shrink: 0;
    padding: 0 10px;
    margin: 12px 0;
  }
  &__filter {
    position: absolute;
    z-index: 1000;
  }
}
</style>

<style lang="scss">
.link-radar-table {
  .filter-tags-header__container {
    padding: 0 15px;
  }
  .el-cascader {
    width: 428px;
  }
  .tags-category-list__data--checkbox {
    .el-checkbox-button__inner {
      box-sizing: border-box;
      height: 28px;
      padding: 0 10px;
      line-height: 26px;
    }
  }
  .tags-category-list {
    &:last-of-type {
      padding-bottom: 8px;
    }
  }
  &__cascader {
    .el-cascader__tags {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;
    }
  }
}
</style>
