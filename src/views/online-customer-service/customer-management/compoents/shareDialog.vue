<template>
  <el-dialog
    :before-close="handleClose"
    class="new-style-dialog"
    :title="detail.kfName + '分享'"
    :visible.sync="dialogVisible"
    width="650px"
  >
    <div class="share-item">
      <div class="share-header">
        <div class="share-title">链接分享</div>
        <div class="line"></div>
      </div>
      <div class="share-link">
        <div class="share-link__text">链接：</div>
        <el-scrollbar class="url-wrap" height="56px">
          <div class="share-link__url">
            {{ detail.chatLink }}
          </div>
        </el-scrollbar>
        <el-button class="share-link__button" @click="copyUrl">
          <i class="web-icon-snippets web-iconfont"></i>
          复制
        </el-button>
      </div>
    </div>
    <div class="share-item">
      <div class="share-header">
        <div class="share-title">二维码分享</div>
        <div class="line"></div>
      </div>
      <div id="qrcode" class="share-code">
        <qrcode-vue class="qrcode-img" :size="100" :value="detail.chatLink"></qrcode-vue>
        <el-button class="download handle-button" @click="download">
          <i class="icon-download iconfont"></i>
          下载
        </el-button>
        <el-button class="copy handle-button" @click="copy">
          <i class="web-icon-snippets web-iconfont"></i>
          复制
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { copyToClipboard } from '@/utils/copy-to-clipboard'
import QrcodeVue from 'qrcode.vue'

export default {
  name: 'ShareDialog',

  components: {
    QrcodeVue
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      nameLike: '',
      checkedItem: [],
      page: 1,
      options: [],
      activeName: 'skill'
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('dialogClose')
    },
    copyUrl() {
      const value =
        typeof this.detail.chatLink === 'string'
          ? this.detail.chatLink
          : JSON.stringify(this.detail.chatLink)
      copyToClipboard(value)
        .then(() => {
          this.$message({
            message: this.$t('message.copySuccess'),
            type: 'success'
          })
        })
        .catch((err) => {
          console.log(2323, err)
          this.$message({
            message: this.$t('message.copyOpFailed'),
            type: 'warning'
          })
        })
    },
    download() {
      const canvas = document.getElementById('qrcode').getElementsByTagName('canvas')
      const a = document.createElement('a')
      a.href = canvas[0].toDataURL('img/png')
      a.download = '二维码'
      a.click()
    },
    copy() {
      const canvas = document.getElementById('qrcode').getElementsByTagName('canvas')
      canvas[0].toBlob((blob) => {
        const data = [new ClipboardItem({ [blob.type]: blob })]
        navigator.clipboard
          .write(data)
          .then(() => {
            this.$message({
              message: this.$t('message.copySuccess'),
              type: 'success'
            })
          })
          .catch(() => {
            this.$message({
              message: this.$t('message.copyOpFailed'),
              type: 'success'
            })
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.share-item {
  margin-bottom: 32px;
  .share-header {
    .share-title {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      color: $text-plain;
      letter-spacing: 0em;
    }
    .line {
      margin-bottom: 20px;
      border: 1px dashed $neutral-color-3;
    }
  }
  .share-link {
    display: flex;
    align-items: flex-end;
    width: 100%;
    &__text {
      height: 90px;
      color: $text-plain;
    }
    .url-wrap {
      width: 430px;
      height: 72px;
      padding: 8px;
      font-size: 12px;
      line-height: 18px;
      color: $text-plain;
      background-color: $neutral-color-1;
      border: 1px solid $line-cut-table;
      border-radius: 4px;
    }
    &__url {
      width: 100%;
      overflow: hidden;
      cursor: not-allowed;
    }
    &__button {
      width: 52px;
      height: 24px;
      padding: 0;
      margin-left: 14px;
      font-size: 12px;
    }
  }
  .share-code {
    display: flex;
    align-items: flex-end;
    width: 100%;
    height: 104px;
    img {
      margin-right: 24px;
    }
    .handle-button {
      width: 52px;
      height: 24px;
      padding: 0;
      margin-left: 12px;
    }
  }
}
</style>
