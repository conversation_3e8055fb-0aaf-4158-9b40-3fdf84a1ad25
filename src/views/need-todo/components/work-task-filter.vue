<!--
 * @Description: 工作任务筛选条件
 -->
<!--  -->
<template>
  <div class="todo-filter-block" :class="isPopup ? 'popup-filter' : 'common-filter'">
    <template v-for="item in fieldList">
      <!-- 范围筛选 -->
      <div v-if="item.attr === 'rangeScreen'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <RangeSelect
          class="range-select-item"
          :field-info="item"
          hide-department
          :special-clear-flag="specialClearFlag"
          @valueChange="valueChange"
        ></RangeSelect>
      </div>
      <!-- 任务状态 -->
      <div v-else-if="item.attr === 'status'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <el-select
          v-model="filterData.status"
          class="form-item__input"
          clearable
          :disabled="istoExecute"
        >
          <el-option
            v-for="status of item.items"
            :key="status.value"
            :label="status.text"
            :value="status.value"
          ></el-option>
        </el-select>
      </div>
      <!-- 任务来源 -->
      <div v-else-if="item.attr === 'taskType'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <el-select v-model="filterData.taskType" class="form-item__input" clearable>
          <el-option
            v-for="task of item.items"
            :key="task.value"
            :label="task.text"
            :value="[task.value]"
          ></el-option>
        </el-select>
      </div>
      <!-- 计划开始时间 -->
      <div v-else-if="item.attr === 'startTime'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <el-date-picker
          v-model="filterData.startTime"
          class="form-item__input"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('label.endTime')"
          :range-separator="$t('unit.to')"
          :start-placeholder="$t('label.startTime')"
          type="daterange"
          value-format="timestamp"
        >
        </el-date-picker>
      </div>
      <!-- 计划截止时间 -->
      <div v-else-if="item.attr === 'endTime'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <el-date-picker
          v-model="filterData.endTime"
          class="form-item__input"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('label.endTime')"
          :range-separator="$t('unit.to')"
          :start-placeholder="$t('label.startTime')"
          type="daterange"
          value-format="timestamp"
        >
        </el-date-picker>
      </div>
      <!-- 创建时间 -->
      <div v-else-if="item.attr === 'addTime'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <el-date-picker
          v-model="filterData.addTime"
          class="form-item__input"
          :default-time="['00:00:00', '23:59:59']"
          :end-placeholder="$t('label.endTime')"
          :picker-options="pickerOptions"
          :range-separator="$t('unit.to')"
          :start-placeholder="$t('label.startTime')"
          type="daterange"
          value-format="timestamp"
        >
        </el-date-picker>
      </div>
      <!-- 创建人 -->
      <div v-else-if="item.attr === 'creatorId'" :key="item.attr" class="form-item">
        <div class="label">{{ item.attrName }}</div>
        <el-select
          v-model="filterData.creatorId"
          v-lazyLabel="{
            value: modelVal,
            options: options,
            key: 'value'
          }"
          class="form-item__input"
          clearable
          filterable
          :loading="selectLoading"
          :placeholder="$t('placeholder.selectOrSearch')"
          remote
          :remote-method="remoteMethod"
          reserve-keyword
          @visible-change="visibleChange"
        >
          <template v-if="!selectLoading">
            <template v-if="options.length">
              <template v-for="item in options">
                <el-option
                  v-if="item.value || item.value === 0"
                  :key="item.value"
                  :label="item.text"
                  :value="[item.value]"
                >
                  <div style="display: flex; align-items: center">
                    <avatar-img
                      :alt="item.text"
                      :radius="true"
                      :size="24"
                      :src="item.avatar"
                    ></avatar-img>
                    <span style="margin-left: 8px">{{ item.text }}</span>
                  </div>
                </el-option>
              </template>
            </template>
            <el-option v-else :disabled="true" :value="''">{{
              $t('operation.noOption')
            }}</el-option>
          </template>
        </el-select>
      </div>
    </template>
    <!-- 清空筛选项 -->
    <div v-if="!isPopup" class="clear-filter-item setting-icon" @click="removeFilterValue">
      <span><i class="web-icon-qingliclear web-iconfont"></i></span>
    </div>
    <!-- 切换状态 -->
    <div v-if="!isPopup" class="filter-status setting-icon" @click="changeViewType">
      <span><i class="web-icon-quxiaoguding web-iconfont"></i></span>
    </div>
  </div>
</template>

<script>
import { getFilterConditions } from '@/api/decisionTree'
import { todoConditionList } from '@/api/todoTask'
import rangeSelect from '@/components/independentFields/range-select'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'WorkTaskFilter',
  components: {
    RangeSelect: rangeSelect
  },
  props: {
    isPopup: {
      type: Boolean
    },
    currentTab: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      options: [],
      selectLoading: false,
      pickerOptions: {
        // 限制查询时间为近一年内
        disabledDate(time) {
          const t = new Date()
          const Y = t.getFullYear()
          const M = t.getMonth()
          const D = t.getDate()
          return (
            time.getTime() < new Date(Y - 1, M, D).getTime() ||
            time.getTime() > new Date(Y, M, D + 1).getTime()
          )
        }
      },
      specialClearFlag: false,
      fieldList: [], // 筛选项
      filterData: {} // 筛选数据
    }
  },
  computed: {
    // 是否是任务目录中的待我执行
    istoExecute() {
      return this.currentTab.alias === 'toExecute'
    },
    copyFilterData() {
      return JSON.parse(JSON.stringify(this.filterData))
    }
  },

  watch: {
    copyFilterData: {
      deep: true,
      handler(newVal, oldVal) {
        this.filterDataChangeDebounce()
      }
    }
  },
  mounted() {
    this.getCondition()
  },
  methods: {
    visibleChange(val) {
      if (val) {
        this.remoteMethod('')
      }
    },
    remoteMethod(val) {
      const params = {
        value: val,
        page: 1,
        pageSize: 10,
        saasMark: 1,
        fieldType: 10009,
        attr: 'creatorId',
        businessType: 21500,
        subBusinessType: 21500
      }
      getFilterConditions(params).then((res) => {
        this.options = res.result.items
      })
    },
    valueChange(val) {
      this.filterData.rangeScreen = [val]
    },
    // 获取筛选项
    getCondition() {
      todoConditionList({ type: this.currentTab.type, groupAlias: this.currentTab.alias }).then(
        (res) => {
          this.fieldList = res.result.fieldList
          this.initFilterData()
        }
      )
    },
    @xbb.debounceWrap(500)
    filterDataChangeDebounce() {
      const queryData = this.fieldList.map((item) => {
        let itemValue = this.filterData[item.attr] || []
        if (item.fieldType === 4) {
          // 给后端传秒级
          itemValue = itemValue.map((item) => item / 1000)
        }
        return {
          attr: item.attr,
          fieldType: item.fieldType,
          value: typeof itemValue === 'number' ? [itemValue] : itemValue
        }
      })
      this.$emit('filterDataChange', { type: 'filter', queryData })
    },
    changeViewType() {
      this.$emit('change-view-type', false)
    },
    initFilterData() {
      this.specialClearFlag = !this.specialClearFlag
      this.fieldList.forEach((item) => {
        this.$set(this.filterData, item.attr, item.fieldType === 3 ? '' : [])
      })
      if (this.currentTab.alias !== 'allWorkTask') {
        // 默认选中未完成的任务状态
        this.filterData.status = 2
      }
    },
    removeFilterValue() {
      this.initFilterData()
      if (!this.istoExecute) {
        // 清除筛选项，只有待我执行的tab仍然保留未完成的任务状态
        this.filterData.status = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-filter-block {
  display: flex;
  flex-wrap: wrap;
  width: 95%;
  &.common-filter {
    .form-item > .label:after {
      content: ':';
    }
  }
}
.range-select {
  width: 275px;
  margin-right: 0px !important;
  .el-input-group__prepend {
    box-sizing: border-box;
    padding-left: 14px;
    font-size: 13px;
    color: rgb(96, 98, 102);
    background: $base-white;
  }
  .range-select__type {
    width: 74px;
    .el-input__inner {
      padding-right: 20px !important;
    }
    .el-input__suffix {
      width: 20px;
    }
  }
}
.label {
  display: inline-block;
  width: 70px;
  padding-right: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}
.form-item {
  display: inline-block;
  margin-right: 15px;
  margin-bottom: 10px;
  .el-date-editor,
  .form-item__input,
  .el-select {
    width: 275px;
  }
  .range-select-item {
    display: inline-block;
  }
}
.setting-icon {
  position: absolute;
  display: inline-block;
  padding: 0 5px 5px 5px;
  margin-left: 10px;
  font-size: 20px;
  color: $text-auxiliary;
  cursor: pointer;
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
  &:hover {
    color: $brand-color-5;
    border: 1px solid $brand-color-5;
  }
  &.filter-status {
    top: 15px;
    right: 20px;
  }
  &.clear-filter-item {
    top: 15px;
    right: 55px;
  }
}
</style>
