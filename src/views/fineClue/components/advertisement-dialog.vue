<template>
  <el-dialog
    append-to-body
    class="new-style-dialog"
    :close-on-click-modal="false"
    custom-class="advertisement-view"
    title="其它导入"
    :visible.sync="isVisible"
    width="800px"
  >
    <div class="advertisement-view-box">
      <div v-for="(item, index) in AD_LIST" :key="index" class="column">
        <div class="column-left">
          <div class="left-images" :class="item.icon"></div>
          <div class="left-info">
            <div class="info-title">{{ item.title }}</div>
            <div class="info-content">{{ item.content }}</div>
          </div>
        </div>
        <div class="column-right" @click="toCenter">去开通</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

const AD_LIST = [
  {
    icon: 'souke-icon-01',
    title: '广告助手',
    content: '百度、腾讯、头条、抖音等全网广告平台自动汇聚，高效分配与跟踪'
  },
  {
    icon: 'souke-icon-02',
    title: '搜客',
    content: '轻松挖掘线索，没事搜一搜解锁无限商机，让查找更高效，获客更精准'
  },
  {
    icon: 'souke-icon-03',
    title: '智能表单',
    content: '自定义制作表单，一键分享链接客户，快速收集信息',
    tag: '旗舰版限免'
  },
  {
    icon: 'souke-icon-04',
    title: 'API导入',
    content:
      '通过应用程序接口，对各个应用之间进行数据连接，使企业摆脱数据孤岛的现状，实现全自动数据流转功能'
  }
]

export default {
  data() {
    return {
      AD_LIST
    }
  },
  computed: {
    ...mapGetters(['proImport/getAdvertisementView']),
    isVisible: {
      get: function () {
        return this['proImport/getAdvertisementView']
      },
      set: function (val) {
        this.setAdvertisementView(val)
      }
    }
  },
  created() {
    console.log('??????')
  },
  methods: {
    ...mapActions(['proImport/setAdvertisementView']),
    ...mapActions({ setAdvertisementView: 'proImport/setAdvertisementView' }),
    toCenter() {
      this.setAdvertisementView(false)
      this.$router.push('/appModule/index')
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  background: $neutral-color-1;
  border-radius: 0 0 8px 8px;
}
.advertisement-view {
  .advertisement-view-box {
    // overflow: hidden;
    // border-radius: 8px;
  }
  .column {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 16px 20px;
    overflow: hidden;
    background-color: $base-white;
    border-radius: 8px;
    .column-left {
      display: flex;
      align-items: flex-start;
      .left-images {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        margin-right: 20px;
        background-size: cover;
        border-radius: 8px;
      }
      .souke-icon-01 {
        background-image: url('../../../assets/souke-icon-01.png');
      }
      .souke-icon-02 {
        background-image: url('../../../assets/souke-icon-02.png');
      }
      .souke-icon-03 {
        background-image: url('../../../assets/souke-icon-03.png');
      }
      .souke-icon-04 {
        background-image: url('../../../assets/souke-icon-04.png');
      }
      .left-info {
        .info-title {
          margin-bottom: 8px;
          font-size: 18px;
          font-weight: 600;
          color: $text-main;
        }
        .info-content {
          font-size: 14px;
          line-height: 18px;
          color: $text-plain;
        }
      }
    }
    .column-right {
      flex-shrink: 0;
      width: 68px;
      height: 32px;
      margin-left: 20px;
      font-size: 12px;
      line-height: 32px;
      color: $brand-base-color-6;
      text-align: center;
      border: 1px solid $brand-base-color-6;
      border-radius: 4px;
    }
  }
}
</style>
