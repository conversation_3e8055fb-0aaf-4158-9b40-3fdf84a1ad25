<template>
  <div class="message-item">
    <div class="message-item__title">
      <span class="message-title">{{ dataInfo.msg.body.title }}</span>
      <span class="message-time">{{ formatTime(dataInfo.pushTime) }}</span>
    </div>
    <div class="message-item__content">
      <p class="message-content">{{ dataInfo.msg.body.content || '--' }}</p>
      <i v-show="!dataInfo.isRead" class="message-unread"></i>
    </div>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'

export default {
  name: 'MessageItem',

  props: {
    dataInfo: {
      type: Object,
      default: () => ({
        msg: {
          body: {
            title: '',
            content: ''
          }
        },
        pushTime: 0,
        isRead: 0
      })
    }
  },

  data() {
    return {}
  },

  methods: {
    // 格式化时间
    formatTime(time) {
      const poor = parseInt(new Date().getTime() / 1000) - time
      if (poor > 0) {
        return xbb.formatTimeCompare(time)
      } else {
        const dateObj = xbb.timestampToTime(time)
        return `${dateObj.year}-${dateObj.month}-${dateObj.day} ${dateObj.hours}:${dateObj.minutes}`
      }
    }
  },

  created() {}
}
</script>

<style lang="scss" scoped>
.message-item {
  position: relative;
  box-sizing: border-box;
  padding: 12px 20px;
  cursor: pointer;

  &:hover {
    opacity: 0.85;
  }

  ::after {
    position: absolute;
    bottom: 0;
    left: 20px;
    width: calc(100% - 40px);
    height: 1px;
    content: '';
    background: $line-cut-light;
  }

  &__title,
  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__title {
    height: 20px;
    line-height: 20px;
    .message-title {
      font-size: 14px;
      font-weight: 600;
      color: $text-main;
      @include singleline-ellipsis();
    }
    .message-time {
      flex-shrink: 0;
      margin-left: 16px;
      font-size: 13px;
      color: $text-grey;
    }
  }
  &__content {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    .message-content {
      // width: 400px;
      font-size: 13px;
      line-height: 16px;
      color: $text-auxiliary;
      @include multiline-ellipsis(2);
    }
    .message-unread {
      display: inline-block;
      flex-shrink: 0;
      width: 8px;
      height: 8px;
      margin-left: 16px;
      background: $error-color-5;
      border-radius: 50%;
    }
  }
}
</style>
