<!-- 把工作流里常见的几种 tooltip 行内容封装一下，方便复用 -->
<template>
  <div class="graphic-tooltip-item" :class="`is-${type}`" :style="contentStyle">
    <div
      v-for="(column, columnIndex) in computedContent"
      :key="columnIndex"
      class="graphic-tooltip-item__column"
      :style="columnStyle"
    >
      <!-- 图片 -->
      <img v-if="type === 'image'" alt="image" :src="column" />
      <!-- 键值对 -->
      <template v-else-if="type === 'key-value'">
        <span class="generate-object__popover-content__label">{{ column.label }}:</span>
        <span class="generate-object__popover-content__text">{{ column.text }}</span>
      </template>
      <!-- 纯文本 -->
      <span v-else>{{ column }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GraphicTooltipItem',

  props: {
    // 要渲染的内容 文本/url字符串, 键值对对象{ label, text }。如果多列则按数组来处理
    content: {
      type: [String, Array, Object],
      default: ''
    },
    type: {
      type: String,
      default: 'text',
      validator(value) {
        // 目前只支持纯文本、图片和键值对的场景
        return ['text', 'image', 'key-value'].includes(value)
      }
    },
    // 列间距，若有值则意味着该行多列展示
    columnGap: {
      type: [Number, String],
      default: 0
    },
    // 行间距，方便定制大间距
    bottomGap: {
      type: [Number, String],
      default: '12px'
    }
  },

  computed: {
    computedContent() {
      return Array.isArray(this.content) ? this.content : [this.content]
    },
    contentStyle() {
      return typeof this.bottomGap === 'string'
        ? { 'margin-bottom': this.bottomGap }
        : { 'margin-bottom': `${this.bottomGap}px` }
    },
    columnStyle() {
      return typeof this.bottomGap === 'string'
        ? { gap: this.columnGap }
        : { gap: `${this.columnGap}px` }
    }
  }
}
</script>

<style lang="scss" scoped>
.graphic-tooltip-item {
  display: flex;
  flex-direction: row;
  gap: 0;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12px;
  .graphic-tooltip-item__column {
    flex: 1 1 0;
  }
}
.graphic-tooltip-item:last-child {
  margin-bottom: 0 !important;
}
.graphic-tooltip-item.is-text,
.graphic-tooltip-item.is-key-value {
  .graphic-tooltip-item__column {
    font-size: 0;
    line-height: 16px;
    color: $text-plain;

    span {
      font-size: 12px;
    }
  }
}
.graphic-tooltip-item.is-key-value {
  .generate-object__popover-content__label {
    margin-right: 6px;
    font-weight: 600;
    color: $text-main;
  }
}
.graphic-tooltip-item.is-image {
  img {
    width: 100%;
  }
}
</style>
