<!-- checkbox 样式的多选下拉框 -->
<template>
  <div class="checkbox-select">
    <el-input
      v-popover:checkPopoverRef
      class="checkbox-select__input"
      :disabled="disabled"
      :placeholder="placeholder"
      :prefix-icon="prefixIcon"
      :readonly="true"
      :value="valueString"
      @mouseenter.native="inputHovering = true"
      @mouseleave.native="inputHovering = false"
    >
      <template v-if="$slots.prefix" slot="prefix">
        <slot name="prefix"></slot>
      </template>
      <template slot="suffix">
        <i v-show="!showClose" class="checkbox-select__caret" :class="arrowClass"></i>
        <i
          v-if="showClose"
          class="checkbox-select__caret el-icon-circle-close"
          @click="checkClearHandler"
        ></i>
      </template>
    </el-input>
    <el-popover
      ref="checkPopoverRef"
      v-model="popShow"
      placement="bottom"
      popper-class="checkbox-select__options-wrapper"
      trigger="focus"
      width="200"
    >
      <template v-if="options && options.length">
        <el-checkbox
          v-model="checkAll"
          class="checkbox-select__options-item"
          :indeterminate="isIndeterminate"
          >{{ $t('operation.selectAll') }}</el-checkbox
        >
        <el-checkbox-group v-model="localValue">
          <el-checkbox
            v-for="(item, index) in options"
            :key="index"
            class="checkbox-select__options-item"
            :label="item"
            >{{ item[labelKey] }}</el-checkbox
          >
        </el-checkbox-group>
      </template>
      <span v-else class="checkbox-select__empty">{{ $t('message.noData') }}</span>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'CheckboxSelect',

  props: {
    value: {
      type: Array
    },
    options: {
      type: Array
    },
    valueKey: {
      // 默认返回整个选择元素，若设置了valueKey，则返回对应key值
      type: String
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    // ...
    placeholder: {
      type: String
    },
    prefixIcon: {
      type: String
    },
    clearable: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  model: {
    prop: 'value',
    event: 'update'
  },

  data() {
    return {
      inputHovering: false,
      popShow: false
    }
  },

  computed: {
    showClose() {
      const hasValue = Array.isArray(this.value) && this.value.length > 0
      const criteria = this.clearable && !this.disabled && this.inputHovering && hasValue
      return criteria
    },
    arrowClass() {
      return this.popShow ? 'el-icon-arrow-up is-reverse' : 'el-icon-arrow-up'
    },
    localValue: {
      get() {
        if (this.valueKey) {
          return this.options.filter((item) => (this.value || []).includes(item[this.valueKey]))
        }
        return this.value || []
      },
      set(val) {
        const values = this.valueKey
          ? val
              .map((item) => item[this.valueKey])
              .sort((a, b) => {
                return a < b ? -1 : 1
              })
          : val
        this.$emit('update', values)
        this.$emit('change', values)
      }
    },
    valueString() {
      return this.localValue.map((item) => item[this.labelKey]).join(', ')
    },
    isIndeterminate() {
      const valueLen = (this.localValue || []).length
      const optionsLen = (this.options || []).length
      return valueLen > 0 && valueLen < optionsLen
    },
    checkAll: {
      get() {
        return (this.localValue || []).length === (this.options || []).length
      },
      set(val) {
        this.localValue = val ? [...this.options] : []
      }
    }
  },

  methods: {
    checkClearHandler() {
      this.localValue = []
    }
  }
}
</script>

<style lang="scss" scoped>
.checkbox-select {
  width: 100%;
  .checkbox-select__input {
    .checkbox-select__caret {
      width: 25px;
      height: 100%;
      font-size: 14px;
      line-height: 32px;
      color: $text-grey;
      text-align: center;
      cursor: pointer;
      transition: transform 0.3s;
      transform: rotateZ(180deg);
      &.is-reverse {
        transform: rotateZ(0deg);
      }
    }
  }
}
</style>

<style lang="scss">
.checkbox-select {
  .checkbox-select__input {
    .el-input__inner {
      cursor: pointer;
      @include singleline-ellipsis;
    }
  }
}
.checkbox-select__options-wrapper {
  max-height: 248px;
  overflow: auto;
  .checkbox-select__options-item {
    display: block;
    width: 100%;
    height: 30px;
    line-height: 30px;

    &:hover {
      background: $brand-color-1;
    }
  }
  .checkbox-select__empty {
    display: block;
    width: 100%;
    color: $text-grey;
    text-align: center;
  }
}
</style>
>
