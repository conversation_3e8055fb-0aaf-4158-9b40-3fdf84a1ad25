<!-- key-value 模式范例生成 -->
<template>
  <div class="key-genarate">
    <div class="key-genarate__content">
      <div v-for="(paramItem, index) in localOriginalParam" :key="index" class="content__item">
        <el-input
          v-model="paramItem.key"
          :autosize="{ minRows: 1, maxRows: 5 }"
          class="item__key"
          :disabled="readOnly"
          type="textarea"
          @change="paramChangeHandler"
        ></el-input>
        <el-input
          v-model="paramItem.exampleValue"
          :autosize="{ minRows: 1, maxRows: 5 }"
          class="item__example"
          :disabled="readOnly"
          type="textarea"
          @change="paramChangeHandler"
        ></el-input>
        <el-button
          class="item__btn"
          :disabled="readOnly"
          icon="el-icon-delete"
          type="text"
          @click="deleteOneHandler(index)"
        ></el-button>
      </div>
      <div class="content__operate">
        <el-button class="operate__btn" :disabled="readOnly" type="text" @click="addOneHandler"
          >+Key-value</el-button
        >
      </div>
    </div>
    <div class="key-genarate__footer">
      <el-button
        class="footer__btn"
        :disabled="readOnly || !localOriginalParam.length"
        @click="nextHandler"
        >{{ $t('operation.nextStep') }}</el-button
      >
    </div>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import { PARAMETER_VAL_TYPE } from './webhook-constant'

export default {
  name: 'KeyGen',
  props: {
    readOnly: {
      type: Boolean,
      default: false
    },
    parameters: {
      type: Array
    },
    // 注意，k-v 生成模式下，传进来的 originalParam 是 Array
    originalParam: {
      type: Array
    },
    workFlowInfo: {
      type: Object
    }
  },
  data() {
    return {
      localOriginalParam: [{ key: '', exampleValue: '' }],
      localParameters: null // 本地parameters
    }
  },
  mounted() {
    if (xbb._isEmpty(this.originalParam) || !Array.isArray(this.originalParam)) return false

    this.localOriginalParam = xbb.deepClone(this.originalParam)
  },
  methods: {
    addOneHandler() {
      this.localOriginalParam.push({
        key: '',
        exampleValue: ''
      })
      this.$emit('update:originalParam', xbb.deepClone(this.localOriginalParam))
    },
    paramChangeHandler() {
      this.$emit('update:originalParam', xbb.deepClone(this.localOriginalParam))
    },
    deleteOneHandler(index) {
      this.localOriginalParam.splice(index, 1)
      this.$emit('update:originalParam', xbb.deepClone(this.localOriginalParam))
    },

    keyValToParameters(keyVal) {
      return keyVal.map((item) => ({
        key: item.key || '',
        exampleValue: item.exampleValue || '',
        required: 0,
        type: PARAMETER_VAL_TYPE.TEXT
      }))
    },
    validate() {
      const isUnComplete =
        !this.localParameters.length ||
        this.localParameters.some((item) => {
          return !item.key
        })
      if (isUnComplete) {
        this.$message({
          type: 'warning',
          message: this.$t('rule.inComplete', { attr: 'Key-value' })
        })
        return false
      }

      const keySet = new Set(this.localParameters.map((item) => item.key))
      const isKeyDuplicate = keySet.size !== this.localParameters.length
      if (isKeyDuplicate) {
        this.$message({
          type: 'warning',
          message: this.$t('rule.duplicate', { attr: this.$t('lowCode.parameterName') })
        })
        return false
      }
      return true
    },

    nextHandler() {
      this.localParameters = this.keyValToParameters(this.localOriginalParam)

      if (this.validate()) {
        this.$emit('update:parameters', this.localParameters)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.key-genarate {
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
  &__content {
    padding: 6px 12px;
    .content__item {
      display: flex;
      margin: 6px 0;
      .item__key {
        width: 122px;
        margin-right: 6px;
      }
      .item__example {
        flex: 1;
        margin-right: 6px;
      }
    }
    .content__operate {
      .operate__btn {
        padding: 4px 0;
      }
    }
  }
  &__footer {
    padding: 5px 8px;
    border-top: 1px solid $neutral-color-3;
    button {
      height: 28px;
      padding: 4px 16px;
    }
  }
}
</style>
