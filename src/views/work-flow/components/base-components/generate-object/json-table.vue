<!-- json table 生成参考对象回显 -->
<template>
  <div class="json-table">
    <el-table
      class="json-table__content"
      :data="computedParameters"
      max-height="250"
      style="width: 100%"
    >
      <el-table-column
        v-for="header in tableHeaders"
        :key="header.prop"
        :label="header.label"
        :prop="header.prop"
        show-overflow-tooltip
        :width="header.width"
      >
        <template slot-scope="scope">
          <el-checkbox
            v-if="header.prop === 'required'"
            :disabled="readOnly"
            :false-label="0"
            :true-label="1"
            :value="scope.row.required"
            @change="(val) => requiredCheckHandler(val, scope.$index)"
          ></el-checkbox>
          <span v-else>{{ scope.row[header.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="json-table__footer">
      <el-button class="refresh-btn" :disabled="readOnly" @click="refreshHandler">{{
        btnName || $t('workflow.reGenerate')
      }}</el-button>
    </div>
  </div>
</template>

<script>
import { VAL_TYPE_NAME } from './webhook-constant'

export default {
  name: 'JsonTable',
  props: {
    parameters: {
      type: Array
    },
    // 不允许编辑和操作
    readOnly: {
      type: Boolean,
      default: false
    },
    btnName: {
      type: String
    }
  },
  computed: {
    tableHeaders() {
      return [
        { prop: 'key', label: this.$t('lowCode.parameterName'), width: 96 },
        { prop: 'type', label: this.$t('nouns.type') },
        { prop: 'exampleValue', label: this.$t('workflow.exampleValue'), width: 106 },
        { prop: 'required', label: this.$t('form.required'), width: 48 }
      ]
    },
    computedParameters() {
      return this.parameters.map((item) => ({
        ...item,
        type: VAL_TYPE_NAME[item.type] || this.$t('nouns.text')
      }))
    }
  },
  methods: {
    /**
     * @description: 修改必填
     * @param {number} val 1 必填 0 非必填
     * @param {number} index 数据行索引
     * @return {*}
     * @$emit update:parameters 更新parameters值, Array
     */
    requiredCheckHandler(val, index) {
      const parameters = this.parameters.map((item, lineIndex) => ({
        ...item,
        required: lineIndex === index ? val : item.required
      }))
      this.$emit('update:parameters', parameters)
    },
    /**
     * @description: 刷新
     * @return {*}
     * @$emit refresh
     */
    refreshHandler() {
      this.$emit('refresh')
    }
  }
}
</script>

<style lang="scss" scoped>
.json-table {
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
  &__footer {
    padding: 5px 8px;
    button {
      height: 28px;
      padding: 4px 16px;
    }
  }
}
</style>
