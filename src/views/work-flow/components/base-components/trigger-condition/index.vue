<template>
  <div class="flow-trigger-condition">
    <div class="flow-trigger-condition__title">
      <span class="title-content">{{ $t('nouns.triggeringConditions') }}</span>
      <el-dropdown class="title-btn" trigger="click">
        <el-button class="btn-item" :disabled="readOnly" icon="el-icon-plus" type="text">{{
          $t('formDesign.addingConditions')
        }}</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in filterLinkWeightList"
            :key="item.attr"
            :disabled="hasOtherSubFormField(item.attr, triggerConditions) || !addPermission(item)"
            @click.native="addCondition(item)"
          >
            {{ item.attrName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="flow-trigger-condition-content">
      <div
        v-for="(item, index) in triggerConditions"
        :key="`${item.attr}_${index}`"
        class="condition-row"
      >
        <div class="condition-row__attr">{{ item.attrName }}</div>
        <div class="condition-row__setting">
          <div class="symbol">
            <el-select
              v-model="item.symbol"
              :disabled="readOnly || !editPermission(item)"
              :placeholder="$t('placeholder.choosePls', { attr: $t('formDesign.ruleType') })"
            >
              <el-option
                v-for="logic in logicListMap[item.attr]"
                :key="logic.symbol"
                :label="logic.memo"
                :value="logic.symbol"
              />
            </el-select>
          </div>
          <div v-if="!['empty', 'noempty'].includes(item.symbol)" class="value">
            <!-- 这里老逻辑复制过来的，不敢动     fieldInfo 传值需要兼容一下 -->
            <component
              :is="componentName(item.fieldType, item)"
              v-model="item.value"
              class-name-rule="rule-width"
              :condition="item"
              :field-info="getFieldInfo(item.attr, fieldList)"
              :field-list="[]"
              :is-design="false"
              :is-edit="true"
              :is-see="false"
              :is-work-flow="true"
              mode="sub"
              :prop="item.attr"
              :readonly="readOnly || !editPermission(item)"
              :show-label="false"
            />
          </div>
          <el-button
            :disabled="readOnly || !delPermission(item)"
            icon="el-icon-delete"
            size="mini"
            style="font-size: 16px"
            type="text"
            @click="deleteCondition(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import trigglerConditionMixins from '@/views/work-flow/components/base-components/trigger-condition/field-map-mixin.js'
import xbb from '@xbb/xbb-utils'

const MAX_CONDITIONS_LIMIT = 5
export default {
  name: 'TriggerCondition',

  mixins: [trigglerConditionMixins],

  props: {
    fieldList: {
      type: Array,
      default: () => []
    },
    conditions: {
      type: Array,
      default: () => []
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    operatePermission: {
      type: Object,
      default: () => ({
        /**
         * @description: 回调函数用于精准控制具体条件操作权限
         * @param {Object} condition 条件行 or 字段
         * @return {Boolean} true 允许操作
         */
        add: (condition) => true,
        edit: (condition) => true,
        del: (condition) => true
      })
    }
  },
  model: {
    prop: 'conditions',
    event: 'update'
  },

  computed: {
    triggerConditions: {
      get() {
        return this.conditions || []
      },
      set(val) {
        this.$emit('update', val)
      }
    },
    filterLinkWeightList: {
      get() {
        return this.fieldList.filter((item) => {
          const c2 = item.editHide !== 1 // 设计阶段隐藏的字段过滤掉 （https://xbb.yuque.com/xbb/vsf9sv/kubvxk）
          const c3 = typeof item.showType === 'undefined' ? true : item.showType === 0
          return c2 && c3
        })
      }
    },
    logicListMap() {
      const map = {}
      this.fieldList.forEach((item) => {
        map[item.attr] = item.logicList || []
      })
      return map
    }
  },

  methods: {
    hasOtherSubFormField(attr = '', conditions = []) {
      // 如果待判断的是子表单
      if (/subForm/.test(attr)) {
        //  如果条件中有 主 对 子 则不能添加 子 and *
        const hasBaseAndSub = conditions.find((item, _index) => {
          return (
            item.valueType === 1 &&
            item.valueAttr &&
            this.getFieldType(item.valueAttr) !== 'base' &&
            this.getFieldType(item.attr) === 'base'
          )
        })
        if (hasBaseAndSub) {
          return true
        }
        // 如果已经有了 子 对 其他 则只能选这个子
        const hasSubAndOther = conditions.find((item, _index) => {
          return this.getFieldType(item.attr) !== 'base'
        })
        if (hasSubAndOther) {
          return this.getFieldType(attr) !== this.getFieldType(hasSubAndOther.attr)
        }
      }
    },
    getFieldType(attr) {
      if (!attr) return NaN
      if (!/subForm_/.test(attr)) {
        return 'base'
      }
      return attr.replace(/\.\w*$/, '')
    },
    addCondition(item) {
      const { logicList, ...reset } = item
      if (this.triggerConditions.length >= MAX_CONDITIONS_LIMIT) {
        this.$message({
          message: this.$t('message.mostSet', {
            attr: this.$t('nouns.filterCondition'),
            num: MAX_CONDITIONS_LIMIT
          }),
          type: 'warning'
        })
        return
      }
      const condition = { ...reset }
      condition['symbol'] = item.logicList[0]['symbol']
      condition['value'] = [12, 10003].includes(item.fieldType)
        ? { city: '', address: '', district: '', province: '' }
        : undefined

      this.triggerConditions = [...this.triggerConditions, condition]
    },
    deleteCondition(index) {
      this.triggerConditions = this.triggerConditions.filter((_item, idx) => idx !== index)
    },
    validate() {
      const isEmptySence = (val) => ['empty', 'noempty'].includes(val)
      const isNil = (val) => {
        return typeof val === 'undefined' || val === null || val === ''
      }
      const isEmpty = (val) => {
        return typeof val === 'object' && xbb._isEmpty(val)
      }
      const isNilOrEmpty = (val) => {
        return isNil(val) || isEmpty(val)
      }
      const valueIncompleteJudge = (condition) => {
        if (isNilOrEmpty(condition.value)) {
          return true
        } else if (condition.fieldType !== 12) {
          const judgeFunc = Array.isArray(condition.value) ? isNilOrEmpty : isNil
          return Object.values(condition.value).some((subVal) => {
            return judgeFunc(subVal)
          })
        } else {
          // 地址字段为空的特殊判断: 只判断到城市一级
          return isNilOrEmpty(condition.value.city)
        }
      }

      for (const condition of this.triggerConditions) {
        const isEmptySymbol = isEmptySence(condition.symbol)
        const isValueIncomplete = valueIncompleteJudge(condition)
        if (!isEmptySymbol && isValueIncomplete) {
          this.$message.warning(this.$t('workflow.triggerConditionsNotComplete'))
          return false
        }
      }
      return true
    },
    // 新增计算
    addPermission(condition) {
      return this.operatePermission?.add?.(condition) ?? true
    },
    delPermission(condition) {
      return this.operatePermission?.del?.(condition) ?? true
    },
    editPermission(condition) {
      return this.operatePermission?.edit?.(condition) ?? true
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-trigger-condition {
  .flow-trigger-condition__title {
    height: 20px;
    font-size: 0;
    line-height: 20px;
    .title-content {
      display: inline-block;
      height: 20px;
      font-size: 14px;
      font-weight: bolder;
      // line-height: 20px;
      // vertical-align: top;
    }
    .title-btn {
      height: 20px;
      margin-left: 12px;
      // line-height: 20px;
      .btn-item {
        padding: 0;
        line-height: 20px;
        vertical-align: top;
        border: 0;
      }
    }
  }
}
.flow-trigger-condition-content {
  &:not(:empty) {
    margin-top: 16px;
  }
  .condition-row {
    margin-bottom: 10px;
    .condition-row__attr {
      margin-bottom: 5px;
      font-size: 14px;
      line-height: 22px;
    }
    .condition-row__setting {
      display: flex;
      justify-content: space-between;
      .symbol {
        width: 90px;
        min-width: 90px;
      }
      .value {
        // width: 300px;
        width: 270px;
        margin: 0 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.flow-trigger-condition-content .condition-row .condition-row__setting .value {
  .custom-checkbox__wrapper {
    width: 100% !important;
  }
}
.el-dropdown-menu {
  max-height: 200px;
  overflow: auto;
}
</style>
