<!-- 短信节点的短信内容属性设置 -->
<template>
  <div class="message-content__wrap">
    <FlowSettingLayout :title="$t('business.messageContent')">
      <!-- 新建短信模板按钮 -->
      <p slot="title" class="flow-setting-layout__memo">
        {{ $t('workflowDesign.selectApproved') }}
      </p>
      <!--选择短信模板按钮 -->
      <div
        v-if="!smsTemplate.smsTemplateId"
        class="select-template-button"
        :class="{ disabled }"
        @click="handleSelectBtnClick"
      >
        <i class="el-icon-plus" style="color: #ff8c2e"></i>
        <span class="button-text">{{ $t('workflowDesign.clickSelectTemplate') }}</span>
      </div>
      <!-- 短信模板回显组件 -->
      <div v-else class="message-template__wrap">
        <!--        -->
        <div class="padding-box template-name-header">
          <span class="template-name">
            {{ smsTemplate.templateName }}
          </span>
        </div>

        <!-- 短信模板内容 -->
        <div class="padding-box template-content__wrap">
          <p>
            <span class="label-text text"> {{ $t('workflowDesign.smsSign') }}：</span>
            <span class="content-text text">
              {{ smsTemplate.signature }}
            </span>
          </p>
          <p>
            <span class="label-text text">{{ $t('workflowDesign.smsContent') }}：</span>
            <span class="content-text text">
              {{ smsTemplate.content }}
            </span>
          </p>
        </div>
        <!-- 操作栏 -->
        <div class="padding-box template-action__wrap">
          <span class="delete-btn" :class="{ disabled }" @click="clearTemplate">{{
            $t('operation.delete')
          }}</span>
          <span class="change-btn" :class="{ disabled }" @click="changeTemplate">{{
            $t('operation.change')
          }}</span>
        </div>
      </div>
      <!-- 短信模板选择弹窗-->
      <message-template-dialog
        :show.sync="dialogVisible"
        :sms-template="smsTemplate"
        @close-dialog="handleCloseDialog"
        @save-template="handleSaveTemplate"
      />
    </FlowSettingLayout>
  </div>
</template>

<script>
import FlowSettingLayout from '../flow-setting-layout'
import MessageTemplateDialog from './message-template-dialog'

export default {
  name: 'MessageContent',
  components: {
    MessageTemplateDialog,
    FlowSettingLayout
  },
  props: {
    smsTemplate: {
      type: Object,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {
    /**
     * 选择短信模板按钮的触发事件
     */
    handleSelectBtnClick() {
      if (this.disabled) return

      this.dialogVisible = true
    },
    /**
     * 关闭短信模板选择弹窗的触发事件
     */
    handleCloseDialog() {
      this.dialogVisible = false
    },

    /**
     * 选择短信模板后点击保存
     */
    handleSaveTemplate(template) {
      this.$emit('change-template', template)
      this.handleCloseDialog()
    },

    /**
     * 删除已选择的短信模板
     */
    clearTemplate() {
      if (this.disabled) return

      this.$emit('change-template', {})
    },

    /**
     * 更换选择短信模板
     */
    changeTemplate() {
      if (this.disabled) return

      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.message-content__wrap {
  // 新建模板按钮
  .title-button {
    font-size: 12px;
    color: $brand-color-5;
    cursor: pointer;
  }

  // 选择模板按钮
  .select-template-button {
    width: 100%;
    height: 80px;
    margin-top: 20px;
    line-height: 80px;
    text-align: center;
    cursor: pointer;
    border: 1px dashed $neutral-color-3;
    border-radius: 4px;
    .button-text {
      color: $text-auxiliary;
    }
    &.disabled {
      cursor: not-allowed;
      i {
        color: $text-auxiliary;
      }
    }
  }

  // 模板回显组件
  .message-template__wrap {
    margin-top: 16px;
    border: 1px solid rgba(220, 223, 230, 1);
    border-radius: 4px;
    .padding-box {
      padding: 12px 11px;
    }
    // 模板名称及审核状态
    .template-name-header {
      color: $text-plain;
      background: $neutral-color-1;
      .template-status {
        float: right;
      }
    }
    // 短信模板内容
    .template-content__wrap {
      font-size: 13px;
      line-height: 20px;
      border-color: rgba(220, 223, 230, 1);
      border-style: solid;
      border-width: 1px 0;
      p {
        padding-bottom: 8px;
        .text {
          display: inline-block;
        }
        .label-text {
          width: 65px;
          color: $text-main;
          vertical-align: top;
        }
        .content-text {
          width: 295px;
          color: $text-plain;
        }
      }
    }

    // 操作栏
    .template-action__wrap {
      display: flex;
      flex-direction: row-reverse;
      span {
        margin-right: 10px;
        color: $brand-color-5;
        cursor: pointer;
        &.disabled {
          color: $text-auxiliary;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
