<!-- eslint vue/no-mutating-props: 1 -->

<template>
  <div>
    <FlowSettingLayout :title="$t('workflowDesign.nodeName')">
      <el-input v-model="flowParams.name" :disabled="isEnable" />
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('mail.outMail')">
      <el-select
        v-model="nodeConfig.sender"
        class="w-full"
        :disabled="isEnable"
        :placeholder="$t('placeholder.choosePls', { attr: $t('mail.outMail') })"
      >
        <el-option
          v-for="item in systemMailList"
          :key="item.account"
          :label="item.account"
          :value="item.account"
        >
        </el-option>
      </el-select>
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('mail.recipients')">
      <NodePersonSelect
        :disabled="isEnable"
        :error-list="errorNodePersonSelectUserList"
        :text="$t('mail.clickToSetRecipients')"
        :user-list="nodePersonSelectUserList"
        @click="addUserClick"
      />
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('mail.mailSubject')">
      <NodePersonSelect
        :disabled="isEnable"
        :error-list="errorFields.title"
        is-info-content
        :text="$t('mail.clickToSetMailSubject')"
        :user-list="nodeConfig.title"
        @click="addMailSubject"
      />
    </FlowSettingLayout>
    <FlowSettingLayout :title="$t('mail.mailContent')">
      <div
        v-if="nodeConfig.mailContentTemplate"
        class="mail-content"
        :class="{ disabled: isEnable }"
        @click="addMailContent"
        v-html="convertString(nodeConfig.mailContentTemplate)"
      ></div>
      <div
        v-else
        class="mail-content-empty"
        :class="{ disabled: isEnable }"
        @click="addMailContent"
      >
        <i class="el-icon-plus"></i>
        <span>{{ $t('mail.clickToSetMailContent') }}</span>
      </div>
    </FlowSettingLayout>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import FlowSettingLayout from './base-components/flow-setting-layout'
import NodePersonSelect from './base-components/node-person-select'
import { coreNameMap } from '@/views/work-flow/flow-design-map.js'
import { FLOW_TYPE } from '@/constants/common/workflow'

import { getMailAccountList } from '@/api/mail'

export default {
  name: 'SendMailBlock',

  components: { FlowSettingLayout, NodePersonSelect },

  mixins: [],

  props: {
    itemNode: {
      type: Object,
      required: true,
      default: () => ({})
    },
    flowParams: {
      // 后端返回的核心配置对象
      type: Object,
      default: () => ({})
    },
    workFlowInfo: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      systemMailList: []
    }
  },

  computed: {
    flowTypeCheck() {
      return {
        isWebhookFlow: this.workFlowInfo.type === FLOW_TYPE.WEB_HOOK
      }
    },
    nodeConfig() {
      return this.flowParams && this.flowParams.coreConfiguration.sendMailNode
    },
    addUserConfig() {
      const type = ['mailRecipient', 'externalUserList']
      const showTabsName = [
        'dept',
        'user',
        'role',
        'userGroup',
        'workflowDynamicManager',
        'externalMember'
      ]
      const showTabsNameMap = {
        // 配置对应的tab存入哪些属性中
        dept: 'mailRecipient',
        user: 'mailRecipient',
        role: 'mailRecipient',
        userGroup: 'mailRecipient',
        workflowDynamicManager: 'mailRecipient',
        externalMember: 'externalUserList'
      }
      // TODO: 先做成只有webhook支持，后续其他节点等后端支持再放开
      // if (this.flowTypeCheck.isWebhookFlow) {
      //   type.push('externalUserList')
      //   showTabsName.push('externalMember')
      //   showTabsNameMap.externalMember = 'externalUserList'
      // }

      return {
        type,
        showTabsName,
        selectNodeName: coreNameMap[this.itemNode.type],
        showTabsNameMap
      }
    },
    nodePersonSelectUserList() {
      return this.getUserList(this.addUserConfig, this.nodeConfig)
    },
    errorFields() {
      return this.nodeConfig.errorSet ?? {}
    },
    errorNodePersonSelectUserList() {
      return this.getUserList(this.addUserConfig, this.errorFields)
    },
    isEnable() {
      return this.workFlowInfo.enable === 1
    }
  },

  watch: {},

  created() {
    const params = {
      isSystem: 1,
      isPersonal: 0,
      pageNumber: 1,
      pageSize: 9999
    }
    getMailAccountList(params).then((res) => {
      this.systemMailList = res.result.list
    })
  },

  beforeDestroy() {},

  methods: {
    getUserList(config, node) {
      if (!config) return []

      return config?.type
        .map((attr) => {
          let userList = node?.[attr] ?? []
          // 节点参数人员需要额外标识下
          if (config?.showTabsNameMap?.externalMember === attr) {
            userList = userList.map((tag) => ({
              ...tag,
              property: 'externalMember'
            }))
          }
          return userList
        })
        .flat()
    },
    addUserClick() {
      this.$emit('addUserClick', this.addUserConfig)
    },
    addMailSubject() {
      this.$emit('addManualContent', {
        dialogTitle: '邮件主题',
        contentKey: 'title',
        manualTagLengthLimit: 20,
        manualTagLimitNum: this.flowParams.titleAttrNum ?? 3
      })
    },
    addMailContent() {
      if (this.isEnable) return
      this.$emit('addMailContent')
    },
    convertString(htmlString) {
      const imgReg = /<img.*?>/g
      htmlString = htmlString.replace(imgReg, '[图片]')

      const doc = new DOMParser().parseFromString(htmlString, 'text/html')
      const textContent = doc.body.textContent || ''
      return textContent.trim()
    },
    checkFlowParams() {
      if (!this.flowParams.name.trim()) {
        this.$message({
          message: this.$t('workflow.notSetNodeName'),
          type: 'warning'
        })
        return false
      }
      // 收件人校验
      if (!this.nodePersonSelectUserList?.length) {
        this.$message({
          message: `"${this.flowParams.name}" ${this.$t('workflow.notSetMailRecipient')}`,
          type: 'warning'
        })
        return false
      }
      // 邮件主题
      if (!this.nodeConfig.title.length) {
        this.$message({
          message: this.$t('workflow.notSetMailSubject'),
          type: 'warning'
        })
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.mail-content {
  box-sizing: border-box;
  height: 75px;
  padding: 10px 16px;
  font-size: 14px;
  line-height: 20px;
  color: $text-plain;
  cursor: pointer;
  border: 1px dashed $neutral-color-3;
  @include multiline-ellipsis(3);
  &.disabled {
    cursor: not-allowed;
  }
}

.mail-content-empty {
  height: 80px;
  font-size: 13px;
  line-height: 80px;
  color: $text-auxiliary;
  text-align: center;
  cursor: pointer;
  border: 1px dashed $neutral-color-3;
  i {
    color: $brand-color-5;
  }
  &.disabled {
    cursor: not-allowed;
    i {
      color: $text-auxiliary;
    }
  }
}
</style>
