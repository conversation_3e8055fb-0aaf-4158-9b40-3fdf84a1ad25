<template>
  <div class="container">
    <div v-if="isH5" class="back">
      <router-link to><span @click="$router.go(-1)">返回</span></router-link>
    </div>
    <div class="book">
      <h2>账号注销协议</h2>
      <p>您在申请注销流程中点击同意前，应当认真阅读《账号注销协议》（以下简称“本协议”）。</p>
      <p>
        特别提醒您，当您成功提交注销申请后，即表示您已充分阅读、理解并接受本协议的全部内容。阅读本协议的过程中，如果您不同意相关任何条款，请您立即停止账号注销程序。如您对本协议有任何疑问，可联系我们的客服。
      </p>
      <ol>
        <li>您的账号注销后，该账号在原绑定企业中状态按照离职员工的方式处理。</li>
        <li>
          您应确保您有权决定该账号的注销事宜，不侵犯任何第三方的合法权益，如因此引发任何争议，由您自行承担。
        </li>
        <li>
          您理解并同意，账号注销后我们没有义务为您保留原账户中或与之相关的任何信息，也无法协助您重新恢复账号。请您在申请注销前自行备份您欲保留的本账号信息和数据，并请确认与本销帮帮账号相关的所有服务均已进行妥善处理。
        </li>
        <li>
          注销账号后，您将无法再使用本账号，也将无法找回您账号中及与账号相关的任何内容或信息，包括但不限于：
          <ol>
            <li>您将无法继续使用该账号进行登录；</li>
            <li>您账号的个人资料和历史信息（包含昵称、头像、消息记录、评论等）都将无法找回；</li>
          </ol>
        </li>
        <li>
          在账号注销期间，如果您的账号被他人投诉、被国家机关调查或者正处于诉讼、仲裁程序中，我们有权自行终止您的账号注销程序，而无需另行得到您的同意。
        </li>
        <li>请注意，注销您的账号并不代表本账号注销前的账号行为和相关责任得到豁免或减轻。</li>
      </ol>
    </div>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'

export default {
  name: 'AccountCancellationAgreement',
  data() {
    return {
      isH5: xbb.isThirdPC(['h5'], true)
    }
  }
}
</script>

<style lang="scss" scoped>
@import './book.scss';
</style>
