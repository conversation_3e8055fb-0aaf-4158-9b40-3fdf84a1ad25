import i18n from '@/lang/index.js'

export const passwordRule = [
  {
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (/^.*(?=.{8,16}).*$/.test(value)) {
        callback()
      } else {
        callback(new Error('密码长度需在8-16位之间'))
      }
    }
  },
  {
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (/^.*(?=.*\d).*$/.test(value)) {
        callback()
      } else {
        callback(new Error('至少包含一个数字'))
      }
    }
  },
  {
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (/^.*(?=.*[A-Z])(?=.*[a-z]).*$/.test(value)) {
        callback()
      } else {
        callback(new Error('需包含大写与小写字母'))
      }
    }
  }
]

export const mobileRule = {
  validator(rule, value, callback) {
    if (/^1[3456789]\d{9}$/.test(value)) {
      callback()
    } else {
      callback(new Error(i18n.t('standLogin.enterCorrectMobileNumber')))
    }
  },
  trigger: 'blur'
}

// 手机号/邮箱 输入框校验
export const accountRule = {
  validator(rule, value, callback) {
    if (/^1[3456789]\d{9}$/.test(value)) {
      callback()
    } else if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的账号'))
    }
  },
  trigger: 'blur'
}
// 邮箱校验
export const emailRule = {
  validator(rule, value, callback) {
    if (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的邮箱'))
    }
  },
  trigger: 'blur'
}
