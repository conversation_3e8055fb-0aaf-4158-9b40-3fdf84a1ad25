<!--
 * @Description: 工作报告
 -->
<!--
组件关联页面：工作报告列表
-->
<template>
  <div class="list work-report">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :show-search="false"
        :top-permissions="topPermissions"
        @topBtnHandle="topBtnHandle4WorkReport"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <!-- 上方筛选 -->
        <!-- <list-panel-special
          :showFilterList="specialFilter"
          :commonFilterList="commonFilter"
          @getFormData="panelFilterParams"
          ></list-panel-special> -->
        <!--
            :form-id="formId"
            :custom-mode-config="{ viewConfig, viewButton }"
            -->
        <list-panel
          :custom-mode-config="{ viewButton }"
          :show-column-setting="false"
          :show-filter-list="specialFilter"
          :show-switch-advanced="false"
          @getFormData="panelFilterParams"
        ></list-panel>
        <div v-if="reportList.length" class="commun-wrap">
          <div ref="scroll" class="com-left">
            <ul class="left-report">
              <template v-for="(dayReport, index) in reportList">
                <li :key="index" class="dayItem">
                  <ul>
                    <li v-if="dayReport[0]" class="item time">
                      <span class="detail-time">{{ formatDataCh(dayReport[0].reportDate) }}</span>
                      <!-- <span class="el-icon-caret-bottom"></span> -->
                    </li>
                    <template v-for="report in dayReport">
                      <li
                        :key="report.id"
                        class="item report"
                        :class="{ active: curIndex === report.id }"
                        @click="getReportDetail(report.id, report)"
                      >
                        <div class="report-left">
                          <span v-if="report.type === 1" class="dayReport tag">{{
                            $t('CRM.dayRecord')
                          }}</span>
                          <span v-else-if="report.type === 2" class="tag weekReport">{{
                            $t('CRM.weekRecord')
                          }}</span>
                          <span v-else-if="report.type === 3" class="monthReport tag">{{
                            $t('CRM.monthRecord')
                          }}</span>
                        </div>
                        <div class="report-right">
                          <div class="report-first">
                            <span
                              v-if="report.type === 1"
                              class="name"
                              v-html="addNodeByWX(report.userName) + $t('CRM.ofDayRecord')"
                            ></span>
                            <span
                              v-if="report.type === 2"
                              class="name"
                              v-html="addNodeByWX(report.userName) + $t('CRM.ofWeekRecord')"
                            ></span>
                            <span
                              v-if="report.type === 3"
                              class="name"
                              v-html="addNodeByWX(report.userName) + $t('CRM.ofMonthRecord')"
                            ></span>
                            <span
                              class="report-time"
                              :title="report.reportDate"
                              v-html="report.reportDate"
                            ></span>

                            <span v-if="report.isReview" class="Read">
                              <span> {{ report.reviewerNum }}人 </span>
                              {{ $t('CRM.read') }}
                            </span>
                            <span v-else class="noRead">{{ $t('CRM.notRead') }}</span>
                          </div>
                          <div class="report-msg">
                            {{ report.content }}
                          </div>
                        </div>
                        <span v-if="report.isReissue === 1" class="bufa"
                          ><b class="fon">补</b></span
                        >
                      </li>
                    </template>
                  </ul>
                </li>
              </template>
              <li class="center">
                <i v-if="showMore" class="el-icon-loading"></i>
                <span v-else>{{ $t('message.noMore') }}</span>
              </li>
            </ul>
          </div>

          <div v-loading="detailLoading" class="com-right">
            <div class="headline">
              <div class="first">
                <div>
                  <span v-if="isMyReport" class="reportName">{{ $t('CRM.myReport') }}</span>
                  <span v-else class="reportName">{{ reportDetail.onlyUserName + '的报告' }}</span>
                  <span v-if="reportDetail.type === 1" class="dayReport tag">{{
                    $t('CRM.dayRecord')
                  }}</span>
                  <span v-else-if="reportDetail.type === 2" class="tag weekReport">{{
                    $t('CRM.weekRecord')
                  }}</span>
                  <span v-else-if="reportDetail.type === 3" class="monthReport tag">{{
                    $t('CRM.monthRecord')
                  }}</span>
                  <span v-if="reportDetail.isReissue === 1" class="bufaReport tag">补</span>
                  <i
                    v-if="hasOuterLinkShare === 1"
                    class="icon-fenxiang iconfont outer-link-share"
                    @click="getOuterLinkShare"
                  ></i>
                </div>
                <el-button-group>
                  <el-button
                    v-if="isMyReport && editPermission"
                    icon="el-icon-edit"
                    plain
                    @click="editWorkReport(reportDetail.id)"
                    >{{ $t('operation.edit') }}</el-button
                  >
                  <el-button
                    v-if="(isMyReport && delPermission) || rolePermission"
                    icon="el-icon-delete"
                    plain
                    style="color: red"
                    @click="
                      deleteSingle({
                        dataIdList: [reportDetail.id],
                        specialAppInfo: { formId: formMsg.formId }
                      })
                    "
                    >{{ $t('operation.delete') }}</el-button
                  >
                  <el-button v-if="printPermission" @click="beforePrint(reportDetail)">
                    <i class="el-icon-printer" style="display: inline-block; margin-right: 5px"></i
                    >{{ $t('CRM.print') }}</el-button
                  >
                </el-button-group>
              </div>
              <div class="two">
                <div class="two-user">
                  <span class="two-first"
                    >{{ $t('CRM.submitPerson') + ':'
                    }}<span v-html="addNodeByWX(reportDetail.userName)"></span
                  ></span>
                  <span v-if="reportDetail.copySomeOne" class="last-child"
                    >{{ $t('business.cCPerson') + ':' }}
                    <el-tooltip :content="reportDetail.copySomeOne" placement="top">
                      <span v-html="addNodeByWX(reportDetail.copySomeOne)"></span>
                    </el-tooltip>
                  </span>
                </div>
                <span
                  >{{ $t('CRM.submitTime') + ':' }}<span>{{ reportDetail.addTime }}</span></span
                >
              </div>
              <div class="three">
                <span class="first-child"
                  >{{ $t('CRM.reportTime') + ':' }}<span>{{ reportDetail.reportDate }}</span></span
                >
                <span class="last-child"
                  >{{ $t('nouns.updateTime') + ':'
                  }}<span>{{ reportDetail.updateTime }}</span></span
                >
              </div>
            </div>
            <div v-if="isStatisticContentEmpty(statisticContent)" class="newAdd">
              <div
                class="left-tag"
                :class="{
                  dayReport: reportDetail.type === 1,
                  weekReport: reportDetail.type === 2,
                  monthReport: reportDetail.type === 3
                }"
              >
                {{ addData }}
              </div>
              <ul class="right-tag">
                <template v-for="(item, index) in statisticResultList">
                  <li :key="index" class="right-item" @click="pierceThrough(item.through)">
                    <span class="number">{{ item.value }}</span>
                    <span>{{ item.name }}</span>
                  </li>
                </template>
                <!-- <li class="right-item" v-if="statisticContent.customerAddCount !== undefined" @click="pierceThrough(100)">
                    <span class="number">{{formatIntStatisticContent(statisticContent.customerAddCount)}}</span>
                    <span>客户/个</span>
                  </li>
                  <li class="right-item" v-if="statisticContent.communicateAddCount !== undefined" @click="pierceThrough('communicate')">
                    <span class="number">{{formatIntStatisticContent(statisticContent.communicateAddCount)}}</span>
                    <span>跟进记录/次</span>
                  </li>
                  <li class="right-item" v-if="statisticContent.opportunityAddCount !== undefined" @click="pierceThrough('opportunity')">
                    <span class="number">{{formatIntStatisticContent(statisticContent.opportunityAddCount)}}</span>
                    <span>机会/个</span>
                  </li>
                  <li class="right-item" v-if="statisticContent.oppAmount !== undefined" @click="pierceThrough('opportunity')">
                    <span class="number">{{formatStatisticContent(statisticContent.oppAmount)}}</span>
                    <span>机会金额/元</span>
                  </li>
                  <li class="right-item" v-if="statisticContent.contractAddCount !== undefined" @click="pierceThrough('contract')">
                    <span class="number">{{formatIntStatisticContent(statisticContent.contractAddCount)}}</span>
                    <span>合同/份</span>
                  </li>
                  <li class="right-item" v-if="statisticContent.contractAmount !== undefined" @click="pierceThrough('contract')">
                    <span class="number">{{formatStatisticContent(statisticContent.contractAmount)}}</span>
                    <span>合同金额/元</span>
                  </li>
                  <li class="right-item" v-if="statisticContent.paymentSheetAmount !== undefined" @click="pierceThrough('paymentSheet')">
                    <span class="number">{{formatStatisticContent(statisticContent.paymentSheetAmount)}}</span>
                    <span>回款金额/元</span>
                  </li> -->
              </ul>
            </div>
            <div class="tabs">
              <DetailTabs
                v-if="!detailLoading"
                :active-name.sync="activeName"
                :detail-tabs="detailTabs"
                :form-query="catchAppInfo"
                :query="tabQuery"
              />
            </div>
            <div class="item-options">
              <commentAndLike
                :app-info="getAppInfo"
                :comment-count.sync="reportDetail.commentCount"
                :detail-info="reportDetail"
              >
                <div slot="notice">
                  <el-button class="reviewer" type="text" @click="showReviewerPeople = true">
                    {{ $t('operation.view') }}
                  </el-button>
                  <span class="read-status">
                    {{ $t('CRM.readed') + ': ' }}{{ reviewerArray.length }}
                  </span>
                  <span class="read-status">
                    {{ $t('CRM.unread') + ': ' }}{{ unReadArray.length }}
                  </span>
                </div>
              </commentAndLike>
            </div>
            <!-- <report :refId="reportDetail.id" :refType="'workReport'" v-if="isShowComment" :commentCount.sync="reportDetail.commentCount"></report> -->
          </div>
        </div>
        <div v-else class="commun-wrap">
          <v-empty :notice="$t('CRM.no') + pageInfo.pageTitle"></v-empty>
        </div>
      </div>
      <template slot="layout-dialog">
        <transition name="slide">
          <reviewer-people
            v-if="showReviewerPeople"
            ref="reviewerPeopleBox"
            :reviewer-array="reviewerArray"
            :un-read-array="unReadArray"
            @close="showReviewerPeople = false"
            @sendMessageInform="sendMessageInform"
          ></reviewer-people>
        </transition>
        <transition name="fade">
          <div
            v-if="showReviewerPeople"
            class="mask"
            style="opacity: 0"
            @click="showReviewerPeople = false"
          ></div>
        </transition>
        <!-- 穿透 -->
        <!-- <transition name="slide">
          <pierce-through ref="pierceBox" v-if="showPierceBox"
          :appInfo="getAppInfo"
          :selectType="selectType"
          :reportId="reportDetail.id"
          @close="showPierceBox = false"></pierce-through>
        </transition>
        <transition name="fade">
          <div class="mask" style="opacity: 0;" v-if="showPierceBox" @click="showPierceBox=false"></div>
        </transition> -->
      </template>
    </v-top-bottom-page>
    <!-- 穿透 -->
    <div v-if="showPierceBox" class="penetrate">
      <penetrate
        :back-title="'工作报告'"
        :penetrate-visible.sync="showPierceBox"
        :throught="penetrateThrough"
      ></penetrate>
    </div>
  </div>
</template>

<script>
import penetrate from '@/views/penetrate'
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'
import commentAndLike from '@/components/common/comment-and-like/'
import DetailTabs from '@/components/form-data-detail/components/detail-tabs'
import reviewerPeople from './components/reviewer-people'
import {
  getWorkReportList,
  getWorkReportDetail,
  getWorkReportVisibleRange,
  getWorkReportFilterList,
  sentWorkReportRemind
} from '@/api/form-special-list.js'
import { setLikeAdd } from '@/api/detail-handle.js'
import { getFormDataDetail } from '@/api/form-detail'
import { exportWorkReportFormData, getExplainList } from '@/api/export.js'
import { getOuterLinkShareUrl } from '@/api/outer-link.js'
import xbb from '@xbb/xbb-utils'
import { copyLink } from '@/views/scrm/utils/index.js'
// components
import ListHead from '@/components/list/container-view/list-head/index.vue'
import ListPanel from '@/components/list/container-view/list-panel/index.vue'

export default {
  name: 'WorkReport',

  components: {
    CommentAndLike: commentAndLike,
    ReviewerPeople: reviewerPeople,
    DetailTabs,
    Penetrate: penetrate,
    ListHead,
    ListPanel
  },

  mixins: [listMixin, listEditMixin],

  provide: function () {
    return {
      uiPaasPreviewExplainList: {},
      uiPaasPreviewPaasEntity: {},
      parentDetail: this // 用于详情的tab中的关联跳转
    }
  },

  data() {
    return {
      detailTabs: [
        {
          key: 'baseDetailTab',
          name: this.$t('CRM.basicDetail')
        }
      ],
      activeName: 'baseDetailTab',
      tabQuery: {},
      penetrateThrough: '', // 穿透参数
      statisticResultList: [], // 穿透数据
      showPierceBox: false, // 穿透显示
      selectType: -1,

      showReviewerPeople: false, // 已读人员弹框
      formMsg: {}, // 详情的基本信息
      reportList: [],
      sendReportType: 1,
      curIndex: 0,
      detailLoading: false,
      reportDetail: {},
      statisticContent: {},
      thisPlan: {},
      nextPlan: {},
      copySomeone: [],
      isMyReport: false,
      showWatchNum: false,
      delPermission: false,
      editPermission: false,
      printPermission: false,
      rolePermission: false, // 增加超管和老板具有删除权限
      addData: this.$t('CRM.todayAdd'),
      thisPlanName: this.$t('CRM.todayTarget'),
      nextPlanName: this.$t('CRM.tomorrowTarget'),
      likeBox: false,
      isShowComment: false,
      reviewerArray: [], // 已读人员列表
      unReadArray: [], // 未读人员列表
      showMore: true,
      parSubFieldList: [], // 自定义导出 - 表单字段
      hasOuterLinkShare: 0,
      outerLinkShareUrl: '', //外链分享的url
      // TODO: ui-paas兼容list-panel
      viewButton: [
        {
          attr: 'filter', //按钮属性值
          disabled: 0, //是否禁用(0未禁用1禁用)
          distributorMark: 0,
          icon: 'icon-shitu', //按钮icon
          needRemind: 0,
          type: '3',
          value: '筛选', //按钮文本
          sort: 1 //排序
        },
        {
          attr: 'fullScreen', //按钮属性值
          disabled: 0, //是否禁用(0未禁用1禁用)
          distributorMark: 0,
          icon: 'icon-shitu', //按钮icon
          needRemind: 0,
          type: '5',
          value: '全屏', //按钮文本
          sort: 2 //排序
        }
      ]
    }
  },

  mounted() {
    // 获取操作权限
    this.getVisibleRange()
  },
  methods: {
    // 获取列表
    getSpecialFormList(params) {
      const { commonFilter } = params
      const newParams = {
        ...commonFilter,
        ...params
        // type: this.reportDetail.type
      }
      // 分组id，根据分组id改变显示内容
      this.sendReportType = params.listGroupId
      // 删除多余不需要传递的字段
      delete newParams.commonFilter
      // 如果没有筛选项，则获取筛选项
      !this.commonFilter.length &&
        getWorkReportFilterList(params)
          .then((data) => {
            this.commonFilter = data.result.fieldList
          })
          .catch(() => {})
      this.exportParams = newParams
      newParams['formId'] = this.$refs.listHead.curTemplate.formId
      // 获取左侧列表
      getWorkReportList(newParams)
        .then((data) => {
          this.disposeCommonFormListData(data)
          this.showMore = false
          // if (data.result) {
          //   this.total = data.result.length
          // }
          // this.receivedStatistics = data.receivedStatistics || {}
          // this.sentStatistics = data.sentStatistics || {}
          const list = data.result.list
          const reportList = []
          let object = [list[0]]
          if (list.length) {
            for (let i = 1; i < list.length; i++) {
              if (list[i].reportDate === list[i - 1].reportDate) {
                object.push(list[i])
              } else {
                reportList.push(object)
                object = []
                object.push(list[i])
              }
            }
          } else {
            this.reportList = []
            return
          }
          reportList.push(object)
          // this.pageHelper = data.pageHelper
          // 通过currentPage来判断当前是下拉加载还是筛选操作
          if (this.pageHelper.currentPage === 1) {
            // 初始化左侧滚动加载
            this.$nextTick(() => {
              const el = this.$refs.scroll
              const that = this
              if (el) {
                el.onscroll = function (e) {
                  const st = el.scrollTop
                  const oh = el.offsetHeight
                  const sh = el.scrollHeight
                  if (st + oh > sh - 20 && !that.showMore) {
                    that.loadMore()
                  }
                }
              }
            })
            this.getReportDetail(list[0].id, list[0])
            this.reportList = []
            this.reportList = reportList
          } else {
            if (
              reportList[0][0].reportDate ===
              this.reportList[this.reportList.length - 1][0].reportDate
            ) {
              this.reportList[this.reportList.length - 1] = this.reportList[
                this.reportList.length - 1
              ].concat(reportList[0])
              reportList.shift()
              this.reportList = this.reportList.concat(reportList)
            } else {
              this.reportList = this.reportList.concat(reportList)
            }
          }
          this.parSubFieldList = data.result.explainList // parSubFieldList：自定义导出的表单字段列表
        })
        .catch(() => {})
    },

    /**
     * @description: 工作报告导出前的数据处理
     * @param {type}
     * @return:
     */
    beforeExport() {
      if (this.reportList.length === 0) return
      const param = this.exportParams
      // 工作报告筛选改造,为适应原导出接口,在外层补上报告类型type
      const isAllType =
        this.$refs.listHead.curTemplate.formId === 0 ||
        this.$refs.listHead.curTemplate.formName === '全部'
      if (!isAllType) {
        param['type'] = this.reportDetail ? this.reportDetail.type : 1
      }
      param.conditions.forEach((e) => {
        if (e.saasAttr === 'type') {
          param['type'] = e.value[0]
        }
      })
      const params = {
        promise: exportWorkReportFormData({ ...param })
      }
      this['proExport/startExport'](params)
    },

    // 自定义导出
    beforeCustomExport() {
      if (this.reportList.length === 0) return
      getExplainList({ ...this.getAppInfo, isSync: true, groupId: 0, saasMark: 1 })
        .then(({ result: { explainList, menuName } }) => {
          const parSubFieldList = explainList
          const param = this.exportParams
          const isAllType =
            this.$refs.listHead.curTemplate.formId === 0 ||
            this.$refs.listHead.curTemplate.formName === '全部'
          if (!isAllType) {
            param['type'] = this.reportDetail ? this.reportDetail.type : 1
          }
          // 工作报告筛选改造,为适应原导出接口,在外层补上报告类型type
          param.conditions.forEach((e) => {
            if (e.saasAttr === 'type') {
              param['type'] = e.value[0]
            }
          })

          const {
            appId,
            formId,
            businessType,
            subBusinessType
            // menuId
          } = this.getAppInfo

          const params = {
            title: this.pageInfo.pageTitle,
            parSubFieldList,
            appId,
            formId,
            businessType,
            subBusinessType,
            commonListExportParams: { ...param }, // 通用导出需要的参数
            specialExport: exportWorkReportFormData, // 特殊导出的方法
            count: this.pageHelper.total,
            menuName
            // exportFormData // 通用导出的方法
          }
          // params['count'] = this.getSelectDataIds.length === 0 ? this.pageHelper.total : this.getSelectDataIds.length // 表单数据总数，如果超过10000条禁止使用合并单元格
          this['proExport/openCustomExportDialog'](params)
        })
        .catch(() => {})
    },

    // 顶部按钮操作
    topBtnHandle4WorkReport(method) {
      const type = method.attr
      switch (type) {
        // 日报新建
        case 'workReportDaily':
          this.addWorkReport(2102)
          break
        // 周报新建
        case 'workReportWeekly':
          this.addWorkReport(2103)
          break
        // 月报新建
        case 'workReportMonthly':
          this.addWorkReport(2104)
          break
        case 'export':
          utils.isBindWXApp({
            onSuccess: (_) => {
              this.beforeExport()
            },
            store: this.$store
          })
          break
        case 'customExport': // 显示自定义导出窗口
          this.beforeCustomExport()
          break
        case 'statistics':
          this.$router.push({ name: 'workReportStatistical' })
          break
      }
    },
    // 新建工作报告
    addWorkReport(subBusinessType) {
      const { appId, menuId, formId, saasMark, businessType } = this.getAppInfo
      const params = {
        appId,
        menuId,
        formId,
        saasMark,
        businessType,
        subBusinessType
      }
      // 新建接口，打开弹窗
      this.formDataAdd(params)
    },

    /**
     * @description: 打开穿透的列表
     * @param {String} [through] 穿透的参数
     * @return:
     */
    pierceThrough(through) {
      this.penetrateThrough = through
      through && (this.showPierceBox = true)
    },

    // 编辑工作报告
    editWorkReport(dataId) {
      // TODO: 不太理解老逻辑是想更新还是想覆盖，按照老代码的行为，实际是在走更新逻辑，这里先用update处理
      const appInfo = {
        formId: this.formMsg.formId,
        subBusinessType: this.formMsg.businessType
      }
      this['proList/updateAppInfo'](appInfo).then(() => {
        this.editItem(dataId)
      })
    },
    isStatisticContentEmpty(object) {
      for (const attr in object) {
        return attr
      }
    },
    formatIntStatisticContent(value) {
      if (value !== 'undefined' && !isNaN(value) && value) {
        return parseInt(value)
      }
      return '0'
    },
    formatStatisticContent(value) {
      if (value !== 'undefined' && !isNaN(value) && value) {
        return parseFloat(value)
      }
      return '0.00'
    },
    // 展示点赞者头像
    showLike(num) {
      if (num) {
        this.likeBox = true
      }
    },
    // 隐藏点赞者头像
    hideLike(num) {
      if (num) {
        this.likeBox = false
      }
    },
    // 点击评论
    handleReport() {
      if (this.isShowComment) {
        this.isShowComment = false
        // 关闭评论的时候没必要刷新详情，评论个数直接改变即可
        // this.getReportDetail(this.reportDetail.id)
      } else {
        this.isShowComment = true
      }
    },
    /**
     * @description: 打印前的数据处理操作
     * @param {type}
     * @return:
     */
    beforePrint(data) {
      utils.isBindWXApp({
        onSuccess: (_) => {
          const { businessType } = this.getAppInfo
          const btnOption = {
            ...this.formMsg,
            saasMark: 1,
            businessType
          }
          console.log(this.$t('CRM.whatsThis'), data)
          const dataIdList = [data.id]
          this.handelBatchPrint({ dataIdList, btnOption })
        },
        store: this.$store
      })
    },
    // 格式化日期（9月22日）
    formatDataCh(time) {
      let arr = []
      arr = time.split('-')
      return arr[1] + '月' + arr[2] + '日'
    },
    // 点赞
    handleLike() {
      const { appId, businessType } = this.getAppInfo
      const params = {
        dataId: this.reportDetail.id,
        appId,
        businessType
      }
      setLikeAdd(params)
        .then((data) => {
          this.reportDetail.likeCount += 1
        })
        .catch(() => {})
    },
    loadMore() {
      if (this.pageHelper.pageTotal > this.pageHelper.currentPage) {
        this.showMore = true
        this.pageHelper.currentPage++
        this.getFormList()
      } else {
        this.showMore = false
      }
    },
    getReportDetail(id, report) {
      this.isMyReport = report.isMyReport
      const { formId, appId, menuId, saasMark } = this.catchAppInfo
      // if (this.loading) {
      //   return
      // }
      this.detailLoading = true
      this.curIndex = id
      this.isTodayInfo = false
      this.isLackReport = false
      this.isShowComment = false
      this.isReportDetail = true
      const params = {
        dataId: id,
        formId,
        appId,
        menuId,
        saasMark
      }
      getWorkReportDetail(params)
        .then((data) => {
          this.getTabsDetail(data, id)
          if (report) {
            report.isReview = true
            report.reviewerNum = data.result.head.reviewerArray.length
          }
          if (data.result.formMsg) {
            this.formMsg = data.result.formMsg
          }
          // 针对标题中性名后面带有(离职)的做特殊处理
          data.result.head['onlyUserName'] = data.result.head.userName
          const onlyUserName = data.result.head['onlyUserName']
          if (onlyUserName.indexOf('离职') !== -1 || onlyUserName.indexOf('Quit') !== -1) {
            const index = onlyUserName.indexOf('离职') || onlyUserName.indexOf('Quit')
            data.result.head['onlyUserName'] = onlyUserName.substring(0, index - 1)
          }
          this.reportDetail = data.result.head
          const detailHead = data.result.head
          this.statisticContent = data.result.statisticContent
          this.statisticResultList = data.result.statisticResultList
          // this.redundantJson = this.reportDetail.redundantArray
          this.reviewerArray = this.reportDetail.reviewerArray || []
          this.unReadArray = this.reportDetail.unReadArray || []
          if (parseInt(detailHead.type) === 1) {
            this.thisPlanName = this.$t('CRM.todayTarget')
            this.nextPlanName = this.$t('CRM.tomorrowTarget')
            this.addData = this.$t('CRM.todayAdd')
          } else if (parseInt(detailHead.type) === 2) {
            this.thisPlanName = this.$t('CRM.weekTarget')
            this.nextPlanName = this.$t('CRM.nextWeekTarget')
            this.addData = this.$t('CRM.weekAdd')
          } else {
            this.thisPlanName = this.$t('CRM.monthTarget')
            this.nextPlanName = this.$t('CRM.nextMonthTarget')
            this.addData = this.$t('CRM.monthAdd')
          }
        })
        .catch(() => {})
        .finally(() => {
          setTimeout(() => {
            this.detailLoading = false
          }, 200)
        })
    },
    @xbb.debounceWrap()
    sendMessageInform() {
      const { formId, appId, menuId, saasMark } = this.catchAppInfo
      const params = {
        dataId: this.curIndex,
        formId,
        appId,
        menuId,
        saasMark
      }
      sentWorkReportRemind(params)
        .then(() => {
          this.$message.success(this.$t('decisionTree.successMessage'))
        })
        .catch(() => {
          this.$message.warning(this.$t('decisionTree.failMessage'))
        })
    },
    // 获取tab的基本信息
    getTabsDetail(data, id) {
      const { formId, menuId } = data.result.formMsg
      const { appId, saasMark, businessType } = this.catchAppInfo
      const params = {
        saasMark,
        formId,
        appId,
        menuId,
        businessType,
        subBusinessType: data.result.formMsg.businessType, // 这个专属日报周报的枚举，需要从详情接口里拿
        dataId: id
      }
      this.detailTabs = []
      getFormDataDetail(params)
        .then((data) => {
          this.detailTabs = data.result.tab
          this.detailTabs.length && (this.tabQuery = params)
          this.hasOuterLinkShare = data.result.head.hasOuterLinkShare // 外链分享在详情中的分享按钮显示
          this.hasOuterLinkShare && this.getOuterLinkUrl()
        })
        .catch(() => {})
    },
    // 获取操作权限
    getVisibleRange() {
      const params = {}
      getWorkReportVisibleRange(params)
        .then((data) => {
          this.delPermission = data.result.delPermission
          this.editPermission = data.result.editPermission
          this.printPermission = data.result.printPermission
          this.rolePermission = data.result.rolePermission
        })
        .catch(() => {})
    },
    //提前获取外链分享的url
    getOuterLinkUrl() {
      // 调用接口获取链接地址
      getOuterLinkShareUrl({
        formId: this.tabQuery.formId,
        businessType: this.tabQuery.businessType,
        dataId: this.tabQuery.dataId,
        saasMark: this.catchAppInfo.saasMark
      }).then((res) => {
        this.outerLinkShareUrl =
          process.env.NODE_ENV === 'development'
            ? res.result.url.replace('localhost:8017', window.location.host)
            : res.result.url
      })
    },
    // 外链分享-分享
    @xbb.debounceWrap()
    getOuterLinkShare() {
      copyLink(this.outerLinkShareUrl)
    }
  },

  watch: {
    sendReportType(val) {
      switch (val) {
        case 75: // 我发送的
          this.showWatchNum = true
          break
        case 76: // 发送给我的
          this.showWatchNum = false
          break
        case 77: // 抄送给我的
          this.showWatchNum = false
          break
        default:
          // 全部
          this.showWatchNum = true
          break
      }
      // if (this.sendReportType === 0) {
      //   this.isMyReport = true
      // } else {
      //   this.isMyReport = false
      // }
      // this.isLackReport = false
      // this.isTodayInfo = false
      // this.isReportDetail = true
    }
  }
}
</script>

<style lang="scss" scoped>
.work-report {
  .disabled {
    color: $text-grey;
    background-color: $base-white;
    border: 1px solid $neutral-color-3;
  }
  .selectPlay {
    position: relative;
    .manager {
      display: inline-block;
      width: 160px;
      margin: 0;
      vertical-align: top;
      .el-input {
        .el-input__inner {
          font-size: 15px;
          font-weight: bold;
          border-width: 0;
        }
      }
    }
  }
  .commun-panel {
    display: flex;
    align-items: center;
    height: 60px;
    .scope {
      position: relative;
      display: inline-block;
      // width: 320px;
      padding-left: 72px;
      margin-left: 15px;
      .label {
        position: absolute;
        top: 6px;
        left: 0;
      }
      .el-input {
        margin-left: 8px;
      }
      .el-select {
        min-width: 86px;
      }
      .dropbox-wrapper {
        position: absolute;
        top: 32px;
        left: 78px;
        z-index: 8;
        width: 260px;
        height: 0;
      }
      .el-icon-close {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 12px;
        // color: @primary;
        cursor: pointer;
      }
    }
    .state {
      position: relative;
      margin-left: 30px;
      .el-input__inner {
        width: 128px;
        line-height: 24px; //datepicker格式不正确这里影响 zpp
      }
      .label {
        display: inline-block;
        margin-right: 6px;
        font-size: 13px;
        line-height: 30px;
        vertical-align: top;
      }
      .label + div {
        display: inline-block;
        width: 130px;
        vertical-align: top;
        .el-range-input {
          top: 0;
          width: 33%;
        }
      }
      .el-select {
        .el-select__tags {
          overflow: hidden;
          .el-tag {
            height: 22px;
            margin: 1px 0 1px 6px;
            line-height: 22px;
          }
        }
        .el-select__tags > span {
          white-space: nowrap;
        }
      }
    }
    .noRead {
      position: absolute;
      top: 15px;
      right: 76px;
      width: 60px;
      height: 30px;
      font-size: 13px;
      line-height: 30px;
      color: $base-white;
      text-align: center;
      background: $brand-color-5;
      border-radius: 2px;
    }
  }
}
</style>

<style lang="scss" scoped>
.work-report {
  height: 100%;
  .content {
    display: flex;
    flex: auto;
    flex-direction: column;
    width: 100%;
    &-head {
      box-shadow: 0 0 6px 0 hsla(0, 0%, 80%, 0.5);
    }
  }
  .penetrate {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 60px;
    z-index: 99;
    // width: 100%;
    // height: 100%;
  }
}
.work-report {
  .com-left {
    overflow: auto !important;
  }
  .noPermission {
    position: absolute;
    top: 70px;
    right: 10px;
    bottom: 10px;
    left: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: $base-white;
  }
  .message {
    word-break: break-all;
  }
}
.headline {
  margin-top: 20px;
  font-size: 13px;
  color: $text-plain;
  .first {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    .reportName {
      display: inline-block;
      margin-right: 15px;
      font-size: 20px;
      color: $text-main;
    }
    .tag {
      display: inline-block;
      width: 32px;
      height: 18px;
      font-size: 12px;
      line-height: 18px;
      color: $base-white;
      text-align: center;
      border-radius: 2px;
    }
    .outer-link-share {
      margin-left: 10px;
      font-size: 18px;
      color: #f88e3e;
      cursor: pointer;
    }
  }
  .two {
    display: flex;
    justify-content: space-between;
    .two-user {
      display: flex;
      overflow: hidden;
      white-space: nowrap;
    }
    .last-child {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .two,
  .three,
  .second {
    margin-bottom: 10px;
    > span {
      display: inline-block;
      flex: 0 0 auto;
      padding: 0 25px;
    }
    .two-first {
      padding-right: 25px;
      padding-left: 0;
      line-height: 16px;
    }
    .first-child {
      padding-left: 0;
    }
    .last-child {
      display: inline-block;
      padding-left: 25px;
      line-height: 16px;
      border-left: 1px solid $text-plain;
    }
  }
}
.reportPeople {
  padding-top: 30px;
  .box .tag {
    display: inline-block;
    width: 78px;
    height: 32px;
    font-size: 14px;
    line-height: 32px;
    color: $base-white;
    text-align: center;
    border-radius: 2px;
  }
  .nofinish {
    margin-right: 20px;
    background: $brand-color-5;
  }
  .finish {
    background: #78c06e;
  }
  .imgItem {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    .staff {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 24px 24px 0px 0;
      .staffImg {
        width: 60px;
        height: 60px;
        margin-bottom: 10px;
        border-radius: 100%;
      }
      .staffName {
        font-size: 14px;
        color: $text-plain;
      }
    }
  }
}
.work-report {
  .dayReport {
    background: $link-color-4;
  }
  .monthReport {
    background: $brand-color-4;
  }
  .weekReport {
    background: $blue-color-4;
  }
  .bufaReport {
    background: $error-color-4;
  }
  .noReport {
    position: absolute;
    top: 70px;
    right: 0px;
    bottom: 10px;
    left: 50px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: $base-white;
    .text {
      margin-top: 15px;
    }
  }
  .content-head {
  }
  .commun-wrap {
    display: flex;
    flex: 1;
    min-height: 0;
    background-color: $base-lineGray;
    .com-left {
      width: 520px;
      min-width: 400px;
      padding: 10px 20px;
      overflow: hidden;
      .left-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        padding: 0 20px;
        margin-bottom: 10px;
        cursor: pointer;
        background: $base-white;
        .right {
          display: flex;
          align-items: center;
          .tag {
            display: inline-block;
            width: 72px;
            height: 24px;
            line-height: 24px;
            color: $base-white;
            text-align: center;
            background: $brand-color-5;
            border-radius: 2px;
          }
          .list {
            display: flex;
            flex-direction: row;
            align-items: center;
            height: 100%;
            padding-left: 0;
            background: 0;
            li {
              padding: 0 16px;
              border-right: 1px solid $text-plain;
            }
            li:last-child {
              color: red;
              border-right: none;
            }
          }
        }
      }
      .left-report {
        .dayItem {
          .item {
            display: flex;
            padding: 0 20px 0 15px;
            margin-bottom: 15px;
            background: $base-white;
          }
          .time {
            align-items: center;
            justify-content: space-between;
            height: 42px;
            background: $base-lineGray;
            .detail-time {
              font-size: 16px;
              color: $text-main;
            }
            .el-icon-caret-bottom,
            .el-icon-caret-top {
              font-size: 12px;
              color: $text-auxiliary;
            }
          }
          .active {
            border-left: 4px solid $brand-color-5;
            box-shadow: 0 0 8px 0 $bg-blue;
          }
          .report {
            position: relative;
            box-sizing: border-box;
            height: 80px;
            padding: 14px 0 14px 0;
            cursor: pointer;
            border-radius: 4px;
            .bufa {
              position: absolute;
              right: 0px;
              bottom: 0px;
              display: block;
              font-size: 13px;
              border: 17px solid #fe8282;
              border-top-color: transparent;
              border-left-color: transparent;
              border-radius: 2px;
              .fon {
                position: absolute;
                color: $base-white;
              }
            }
            .report-left {
              display: flex;
              flex: 0 0 auto;
              align-items: center;
              justify-content: center;
              width: 75px;
              border-right: 1px dotted $neutral-color-3;
              .tag {
                display: inline-block;
                width: 44px;
                height: 24px;
                font-size: 12px;
                line-height: 24px;
                color: $base-white;
                text-align: center;
                border-radius: 2px;
              }
            }
            .report-right {
              display: flex;
              flex-direction: column;
              justify-content: center;
              width: 100%;
              padding: 0 20px 0 14px;
              overflow: hidden;
              .report-first {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                margin-bottom: 15px;
                overflow: hidden;
                // .report-info {
                //   flex: auto;
                //   display: flex;
                //   flex-wrap: nowrap;
                .name {
                  flex: 0 1 auto;
                  font-size: 15px;
                  line-height: 18px;
                  color: $text-plain;
                  // display: inline-block;
                  // max-width: 110px;
                  @include singleline-ellipsis;
                }
                .report-time {
                  flex: 1 0 auto;
                  padding-right: 10px;
                  margin-left: 12px;
                  // display: inline-block;
                  font-size: 14px;
                  line-height: 18px;
                  color: $text-auxiliary;
                }
                // }
                .Read {
                  flex: none;
                  font-size: 13px;
                  line-height: 18px;
                  color: $text-grey;
                  @include singleline-ellipsis;
                }
                .noRead {
                  flex: none;
                  font-size: 13px;
                  line-height: 18px;
                  color: $error-base-color-6;
                  @include singleline-ellipsis;
                }
              }
              .report-msg {
                overflow: hidden;
                font-size: 13px;
                line-height: 18px;
                color: $text-plain;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
        .center {
          text-align: center;
        }
      }
    }
    .com-right {
      box-sizing: border-box;
      flex: 1 1 auto;
      width: 980px;
      padding: 0 20px 20px 20px;
      overflow: auto;
      background-color: $base-white;
      .item-options {
        position: relative;
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        margin-bottom: 30px;
        .reviewer {
          font-size: 13px;
          .num {
            font-style: normal;
          }
        }
        .read-status {
          margin-left: 10px;
          color: $text-plain;
        }
        .like {
          padding-right: 12px;
          margin-right: 12px;
          border-right: 1px solid $neutral-color-4;
        }
        .option {
          position: relative;
          display: inline-block;
          padding-right: 12px;
          margin-right: 12px;
          font-size: 13px;
          line-height: 1;
          color: $text-plain;
          border-right: 1px solid $neutral-color-4;
          &:last-child {
            padding-right: 0;
            margin-right: 0;
            border-right-width: 0;
          }
          &:before {
            position: absolute;
            top: 23px;
            left: 28px;
            z-index: 2;
            display: none;
            width: 10px;
            height: 10px;
            content: '';
            background-color: $base-white;
            box-shadow: 0 0 1px 1px #cdd9eb inset;
            transform: rotate(45deg);
          }
          &:after {
            position: absolute;
            top: 28px;
            left: 27px;
            z-index: 3;
            display: none;
            width: 12px;
            height: 7px;
            content: '';
            background-color: $base-white;
          }
          &.active {
            &:before {
              display: block;
            }
            &:after {
              display: block;
            }
          }
          .icon {
            vertical-align: middle;
          }
          .text {
            display: inline-block;
            margin-left: 2px;
            vertical-align: middle;
            .num {
              font-style: normal;
            }
          }
        }
      }
      .lackRecord {
        .title {
          display: inline-block;
          margin: 0 56px 25px 0;
          font-size: 20px;
          color: $text-main;
        }
      }
      .record {
        .detail {
          margin-bottom: 20px;
          font-size: 13px;
          color: $text-auxiliary;
          button {
            display: inline-block;
            margin-left: 37px;
          }
        }
      }
      .newAdd {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        padding: 10px;
        margin-top: 20px;
        margin-bottom: 18px;
        border: 1px solid $bg-primary;
        .left-tag {
          box-sizing: border-box;
          display: flex;
          flex: 1 1 auto;
          align-items: center;
          justify-content: center;
          width: 54px;
          height: 54px;
          padding: 12px;
          margin-right: 10px;
          font-size: 13px;
          color: $base-white;
        }
        .right-tag {
          display: flex;
          width: 100%;
          overflow-x: auto;
          overflow-y: hidden;
          .right-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 110px;
            padding: 5px 10px;
            font-size: 13px;
            color: $text-auxiliary;
            cursor: pointer;
            .number {
              display: inline-block;
              margin-bottom: 14px;
              font-size: 16px;
              color: $link-base-color-6;
            }
          }
        }
      }
      .message {
        min-height: 140px;
        padding: 10px 25px;
        margin-bottom: 18px;
        background: $bg-li;
        .textContent {
          margin-bottom: 20px;
          font-size: 13px;
          font-weight: 300px;
          line-height: 22px;
          color: $text-plain;
          text-align: justify;
        }
        .img {
          display: inline-block;
          width: 68px;
          height: 68px;
          margin-right: 24px;
        }
        .content {
          .item {
            display: table;
            padding-top: 6px;
            .label,
            .text {
              font-size: 13px;
              line-height: 20px;
            }
            .label {
              display: inline-block;
              padding-right: 10px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              // color: @light-gray;
              &.rate {
                text-align: left;
              }
            }
            .text {
              display: table-cell;
              word-break: break-all;
              vertical-align: top;
            }
            .tag {
              display: inline-block;
              width: 20px;
              height: 20px;
              margin-left: 20px;
              font-size: 12px;
              line-height: 20px;
              color: $base-white;
              text-align: center;
              border-radius: 2px;
              transform: scale(0.8);
              &.high {
                background-color: $error-base-color-6;
              }
              &.mid {
                background-color: $brand-color-5;
              }
              &.low {
                background-color: #78c06e;
              }
            }
            .el-rate {
              display: inline-block;
              padding-top: 2px;
              vertical-align: top;
              .el-rate__icon {
                font-size: 16px;
              }
            }
            .img-box {
              display: table-cell;
              word-break: break-all;
              vertical-align: top;
              .img {
                margin-right: 6px;
              }
            }
            .phone-arr {
              display: table-cell;
              word-break: break-all;
              vertical-align: top;
              .text {
                display: block;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<style>
.item .img-box .file-content .file-info {
  padding: 0;
  margin: 0;
  border: none;
}
.item .img-box .file-content .file-info .label {
  display: none;
}
.item .img-box .file-content .file-info .file-name {
  min-width: 240px;
}
</style>
