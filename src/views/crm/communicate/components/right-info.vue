<!--
 * @Description: 跟进记录右侧详情
 -->
<template>
  <div class="rightInfo">
    <template v-for="(item, index) in rightInfoData">
      <div v-if="item.data.length" :key="index">
        <div v-if="item.fieldType !== 8" class="label">{{ item.attrName }}：</div>
        <!-- 可点击进入详情 -->
        <template v-if="item.fieldType === 20001">
          <p v-for="con in item.data" :key="con.id" class="text">
            <span v-if="con.noView === 1">
              {{ con.contactName }}
              <span v-show="con.position">
                {{ '  -  ' + con.position }}
              </span>
            </span>
            <a v-else @click.prevent="openDetail(con.contactId, 'contactApi')"
              >{{ con.contactName
              }}<span v-show="con.position">{{ '  -  ' + con.position }}</span></a
            >
          </p>
        </template>
        <template v-else-if="!item.popType && [5, 6, 8].indexOf(item.fieldType) === -1">
          <p class="text">{{ item.value4Show }}</p>
        </template>
        <template v-else-if="item.popType === 1">
          <p class="text">
            <span v-if="item.communicateType === 'opportunity'" style="color: #323233"
              >{{ $t('business.opportunity') + ':' }}
            </span>
            <span v-if="item.communicateType === 'contract'" style="color: #323233"
              >{{ $t('business.contract') + ':' }}
            </span>
            <span v-if="item.noView === 1">{{ item.value4Show }}</span>
            <a v-else @click.prevent="openDetail(item.id, item.popModel)">{{ item.value4Show }}</a>
          </p>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'

export default {
  components: {},
  props: {
    rightInfoData: {
      required: true
    }
  },

  methods: {
    openDetail(contactId, moduleName) {
      this.$emit('openDetail', contactId, moduleName)
    },
    strToArr(str) {
      return xbb.json(str)
    },
    openLightbox(url) {
      // lightBox(url)
    }
  }
}
</script>

<style lang="scss">
.rightInfo {
  .title {
    padding-top: 20px;
    font-size: 14px;
    font-weight: bold;
  }
  .label {
    margin-top: 20px;
    margin-bottom: 6px;
    font-size: 13px;
    color: $text-auxiliary;
  }
  .text {
    overflow: hidden;
    font-size: 14px;
    line-height: 1.6;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
