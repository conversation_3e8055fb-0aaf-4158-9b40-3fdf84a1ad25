<!--
 * @Description: 退货退款列表页
 -->
<template>
  <div class="list returns-and-refunds">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          ref="listPanel"
          :custom-mode-config="{ viewConfig, viewButton }"
          :gantt-head-list="ganttViewInfo.ganttHeadList"
          :show-filter-list="specialFilter"
          :stage-head-list="stageViewInfo.stageHeadList"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @changeGroupLaneVersion="changeGroupLaneVersion"
          @getFormData="panelFilterParams"
          @getLaneVersionData="getLaneVersionData"
          @getShowSwitchPhase="getShowSwitchPhase"
          @onViewChange="onViewChange"
        ></list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams,
            ...stageProps
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onStageView="onStageView"
          @onTableView="onTableView"
          @oncalendarView="oncalendarView"
        ></component>
      </div>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'

export default {
  name: 'ReturnsAndRefunds',

  mixins: [listMixin, listEditMixin],

  data() {
    return {
      tableInfo: {}
    }
  },

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.initViewType({ params, api: this.formListApi.getReturnsAndRefundsList })
    }
  }
}
</script>

<style lang="scss" scoped>
.returns-and-refunds {
  height: 100%;
}
</style>
