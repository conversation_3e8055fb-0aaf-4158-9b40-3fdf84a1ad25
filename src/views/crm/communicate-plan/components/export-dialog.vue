<!--
 * @Description: 导出的弹窗选择
 -->
<template>
  <div class="export-dialog">
    <el-dialog
      :append-to-body="true"
      :before-close="beforeClose"
      :title="$t('CRM.exportPlan')"
      :visible.sync="dialogVisible"
    >
      <el-form
        ref="form"
        label-width="100px"
        :model="exportForm"
        :rules="rules"
        @submit.native.prevent
      >
        <el-form-item :label="$t('CRM.runDay')" prop="date">
          <el-date-picker
            v-model="exportForm.date"
            align="right"
            :end-placeholder="$t('label.endTime')"
            :picker-options="pickerOptions"
            :range-separator="$t('unit.to')"
            :start-placeholder="$t('label.startTime')"
            type="daterange"
            unlink-panels
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="beforeClose">{{ $t('operation.cancel') }} </el-button>
        <el-button type="primary" @click="submitFrom">{{ $t('operation.confirm') }} </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'

export default {
  name: 'ExportDialog',

  props: {
    dialogFormVisible: {
      // 弹窗的关闭隐藏
      type: Boolean
    },
    isCustomExport: {
      // 是否为自定义导出
      type: Boolean
    }
  },

  data() {
    return {
      pickerOptions: {
        shortcuts: utils.pickerOptionsShortcuts()
      },
      exportForm: {
        // 导出的查询参数
        date: ''
      },
      rules: {
        // 表单校验规则
        date: [{ required: true, message: this.$t('CRM.chooseExportRange'), trigger: 'blur' }]
      }
      // dialogVisible: false
    }
  },

  computed: {
    dialogVisible: {
      get: function () {
        return this.dialogFormVisible
      },
      set: function (val) {
        this.$emit('update:dialogFormVisible', val)
      }
    },
    isCustomExportDialog: {
      get: function () {
        return this.isCustomExport
      },
      set: function (val) {
        this.$emit('update:isCustomExport', val)
      }
    }
  },

  watch: {
    // dialogVisible () {
    //   debugger
    // },
    // dialogFormVisible (val) {
    //   this.dialogVisible = val
    // }
  },

  methods: {
    /**
     * @description: 弹窗关闭的处理
     * @param {type}
     * @return:
     */
    beforeClose() {
      this.$refs['form'].clearValidate()
      this.exportForm.date = ''
      this.dialogVisible = false
      this.isCustomExportDialog = false
    },

    /**
     * @description: 导出确认
     * @param {type}
     * @return:
     */
    submitFrom() {
      // 这里发现后面的validate方法会重置掉date数据，导致每次选了，发送的都是重置后的数组空
      const catchExportForm = xbb.deepClone(this.exportForm)
      if (this.exportForm.date) {
        this.exportForm.date.every((item) => {
          return item
        }) || (this.exportForm.date = '')
      }
      this.$refs['form'].validate((valid, obj) => {
        if (valid) {
          this.$emit('submit', catchExportForm)
          this.beforeClose()
        } else {
          return false
        }
      })
      // debugger
    }
  }
}
</script>

<style lang="scss" scoped></style>
