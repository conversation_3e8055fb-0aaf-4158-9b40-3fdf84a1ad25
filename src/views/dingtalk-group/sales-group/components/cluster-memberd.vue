<!--
 * @Description: 群成员
 -->
<template>
  <div class="clusterMemberd">
    <div class="sale-team-detail__body">
      <div class="main-user__block user__block">
        <div class="user--items__block">
          <div v-for="item in memberdList" :key="'main' + item.id" class="user--item">
            <div class="team__img">
              <avatar-img :alt="item.name" :size="30" :src="item.avatar" />
              <template v-if="!!item.identity">
                <div
                  v-show="memberBadge"
                  class="team--delete__badge"
                  @click="setBundleUserDelete(item)"
                >
                  <i class="el-icon-close"></i>
                </div>
              </template>
            </div>
            <el-tooltip
              class="item"
              :content="item.name"
              :disabled="isNameShow(item.name)"
              effect="dark"
              placement="bottom"
            >
              <div class="user--name" v-html="addNodeByWX(item.userName)"></div>
            </el-tooltip>
          </div>
          <!-- 添加 删除 -->
          <div class="team-btn">
            <el-button
              v-if="addMemberPermission"
              circle
              icon="el-icon-plus"
              plain
              type="primary"
              @click="openPerson"
            >
            </el-button>
            <el-button
              v-if="removeMemberPermission"
              circle
              icon="el-icon-minus"
              :plain="!memberBadge"
              type="danger"
              @click="memberBadge = !memberBadge"
            >
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import avatarImg from '@/components/base/avatar-img'
import { getBundleUserList, setBundleUserDelete } from '@/api/dingtalk-group'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'SaleBundleMember',
  components: {
    AvatarImg: avatarImg
  },

  props: {
    dataId: {
      type: Number
    }
  },

  data() {
    return {
      memberdList: [],
      memberBadge: false,
      addMemberPermission: false,
      removeMemberPermission: false
    }
  },
  mounted() {
    this.getBundleUserList()
  },
  methods: {
    // 获取群成员
    getBundleUserList() {
      const params = {
        dataId: this.dataId
      }
      getBundleUserList(params)
        .then(({ result }) => {
          this.memberdList = result.list
          this.addMemberPermission = result.addMemberPermission
          this.removeMemberPermission = result.removeMemberPermission
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    },
    // 打开人员选择
    openPerson() {
      if (this.memberBadge) {
        this.memberBadge = false
      }
      this.$emit('changePersonDialog', true)
    },
    // 删除成员
    setBundleUserDelete({ userId }) {
      const params = {
        dataId: this.dataId,
        deleteMembers: [userId]
      }
      setBundleUserDelete(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg || this.$t('message.operateSuccessSymbol')
          })
          this.memberBadge = false
          this.getBundleUserList()
        })
        .catch(() => {})
    },
    @xbb.debounceWrap(800)
    addSubmitOnce(arr) {
      this.addSubmit(arr)
    },
    // 判断用户名称是否超出四个字符
    isNameShow(name) {
      return name.length <= 4
    }
  }
}
</script>

<style lang="scss" scoped>
.clusterMemberd {
  height: 100%;
  padding-top: 15px;
  overflow: auto;
  .sale-team-detail__body {
    box-sizing: border-box;
    .main-user__block {
      margin-bottom: 5px;
    }
    .user__block {
      padding: 20px;
      padding-top: 0;
      .team-title {
        margin-bottom: 25px;
      }
      .team__img {
        // overflow: hidden;
        position: relative;
        display: inline-block;
        width: 30px;
        height: 30px;
        margin-bottom: 5px;
        & > img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
        & > .team--delete__badge {
          position: absolute;
          top: -5px;
          right: -5px;
          width: 15px;
          height: 15px;
          font-size: 12px;
          line-height: 14px;
          color: $base-white;
          text-align: center;
          cursor: pointer;
          background: $error-base-color-6;
          border-radius: 50%;
        }
      }
      .team-btn {
        display: inline-block;
      }
      .item--badge {
        display: flex;
        align-items: center;
      }
      .user--items__block {
        // @include clearfix;
        .user--item {
          // height: 32px;
          // width: 100px;
          // display: flex;
          // align-items: center;
          // margin-bottom: 20px;
          // // background: skyblue;
          float: left;
          margin-right: 10px;
          margin-bottom: 10px;
          text-align: center;
          .user--name {
            max-width: 57px;
            overflow: hidden;
            // width:100%;
            line-height: 16px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
