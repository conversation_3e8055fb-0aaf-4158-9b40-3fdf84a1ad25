<!--
 * @Description: 待抢单列表
 -->
<template>
  <div class="list wait-grab-work-order">
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :back-btn-show="true"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
        <!-- <span slot="back" class="header-back" @click="$router.go(-1)"> 返回 </span> -->
        <!-- <span slot="back" slot-scope="scope">{{scope.title}}/</span> -->
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :custom-mode-config="{ viewConfig, viewButton }"
          :form-id="formId"
          :show-filter-list="specialFilter"
          :table-head-list="tableHeadParSubFieldList"
          :view-id.sync="currViewId"
          :view-info.sync="currentViewConfig"
          @getFormData="panelFilterParams"
        >
        </list-panel>
        <!-- 视图 -->
        <component
          v-bind="{
            loading,
            uiPaasParams,
            bottomButton,
            formListParams,
            specialFilterParams,
            commonFilter,
            catchAppInfo
          }"
          :is="viewTypeComponentName"
          v-if="viewTypeComponentName"
          :ref="viewTypeComponentName"
          :app-info="getAppInfo"
          :top-permissions="topPermissions"
          @onGanttView="onGanttView"
          @onTableView="onTableView"
          @oncalendarView="oncalendarView"
        >
        </component>
      </div>
      <template slot="layout-dialog"> </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/view.list-mixin.js'
import listEditMixin from '@/mixin/view.list-edit-mixin.js'
import { getWaitGrabWorkOrderList } from '@/api/work-order-filter-list.js'

export default {
  name: 'WaitGrabWorkOrder',

  mixins: [listMixin, listEditMixin],

  data() {
    return {
      specialFilter: {
        // 特殊筛选显示的内容
        // statusFilter: {
        //   options: ['全部', '已归档', '未归档']
        // }
      }
    }
  },

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.initViewType({ params, api: getWaitGrabWorkOrderList })
    }
  }
}
</script>

<style lang="scss" scoped>
.wait-grab-work-order {
  height: 100%;
}
</style>
