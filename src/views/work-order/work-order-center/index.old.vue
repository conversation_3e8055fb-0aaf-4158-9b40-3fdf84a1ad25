<!--
 * @Description: 工单中心列表
 -->
<template>
  <div class="work-order-center">
    <!-- <div class="notice">
      <div class="fixed-content">
        <div class="content-text">
          由于系统迭代更新，旧版工单将在合同到期后不再免费提供。我们将限时提供新版工单优惠政策，请联系我们的售后服务人员进行开通。
        </div>
        <el-button class="content-btn" @click="showCompare">查看功能对比</el-button>
      </div>
    </div> -->
    <v-top-bottom-page :template-empty="templateEmpty">
      <list-head
        ref="listHead"
        slot="header"
        :top-permissions="topPermissions"
        @searchEmit="commonSearch"
        @sendHeadFilter="getHeadFilter"
        @topBtnHandle="topBtnHandle"
      >
      </list-head>
      <div slot="layout-column" class="content">
        <list-panel
          :data-list="parSubFieldList"
          :show-filter-list="specialFilter"
          @getDataSummary="getTableSummary"
          @getFormData="panelFilterParams"
        >
        </list-panel>
        <list-table
          ref="table"
          :column-invisible-field="columnInvisibleField"
          :data-list="parSubFieldList"
          :loading="loading"
          :summary="summary"
          :table-data="tableData"
          :table-head="tableHead"
          @editClick="editTable"
          @editForm="editFormDispatch"
        ></list-table>
        <list-footer
          :archived="archived"
          :page-helper="pageHelper"
          :permission="batchEditButtons"
          @batch-handle="batchHandle"
          @getFormData="footerFilterParams"
        >
          <!-- 批量操作按钮 -->
          <template slot="permission" slot-scope="props">
            <el-button
              plain
              size="mini"
              :type="buttonType(props.type.attr)"
              @click="batchHandle(props.type)"
            >
              <div class="list-button-icon">
                <i :class="props.type.icon + ' iconfont'"></i>
                <span>{{ props.text }}</span>
              </div>
            </el-button>
          </template>
        </list-footer>
      </div>
      <template slot="layout-dialog">
        <!-- 成员选择的弹窗 -->
        <organization-select
          v-if="showOrganizationSelect"
          :active-tab-name="organizationSelectActiveName"
          :default-val.sync="selectPersonsDefault"
          :dialog-visible.sync="showOrganizationSelect"
          :multiple="tabOption"
          :range-params="rangeParams"
          :show-tabs-name="organizationSelectShowTab"
          @dialogSubmit="SubmitSelectPersons"
        >
        </organization-select>
      </template>
    </v-top-bottom-page>
  </div>
</template>

<script>
import listMixin from '@/mixin/list-mixin.js'
import listEditMixin from '@/mixin/list-edit-mixin.js'
import { getWorkOrderList } from '@/api/work-order-filter-list.js'
import { exportWorkOrderFormData } from '@/api/export.js'

export default {
  name: 'WorkOrderCenter',

  mixins: [listMixin, listEditMixin],

  data() {
    return {
      specialFilter: {
        // 特殊筛选显示的内容
        // statusFilter: {
        //   options: ['全部', '已归档', '未归档']
        // }
      }
    }
  },

  methods: {
    // 获取表单列表
    getSpecialFormList(params) {
      this.exportParams = params
      getWorkOrderList(params)
        .then((data) => {
          // 处理请求数据格式
          this.disposeFormListData(data)
        })
        .catch(() => {})
    },

    /**
     * @description: 通用列表的特殊导出接口
     * @param {type}
     * @return:
     */
    specialExport(ExportParams) {
      return {
        promise: exportWorkOrderFormData(ExportParams)
      }
    },

    /**
     * @description: 通用列表的特殊导出接口(自定义导出专用)
     * @param {type}
     * @return:
     */
    specialCustomExport(ExportParams) {
      return exportWorkOrderFormData(ExportParams)
    },

    // 跳转URL展示对比图片
    showCompare() {
      const routeData = this.$router.resolve({ path: '/work-order-compare' })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.work-order-center {
  height: 100%;
  .notice {
    position: relative;
    width: 100%;
    height: 56px;
    margin-top: -10px;
    margin-bottom: 10px;
    .fixed-content {
      position: fixed;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 56px;
      font-size: 16px;
      font-weight: normal;
      line-height: normal;
      text-align: center;
      background-color: $base-white;
    }
    .content-btn {
      width: 116px;
      height: 32px;
      padding: 0;
      margin-left: 24px;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      text-align: center;
    }
  }
}
</style>
