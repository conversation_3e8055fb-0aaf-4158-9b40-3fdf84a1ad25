<template>
  <SettingCard
    v-if="layoutConfig.layoutType === 2 && canShowJouneryConfigKey.includes('baseDetailTab')"
    :sub-title="$t('layoutDesign.onlyTopLayout')"
    :title="$t('layoutDesign.extendConfig')"
    title-bold
  >
    <el-checkbox
      v-model="showJourney"
      :disabled="isDefaultLayout"
      :false-label="0"
      :true-label="1"
      >{{ $t('layoutDesign.showJourney') }}</el-checkbox
    >
  </SettingCard>
  <Empty v-else :text="$t('message.noConfig')"></Empty>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import SettingCard from '../../setting-card.vue'
import Empty from '@/components/base/empty.vue'

const props = defineProps({
  componentConfig: {
    type: Object,
    default: () => {}
  },
  layoutConfig: {
    type: Object,
    default: () => {}
  },
  componentPositionInfo: {
    type: Object,
    default: () => {}
  },
  formQuery: {
    type: Object,
    default: () => {}
  },
  isDefaultLayout: {
    type: Boolean,
    default: false
  },
  canShowJouneryConfigKey: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:componentConfig'])

const showJourney = computed({
  set: (val) => {
    console.log(val)
    const obj = props.componentConfig
    obj.showJourney = val
    emit('update:componentConfig', obj)
  },
  get: () => {
    return props.componentConfig?.showJourney
  }
})

onMounted(() => {
  if (props.layoutConfig.layoutType === 1) {
    showJourney.value = 0
  }
})
</script>

<style lang="scss" scoped></style>
