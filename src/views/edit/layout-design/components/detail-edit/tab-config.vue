<template>
  <div class="tab-config">
    <ComponentTagInfo :tag="$t('layoutDesign.tabContainer')" />
    <SettingCard
      v-if="!isDefaultLayout"
      :title="$t('layoutDesign.tabShowAndSortConfig')"
      title-bold
    >
      <!--      <div class="tab-person-setting">-->
      <!--        <el-checkbox v-model="allowSetTabVisibleSort" :false-label="0" :true-label="1">-->
      <!--          <span>{{ $t('layoutDesign.allowTabShowFrontConfig') }}</span>-->
      <!--        </el-checkbox>-->
      <!--        <el-popover placement="top" trigger="hover" width="320">-->
      <!--          <div class="cover-tips">-->
      <!--            开启时，分组后显示分组设置按钮，支持个人自定义分组内容；未开启则隐藏按钮，不支持操作-->
      <!--            <img-->
      <!--              src="https://cloudcode-devstorage.oss-cn-hangzhou.aliyuncs.com/ding66041eb1c6df73f535c2f4657eb6378f/front/3B1F1892-AD7C-4F27-9C1E-2B43BDB9AB7B.png"-->
      <!--            />-->
      <!--          </div>-->
      <!--          <i slot="reference" class="icon-question-line t-iconfont"></i>-->
      <!--        </el-popover>-->
      <!--      </div>-->
      <div class="tab-person-cover-setting">
        <SettingCover v-model="coverConfig"> </SettingCover>
        <!--        <el-checkbox v-model="coverConfig" :false-label="0" :true-label="1">-->
        <!--          <span>覆盖前台个人设置</span>-->
        <!--        </el-checkbox>-->
      </div>
    </SettingCard>
    <SettingCard :title="$t('layoutDesign.tabShow')" title-bold>
      <div v-if="!isDefaultLayout" class="border-wrap">
        <SortableItemList
          ref="uiPassTabSort"
          :draggable="editTabNameIndex === -1 && !showNewTabInput"
          :draggable-option="{ sort: true }"
          :items.sync="tabList"
          mode="NonSelectedFields"
          node-key="key"
          :show-add-search="false"
          :show-limit="false"
          title-key="name"
          @sort="handleSortTab"
        >
          <template #itemName="{ item, index }">
            <el-input
              v-if="editTabNameIndex === index"
              v-model="editTabName"
              :maxlength="10"
              :placeholder="$t('placeholder.inputPls', { attr: '' })"
              size="small"
              @mousemove.stop.native
            ></el-input>
            <span>{{ item.name }}</span>
          </template>
          <template #operationGroup="{ item, index }">
            <template v-if="editTabNameIndex === index">
              <i class="icon-button icon-check-line t-iconfont" @click="handleNewTabChange"></i>
              <i class="icon-close-line t-iconfont tab-set-close-icon" @click="cancelAddTab"></i>
            </template>
            <template v-else>
              <i
                v-if="!isDefaultLayout"
                class="icon-button icon-edit-line t-iconfont"
                @click="handleEdit(item, index)"
              ></i>
              <el-popconfirm
                v-if="tabList.length > 1 && !isDefaultLayout"
                :title="$t('layoutDesign.deleteTabTips')"
                @onConfirm="handleRemove(item, index)"
              >
                <i slot="reference" class="icon-button icon-delete-bin-7-line t-iconfont"></i>
              </el-popconfirm>
            </template>
          </template>
        </SortableItemList>
        <div v-if="showNewTabInput" class="addTab">
          <el-input
            ref="addInput"
            v-model="newTabName"
            :maxlength="10"
            :placeholder="$t('placeholder.inputPls', { attr: '' })"
            size="small"
            @focus="editTabNameIndex = -1"
          ></el-input>
          <i class="icon-button icon-check-line t-iconfont" @click="handleNewTabChange"></i>
          <i class="icon-close-line t-iconfont tab-set-close-icon" @click="cancelAddTab"></i>
        </div>
      </div>
      <template v-else>
        <div class="border-wrap">
          <SortableItemList
            ref="noDragTabListForDefaultLayoutList"
            :draggable-option="{ sort: false }"
            :items="noDragTabListForDefaultLayout"
            mode="NonSelectedFields"
            node-key="key"
            :show-add-search="false"
            :show-limit="false"
            title-key="name"
            @sort="handleSortTab"
          >
            <template #operationGroup="{ item, index }">
              <i
                v-if="['panorama', 'activityEffect'].includes(item.attr)"
                class="icon-button t-iconfont"
                :class="item.enable === 0 ? 'icon-eye-off-line' : 'icon-eye-line'"
                @click="handleSwitchPanorama(item, index)"
              ></i>
            </template>
          </SortableItemList>
        </div>
        <SettingCard :title="$t('formDesign.associationList')" title-bold>
          <span id="detail_step_2" slot="titlePrefix"></span>
          <div v-if="canDragTabListForDefaultLayout.length !== 0" class="border-wrap">
            <SortableItemList
              ref="canDragTabListForDefaultLayoutList"
              draggable
              :draggable-option="{ sort: true }"
              :items.sync="canDragTabListForDefaultLayout"
              mode="NonSelectedFields"
              node-key="key"
              :show-add-search="false"
              :show-limit="false"
              title-key="name"
              @sort="handleSortDefaultLinkTab"
            >
              <template #operationGroup="{ item, index }">
                <i
                  class="icon-button icon-close-large-line t-iconfont"
                  @click="handleRemoveByDefaultLinkList(item, index)"
                ></i>
              </template>
            </SortableItemList>
          </div>
          <AddDataPopover
            v-if="isDefaultLayout"
            :btn-name="$t('operation.append') + $t('formDesign.associationList')"
            title-key="name"
            :visible.sync="defaultTabsAddPopVisible"
            @searchInput="handleDefaultAddSearchInput"
          >
            <template #addList>
              <div class="add-data-popover__content">
                <div v-if="defaultAssociationList.length === 0">
                  {{ $t('layoutDesign.uiPaasNoDisabledList') }}
                </div>
                <div v-for="item in defaultAssociationList" :key="item.atutr">
                  <div
                    v-if="!canDragTabListForDefaultLayout.find((n) => n.attr === item.attr)"
                    class="item-Field_item"
                  >
                    <el-checkbox v-model="item.enable" :false-label="0" :true-label="1">
                      <span class="item-list__item-title">{{ item.name }}</span>
                    </el-checkbox>
                  </div>
                </div>
              </div>
              <div class="add-data-popover__footer">
                <template v-if="defaultAssociationList.length !== 0">
                  <el-button size="mini" @click="restoreAssociationList">{{
                    $t('operation.cancel')
                  }}</el-button>
                  <el-button size="mini" type="primary" @click="addDefaultTab">{{
                    $t('operation.confirm')
                  }}</el-button>
                </template>
              </div>
            </template>
          </AddDataPopover>
        </SettingCard>
      </template>
      <el-button
        v-if="!isDefaultLayout"
        class="field_button"
        round
        size="medium"
        @click.stop="addNewTab"
      >
        + {{ $t('operation.append') + $t('layoutDesign.customerTabName') }}
      </el-button>
    </SettingCard>
  </div>
</template>

<script setup>
import ComponentTagInfo from './component-tag-info.vue'
import SettingCard from '../setting-card.vue'
import { ref, onMounted, computed, watch, getCurrentInstance, nextTick, set, toRaw } from 'vue'
import SortableItemList from '../item-list/sortable-item-list.vue'
import xbb from '@xbb/xbb-utils'
import AddDataPopover from '@/views/edit/layout-design/components/add-data-popover/add-data-popover.vue'
import SettingCover from '@/views/edit/layout-design/components/setting-cover.vue'

const vm = getCurrentInstance().proxy

const props = defineProps({
  layoutConfig: {
    type: Object,
    default: () => {}
  },
  container: {
    type: String,
    default: 'leftTabContainer'
  },
  activeHead: {
    type: String,
    default: 'pc'
  },
  isDefaultLayout: {
    type: Boolean,
    default: false
  },
  formQuery: {
    type: Object,
    default: () => {}
  },
  linkLists: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['removeComponent', 'addLinkButton'])

const noDragTabListForDefaultLayoutList = ref()
const canDragTabListForDefaultLayoutList = ref()
const tabList = ref([])

const noDragTabListForDefaultLayout = computed({
  get() {
    return tabList.value.filter(
      (n) => !n.tabKey || (n.tabKey !== 'linkList' && n.tabKey !== 'workTaskTab')
    )
  },
  set() {}
})

const canDragTabListForDefaultLayout = computed({
  get() {
    return tabList.value.filter((n) => n.tabKey === 'linkList' || n.tabKey === 'workTaskTab')
  },
  set() {}
})

const showNewTabInput = ref(false)
const newTabName = ref('')
const editTabName = ref('')
const editTabNameIndex = ref(-1)
const addInput = ref()

const updateTabList = () => {
  if (props.activeHead === 'pc') {
    set(props.layoutConfig.webConfig, props.container, tabList.value)
  } else {
    set(props.layoutConfig.mobileConfig, 'tabContainers', tabList.value)
  }
}

const initVal = () => {
  if (props.activeHead === 'pc') {
    if (props.layoutConfig.webConfig[props.container] === undefined) {
      set(props.layoutConfig.webConfig, props.container, [])
    }
    tabList.value = props.layoutConfig.webConfig[props.container].map((item) => {
      if (item.enable === undefined) {
        item.enable = 1
      }
      return xbb.deepClone(item)
    })
  } else {
    if (props.layoutConfig.mobileConfig.tabContainers === undefined) {
      set(props.layoutConfig.mobileConfig, 'tabContainers', [])
    }
    tabList.value = props.layoutConfig.mobileConfig.tabContainers
  }
}

const defaultTabsAddPopVisible = ref(false)
const defaultAssociationList = ref([])
const originDefaultAssociationList = ref([])

onMounted(() => {
  initVal()
  if (props.isDefaultLayout) {
    watch(
      () => props.linkLists,
      () => {
        originDefaultAssociationList.value = props.linkLists.filter((n) => n.enable === 0)
        defaultAssociationList.value = xbb.deepClone(originDefaultAssociationList.value)
      },
      {
        deep: true,
        immediate: true
      }
    )
  }
})

watch(
  () => props.container,
  (val) => {
    initVal()
  }
)

// const allowSetTabVisibleSort = computed({
//   get: () => {
//     if (props.container === 'leftTabContainer') {
//       return props.layoutConfig.allowSetTabVisibleSort
//     } else if (props.activeHead === 'mobile') {
//       return props.layoutConfig.tabVisibleSort
//     } else {
//       return props.layoutConfig.rightSetTabVisibleSort
//     }
//   },
//   set: (val) => {
//     set(
//       props.layoutConfig,
//       props.activeHead === 'mobile'
//         ? 'tabVisibleSort'
//         : props.container === 'leftTabContainer'
//         ? 'allowSetTabVisibleSort'
//         : 'rightSetTabVisibleSort',
//       val
//     )
//   }
// })

const handleSortTab = (e, item) => {
  if (props.activeHead === 'pc') {
    set(props.layoutConfig.webConfig, props.container, xbb.deepClone(item))
    emit('sortChange', props.container)
  } else {
    set(props.layoutConfig.mobileConfig, 'tabContainers', xbb.deepClone(item))
    emit('sortChange', 'tabContainers')
  }
}

const handleNewTabChange = () => {
  let arr
  if (props.activeHead === 'pc') {
    arr = xbb.deepClone(props.layoutConfig.webConfig[props.container])
  } else {
    arr = xbb.deepClone(props.layoutConfig.mobileConfig.tabContainers)
  }
  if (editTabNameIndex.value === -1) {
    const key = xbb.guid()
    arr.push({
      attr: key,
      key: key,
      list: [],
      name:
        newTabName.value && newTabName.value.trim().length !== 0
          ? newTabName.value.trim()
          : vm.$t('layoutDesign.unSetNameTab')
    })
    nextTick(() => {
      showNewTabInput.value = false
    })
  } else {
    arr[editTabNameIndex.value].name = editTabName.value || vm.$t('layoutDesign.unSetNameTab')
  }
  newTabName.value = ''
  editTabName.value = ''
  editTabNameIndex.value = -1
  tabList.value = arr
  if (props.activeHead === 'pc') {
    set(props.layoutConfig.webConfig, props.container, arr)
  } else {
    set(props.layoutConfig.mobileConfig, 'tabContainers', arr)
  }
  // eslint-disable-next-line no-use-before-define
  document.removeEventListener('click', inputClickOutSide)
  // eslint-disable-next-line no-use-before-define
  document.removeEventListener('keydown', enterCb)
}

const inputClickOutSide = (e) => {
  if (
    e.target.className !== 'el-input__inner' &&
    e.target.className !== 'icon-button icon-check-line t-iconfont' &&
    e.target.className !== 'icon-close-line t-iconfont tab-set-close-icon'
  ) {
    handleNewTabChange()
  }
}

const enterCb = (event) => {
  // 同时检查 key、keyCode 和 which 属性
  if (
    (event.key === 'Enter' ||
      event.code === 'Enter' ||
      event.keyCode === 13 ||
      event.which === 13) &&
    !event.isComposing
  ) {
    handleNewTabChange()
  }
}

const addNewTab = () => {
  showNewTabInput.value = true
  editTabNameIndex.value = -1
  nextTick(() => {
    addInput.value.focus()
    document.addEventListener('click', inputClickOutSide)
    document.addEventListener('keydown', enterCb)
  })
}

const handleEdit = (item, index) => {
  if (showNewTabInput.value) return
  editTabNameIndex.value = index
  editTabName.value = item.name
  nextTick(() => {
    document.addEventListener('click', inputClickOutSide)
    document.addEventListener('keydown', enterCb)
  })
}

const restoreAssociationList = () => {
  originDefaultAssociationList.value.forEach((item) => {
    item.enable = 0
  })
  defaultTabsAddPopVisible.value = false
}
const addDefaultTab = () => {
  const arr = []
  defaultAssociationList.value.forEach((item) => {
    if (item.enable === 1) {
      arr.push(xbb.deepClone(item))
      const index = props.linkLists.findIndex((n) => n.attr === item.attr)
      set(props.linkLists[index], 'enable', 1)
    }
  })
  tabList.value = [...tabList.value, ...arr]
  updateTabList()
  defaultTabsAddPopVisible.value = false
  // restoreAssociationList()
  nextTick(() => {
    canDragTabListForDefaultLayoutList.value.updateItems()
  })
  // web 端添加了的移动端也得同步添加
  set(props.layoutConfig.mobileConfig, 'tabContainers', [
    ...props.layoutConfig.mobileConfig.tabContainers,
    ...arr
  ])
  arr.forEach((item) => {
    emit('addLinkButton', item, 0)
  })
}
const handleRemove = (tab, index) => {
  tab.list.forEach((item) => {
    emit('removeComponent', item, tab.attr, props.container)
  })
  tabList.value.splice(index, 1)
  updateTabList()
}

const cancelAddTab = () => {
  // if (showNewTabInput.value) {
  //   // handleRemove(tabList.value[tabList.value.length - 1], tabList.value.length - 1)
  // } else {
  //   handleRemove(tabList.value[editTabNameIndex.value], editTabNameIndex.value)
  // }
  showNewTabInput.value = false
  editTabNameIndex.value = -1
  newTabName.value = ''
  // eslint-disable-next-line no-use-before-define
  document.removeEventListener('click', inputClickOutSide)
  // eslint-disable-next-line no-use-before-define
  document.removeEventListener('keydown', enterCb)
}

const handleRemoveByDefaultLinkList = (item, index) => {
  // debugger
  // defaultAssociationList.value[index].enable = 0
  const targetIndex = tabList.value.findIndex((n) => n.attr === item.attr)
  tabList.value.splice(targetIndex, 1)
  if (!defaultAssociationList.value.find((n) => n.attr === item.attr)) {
    defaultAssociationList.value.push({
      ...item,
      enable: 0
    })
  }
  const linkListIndex = props.linkLists.findIndex((n) => n.attr === item.attr)
  set(props.linkLists[linkListIndex], 'enable', 0)
  updateTabList()
  // 这边删除完了 还需要把移动端的也给他删了
  const mobileTabList = props.layoutConfig.mobileConfig.tabContainers
  const mobileIndex = mobileTabList.findIndex((n) => n.attr === item.attr)
  mobileTabList.splice(mobileIndex, 1)
  set(props.layoutConfig.mobileConfig, 'tabContainers', mobileTabList)

  // set(props.linkLists[targetIndex], 'enable', 0)
  nextTick(() => {
    console.log(canDragTabListForDefaultLayout.value)
    canDragTabListForDefaultLayoutList.value.updateItems()
  })
}

const handleSwitchPanorama = (item, index) => {
  if (tabList.value[index].enable === 0) {
    set(tabList.value[index], 'enable', 1)
  } else {
    set(tabList.value[index], 'enable', 0)
  }
  updateTabList()
  nextTick(() => {
    noDragTabListForDefaultLayoutList.value.updateItems()
  })
}

const handleSortDefaultLinkTab = (e, item) => {
  if (props.activeHead === 'pc') {
    set(props.layoutConfig.webConfig, props.container, [
      ...noDragTabListForDefaultLayout.value,
      ...item
    ])
  } else {
    set(props.layoutConfig.mobileConfig, 'tabContainers', [
      ...noDragTabListForDefaultLayout.value,
      ...item
    ])
  }
}

const handleDefaultAddSearchInput = (e) => {
  console.log(e)
  if (e) {
    const arr = originDefaultAssociationList.value.filter((item) => {
      return item.name.toLowerCase().includes(e.toLowerCase())
    })
    defaultAssociationList.value = xbb.deepClone(arr)
  } else {
    defaultAssociationList.value = xbb.deepClone(originDefaultAssociationList.value)
  }
}

const coverConfig = computed({
  get() {
    if (props.activeHead === 'mobile') {
      return props.layoutConfig.mobileConfig.cover || 0
    } else {
      const config = toRaw(props.layoutConfig)
      if (props.container === 'leftTabContainer') {
        return config.webConfig.leftCover || 0
      } else {
        return config.webConfig.rightCover || 0
      }
    }
  },
  set(val) {
    if (props.activeHead === 'mobile') {
      set(props.layoutConfig.mobileConfig, 'cover', val)
    } else {
      if (props.container === 'leftTabContainer') {
        set(props.layoutConfig.webConfig, 'leftCover', val)
      } else {
        set(props.layoutConfig.webConfig, 'rightCover', val)
      }
    }
  }
})

defineExpose({
  initVal
})
</script>

<style lang="scss" scoped>
.tab-config {
  padding-bottom: 20px;
}
.icon-edit-line,
.icon-check-line,
.icon-delete-bin-7-line {
  color: $text-auxiliary;
  cursor: pointer;
}

.field_button {
  width: 100%;
  padding: 7px 20px;
  font-size: 12px;
  color: $text-plain;
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
}
.border-wrap {
  margin-bottom: 10px;
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
  :deep(.item-list__wrapper) {
    border: none;
  }
}
.tab-person-setting {
  display: flex;
  margin-top: 20px;
}
.addTab {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: $neutral-color-1;
  border-radius: 4px;
  .el-input {
    width: 228px;
  }
  .icon-check-line,
  .icon-close-line {
    width: 20px;
    height: 20px;
    margin-left: 8px;
    font-size: 16px;
  }
}
.empty-tips {
  color: $text-plain;
}
.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 16px;
  color: $text-auxiliary;
  cursor: pointer;
  border-radius: 4px;

  &:not(.draggable-icon) {
    &:hover {
      background: $neutral-color-2;
    }
  }

  &.disabled {
    color: $text-grey;
    cursor: not-allowed;

    &:hover {
      background: inherit;
    }
  }
}

.cover-tips {
  img {
    width: 100%;
  }
}
.icon-question-line {
  margin-left: 4px;
  color: $text-grey;
  cursor: pointer;
  &:hover {
    color: $brand-base-color-6;
  }
}
</style>

<style lang="scss">
.tab-set-close-icon {
  color: $text-auxiliary;
  cursor: pointer;
}
.add-data-popover__content {
  .item-Field_item {
    height: 32px;
    line-height: 32px;
  }
}
</style>
