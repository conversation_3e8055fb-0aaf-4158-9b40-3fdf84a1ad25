<template>
  <div class="draggable-setting-group">
    <slot></slot>
  </div>
</template>

<script setup>
import { provide } from 'vue'

// 目前组件只有渲染的作用。provide主要用于插槽里draggable-setting组件判断是否在容器中，用于渲染，后续若容器组件有更多属性且需要给子组件判断使用时可以传到对象里
provide('draggableSettingGroup', {})
</script>

<style lang="scss" scoped>
.draggable-setting-group {
  box-sizing: border-box;
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
}
</style>
