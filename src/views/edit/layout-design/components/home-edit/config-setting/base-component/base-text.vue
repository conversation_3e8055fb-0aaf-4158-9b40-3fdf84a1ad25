<template>
  <!-- 基础组件---文本 -->
  <div class="base-text">
    <FieldTitle
      :default-title="componentConfig.name"
      :enable.sync="configControl.displayTitle"
      :title.sync="configControl.name"
    ></FieldTitle>
    <TitleLine text="内容"></TitleLine>
    <FieldBox class="base-text__textarea">
      <!-- 富文本 -->
      <QuillEditor
        ref="refQuillEditor"
        v-model="configControl.config.content"
        :options="editorOption"
      >
      </QuillEditor>
    </FieldBox>
  </div>
</template>

<script setup>
// vue
import { onMounted, ref, nextTick, onUnmounted } from 'vue'
// mixin
import { useComponent, usePropsData } from '../config-setting.mixin.js'
// emits
const emit = defineEmits(['update:config'])
// props
const props = defineProps({
  // 对应组件的配置info
  config: {
    type: Object,
    required: true,
    default: () => ({
      // name: ''
      // displayTitle
      // config: { content: '' }
    })
  },
  // 对应组件的枚举配置
  componentConfig: {
    type: Object,
    required: true,
    default: () => ({})
  }
})
// components
import { quillEditor as QuillEditor } from 'vue-quill-editor'

const { TitleLine, FieldBox, FieldTitle } = useComponent()
// data
let topDom = null // 顶层容器节点，兼容富文本插件输入文本链接时被遮挡的问题
let tabDom = null //
const editorOption = ref({
  placeholder: '请输入',
  modules: {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['link'], // 这里包含了超链接工具按钮
        // ['link', 'image'],
        ['clean']
      ]
    }
  },
  bounds: document.body // 设置 bounds 为整个页面
})
const refQuillEditor = ref(null)
// computed
const configControl = usePropsData({ emit, props, propName: 'config' })
// methods
const iniQuillEditor = () => {
  const myQuillEditor = refQuillEditor.value
  // 监听富文本内部粘贴行为
  myQuillEditor.quill.root.addEventListener(
    'paste',
    (evt) => {
      if (evt.clipboardData && evt.clipboardData.files && evt.clipboardData.files.length) {
        evt.preventDefault()
        ;[].forEach.call(evt.clipboardData.files, async (file) => {
          console.log('🚀 ~ [].forEach.call ~ file:', file)
          // 区分图片粘贴还是文字粘贴
          if (file.type.match(/^image\/(gif|jpe?g|a?png|bmp)/i)) {
            return
          }
        })
      }
    },
    false
  )
}
// lice-cycle
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      topDom = document.querySelector('.layout-edit__side.right')
      tabDom = topDom.querySelector('.el-tabs__content')
      console.log('🚀 ~ setTimeout ~ topDom:', topDom, tabDom)
      topDom.style.overflow = 'unset'
      tabDom.style.overflow = 'unset'
      iniQuillEditor()
    }, 100)
  })
})

onUnmounted(() => {
  topDom.style.overflow = 'auto'
  tabDom.style.overflow = 'auto'
})
</script>

<style lang="scss" scoped>
.base-text {
  &__textarea {
    position: relative; /* 确保 z-index 生效 */
    z-index: 9999; /* 提升整个编辑器容器的层级 */
    box-sizing: border-box;
    :deep(.quill-editor) {
      .ql-container {
        height: 40vh !important;
        // max-height: 50vh !important;
        // overflow: auto;
      }
      .ql-snow {
        // 新增
        .ql-editing[data-mode='link']::before {
          content: '请输入链接地址:';
        }
        .ql-editing a.ql-action::after {
          padding-right: 0px;
          content: '保存';
          border-right: 0px;
        }
        .ql-editing a.ql-action::before {
          display: none !important;
        }
        // a.ql-preview::after {
        //   padding-right: 0px;
        //   content: '预览url';
        //   border-right: 0px;
        // }
        .ql-tooltip::before {
          content: '查看 URL';
        }
        a.ql-action::after {
          padding-right: 0px;
          content: '编辑';
          border-right: 0px;
        }
        a.ql-remove::before {
          padding-right: 0px;
          content: '移除';
          border-right: 0px;
        }
      }
    }
    // &--focus {
    //   :deep(.ql-container) {
    //     height: 50vh !important;
    //   }
    // }
  }
}
</style>
