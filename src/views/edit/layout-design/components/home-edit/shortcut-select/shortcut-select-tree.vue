<template>
  <div class="shortcut-select-tree">
    <!-- search -->
    <el-input
      v-show="isSearchVisible"
      v-model="nameLike"
      clearable
      placeholder="请输入名称"
      prefix-icon="el-icon-search"
    ></el-input>
    <!-- tree -->
    <el-scrollbar v-loading="loading" class="shortcut-select-tree__scrollbar">
      <el-tree
        ref="refTree"
        :data="dataList"
        :filter-node-method="filterNode"
        :node-key="props.defaultProps.key"
        :props="props.defaultProps"
      >
        <p
          slot-scope="{ data }"
          class="custom-tree-node"
          :class="!data[defaultProps.children] && 'is-deep-content'"
          @click="toSelect(data, $event)"
        >
          <template v-if="!data[defaultProps.children]">
            <el-checkbox
              :disabled="limitSelect && !selectValueIdList.includes(data[defaultProps.key])"
              size="medium"
              :value="selectValueIdList.includes(data[defaultProps.key])"
            ></el-checkbox>
            <!-- <el-checkbox
              v-if="limitSelect && !selectValueIdList.includes(data[defaultProps.key])"
              :disabled="true"
              size="medium"
            ></el-checkbox>
            <el-checkbox
              v-else
              size="medium"
              :value="selectValueIdList.includes(data[defaultProps.key])"
              @click.prevent.native="toSelect(data)"
            ></el-checkbox> -->
          </template>
          <i class="iconfont" :class="data.icon"></i>
          <span class="custom-tree-node__text">{{ data[defaultProps.label] }}</span>
        </p>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { onMounted, ref, computed, watch, nextTick } from 'vue'
// api
import { getMenuList } from '@/api/system.js'
import { getToolList } from '@/api/ui-paas/home.js'
// enum
import { ENUM_TAB_INFO } from './enum.shortcut-select.js'
// emits
const emit = defineEmits(['update:select-value'])
// props
const props = defineProps({
  selectValue: {
    type: Array,
    default: () => []
  },
  // 当前tab类型
  activeTab: {
    type: String,
    default: ''
    // ['QuickNew', 'CommonForms', 'Tools']
  },
  // 当前tab类型info
  activeTabInfo: {
    type: Object,
    default: () => ({})
  },
  // 是否限制可选项
  limitSelect: {
    type: Boolean,
    default: false
  },
  defaultProps: {
    type: Object,
    default: () => ({
      children: 'subMenuList',
      label: 'name',
      key: 'key'
    })
  },
  // 平台标识
  plateForm: {
    type: String,
    default: 'web'
  }
})

// data
const loading = ref(true)
const dataList = ref([])
const refTree = ref(null)
const nameLike = ref('')
// computed
const selectValueControl = computed({
  set(val) {
    emit('update:select-value', val)
  },
  get() {
    return props.selectValue
  }
})
const selectValueIdList = computed(() => {
  return selectValueControl.value.map((item) => item[props.defaultProps.key])
})
const isSearchVisible = computed(() => {
  // 不渲染搜索的list
  const notShowList = [ENUM_TAB_INFO.Tools.componentName]
  return !notShowList.includes(props.activeTab)
})
// 树节点过滤方法
watch(nameLike, (newVal) => {
  refTree.value.filter(newVal)
})
const filterNode = (value, data) => {
  if (!value) return true
  return data[props.defaultProps.label].indexOf(value) !== -1
}
const formatTree = (treeList, childrenKey, isPass = false) => {
  const dataList = []
  treeList.forEach((item) => {
    const obj = {
      ...item,
      menuId: item.id,
      key: String(item.id)
    }
    if (obj[childrenKey]) {
      const children = formatTree(item[childrenKey], childrenKey, isPass)
      // 过滤掉没有子节点的父节点
      if (children.length) {
        obj[childrenKey] = children
        dataList.push(obj)
      }
    } else if (isPass) {
      dataList.push(obj)
    } else {
      obj.hasQuickNewFlag === 1 && dataList.push(obj)
    }
  })
  return dataList
}
/**
 * 分块渲染方法，使用 requestAnimationFrame 来避免一次性渲染导致的性能问题
 * @param {Array} data - 需要渲染的数据
 * @param {Function} renderChunk - 渲染单个块的回调函数
 * @param {Number} chunkSize - 每次渲染的块大小
 * @param {Function} [onComplete] - 可选，渲染完成后的回调
 */
const chunkedRender = ({ data, renderChunk, chunkSize = 10, onComplete }) => {
  let index = 0 // 当前渲染的索引
  const totalData = data // 总数据
  const totalChunks = Math.ceil(totalData.length / chunkSize) // 总块数

  // 渲染单个块的函数
  function render() {
    // 计算当前块的起始和结束位置
    const start = index * chunkSize
    const end = Math.min(start + chunkSize, totalData.length)

    // 获取当前块的数据
    const chunk = totalData.slice(start, end)

    // 调用渲染回调函数
    renderChunk(chunk, start)

    // 增加索引
    index++

    // 如果还有未渲染的块，继续请求下一帧
    if (index < totalChunks) {
      requestAnimationFrame(render)
    } else if (onComplete) {
      // 渲染完成，调用 onComplete 回调
      onComplete()
    }
  }

  // 启动渲染
  requestAnimationFrame(render)
}

const getQuickNew = () => {
  loading.value = true
  getMenuList({
    isSync: true,
    uiPaasSet: props.plateForm === 'app' ? 1 : undefined
  })
    .then((res) => {
      const childrenKey = props.defaultProps.children
      // dataList.value = res.result.menuList.map((item) => {
      //   const menuInfoChildren = []
      //   item[childrenKey].forEach((childItem) => {
      //     childItem.hasQuickNewFlag === 1 &&
      //       menuInfoChildren.push({
      //         ...childItem,
      //         menuId: childItem.id,
      //         key: String(childItem.id)
      //       })
      //   })
      //   const menuInfo = {
      //     ...item,
      //     [childrenKey]: menuInfoChildren
      //   }
      //   return menuInfo
      // })
      // dataList.value = formatTree(res.result.menuList, childrenKey)
      chunkedRender({
        data: formatTree(res.result.menuList, childrenKey),
        renderChunk: (data) => {
          console.log('🚀 ~ .then ~ data:', data)
          dataList.value.push(...data)
        },
        onComplete: async () => {
          await nextTick()
          setTimeout(() => {
            loading.value = false
          }, 100)
        }
      })
    })
    .finally(() => {})
}
const getCommonForms = () => {
  loading.value = true
  getMenuList({
    isSync: true,
    uiPaasSet: props.plateForm === 'app' ? 1 : undefined
  })
    .then((res) => {
      const childrenKey = props.defaultProps.children
      // dataList.value = res.result.menuList.map((item) => {
      //   return {
      //     ...item,
      //     [childrenKey]: item[childrenKey].map((childItem) => {
      //       return {
      //         ...childItem,
      //         menuId: childItem.id,
      //         key: childItem.id
      //       }
      //     })
      //   }
      // })
      // dataList.value = formatTree(res.result.menuList, childrenKey, true)
      chunkedRender({
        data: formatTree(res.result.menuList, childrenKey, true),
        renderChunk: (data) => {
          console.log('🚀 ~ .then ~ data:', data)
          dataList.value.push(...data)
        },
        onComplete: async () => {
          await nextTick()
          setTimeout(() => {
            loading.value = false
          }, 100)
        }
      })
    })
    .finally(() => {})
}
const getTools = () => {
  loading.value = true
  getToolList()
    .then((res) => {
      const dataAlias = props.plateForm === 'web' ? 'webTool' : 'mobileTool'
      dataList.value = (res.result[dataAlias] || []).map((item) => {
        return {
          ...item,
          key: item.alias
        }
      })
    })
    .finally(() => {
      loading.value = false
    })
}
const getDataList = () => {
  // constant
  const ENUM_API = {
    QuickNew: getQuickNew,
    CommonForms: getCommonForms,
    Tools: getTools
  }
  // TODO: 到时候根据不同接口去请求对应数据，并统一处理
  // 映射tab数据
  // setTimeout(() => {
  ENUM_API[props.activeTab] && ENUM_API[props.activeTab]()
  // }, 100)
}
const toSelect = (data, event) => {
  if (data[props.defaultProps.children]) return
  event.stopPropagation()
  event.preventDefault()
  if (props.limitSelect && !selectValueIdList.value.includes(data[props.defaultProps.key])) return
  console.log('🚀 ~ toSelect ~ data:', data)
  const hasIndex = selectValueControl.value.findIndex(
    (item) => item[props.defaultProps.key] === data[props.defaultProps.key]
  )
  if (hasIndex > -1) {
    // 取消
    selectValueControl.value.splice(hasIndex, 1)
  } else {
    // 选中
    selectValueControl.value.push({
      ...data,
      // 需要type标识
      type: props.activeTabInfo.type
    })
  }
}

// lice-cycle
onMounted(() => {
  // 获取选择列表数据
  getDataList()
  // // 如果当前是【工具】tab
  // if(props.activeTab === ENUM_TAB_INFO.Tools.componentName) {
  //   // 隐藏el-tree的左侧占位
  //   const app = getCurrentInstance().proxy
  //   app.$el.classList.add('normal-list')
  //   //
  // }
})
</script>

<style lang="scss" scoped>
.shortcut-select-tree {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 12px;
  .el-input {
    margin-bottom: 12px;
  }
  &__scrollbar {
    flex-grow: 1;
    height: 0;
  }
  // &.normal-list {
  :deep(.el-tree .el-tree-node .el-tree-node__content) {
    height: 36px;
    .is-deep-content {
      .el-checkbox {
        .el-checkbox__input,
        .el-checkbox__inner {
          box-sizing: border-box;
          width: 18px;
          height: 18px;
        }
        .el-checkbox__input .el-checkbox__inner::after {
          width: 6px;
          height: 8px;
        }
      }
      display: flex;
      align-items: center;
      margin-left: 12px;
    }
    .custom-tree-node {
      display: flex;
      flex-grow: 1;
      align-items: center;
      width: 100%;
      height: 100%;
      & > .el-checkbox {
        margin-right: 8px;
      }
      .iconfont {
        display: inline-block;
        flex-shrink: 0;
        width: 22px;
        height: 16px;
        margin-right: 8px;
        // margin-right: 8px;
        // font-size: 14px;
        font-size: 16px;
        color: $text-auxiliary;
        text-align: center;
      }
      .custom-tree-node__text {
        color: $text-plain;
      }
    }
    .is-leaf {
      display: none !important;
    }
    .el-checkbox {
      transform: scale(0.8889);
    }
  }
  // }
}
</style>
