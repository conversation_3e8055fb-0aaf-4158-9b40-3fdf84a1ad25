<template>
  <div class="setting-cover">
    <el-checkbox v-model="checkedValue" :false-label="0" :true-label="1"></el-checkbox>
    <span class="setting-cover__title">{{ title }}</span>
    <el-tooltip :effect="effect" :open-delay="500" placement="top">
      <div slot="content" v-html="content"></div>
      <i class="icon-question-line t-iconfont"></i>
    </el-tooltip>
  </div>
</template>

<script>
import i18n from '@/lang'

export default {
  name: 'SettingCover',
  model: {
    prop: 'value',
    event: 'modelChange'
  },
  props: {
    value: {
      type: Number,
      default: 0
    },
    title: {
      type: String,
      default: () => i18n.t('layoutDesign.coverViewSet')
    },
    content: {
      type: String,
      default: () => i18n.t('layoutDesign.coverViewSetTips')
    },
    effect: {
      type: String,
      default: 'dark'
    }
  },
  computed: {
    checkedValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('modelChange', value)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.setting-cover {
  display: flex;
  margin-bottom: 20px;
  &__title {
    margin-left: 8px;
    font-size: 14px;
    color: $text-main;
  }
  .t-iconfont {
    margin-left: 6px;
    font-size: 16px;
    color: $text-grey;
    cursor: pointer;
    &:hover {
      color: $brand-base-color-6;
    }
  }
}
</style>
