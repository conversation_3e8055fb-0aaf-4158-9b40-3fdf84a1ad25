<!--
 * @Description: 描述
 -->
<template>
  <div class="workflow-design-template">
    <template v-if="isProcessForm === 1">
      <div class="flow-chart-panel">
        <div class="top-tool">
          <flow-version
            v-model="currentFlowId"
            :add-loading.sync="addLoading"
            :flow-list="flowList"
            @addFlowVersion="addFlowVersion"
            @change="currentFlowIdChange"
            @delFlowVersion="delFlowVersion"
            @flowInit="flowInit"
            @selectFlowVersion="selectFlowVersion"
          ></flow-version>
          <div class="top-tool__opration">
            <el-button
              v-if="saveBtn"
              class="pass-btn"
              :disabled="saveLoading"
              :loading="saveLoading"
              plain
              @click="timer_saveVersion"
              >{{ $t('operation.save') }}</el-button
            >
            <el-button
              v-if="flowInfo && flowInfo.processTemplate.enable === 0"
              class="pass-btn"
              :disabled="enableLoading"
              :loading="enableLoading"
              type="primary"
              @click="timer_enableClick"
              >{{ $t('workflowDesign.enablingProcess') }}</el-button
            >
            <el-button
              v-else-if="flowInfo && flowInfo.processTemplate.enable === 1"
              @click="closeFlowClick"
              >{{ $t('workflowDesign.pauseProcess') }}</el-button
            >
          </div>
        </div>

        <FlowDesign
          ref="flowDesign"
          :explain-list="explainList"
          :prop-node-data="processTree"
          :readonly="!!nodeEnable"
          :select-item="itemConnect || itemNode"
          style="height: calc(100% - 45px)"
          :summary="summary"
          @conditionNodeClick="conditionNodeClick"
          @nodeClick="nodeClick"
        />
        <!-- <js-plumb :enable="enable" ref="jsPlumb" :defaultConnects="flowInfo&&flowInfo.conditionList||[]" :defaultNodes="flowInfo&&flowInfo.nodeList||[]" @flow-select="flowSelect" /> -->
      </div>
      <template>
        <RightSetting
          :key="(itemNode && itemNode.nodeId) || (itemConnect && itemConnect.nodeId) || +Date.now()"
          ref="rightSetting"
          v-model="activeName"
          :explain-list="filterExplainList"
          :flow-info="flowInfo"
          :form-info="formInfo"
          :form-info-field-list="formInfoFieldList"
          :form-model="formModel"
          :form-rules="formRules"
          :item-branch-wrap="itemBranchWrap"
          :item-connect="itemConnect"
          :item-node="itemNode"
          :item-prop-info="itemPropInfo"
          :need-approve-fields="needApproveFields"
          :prop-attr-type.sync="attrType"
          :readonly="!!enable"
          :role-list="roleList"
          @addConditionClick="addConditionClick"
          @addUserClick="addUserClick"
          @closeFlow="closeFlow"
          @nodeNameChange="nodeNameChange"
          @nodeOperationEdit="nodeOperationEdit"
          @nodeOperationSubmit="nodeOperationSubmit"
        >
        </RightSetting>
      </template>
    </template>
    <template v-else>
      <el-button
        class="open-flow-btn yellow-btn"
        style="font-weight: normal"
        @click="openFlowClick"
        >{{ $t('workflowDesign.useProcessForm') }}</el-button
      >
      <p class="open-flow-p">{{ $t('workflowDesign.buildApprovalHint') }}</p>
    </template>
    <!-- <personnel-select
      v-if="personDialogShow"
      :defaultVal="itemSelectUserDefaultVal"
      @dialog-submit="personDialongSubmit"
      :show.sync="personDialogShow">
    </personnel-select> -->
  </div>
</template>

<script>
import store from '@/store'
import { getConditionList } from '@/api/statistics'
import {
  addFlow,
  getFlowInfo,
  getFlowList,
  getNeedApproveFields,
  getProcessFormFields,
  processDelete,
  processEnable,
  processOpen,
  processSave
} from '@/api/workflow-design'
import PersonSelect from '@/components/person-select/index'
import { roleList } from '@/api/system'
import RightSetting from './right-setting'
import FlowDesign from '@/components/flow-design'
import FlowVersion from './components/flow-version'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'Flow',
  components: {
    RightSetting,
    FlowDesign,
    FlowVersion
  },
  data() {
    return {
      enableLoading: false,
      saveLoading: false,
      addLoading: true, // 添加按钮loading状态
      isPause: false, // 是否流程暂停
      saveBtn: false, // 设计模式时 显示保存按钮
      flowVersionType: '', // 当前流程版本类型1：设计 2：启用 3：历史
      flowList: [], // 流程列表
      currentFlowId: 0, // 当前流程id
      limitNum: 0, // 0代表不限制 大于0以上代表限制的个数
      explainList: [],
      flowDesignReadonly: false,
      processTree: {},
      needApproveFields: [], // 需审批字段
      roleList: [],
      allConditionMap: null,
      summaryIsCheckAll: false,
      itemSelectUserType: '',
      itemSelectUserDefaultVal: [],
      personDialogShow: false,
      activeName: 'flow',
      attrType: this.$t('workflowDesign.basicAttribute'),
      itemNode: null, // 当前节点
      itemBranchWrap: null, // da
      itemConnect: null, // 当前连接
      itemPropInfo: null, // 用来记录当前是否有节点操作需要设置
      flowInfo: null,
      flowInfoInit: null,
      // 用来缓存节点操作未保存状态的值
      formModel: {
        commitText: '', // 提交文字
        storageFlag: false, // 暂存是否开启
        storageText: '', // 暂存文字
        commitPrintFlag: false, // 是否开启提交并打印（1：开启；0：不开启）
        commitPrintText: '', // 提交并打印文案
        backFlag: 0, // 是否开启回退（1：开启；0：不开启）
        backType: 0, // 0 上一节点 1：所选节点
        backText: '', // 回退文案
        transferFlag: 0, // 是否开启转交（1：开启；0：不开启）
        transferText: '', // 转交文案
        endFlag: '', // 是否开启结束流程（1：开启；0：不开启）
        endText: '', // 结束流程文案
        conditionVal: [] // 连线流转条件
      },
      commonRules: [
        {
          required: true,
          message: this.$t('workflowDesign.buttonNameCannotBeEmpty'),
          trigger: 'change'
        },
        { min: 1, max: 5, message: '长度在 3 到 5 个字符', trigger: 'change' }
      ],
      formRules: {
        commitText: this.commonRules,
        storageText: this.commonRules,
        commitPrintText: this.commonRules,
        backText: this.commonRules,
        transferText: this.commonRules,
        endText: this.commonRules
      }
    }
  },
  computed: {
    personSelectShowTabsName() {
      const arr = ['dept', 'user', 'role', 'userGroup']
      if (this.itemSelectUserType !== 'transferUserList') {
        arr.push('dynamicManager')
      }
      return arr
    },
    filterExplainList() {
      const filterFields = [10002]
      let res = []
      res = this.explainList.filter((item) => {
        return !filterFields.includes(item.fieldType) && item.isOpen !== 2 // 排除回收站字段
      })
      return res
    },
    nodeEnable() {
      return this.flowInfo && this.flowInfo.processTemplate && this.flowInfo.processTemplate.enable
    },
    /**
     * 当前流程是否启用中
     */
    enable() {
      return this.flowInfo && this.flowInfo.processTemplate && this.flowInfo.processTemplate.enable // 新增当前版本为历史版本也不可编辑 (this.flowVersionType === 3)
    },
    formInfo() {
      return this.$store.state.form.formInfo
    },
    formAttr() {
      return this.formInfo && this.formInfo.formAttr
    },
    summary() {
      return (this.formAttr && this.formAttr.summary) || []
    },
    isProcessForm() {
      return this.formInfo && this.formInfo.formAttr && this.formInfo.formAttr.isProcessForm
    },
    // editChangeFlowSave () {
    //   return this.$store.state.editPage.editChangeFlowSave
    // },
    /**
     * 根据当前表单对象计算出字段
     * 主要是因为默认就带3个字段
     */
    formInfoFieldList() {
      // const canFilterField = [4, 10015, 10014, 1, 3, 10000, 10010, 10012, 10009, 10011, 12, 10003, 7, 2, 9, 10001, 10013, 10014, 10015, 10016, 10017, 10018] // 可以设置条件的字段
      let res = [].concat(this.formInfo.explainList)
      let flowFields = [
        {
          attr: 'creatorId',
          attrName: this.$t('nouns.founder'),
          fieldType: 10013,
          sys: 1
        },
        {
          attr: 'creator_dept',
          attrName: this.$t('workflowDesign.initiatingDepartment'),
          fieldType: 50001,
          sys: 1
        },
        {
          attr: 'addTime',
          attrName: this.$t('label.addTime'),
          fieldType: 10014,
          sys: 1
        },
        {
          attr: 'updateTime',
          attrName: this.$t('nouns.updateTime'),
          fieldType: 10015,
          sys: 1
        }
      ]
      if (+this.$route.query.businessType === 100) {
        flowFields = [
          // {
          //   attr: 'creatorId',
          //   attrName: this.$t('nouns.founder'),
          //   fieldType: 10013
          // },
          {
            attr: 'creator_dept',
            attrName: this.$t('workflowDesign.initiatingDepartment'),
            fieldType: 50001,
            sys: 1
          },
          {
            attr: 'addTime',
            attrName: this.$t('label.addTime'),
            fieldType: 10014,
            sys: 1
          },
          {
            attr: 'updateTime',
            attrName: this.$t('nouns.updateTime'),
            fieldType: 10015,
            sys: 1
          }
        ]
      }
      res = res.filter(({ attr }) => {
        return !/^(creatorId|addTime|updateTime)$/g.test(attr)
      })
      // const flowFields = []
      // const attrs = this.formInfo.explainList.map(item => item.fieldType)
      res = res.concat(flowFields)
      // 文档 https://xbb.yuque.com/xbb/vsf9sv/hsq93o/edit
      res = res.filter((item) => {
        const c1 = true // canFilterField.includes(item.fieldType) // 条件1
        const c2 = item.editHide !== 1 // 设计阶段隐藏的字段过滤掉 （https://xbb.yuque.com/xbb/vsf9sv/kubvxk）
        // let c3 = (item.showType !== undefined && item.showType === 0) || true
        const c3 = item.showType === undefined ? true : /^(0|1|4|5)$/g.test(item.showType)
        // let c4 = item.isOpen !== 0
        const c4 = ![0, 2].includes(item.isOpen) // 增加回收站字段的过滤
        return c1 && c2 && c3 && c4
      })
      return res
    }
  },
  methods: {
    init() {
      this.flowInfo = null
      this.getRoleList()
      this.getAllConditionList()
      this.getNeedApproveFields()
    },
    getProcessFormFields() {
      getProcessFormFields({
        ...this.$route.query
        // 'appId': this.$route.query.appId,
        // 'menuId': this.$route.query.menuId,
        // 'formId': this.$route.query.formId,
      })
        .then(({ result: { explainList } }) => {
          this.explainList = explainList
        })
        .catch(() => {})
    },
    nodeClick(data) {
      this.itemPropInfo = null
      this.attrType = this.$t('workflowDesign.basicAttribute')
      // data 为空表示当前无选中目标
      if (data) {
        const type = data.type
        // 节点类型，1.开始节点，2.结束节点，3.流程节点，4.抄送节点 line.连接
        if (type === 5) {
          this.itemNode = null
          this.itemConnect = data
          this.activeName = 'node'
        } else {
          this.itemConnect = null
          this.itemNode = data
          this.activeName = 'node'
        }
      } else {
        this.itemNode = null
        this.itemConnect = null
        this.activeName = 'flow'
      }
    },
    // 条件节点点击
    conditionNodeClick(data) {
      this.itemBranchWrap = data
    },
    /**
     * 添加流程流转条件点击事件
     */
    addConditionClick(item, subForm) {
      this.itemConnect.data.conditions.push({
        subForm,
        name: item.attrName,
        subName: subForm ? subForm.attrName + '.' + item.attrName : '',
        field: item,
        whereList: this.allConditionMap[item.fieldType],
        whereVal: this.allConditionMap[item.fieldType][0]['symbol'],
        attr: subForm ? subForm.attr : item.attr,
        editable: 1, // 是否可编辑
        subAttr: subForm ? item.attr : '', // 记录子表单的字段
        fieldType: item.fieldType, // 属性/字段
        symbol: this.allConditionMap[item.fieldType][0]['symbol'], // 判断条件 equal：等于 noEqual 不等于：empty: 为空；noEmpty：不为空；like：包含任意一个；noLike：不包含任意一个；in：等于任意一个；
        // noIn：不等于任意一个；greaterEqual：大于等于；lessEqual：小于等于；range：选择范围
        value: undefined // 判断参数
      })
    },
    closeFlowClick() {
      this.$confirm(this.$t('workflowDesign.suspendHint'), this.$t('message.confirmTitle'), {
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      }).then(() => {
        processEnable({
          appId: this.flowInfo.appId, // 应用id
          menuId: this.flowInfo.processTemplate.menuId, // 菜单id
          formId: this.flowInfo.processTemplate.formId, // 表单id
          processTemplateId: this.flowInfo.processTemplate.id, // 节点属性
          enable: 0, // 是否启用， 1启用，0不启用 必传
          saasMark: this.$route.query.saasMark,
          businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
        })
          .then((res) => {
            this.$message({
              message: this.$t('workflowDesign.processPausedSuccessfully'),
              type: 'success',
              duration: 2000
            })
            this.isPause = true // 暂停标志
            this.flowInit()
          })
          .catch((erro) => {
            this.$message.error(this.$t('workflowDesign.processPauseFailed'))
          })
      })
    },
    @xbb.debounceWrap()
    timer_enableClick() {
      return this.enableClick()
    },
    enableClick() {
      this.$refs['flowDesign'].validate((vali, erroList) => {
        if (vali) {
          this.enableLoading = true
          this.savePromise()
            .then(() => {
              this.$store.commit('SET_FLOW_HASCHANGENOSAVE', false)
              this.enableFlow()
            })
            .finally(() => {
              this.enableLoading = false
            })
        } else {
          this.$message.error(this.$t('workflowDesign.errorHint'))
        }
      })
    },
    saveFlowClick() {
      return new Promise((resolve, reject) => {
        this.$refs['flowDesign'].validate((vali, erroList) => {
          if (vali) {
            this.savePromise()
              .then((res) => {
                this.$store.commit('SET_FLOW_HASCHANGENOSAVE', false)
                this.getFlowInfo()
                this.$message({
                  message: this.$t('workflowDesign.processSavedSuccessfully'),
                  type: 'success',
                  duration: 2000
                })
                resolve()
              })
              .catch((erro) => {
                console.log(erro)
              })
          } else {
            this.$message.error(this.$t('workflowDesign.failedToEnableHint'))
          }
        })
      })
    },
    enableFlow() {
      return new Promise((resolve, reject) => {
        processEnable({
          appId: this.flowInfo.appId, // 应用id
          menuId: this.flowInfo.processTemplate.menuId, // 菜单id
          formId: this.flowInfo.processTemplate.formId, // 表单id
          processTemplateId: this.flowInfo.processTemplate.id, // 节点属性
          enable: 1, // 是否启用， 1启用，0不启用 必传

          saasMark: this.$route.query.saasMark,
          businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
        })
          .then((res) => {
            this.$message({
              message: this.$t('workflowDesign.processOpenedSuccessfully'),
              type: 'success',
              duration: 2000
            })
            this.flowInit()
            resolve()
          })
          .catch((erro) => {
            reject(erro)
            // this.$message.error(this.$t('workflowDesign.processOpenFailed'))
          })
      })
    },
    savePromise() {
      return new Promise((resolve, reject) => {
        const processList = this.$refs['flowDesign'].formatData()
        const mapProcessList = processList.map((item) => {
          if (item.type === 5) {
            const data = item.data
            const conditions = data.conditions || []
            const filterConditions = conditions.filter(({ value, symbol }) => {
              return (
                /^(empty|noempty)$/.test(symbol) ||
                (value && value.length && (value[0] === 0 ? true : value[0]))
              )
            })
            return {
              ...item,
              data: {
                ...data,
                conditions: filterConditions
              }
            }
          }
          return item
        })
        console.log(mapProcessList)
        // if (this.flowVersionType === 3) { // 当前版本为历史版本 不保存
        //   resolve()
        //   return
        // }
        processSave({
          type: 2,
          saasMark: this.$route.query.saasMark,
          businessType: this.$route.query.businessType, // 区分SaaS和PaaS流程 PaaS流程此字段为空
          processTemplate: this.flowInfo.processTemplate,
          appId: this.flowInfo.appId,
          formId: this.flowInfo.formId,
          menuId: this.flowInfo.processTemplate.menuId, // 菜单id
          nodeList: [],
          conditionList: [],
          newProcessNodeList: mapProcessList.map((item) => {
            const obj = JSON.parse(JSON.stringify(item))
            delete obj.childNode
            delete obj.conditionNodes
            return obj
          })
        })
          .then((res) => {
            this.itemConnect = null
            this.itemNode = null
            resolve(res)
          })
          .catch(() => {})
          .finally(() => {
            this.enableLoading = false
          })
      })
    },

    /**
     * 获取需审批字段
     */
    getNeedApproveFields() {
      const { appId, menuId, formId } = this.$route.query
      getNeedApproveFields({
        appId: appId,
        menuId: menuId, // 菜单id 必传
        formId: formId,

        saasMark: this.$route.query.saasMark,
        businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
      })
        .then(({ result: { list } }) => {
          this.needApproveFields = list
        })
        .catch(() => {})
    },
    personDialongSubmit(val) {
      switch (this.itemSelectUserType) {
        case 'transferUserList':
          this.itemNode.data.transferUserList = val
          break
        case 'mainUserList':
          this.itemNode.data.mainUserList = val
          break
        case 'ccUserList':
          this.itemNode.data.ccUserList = val
          break
        default:
          break
      }
    },
    /**
     * 关闭流程
     */
    closeFlow() {
      this.$confirm(this.$t('workflowDesign.closeHint'), this.$t('message.confirmTitle'), {
        // showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        distinguishCancelAndClose: true,
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(() => {
          processDelete({
            appId: this.flowInfo.appId, // 应用id
            menuId: this.flowInfo.processTemplate.menuId, // 菜单id
            formId: this.flowInfo.formId, // 表单模板id
            processTemplateId: this.flowInfo.processTemplate.id, // 流程模板id 必传

            saasMark: this.$route.query.saasMark,
            businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
          })
            .then((res) => {
              if (res.code === 1) {
                this.$store
                  .dispatch('getFormInfo', {
                    appId: this.$route.query.appId, // 应用的id 必传
                    menuId: this.$route.query.menuId, // 表单模板id 必传
                    formId: this.flowInfo.formId, // 表单模板id
                    processTemplateId: this.flowInfo.processTemplate.id, // 流程模板id 必传
                    saasMark: this.$route.query.saasMark,
                    businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
                  })
                  .then((res) => {
                    this.$message({
                      type: 'success',
                      message: this.$t('workflowDesign.closedSuccessfull')
                    })
                  })
              }
            })
            .catch(() => {})
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: this.$t('workflowDesign.cancelled')
          })
        })
    },
    getRoleList() {
      roleList({})
        .then((res) => {
          this.roleList = (res.result && res.result.roleList) || []
        })
        .catch(() => {})
    },
    /**
     * type / String 标明当前是需要修改什么属性
     * transferUser 转交人
     * mainUser 节点负责人
     * ccUser 抄送人
     */
    addUserClick(type) {
      if (this.$route.path === '/edit/flow') {
        this.limitNum = 20
      } else {
        this.limitNum = 0
      }
      this.itemSelectUserDefaultVal = this.itemNode.data[type]
      this.itemSelectUserType = type

      PersonSelect({
        activeTabName: 'dept',
        title: '适用范围',
        defaultVal: this.itemSelectUserDefaultVal,
        showTabsName: this.personSelectShowTabsName,
        multiple: {
          user: true,
          dept: true,
          role: true,
          dynamicManager: true,
          userGroup: true
        },
        limitNum: this.limitNum,
        tabMultiple: true,
        otherParams: {
          formQuery: {
            formId: Number(this.$route.query.formId),
            businessType: Number(this.$route.query.businessType)
          },
          deptVisibleRule: true,
          isChecked: false,
          parentStrictly: this.itemSelectUserType === 'ccUserList',
          // 是否控制组织架构权限范围
          userGroupScene: 'workflow'
        }
      }).then((res) => {
        if (res.data) {
          this.personDialongSubmit(res.data)
        }
      })
    },
    /**
     * 获取所有的流程条件
     */
    getAllConditionList() {
      getConditionList({
        frontDev: 1
      })
        .then(({ result: { conditionList } }) => {
          this.allConditionMap = {
            ...this.allConditionMap,
            ...conditionList
          }
        })
        .catch(() => {})
    },
    /**
     * 提交操作点击完成
     */
    nodeOperationSubmit(propFlag) {
      // commit 提交、 storage 暂停、 commitPrint 提交打印、 back 回退、 transfer 转交、 end 结束流程
      switch (propFlag) {
        case 'commit':
          this.$refs['rightSetting'].$refs['commitForm'].validate((valid) => {
            if (valid) {
              this.itemNode.data.commitText = this.formModel.commitText
              this.itemPropInfo = null
            } else {
              this.$message({
                message: this.$t('workflowDesign.documentsHint'),
                type: 'warning'
              })
              return false
            }
          })
          break
        case 'storage':
          this.$refs['rightSetting'].$refs['storageForm'].validate((valid) => {
            if (valid) {
              this.itemNode.data.storageFlag = +this.formModel.storageFlag
              this.itemNode.data.storageText = this.formModel.storageText
              this.itemPropInfo = null
            } else {
              this.$message({
                message: this.$t('workflowDesign.submissionOfDocumentsHint'),
                type: 'warning'
              })
              return false
            }
          })
          break
        case 'commitPrint':
          this.$refs['rightSetting'].$refs['commitPrintForm'].validate((valid) => {
            if (valid) {
              this.itemNode.data.commitPrintFlag = +this.formModel.commitPrintFlag
              this.itemNode.data.commitPrintText = this.formModel.commitPrintText
              this.itemPropInfo = null
            } else {
              this.$message({
                message: this.$t('workflowDesign.submissionOfDocumentsHint'),
                type: 'warning'
              })
              return false
            }
          })
          break
        case 'back':
          this.$refs['rightSetting'].$refs['backForm'].validate((valid) => {
            if (valid) {
              this.itemNode.data.backFlag = +this.formModel.backFlag
              this.itemNode.data.backText = this.formModel.backText
              this.itemPropInfo = null
            } else {
              this.$message({
                message: this.$t('workflowDesign.submissionOfDocumentsHint'),
                type: 'warning'
              })
              return false
            }
          })
          break
        case 'transfer':
          this.$refs['rightSetting'].$refs['transferForm'].validate((valid) => {
            if (valid) {
              this.itemNode.data.transferFlag = +this.formModel.transferFlag
              this.itemNode.data.transferText = this.formModel.transferText
              this.itemPropInfo = null
            } else {
              this.$message({
                message: this.$t('workflowDesign.submissionOfDocumentsHint'),
                type: 'warning'
              })
              return false
            }
          })
          break
        case 'end':
          this.$refs['rightSetting'].$refs['endForm'].validate((valid) => {
            if (valid) {
              this.itemNode.data.endFlag = +this.formModel.endFlag
              this.itemNode.data.endText = this.formModel.endText
              this.itemPropInfo = null
            } else {
              this.$message({
                message: this.$t('workflowDesign.submissionOfDocumentsHint'),
                type: 'warning'
              })
              return false
            }
          })
          break

        default:
          break
      }
    },
    /**
     * 节点更多属性中，节点操作的编辑
     * propName String 待编辑字段的属性名称
     */
    nodeOperationEdit(propName) {
      // commit 提交、 storage 暂停、 commitPrint 提交打印、 back 回退、 transfer 转交、 end 结束流程
      this.itemPropInfo = propName
      // 同时对formModel 赋值回填
      this.formModel = {
        ...this.itemNode.data,
        storageFlag: +this.itemNode.data.storageFlag, // 是否开启暂存（1：开启；0：不开启）
        commitPrintFlag: +this.itemNode.data.commitPrintFlag, // 是否开启提交并打印（1：开启；0：不开启）
        backFlag: +this.itemNode.data.backFlag, // 是否开启暂存（1：开启；0：不开启）
        transferFlag: +this.itemNode.data.transferFlag // 是否开启转交（1：开启；0：不开启）
      }
    },
    /**
     * 流程图被选中的节点回调
     */
    flowSelect(data) {
      this.itemPropInfo = null
      this.attrType = this.$t('workflowDesign.basicAttribute')
      // data 为空表示当前无选中目标
      if (data) {
        const type = data.type + ''
        // 节点类型，1.开始节点，2.结束节点，3.流程节点，4.抄送节点 line.连接
        if (type === 'line') {
          this.itemNode = null
          this.itemConnect = data
          this.activeName = 'node'
        } else {
          this.itemConnect = null
          this.itemNode = data
          this.activeName = 'node'
        }
      } else {
        this.itemNode = null
        this.itemConnect = null
        this.activeName = 'flow'
      }
      // console.log(data)
    },
    /**
     * 节点名称修改
     */
    nodeNameChange(data) {
      const names = [
        '',
        this.$t('workflowDesign.processStartNode'),
        this.$t('workflowDesign.processEndNode'),
        this.$t('workflowDesign.processNode'),
        this.$t('workflowDesign.cCNode')
      ]
      if (data.name === '') {
        data.name = names[data.type]
      }
    },
    /**
     * 开启流程点击
     */
    openFlowClick() {
      this.openFlow().then(() => {
        this.$store.commit('SET_FLOW_HASCHANGENOSAVE', true)
      })
    },
    /**
     * 开启流程
     */
    openFlow() {
      return new Promise((resolve, reject) => {
        processOpen({
          distributorMark: this.$route.query.distributorMark, // 经销商标识（流程设定页面已经由模板上按钮（编辑、查看等按钮）传到路由，跟原来逻辑是一样的）
          menuId: this.$route.query.menuId, // 菜单id
          appId: this.$route.query.appId, // 应用id
          formId: this.$route.query.formId, // 表单模板id 必传
          saasMark: this.$route.query.saasMark,
          businessType: this.$route.query.businessType, // 区分SaaS和PaaS流程 PaaS流程此字段为空
          type: 2
        })
          .then((res) => {
            this.$message({
              showClose: true,
              message: res.msg,
              type: 'success'
            })

            this.$store.dispatch('getFormInfo', {
              appId: this.$route.query.appId, // 应用的id 必传
              menuId: this.$route.query.menuId, // 表单模板id 必传
              formId: this.$route.query.formId, // 表单模板id 必传
              saasMark: this.$route.query.saasMark,
              businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
            })

            resolve()
          })
          .catch(() => {})
      })
    },
    /**
     * 选中流程版本类型
     */
    selectFlowVersion(type) {
      this.flowVersionType = type // 获取当前版本类型
    },
    @xbb.debounceWrap()
    timer_saveVersion() {
      return this.saveVersion()
    },
    /**
     * 保存设计版本
     */
    saveVersion() {
      const processList = this.$refs['flowDesign'].formatData()
      const mapProcessList = processList.map((item) => {
        if (item.type === 5) {
          const data = item.data
          const conditions = data.conditions || []
          const filterConditions = conditions.filter(({ value, symbol }) => {
            return (
              /^(empty|noempty)$/.test(symbol) ||
              (value && value.length && (value[0] === 0 ? true : value[0]))
            )
          })
          return {
            ...item,
            data: {
              ...data,
              conditions: filterConditions
            }
          }
        }
        return item
      })
      this.saveLoading = true
      processSave({
        type: 2,
        saasMark: this.$route.query.saasMark,
        businessType: this.$route.query.businessType, // 区分SaaS和PaaS流程 PaaS流程此字段为空
        processTemplate: this.flowInfo.processTemplate,
        appId: this.flowInfo.appId,
        formId: this.flowInfo.formId,
        menuId: this.flowInfo.processTemplate.menuId, // 菜单id
        nodeList: [],
        conditionList: [],
        newProcessNodeList: mapProcessList.map((item) => {
          const obj = JSON.parse(JSON.stringify(item))
          delete obj.childNode
          delete obj.conditionNodes
          return obj
        })
      })
        .then((res) => {
          this.itemConnect = null
          this.itemNode = null
          this.$message({
            type: 'success',
            message: this.$t('message.saveSuccess')
          })
          this.getFlowInfo()
        })
        .catch(() => {})
        .finally(() => {
          this.saveLoading = false
        })
    },
    /**
     * 删除流程版本
     */
    delFlowVersion(id) {
      this.$confirm(this.$t('workflowDesign.deleteHint'), this.$t('message.confirmTitle'), {
        // showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        distinguishCancelAndClose: true,
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(() => {
          processDelete({
            appId: this.$route.query.appId, // 应用id
            menuId: this.$route.query.menuId, // 菜单id
            formId: this.$route.query.formId, // 表单模板id
            processTemplateId: id, // 流程模板id 必传
            saasMark: this.$route.query.saasMark,
            businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
          })
            .then((res) => {
              if (res.code === 1) {
                this.$store
                  .dispatch('getFormInfo', {
                    appId: this.$route.query.appId, // 应用的id 必传
                    menuId: this.$route.query.menuId, // 表单模板id 必传
                    formId: this.flowInfo.formId, // 表单模板id
                    processTemplateId: this.flowInfo.processTemplate.id, // 流程模板id 必传
                    saasMark: this.$route.query.saasMark,
                    businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
                  })
                  .then((res) => {
                    this.$message({
                      type: 'success',
                      message: this.$t('message.delSuccess')
                    })
                    this.flowInit()
                  })
              }
            })
            .catch(() => {})
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: this.$t('workflowDesign.cancelled')
          })
        })
    },
    /**
     * 添加流程版本
     */
    addFlowVersion(id) {
      if (this.flowList.length >= 50) {
        this.$message({
          showClose: true,
          message: this.$t('workflowDesign.versionsHint'),
          type: 'warning'
        })
        this.addLoading = true
        return
      }
      addFlow({
        processTemplateId: id,
        menuId: this.$route.query.menuId, // 菜单id
        appId: this.$route.query.appId, // 应用id
        formId: this.$route.query.formId, // 表单模板id 必传
        saasMark: this.$route.query.saasMark,
        businessType: this.$route.query.businessType, // 区分SaaS和PaaS流程 PaaS流程此字段为空
        type: 2
      }).then((res) => {
        if (this.formInfo && this.formInfo.formAttr && this.formInfo.formAttr.isProcessForm === 1) {
          getFlowList({
            menuId: this.$route.query.menuId, // 菜单id
            appId: this.$route.query.appId, // 应用id
            formId: this.$route.query.formId, // 表单模板id 必传
            saasMark: this.$route.query.saasMark,
            businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
          })
            .then(({ result: { processVersionPojoList } }) => {
              this.flowList = []
              this.flowList = processVersionPojoList
              if (processVersionPojoList.length) {
                this.currentFlowId =
                  processVersionPojoList[processVersionPojoList.length - 1].processTemplateId
                this.flowVersionType =
                  processVersionPojoList[processVersionPojoList.length - 1].versionType
              }
              this.addLoading = true
            })
            .catch(() => {})
        }
      })
    },
    /**
     * 获取流程列表
     */
    flowInit() {
      if (this.formInfo && this.formInfo.formAttr && this.formInfo.formAttr.isProcessForm === 1) {
        getFlowList({
          menuId: this.$route.query.menuId, // 菜单id
          appId: this.$route.query.appId, // 应用id
          formId: this.$route.query.formId, // 表单模板id 必传
          saasMark: this.$route.query.saasMark,
          businessType: this.$route.query.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
        })
          .then(({ result: { processVersionPojoList } }) => {
            this.flowList = []
            this.flowList = processVersionPojoList
            if (processVersionPojoList.length) {
              if (!this.isPause) {
                this.currentFlowId = processVersionPojoList[0].processTemplateId
              }
              processVersionPojoList.forEach((item) => {
                if (item.processTemplateId === this.currentFlowId) {
                  this.flowVersionType = item.versionType // 当前id对应的versionType
                }
              })
            }
            this.isPause = false // 暂停标志init
            this.getFlowInfo()
          })
          .catch(() => {})
      }
    },
    /**
     * 获取流程详细
     */
    getFlowInfo() {
      if (this.formInfo && this.formInfo.formAttr && this.formInfo.formAttr.isProcessForm === 1) {
        getFlowInfo({
          processTemplateId: this.currentFlowId, // 当前流程id
          menuId: this.$route.query.menuId, // 菜单id
          appId: this.$route.query.appId, // 应用id
          formId: this.$route.query.formId, // 表单模板id 必传
          saasMark: this.$route.query.saasMark,
          businessType: this.$route.query.businessType, // 区分SaaS和PaaS流程 PaaS流程此字段为空
          type: 2,
          versionType: this.flowVersionType
        })
          .then(({ result, result: { processTree } }) => {
            this.flowInfo = result
            this.flowInfoInit = JSON.parse(JSON.stringify(result.processTemplate))
            this.processTree = processTree
            this.itemNode = null // 当前节点
            this.itemConnect = null // 当前连接
            // 初始化
            this.$store.commit('SET_EDIT_CHANGE_FLOW', false)
            // this.$store.commit('SET_EDIT_CHANGE_FLOW_SAVE', false)
            this.flowInfoInit = JSON.parse(JSON.stringify(result.processTemplate))
          })
          .catch(() => {})
      } else {
        return false
      }
    },

    /**
     * 改变流程版本
     */
    currentFlowIdChange(id) {
      this.getFlowInfo()
    }
  },
  watch: {
    isProcessForm: {
      handler(val, oldVal) {
        if (val === 1) {
          this.flowInit()
        }
      },
      immediate: true
    },
    /**
     * 当当前编辑的节点被删除时，tab切换
     */
    'itemNode.data.isDelete': {
      handler(val) {
        if (val) {
          this.activeName = 'flow'
        }
      }
    },
    'itemConnect.data.isDelete': {
      handler(val) {
        if (val) {
          this.activeName = 'flow'
        }
      }
    },
    'itemConnect.data.conditions': [
      {
        handler(val) {
          if (val) {
            const tempCondition = []
            val.forEach((item) => {
              item.whereList = this.allConditionMap[item.fieldType]
              if (item.subAttr) {
                tempCondition.push(item.attr + '.' + item.subAttr)
              } else {
                tempCondition.push(item.attr)
              }
            })
            if (tempCondition.length) {
              this.formInfoFieldList.forEach((item, index) => {
                if (item.attrType === 'subForm') {
                  item.subForm.items.forEach((sub) => {
                    if (tempCondition.includes(item.attr + '.' + sub.attr)) {
                      this.$set(sub, 'isDisable', true)
                    } else {
                      this.$set(sub, 'isDisable', false)
                    }
                  })
                } else {
                  if (tempCondition.includes(item.attr)) {
                    this.$set(item, 'isDisable', true)
                  } else {
                    this.$set(item, 'isDisable', false)
                  }
                }
              })
            }
          }
        }
      },
      {
        handler(val, oldVal) {
          if (val === oldVal) {
            this.$store.commit('SET_FLOW_HASCHANGENOSAVE', true)
          }
        },
        deep: true
      }
    ],
    'flowInfo.processTemplate': {
      handler(val, oldVal) {
        if (oldVal) {
          // 流程属性
          const flag1 = JSON.stringify(val)
          const flag2 = JSON.stringify(this.flowInfoInit)
          if (flag1 === flag2) {
            this.$store.commit('SET_EDIT_CHANGE_FLOW', false)
          } else {
            this.$store.commit('SET_EDIT_CHANGE_FLOW', true)
          }
        }
        if (val === oldVal) {
          this.$store.commit('SET_FLOW_HASCHANGENOSAVE', true)
        }
      },
      deep: true
    },

    flowVersionType(val) {
      if (val === 1 || val === 3) {
        this.saveBtn = true
      } else {
        this.saveBtn = false
      }
    }
  },
  created() {
    this.init()
    this.$store.commit('SET_FLOW_SETTING', this)
  },
  beforeRouteEnter(to, from, next) {
    if (to.query && to.query.appId && (to.query.menuId || to.query.menuId === 0)) {
      const promise1 = getProcessFormFields({
        ...to.query
        // 'appId': this.$route.query.appId,
        // 'menuId': this.$route.query.menuId,
        // 'formId': this.$route.query.formId,
      })
      // .then(({ result: { explainList } }) => {
      //   this.explainList = explainList
      // })
      const promise2 = store.dispatch('getFormInfo', {
        ...to.query
      })
      // .then(({ result: {formAttr} }) => {
      //   next()
      // })
      Promise.all([promise1, promise2]).then(([res1, res2]) => {
        const {
          result: { explainList }
        } = res1
        next((vm) => {
          vm.explainList = explainList
        })
      })
    } else {
      // next({ path: '/' })
    }
  }
}
</script>

<style lang="scss">
.condition-dropdown-menu {
  max-height: 250px;
  overflow-y: scroll;
}
.workflow-design-template {
  position: relative;
  display: flex;
  flex-flow: row;
  width: 100%;
  height: calc(100%);
  overflow: hidden;
  background-color: white;
  .open-flow-btn {
    position: absolute;
    left: 50%;
    box-sizing: border-box;
    width: 120px;
    height: 36px;
    padding: 0px;
    margin-top: 40px;
    margin-left: -60px;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    border-radius: 2px;
  }
  .open-flow-p {
    position: absolute;
    top: 92px;
    left: 0px;
    width: 100%;
    font-size: 12px;
    color: $text-auxiliary;
    text-align: center;
  }
  .top-tool {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 45px;
    line-height: 44px;
    // background-color: $neutral-color-1;
    border-bottom: 1px solid $neutral-color-3;
    .top-tool__opration {
      width: 240px;
      height: 45px;
      text-align: right;
      & > .el-button {
        margin-right: 12px;
        margin-left: 0px;
      }
    }
  }
  .flow-chart-panel {
    width: calc(100% - 300px);
    //flex: 1;
    height: 100%;
  }
  .right-panel {
    box-sizing: border-box;
    flex-shrink: 0;
    width: 300px;
    height: calc(100%);
    border-left: 1px solid $neutral-color-3;
    .el-tabs {
      height: calc(100%);
      .el-tabs__content {
        .el-scrollbar__wrap {
          overflow-x: hidden;
        }
        height: calc(100% - 43px);
        .el-tab-pane {
          height: 100%;
          .connect-condition {
            height: calc(100% - 150px);
            .condition-block {
              height: calc(100% - 31px);
              .el-scrollbar__wrap {
                overflow-x: hidden;
              }
            }
          }
        }
      }
    }
    .node-prop-bolck {
      position: relative;
      box-sizing: border-box;
      padding: 0px 10px;
      .transferUser-block {
        box-sizing: border-box;
        height: 80px;
        margin-bottom: 20px;
        cursor: pointer;
        border: 1px dashed $neutral-color-3;
        .has-user {
          height: 100%;
          .el-scrollbar__wrap {
            overflow-x: hidden;
          }
        }
        .un-has-user {
          height: 100%;
          line-height: 80px;
          color: #989898;
          text-align: center;
        }
      }
      .toggle-button {
        position: absolute;
        top: 50px;
        right: 20px;
      }
      .top-btn {
        & > .el-button {
          width: 100%;
        }
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid $neutral-color-3;
      }
      .text-bolder {
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 700;
      }
      .text-no-bolder {
        margin-bottom: 10px;
        font-size: 14px;
        color: $text-auxiliary;
      }
    }
    .no-select-tips {
      display: block;
      padding-top: 100px;
      font-size: 24px;
      color: red;
      color: #989898;
      text-align: center;
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
    }
    .el-tabs__item {
      width: 149.5px;
      height: 44px;
      font-size: 14px;
      line-height: 44px;
      text-align: center;
    }
    .attr-setting {
      position: relative;
      z-index: 11;
      width: 100%;
      width: calc(100% - 20px);
      margin: 0px 10px 20px 10px;
      //padding-bottom: 5px;
      .el-radio-button__inner {
        width: 136.5px;
      }
    }
    .connect-condition {
      width: calc(100% - 20px);
      margin: 0px 10px 20px 10px;
    }
    .flow-block {
      position: relative;
      box-sizing: border-box;
      width: calc(100% - 20px);
      padding-bottom: 15px;
      margin: 0px 10px 20px 10px;
      color: $text-auxiliary;
      border-bottom: 1px solid $neutral-color-3;
      .start-permission {
        position: absolute;
        top: -2px;
        right: 10px;
      }
      .start-permission_alert {
        padding: 8px 0;
        margin-bottom: 7px;
      }
      .text-overflow {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      //节点操作的div块
      .node-set {
        position: relative;
        display: flex;
        height: 30px;
        margin-top: 10px;
        line-height: 30px;
        border: 1px solid $neutral-color-3;
        .node-ellipsis {
          max-width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .node-set-name {
          margin-left: 10px;
          color: $text-main;
        }
        .node-set-isset {
          margin-left: 5px;
          color: $brand-color-5;
        }
        .node-set-noset {
          margin-left: 5px;
        }
        & > a {
          position: absolute;
          top: 0px;
          right: 10px;
          color: $brand-color-5;
        }
      }
      .attr-select {
        width: 100%;
        margin-top: 5px;
      }
      .attr-opinion {
        position: absolute;
        top: 38px;
        left: 50px;
        color: $brand-color-5;
      }
      .flow-block-title {
        padding-bottom: 5px;
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: bolder;
        color: $text-main;
      }
      .input-box {
        width: 100%;
        height: 30px;
        margin-top: 10px;
        cursor: pointer;
        border: 1px solid $neutral-color-3;
      }
      .users {
        height: 80px;
        margin: 10px 0px;
        cursor: pointer;
        border: 1px dashed $neutral-color-3;
        .el-scrollbar__wrap {
          overflow-x: hidden;
        }
        & > .no {
          height: 100%;
          font-size: 13px;
          line-height: 80px;
          color: $text-auxiliary;
          text-align: center;
        }
        & > .has {
          height: 100%;
        }
      }
      .no-field {
        display: flex;
        flex-wrap: wrap;
        align-content: center;
        align-items: center;
        justify-content: center;
        height: 100px;
        margin-top: 10px;
        text-align: center;
        border: 1px dashed $neutral-color-3;
        & > p {
          width: 100%;
          margin-bottom: 5px;
        }
      }
    }
  }
}
</style>
