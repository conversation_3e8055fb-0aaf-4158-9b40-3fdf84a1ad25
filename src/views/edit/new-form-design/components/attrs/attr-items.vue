<!-- eslint vue/return-in-computed-property: 1 -->

<!--
 * @Description: 选项属性--组件（单选-复选-下拉单选-下拉复选）
 -->
<template>
  <div v-if="!forbiddenSettingMap['items']" class="attr-items">
    <!-- :title="fieldInfo.attrName || '选项'" -->
    <SettingBlock :title="$t('formDesign.option')">
      <template slot="title">
        {{ $t('formDesign.option') }}
        <i
          class="icon-question-line t-iconfont"
          style="color: #969799; cursor: pointer"
          @click="exampleDialogVisible = true"
        ></i>
      </template>
      <!-- 父字段开启了数据联动 -->
      <template v-if="subFormIsRely">
        <div class="selectItem-disable">
          {{ $t('formDesign.bySubForm') }}
        </div>
      </template>
      <template v-else>
        <!-- 只有下拉框和下拉复选框才开放次功能 -->
        <el-select
          v-if="/^(3|10001)$/.test(fieldInfo.fieldType) && !forbiddenSettingMap.selfDefine"
          v-model="fieldInfo.comboType"
          style="width: 100%; margin-bottom: 10px"
          @change="itemTypeChange"
        >
          <el-option
            v-for="item in computedDefaultTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
        <!-- 选项 - 自定义 -->
        <template v-if="fieldInfo.comboType === 0">
          <el-alert
            v-if="!forbiddenSettingMap.selfDefine"
            class="mg-b-10"
            show-icon
            :title="$t('formDesign.modifyHint5')"
            type="warning"
          >
          </el-alert>
          <div class="attr-items--item">
            <el-radio v-model="noChecked" :disabled="!!itemBanDefault" :label="true">
              {{ $t('formDesign.doNotSetDefault') }}
            </el-radio>
          </div>
          <VueDraggable v-model="fieldInfo.items" handle=".move" v-bind="{ animation: 150 }">
            <template v-for="(item, index) in fieldInfo.items">
              <div
                v-if="!item.isOther && !item.editHide"
                :key="item.value"
                class="attr-items--item"
              >
                <!-- 单选 + 下拉单选 -->
                <el-radio
                  v-if="/^(10000|3)$/.test(fieldInfo.fieldType)"
                  v-model="item.checked"
                  :disabled="radioDisabled(item)"
                  :label="true"
                  @change="radioChange(item, index)"
                >
                  {{ '' }}
                </el-radio>
                <!-- 复选 + 下拉复选 -->
                <el-checkbox
                  v-else
                  v-model="item.checked"
                  :disabled="checkboxDisabled(item)"
                  :label="true"
                >
                  {{ '' }}
                </el-checkbox>
                <el-input
                  v-model="item.text"
                  :disabled="fieldInfo.saasAttr === 'taskStatus'"
                  :maxlength="item.maxLength || 100"
                  :minlength="1"
                  :placeholder="
                    $t('rule.lessThanFie', { attr: $t('nouns.option'), num: item.maxLength || 100 })
                  "
                  :readonly="!!item.noEdit"
                >
                  <template
                    v-if="!['unit', 'productUnit', 'muladdresstype'].includes(fieldInfo.saasAttr)"
                    slot="suffix"
                  >
                    <ColorSelect
                      v-model="item.color"
                      :custom-colors.sync="customColorsList"
                      :input-style="{ width: '32px', minWidth: '32px', marginRight: '-5px' }"
                      :max-custom-color-count="6"
                      :read-only="Boolean(item.noColor)"
                      :show-delete="false"
                      @handleShowColorSelect="handleShowColorSelect(item.color)"
                    ></ColorSelect>
                  </template>
                </el-input>
                <span
                  v-if="!/^(saleStage|customerStage|clueStage)$/.test(fieldInfo.saasAttr)"
                  v-draggable
                  :class="item.noMove ? 'immoveable' : 'move'"
                  :title="$t('formDesign.sort')"
                  ><i class="el-icon-rank"></i
                ></span>
                <!-- <el-button size="mini"  circle  @click="deleteItems(index)" type="danger">
                    <i title="删除" class="el-icon-remove" style="color: red;font-size: 22px;cursor: pointer"></i>
                  </el-button> -->
                <el-tooltip
                  :content="item.isVisible ? '新建编辑数据时显示' : '新建编辑数据时隐藏'"
                  effect="dark"
                  placement="top"
                >
                  <el-button class="item_eye" type="text" @click="changVisible(index)">
                    <i
                      class="web-iconfont"
                      :class="item.isVisible ? 'web-icon-eye' : 'web-icon-eye-close '"
                    ></i
                  ></el-button>
                </el-tooltip>
                <el-button
                  class="item--delete"
                  :disabled="!canAddItem || !!item.noDel"
                  type="text"
                  @click="deleteItems(index)"
                  ><i class="el-icon-delete"></i
                ></el-button>
                <!-- <i title="删除" class="el-icon-remove" style="color: red;font-size: 22px;cursor: pointer" @click="deleteItems(index)"></i> -->
              </div>
            </template>
          </VueDraggable>
          <div v-if="lastItem && lastItem.isOther" class="attr-items--item">
            <el-radio
              v-if="/^(10000|3)$/.test(fieldInfo.fieldType)"
              v-model="fieldInfo.items[fieldInfo.items.length - 1].checked"
              :label="true"
              @change.native="radioChange(fieldInfo.items[fieldInfo.items.length - 1])"
              >{{ '' }}</el-radio
            >
            <el-checkbox
              v-else
              v-model="fieldInfo.items[fieldInfo.items.length - 1].checked"
              :label="true"
              >{{ '' }}</el-checkbox
            >
            <el-input
              v-model="fieldInfo.items[fieldInfo.items.length - 1].text"
              class="form-input"
              :disabled="true"
              style="margin-right: 7px; margin-bottom: 5px"
              @change="itemChange(fieldInfo.items[fieldInfo.items.length - 1])"
            >
              <template slot="suffix">
                <ColorSelect
                  v-model="fieldInfo.items[fieldInfo.items.length - 1].color"
                  :custom-colors.sync="customColorsList"
                  :input-style="{ width: '32px', minWidth: '32px', marginRight: '-5px' }"
                  :max-custom-color-count="6"
                  :show-delete="false"
                  @handleShowColorSelect="
                    handleShowColorSelect(fieldInfo.items[fieldInfo.items.length - 1].color)
                  "
                ></ColorSelect>
              </template>
            </el-input>
            <span class="move" style="visibility: hidden">
              <svg-icon icon-class="yidong_mian" style="line-height: 40px"></svg-icon
            ></span>
            <span class="move" style="visibility: hidden"
              ><svg-icon icon-class="yidong_mian" style="line-height: 40px"></svg-icon
            ></span>
            <i
              class="el-icon-delete"
              style="font-size: 16px; color: #969799; cursor: pointer"
              @click="deleteItems(fieldInfo.items.length - 1)"
            ></i>
          </div>
          <div v-if="canAddItem" class="attr-items__btns">
            <template v-if="/^(10000|3|9|10001)$/.test(fieldInfo.fieldType)">
              <span @click="addItems">{{ $t('formDesign.addOptionValue') }}</span>
              |
              <span
                v-if="isShowItemOther"
                :class="{ 'add-item--disabled': isItemHasOther() }"
                @click="addOtherItems"
              >
                {{ $t('formDesign.addOtherItems') }}
              </span>
            </template>
            <template v-else>
              <span style="text-align: center" @click="addItems">{{
                $t('formDesign.addOptionValue')
              }}</span>
            </template>
            <span @click="batchEdit">{{ $t('form.batchAdd') }}</span>
          </div>
          <!-- 子表单内的字段不能选项关联 -->
          <template
            v-if="
              !fieldInfo.parentAttr && fieldInfo.noItemLink !== 1 && !forbiddenSettingMap.optionRely
            "
          >
            <LineBlock style="margin-top: 10px" />
            <el-button style="width: 100%; margin-top: 10px" @click="associatedButton">{{
              $t('formDesign.optionAssociationSettings')
            }}</el-button>
          </template>
          <!-- 多单位设置 -->
          <template v-if="enableMultiUnit && !enableMultiUnit.editHide">
            <LineBlock style="margin-top: 10px" />
            <el-button style="width: 100%; margin-top: 10px" @click="openUnitSet">{{
              $t('multiUnit.setMultiUnit')
            }}</el-button>
          </template>

          <template v-if="fieldInfo.isDictionary">
            <LineBlock style="margin-top: 10px" />
            <el-button style="width: 100%; margin-top: 10px" @click="openSettingDialog"
              >{{ fieldInfo.attrName + $t('operation.set') }}
            </el-button>
          </template>
        </template>
        <!-- 选项 - 关联其他表单数据 -->
        <!-- 子表单中进行单独处理 -todo -->
        <template v-else-if="fieldInfo.comboType === 1">
          <!-- <select-single-tree
            v-model="fieldInfo.linkForm"
            :options="linkFormList"
            :props="defaultProps">
          </select-single-tree> -->
          <FormSelectTree
            v-model="fieldInfo.linkForm"
            :app-id="$route.query.appId"
            :form-id="$route.query.formId"
            :menu-id="$route.query.menuId"
          >
          </FormSelectTree>
        </template>
        <!-- 选项 - 数据联动 -->
        <template v-else-if="fieldInfo.comboType === 2">
          <template>
            <el-button style="width: 100%" @click="dataRely">{{
              $t('formDesign.dataLinkageSettings')
            }}</el-button>
          </template>
        </template>
        <!-- 选项 - 工商查询 -->
        <template v-else-if="fieldInfo.comboType === 3 || fieldInfo.comboType === 4">
          <el-select v-model="businessSearch" @change="businessSearchData">
            <el-option
              v-for="item in businessSearchItem"
              :key="item.attr"
              :label="item.attrName"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </template>
      </template>
    </SettingBlock>
    <LineBlock />

    <!-- 数据联动弹窗 -->
    <dataRelyDialog
      v-if="dataRelyDialogActive"
      ref="dataRely"
      :attr-name="fieldInfo.attrName"
      :current-attr="fieldInfo.attr"
      :field-info="fieldInfo"
      :field-type="fieldInfo.fieldType"
      :layout-model="layoutModel"
      :rely="fieldInfo.defaultAttr.rely"
      :show.sync="dataRelyDialogActive"
      @dataRelyDialogSubmit="dataRelyDialogSubmit"
      @dataRelyDialogSubmitMulti="dataRelyDialogSubmitMulti"
      @dialogConfirmCancel="dataRelyDialogActive = false"
      @relyMultiTemplatedataDelect="relyMultiTemplatedataDelect"
    />
    <!-- 选项关联字段设置 -->
    <radio-group-associated-dialog
      ref="associatedDialog"
      :all-fields="layoutModel"
      :field-list="fieldList"
      :has-field-map="hasFieldMap"
      :is-active.sync="radioGroupAssociatedDialogActive"
      :radio-list="fieldInfo.items"
      @dialogConfirmCancel="radioGroupAssociatedDialogActive = false"
      @dialogConfirmSubmit="associatedDialogSubmit"
    />

    <!-- 批量添加选项 -->
    <batch-edit
      ref="dialogBatchEdit"
      :is-active="dialogBatchEditActive"
      @dialogConfirmCancel="dialogBatchEditActive = false"
      @dialogSubmit="dialogSubmit"
    />
    <!-- :widget-detail-items="fieldInfo.items" -->

    <!-- 客户阶段设置 -->
    <CustomerStageDialog
      v-if="customerStageDialogShow"
      :show.sync="customerStageDialogShow"
      @customerStageClose="customerStateOrStageClose"
    />
    <!-- 线索阶段设置 -->
    <ClueStageDialog
      v-if="clueStageDialogShow"
      :show.sync="clueStageDialogShow"
      @customerStageClose="customerStateOrStageClose"
    />
    <!-- 销售阶段设置 -->
    <SaleStageEditDialog
      v-if="saleStageDialogShow"
      :show.sync="saleStageDialogShow"
      @saleStageClose="saleStageClose"
    />
    <!-- 客户状态合同状态设置 -->
    <DataEditDialog
      v-if="dataEditDialogShow"
      :field-info="fieldInfo"
      :show.sync="dataEditDialogShow"
      @customerStateClose="dataEditDialogClose"
    />
    <!-- 多单位设置弹窗 -->
    <mutilUnitDialog
      v-if="mutilUnitDialogShow"
      :show.sync="mutilUnitDialogShow"
      @mutilDialogClose="mutilDialogClose"
    ></mutilUnitDialog>
    <!-- 选项示例弹窗 -->
    <custom-dialog
      ref="dialog"
      class="example-dialog"
      :show.sync="exampleDialogVisible"
      :show-footer="false"
      :title="$t('formDesign.optionToHideImpact')"
      width="800px"
    >
      <template #content>
        <div class="prompt-dialog-content">
          <div class="font-600 prompt-dialog-content_title">
            {{ $t('formDesign.impactRangeTitle') }}
          </div>
          <ul v-html="$t('formDesign.impactRangeContent')"></ul>
        </div>
        <div class="prompt-dialog-content">
          <div class="prompt-dialog-content_title">{{ $t('formDesign.editScene') }}</div>
          <ul v-html="$t('formDesign.editSceneContent')"></ul>
        </div>
        <div class="prompt-dialog-content">
          <div class="prompt-dialog-content_title">{{ $t('formDesign.newScene') }}</div>
          <ul v-html="$t('formDesign.newSceneContent')"></ul>
        </div>
      </template>
    </custom-dialog>
  </div>
</template>

<script>
/* eslint vue/return-in-computed-property: 1 */

import mixin from './mixin'
// import bus from '@/utils/temp-vue'
// import { guid } from '@/utils/uuid'
import xbb from '@xbb/xbb-utils'
import { businessFieldList, fineClueFieldList } from '@/api/formData'
import commonBusinessType from '@/constants/common/business-type'
import ColorSelect from '@/components/common/color-popover'
import { textPlain } from '@/styles/setting-variable.scss'
import CustomDialog from '@/components/common/custom-dialog.vue'
// import { getFormAttr } from '@/api/form'
export default {
  name: 'AttrItems',
  components: {
    ColorSelect: ColorSelect,
    CustomDialog
  },
  mixins: [mixin],

  inject: ['isBusiness', 'businessType', 'formDesign'],
  data() {
    return {
      dialogBatchEditActive: false,
      radioGroupAssociatedDialogActive: false,
      dataRelyDialogActive: false,
      linkFormList: [], // 当前应用下面的所有表单
      // 数据默认字段
      defaultProps: {
        parent: 'parentId',
        value: 'id',
        label: 'name',
        children: 'formExplain'
      },
      // 其他选项
      otherItem: {
        checked: false,
        text: this.$t('formDesign.other'),
        isOther: 1,
        value: ''
      },
      defaultTypeList: [
        { key: 0, value: this.$t('nouns.custom') },
        { key: 1, value: this.$t('formDesign.associateOtherFormData') },
        { key: 2, value: this.$t('formDesign.dataLinkage') },
        { key: 3, value: this.$t('formDesign.businessSearch') }
      ],
      customerStageDialogShow: false,
      clueStageDialogShow: false,
      saleStageDialogShow: false,
      dataEditDialogShow: false,
      mutilUnitDialogShow: false, // 多单位弹窗开关
      businessSearchItem: [],
      businessSearch: '',
      exampleDialogVisible: false, // 选项示例弹窗
      customColorsList: []
    }
  },
  mounted() {
    // 如果items 中有选项默认没有颜色就给他刷成默认文字颜色
    this.fieldInfo.items &&
      this.fieldInfo.items.forEach((item, index) => {
        if (!item.color) {
          this.$set(this.fieldInfo.items[index], 'color', textPlain)
        }
        if (item.isVisible === undefined) {
          this.$set(this.fieldInfo.items[index], 'isVisible', 1)
        }
      })
  },
  computed: {
    itemBanDefault() {
      return !!this.fieldInfo.itemBanDefault
    },
    /**
     * 选项的最后一项
     */
    lastItem() {
      const items = this.fieldInfo.items
      if (items && items.length >= 1) {
        return items[items.length - 1]
      } else {
        return null
      }
    },
    // 是否是字典字段
    isDictionaryField() {
      let res = false
      if (this.isSaas && this.fieldInfo.dictionaryFlag === 1) {
        res = true
      }
      return res
    },
    // 能否添加选项
    canAddItem() {
      let res = false
      if (this.fieldInfo.isRedundant) {
        res = true
        return res
      }
      if (!this.isSaas) {
        res = true
        return res
      }
      if (this.isDictionaryField) {
        res = true
        return res
      }
    },
    noChecked: {
      get() {
        const check = this.fieldInfo.items.some((item) => item.checked)
        return !check
      },
      set(val) {
        if (val) {
          this.fieldInfo.items.forEach((item) => {
            item.checked = false
          })
        }
      }
    },
    /**
     * 获取套餐信息
     */
    isProfessionalEdition() {
      return this.$store.getters.isProfessionalEdition
    },
    /**
     * 过滤一些选项类型下拉
     */
    computedDefaultTypeList() {
      let res = this.defaultTypeList
      // 是否是竞争对手分析
      const isCompetitorAnalysis =
        this.formDesign.subBusinessType &&
        this.formDesign.subBusinessType === commonBusinessType.CUSTOMER_COMPETITOR_ANALYSIS
      // 如果是系统字段（非自定义字段）
      if (this.fieldInfo.isRedundant !== 1 || isCompetitorAnalysis) {
        res = [{ key: 0, value: this.$t('nouns.custom') }]
      }
      // isProfessionalEdition // 0 （标准版阉割） 1 (高级版阉割)
      if (this.isProfessionalEdition === 0) {
        res = res.filter((item) => item.key !== 1 && item.key !== 2) // 1 关联其他表单数据 2 数据联动
      }
      // 判断businessType
      if (this.isBusiness !== 1 || this.fieldInfo.isRedundant === 0) {
        res = res.filter((item) => item.key !== 3)
      }
      // 当子表单默认值不为工商查询时，里面的元素默认值不能设置为工商查询
      if (this.isSub === true && this.subFormInfo.defaultAttr.defaultType !== 'business') {
        res = res.filter((item) => item.key !== 3)
      }
      return res
    },
    fieldList() {
      const itemIndex = this.layoutModel.indexOf(this.fieldInfo)
      return this.layoutModel.filter((item, index) => {
        return index > itemIndex
      })
    },
    queryBusinessType() {
      return +this.$route.query.businessType
    },
    // isEmpty () {
    //   return this.fieldInfo.items.some(item => item.text === '')
    // }
    // 存在关联字段
    hasFieldMap() {
      return (
        this.fieldInfo.items &&
        this.fieldInfo.items.some((e) => {
          return xbb._get(e, 'fieldMap.length', 0) !== 0
        })
      )
    },
    isShowItemOther() {
      return !(this.fieldInfo.saasAttr === 'status' && this.businessType === 20300)
    },
    radioDisabled() {
      return (item) => {
        return !!this.itemBanDefault || !item.isVisible
      }
    },
    checkboxDisabled() {
      return (item) => {
        return !item.isVisible
      }
    }
  },

  watch: {
    'fieldInfo.comboType': {
      immediate: true,
      handler(val) {
        if (val === 1) {
          this.getFormAttr()
        }
      }
    }
  },

  created() {
    this.itemTypeChange()
  },

  methods: {
    init() {},
    itemTypeChange() {
      this.changeDefaultOrItemType()
      console.log('last', this.subFormInfo)
      // 前面（）判断代表工商查询  后端代表精线索

      if (
        (this.fieldInfo.defaultAttr && this.fieldInfo.comboType === 3 && this.isBusiness === 1) ||
        this.formDesign.formInfo.isFineClue === 1
      ) {
        this.getBusinessFieldList()
        if (xbb._get(this.fieldInfo, 'defaultAttr.businessMatchAttr', undefined)) {
          this.businessSearch = this.fieldInfo.defaultAttr.businessMatchAttr.attrName
        }
      } else if (
        this.fieldInfo.defaultAttr &&
        this.fieldInfo.comboType === 3 &&
        this.isBusiness === 0
      ) {
        this.fieldInfo.comboType = 0
        delete this.fieldInfo.defaultAttr.businessMatchAttr
      } else if (
        this.fieldInfo.defaultAttr &&
        this.fieldInfo.comboType !== 3 &&
        this.fieldInfo.defaultAttr.businessMatchAttr
      ) {
        delete this.fieldInfo.defaultAttr.businessMatchAttr
        this.businessSearch = ''
      }
    },
    // 获取下拉框的工商查询下拉框数据
    getBusinessFieldList() {
      let needApi = businessFieldList
      if (this.formDesign.formInfo.isFineClue === 1) {
        needApi = fineClueFieldList
      }
      // console.log('我要准备请求接口了！！！')
      if (this.isSub === false) {
        needApi({
          businessType: this.businessType,
          fieldType: this.fieldInfo.fieldType,
          attr: this.fieldInfo.attr
        })
          .then((res) => {
            // console.log('businessFieldListdata:', res)
            this.businessSearchItem = res.result.fieldList
          })
          .catch(() => {})
      } else {
        // 当组件为子表单里面的单行文本时，工商查询里面的数据应该是子表单选中的工商查询数据里面的子数据
        needApi({
          businessType: this.businessType,
          fieldType: this.subFormInfo.fieldType,
          attr: this.subFormInfo.attr
        })
          .then((res) => {
            // console.log('businessFieldListdata:', res)
            const childItem = res.result.fieldList.find(
              (i) => i.attr === this.subFormInfo.defaultAttr.businessMatchAttr.attr
            )
            this.businessSearchItem = childItem.childField
          })
          .catch(() => {})
      }
    },
    // 选择工商查询里面的字段
    businessSearchData(val) {
      const item = this.businessSearchItem.find((i) => i.code === val)
      this.fieldInfo.defaultAttr.businessMatchAttr = item
      this.businessSearch = item.attrName
      if (this.fieldInfo.defaultAttr.formulaInfo) {
        delete this.fieldInfo.defaultAttr.formulaInfo
      } else if (this.fieldInfo.defaultAttr.rely) {
        delete this.fieldInfo.defaultAttr.rely
      }
    },
    // 打开多单位设置弹窗
    openUnitSet() {
      this.mutilUnitDialogShow = true
    },
    mutilDialogClose() {
      // 关闭多单位弹窗
      console.log('关闭多单位弹窗')
      this.mutilUnitDialogShow = false
    },
    saleStageClose(dataList = []) {
      // 以后有必要改成接口

      const filterArr = dataList.filter((item) => item.enable)
      const currentItems = this.fieldInfo.items
      const itemsMap = {}
      currentItems.forEach((item) => {
        const { value } = item
        itemsMap[value] = item
      })

      const newItems = filterArr.map((item) => {
        const { name, value } = item
        const oldItem = itemsMap[value]
        return {
          ...oldItem,
          value: value,
          text: name
        }
      })
      this.fieldInfo.items = newItems
    },
    dataEditDialogClose(dataList = []) {
      const filterArr = dataList.filter((item) => item.enable)
      const currentItems = this.fieldInfo.items
      const itemsMap = {}
      currentItems.forEach((item) => {
        const { value } = item
        itemsMap[value] = item
      })
      const newItems = filterArr.map((item) => {
        const { value, text } = item
        const oldItem = itemsMap[value]
        return {
          ...oldItem,
          value: value,
          text: text
        }
      })
      this.fieldInfo.items = newItems
    },
    customerStateOrStageClose(dataList = []) {
      // 以后有必要改成接口
      const filterArr = dataList.filter((item) => item.enable)
      const currentItems = this.fieldInfo.items
      const itemsMap = {}
      currentItems.forEach((item) => {
        const { value } = item
        itemsMap[value] = item
      })
      const newItems = filterArr.map((item) => {
        // let { value, text } = item
        // let oldItem = itemsMap[value]
        // return {
        //   ...oldItem,
        //   value: value,
        //   text: text
        // }
        const { name, value } = item
        const oldItem = itemsMap[value]
        return {
          ...oldItem,
          value: value,
          text: name
        }
      })
      this.fieldInfo.items = newItems
    },
    radioChange(target, index) {
      this.fieldInfo.items.forEach((item, i) => {
        if (i === index) return false
        this.$set(item, 'checked', false)
      })
      this.$nextTick().then(() => {
        this.$set(target, 'checked', true)
      })
    },
    // 改变选项值显隐
    changVisible(index) {
      console.log('this.fieldInfo.items', this.fieldInfo.items)
      const changeItem = this.fieldInfo.items[index] // 待改变显隐项
      const itemVisible = changeItem.isVisible ? 0 : 1
      this.$set(this.fieldInfo.items[index], 'isVisible', itemVisible)
      if (!itemVisible) {
        this.$set(this.fieldInfo.items[index], 'checked', false)
      }
    },
    // 删除选择项
    deleteItems(index) {
      this.$confirm(this.$t('formDesign.deleteItemsCheck'), this.$t('message.confirmTitle'), {
        confirmButtonText: this.$t('operation.confirm'),
        cancelButtonText: this.$t('operation.cancel'),
        type: 'warning'
      })
        .then(() => {
          const items = this.fieldInfo.items
          const delItem = this.fieldInfo.items[index] // 待删除项
          // 过滤出来非其他选项
          const noOtherItems = items.filter((item) => !item.isOther)
          // 如果要删除的选项不是其他选项 且当前选项小于等于1
          if (!delItem.isOther && noOtherItems.length <= 1) {
            this.$message.error(this.$t('formDesign.modifyHint4'))
            return
          }
          this.fieldInfo.items.splice(index, 1)
        })
        .catch((err) => {
          console.error(err)
        })
    },
    // 添加选择项
    addItems() {
      this.remindlink().then((e) => {
        if (this.isItemHasOther()) {
          this.fieldInfo.items.splice(this.fieldInfo.items.length - 1, 0, {
            text: this.$t('formDesign.optionValue'),
            value: xbb.guid(),
            checked: false,
            color: textPlain,
            isVisible: 1
          })
        } else {
          this.fieldInfo.items.push({
            text: this.$t('formDesign.optionValue'),
            value: xbb.guid(),
            checked: false,
            color: textPlain,
            isVisible: 1
          })
        }
      })
    },
    // 添加其他选择项
    addOtherItems() {
      this.remindlink().then((e) => {
        if (!this.isItemHasOther()) {
          this.fieldInfo.items.push({
            checked: false,
            text: this.$t('formDesign.other'),
            isOther: 1,
            value: '',
            color: textPlain
          })
        }
      })
    },
    // 选项的批量编辑
    batchEdit() {
      this.remindlink().then((e) => {
        this.dialogBatchEditActive = true
        this.$refs.dialogBatchEdit.init()
      })
    },
    isItemHasOther() {
      let isHas = false
      this.fieldInfo.items.forEach((item) => {
        if (item.isOther === 1) {
          isHas = true
        }
      })
      return isHas
    },
    // 选项关联设置
    associatedButton() {
      this.radioGroupAssociatedDialogActive = true
      this.$refs.associatedDialog.init()
    },
    // 数据联动设置
    dataRely() {
      this.dataRelyDialogActive = true
      // this.$refs.dataRely.init()
    },
    // 数据联动确认
    dataRelyDialogSubmit(data) {
      this.fieldInfo.defaultAttr.rely = data
    },
    dataRelyDialogSubmitMulti(data) {
      this.fieldInfo.defaultAttr.relyMultiTemplate = 1
      this.fieldInfo.defaultAttr.relyMulti = data
    },
    relyMultiTemplatedataDelect() {
      this.$delete(this.fieldInfo.defaultAttr, 'relyMultiTemplate')
      this.$delete(this.fieldInfo.defaultAttr, 'relyMulti')
    },
    // 选项关联提交
    associatedDialogSubmit(data) {
      this.fieldInfo.items = data
    },
    getFormAttr() {},
    dialogSubmit(arr) {
      // 批量编辑变为批量添加
      // this.fieldInfo.items = JSON.parse(JSON.stringify(arr))
      if (this.isItemHasOther()) {
        this.fieldInfo.items.splice(
          this.fieldInfo.items.length - 1,
          0,
          ...JSON.parse(JSON.stringify(arr))
        )
      } else {
        this.fieldInfo.items.push(...JSON.parse(JSON.stringify(arr)))
      }
      this.dialogBatchEditActive = false
    },
    // 打开客户阶段和销售阶段设置
    openSettingDialog() {
      switch (this.fieldInfo.saasAttr) {
        case 'customerStage':
          this.customerStageDialogShow = true
          break
        case 'clueStage':
          this.clueStageDialogShow = true
          break
        case 'saleStage':
          this.saleStageDialogShow = true
          break
        case 'wastage':
        case 'delayCause':
        case 'status':
        case 'type':
        case 'expressName':
        case 'clueStatus':
        case 'invalidReason':
          this.dataEditDialogShow = true
          break
        default:
          break
      }
    },
    isOrEmpty() {
      // bus.$emit('isEmpty', this.isEmpty)
    },
    // 提醒新增选项需要关联关联选项
    remindlink() {
      if (this.fieldInfo.showfirst === undefined && this.hasFieldMap) {
        this.fieldInfo.showfirst = false
        return this.$confirm(
          this.$t('formDesign.optionAssociationTip'),
          this.$t('message.confirmTitle'),
          {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            closeOnClickModal: false,
            type: 'warning'
          }
        )
      } else {
        return new Promise((resolve, reject) => {
          resolve()
        })
      }
    },
    handleShowColorSelect(color) {
      if (!this.customColorsList.includes(color)) {
        this.customColorsList.unshift(color)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.attr-items {
  .attr-items--item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    :deep(.el-radio) {
      margin-right: 5px;
    }
    & > .move {
      margin-left: 5px;
      font-size: 18px;
      color: $text-auxiliary;
      cursor: move;
    }
    & > .immoveable {
      margin-left: 5px;
      font-size: 18px;
      color: $text-auxiliary;
      cursor: not-allowed;
    }
    & > .el-button {
      margin-left: 5px;
    }
    & > .item--delete {
      padding: 10px 0 8px 0;
      font-size: 16px;
      color: $text-auxiliary;
      &.is-disabled {
        color: $text-grey;
        &:hover {
          color: $text-grey;
        }
      }
      &:hover {
        color: $error-base-color-6;
      }
    }
    & > .item_eye {
      font-size: 16px;
      color: $text-auxiliary;
      &:hover {
        color: $brand-color-5;
      }
    }
  }
  .attr-items__btns {
    display: flex;
    margin-top: 10px;
    font-size: 14px;
    color: $brand-color-5;
    & > span {
      flex: 1;
      margin: 0 10px;
      cursor: pointer;
    }
    & > span:nth-child(1) {
      flex: 1;
      text-align: right;
    }
    & > span:last-child {
      flex: 1;
      text-align: left;
    }
    // &>span{
    //   display: block;
    //   margin: 0 5px;
    // }
    & > .add-item--disabled {
      color: $text-grey;
      cursor: not-allowed;
    }
    // &>span:last-child{
    //   flex: 1;
    //   text-align: left;
    // }
  }
  .selectItem-disable {
    line-height: 32px;
    text-align: center;
    background: $neutral-color-1;
  }
}
.prompt-dialog-content {
  padding: 8px 12px;
  margin-bottom: 24px;
  font-family: PingFang SC;
  color: $text-plain;
  background: $neutral-color-1;
  border-radius: 4px;
  &_title {
    line-height: 25px;
    color: $text-main;
  }
  &:last-child {
    margin-bottom: 0;
  }
  li {
    line-height: 25px !important;
  }
}
</style>

<style lang="scss">
.prompt-dialog-content {
  li {
    line-height: 25px !important;
  }
  img {
    width: 100%;
  }
}
</style>
