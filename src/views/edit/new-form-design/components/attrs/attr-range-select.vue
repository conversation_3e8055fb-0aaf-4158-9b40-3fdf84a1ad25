<!--
 * @Description: 选者范围--属性-组件
 -->
<template>
  <div v-if="!forbiddenSettingMap['scope']" class="attr-range-select">
    <SettingBlock :title="$t('formDesign.choiceScope')">
      <template>
        <el-select v-model="rangeValue" clearable style="width: 100%">
          <el-option
            v-for="item in rangeTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
        <div v-if="rangeValue === 'custom'" class="attr-range-select__setting">
          <el-button style="width: 100%; margin-bottom: 10px" @click="initFilterConditions">
            {{ $t('operation.set') }}
          </el-button>
        </div>
      </template>
    </SettingBlock>
    <LineBlock />
    <el-dialog :title="$t('formDesign.choiceScope')" :visible.sync="rangeDialogShow" width="900px">
      <span class="attr-range-select__title">
        {{ $t('formDataEdit.rangeSelectTips') }}
      </span>
      <span class="attr-range-select__desc">
        {{ $t('formDataEdit.rangeSelectTipsDesc') }}
      </span>
      <br />
      <span class="attr-range-select__desc">
        {{ $t('formDataEdit.rangeSelectTipsWarn') }}
      </span>
      <el-form ref="rangeSelectFormRef" :model="form">
        <RuleConditions
          :field-list="fieldList"
          :filter-conditions="form.rule.filterConditions"
          :fixed-val="true"
          :hide-title="true"
          :range-select="true"
          :rule-type="1"
          :show-triggler-condition="true"
          :show-value-type="true"
          :target="{}"
          @addCondition="addCondition"
        />
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rangeDialogShow = false">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="saveRange">{{ $t('operation.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixin from './mixin'
import RuleConditions from '@/views/edit/new-form-design/components/business-rules/rule-conditions.vue'
import businessMixin from '@/views/edit/new-form-design/components/business-rules/business-rules'
import { getRangeConditionsAttr } from '@/api/form'
import { FieldTypeEnum } from '@/constants/enum/fieldType'

import xbb from '@xbb/xbb-utils'

const fieldCorrespondence = {
  900050: [10009, 10010, 10013, 10017, 10018, 900053], // 成员姓名: 成员单选、成员多选、创建人、负责人、协同人、当前登录用户姓名
  900051: [10011, 10012, 900054], // 部门名称: 部门单选、部门多选、当前登录用户部门
  900052: [] // 成员角色
}

export default {
  name: 'AttrRangeSelect',

  components: {
    RuleConditions
  },

  mixins: [mixin, businessMixin],

  data() {
    return {
      fieldList: [],
      form: {
        rule: {
          filterConditions: []
        }
      },
      rangeDialogShow: false,
      rangeTypeList: [
        { key: 'all', value: this.$t('formDesign.allScope') },
        { key: 'custom', value: this.$t('nouns.custom') }
      ]
    }
  },

  computed: {
    rangeValue: {
      get() {
        const type = xbb._get(this.fieldInfo, 'limitChooseRange.type', 'all')
        return !type || type === 'optionRange' ? 'all' : type
      },
      set(val) {
        this.$set(
          this.fieldInfo,
          'limitChooseRange',
          this.fieldInfo.limitChooseRange
            ? {
                ...this.fieldInfo.limitChooseRange,
                type: val
              }
            : { type: val }
        )
      }
    }
  },

  mounted() {
    this.init()
  },

  methods: {
    init() {
      this.getFilterList()
    },
    // 获取字段解释
    getFilterList() {
      getRangeConditionsAttr({
        fieldType: this.fieldInfo.fieldType,
        appId: this.$route.query.appId,
        menuId: this.$route.query.menuId,
        formId: this.$route.query.formId
      }).then((res) => {
        this.fieldList = res.result.explainList
      })
    },
    // 初始化默认值
    initFilterConditions() {
      this.form.rule.filterConditions = xbb.deepClone(
        xbb._get(this.fieldInfo, 'limitChooseRange.filterConditions', [])
      )
      this.form.rule.filterConditions.forEach((item, index) => {
        if (!item.targetFieldList) {
          // targetFieldList 后端不存，初始化时需要前端获取最新的字段列表
          this.rangeSelectChange(item.fieldType, index)
        }
      })
      this.rangeDialogShow = true
    },
    // 添加过滤条件
    addCondition({ attr, fieldType }) {
      const fieldData = this.fieldMap[attr]['fieldInfo']
      const symbol = fieldData['logicList'][0]['symbol']
      const valueType = fieldData['valueTypeList'] ? fieldData['valueTypeList'][0]['type'] : ''
      this.form.rule.filterConditions.push({
        attr, // 目标表单字段
        fieldType, // 字段类型
        symbol, // 操作标示，比如等于，不等于，大于，小于等等
        valueType, // 值类型，1表示动态值，2表示固定值
        value: undefined, // 固定值类型的值
        valueAttr: undefined, // 动态值对应的字段
        targetFieldList: [], // 可选列表
        rel: 'and' // 列表中第一个过滤条件该字段为空，其他则为：并且：and 或者：or
      })
      this.rangeSelectChange(fieldType)
    },
    // 范围选择，不走接口，前端筛选可选值
    rangeSelectChange(fieldType, index) {
      let attrArr = []
      // 判断当前字段是不是子表单内的字段 如果是 提供当前 base 表单和子表单内可关联的字段 否则仅提供 base 表单可关联字段
      if (this.fieldInfo.parentAttr) {
        attrArr = this.getFieldCorrespondence(fieldType, this.fieldInfo.parentAttr)
      } else {
        attrArr = this.getFieldCorrespondence(fieldType)
      }
      this.getCurrentInfo(fieldType, attrArr)
      console.log(attrArr)
      this.form.rule.filterConditions[
        index > -1 ? index : this.form.rule.filterConditions.length - 1
      ].targetFieldList = attrArr
    },
    /**
     * 获取可选字段
     * 注：子表单可以选择子表单自己的其他字段，也可以选择base表单字段，但不能选择其他子表单的字段
     * base表单字段不能选择任何sub表单的字段，因sub表单可添加多行数据
     */
    getFieldCorrespondence(fieldType, subFormAttr) {
      const arr = []
      const fieldLinkAttr = fieldCorrespondence[fieldType]
      this.layoutModel.forEach((res) => {
        // 子表单
        if (
          subFormAttr &&
          res.hasOwnProperty('subForm') &&
          res.attr === subFormAttr &&
          res.subForm.items &&
          res.subForm.items.length > 0
        ) {
          res.subForm.items.forEach((item) => {
            if (fieldLinkAttr.indexOf(item.fieldType) !== -1 && item.attr !== this.fieldInfo.attr) {
              arr.push({
                ...item,
                attr: `${res.attr}.${item.attr}`,
                attrName: `${res.attrName}.${item.attrName}`
              })
            }
          })
        }
        if (
          fieldLinkAttr.indexOf(res.fieldType) !== -1 &&
          (this.fieldInfo.parentAttr || this.fieldInfo.attr !== res.attr)
        ) {
          arr.push(res)
        }
      })
      return arr
    },
    // 成员，部门特殊逻辑
    // 因没有实体的字段支撑，所以前后端写死固定的type支撑当前字段
    getCurrentInfo(fieldType, attrArr) {
      let arr = null
      if (fieldType === FieldTypeEnum.userName) {
        arr = {
          attr: 'register_user',
          attrName: '当前登录用户姓名',
          fieldType: FieldTypeEnum.loginUser
        }
      } else if (fieldType === FieldTypeEnum.deptName) {
        arr = {
          attr: 'register_dept',
          attrName: '当前登录用户部门名称',
          fieldType: FieldTypeEnum.loginDept
        }
      }
      arr && attrArr.push(arr)
    },
    saveRange() {
      this.$refs.rangeSelectFormRef.validate((valid) => {
        if (valid) {
          this.fieldInfo.limitChooseRange.filterConditions = xbb.deepClone(
            this.form.rule.filterConditions
          )
          this.rangeDialogShow = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.attr-range-select {
  &__setting {
    margin-top: 10px;
  }
  &__desc {
    display: block;
    margin-top: 10px;
    color: $text-auxiliary;
  }
}
</style>
