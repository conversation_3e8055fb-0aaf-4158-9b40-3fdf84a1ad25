<!--
 * @Description: 字段类型属性
 -->
<template>
  <div class="attr-field-type">
    <el-tag>{{ fieldTypeName }}</el-tag>
  </div>
</template>

<script>
import { fieldComponentsMap } from '@/views/edit/new-form-design/fields-design-map.js' // 字段属性map字典
import mixin from './mixin'

export default {
  name: 'AttrFieldType',

  mixins: [mixin],

  computed: {
    fieldTypeName() {
      const fieldType = this.fieldInfo.fieldType
      return fieldComponentsMap[fieldType] && fieldComponentsMap[fieldType]['attrName']
    }
  },

  methods: {
    init() {}
  }
}
</script>

<style lang="scss" scoped>
.attr-field-type {
  position: absolute;
  top: 5px;
  right: 15px;
  // top: 10px;
}
</style>
