/*
 * @Description: 字段属性公共mixin
 */
import dataRelyDialog from './../other/data-rely-dialog'
import productDataRelyDialog from './../other/product-data-rely-dialog'
import SelectSingleTree from './../other/SelectSingleTree'
import ConditionDialog from './../other/conditionDialog'
import batchEdit from './../other/batch-edit.vue'
import radioGroupAssociatedDialog from './../other/radiogroup-associated-dialog'
import multiVerAssociatedDialog from './../other/multi-ver-associated-dialog'

import UserDeptSelect from '@/components/user-dept-select/user-dept-select'

import FormSelectTree from '@/components/form-select-tree/form-select-tree'
import AppFormSelectTree from './../other/form-select-tree'

import LinkConditionDialog from './../other/link-condition-dialog'

import DeptSingleDialog from '@/components/dept-single-dialog/dept-single-dialog'
// import PersonSelect from '@/components/person/person-select.vue'
import FormulaDialog from '@/components/codemirror-formula/formula-dialog.vue'

import CustomerStageDialog from '../other/customer-stage-dialog'
import ClueStageDialog from '../other/clue-stage-dialog'
import SaleStageEditDialog from '../other/sale-stage-dialog'
import DataEditDialog from '../other/data-edit-dialog'
import chooseModelFieldDialog from '../other/choose-model-field'

import tagSetDialog from '@/components/label-setting/tag-manage-dialog.vue'
// import tagSetDialog from '../other/tag-set-dialog'

import MutilUnitDialog from '../other/mutil-unit-dialog.vue' // 多单位弹框组件
import MyQuillEditor from './components/my-quill-editor.vue'

import MultiVerSetting from '../other/multi-ver-setting.vue' // 多版本是否启用/默认启用版本组件
import MultiVerPriorityDialog from '../other/multi-ver-priority-dialog.vue'

export default {
  props: {
    fieldInfo: {
      // 字段解释详细
      type: Object,
      required: true
    },
    layoutModel: {
      // 全部字段
      type: Array,
      required: true
    },
    formExplainSet: {
      // 镜像字段权限控制
      type: Object
    },
    // 是否为子表单
    isSubform: {
      type: Boolean,
      default: false
    },
    formAttr: {
      type: Object
    }
  },
  computed: {
    enableMultiUnit() {
      // 获取是否启用多单位的标识
      return this.fieldInfo && this.fieldInfo.saasAttr === 'productUnit'
        ? this.layoutModel.find((item) => item.saasAttr === 'enableMultiUnit')
        : undefined
    },
    forbiddenSettingMap() {
      const res = {}
      const forbiddenSettingList = this.fieldInfo.forbiddenSettingList || []
      forbiddenSettingList.forEach((item) => {
        res[item] = true
      })
      return res
    },
    // 是否是saas 所属字段
    isSaas() {
      return +this.$route.query.saasMark === 1
    },
    isSub: {
      get() {
        let res = false
        if (this.fieldInfo.parentAttr) {
          res = true
        }
        return res
      }
    },
    // 所在子表单
    subFormInfo() {
      const parentAttr = this.fieldInfo.parentAttr
      const val = this.layoutModel.find((item) => item.attr === parentAttr)
      return (this.isSub && val) || null
    },
    // 所在子表单是否启用数据联动
    subFormIsRely() {
      const res = false
      if (
        this.subFormInfo &&
        this.subFormInfo.defaultAttr &&
        this.subFormInfo.defaultAttr.defaultType === 'dataRely'
      ) {
        // 在父字段中对子表单字段设置过关联显示，就不允许给子字段设置数据关联，否则可以继续设置
        const subRelyFieldList = this.subFormInfo.defaultAttr.rely.subRelyFieldList
        return subRelyFieldList.some((item) => {
          return item.thisField === this.fieldInfo.attr
        })
      } else if (
        this.subFormInfo &&
        this.subFormInfo.fieldType === 20004 &&
        this.subFormInfo.relyList &&
        this.subFormInfo.defaultAttr.defaultType === 'productDataRely'
      ) {
        // 关联产品数据联动已经设置过子字段的联动则返回true
        console.log(8, this.subFormInfo.relyList)
        const relyList = this.subFormInfo.relyList
        return relyList.some((item) => {
          return item.subRelyFieldList.some((it) => {
            return it.thisField === this.fieldInfo.attr
          })
        })
      }
      return res
    }
  },
  methods: {
    changeDefaultOrItemType() {
      // debugger
      // 子表单本来里面设置了字字段数据联动，用户把数据联动改为其他（比如自定义），再对子字段进行自己数据联动，就把原来设置里面数据去掉
      if (
        this.subFormInfo &&
        this.subFormInfo.fieldType === 20004 &&
        this.subFormInfo.relyList &&
        this.subFormInfo.defaultAttr &&
        this.subFormInfo.defaultAttr.defaultType &&
        this.subFormInfo.defaultAttr.defaultType !== 'productDataRely'
      ) {
        // 关联产品
        this.subFormInfo.relyList.forEach((item) => {
          if (item.subRelyFieldList) {
            item.subRelyFieldList.forEach((it, index) => {
              if (this.fieldInfo.attr === it.thisField) {
                item.subRelyFieldList.splice(index, 1)
              }
            })
          }
        })
      } else if (
        this.subFormInfo &&
        this.subFormInfo.fieldType === 10006 &&
        this.subFormInfo.defaultAttr &&
        this.subFormInfo.defaultAttr.rely &&
        this.subFormInfo.defaultAttr.defaultType !== 'dataRely'
      ) {
        // 普通子表单
        const subRelyFieldList = this.subFormInfo.defaultAttr.rely.subRelyFieldList
        if (subRelyFieldList) {
          for (let i = 0; i < subRelyFieldList.length; i++) {
            if (this.fieldInfo.attr === subRelyFieldList[i].thisField) {
              subRelyFieldList.splice(i, 1)
              break
            }
          }
        }
      } else if (
        this.subFormInfo &&
        [10006, 20003].includes(this.subFormInfo.fieldType) &&
        this.subFormInfo.defaultAttr &&
        this.subFormInfo.defaultAttr.defaultType !== 'business'
      ) {
        // 普通子表单切换工商查询的处理
        // 当子表单的默认值由工商查询切换至其他选项时，将子表单中的单行文本还有下拉框里面的默认值设置为自定义并清除工商查询信息
        this.subFormInfo.subForm.items.forEach((item) => {
          if (item.fieldType === 1 && item.defaultAttr.defaultType === 'business') {
            item.defaultAttr.defaultType = 'custom'
            delete item.defaultAttr.businessMatchAttr
          } else if (item.fieldType === 3 && item.defaultAttr.defaultType === 'business') {
            item.comboType = 0
            delete item.defaultAttr.businessMatchAttr
          }
        })
      }
    }
  },
  components: {
    dataRelyDialog,
    productDataRelyDialog,
    SelectSingleTree,
    ConditionDialog,
    radioGroupAssociatedDialog,
    multiVerAssociatedDialog,
    batchEdit,
    UserDeptSelect,
    FormSelectTree,
    AppFormSelectTree,
    LinkConditionDialog,
    DeptSingleDialog,
    FormulaDialog,
    CustomerStageDialog,
    ClueStageDialog,
    SaleStageEditDialog,
    DataEditDialog,
    chooseModelFieldDialog,
    MutilUnitDialog,
    MyQuillEditor,
    tagSetDialog,
    MultiVerSetting,
    MultiVerPriorityDialog
  }
}
