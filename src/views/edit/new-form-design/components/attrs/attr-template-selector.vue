<!--
 * @Description: 选择模版字段
 -->
<template>
  <div class="attr-template-selector">
    <SettingBlock title="关联字段">
      <el-button class="setting_width" @click="SelectTemplateFun"> 选择模版字段 </el-button>
    </SettingBlock>
    <LineBlock />
    <!-- 字段依赖 -->
    <template-field-selector
      v-if="templateFieldSelectorDialogShow"
      :field-info="fieldInfo"
      :is-sub="isSubform"
      :layout-model="layoutModel"
      :show.sync="templateFieldSelectorDialogShow"
    ></template-field-selector>
  </div>
</template>

<script>
import mixin from './mixin'
import TemplateFieldSelector from './components/template-field-selector'

export default {
  name: 'AttrTemplateSelector',

  mixins: [mixin],
  components: { TemplateFieldSelector },

  data() {
    return {
      templateFieldSelectorDialogShow: false
    }
  },

  methods: {
    init() {},
    SelectTemplateFun() {
      if (!this.fieldInfo.linkInfo.linkBusinessType) {
        this.$message({
          showClose: true,
          message: '先选择一个表单',
          type: 'error'
        })
        return
      }
      this.templateFieldSelectorDialogShow = true
    }
  }
}
</script>

<style scoped>
.template-field-selector {
  width: 100%;
  padding: 8px 0;
  color: $text-plain;
  text-align: center;
  border: 1px solid $neutral-color-3;
  border-radius: 4px;
}
.setting_width {
  width: 100%;
}
</style>
