<!--
 * @Description: 20001关联字段 https://xbb.yuque.com/lfwuxq/vsf9sv/ci5q0n#QokdD
 -->
<template>
  <div v-if="!forbiddenSettingMap['correlationField']" class="attr-association">
    <SettingBlock :title="$t('business.related', { attr: $t('nouns.field') })">
      <el-button style="width: 100%" @click="chooseModel">{{
        $t('formDesign.selectTemplateField')
      }}</el-button>
    </SettingBlock>
    <LineBlock />
    <chooseModelFieldDialog
      v-if="chooseModelFieldDialogShow"
      :field-info="fieldInfo"
      :layout-model="layoutModel"
      :show.sync="chooseModelFieldDialogShow"
    ></chooseModelFieldDialog>
  </div>
</template>

<script>
import mixin from './mixin'

export default {
  name: 'AttrAssociation',

  components: {},

  filters: {},

  mixins: [mixin],
  props: {},

  data() {
    return {
      chooseModelFieldDialogShow: false
    }
  },

  computed: {},

  watch: {},

  mounted() {},

  methods: {
    chooseModel() {
      this.chooseModelFieldDialogShow = true
    }
  }
}
</script>

<style lang="scss" scoped></style>
