<!--
 * @Description: 规则条件
 -->
<template>
  <div class="rule-conditions">
    <div v-if="!hideTitle" class="block__title">
      <p style="margin: 0 0 10px">
        <span class="_title">{{ $t('formDesign.scopeOfOperation') }}</span>
        <span class="_memo">{{ $t('formDesign.operateHint1') }}</span>
      </p>
      <p class="_memo">{{ $t('formDesign.operateHint2') }}</p>
    </div>
    <!-- 添加菜单 -->
    <el-dropdown trigger="click">
      <!-- @click="addCondition" -->
      <el-button icon="el-icon-plus" type="text">{{
        $t('formDesign.addFilterCondition')
      }}</el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in filterFieldList"
          :key="item.attr"
          :disabled="hasOtherSubFormField(item.attr, filterConditions)"
          @click.native="addCondition(item)"
          >{{ item.attrName }}</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 添加过滤条件 -->
    <AddConditionsRow
      v-if="!showTrigglerCondition"
      :field-list="fieldList"
      :filter-conditions="filterConditions"
      :operations="operations"
      :rule-type="ruleType"
    />
    <!-- 查看触发条件 -->
    <TrigglerConditionsRow
      v-else
      :field-list="fieldList"
      :filter-conditions="filterConditions"
      :operations="operations"
      :rule-type="ruleType"
      :show-value-type="showValueType"
    />
  </div>
</template>

<script>
import { getFormRelationRuleList } from '@/api/form'
import businessMixin from './business-rules'
import componentsMixin from './component/componentsMixin'
import validationUtils from './validation-utils'
import AddConditionsRow from '@/components/business-rules/add-conditions-row'
import TrigglerConditionsRow from '@/components/business-rules/triggler-conditions-row'

export default {
  name: 'RuleConditions',
  components: {
    AddConditionsRow,
    TrigglerConditionsRow
  },
  mixins: [businessMixin, componentsMixin, validationUtils],
  props: {
    // 隐藏顶部标题
    hideTitle: {
      type: Boolean,
      default: false
    },
    // 范围选择
    rangeSelect: {
      type: Boolean,
      default: false
    },
    ruleType: {
      type: Number,
      required: true
    },
    target: {
      default: () => {
        return {}
      }
    },
    layoutModel: {
      default: () => []
    },
    fieldList: {
      default: () => []
    },
    filterConditions: {
      default: () => []
    },
    operations: {
      default: () => []
    },
    showValueType: {
      default: true
    },
    showTrigglerCondition: {
      default: false
    },
    fixedVal: {
      default: false
    }
  },
  computed: {},

  watch: {},

  created() {
    this.init()
  },

  methods: {
    init() {
      this.symbolArray = this.symbolOptions // 条件初始化
      this.fixedVal ||
        this.filterConditions.forEach((item, index) => {
          console.log('getFormRelationRuleList')
          this.getFormRelationRuleList(index, item.attr)
        })
    },
    addCondition(item) {
      if (this.filterConditions.length >= 5) {
        this.$message({
          message: this.$t('message.mostSet', { attr: this.$t('nouns.filterCondition'), num: 5 }),
          type: 'warning'
        })
        return
      }
      this.$emit('addCondition', item)
      // 范围选择拦截，不走接口
      if (this.rangeSelect) return
      const len = this.filterConditions.length
      this.linkFieldChange(len - 1, this.filterConditions[len - 1])
    },
    linkFieldChange(index, item) {
      const row = this.filterConditions[index]
      const rowAttr = row['attr']
      row['fieldType'] = this.fieldMap[rowAttr]['fieldType']
      row['valueType'] = this.valueTypeMap[item.attr][0]
        ? this.valueTypeMap[item.attr][0]['type']
        : 2
      row['symbol'] = this.logicListMap[item.attr][0]?.symbol
      row['valueAttr'] = ''
      console.log(this.valueTypeMap, row)
      this.getFormRelationRuleList(index, rowAttr)
      // 数字2 日期4、创建时间10014、修改时间10015
      // if ([2, 4, 10014, 10015].includes(item.fieldType)) { // 规则https://xbb.yuque.com/lfwuxq/vsf9sv/akalgf
      //   this.symbolArray = this.symbolOptions // 条件初始化
      // } else {
      //   this.symbolArray = this.symbolJudge
      //   item.symbol = 'equal'
      // }
      if ([12, 10003].includes(item.fieldType)) {
        item.value = { city: '', address: '', district: '', province: '' }
      } else {
        item.value = undefined // init
      }
    },
    getFormRelationRuleList(index, attr) {
      if (!attr) return false
      getFormRelationRuleList({
        appId: this.$route.query.appId,
        menuId: this.$route.query.menuId,
        formId: this.$route.query.formId,
        saasMark: this.$route.query.saasMark,
        businessType: this.$route.query.businessType,
        fieldType: this.fieldMap[attr]['fieldType'],
        linkInfo: this.fieldMap[attr]['fieldInfo']['linkInfo'],
        linkedType: this.fieldMap[attr]['fieldInfo']['linkedType'],
        target: this.target
      })
        .then(({ result: { formExplainList } }) => {
          this.$set(this.filterConditions, index, {
            ...this.filterConditions[index],
            targetFieldList: formExplainList
          })
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.rule-conditions {
  width: 100%;
  margin-top: 10px;
  // margin-bottom: 20px;
  & > .block__title {
    margin-bottom: 10px;
    ._title {
      margin: 10px 0 4px;
      font-size: 14px;
      color: $text-main;
    }
    ._memo {
      font-size: 12px;
      color: $text-auxiliary;
    }
  }
  .rule-conditions__row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    .no-magin-form {
      margin-bottom: 0;
    }
    // margin-bottom: 5px;
    & > .rule-conditions__field {
      display: flex;
      flex: 3;
      & > .item__value {
        flex: 4;
      }
      & > .item__rel {
        flex: 2;
        margin-right: 5px;
      }
    }
    & > .rule-conditions__text {
      flex: 0.5;
      text-align: center;
      // margin-bottom: 18px;
    }
    & > .rule-conditions__symbol {
      flex: 1;
      margin-right: 5px;
    }
    & > .rule-conditions__val-type {
      flex: 1;
      margin-right: 5px;
    }
    & > .rule-conditions__value {
      width: 280px;
      // margin-bottom: 18px;
      :deep(.el-form) {
        & > div {
          width: 100%;
          & > div {
            width: 100% !important;
          }
        }
      }
    }
    & > .rule-conditions__btn {
      flex: 0.5;
      text-align: center;
      // margin-bottom: 18px;
    }
  }
}
</style>
