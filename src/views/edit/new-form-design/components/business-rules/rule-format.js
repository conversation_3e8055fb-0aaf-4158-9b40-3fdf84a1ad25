/*
 * @Description: 业务规则从低级到高级的转化
 */
import { fieldsMap } from 'src/constants/common/index.js'
// RuleFormalUtils
export default {
  target: null,
  operation: null,
  currentFormAttr: null,
  /**
   * @param {*} symbol  enum 操作方式 等于 | 加 | 减
   * @param {*} valueType enum 1 = 动态值 2 | 固定值
   */
  toFormulaInfo(operation, target, currentFormAttr) {
    this.target = target
    this.operation = operation
    this.currentFormAttr = currentFormAttr
    return this._format(operation.symbol)
  },
  _format(symbol) {
    let formula
    switch (symbol) {
      // 等于
      case 'equal':
        formula = this._equalHandler()
        break
      case 'sum':
        formula = this._sumHandler()
        break
      case 'less':
        formula = this._lessHandler()
        break

      default:
        break
    }
    return formula
  },
  // 等于赋值的转化
  _equalHandler() {
    const { valueType, valueAttr, valueAttrName, fieldType, value } = this.operation
    // let { formId, name } = this.target
    // let { self } = this.currentFormAttr
    // 动态值
    if (valueType === 1) {
      if (!valueAttr) {
        return {
          formula: ``,
          labelMap: {},
          text: ``
        }
      }
      const key = `self.${valueAttr}`
      const attrName = `${valueAttrName}`
      return {
        formula: `{${key}}`,
        labelMap: {
          [key]: attrName
        },
        text: `{${attrName}}`
      }
    } else if (valueType === 2) {
      // 固定值

      const formatVal =
        value === undefined ? undefined : JSON.stringify(fieldsMap[fieldType].toFormat(value))
      console.log(formatVal)
      if ([10009, 10010, 10011, 10012, 10017, 10018].includes(fieldType)) {
        const { val, label, text } = formatVal
        return {
          formula: val,
          labelMap: label,
          text
        }
      } else {
        return {
          formula: formatVal === undefined ? undefined : formatVal,
          labelMap: {},
          text: formatVal === undefined ? undefined : formatVal
        }
      }
    }
  },
  // 加
  _sumHandler(less = false) {
    const handler = less ? ' - ' : ' + '
    let { attr, attrName, valueType, valueAttr, valueAttrName, value } = this.operation
    const { formId, name } = this.target
    const targetKey = `${formId}.${attr}`
    const targetText = `${name}.${attrName}`
    const selfKey = `self.${valueAttr}`
    const selfText = `${valueAttrName}`
    // 动态值
    if (valueType === 1) {
      if (!valueAttr) {
        return {
          formula: ``,
          labelMap: {},
          text: ``
        }
      }
      return {
        formula: `{${targetKey}}${handler}{${selfKey}}`,
        labelMap: {
          [targetKey]: targetText,
          [selfKey]: selfText
        },
        text: `{${targetText}} ${handler} {${selfText}}`
      }
    } else if (valueType === 2) {
      if (value === undefined) {
        value = 0
      }
      // 固定值
      return {
        formula: `{${targetKey}} ${handler} ${value}`,
        labelMap: {
          [targetKey]: targetText
        },
        text: `{${targetText}} ${handler} ${value} `
      }
    }
  },
  // 减
  _lessHandler() {
    return this._sumHandler(true)
  }
}
