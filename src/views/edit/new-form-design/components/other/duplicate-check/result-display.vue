<template>
  <div class="result-display-dialog">
    <div class="title">
      <span>{{ formName + $t('formDesign.checkDisplaySetting') }}</span>
      <LineBlock />
    </div>
    <div class="content">
      <el-button class="btn content" size="medium" type="text" @click="previewTable">
        {{ $t('duplicate.effectPre') }}
      </el-button>
      <el-transfer
        ref="transferEl"
        v-model="chooseVal"
        v-loading="loading"
        :data="transData"
        :filter-method="filterMethod"
        filterable
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}'
        }"
        target-order="push"
        :titles="[$t('operation.allFields'), $t('operation.selectedFields')]"
        @change="handleChange"
      >
        <div slot-scope="{ option }" class="stage--move">
          <span>{{ option.attrName }}</span>
          <i class="el-icon-sort"></i>
        </div>
      </el-transfer>
    </div>
    <el-dialog
      v-if="showDailog"
      append-to-body
      :title="$t('CRM.repeatTitle')"
      :visible.sync="showDailog"
    >
      <search-table :data-item="dataItem" />
    </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import searchTable from '@/views/crm/customer/components/search-table'
import { repeatResultRuleList, repeatResultSave } from '@/api/form-setting'

export default {
  // 查重结果展示
  name: 'ResultDisplay',
  components: {
    SearchTable: searchTable
  },
  props: {
    formName: {
      type: String,
      default: ''
    },
    formInfo: {
      type: Object,
      default: () => ({})
    },
    dialogShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      transData: [],
      chooseVal: [],
      showDailog: false,
      dataItem: {
        checkList: [
          {
            headList: [],
            dataList: [],
            formName: this.formName
          }
        ],
        menuName: ''
      },
      enum: {
        8000: this.$t('nouns.clue'),
        100: this.$t('nouns.customer'),
        401: this.$t('CRM.contacts')
      },
      loading: true
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.sortable && this.sortable.destroy()
  },
  methods: {
    async init() {
      const { businessType, formId, saasMark } = this.formInfo
      try {
        const { result } = await repeatResultRuleList({ businessType, formId, saasMark })
        const { formExplainList, ruleList } = result
        this.transData = formExplainList.map((item, idx) => ({ ...item, key: idx }))
        ruleList.forEach((rule) => {
          const field = this.transData.find((trans) => trans.attr === rule)
          if (field) {
            this.chooseVal.push(field.key)
          } else {
            this.transData.push({
              attr: rule,
              attrName: rule,
              fieldType: 1,
              key: this.transData.length,
              isDel: 1 // 被删除的字段
            })
            this.chooseVal.push(this.transData.length - 1)
          }
        })
        this.dataItem.menuName = this.enum[+this.$route.query.businessType]
        this.setSort()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    setSort() {
      if (!this.$refs.transferEl) return
      const transfer = this.$refs.transferEl.$el
      const panel = transfer.getElementsByClassName('el-transfer-panel')
      // 给左边加入class标识
      panel[0].classList.add('is-left-transfer')
      const rightPanel = panel[1].getElementsByClassName('el-transfer-panel__body')[0]
      const rightEl = rightPanel.getElementsByClassName('el-transfer-panel__list')[0]
      Sortable.create(rightEl, {
        handle: '.stage--move',
        scroll: true,
        onEnd: ({ oldIndex, newIndex }) => {
          const targetRow = this.chooseVal.splice(oldIndex, 1)[0]
          this.chooseVal.splice(newIndex, 0, targetRow)
        }
      })
    },
    // 保存
    saveSetting() {
      const ruleList = this.getChooseVal(true)
      const { businessType, formId, saasMark } = this.formInfo
      repeatResultSave({ businessType, formId, saasMark, ruleList })
        .then(({ success, msg }) => {
          success && this.$message.success(msg)
          this.$emit('update:dialogShow', false)
        })
        .catch((e) => {
          console.log(e)
        })
    },
    // 效果预览
    previewTable() {
      this.$set(this.dataItem.checkList[0], 'headList', this.getChooseVal())
      this.showDailog = true
    },
    // 获取已选字段信息
    getChooseVal(isAttr = false) {
      const arr = []
      this.chooseVal.forEach((item) => {
        const field = isAttr ? this.transData[item].attr : this.transData[item]
        arr.push(field)
      })
      return arr
    },
    // 搜索
    filterMethod(query, item) {
      return item.attrName.indexOf(query) > -1
    },
    handleChange(value, direction, changeIdx) {
      // 被删除的字段 设置未选中 直接删除
      if (direction === 'left' && this.transData[changeIdx].isDel) {
        this.transData.splice(changeIdx, 1)
      }
      // 选中字段最多10个判断
      if (direction === 'right' && value.length > 10) {
        this.chooseVal = this.chooseVal.slice(0, 10)
        this.$message.warning(this.$t('person.mostSelect', { attr: 10 }))
      }
    }
  }
}
</script>

<style lang="scss">
// 穿梭框样式错乱修正
.result-display-dialog {
  .el-transfer {
    height: 388px;
    .el-transfer-panel {
      width: 250px;
      .el-transfer-panel__body {
        height: 346px;
        .el-transfer-panel__list {
          padding-bottom: 12px;
        }
      }
      .el-checkbox-group {
        height: 294px;
      }
    }
    // 消失icon
    .is-left-transfer {
      .el-icon-sort {
        display: none;
      }
    }
  }
  .el-transfer__buttons .el-button {
    display: block;
    margin: 0;
    span {
      margin: 0;
    }
  }
  .el-transfer-panel__filter {
    width: 218px !important;
  }
  .el-transfer__buttons .el-button:first-child {
    margin-bottom: 10px;
  }
  .el-transfer-panel__item {
    margin-right: 0 !important;
  }
}
</style>

<style lang="scss" scoped>
.result-display-dialog {
  .title {
    span {
      padding-left: 10px;
      font-size: 16px;
      font-weight: 500px;
      line-height: 24px;
      color: $text-main;
      border-left: 4px solid $brand-color-5;
    }
  }
  .content {
    &.btn {
      margin: 10px 0;
    }
    .draggable-index__field-item--fallback {
      // background: rgba(255,142,61,0.1);
      z-index: 10;
      cursor: pointer;
    }
    // 拖动时的虚影
    .draggable-index__field-item--ghost {
      background: transparent !important;
      border: 1px dashed $brand-color-5 !important;
      * {
        visibility: hidden;
      }
    }
    .stage--move {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 10px;
      span {
        max-width: 175px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .stage--move-class {
      cursor: pointer;
      i {
        font-size: 14px;
      }
    }
    .stage--move--disable {
      color: $neutral-color-1;
      cursor: not-allowed !important;
    }
    .stage-edit {
      color: $brand-color-5;
      text-decoration: underline;
      cursor: pointer;
    }
  }
}
</style>
