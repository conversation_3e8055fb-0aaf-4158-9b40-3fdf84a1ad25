<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Author: xuwang.bao
 * @Date: 2019-01-02 16:39:13
 * @LastEditors: yunfei.wang
 * @LastEditTime: 2021-11-25 14:49:44
 * @Description: 搬运字段选项属性-批量编辑弹窗-组件
 -->

<template>
  <el-dialog
    :append-to-body="true"
    :before-close="cancel"
    :title="$t('formDesign.batchEditing')"
    :visible.sync="isActive"
    width="800px"
  >
    <el-container style="height: 440px">
      <el-input
        v-model="optionsItems"
        class="batch-edit__textarea"
        :placeholder="$t('rule.lessThanFie', { attr: $t('nouns.option'), num: 50 })"
        type="textarea"
      >
      </el-input>
    </el-container>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">{{ $t('operation.cancel') }} </el-button>
      <el-button type="primary" @click="submit">{{ $t('operation.confirm') }} </el-button>
    </span>
  </el-dialog>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

// import { guid } from '@/utils/uuid'
import xbb from '@xbb/xbb-utils'

export default {
  name: 'BatchEdit',

  props: {
    isActive: {
      type: Boolean,
      default: false
    },
    // 是否是决策树拉起的弹窗
    isDecisionTree: {
      type: Boolean,
      default: false
    }
    // 字段详情设置
    /* widgetDetailItems: {
      type: Array,
      default: () => []
    } */
  },
  data() {
    return {
      optionsItems: '',
      optionsItemArr: [],
      otherItem: {}
    }
  },
  watch: {},
  created() {},
  mounted() {},

  methods: {
    cancel() {
      this.$emit('dialogConfirmCancel')
    },
    submit() {
      let arr = this.optionsItems.split('\n')
      arr = arr.filter((item) => {
        return !(item === null || item === '' || item === undefined)
      })
      /* this.arr.forEach(item => {
        item = item.replace(/\s/g, '')
      }) */
      this.optionsItemArr = []
      arr.forEach((item) => {
        this.optionsItemArr.push({
          text: item.trim().substring(0, 50),
          value: xbb.guid(),
          checked: false,
          color: this.isDecisionTree ? '#722ED1' : '#646566',
          isVisible: 1
        })
      })
      if (Object.keys(this.otherItem).length !== 0) {
        this.optionsItemArr.push(this.otherItem)
      }
      this.$emit('dialogSubmit', this.optionsItemArr)
    },
    init() {
      this.optionsItems = ''
      this.optionsItemArr = []
      /* let tempItems = JSON.parse(JSON.stringify(this.widgetDetailItems))
      this.optionsItems = ''
      this.otherItem = {}
      if (tempItems[tempItems.length - 1].isOther === 1) {
        this.otherItem = tempItems.splice(tempItems.length - 1, 1)[0]
      }
      tempItems.forEach(item => {
        this.optionsItems += item.text + '\n'
      }) */
    }
  }
}
</script>

<style lang="scss">
.batch-edit__textarea {
  height: 100%;
  & > textarea {
    height: 100%;
    resize: none;
  }
}
</style>
