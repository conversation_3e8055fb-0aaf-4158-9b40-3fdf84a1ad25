<!-- 完成条件 -->
<template>
  <el-dialog
    :append-to-body="true"
    :before-close="conditionDialogCloseHandler"
    class="complete-condition-dialog"
    destroy-on-close
    :model="false"
    :title="$t('operation.set') + $t('stageProcess.completeCondition')"
    :visible.sync="dialogShow"
    width="680px"
  >
    <el-form
      ref="conditionRef"
      label-position="left"
      label-width="110px"
      :model="conditionData"
      :rules="conditionRules"
      size="small"
    >
      <el-form-item label-width="80px" prop="name">
        <span slot="label" style="width: 70px; font-weight: bold">{{
          $t('stageProcess.conditionName')
        }}</span>
        <el-input
          v-model="conditionData.name"
          autocomplete="off"
          :disabled="readOnly"
          :maxlength="maxNameLen"
          :placeholder="$t('placeholder.inputPls', { attr: $t('stageProcess.conditionName') })"
          show-word-limit
          style="width: 215px"
        ></el-input>
      </el-form-item>
      <el-form-item label-width="0" prop="filter">
        <complete-condition
          ref="completeConditionRef"
          v-model="conditionData.filter"
          :disabled="readOnly"
          :required="true"
        />
      </el-form-item>
      <el-form-item class="complete-condition-dialog__msg" label-width="0" prop="messageContent">
        <div class="msg-header">
          <span class="msg-header__title">{{ $t('stageProcess.noticeContent') }}</span>
          <span class="msg-header__tip">{{ $t('stageProcess.noticeContentMemo') }}</span>
        </div>
        <field-message-checkbox
          v-model="conditionData.messageContent"
          :form-attr="formAttr"
          :read-only="readOnly"
          :stage-attr="stageAttr"
          :text="$t('stageProcess.clickSetNotice')"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="complete-condition-dialog__footer">
      <el-button @click="conditionDialogCloseHandler">{{ $t('operation.cancel') }}</el-button>
      <el-button :disabled="readOnly" type="primary" @click="conditionDialogSaveHandler">{{
        $t('operation.confirm')
      }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import { mapGetters } from 'vuex'
import completeCondition from './complete-condition'
import fieldMessageCheckbox from '../../field-message-checkbox'
import dialogMixin from '@/mixin/dialog'

const CONDITION_NAME_MAX_LENGTH = 10 // 任务名称长度
export default {
  name: 'CompleteConditionDialog',

  components: {
    CompleteCondition: completeCondition,
    FieldMessageCheckbox: fieldMessageCheckbox
  },

  mixins: [dialogMixin],

  props: {
    condition: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    stageAttr: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      conditionData: {}
    }
  },

  computed: {
    ...mapGetters(['formInfo']),
    formAttr() {
      return {
        menuId: this.formInfo.formAttr.menuId,
        saasMark: this.formInfo.formAttr.saasMark,
        formId: this.formInfo.formId,
        businessType: this.formInfo.formAttr.businessType,
        appId: this.formInfo.formAttr.appId,
        name: this.formInfo.formAttr.name
      }
    },
    maxNameLen() {
      return CONDITION_NAME_MAX_LENGTH
    },
    conditionRules() {
      return {
        name: [
          {
            required: true,
            message: this.$t('placeholder.inputPls', {
              attr: this.$t('stageProcess.conditionName')
            }),
            trigger: 'blur'
          }
        ],
        filter: [
          {
            required: true,
            message: this.$t('placeholder.choosePls', {
              attr: this.$t('stageProcess.completeCondition')
            }),
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              if (Array.isArray(value) && !value.length) {
                callback(
                  new Error(
                    this.$t('placeholder.choosePls', {
                      attr: this.$t('stageProcess.completeCondition')
                    })
                  )
                )
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      }
    }
  },

  watch: {},

  created() {
    this.conditionData = xbb.deepClone(this.condition)
  },

  mounted() {},

  methods: {
    conditionDialogCloseHandler() {
      this.dialogShow = false
    },
    conditionDialogSaveHandler() {
      const conditionPromise = this.conditionValidate()
      const completePromise =
        !this.$refs.completeConditionRef || this.$refs.completeConditionRef.validate()

      Promise.all([conditionPromise, completePromise])
        .then((results) => {
          this.$emit('updateCondition', this.conditionData)
          this.dialogShow = false
        })
        .catch((err) => {
          console.log('condition', err)
        })
    },
    conditionValidate() {
      if (!this.$refs.conditionRef) return Promise.resolve()
      return new Promise((resolve, reject) => {
        this.$refs.conditionRef.validate((res) => {
          if (res) {
            resolve(res)
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.complete-condition-dialog__msg {
  .msg-header {
    display: flex;
    &__title {
      margin-right: 8px;
      font-weight: bold;
      color: $text-main;
    }
    &__tip {
      font-size: 12px;
      color: $text-auxiliary;
    }
  }
}
</style>

<style lang="scss">
.complete-condition-dialog {
  .el-form-item__label {
    color: $text-main;
  }
}
</style>
