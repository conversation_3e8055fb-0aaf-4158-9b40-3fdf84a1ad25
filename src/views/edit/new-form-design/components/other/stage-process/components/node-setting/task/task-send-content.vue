<!-- 设置发送内容 -->
<template>
  <div class="task-send-content">
    <stage-setting-block :required="required" :title="'发送内容'">
      <SendContentBox
        ref="sendContent"
        :data-info.sync="node"
        :direction="'btt'"
        :form-info="{ ...formAttr, formBusinessType: formAttr.businessType }"
        :props="{ contentAlias: 'description', materialAlias: 'contentMaterial' }"
        style="width: 100%"
      ></SendContentBox>
    </stage-setting-block>
  </div>
</template>

<script>
import stageSettingBlock from '../stage-setting-block'
import SendContentBox from '@/views/scrm/components/business/send-content-box'

export default {
  name: 'TaskSendContent',

  inject: ['formAttr'],

  components: {
    StageSettingBlock: stageSettingBlock,
    SendContentBox: SendContentBox
  },

  mixins: [],

  watch: {
    'node.description'(newVal, oldVal) {
      this.$nextTick(() => {
        this.$refs.sendContent.initContent()
      })
    }
  },

  props: {
    required: {
      type: Boolean,
      default: false
    },
    // 节点详情
    nodeData: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 1000
    }
  },

  computed: {
    node() {
      return this.nodeData
    }
  },

  methods: {
    validate() {
      if (this.required && !this.$refs.sendContent.validateContent()) {
        this.$message.error(this.$t('rule.require', { attr: '发送内容' }))
        return false
      }
      return true
    }
  }
}
</script>
