<!-- 设置任务延期/关闭操作 -->
<template>
  <div class="task-operate">
    <stage-setting-block :required="required" :title="'任务延期'">
      <el-checkbox v-model="allowDelay">允许任务延期</el-checkbox>
    </stage-setting-block>
    <stage-setting-block :required="required" :title="'任务取消'">
      <el-checkbox v-model="allowClose" :disabled="!!nodeData.required">允许任务取消</el-checkbox>
    </stage-setting-block>
  </div>
</template>

<script>
import stageSettingBlock from '../stage-setting-block'

export default {
  name: 'TaskOperate',

  components: {
    StageSettingBlock: stageSettingBlock
  },

  mixins: [],

  props: {
    required: {
      type: Boolean,
      default: false
    },
    // 节点详情
    nodeData: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 30
    }
  },
  computed: {
    allowDelay: {
      get() {
        return !!this.nodeData.allowDelay
      },
      set(val) {
        // 防止初始的时候属性不存在
        this.$set(this.nodeData, 'allowDelay', val ? 1 : 0)
      }
    },
    allowClose: {
      get() {
        return !!this.nodeData.allowClose
      },
      set(val) {
        // 防止初始的时候属性不存在
        this.$set(this.nodeData, 'allowClose', val ? 1 : 0)
      }
    }
  },

  data() {
    return {}
  },
  methods: {
    validate() {
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.task-operate {
  .el-checkbox {
    display: block;
    height: 30px;
    line-height: 30px;
  }
}
</style>
