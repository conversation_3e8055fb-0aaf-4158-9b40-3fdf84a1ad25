<!-- eslint vue/no-mutating-props: 1 -->

<!-- 添加字段条件 -->
<template>
  <div>
    <!--  -->
    <div v-if="curLinkForm && curLinkForm.linkInfoMultiTemplate" class="add-field-condition_top">
      <div class="add-field-condition_top-tablist">
        <div
          v-if="curLinkForm && curLinkForm.linkInfoMultiTemplate && linkFormListTabs.length === 0"
          class="add-field-condition_top-tablist--notemplate"
          style="margin-top: 15px"
        >
          请添加模版
        </div>
        <div v-else>
          <div class="LinkControl_template-tablist-scroll">
            <div
              v-for="item in linkFormListTabs"
              :key="item.linkFormId"
              class="tabs__item"
              :class="{ active: item.linkFormId === selectedTab }"
            >
              <p @click="selectTab(item)">
                {{ item.linkFormName
                }}<span class="delete-icon"
                  ><i class="el-icon-close" @click="closeLinkFormListTabs(item)"></i
                ></span>
              </p>
            </div>
          </div>
        </div>
        <!-- <el-tabs v-else v-model="linkFormListTabsValue">
          <el-tab-pane
            v-for="item in linkFormListTabs"
            :key="item.linkFormName"
            :label="item.linkFormName"
            :name="item.linkFormName"
          >
            <span slot="label">
              {{ item.linkFormName }}
              <i class="el-icon-close" @click="closeLinkFormListTabs(item)"></i
            ></span>
          </el-tab-pane>
        </el-tabs> -->
      </div>
      <!-- 最右边选择模版下拉 -->
      <div>
        <el-dropdown trigger="click">
          <div class="add-field-condition_top-btn" icon="el-icon-plus" type="text">
            <i class="el-icon-plus"></i> 添加模版
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in conditionFormAttrlinkFormList"
              :key="item.linkFormId"
              @click.native="addLinkFormListHandler(item, selectMap[item.linkFormId])"
            >
              <div class="template_plan">
                <span>{{ item.linkFormName }}</span>
                <i
                  v-show="selectMap[item.linkFormId]"
                  class="el-icon-check field-check"
                  style="color: #ff8c2e"
                ></i>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div v-if="linkFormListTabs.length === 0" class="LinkControl_tip">
      添加模版后，可设置关联条件和提示内容
    </div>
    <div
      v-if="
        (curLinkForm && curLinkForm.linkInfoMultiTemplate === undefined) ||
        (curLinkForm && curLinkForm.linkInfoMultiTemplate && linkFormListTabs.length > 0)
      "
      class="add-field-condition"
    >
      <div class="add-field-condition__left">
        <div class="add-field-condition__header">
          <span class="header__title">{{ $t('stageProcess.allFields') }}</span>
        </div>
        <div class="add-field-condition__content border-left">
          <div class="left__search">
            <el-input
              v-model="searchText"
              :placeholder="$t('placeholder.inputPls', { attr: '' })"
              prefix-icon="el-icon-search"
              @change="searchHandler"
            ></el-input>
          </div>
          <el-scrollbar class="left__scrollbar">
            <el-checkbox-group v-model="checkedFields" class="left__checkbox" :disabled="readOnly">
              <el-checkbox
                v-for="(field, index) in searchAllFields"
                :key="field.attr"
                :label="field.attr"
              >
                <span
                  :ref="`leftTextRef${index}`"
                  v-tooltip.html.right-end="{
                    content: field.attrName || field.attr,
                    disabled: !singleEllipsis(`leftTextRef${index}`)
                  }"
                  class="left__checkbox--text"
                >
                  {{ field.attrName || field.attr }}
                </span>
              </el-checkbox>
            </el-checkbox-group>
          </el-scrollbar>
        </div>
      </div>
      <div class="add-field-condition__right">
        <div class="add-field-condition__header">
          <span class="header__title">{{ $t('stageProcess.checkedFields') }}</span>
        </div>
        <div class="add-field-condition__content">
          <el-table
            ref="rightTableRef"
            border
            cell-class-name="right__table--cell"
            class="right__table"
            :data="curLinkForm && curLinkForm.linkInfoMultiTemplate ? MultiIList : fieldList"
            :empty-text="$t('message.noData')"
            height="100%"
            row-class-name="right__table--row"
            row-key="attr"
          >
            <el-table-column
              align="left"
              class-name="right__table--text"
              :label="$t('modal.fieldName')"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{
                  curLinkForm.linkInfoMultiTemplate
                    ? scope.row.attrName
                    : getAttrName(scope.row.attr)
                }}
              </template>
            </el-table-column>
            <el-table-column
              align="left"
              class-name="right__table--text"
              :label="$t('formDesign.fieldType')"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ getFieldTypeName(scope.row.fieldType) }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              class-name="right__table--operate"
              :label="$t('form.required')"
              width="86"
            >
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.required"
                  :disabled="readOnly || notAllowRequired(scope.row)"
                  :false-label="0"
                  :true-label="1"
                ></el-checkbox>
              </template>
            </el-table-column>
            <!-- <el-table-column
            align="center"
            class-name="right__table--operate"
            :label="$t('operation.sort')"
            width="86"
          >
            <template>

            </template>
          </el-table-column> -->
            <el-table-column
              align="center"
              class-name="right__table--operate"
              :label="$t('operation.edit')"
              width="86"
            >
              <template slot-scope="scope">
                <el-button
                  class="right__table--sort"
                  :class="{ move: !readOnly }"
                  :disabled="readOnly"
                  icon="el-icon-rank"
                  type="text"
                ></el-button>
                <el-button
                  class="right__table--delete"
                  :disabled="readOnly"
                  icon="icon-delete-bin-7-line  t-iconfont"
                  type="text"
                  @click="deleteFieldHandler(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import Sortable from 'sortablejs'
import { fieldsMap } from '@/constants/common/fieldsMap.js'
import { getFieldCondition } from '@/api/stage-process-design'

export default {
  name: 'AddFieldCondition',

  components: {},

  mixins: [],

  model: {
    prop: 'fieldList',
    event: 'update'
  },

  props: {
    readOnly: {
      type: Boolean,
      default: false
    },
    allFieldList: {
      type: Array,
      default: () => []
    },
    fieldList: {
      type: Array,
      default: () => []
    },
    curLinkForm: {
      type: Object,
      default: () => {}
    },
    linkData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      searchText: '',
      linkFormListTabs: [],
      searchAllFields: [],
      selectedTab: '',
      MultiILinkFormList: [],
      MultiAllFieldList: []
    }
  },

  computed: {
    selectMap: {
      get() {
        const map = {}
        this.linkFormListTabs.forEach((item) => {
          map[item.linkFormId] = true
        })
        return map
      }
    },
    MultiIList() {
      const targetIndex = this.linkFormListTabs.findIndex(
        (item) => item.linkFormId === this.selectedTab
      )
      return (
        (this.MultiILinkFormList.length &&
          this.MultiILinkFormList[targetIndex] &&
          this.MultiILinkFormList[targetIndex].fieldList) ||
        []
      )
    },
    linkFormListTabsWatch: function () {
      // 其实做到这一步就可以监听到pullStreamList数据的变化了，再用JSON.parse做数据还原方便后边数据处理。
      //return JSON.stringify(this.pullStreamList);
      return JSON.parse(JSON.stringify(this.linkFormListTabs))
    },
    conditionFormAttrlinkFormList() {
      return this.curLinkForm.linkInfoList || []
    },
    allFieldMap() {
      const map = {}
      this.allFieldList.forEach((item) => {
        map[item.attr] = item
      })
      return map
    },
    multiAllFieldMap() {
      const map = {}
      this.MultiAllFieldList.forEach((item) => {
        map[item.attr] = item
      })
      return map
    },
    checkedFields: {
      get() {
        const targetIndex = this.linkFormListTabs.findIndex(
          (item) => item.linkFormId === this.selectedTab
        )
        return (
          (this.MultiILinkFormList.length &&
            this.MultiILinkFormList[targetIndex] &&
            this.MultiILinkFormList[targetIndex].fieldList.map((item) => item.attr)) ||
          []
        )
      },
      set(val) {
        this.MulticheckedFields(val)
      }
    }
  },

  watch: {
    allFieldList: {
      immediate: true,
      deep: true,
      handler(val) {
        this.searchAllFields = val
      }
    },
    MultiAllFieldList: {
      immediate: true,
      deep: true,
      handler(val) {
        if (this.curLinkForm) {
          this.searchAllFields = val
        }
      }
    },
    linkFormListTabsWatch: {
      handler(val, oldVal) {
        // debugger
        if (oldVal.length === 0) {
          this.selectedTab = this.linkFormListTabs[0]?.linkFormId
        } else {
          console.log('1111', val)
        }
      },
      deep: true
    },
    // 切换
    selectedTab(val, oldVal) {
      const targetItem = this.linkFormListTabs.find((item) => item.linkFormId === this.selectedTab)
      this.seleclinkForm = targetItem
      this.getAllFieldList(targetItem)
    }
  },

  created() {},

  mounted() {
    this.rowDropInit()
    if (this.curLinkForm && this.linkData?.list?.length > 0) {
      for (let i = 0; i < this.linkData.list.length; i++) {
        const formID = this.linkData.list[i].formId
        const targetItem = this.linkData.list[i]
        // 在 conditionFormAttrlinkFormList 中查找匹配的对象
        const matchedItem = this.conditionFormAttrlinkFormList.find(
          (item) => item.linkFormId === formID
        )
        const MultiItem = {
          formName: targetItem.formName,
          businessType: targetItem.businessType,
          formId: targetItem.formId,
          saasMark: targetItem.saasMark,
          fieldList: targetItem.fieldList
        }
        this.MultiILinkFormList.push(MultiItem)
        // de
        if (matchedItem) {
          // 将匹配的项目塞到 linkFormListTabs 中
          this.linkFormListTabs.push(matchedItem)
        }
      }
    }
  },

  methods: {
    selectTab(tabName) {
      // debugger
      this.selectedTab = tabName.linkFormId
      // 切换了也要重新请求一下了获取可联动表单字段
      // const matchedItem = this.linkFormListTabs.find((item) => item.formId === this.selectedTab)
      // this.multiGetLinkFieldList(matchedItem)
    },
    // 关闭当前tab
    closeLinkFormListTabs(item) {
      const index = this.linkFormListTabs.indexOf(item)
      if (index > -1) {
        this.linkFormListTabs.splice(index, 1)
        this.MultiILinkFormList.splice(index, 1)
        this.$emit('updataMultiILinkFormList', this.MultiILinkFormList)
      }
      if (item.linkFormId === this.selectedTab && this.linkFormListTabs.length > 0) {
        this.selectedTab = this.linkFormListTabs[0].linkFormId
      }
    },
    // 选择变成高亮check
    MulticheckedFields(val) {
      const targetItem = this.linkFormListTabs.find((item) => item.linkFormId === this.selectedTab)
      const index = this.MultiILinkFormList.findIndex(
        (item) => item.formId === targetItem.linkFormId
      )
      if (this.MultiILinkFormList.length === 0 || index === -1) {
        const MultiItem = {
          formName: targetItem.linkFormName,
          businessType: targetItem.linkBusinessType,
          formId: targetItem.linkFormId,
          saasMark: targetItem.linkSaasMark,
          fieldList: [
            {
              attrName: this.multiAllFieldMap[val[0]].attrName,
              attr: val[0],
              required: 0,
              fieldType: this.multiAllFieldMap[val[0]].fieldType
            }
          ]
        }
        this.MultiILinkFormList.push(MultiItem)
      } else {
        // 再填加
        const addFieldList = val
          .filter((attr) => !this.MultiIList.find((item) => item.attr === attr))
          .map((attr) => ({
            attrName: this.multiAllFieldMap[attr].attrName,
            attr,
            required: 0,
            fieldType: this.multiAllFieldMap[attr].fieldType
          }))
        if (addFieldList.length > 0) {
          this.MultiILinkFormList[index].fieldList.push(addFieldList[0])
        } else {
          // 遍历目标数组，将与 val 不匹配的元素放入新数组
          const newArray = this.MultiILinkFormList[index].fieldList.filter((item) =>
            val.includes(item.attr)
          )
          this.MultiILinkFormList[index].fieldList = newArray
        }
        // this.$set(, 'fieldList', [...restFieldList, ...addFieldList]);
      }
      this.$emit('updataMultiILinkFormList', this.MultiILinkFormList)
    },
    addLinkFormListHandler(item, isSelected) {
      // this.linkFormListTabs.push(item)
      if (isSelected) {
        // this.removeLinkWet(item)
      } else {
        this.linkFormListTabs.push(item)
      }
    },
    // 获取字段信息
    getAllFieldList(formAttr) {
      if (!formAttr) return
      const params = {
        appId: formAttr.linkAppId,
        menuId: formAttr.linkMenuId,
        formId: formAttr.linkFormId,
        saasMark: formAttr.linkSaasMark,
        businessType: formAttr.linkBusinessType
      }
      return getFieldCondition(params).then(({ result: { explainList } }) => {
        this.MultiAllFieldList = explainList
      })
    },
    searchHandler() {
      const search = this.searchText.trim()
      this.searchAllFields = this.MultiAllFieldList.filter((item) => {
        return !search || item.attrName.includes(search)
      })
    },
    getAttrName(attr) {
      const field = this.allFieldMap[attr] || {}
      return field.attrName || attr
    },
    getFieldTypeName(fieldType) {
      return (fieldsMap[fieldType] && fieldsMap[fieldType].attrName) || fieldType
    },
    // 删除方法
    deleteFieldHandler(index, row) {
      if (this.curLinkForm) {
        const linkIndex = this.linkFormListTabs.findIndex(
          (item) => item.linkFormId === this.selectedTab
        )
        this.MultiILinkFormList[linkIndex].fieldList.splice(index, 1)
        this.$emit('updataMultiILinkFormList', this.MultiILinkFormList)
      }
    },
    rowDropInit() {
      if (!this.$refs.rightTableRef) return
      const tbody = this.$refs.rightTableRef.bodyWrapper.querySelectorAll('tbody')[0]
      this.sortable = Sortable.create(tbody, {
        handle: '.move',
        delay: 10,
        swapThreshold: 0.5,
        animation: 150,
        // ghostClass: 'sortable-item__ghost',
        // forceFallback: true,
        scroll: true,
        // dragClass: 'stage-table__sort--drag',
        onEnd: ({ newIndex, oldIndex }) => {
          const currRow = this.fieldList.splice(oldIndex, 1)[0]
          this.fieldList.splice(newIndex, 0, currRow)
          this.$emit('update', this.fieldList)
        }
      })
      this.$once('hook:beforeDestroy', () => {
        this.sortable && this.sortable.destroy()
      })
    },
    // 判断当前字段是否可以选择必填（存在如描述文本之类的字段又希望显示又希望不必填）
    notAllowRequired(row) {
      return [10004].includes(row.fieldType)
    },
    /**
     * @description: 计算当前文本是否超出容器
     * @param {String} refName 相应 dom 的 ref 名
     * @return {Boolean} true 文本超出
     */
    singleEllipsis(refName) {
      const ref = this.$refs[refName]
      const dom = Array.isArray(ref) ? ref[0] : ref
      return dom && dom.offsetWidth < dom.scrollWidth
    }
  }
}
</script>

<style lang="scss" scoped>
.template_plan {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
}
.add-field-condition_top::after {
  position: absolute;
  display: block;
  width: 752px;
  margin-top: 39px;
  content: '';
  border-bottom: 1px solid $neutral-color-2;
}
.add-field-condition_top {
  display: flex;
  justify-content: space-between;
  height: 30px;
  margin: 0 24px;
  margin-top: 20px;
  &-tablist {
    font-size: 14px;
    color: $text-grey;
  }
  &-btn {
    padding: 3px 5px;
    margin-top: 12px;
    margin-left: 2px;
    font-size: 12px;
    color: $text-auxiliary;
    cursor: pointer;
    border: 1px solid $neutral-color-3;
    border-radius: 2px;
  }
}
.border-left {
  border-left: 1px solid $neutral-color-3;
}
:deep(.el-table::before) {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0px;
}
.add-field-condition {
  box-sizing: border-box;
  display: flex;
  // width: 100%;
  height: 500px;
  margin: 0px 24px;
  border-bottom: 1px solid $neutral-color-3;
  &__left {
    box-sizing: border-box;
    width: 200px;
    // border-right: 1px solid $neutral-color-3;
  }
  &__right {
    box-sizing: border-box;
    width: calc(100% - 200px);
    // border-right: 1px solid $neutral-color-3;
  }
  &__header {
    box-sizing: border-box;
    height: 48px;
    padding-top: 10px;
    padding-left: 20px;
    border-bottom: 1px solid $neutral-color-3;
    .header__title {
      height: 38px;
      font-size: 13px;
      line-height: 38px;
      color: $text-auxiliary;
    }
  }
  &__content {
    box-sizing: border-box;
    height: calc(100% - 48px);
    // border-left: 1px solid $neutral-color-3;
    & > .left__search {
      padding: 8px;
      // height: 32px;
    }
    & > .left__scrollbar {
      box-sizing: border-box;
      height: calc(100% - 40px);
      padding: 8px 0 12px 8px;
      margin-right: 10px;
      .left__checkbox {
        display: flex;
        flex-direction: column;
        // margin-right: 20px;
        .el-checkbox {
          margin-right: 12px;
          margin-bottom: 16px;
          &:last-child {
            margin-bottom: 0;
          }
          .left__checkbox--text {
            @include singleline-ellipsis;
            display: inline-block;
            width: 100%;
            // overflow: hidden;
            vertical-align: bottom; // inline-block + overflow: hidden 会导致 baseline 下移，而引起不对齐
          }
        }
      }
    }
    .right__table {
      &--operate {
        .el-button {
          padding-top: 0;
          padding-bottom: 0;
          font-size: 20px;
          color: $text-auxiliary;
        }
      }
      &--sort {
        color: $text-auxiliary;
      }
    }
  }
}
.active {
  color: $brand-color-5 !important;
}
.active::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  display: block;
  height: 3px;
  content: '';
  background-color: $brand-color-5; /* 高亮状态下的横杠颜色 */
  transition: transform 0.3s; /* 过渡动画效果 */
}
.tabs__item {
  position: relative;
  box-sizing: border-box;
  display: inline-block;
  height: 39px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  line-height: 39px;
  color: $text-auxiliary;
  list-style: none;
  cursor: pointer;
  user-select: none; /* 禁止选择文本 */
}
.delete-icon {
  margin-left: 5px;
  color: $text-grey;
  visibility: hidden; /* 初始状态隐藏删除图标 */
}
.delete-icon .el-icon-close:hover {
  color: $text-grey;
  background-color: $bg-primary;
  border-radius: 2px;
}
.tabs__item:hover .delete-icon,
.tabs__item.active .delete-icon,
.delete-icon:hover {
  visibility: visible; /* 鼠标经过时显示删除图标 */
}
.LinkControl_template-tablist-scroll {
  display: block;
  width: 640px;
  overflow-x: auto;
  white-space: nowrap;
}
.LinkControl_tip {
  padding: 12px 16px;
  margin: 30px 24px;
  font-size: 14px;
  color: $text-auxiliary;
  background: $neutral-color-1;
  border-radius: 4px;
}
</style>

<style lang="scss">
.add-field-condition {
  &__content > .left__scrollbar {
    // 过长省略
    .left__checkbox .el-checkbox .el-checkbox__label {
      width: calc(100% - 26px);
    }
  }
}
</style>
