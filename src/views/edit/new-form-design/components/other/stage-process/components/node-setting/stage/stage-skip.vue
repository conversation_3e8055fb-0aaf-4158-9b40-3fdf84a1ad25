<!-- 设置阶段流程跳转设置 -->
<template>
  <div class="stage-skip">
    <stage-setting-block :required="required" :title="$t('stageProcess.stageProcessSkipSetting')">
      <el-checkbox
        v-model="allowSkipStage"
        class="stage-skip__check"
        :disabled="readOnly"
        :false-label="0"
        :true-label="1"
        >{{ $t('stageProcess.allowSkipThisStage') }}</el-checkbox
      >
      <el-checkbox
        v-model="enableAutoJump"
        class="stage-skip__check"
        :disabled="readOnly"
        :false-label="0"
        :true-label="1"
        >{{ $t('stageProcess.enableAutoJump') }}</el-checkbox
      >
    </stage-setting-block>
  </div>
</template>

<script>
import stageSettingBlock from '../stage-setting-block'

export default {
  name: 'StageSkip',

  components: {
    StageSettingBlock: stageSettingBlock
  },

  mixins: [],

  props: {
    required: {
      type: Boolean,
      default: false
    },
    // 节点详情
    nodeData: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {
    allowSkipStage: {
      get() {
        return this.nodeData.allowSkipStage
      },
      set(val) {
        // 防止初始的时候属性不存在
        this.$set(this.nodeData, 'allowSkipStage', val)
      }
    },
    enableAutoJump: {
      get() {
        return this.nodeData.enableAutoJump
      },
      set(val) {
        // 防止初始的时候属性不存在
        this.$set(this.nodeData, 'enableAutoJump', val)
      }
    }
  },

  methods: {
    validate() {
      if (
        this.required &&
        [this.allowSkipStage, this.enableAutoJump].every((item) => typeof item === 'undefined')
      ) {
        this.$message.error(
          this.$t('rule.require', { attr: this.$t('stageProcess.stageProcessSkipSetting') })
        )
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.stage-skip {
  &__check {
    margin-top: 12px;
  }
  &__check:first-child {
    margin-top: 0px;
  }
}
</style>
