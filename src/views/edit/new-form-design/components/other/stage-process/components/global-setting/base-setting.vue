<!-- 阶段设置 -->
<template>
  <div class="base-setting">
    <div class="setting-title">
      <span v-if="required" class="setting-title__require">*</span>
      <span class="setting-title__text">{{ $t('stageProcess.stageSetting') }}</span>
    </div>
    <el-form
      ref="settingRef"
      class="setting-content"
      label-width="0"
      :model="processData"
      :rules="settingRules"
    >
      <el-form-item class="setting-content__item" prop="stageUnit">
        <span>{{ $t('stageProcess.stageStayUnit') }}:</span>
        <el-select
          v-model="stageUnit"
          class="item-content--unit-select"
          :placeholder="$t('placeholder.choosePls', { attr: '' })"
        >
          <el-option
            v-for="unit in stageUnits"
            :key="unit.value"
            :label="unit.text"
            :value="unit.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="stageAllowScheduled" class="setting-content__item" prop="executionMode">
        <span>阶段任务按</span>
        <el-select
          v-model="workMode"
          class="item-content--execution-select"
          :disabled="true"
          :placeholder="$t('placeholder.choosePls', { attr: '' })"
        >
          <el-option
            v-for="unit in workModes"
            :key="unit.value"
            :label="unit.text"
            :value="unit.value"
          >
          </el-option>
        </el-select>
        <span>进行设置</span>
        <i
          v-tooltip.html.right-end="
            workMode === 1 ? $t('stageProcess.stageWorkMode') : $t('stageProcess.timeWorkMode')
          "
          class="el-icon-question"
          style="color: #ff8e3d"
        ></i>
      </el-form-item>
      <el-form-item class="setting-content__item" prop="allowBack">
        <el-checkbox
          v-model="allowBack"
          class="item-content--checkbox"
          :false-label="0"
          :true-label="1"
          >{{ $t('stageProcess.allowRollback') }}</el-checkbox
        >
        <i
          v-tooltip.html.right-end="$t('stageProcess.allowRollbackTip')"
          class="el-icon-question"
          style="color: #ff8c2e"
        ></i>
      </el-form-item>
      <el-form-item class="setting-content__item" prop="allowNewSelect">
        <el-checkbox
          v-model="allowNewSelect"
          class="item-content--checkbox"
          :false-label="0"
          :true-label="1"
          >{{ $t('stageProcess.allowChooseStage') }}</el-checkbox
        >
        <i
          v-tooltip.html.right-end="$t('stageProcess.allowChooseStageTip')"
          class="el-icon-question"
          style="color: #ff8c2e"
        ></i>
        <member-input
          v-if="allowNewSelect"
          v-model="allowNewSelectUser"
          class="item-content--member-input"
          :text="$t('stageProcess.addSetUser')"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import memberInput from '@/components/person/member-input'

import { mapGetters } from 'vuex'

export default {
  name: 'BaseSetting',

  components: {
    MemberInput: memberInput
  },

  mixins: [],

  props: {
    processData: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {
    ...mapGetters('stageProcessDesign', ['stageAllowScheduled']),

    settingRules() {
      return {}
    },
    stageUnits() {
      return [
        { text: this.$t('unit.hour'), value: 1 },
        { text: this.$t('unit.day'), value: 2 },
        { text: this.$t('unit.week'), value: 3 },
        { text: this.$t('unit.month'), value: 4 }
      ]
    },
    workModes() {
      return [
        { text: '指定时间执行任务', value: 2 },
        { text: '各阶段固定执行任务', value: 1 }
      ]
    },
    // 阶段停留单位
    stageUnit: {
      get() {
        return this.processData.stageUnit
      },
      set(val) {
        this.$set(this.processData, 'stageUnit', val)
      }
    },
    // 阶段执行模式
    workMode: {
      get() {
        return this.processData.workMode || 2
      },
      set(val) {
        this.$set(this.processData, 'workMode', val)
      }
    },
    // 允许回退阶段
    allowBack: {
      get() {
        return this.processData.allowBack
      },
      set(val) {
        this.$set(this.processData, 'allowBack', val)
      }
    },
    // 允许新建选择
    allowNewSelect: {
      get() {
        return this.processData.allowNewSelect
      },
      set(val) {
        this.$set(this.processData, 'allowNewSelect', val)
      }
    },
    // 新建选择人员
    allowNewSelectUser: {
      get() {
        return this.processData.allowNewSelectUser
      },
      set(val) {
        this.$set(this.processData, 'allowNewSelectUser', val)
      }
    }
  },

  watch: {
    allowNewSelect(val, oldVal) {
      // checkbox 切换时置空
      if (!val) this.allowNewSelectUser = []
    }
  },

  mounted() {},

  methods: {
    // 校验
    validate() {
      if (!this.$refs.settingRef) return Promise.resolve()
      return new Promise((resolve, reject) => {
        this.$refs.settingRef.validate((res) => {
          if (res) {
            resolve()
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>

<style lang="scss">
.base-setting .setting-content__item .el-form-item__content {
  line-height: 1;
}
</style>

<style lang="scss" scoped>
.base-setting {
  .setting-title {
    padding-top: 5px;
    font-size: 14px;
    line-height: 14px;
    &__require {
      color: $error-color-5;
    }
    &__text {
      font-weight: bold;
      color: $text-main;
    }
  }
  .setting-content__item {
    margin-top: 10px;
    margin-bottom: 0px;
    .item-content--checkbox {
      height: 16px;
    }
    .item-content--unit-select {
      width: 90px;
    }
    .item-content--execution-select {
      width: 200px;
    }
    .item-content--member-input {
      width: 476px;
      height: 80px;
    }
  }
}
</style>
