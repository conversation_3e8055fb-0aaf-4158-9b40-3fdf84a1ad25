<!-- 阶段信息展示设置 -->
<template>
  <div class="show-setting">
    <div class="setting-title">
      <span v-if="required" class="setting-title__require">*</span>
      <span class="setting-title__text">{{ $t('stageProcess.stageInfoShow') }}</span>
      <i
        v-tooltip.html.right-end="$t('stageProcess.stageInfoShowTip')"
        class="el-icon-question"
        style="color: #ff8c2e"
      ></i>
    </div>
    <el-form
      ref="settingRef"
      class="setting-content"
      label-width="0"
      :model="processData"
      :rules="settingRules"
    >
      <el-form-item class="setting-content__item" prop="showTimeUse">
        <el-checkbox v-model="showTimeUse" class="item-content" :false-label="0" :true-label="1">{{
          $t('stageProcess.stageUsedTime')
        }}</el-checkbox>
      </el-form-item>
      <el-form-item class="setting-content__item" prop="showTimeStay">
        <el-checkbox v-model="showTimeStay" class="item-content" :false-label="0" :true-label="1">{{
          $t('stageProcess.stageStayTime')
        }}</el-checkbox>
      </el-form-item>
      <el-form-item class="setting-content__item" prop="showWorkCompletion">
        <el-checkbox
          v-model="showWorkCompletion"
          class="item-content"
          :false-label="0"
          :true-label="1"
          >{{ $t('stageProcess.taskCompleteStatus') }}</el-checkbox
        >
      </el-form-item>
      <el-form-item class="setting-content__item" prop="showAverageCompletionTime">
        <el-checkbox
          v-model="showAverageCompletionTime"
          class="item-content"
          :false-label="0"
          :true-label="1"
          >{{ $t('stageProcess.averageCompanyCompleteTime') }}</el-checkbox
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ShowSetting',

  components: {},

  mixins: [],

  props: {
    processData: {
      type: Object,
      default: () => ({})
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {}
  },

  computed: {
    settingRules() {
      return {}
    },
    // 阶段用时
    showTimeUse: {
      get() {
        return this.processData.showTimeUse
      },
      set(val) {
        this.$set(this.processData, 'showTimeUse', val)
      }
    },
    // 阶段停留时长
    showTimeStay: {
      get() {
        return this.processData.showTimeStay
      },
      set(val) {
        this.$set(this.processData, 'showTimeStay', val)
      }
    },
    // 任务完成情况
    showWorkCompletion: {
      get() {
        return this.processData.showWorkCompletion
      },
      set(val) {
        this.$set(this.processData, 'showWorkCompletion', val)
      }
    },
    // 公司平均完成时长
    showAverageCompletionTime: {
      get() {
        return this.processData.showAverageCompletionTime
      },
      set(val) {
        this.$set(this.processData, 'showAverageCompletionTime', val)
      }
    }
  },

  watch: {},

  mounted() {},

  methods: {
    // 校验
    validate() {
      if (!this.$refs.settingRef) return Promise.resolve()
      return new Promise((resolve, reject) => {
        this.$refs.settingRef.validate((res) => {
          if (res) {
            resolve()
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>

<style lang="scss">
.show-setting .setting-content__item .el-form-item__content {
  line-height: 1;
}
</style>

<style lang="scss" scoped>
.show-setting {
  .setting-title {
    padding-top: 5px;
    font-size: 14px;
    line-height: 14px;
    &__require {
      color: $error-color-5;
    }
    &__text {
      font-weight: bold;
      color: $text-main;
    }
  }
  .setting-content__item {
    margin-top: 10px;
    margin-bottom: 0px;
    // height: 30px;
    .item-content {
      height: 16px;
    }
  }
}
</style>
