<!--
 * @Description: 添加字段映射
-->
<template>
  <el-dialog
    :before-close="cancel"
    class="merge-association-set-dialog"
    :title="$t('formDesign.customerTableFieldMapping')"
    :visible.sync="dialogShow"
    width="665px"
  >
    <div class="tip">
      <span class="tip__left">{{ $t('message.confirmTitle') }}：</span>
      <span>
        {{ $t('formDesign.customerTableFieldMappingTips') }}
      </span>
    </div>
    <div v-loading="loading" class="el-operate-layout">
      <el-button
        class="el-operate-button"
        :disabled="tabIndex === 6"
        size="small"
        type="text"
        @click="addTab(editableTabsValue)"
      >
        <i class="el-icon-plus"></i>
        {{ $t('marketManage.linkAge') }}
      </el-button>
      <el-tabs
        v-model="editableTabsValue"
        :closable="editableTabs.length > 1"
        type="card"
        @tab-click="handleClick"
        @tab-remove="removeTab"
      >
        <template v-if="editableTabs.length">
          <el-tab-pane
            v-for="(tabItem, index) in editableTabs"
            :key="tabItem.title + '' + index"
            :label="tabItem.title"
            :name="tabItem.title"
          >
            <!-- 联动表单 -->
            <p class="mg-b-10">{{ $t('formDesign.linkedForm') }}</p>
            <div class="mg-b-10 tree-out" style="width: 230px">
              <AppFormSelectTree
                :app-id.sync="tabItem.sourceAppId"
                :app-list="appList"
                :business-type.sync="tabItem.sourceBusinessType"
                :clearable-input="false"
                :current-business-type="currentBusinessType"
                :current-form-id="currentFormId"
                :current-tab-index="currentTabIndex"
                :form-id.sync="tabItem.sourceFormId"
                :is-convert-set="true"
                :is-product-rely="true"
                :menu-id.sync="tabItem.sourceMenuId"
                :multiple-form="true"
                :rely-list="editableTabs"
                :saas-mark.sync="tabItem.sourceSaasMark"
                @change="sourceFormChange($event, tabItem)"
              >
              </AppFormSelectTree>
            </div>
            <!-- 字段数据联动 -->
            <div>
              <p>
                {{ $t('formDesign.fieldDataLinkage') }}
                <!-- <span class="sub-field-tip">
                  <i class="icon el-icon-warning"></i>
                  {{$t('marketManage.linkAgeTip')}}
                </span> -->
              </p>
              <template v-if="fieldList.length && tabItem.sourceFormId">
                <el-dropdown trigger="click">
                  <el-button class="el-dropdown-link" type="text">
                    <i class="el-icon-plus"></i>
                    {{ $t('operation.addField') }}
                  </el-button>
                  <!-- 可添加的字段选项 -->
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="item in fieldList"
                      :key="item.attr"
                      :disabled="
                        tabItem.subRelyFieldList.some(
                          (el) => el && el.clueRule && el.clueRule.attr === item.attr
                        )
                      "
                      @click.native="addSubRelyField(item)"
                    >
                      {{ item.attrName }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- 字段显示开始 -->
                <div v-for="(subRelyItem, i) in tabItem.subRelyFieldList" :key="i" class="mg-b-10">
                  <!-- 左边的字段 -->
                  <el-input
                    v-if="subRelyItem"
                    :disabled="
                      subRelyItem && subRelyItem.clueRule && subRelyItem.clueRule.attr === 'text_1'
                    "
                    readonly
                    style="width: 230px; margin-right: 8px"
                    :value="subRelyItem && subRelyItem.clueRule && subRelyItem.clueRule.attrName"
                  >
                  </el-input>
                  {{ $t('formDesign.linkageDisplay') }}
                  <!-- 右边的字段 -->
                  <el-select
                    v-if="subRelyItem && subRelyItem.customerRule && subRelyItem.clueRule"
                    v-model="subRelyItem.customerRule"
                    :disabled="subRelyItem.customerRule.attr === 'text_1'"
                    filterable
                    :placeholder="$t('marketManage.linedForm')"
                    style="width: 230px; margin: 0 8px"
                    value-key="attr"
                    @change="(val) => subTargetFieldChange(val, i, subRelyItem.targetFieldOptions)"
                    @visible-change="(val) => linkageSelectClick(val, i, subRelyItem.clueRule)"
                  >
                    <el-option
                      v-for="item in subRelyItem.targetFieldOptions"
                      :key="item.attr + i"
                      :disabled="
                        tabItem.subRelyFieldList.some(
                          (ele) => ele && ele.customerRule && ele.customerRule.attr === item.attr
                        )
                      "
                      :label="item.attrName"
                      :value="item"
                    ></el-option>
                  </el-select>
                  {{ $t('marketManage.values') }}
                  <!-- 删除字段 -->
                  <el-button
                    :disabled="
                      subRelyItem && subRelyItem.clueRule && subRelyItem.clueRule.attr === 'text_1'
                    "
                    plain
                    size="mini"
                    style="padding: 7px 5px"
                    type="danger"
                    @click="deleteSubRelyField(i)"
                  >
                    <i class="el-icon-delete"></i>
                  </el-button>
                </div>
                <!-- 字段显示结束 -->
              </template>
              <template v-else>
                <div class="dropdown-link-tip">
                  {{ $t('marketManage.linedEmpty') }}
                </div>
              </template>
            </div>
          </el-tab-pane>
        </template>
      </el-tabs>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">{{ $t('operation.cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('operation.confirm') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
// mixin
import dialogMixin from '@/mixin/dialog'
import AppFormSelectTree from './form-select-tree'
import { getFormAllList } from '@/api/form'
import i18n from '@/lang'
import { Message } from 'element-ui'

import {
  getFieldConvert,
  saveFieldConvert,
  getFormFields,
  getConvertList
} from '@/api/market-manage'

export default {
  name: 'AddFieldMappingDialog',
  components: {
    AppFormSelectTree
  },
  mixins: [dialogMixin],
  props: {
    layoutModel: {
      type: Array
    }
  },
  data() {
    return {
      loading: false,
      editableTabsValue: i18n.t('formDesign.linkedForm') + '1', // tab选中的那个值
      tabIndex: 1, // 总tab的数量
      currentTabIndex: 0, // 当前选中的tab的index
      editableTabs: [], // 整个tab以及下面表单的值
      appList: [], // form-select-tree的值
      fieldList: [], // 点击添加表单出现的下拉选项
      fieldConvertIds: {} // 保存后端返回的线索转换的id
    }
  },
  computed: {
    currentFormId() {
      return this.$route.query.formId
    },
    // 当前设计表单业务
    currentBusinessType() {
      return Number(this.$route.query.businessType)
    }
  },
  created() {
    this.init()
  },
  methods: {
    // 获取字段映射规则
    getFieldConvert() {
      this.loading = true
      getFieldConvert({
        originFormId: +this.$route.query.formId
      })
        .then(({ result }) => {
          this.fieldConvertIds[result.formId] = result.id
          let currentIndex
          // 获取当前的tab
          if (result.formIds) {
            // 获取当前的tab
            currentIndex = result.formIds.indexOf(result.formId)
            this.currentTabIndex = currentIndex
          } else {
            this.currentTabIndex = 0
          }
          if (result.formIds) {
            // 初始化 this.editableTabs
            result.formIds.forEach((item, index) => {
              this.editableTabs.push({
                sourceMenuId: '',
                sourceAppId: '',
                displayField: '',
                targetField: '',
                linkField: '',
                sourceSaasMark: 2,
                sourceBusinessType: 0,
                subRelyFieldList: [],
                title: this.$t('formDesign.linkedForm') + `${index + 1}`
              })
            })
            // tab数量赋值
            this.tabIndex = result.formIds.length
          } else {
            this.editableTabs.push({
              sourceMenuId: '',
              sourceAppId: '',
              displayField: '',
              targetField: '',
              linkField: '',
              sourceSaasMark: 2,
              sourceBusinessType: 0,
              subRelyFieldList: [],
              title: this.$t('formDesign.linkedForm') + '1'
            })
          }

          result.convertRule.forEach((item) => {
            this.editableTabs[this.currentTabIndex].subRelyFieldList.push({
              clueRule: item.left,
              customerRule: item.right
            })
          })

          this.getFormAllList(currentIndex, result.formIds)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 获取联动表单
    getFormAllList(currentIndex, formIds) {
      getFormAllList({
        clueConvert: 1,
        formId: this.$route.query.formId
      })
        .then(({ result: { appList } }) => {
          if (appList && appList.length > 0) {
            this.appList = appList
            // 获取tabs
            formIds.forEach((item, index) => {
              appList[0].formList.forEach((el) => {
                if (item === el.formId) {
                  this.editableTabs[index] = Object.assign(this.editableTabs[index], {
                    sourceAppId: el.appId,
                    sourceMenuId: el.menuId,
                    sourceFormId: el.formId,
                    sourceSaasMark: el.saasMark,
                    sourceBusinessType: el.businessType,
                    title: el.name
                  })
                }
              })
            })
            if (formIds.length) {
              // 如果联动表单存在的话 就遍历左边的字段拿到右边的下拉选项
              this.editableTabsValue = this.editableTabs[currentIndex].title
              this.editableTabs[currentIndex].subRelyFieldList.forEach((el) => {
                getConvertList({
                  appId: this.editableTabs[currentIndex].sourceAppId,
                  menuId: this.editableTabs[currentIndex].sourceMenuId,
                  formId: this.editableTabs[currentIndex].sourceFormId,
                  saasMark: this.editableTabs[currentIndex].sourceSaasMark,
                  businessType: this.editableTabs[currentIndex].sourceBusinessType,
                  fieldType: el.clueRule.fieldType,
                  attr: el.clueRule.attr
                })
                  .then((res) => {
                    this.$set(el, 'targetFieldOptions', res.result.formExplainList)
                  })
                  .catch(() => {})
              })
            }
          }
        })
        .catch(() => {})
    },
    //  获取支持数据联动的字段类型
    getFormFields() {
      // originType: 1为转换已有客户 2为字段映射
      getFormFields({
        formId: +this.$route.query.formId,
        originType: 2
      })
        .then(({ result }) => {
          this.fieldList = result.fieldEntitys
        })
        .catch(() => {})
    },
    // 保存字段映射规则
    saveFieldConvert() {
      const formIds = this.editableTabs.map((item) => item.sourceFormId)
      const customerFormId = formIds[this.currentTabIndex]
      const convertRule = []
      this.editableTabs[this.currentTabIndex].subRelyFieldList.forEach((item) => {
        const obj = {}
        const key = item.clueRule.attr
        const value = item.customerRule.attr
        obj[key] = value
        convertRule.push(obj)
      })
      saveFieldConvert({
        clueConvert: 1,
        id: this.fieldConvertIds[customerFormId],
        targetFormId: customerFormId,
        targetFormIds: formIds,
        convertRule,
        originFormId: this.$route.query.formId
      })
        .then(() => {
          Message({
            message: this.$t('message.saveSuccess'),
            type: 'success'
          })
          this.dialogShow = false
        })
        .catch(() => {})
    },
    // 循环得到每个的下拉选项
    getLinkFormDisplayAttr() {
      this.editableTabs[this.currentTabIndex].subRelyFieldList.forEach((el) => {
        if (this.editableTabs[this.currentTabIndex].sourceFormId) {
          getConvertList({
            appId: this.editableTabs[this.currentTabIndex].sourceAppId,
            menuId: this.editableTabs[this.currentTabIndex].sourceMenuId,
            formId: this.editableTabs[this.currentTabIndex].sourceFormId,
            saasMark: this.editableTabs[this.currentTabIndex].sourceSaasMark,
            businessType: this.editableTabs[this.currentTabIndex].sourceBusinessType,
            fieldType: el.clueRule.fieldType,
            attr: el.clueRule.attr
          })
            .then((res) => {
              this.$set(el, 'targetFieldOptions', res.result.formExplainList)
            })
            .catch(() => {})
        }
      })
    },
    // 添加字段的时候 点击右边下拉请求下拉数据
    linkageSelectClick(val, index, clueRule) {
      // 如果已经存在就不请求
      if (
        val &&
        !this.editableTabs[this.currentTabIndex].subRelyFieldList[index].targetFieldOptions
      ) {
        // 获取表单联动的字段列表
        getConvertList({
          appId: this.editableTabs[this.currentTabIndex].sourceAppId,
          menuId: this.editableTabs[this.currentTabIndex].sourceMenuId,
          formId: this.editableTabs[this.currentTabIndex].sourceFormId,
          saasMark: this.editableTabs[this.currentTabIndex].sourceSaasMark,
          businessType: this.editableTabs[this.currentTabIndex].sourceBusinessType,
          fieldType: clueRule.fieldType,
          attr: clueRule.attr
        })
          .then((res) => {
            this.$set(
              this.editableTabs[this.currentTabIndex].subRelyFieldList[index],
              'targetFieldOptions',
              res.result.formExplainList
            )
          })
          .catch(() => {})
      }
    },
    addTab(targetName) {
      const newTabName = ++this.tabIndex + ''
      const titleName = this.$t('formDesign.linkedForm') + newTabName
      this.editableTabs.push({
        sourceMenuId: '', // 表单id
        sourceAppId: '', // 应用id
        displayField: '', // 联动显示的字段
        targetField: '', // 联动字段需要等于的字段
        linkField: '', // 联动的字段
        sourceFormId: '',
        sourceSaasMark: 2,
        sourceBusinessType: 0,
        subRelyFieldList: [],
        title: titleName
      })
      this.editableTabsValue = titleName
      // 确保当前看到的数据也是对应的tab的
      this.currentTabIndex = newTabName - 1
    },
    // 切换tab
    handleClick(tab, event) {
      this.currentTabIndex = Number(tab.index)
      const customerFormId = this.editableTabs[+tab.index].sourceFormId
      if (
        this.editableTabs[this.currentTabIndex].sourceFormId &&
        !this.editableTabs[this.currentTabIndex].subRelyFieldList.length
      ) {
        this.loading = true
        getFieldConvert({
          originFormId: +this.$route.query.formId,
          targetFormId: customerFormId
        })
          .then(({ result }) => {
            this.fieldConvertIds[result.formId] = result.id
            result.convertRule.forEach((item) => {
              this.editableTabs[this.currentTabIndex].subRelyFieldList.push({
                clueRule: item.left,
                customerRule: item.right
              })
            })
            this.getLinkFormDisplayAttr()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    removeTab(targetName) {
      let deleteIndex = 0
      this.editableTabs.forEach((tab, index) => {
        if (targetName === tab.title) {
          deleteIndex = index
          this.editableTabs.splice(deleteIndex, 1)
          if (deleteIndex < this.currentTabIndex) {
            this.currentTabIndex = this.currentTabIndex - 1
          } else if (deleteIndex === this.currentTabIndex) {
            this.currentTabIndex = this.editableTabs.length - 1
          }
          for (let i = 0; i < this.editableTabs.length; i++) {
            if (!this.editableTabs[i].sourceFormId) {
              this.editableTabs[i].title = this.$t('formDesign.linkedForm') + (i + 1)
            }
          }
          this.editableTabsValue = this.editableTabs[this.currentTabIndex].title
        }
      })
      this.tabIndex--
    },
    // 当字段的右边变化时，把子字段置空
    subTargetFieldChange(attr, index, list) {
      this.$forceUpdate()
    },
    // 删除字段
    deleteSubRelyField(index) {
      this.editableTabs[this.currentTabIndex].subRelyFieldList.splice(index, 1)
    },
    // 添加字段
    addSubRelyField(item) {
      this.editableTabs[this.currentTabIndex].subRelyFieldList.push({
        clueRule: item,
        customerRule: {
          attr: '',
          attrName: ''
        }
      })
    },
    cancel() {
      this.$emit('dialogConfirmCancel')
    },
    submit() {
      if (this.editableTabs.length) {
        if (this.editableTabs.some((item) => item.sourceFormId === '')) {
          this.$message.error(this.$t('marketManage.completeLinkage'))
        } else if (!this.editableTabs[this.currentTabIndex].subRelyFieldList.length) {
          this.$message.error(this.$t('marketManage.addLinkage'))
        } else if (
          this.editableTabs[this.currentTabIndex].subRelyFieldList.some(
            (item) =>
              item.customerRule.attr === '' ||
              !item.targetFieldOptions ||
              !item.targetFieldOptions.length
          )
        ) {
          this.$message.error(this.$t('marketManage.fillLinkage'))
        } else {
          this.saveFieldConvert()
        }
      } else {
        this.$message.error(this.$t('marketManage.wrongLinkage'))
      }
    },
    // 联动表单修改
    sourceFormChange(val, tabItem) {
      this.loading = true
      const { childName } = val
      tabItem.title = childName
      this.editableTabsValue = childName
      getFieldConvert({
        originFormId: +this.$route.query.formId,
        targetFormId: val.formId
      })
        .then(({ result }) => {
          this.fieldConvertIds[result.formId] = result.id
          this.editableTabs[this.currentTabIndex].subRelyFieldList = []
          result.convertRule.forEach((item) => {
            this.editableTabs[this.currentTabIndex].subRelyFieldList.push({
              clueRule: item.left,
              customerRule: item.right
            })
          })
          this.getLinkFormDisplayAttr()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    init() {
      this.getFormFields()
      this.getFieldConvert()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.dropdown-link-tip {
  font-size: 14px;
  line-height: 1.4;
  color: $brand-color-5;
}
.merge-association-set-dialog {
  .tip {
    display: flex;
    padding: 10px;
    font-size: 13px;
    line-height: 25px;
    color: $text-auxiliary;
    background: $neutral-color-1;
    .tip__left {
      width: 50px;
    }
  }
  .tree-out {
    position: relative;
    .tree-mark {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 3333;
      cursor: not-allowed;
      background: $neutral-color-1;
      opacity: 0.5;
    }
  }
  .el-icon-warning {
    color: $brand-color-5;
  }
  .sub-field-tip {
    color: $text-grey;
  }
  .el-tabs {
    .el-tabs__header {
      width: 80%;
    }
  }
  .el-operate-layout {
    position: relative;
    .el-operate-button {
      position: absolute;
      right: 0;
      z-index: 1;
    }
  }
  .sub_form {
    padding-left: 25px;
  }
}
</style>
