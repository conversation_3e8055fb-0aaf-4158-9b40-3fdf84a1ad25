<!-- eslint vue/no-mutating-props: 1 -->

<!--/*-->
<!--*  DESC: 多版本下拉框关联设置 (会根据当前页面设置字段 进行关联)-->
<!--* */-->
<template>
  <el-dialog
    :before-close="cancel"
    :title="$t('formDesign.relationalSettings')"
    :visible.sync="isActive"
    width="600px"
  >
    <div v-if="hasFieldMap" class="tip">
      <i class="el-icon-warning"></i>{{ $t('formDesign.optionAssociationTip2') }}
    </div>
    <el-container class="pl-10">
      <el-aside style="margin-left: 10px" width="100px">
        <p class="mg-b-10">{{ $t('formDesign.version') }}</p>
        <div class="radio-div">
          <template v-for="(item, index) in enableVerList">
            <p
              :key="index"
              :class="{ active: index === versionIndex }"
              @click="getOptionList(item, index)"
            >
              {{ item.versionName }}
            </p>
          </template>
        </div>
      </el-aside>
      <el-aside style="margin-left: 10px" width="200px">
        <p class="mg-b-10">{{ $t('formDesign.option') }}</p>
        <div class="radio-div">
          <template v-for="(item, index) in fieldListData">
            <p
              v-if="!item.editHide"
              :key="index"
              :class="{ active: index === fieldIndex }"
              @click="getFieldMapList(item, index)"
            >
              {{ item.text }}
            </p>
          </template>
        </div>
      </el-aside>
      <el-main style="background-color: white">
        <p class="mg-b-10">{{ $t('business.related', { attr: $t('nouns.field') }) }}</p>
        <div class="radio-div">
          <span v-if="fieldList.length === 0" class="widget-list-tip">{{
            $t('formDesign.adjustHint')
          }}</span>
          <el-checkbox-group v-model="selectFieldList" @change="fieldChange">
            <template v-for="(item, index) in newFieldList">
              <el-checkbox
                v-if="item.isForbidden !== 0 || selectFieldList.includes(item.attr)"
                :key="index"
                :label="item.attr"
                style="margin-left: 10px"
                >{{ item.attrName }}</el-checkbox
              >
            </template>
          </el-checkbox-group>
        </div>
      </el-main>
    </el-container>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">{{ $t('operation.cancel') }} </el-button>
      <el-button type="primary" @click="submit">{{ $t('operation.confirm') }} </el-button>
    </span>
  </el-dialog>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import unlinkFiledEnum from './unlink-filed.js'
import { mapGetters } from 'vuex'
import { getProcess } from '@/api/stage-process-design'
import { setMultiLinkOptionsAndDefaultVer } from '@/api/stage-process-multi'

export default {
  name: 'MultiVerAssociatedDialog',
  inject: ['businessType'],
  props: {
    // 单选按钮组单选按钮
    radioList: {
      type: Array,
      default: () => []
    },
    isActive: {
      type: Boolean,
      default: false
    },
    allFields: {
      type: Array,
      default: () => []
    },
    // 关联数据列表
    fieldList: {
      type: Array,
      default: () => []
    },
    // 存在关联字段
    hasFieldMap: {
      type: Boolean,
      default: false
    },
    enableVerList: {
      type: Array,
      default: () => []
    },
    defaultCheckedIndex: {
      type: Number,
      default: null
    },
    defaultStageProcessId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      selectFieldList: [],
      fieldIndex: '',
      fieldListData: [],
      // 阶段推进器多版本
      versionIndex: 0
    }
  },
  computed: {
    ...mapGetters(['formInfo']),
    formAttr() {
      return {
        appId: this.formInfo.formAttr.appId,
        menuId: this.formInfo.formAttr.menuId,
        formId: this.formInfo.formId,
        saasMark: this.formInfo.formAttr.saasMark,
        businessType: this.formInfo.formAttr.businessType
      }
    },
    fieldAttrMap() {
      const res = {}
      this.allFields.forEach((item) => {
        const { attr } = item
        res[attr] = item
      })
      return res
    },
    show: {
      get() {
        return this.isActive
      },
      set(val) {
        this.$emit('update:isActive', val)
      }
    },
    selectFieldRelys() {
      // 被关联但处于当前字段上方的字段
      const res = []
      const itemOption = this.radioList[this.fieldIndex]
      if (itemOption) {
        const fieldMap = itemOption.fieldMap || []
        fieldMap.forEach((item) => {
          const fieldAttrList = this.fieldList.map((val) => val.attr)
          if (!fieldAttrList.includes(item)) {
            const field = this.fieldAttrMap[item]
            if (field) {
              res.push(field)
            }
          }
        })
      }
      return res
    },
    filterFieldList() {
      // 过滤固定类型
      return this.fieldList.filter((item) => {
        const filters = [10013, 10014, 10015, 10030]
        return !filters.includes(item.fieldType)
      })
    },
    newFieldList() {
      // 原代码注释
      // return this.selectFieldRelys.concat(this.filterFieldList)
      const businessType = String(this.businessType)
      /**
       * @description 不允许 选项关联设置 字段过滤
       * @param {Object} [unlinkFiledEnum] 不允许的字段
       */
      function unlinkFiled(filed) {
        return (
          !unlinkFiledEnum[businessType] || !unlinkFiledEnum[businessType].includes(filed.saasAttr)
        )
      }
      const newFieldList = this.selectFieldRelys.concat(this.filterFieldList)
      return newFieldList.filter(unlinkFiled)
    }
  },
  methods: {
    // 切换版本获取阶段列表
    getOptionList(item, index) {
      if (!item) return
      this.versionIndex = index
      const params = { id: this.enableVerList[index].id, type: 2, ...this.formAttr }
      getProcess(params).then((res) => {
        this.fieldListData = res.result.stageListPojoList.map((item, index) => ({
          fieldMap: item.linkOption || [],
          isOther: 0,
          id: item.id,
          isDefault: item.isDefault,
          text: item.name,
          value: '' + index
        }))
        // 这里需要重置一下选项焦点，切换版本后选项失焦
        this.fieldIndex = ''
        this.selectFieldList = []
      })
    },
    // 选项切换
    getFieldMapList(item, index) {
      if (!item) return
      // this.fieldListData.forEach((data, dataIndex) => {
      //   data.active = false
      // })
      this.selectFieldList = item.fieldMap
      // item.active = true
      this.fieldIndex = index
    },
    cancel() {
      this.show = false
      this.$emit('dialogConfirmCancel')
    },
    submit() {
      if (this.fieldIndex === '') {
        this.$message.error(this.$t('stageProcess.chooseOptionPlz'))
        return
      }
      const isActiveVersion =
        this.enableVerList[this.versionIndex].id === this.defaultStageProcessId
      const params = {
        ...this.formAttr,
        // 这里只传当前版本的配置，批量修改接口当单个用
        stageDefaultAndLinkOptionSavePojoList: [
          {
            stageProcessId: this.enableVerList[this.versionIndex].id,
            stageList: this.fieldListData.map((item, index) => ({
              id: item.id,
              // 版本和默认值索引相同，则该阶段被设定为自定义默认值
              isDefault: item.isDefault,
              linkOption: item.fieldMap
            }))
          }
        ]
      }
      setMultiLinkOptionsAndDefaultVer(params).then((res) => {
        this.show = false
        this.$message({
          type: 'success',
          message: res.msg
        })
        if (isActiveVersion) {
          const stageIdFieldMapList =
            this.fieldListData?.map(({ fieldMap, id }) => [id, fieldMap]) || []

          const stageIdFieldMapObj = Object.fromEntries(stageIdFieldMapList)

          // 如果改动到当前版本的选项关联，这里则需要同步一下
          this.$emit('dialogConfirmSubmit', stageIdFieldMapObj)
        }
      })
    },
    init() {
      // 这里会隐藏一部分选项，默认选中显示出来的第一个
      let fieldListIndex = this.fieldListData.findIndex((item) => !item.editHide)
      if (fieldListIndex < 0) fieldListIndex = 0
      this.getFieldMapList(this.fieldListData[fieldListIndex], fieldListIndex) // 不存在的情况处理？
      const versionIndex = this.versionIndex
      this.getOptionList(this.enableVerList[versionIndex], versionIndex)
    },
    fieldChange() {
      const obj = this.fieldListData[this.fieldIndex]
      obj.fieldMap = this.selectFieldList
      this.$set(this.fieldListData, this.fieldIndex, obj)
      // this.fieldListData[this.fieldIndex].fieldMaps = this.selectFieldList
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.radio-div {
  height: 360px;
  margin-right: 10px;
  overflow: auto;
  border: 1px solid $neutral-color-3;
  p {
    padding: 10px;
    cursor: pointer;
  }
  p:hover {
    background: $bg-tag;
  }
  .active {
    background: $bg-tag;
  }
  .el-checkbox {
    width: 90%;
    margin-top: 10px;
  }
  .el-checkbox__input {
    float: right;
  }
}
.widget-list-tip {
  padding: 5px;
  font-size: 12px;
  color: #989898;
}
.tip {
  position: relative;
  padding-left: 20px;
  margin-bottom: 15px;
  color: rgba(153, 153, 153, 100);
  .el-icon-warning {
    position: absolute;
    left: -3px;
    font-size: 16px;
    color: $warning-color-5;
  }
}
</style>
