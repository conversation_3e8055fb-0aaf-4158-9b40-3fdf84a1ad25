<template>
  <div v-loading="loading" class="unite-cnki-dialog">
    <!-- <span class="tip">
      {{ $t('notificationPush.tips') }}
      <br />
      {{ $t('duplicate.uniteP1') }}
      <br />
      {{ $t('duplicate.uniteP2') }}
    </span> -->
    <div v-for="setting in settingFieldsGet" :key="setting.type" class="unite-cnki">
      <span class="title">{{ setting['title'] }}</span>
      <el-tooltip effect="light" placement="right">
        <em class="em-color web-icon-question-circle web-iconfont"></em>

        <template slot="content">
          <!-- $text-auxiliary -->
          <div class="tip-text">
            <div v-if="setting['settingAttr'] === 'normalEnable'">
              <div>{{ setting['titleTip'].normalEnableRule1 }}</div>
              <div>{{ setting['titleTip'].normalEnableRule2 }}</div>
              <div>{{ setting['titleTip'].normalEnableRule3 }}</div>
            </div>
            <div v-else>
              <div>{{ setting['titleTip'] }}</div>
            </div>
          </div>
          <!-- $link-base-color-6 -->
          <span class="tip-review" @click="handleCheckingReview(setting['title'], setting)"
            >查看示例</span
          >
        </template>
      </el-tooltip>
      <div class="repeat-line-block"></div>
      <div v-if="setting['showSwitch']" class="content isOpen">
        <span class="open">{{ $t('duplicate.isOpen') }}</span>
        <el-switch
          v-model="setting.enable"
          :active-value="1"
          :inactive-value="0"
          @change="changeEnable(setting.enable)"
        >
        </el-switch>
      </div>
      <setting-table
        v-if="setting.showTable && initTable"
        :attr-keys="attrKeys"
        :attr-list="attrList"
        :contact-attr-list="contactAttrList"
        :contact-form-list="contactFormList"
        :current-business-attr="currentBusinessAttr"
        :dialog-title="setting.title"
        :form-info="formInfo"
        :rule-list-data="setting.tableData"
        :setting-attr="setting.settingAttr"
        @addTableData="handleAddTableData"
        @changeRuleListData="changeRuleListData"
        @changeTableData="changeTableData"
        @deleteTableData="deleteTableData"
      ></setting-table>

      <el-dialog
        :append-to-body="true"
        :title="$t('message.confirmTitle')"
        :visible.sync="dialogVisible"
        width="30%"
      >
        <span><i class="cancel_icon el-icon-warning"></i>{{ $t('duplicateNew.openTip') }}</span>
        <span slot="footer" class="dialog-footer">
          <el-button plain @click="cancelDialog">{{ $t('decisionTree.cancel') }}</el-button>
          <el-button plain type="primary" @click="confirmDialog">{{
            $t('decisionTree.confirm')
          }}</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 查重示例弹框 -->
    <CheckReviewDialog
      v-if="checkReviewDialogShow"
      :business-type-attr="currentBusinessAttr"
      :review-info="reviewInfo"
      :review-type="reviewType"
      :show.sync="checkReviewDialogShow"
      :title="reviewDialogTitle"
    ></CheckReviewDialog>
  </div>
</template>

<script>
import xbb from '@xbb/xbb-utils'
import settingTable from './components/setting-table.vue'
import { ruleFormRecheck, getCheckRuleList, saveSettingRuleNew } from '@/api/form-setting'
import CheckReviewDialog from './components/check-review-dialog.vue'
import { mapGetters } from 'vuex'
import {
  attrENum,
  getSaveKeys,
  getBaseSettingAttr,
  getCurrentBusinessAttr,
  transFormTableItemData,
  getAttrInfo,
  attrKeyName,
  attrSettingName
} from './duplicate.js'

export default {
  // 联合查重设置
  name: 'UniteCnki',
  components: {
    SettingTable: settingTable,
    CheckReviewDialog
  },
  props: {
    formInfo: {
      type: Object,
      default: () => ({})
    },
    dialogShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      attrRepeatArr: ['normalEnable', 'checkToolRule', 'noRepeatRule'],
      contactAttrList: [],
      contactFormList: [],
      dialogVisible: false,
      attrList: [], // 字段列表
      heaAttrs: [], //渲染的规则行数的字段，比如查客户，查线索，查联系人
      attrKeys: [], //渲染的表格的头部的字段
      settingFields: [
        {
          title: this.$t('duplicateNew.commonDuplicate'),
          enable: 0,
          titleTip: {}, //标题的提示
          showSwitch: true, //是否显示切换按钮
          showTable: false, //是否显示表格
          tableData: [],
          settingAttr: 'normalEnable', //表格渲染的数据对应的后端返回的数据的字段
          tipImgInfo: [
            //查看示例的图片信息
            {
              title: '查重工具查询数据',
              imgUrl: [
                'https://xbbpro.oss-cn-zhangjiakou.aliyuncs.com/xbbProPrd/ding0064986624e3b83b35c2f4657eb6378f/02276334205821146722/png/1706663637074710af8401566e5f612ddd4cdadac3e00.png'
              ]
            }
          ]
        },
        {
          title: this.$t('duplicateNew.seniorDuplicate'),
          enable: 0,
          titleTip: '',
          showSwitch: false,
          showTable: true,
          tableData: [],
          settingAttr: 'checkToolRule', //表格渲染的数据对应的后端返回的数据的字段
          tipImgInfo: [
            {
              title: '1.查重工具查询数据',
              imgUrl: [
                'https://xbbpro.oss-cn-zhangjiakou.aliyuncs.com/xbbProPrd/ding0064986624e3b83b35c2f4657eb6378f/02276334205821146722/png/170666377605404b396eb7f9ca7760dac94c3f1d11c91.png'
              ]
            },
            {
              title: '2.新建数据时自动查询',
              imgUrl: [
                'https://xbbpro.oss-cn-zhangjiakou.aliyuncs.com/xbbProPrd/ding0064986624e3b83b35c2f4657eb6378f/02276334205821146722/png/17066638162421d04d7993210f8779bd410b3efd32361.png'
              ]
            },
            {
              title: '3.线索转换重复提示',
              imgUrl: [
                'https://xbbpro.oss-cn-zhangjiakou.aliyuncs.com/xbbProPrd/ding0064986624e3b83b35c2f4657eb6378f/02276334205821146722/png/170666383582377d8f2bfec2931b70243aeed9c396e8f.png',
                'https://xbbpro.oss-cn-zhangjiakou.aliyuncs.com/xbbProPrd/ding0064986624e3b83b35c2f4657eb6378f/02276334205821146722/png/17066638557470f5802007399b98e55db9e3ba3e06a05.png'
              ]
            }
          ]
        }
      ],
      ruleListData: {}, //配置get接口返回的数据
      loading: true,
      initTable: false,
      ruleTips: this.$t('duplicate.improveDuplicate'),
      checkReviewDialogShow: false,
      reviewInfo: {},
      reviewType: '',
      newNoRepeatRule: []
    }
  },
  mounted() {
    this.init()
  },
  computed: {
    // 获取当前的业务attr
    currentBusinessAttr() {
      return getCurrentBusinessAttr(this.formInfo.businessType)
    },
    settingFieldsGet() {
      return this.feeType === 1 && Number(this.formInfo.businessType) === 301
        ? [this.settingFields[0]]
        : this.settingFields
    },
    ...mapGetters(['feeType'])
  },
  methods: {
    changeEnable(enable) {
      // 联系人单模板
      if (this.currentBusinessAttr === 'contact') {
        return
      }
      this.dialogVisible = true
    },
    // 编辑
    changeTableData(data, tableData, index) {
      this.$set(tableData, index, data)
    },
    //添加
    handleAddTableData(data, tableData) {
      tableData.push(data)
    },
    changeRuleListData(newRuleListData) {},
    //删除
    deleteTableData(tableData, index) {
      tableData.splice(index, 1)
    },
    // 初始化提示语，根据不同的业务
    initTipContent() {
      const currentTipContent = attrENum[+this.formInfo.businessType]
      Object.keys(currentTipContent).forEach((item) => {
        this.settingFields.forEach((setting, settingIndex) => {
          if (setting.settingAttr === item) {
            this.settingFields[settingIndex]['titleTip'] = currentTipContent[item]
          }
        })
      })
    },
    async init() {
      console.log('init')
      // 需求要求只有线索表单有新建不允许重复
      if (this.currentBusinessAttr === 'clue') {
        this.settingFields.push({
          title: this.$t('duplicateNew.newNotRepeat'),
          enable: 0,
          titleTip: '',
          showSwitch: false,
          showTable: true,
          tableData: [],
          settingAttr: 'noRepeatRule', //表格渲染的数据对应的后端返回的数据的字段
          tipImgInfo: [
            {
              title: '新建数据时自动查重',
              imgUrl: [
                'https://xbbpro.oss-cn-zhangjiakou.aliyuncs.com/xbbProPrd/ding0064986624e3b83b35c2f4657eb6378f/02276334205821146722/png/17066638798347f2b4e597c7279f34917396ccce59131.png'
              ]
            }
          ]
        })
      }
      const { formId, businessType, saasMark } = this.formInfo
      const params = { formId, businessType, saasMark }
      try {
        const [formResult, rules] = await Promise.all([
          ruleFormRecheck(params),
          getCheckRuleList(params)
        ])
        this.ruleListData = rules.result
        console.log(this.ruleListData)
        this.attrList = formResult.result
        this.ruleId = this.ruleListData.id
        this.contactAttrList = formResult.result.contactAttrList || []
        this.contactFormList = formResult.result.contactFormList || []
        // 处理表格表头，因为表格表头比下拉框选择多一个查重字段，所以这里需要前插一个attr
        this.attrKeys = getSaveKeys(+this.formInfo.businessType)
        this.attrKeys.unshift('attr')
        this.attrKeys = [...new Set(this.attrKeys)]
        // 处理表格的数据
        this.settingFields.forEach((setting, settingIndex) => {
          if (
            setting.settingAttr !== 'normalEnable' &&
            this.ruleListData[setting.settingAttr].length > 0
          ) {
            const { data } = this.transFormTableData(
              this.ruleListData[setting.settingAttr],
              setting.settingAttr
            )
            this.settingFields[settingIndex]['tableData'] = data
            this.settingFields[settingIndex]['prevTableData'] = xbb.deepClone(data)
          } else {
            this.settingFields[settingIndex]['tableData'] = []
            this.settingFields[settingIndex]['prevTableData'] = []
          }
        })
        // 因为第一个常用查重是不需要表格的
        this.settingFields[0]['enable'] = this.ruleListData['normalEnable']
        // 初始化提示语
        this.initTipContent()
        // 存一下新建不允许重复的数据
        this.newNoRepeatRule = rules.result.noRepeatRule
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
        this.initTable = true
      }
    },
    confirmDialog() {
      console.log(Number(this.settingFields[0]['enable']))
      this.dialogVisible = false
      this.settingFields[0]['enable'] = Number(this.settingFields[0]['enable'])
    },
    cancelDialog() {
      this.dialogVisible = false
      this.settingFields[0]['enable'] = Number(!this.settingFields[0]['enable'])
    },
    // 把后端返回的数据里面的每个对象转化成表格需要的数据
    transFormTableData(resultData, settingAttr) {
      if (resultData.length === 0) return
      // const heaAttrs = Object.keys(resultData[0])
      const heaAttrs = ['clue', 'contact', 'customer', 'opportunity']
      const noRepeatRuleList = resultData.slice(0)
      // console.log(noRepeatRuleList)
      const tableData = []
      // 获取表头要渲染哪些字段
      noRepeatRuleList.forEach((repeatRule) => {
        const tableItemData = {}
        heaAttrs.forEach((ruleKey) => {
          const settingRuleKey = ruleKey + attrSettingName
          if (repeatRule[ruleKey] && repeatRule[ruleKey].length > 0) {
            const ruleValue = repeatRule[ruleKey][0]
            const attrParams = {
              attrList: this.attrList,
              allList: [],
              isTable: true,
              isFindAttr: false
            }
            const attrLabel = getAttrInfo(
              this.currentBusinessAttr,
              ruleValue.attr,
              ruleValue.linkFormId,
              attrParams
            )
            const otherParams = {
              settingAttr,
              currentBusinessAttr: this.currentBusinessAttr,
              attrList: this.attrList,
              contactFormList: this.contactFormList,
              clueAttr: attrLabel,
              currentBusinessType: +this.formInfo.businessType,
              isTable: true
            }
            tableItemData[ruleKey] = transFormTableItemData(ruleKey, ruleValue, otherParams)
            tableItemData[settingRuleKey] = ruleValue
            tableItemData['attr'] = attrLabel
            tableItemData['saveAttr'] = ruleValue.attr
          } else {
            tableItemData[ruleKey] = '--'
            tableItemData[settingRuleKey] = getBaseSettingAttr(ruleKey)
          }
        })
        tableData.push(tableItemData)
      })
      return {
        data: tableData,
        keys: heaAttrs
      }
    },

    // 冒泡循环比较
    bubbleCompare(arr) {
      const data = xbb.deepClone(arr)
      let flag = false
      const dataCopy = data.concat()
      for (const item of data) {
        delete item.uid
        dataCopy.shift()
        for (const itemC of dataCopy) {
          delete itemC.uid
          if (xbb._isEqual(item, itemC)) {
            this.ruleTips = this.$t('duplicate.ruleTips')
            flag = true
            break
          }
        }
        if (flag) break
      }
      return flag
    },
    // 重复数据校验
    hasSameData() {
      return this.settings.some((item) => this.bubbleCompare(item.ruleList))
    },
    getBusinessAttr(attrName, attrListName) {
      const attrItem = this.attrList[attrListName].find(
        (attrItem) => attrItem.attrName === attrName
      )
      return attrItem ? attrItem.attr : attrName
    },
    transformSettingData(data, settingType) {
      console.log(data, settingType)
      return data.map((item) => {
        const returnItem = {}
        this.attrKeys.forEach((key) => {
          if (key === 'attr') return
          const itemKey = key + attrSettingName
          // 保存的时候如果有属性或者当前业务的才可以保存数据传给后端
          if (item[itemKey] && item[itemKey].linkFormId === '' && item[itemKey].matchWay === '') {
            returnItem[key] = []
          } else if (
            (item[itemKey] && item[itemKey].linkAttr) ||
            key === this.currentBusinessAttr
          ) {
            returnItem[key] = this.filterSettingKey(
              item[itemKey],
              item.saveAttr,
              this.currentBusinessAttr,
              key,
              settingType
            )
          } else {
            returnItem[key] = []
          }
        })
        return returnItem
      })
    },
    filterSettingKey(setting, clueAttr, businessTypeKey, key, settingType) {
      const { linkAttr, linkBusinessType, linkFormId, matchWay, isAll } = setting
      return [
        {
          attr: this.getBusinessAttr(clueAttr, businessTypeKey + attrKeyName),
          linkAttr,
          linkBusinessType,
          linkFormId:
            key === 'contact' && this.contactFormList.length > 0
              ? this.contactFormList[0].formId
              : linkFormId,
          matchWay: settingType === 'noRepeatRule' ? 1 : matchWay,
          isAll
        }
      ]
    },
    transFormRuleMap(ruleListData) {
      if (ruleListData.length === 0) return {}
      const attrKeys = this.attrKeys.filter((item) => item !== 'attr')
      const returnMap = {}
      ruleListData.forEach((list) => {
        let ruleMap = ''
        attrKeys.forEach((key) => {
          ruleMap += list[key] + ';'
        })
        returnMap[list.saveAttr] = ruleMap
      })
      return returnMap
    },
    // 保存
    saveSetting() {
      console.log('saveSettingsaveSetting')
      const { formId, businessType, saasMark } = this.formInfo
      const params = {
        formId,
        businessType,
        saasMark,
        dataId: this.ruleId
      }
      let list = []
      if (this.feeType === 1 && Number(this.formInfo.businessType) === 301) {
        list = [...this.settingFields]
      } else {
        list = this.settingFields.filter((item) => {
          return ['normalEnable', 'noRepeatRule'].includes(item['settingAttr'])
        })
      }
      console.log('list:', list)
      this.settingFields.forEach((setting) => {
        if (setting['settingAttr'] !== 'normalEnable') {
          params[setting['settingAttr']] = this.transformSettingData(
            setting['tableData'],
            setting['settingAttr']
          )
          if (setting['settingAttr'] === 'checkToolRule') {
            params['oldRuleMemoMap'] = this.transFormRuleMap(setting['prevTableData'])
            params['newRuleMemoMap'] = this.transFormRuleMap(setting['tableData'])
          }
        } else {
          params[setting['settingAttr']] = setting.enable
        }
        // 后端要求不展示也传一下新建不允许重复规则
        if (this.currentBusinessAttr !== 'clue') {
          params['noRepeatRule'] = this.newNoRepeatRule
        }
      })
      console.log(params, 'params')
      saveSettingRuleNew(params)
        .then((res) => {
          console.log(res)
          res.success && this.$message.success(res.msg)
          this.$emit('update:dialogShow', false)
        })
        .catch((e) => {
          console.log(e)
        })
    },
    // 打开高级查重示例
    handleCheckingReview(reviewTypeCn, itemInfo) {
      this.reviewDialogTitle = reviewTypeCn + '示例'
      this.reviewInfo = itemInfo.tipImgInfo
      this.reviewType = itemInfo.title
      // 处理一下提示框的数据
      this.checkReviewDialogShow = true
    }
  }
}
</script>

<style lang="scss">
.cancel_icon {
  margin-right: 10px;
  font-size: 20px;
  color: $icon-orange;
}
</style>

<style lang="scss" scoped>
.unite-cnki-dialog {
  display: flex;
  flex-direction: column;
  min-height: 300px;
  .tip {
    padding: 6px 10px;
    margin-bottom: 10px;
    font-size: 13px;
    line-height: 24px;
    color: $neutral-color-5;
    background-color: $line-cut-table;
    border-radius: 4px;
  }
  .unite-cnki {
    margin-bottom: 30px;
    .title {
      padding-left: 10px;
      font-size: 16px;
      font-weight: 500px;
      font-weight: bold;
      line-height: 24px;
      color: $text-main;
      border-left: 4px solid $line-cut-table;
    }
    .repeat-line-block {
      box-sizing: border-box;
      width: calc(100% - 20px);
      height: 1px;
      margin: 16px 0;
      background: $line-cut-light;
    }
    .em-color {
      color: $text-grey;
      &:hover {
        cursor: pointer;
      }
    }
    .content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      &.isOpen {
        padding-top: 10px;
      }
      .gap {
        margin-left: 10px;
      }
      .open {
        padding-right: 10px;
        font-size: 14px;
      }
    }
    .condition {
      display: flex;
      flex-direction: row;
      .left-line {
        width: 36px;
        height: 14px;
        margin-top: 25px;
        border-top: 1px dashed $text-tip;
        border-right: 1px dashed $text-tip;
        &.line1 {
          margin-top: 14px;
          border-top: 0px !important;
          border-bottom: 1px dashed $text-tip;
        }
        &.line2 {
          height: 25px !important;
          margin-top: 13px;
          border-top: 0px !important;
        }
      }
      .tips {
        position: relative;
        top: -7px;
        right: 20px;
      }
    }
  }
}
.tip-text {
  color: $text-auxiliary;
}
.tip-review {
  display: block;
  margin-top: 10px;
  color: $link-base-color-6;
  cursor: pointer;
}
</style>
