<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 字段权限设置(改造paas老文件)
 -->
<template>
  <div class="attrAuth">
    <div class="form-split"></div>
    <!-- <div class="form_title">{{ $t('formDesign.fieldPermission') }}</div> -->
    <el-checkbox
      v-model="widgetDetail.visible"
      :disabled="isForbidden === 0 && widgetDetail.visible === 1"
      :false-label="0"
      style="width: 100%"
      :true-label="1"
      >{{ $t('nouns.cansee') }}</el-checkbox
    >
    <div class="advance">
      <el-checkbox
        v-if="visibleScopeEnable"
        v-model="widgetDetail.visibleScopeEnable"
        :disabled="!widgetDetail.visible"
        :false-label="0"
        style="width: 100%"
        :true-label="1"
        >{{ $t('authoritySettting.advancedSetting') }}</el-checkbox
      >
      <div v-if="widgetDetail.visible && widgetDetail.visibleScopeEnable" class="authority-select">
        <el-select
          v-model="widgetDetail.visibleScopeRule.type"
          style="width: 100%; margin-bottom: 5px"
        >
          <el-option
            v-for="item in visibleType"
            :key="item.type"
            :label="item.desc"
            :value="item.type"
          ></el-option>
        </el-select>
        <template v-if="relative">
          <el-checkbox-group v-model="widgetDetail.visibleScopeRule.relative">
            <el-checkbox v-for="item in relativeList" :key="item.key" :label="item.key">{{
              item.value
            }}</el-checkbox>
          </el-checkbox-group>
        </template>
        <el-select
          v-model="widgetDetail.visibleScopeRule.role"
          class="padding-content"
          filterable
          multiple
          :placeholder="
            $t('placeholder.choosePls', {
              attr: $t('business.authorize', { attr: $t('label.role') })
            })
          "
          style="width: 100%; margin-top: 5px"
        >
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
          ></el-option>
        </el-select>
        <select-tree v-if="mutiTreeflag" :props4-dep-tree="widgetDetail.visibleScopeRule.dep">
        </select-tree>
        <el-select
          v-model="widgetDetail.visibleScopeRule.user"
          class="padding-content"
          filterable
          multiple
          :placeholder="
            $t('placeholder.choosePls', {
              attr: $t('business.authorize', { attr: $t('label.staff') })
            })
          "
          remote
          :remote-method="getUserList"
          reserve-keyword
          style="width: 100%"
          @change="change"
          @remove-tag="removeTag"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
    </div>
    <template v-if="widgetDetail.showEditable">
      <el-checkbox
        v-model="widgetDetail.editable"
        :disabled="!widgetDetail.visible"
        :false-label="0"
        style="width: 100%"
        :true-label="1"
        >{{ $t('formDesign.editable') }}</el-checkbox
      >
      <div class="advance">
        <el-checkbox
          v-if="widgetDetail.visible"
          v-model="editableAdvance"
          :disabled="!widgetDetail.editable"
          :false-label="0"
          style="width: 100%"
          :true-label="1"
          >{{ $t('authoritySettting.advancedSetting') }}</el-checkbox
        >
        <div v-if="widgetDetail.editable && editableAdvance" class="authority-select">
          <el-select
            v-model="widgetDetail.editableRule.type"
            style="width: 100%; margin-bottom: 5px"
          >
            <el-option
              v-for="item in editableType"
              :key="item.type"
              :label="item.desc"
              :value="item.type"
            ></el-option>
          </el-select>
          <template v-if="relative">
            <el-checkbox-group v-model="widgetDetail.editableRule.relative">
              <el-checkbox v-for="item in relativeList" :key="item.key" :label="item.key">{{
                item.value
              }}</el-checkbox>
            </el-checkbox-group>
          </template>
          <el-select
            v-model="widgetDetail.editableRule.role"
            class="padding-content"
            filterable
            multiple
            :placeholder="
              $t('placeholder.choosePls', {
                attr: $t('business.authorize', { attr: $t('label.role') })
              })
            "
            style="width: 100%; margin-top: 5px"
          >
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            ></el-option>
          </el-select>
          <select-tree v-if="mutiTreeflag" :props4-dep-tree="widgetDetail.editableRule.dep">
          </select-tree>
          <el-select
            v-model="widgetDetail.editableRule.user"
            class="padding-content"
            filterable
            multiple
            :placeholder="
              $t('placeholder.choosePls', {
                attr: $t('business.authorize', { attr: $t('label.staff') })
              })
            "
            remote
            :remote-method="getUserList"
            reserve-keyword
            style="width: 100%"
            @change="change"
            @remove-tag="removeTag"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import { roleList, departmentSimpleList, userList } from '@/api/system'
import selectTree from '@/components/common/v-form-item-choose-department'

export default {
  name: 'WeightAttrAuth',
  components: {
    SelectTree: selectTree
  },
  props: {
    widgetDetail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    editable: {
      // 可编辑权限的显示隐藏
      type: Boolean,
      default: true
    },
    visibleScopeEnable: {
      // 高级权限的显示隐藏
      type: Boolean,
      default: true
    },
    // 是否显示高级权限下的协同人等的多选
    relative: {
      type: Boolean,
      default: true
    },
    // 可导出的显示隐藏
    isExport: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      roleList: [], // 角色列表
      deptList: [], // 部门列表
      userList: [], // 员工列表
      selectedUser: [], // 选中的员工列表
      // 权限列表
      scopeAuthList: [
        { type: 1, desc: this.$t('formDesign.noOneToSee') },
        { type: 2, desc: this.$t('nouns.partiallyVisible') }
      ],
      visibleType: [
        // 可见的设置类型， 正反
        { type: 1, desc: this.$t('formDesign.noOneToSee') },
        { type: 2, desc: this.$t('nouns.partiallyVisible') }
      ],
      editableType: [
        // 可编辑的设置类型， 正反
        { type: 1, desc: this.$t('formDesign.noEditor') },
        { type: 2, desc: this.$t('formDesign.partialEditable') }
      ],
      relativeList: [
        { key: 1, value: this.$t('nouns.personInCharge') },
        { key: 3, value: this.$t('nouns.founder') }
      ],
      // 默认选中值
      selected: [],
      // 数据默认字段
      defaultProps: {
        parent: 'parentId',
        value: 'id',
        label: 'label',
        children: 'children'
      },
      mutiTreeflag: false
      // weightAuthFinish: false
    }
  },
  computed: {
    isForbidden() {
      return this.widgetDetail.isForbidden
    },
    configTips() {
      if (this.widgetDetail.visibleScopeRule.dep.length) {
        return `已选择${this.widgetDetail.visibleScopeRule.dep.length}个分组`
      } else {
        return this.$t('formDesign.departmentHint')
      }
    },
    editableAdvance: {
      get() {
        return this.widgetDetail.editableAdvanceEnable && this.widgetDetail.visible
      },
      set(val) {
        this.widgetDetail.editableAdvanceEnable = val
      }
    }
  },
  watch: {
    // 'widgetDetail.visibleScopeEnable': {
    //   immediate: true,
    //   handler (val) {
    //     val && this.init()
    //   }
    // },
    // 'widgetDetail.editableAdvanceEnable': {
    //   immediate: true,
    //   handler (val) {
    //     val && this.init()
    //   }
    // },
    selectedUser(value) {
      // 选中的人员改变
      this.widgetDetail.visibleScopeRule.userList = value
      this.widgetDetail.editableRule.userList = value
    },
    'widgetDetail.visibleScopeRule.dep'(val, oldVal) {
      console.log(val, oldVal)
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.selectedUser = this.widgetDetail.visibleScopeRule.userList
      this.getUserList()
      // this.getDeptList()
      Promise.all([this.getDeptList(), this.getRoleList()])
        .then(
          () => {
            // this.weightAuthFinish = true
            this.$emit('weightAuthFinish')
          },
          () => {}
        )
        .catch(() => {})
    },
    getSelected(val) {
      this.widgetDetail.visibleScopeRule.dep = val
    },
    // 获取员工列表
    getUserList(query) {
      // widgetDetail.visibleScopeRule.user
      // if (query !== '') {
      userList({ nameLike: query })
        .then((res) => {
          // 这里统一使用id标识
          const userList = res.result.userList.map((item) => {
            return {
              ...item,
              id: item.userId
            }
          })
          this.userList = this.arrNoRepeat([...userList, ...this.selectedUser])
        })
        .catch(() => {})
      // }
    },
    // 将部门结构树整理为符合结构的对象
    manageDepartmentTree(department, parentId) {
      department.id = department.value
      department.parentId = parentId
      if (department.hasOwnProperty('children')) {
        department.children.forEach((item) => {
          this.manageDepartmentTree(item, department.id)
        })
      }
      return department
    },

    /**
     * @description: 人员选择数据改变
     * @param {type}
     * @return:
     */
    change(value) {
      const arr = this.userList.filter((item) => {
        return value.includes(item.userId)
      })
      this.selectedUser = this.arrNoRepeat([...this.selectedUser, ...arr])
    },

    /**
     * @description: 对象数组去重
     * @param {type}
     * @return:
     */
    arrNoRepeat(arr) {
      const newArrId = {}
      const newArr = []
      arr.forEach((item) => {
        newArrId[item.id] || newArr.push(item)
        newArrId[item.id] = true
      })
      return newArr
    },
    removeTag(value) {
      const arr = this.selectedUser.filter((item) => {
        return item.userId === String(value)
      })
      this.selectedUser.splice(this.selectedUser.indexOf(arr[0]), 1)
    },
    getDeptList() {
      return new Promise((resolve, reject) => {
        departmentSimpleList({})
          .then((res) => {
            res.result.departmentTree.forEach((item) => {
              this.deptList.push(this.manageDepartmentTree(item, '0'))
            })
            this.mutiTreeflag = true
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    getRoleList() {
      return new Promise((resolve, reject) => {
        roleList({})
          .then((res) => {
            this.roleList = res.result.roleList
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.attrAuth {
  .el-checkbox {
    margin: 4px 0;
  }
  .el-select--medium {
    width: 100%;
    .el-input__inner {
      height: 30px;
    }
  }
  .el-checkbox-group {
    .el-checkbox + .el-checkbox {
      margin-left: 10px;
    }
  }
  .advance {
    padding-left: 10px;
  }
}
</style>
