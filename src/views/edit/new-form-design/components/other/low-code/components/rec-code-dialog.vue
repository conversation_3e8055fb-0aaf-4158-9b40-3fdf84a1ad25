<!--
 * @Description:
-->
<template>
  <div v-if="visible" class="rec-code-dialog">
    <el-dialog
      :append-to-body="false"
      :before-close="hiddenDialog"
      :modal-append-to-body="false"
      title="请选择要恢复的环境"
      :visible="visible"
    >
      <div class="tips">请注意：我们将会丢弃您现在编辑的代码，为您恢复至您选中的版本。</div>
      <el-radio v-model="env" label="dev">开发环境</el-radio>
      <el-radio v-model="env" label="prod">生产环境</el-radio>
      <div slot="footer" class="dialog-footer">
        <el-button @click="hiddenDialog">取 消</el-button>
        <el-button type="primary" @click="recCode">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RecCodeDialog',

  props: {
    visible: {
      type: <PERSON>olean
    }
  },

  data() {
    return {
      env: 'dev'
    }
  },

  methods: {
    hiddenDialog() {
      this.$emit('hiddenDialog')
    },
    recCode() {
      document.querySelector('#editor').contentWindow.postMessage(
        {
          type: 'recCode',
          data: this.env
        },
        '*'
      )
      this.hiddenDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  margin-bottom: 20px;
  font-weight: bold;
  color: red;
}
</style>
