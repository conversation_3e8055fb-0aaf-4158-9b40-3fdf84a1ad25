<!--
 * @Description: 首页-短信群发-记录查看
 -->
<template>
  <div class="send-record">
    <sms-layout :is-scroll="false">
      <template slot="header">
        <span>{{ $t('appGroupMessaging.sendRecord18.title') }}</span>
        <div
          class="statistics"
          v-html="
            $t('appGroupMessaging.sendRecord18.statistics', {
              counts,
              waitCounts,
              sendCounts,
              successCounts,
              failCounts,
              cancelCounts
            })
          "
        ></div>
      </template>
      <div class="send-record-content">
        <!--筛选条件-->
        <div class="sms-filter-wrapper">
          <el-form class="demo-form-inline" :inline="true" :model="filterConditions">
            <el-form-item :label="$t('nouns.sendingTime')">
              <el-date-picker
                v-model="filterConditions.timeSpan"
                align="left"
                :end-placeholder="$t('label.endTime')"
                :picker-options="pickerOptions"
                size="small"
                :start-placeholder="$t('label.startTime')"
                type="daterange"
                unlink-panels
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              :label="$t('appGroupMessaging.sendRecord18.filterConditions.refTypeName')"
            >
              <el-select
                v-model="filterConditions.refType"
                clearable
                :placeholder="$t('placeholder.choosePls', { attr: '' })"
              >
                <template v-for="item in modelList">
                  <el-option
                    :key="item.refType"
                    :label="item.name"
                    :value="item.refType"
                  ></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('appGroupMessaging.sendRecord18.phoneNumber') + ':'">
              <el-input
                v-model.trim="filterConditions.mobileLike"
                clearable
                :placeholder="$t('placeholder.inputPls', { attr: '' })"
              ></el-input>
            </el-form-item>
            <el-form-item :label="$t('label.operator') + ':'">
              <el-input
                v-model.trim="filterConditions.senderLike"
                clearable
                :placeholder="$t('placeholder.inputPls', { attr: '' })"
              ></el-input>
            </el-form-item>
            <el-form-item :label="$t('appGroupMessaging.sendRecord18.receiver') + ':'">
              <el-input
                v-model.trim="filterConditions.receiverLike"
                clearable
                :placeholder="$t('placeholder.inputPls', { attr: '' })"
              ></el-input>
            </el-form-item>
            <el-form-item :label="$t('appGroupMessaging.sendRecord18.statusName') + ':'">
              <el-select
                v-model="filterConditions.status"
                clearable
                :placeholder="$t('placeholder.choosePls', { attr: '' })"
              >
                <el-option
                  :label="$t('appGroupMessaging.sendRecord18.filterConditions.unsend')"
                  :value="0"
                ></el-option>
                <el-option
                  :label="$t('appGroupMessaging.sendRecord18.filterConditions.sending')"
                  :value="1"
                ></el-option>
                <el-option
                  :label="$t('appGroupMessaging.sendRecord18.filterConditions.sendSuccess')"
                  :value="2"
                ></el-option>
                <el-option
                  :label="$t('appGroupMessaging.sendRecord18.filterConditions.sendFailed')"
                  :value="3"
                ></el-option>
                <el-option
                  :label="$t('appGroupMessaging.sendRecord18.filterConditions.sendCancel')"
                  :value="4"
                ></el-option>
                <el-option
                  :label="$t('appGroupMessaging.sendRecord18.filterConditions.Resend')"
                  :value="5"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('message.contentLike') + ':'">
              <el-input
                v-model.trim="filterConditions.contentLike"
                clearable
                :placeholder="$t('placeholder.inputPls', { attr: '' })"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getLogList(false)">{{
                $t('operation.searchText')
              }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!--表格-->
        <div class="sendRecord-table-wrapper">
          <el-table
            v-loading="loading"
            border
            :data="recordList"
            height="calc(100% - 5px)"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="40"> </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('operation.operate')"
              label-class-name="table-header-cell"
              width="121"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.resendBtn"
                  size="mini"
                  @click="mulitSend(false, scope.row.smsId)"
                  >{{ $t('operation.Resend') }}</el-button
                >
                <el-button
                  v-if="scope.row.cancelBtn"
                  size="mini"
                  type="danger"
                  @click="mulitCancel(false, scope.row.smsId)"
                  >{{ $t('operation.cancel') }}</el-button
                >
              </template>
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('label.operator')"
              label-class-name="table-header-cell"
              prop="sender"
              width="88"
            >
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('appGroupMessaging.sendRecord18.receiver')"
              label-class-name="table-header-cell"
              prop="receiver"
              width="88"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.jump"
                  size="small"
                  style="color: #20a0ff"
                  type="text"
                  @click="goToDetail(scope.row)"
                  >{{ scope.row.receiver }}</el-button
                >
                <span v-else>{{ scope.row.receiver }}</span>
              </template>
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('appGroupMessaging.sendRecord18.table.refTypeName')"
              label-class-name="table-header-cell"
              prop="refTypeName"
              width="88"
            >
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('appGroupMessaging.sendRecord18.phoneNumber')"
              label-class-name="table-header-cell"
              prop="mobile"
              width="133"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.jump"
                  size="small"
                  style="color: #20a0ff"
                  type="text"
                  @click="goToDetail(scope.row)"
                  >{{ scope.row.mobile }}</el-button
                >
                <span v-else>{{ scope.row.mobile }}</span>
              </template>
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('nouns.sendingTime')"
              label-class-name="table-header-cell"
              prop="sendTime"
              width="164"
            >
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('appGroupMessaging.sendRecord18.statusName')"
              label-class-name="table-header-cell"
              prop="statusName"
              width="101"
            >
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('appGroupMessaging.sendRecord18.table.statusCode')"
              label-class-name="table-header-cell"
              prop="statusCode"
              width="101"
            >
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('appGroupMessaging.sendRecord18.table.statusMemo')"
              label-class-name="table-header-cell"
              prop="statusMemo"
              width="101"
            >
            </el-table-column>
            <el-table-column
              class-name="group-cell"
              :label="$t('message.contentLike')"
              label-class-name="table-header-cell"
              prop="content"
            >
              <template slot-scope="scope">
                <div class="table-content-wrapper">
                  <p class="desc">{{ scope.row.content }}</p>
                  <el-button type="text" @click="toDetail(scope.row.content)">{{
                    $t('nouns.detailed')
                  }}</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!--底部栏-->
      <div class="pagination-bottom">
        <div class="pagination-bottom__left">
          <span v-if="smsIdIn.length">{{
            $t('appGroupMessaging.sendRecord18.bottom.selectData', { selectNumber: smsIdIn.length })
          }}</span>
          <span v-else>{{ $t('appGroupMessaging.sendRecord18.bottom.noData') }}</span> |
          <span>{{ $t('appGroupMessaging.sendRecord18.bottom.batch') + ':' }}</span>
          <el-button :disabled="!allowCancelCount.length" @click="mulitCancel">{{
            $t('appGroupMessaging.sendRecord18.bottom.cancelText')
          }}</el-button>
          <el-button :disabled="!allowResendCount.length" @click="mulitSend">{{
            $t('operation.Resend')
          }}</el-button>
        </div>
        <div class="pagination-bottom__right">
          <v-pagination
            :current-page.sync="currentPage"
            layout="slot,sizes, prev, pager, next, jumper"
            :page-size="pageSize"
            :page-sizes="[20, 30, 50, 100, 200]"
            :total="rowsCount"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          >
          </v-pagination>
        </div>
      </div>
    </sms-layout>
  </div>
</template>

<script>
import SmsLayout from '../../application-manage/group-messaging/components/sms-layout'
import {
  getStatistic,
  getLogList,
  smsResend,
  smscancleSend,
  getSmsSearchList
} from '@/api/sms-send'
import { mapMutations } from 'vuex'

export default {
  name: 'SendRecord',

  components: {
    SmsLayout
  },

  data() {
    return {
      // 短信总数
      counts: '',
      // 待发送数
      waitCounts: '',
      // 正发送数
      sendCounts: '',
      // 发送成功数
      successCounts: '',
      // 发送失败数
      failCounts: '',
      // 取消发送数
      cancelCounts: '',
      // 筛选条件
      filterConditions: {
        // 手机号码
        mobileLike: '',
        // 发送人筛选
        senderLike: '',
        // 接收人筛选
        receiverLike: '',
        // 短信状态筛选
        status: '',
        // 模块筛选
        refType: '',
        // 时间筛选
        timeSpan: [],
        // 短信内容
        contentLike: ''
      },
      modelList: [
        /* {
        'name': '客户',
        'refType': 100
      },
      {
        'name': '合同',
        'refType': 201
      },
      {
        'name': '联系人',
        'refType': 401
      } */
      ],
      // 每页显示条目个数
      pageSize: 20,
      // 当前页面
      currentPage: 1,
      // 总条数
      rowsCount: 0,
      // 记录列表
      recordList: [],
      // 表格已选中条数
      selectNumber: '',
      // 表单checkbox选中列表
      smsIdIn: [],
      // 表单checkbox可重发列表
      allowResendCount: [],
      // 表单checkbox可取消列表
      allowCancelCount: [],
      loading: true,
      pickerOptions: {
        shortcuts: [
          {
            text: this.$t('label.today'),
            onClick(picker) {
              const date = new Date()
              picker.$emit('pick', [date, date])
            }
          },
          {
            text: this.$t('label.yesterday'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', [date, date])
            }
          },
          {
            text: this.$t('label.latestWeek'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: this.$t('label.latestMonth'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },

  created() {
    // 获取短信信息
    getStatistic({})
      .then((res) => {
        this.counts = res.result.counts
        this.waitCounts = res.result.waitCounts
        this.sendCounts = res.result.sendCounts
        this.successCounts = res.result.successCounts
        this.failCounts = res.result.failCounts
        this.cancelCounts = res.result.cancelCounts
      })
      .catch(() => {})
    this.getLogList()
    // 获取模块筛选项
    getSmsSearchList({})
      .then((res) => {
        this.modelList = res.result.list
      })
      .catch(() => {})
  },

  mounted() {
    // 监听浏览器窗口缩放更新table高度
    window.addEventListener('resize', this.setTimer)
    // 首次获取筛选元素高度不正确,故延迟
    setTimeout(() => {
      this.setTimer()
    }, 200)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.setTimer)
  },

  methods: {
    ...mapMutations(['SET_DETAIL_DIALOG_SHOW', 'SET_DETAIL_QUERY']),
    // 打开详情页
    openDetail(detailInfo) {
      this['SET_DETAIL_DIALOG_SHOW'](true)
      this['SET_DETAIL_QUERY']({
        appId: detailInfo.appId,
        dataId: +(detailInfo.parentId || detailInfo.dataId || detailInfo.id), // 存在父产品id的时候，要和父产品比较
        saasMark: detailInfo.saasMark,
        businessType: +detailInfo.businessType,
        subBusinessType: +detailInfo.subBusinessType,
        activeName: 'smsSendAndReply' // 短信往来tab栏
      })
    },
    /**
     * @name: 获取短信日志列表
     * @param {Boolean} isJump - 是否是分页跳转
     */
    getLogList(isJump = true) {
      this.loading = true
      const page = isJump ? this.currentPage : 1
      Object.assign(this.filterConditions, { page, pageSize: this.pageSize })
      getLogList(this.filterConditions)
        .then((res) => {
          this.recordList = res.result.list
          this.rowsCount = res.result.pageHelper.rowsCount
          this.currentPage = res.result.pageHelper.currentPageNum
          this.loading = false
        })
        .catch(() => {})
    },
    /**
     * @name: 查看短信详情内容
     * @param {String} content - 单条记录内容
     */
    toDetail(content) {
      this.$alert(content, '短信内容')
        .then(() => {})
        .catch(() => {})
    },
    /**
     * @name: pageSize 改变时会触发
     * @param {Number} pageSize - 每页显示条目个数
     */
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getLogList(false)
    },
    /**
     * @name: currentPage 改变时会触发
     * @param {Number} page - 当前点击分页页数
     */
    handleCurrentChange(page) {
      this.loading = false
      this.currentPage = page
      this.getLogList()
    },
    /**
     * @name: 表格checkbox点击事件
     * @param {Array} val - 当前选中checkbox
     */
    handleSelectionChange(val) {
      this.smsIdIn = val.map((v) => v)
      this.allowCancelCount = this.smsIdIn.filter((v) => v.cancelBtn)
      this.allowResendCount = this.smsIdIn.filter((v) => v.resendBtn).map((v) => v.smsId)
    },
    // 列表穿透
    goToDetail(val) {
      this.openDetail(val.entity)
    },
    /**
     * @name: 重新发送
     * @param {Boolean} isMulti - 是批量发送还是单个发送
     * @param {Number} singSmsId - 单个短信日志id
     */
    mulitSend(isMulti = true, singSmsId) {
      let smsIdIn, allowResendCount
      if (isMulti) {
        smsIdIn = this.smsIdIn.filter((v) => v.resendBtn).map((v) => v.smsId)
        allowResendCount = smsIdIn.length
        this.$confirm(
          `您选中的${this.smsIdIn.length}条数据，有${allowResendCount}条可以重发，确认重新发送吗？`,
          '重新发送',
          {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          }
        )
          .then(() => {
            smsIdIn = smsIdIn.map((v) => v.smsId)
            smsResend({ smsIdIn }).then((res) => {
              this.$message({
                message: res.msg,
                type: 'success'
              })
              this.getLogList()
            })
          })
          .catch(() => {})
      } else {
        smsIdIn = [singSmsId]
        smsResend({ smsIdIn })
          .then((res) => {
            this.$message({
              message: res.msg,
              type: 'success'
            })
            this.getLogList()
          })
          .catch(() => {})
      }
    },
    /**
     * @name: 重新发送
     * @param {Boolean} isMulti - 是批量发送还是单个发送
     * @param {Number} singSmsId - 单个短信日志id
     */
    mulitCancel(isMulti = true, singSmsId) {
      // let smsIdIn = isMulti ? this.smsIdIn : [singSmsId]
      let smsIdIn, allowCancelCount
      if (isMulti) {
        // this.
        smsIdIn = this.smsIdIn.filter((v) => v.cancelBtn)
        allowCancelCount = smsIdIn.length
        this.$confirm(
          `您选中的${this.smsIdIn.length}条数据，有${allowCancelCount}条可以取消，确认取消发送吗？`,
          '取消发送',
          {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel'),
            type: 'warning'
          }
        )
          .then(() => {
            smsIdIn = smsIdIn.map((v) => v.smsId)
            smscancleSend({ smsIdIn }).then((res) => {
              this.$message({
                message: res.msg,
                type: 'success'
              })
              this.getLogList()
            })
          })
          .catch(() => {})
      } else {
        smsIdIn = [singSmsId]
        smscancleSend({ smsIdIn })
          .then((res) => {
            this.$message({
              message: res.msg,
              type: 'success'
            })
            this.getLogList()
          })
          .catch(() => {})
      }
    },
    setTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        const height = document.getElementsByClassName('sms-filter-wrapper')[0]
          ? document.getElementsByClassName('sms-filter-wrapper')[0].clientHeight
          : null
        if (height) {
          const table = document.getElementsByClassName('sendRecord-table-wrapper')[0]
          if (height > 102) {
            table.style.height = `calc(100% - ${height}px)`
          } else {
            table.style.height = 'calc(100% - 102px)'
          }
        }
      }, 50)
    }
  }
}
</script>

<style lang="scss" scoped>
.send-record {
  height: 100%;
  .send-record-content {
    // margin-bottom: 70px;
    // height: calc(100% -72px);
    height: calc(100% - 20px - 50px);
  }
  .statistics {
    position: absolute;
    top: 50%;
    right: 20px;
    font-size: 13px;
    color: $text-auxiliary;
    transform: translateY(-50%);
    &__active {
      color: $brand-color-5;
    }
  }
  .sendRecord-table-wrapper {
    position: relative;
    height: calc(100% - 51px);
  }
  .pagination-bottom {
    box-sizing: border-box;
    // position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // bottom: 0;
    // left: 0;
    width: 100%;
    height: 62px;
    padding: 0 15px;
    border-top: 1px solid $line-cut-deep;
    &__left {
      color: $text-plain;
    }
  }
}
</style>

<style lang="scss">
.send-record {
  .el-table {
    .table-content-wrapper {
      position: relative;
      .desc {
        @include singleline-ellipsis;
        max-width: 500px;
        padding-right: 26px;
      }
      .el-button {
        position: absolute;
        top: -5px;
        right: 0;
      }
    }
  }
}
</style>
