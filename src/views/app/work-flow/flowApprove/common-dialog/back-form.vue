<!--
 * @Description: 审批回退
 -->
<template>
  <el-form ref="formBody" label-position="right" label-width="100px" :model="formData">
    <el-form-item
      v-if="backType"
      :label="`${$t('workflowDesign.backTo')}：`"
      prop="backNodeId"
      :rules="backNodeIdRules"
    >
      <el-select
        v-model="backNodeId"
        class="back-node-select"
        :placeholder="`${$t('placeholder.choosePls', { attr: '' })}`"
        popper-class="back-node-select__popper"
        size="mini"
      >
        <el-option
          v-for="(item, index) in backNodeOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <div class="node-option-item">
            <avatar-img :alt="item.avatarAlt" radius :size="34" :src="item.avatarSrc"></avatar-img>
            <div class="node-desc__wrap">
              <p class="node-name">{{ item.label }}</p>
              <p class="approver-name">{{ item.approverName }}</p>
            </div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="opinionFlag"
      :label="$t('flow.backReason')"
      prop="opinion"
      :rules="getOpinionRules($t('flow.backReason'))"
    >
      <at-who ref="at" :affix="elementTemplate" @update="updateVal">
        <el-input v-model="opinion" autocomplete="off" type="textarea"></el-input>
      </at-who>
    </el-form-item>
  </el-form>
</template>

<script>
import AtWho from '@/components/at-who'
import mixin from './mixin.js'

import xbb from '@xbb/xbb-utils'

export default {
  name: 'BackForm',
  components: {
    AtWho
  },
  mixins: [mixin],
  props: {
    processOperateData: {
      type: Object
    }
  },
  data() {
    return {
      elementTemplate: {
        front: '<span class="at-person">',
        end: '</span>'
      }
    }
  },
  computed: {
    backNodeIdRules() {
      return [
        {
          required: true,
          message: this.$t('placeholder.choosePls', { attr: this.$t('workflowDesign.backNode') })
        }
      ]
    },
    backNodeId: {
      get() {
        return this.formData.backNodeId
      },
      set(val) {
        this.$set(this.formData, 'backNodeId', val)
      }
    },
    backNodeOptions() {
      const backNodes = xbb._get(this, 'processOperateData.backNodeList')
        ? this.processOperateData.backNodeList
        : []
      return backNodes.map((item, index) => {
        const label = item.isPreApproval
          ? `${item.nodeName}（${this.$t('operation.previousStep')}）`
          : `${item.nodeName}`
        // 头像的替代名称
        let avatarAlt =
          (xbb._get(this, 'approverNameList.length') && item.approverNameList[0]) || ''
        // 审批人名字
        const approverName = this.approverNameGenerate(item)
        if (item.approverNameList.length > 1) {
          avatarAlt =
            item.signType === 1 ? this.$t('flowDetail.orSign') : this.$t('flowDetail.counterSign')
        }
        const avatarSrc = item.avatar ? item.avatar : ''
        return {
          label,
          approverName,
          avatarSrc,
          avatarAlt,
          value: item.workflowNodeId
        }
      })
    },
    backType() {
      return this.processOperateData && this.processOperateData.backType
    }
  },
  // 初始化回退表单的选项默认值
  mounted() {
    if (!this.backNodeId.length && this.backNodeOptions.length) {
      this.backNodeId = this.backNodeOptions[0].value
    }
  },
  methods: {
    /**
     * 生成审批人的姓名
     * @param item
     * @returns {*|string}
     */
    approverNameGenerate(item) {
      return xbb._get(item, 'approverNameList.length') === 1
        ? item.approverNameList[0]
        : `${item.approverNameList.slice(0, 2).join('、')}${this.$t('unit.etc')}${
            item.approverNameList.length
          }${this.$t('unit.people')}(${
            item.signType === 1 ? this.$t('flowDetail.orSign') : this.$t('flowDetail.counterSign')
          })`
    },
    updateVal(val) {
      this.$set(this.formData, 'opinion', val)
    },
    atWhoData() {
      const content = this.$refs.at ? this.$refs.at.getInfo().template : ''
      const atUserIds = this.$refs.at ? this.$refs.at.getInfo().templateArr : []
      return {
        content,
        atUserIds
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.back-node-select {
  width: 250px;
}
</style>

<style lang="scss">
.back-node-select__popper {
  .el-select-dropdown__item {
    height: 46px;
    padding: 6px 12px;
    font-size: 12px;
    .node-option-item {
      .node-desc__wrap {
        display: inline-block;
        p {
          height: 17px;
          line-height: 17px;
        }
        .approver-name {
          color: $text-auxiliary;
        }
      }
    }
  }
}
</style>
