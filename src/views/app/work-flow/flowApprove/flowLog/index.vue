<!--
 * @Description: 审批记录
 -->
<template>
  <div class="work-flow-log">
    <!-- <div class="btn-tab">
      <span
        v-for="btn in navList"
        :key="btn.value"
        class="btn-tab__item"
        :class="{ 'is-active': attrType === btn.value }"
        @click="tabCheckHandler(btn.value)"
        >{{ btn.label }}</span
      >
    </div> -->
    <div class="btn-tab">
      <SubTab
        :items="navList"
        label="label"
        :value="attrType"
        value-key="value"
        @change="(btn) => tabCheckHandler(btn.value)"
      ></SubTab>
    </div>
    <div class="log-body">
      <!-- 当前 -->
      <template v-if="attrType === 'present' && currentLog.length">
        <div class="log-content present">
          <div class="log-content__item-wrapper">
            <item-log
              v-for="log in currentLog"
              :key="log.item.id"
              :prop-item="log.item"
              :status="log.status"
              @deleteComment="deleteComment"
              @editDialogFormData="editDialogFormData"
            />
          </div>
          <el-button
            v-if="showMore"
            class="log-content__item-more"
            type="text"
            @click="showMoreCurrentHandler"
            >{{ $t('importExport.viewMore') }}</el-button
          >
        </div>
        <!-- 当前审批节点抄送人信息 -->
        <cc-info v-if="ccUserList.length" :cc-user-list="ccUserList" class="cc-user" />
      </template>
      <!-- 历史 -->
      <template v-else-if="attrType === 'history' && historyLog.length">
        <div v-for="(log, index) in historyLog" :key="log.workflowName + index" class="log-content">
          <collapse-item :index="index" :item="log">
            <item-log
              v-for="item in log.nodeTaskList"
              :key="item.id"
              :prop-item="item"
              status="finish"
            />
          </collapse-item>
        </div>
      </template>
      <Empty v-else :text="$t('flow.noApprovalPermission')" />
    </div>
  </div>
</template>

<script>
import CollapseItem from './collapse.vue'
// import ItemLog from './ItemLog'
import ItemLog from './item-log'
import CcInfo from './cc-info'
import SubTab from '@/components/form-data-detail/uipaas/components/SubTab.vue'
import Empty from '@/components/base/empty.vue'

export default {
  components: {
    Empty,
    ItemLog,
    CollapseItem,
    CcInfo,
    SubTab
  },
  props: {
    taskInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      attrType: 'present'
    }
  },
  computed: {
    navList() {
      return [
        { label: this.$t('label.present'), value: 'present' },
        { label: this.$t('label.history'), value: 'history' }
      ]
    },
    currentLog() {
      if (!this.taskInfo?.currentLog) return []

      const curLog = this.taskInfo.currentLog.map((item) => {
        // 当前和经过的主节点，根据或签会签判断状态（这里是广义的或签、会签）
        const baseIsProcess = [0, 14].includes(item.taskType) // 0 待处理节点，14待系统转交节点
        const subHasProcess = (item.unCommitNodeTaskList || []).some((sub) =>
          [0, 14].includes(sub.taskType)
        )
        const isProcess =
          item.signType === 1
            ? baseIsProcess // 或签主节点状态根据自己来
            : subHasProcess || baseIsProcess // 会签需要考虑子节点状态
        return {
          item,
          status: isProcess ? 'process' : 'finish'
        }
      })
      // 未来的主节点状态判断
      const futureLog = (this.taskInfo.futureLog || []).map((item) => ({
        item,
        status: 'wait'
      }))
      return [...curLog, ...futureLog]
    },
    historyLog() {
      return (this.taskInfo && this.taskInfo.historyLog) || []
    },
    ccUserList() {
      return (this.taskInfo && this.taskInfo.ccUserList) || []
    },
    showMore() {
      return this.taskInfo.currentLogEnd === false // 一定是等于false，如果不存在默认认为也是结束了
    }
  },
  methods: {
    tabCheckHandler(value) {
      this.attrType = value
    },
    editDialogFormData(item) {
      this.$emit('editDialogFormData', item)
    },
    deleteComment(item) {
      this.$emit('deleteComment', item)
    },
    showMoreCurrentHandler() {
      this.$emit('show-more')
    }
  }
}
</script>

<style lang="scss" scoped>
.work-flow-log {
  display: flex;
  // flex: 1;
  flex-direction: column;
  height: 100%;
  // margin-bottom: 60px;
  & .btn-tab {
    flex-shrink: 0;
    width: 108px;
    margin-bottom: 10px;
    .btn-tab__item {
      box-sizing: border-box;
      display: inline-block;
      height: 24px;
      padding: 0 8px;
      font-size: 12px;
      line-height: 24px;
      color: $text-auxiliary;
      cursor: pointer;
      background-color: $bg-primary;
      border-radius: 4px;
      &.is-active {
        color: $brand-base-color-6;
        background-color: #ffeee2;
      }
    }
    .btn-tab__item + .btn-tab__item {
      margin-left: 16px;
    }
  }
  & > .log-body {
    display: flex;
    flex: 1;
    flex-direction: column;
    & > .empty {
      margin: 180px auto 180px;
      font-size: 14px;
      color: $text-auxiliary;
    }
    & > .log-content {
      flex: 1;
      // overflow: auto;
      .log-content__item-more {
        color: $link-base-color-6;
      }
    }
    & > .log-content.present {
      padding-right: 12px;
    }
    & > .cc-user {
      flex-shrink: 0;
      margin-top: 30px;
    }
  }
}
</style>
