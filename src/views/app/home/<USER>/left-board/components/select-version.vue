<template>
  <!-- 首页看板的选择流程版本 -->
  <div v-if="versionList.length" class="select-version">
    <!-- 流程版本选择 -->
    <el-dropdown trigger="click" @command="handelVersion">
      <span class="el-dropdown-link">
        {{ $t('display.chartSystem.stageVersion') }}：{{ version.name
        }}<i class="el-icon--right el-icon-arrow-down"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="(item, index) in versionList"
          :key="index"
          :class="{ 'version-chose': item.id === version.id }"
          :command="index"
          >{{ item.versionName }}</el-dropdown-item
        >
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'SelectVersion',

  props: {
    versionList: {
      type: Array,
      default: () => []
    },
    version: {
      type: Object,
      default: () => ({})
    }
  },

  methods: {
    // 选择版本方法
    handelVersion(index) {
      this.$emit('handelVersion', index)
    }
  }
}
</script>

<style lang="scss">
.version-chose {
  color: $brand-color-5;
  // 下拉选择中，选中的菜单
  background-color: $brand-color-1;
}
</style>

<style lang="scss" scoped>
.select-version {
  position: relative;
  left: 246px;
  display: inline-block;
  color: $text-plain;
  cursor: pointer;
}
</style>
