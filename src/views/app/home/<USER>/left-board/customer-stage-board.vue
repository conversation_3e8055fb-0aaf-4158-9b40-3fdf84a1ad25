<!--
 * @Description: '首页客户阶段漏斗'
 -->

<template>
  <div class="customer-stage-board">
    <div class="board-title">
      {{ dataForm.name }}
      <span v-if="!isBoard" class="board-time"
        ><span
          style="margin-left: 0"
          v-html="addNodeByWX(filterParam.userName, filterParam.companyStructType === 2 ? 1 : 0)"
        ></span
        ><i>/</i> {{ filterParam.timeName }}</span
      >
    </div>
    <set-data-board
      ref="SetDataBoard"
      :data-form="dataForm"
      @saveSuccess="refreshBoard"
    ></set-data-board>
    <div v-if="!childList || !childList.length" class="no-data">
      {{ $t('home.noDataTarget') }}
    </div>
    <div v-else v-loading="isLoading" class="board-content">
      <el-carousel
        arrow="always"
        :autoplay="false"
        height="400px"
        trigger="click"
        @change="changeOpportunityCarousel"
      >
        <el-carousel-item
          v-for="(item, index) in dataForm.appMenuFormIdPojoList"
          :key="index"
          v-loading="isLoading"
        >
          <div class="chance-template-name">
            <span>{{ item.formName }}</span>
            <!-- 流程版本选择 -->
            <SelectVersion
              :version="version"
              :version-list="versionList"
              @handelVersion="handelVersion"
            ></SelectVersion>
          </div>
          <div class="board-content-chart">
            <echarts
              :axis="chartObject.xAxis"
              class="chart graph-item"
              :data="chartObject.data ? chartObject.data : chartObject.dataSeries"
              :data-label-flag="true"
              :feature="{}"
              :graph-name="chartObject.name"
              :graph-type="13"
              :is-board-funnel="true"
              :is-system="true"
              :is-use-mock="false"
              :legend="[]"
              @graph-click="graphClick"
            ></echarts>
            <div v-if="childList[0] && childList[0].table" class="board-table chart">
              <el-table
                border
                cell-class-name="table-td"
                :data="dataList.length ? dataList : []"
                header-cell-class-name="table-th"
                max-height="250"
                width="auto"
              >
                <template v-for="(itemq, index) in childList[0].table.title">
                  <el-table-column
                    :key="index"
                    align="right"
                    header-align="left"
                    :label="itemq.titleValue"
                    width="auto"
                  >
                    <template slot-scope="scope">
                      <span
                        :class="{ through: scope.row[index].through }"
                        @click="clickTable(scope.row[index], scope.row)"
                        >{{ scope.row[index].value }}</span
                      >
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </div>
          </div>
          <div class="board-info">
            <div v-for="(item, index) in childList.slice(1)" :key="index" class="board-item">
              <p class="item-detail">
                <el-tooltip
                  class="item"
                  :content="item.chart[0].value || 0 + item.chart[0].unit"
                  effect="dark"
                  placement="top"
                >
                  <span
                    :class="{ through: item.chart[0].through }"
                    :style="{ 'font-size': adjustFontSize(item.chart[0].value) }"
                    @click="$emit('penetrate', item.chart[0])"
                    >{{ item.chart[0].value || 0 }}</span
                  >
                </el-tooltip>
                {{ item.chart[0].unit }}
              </p>
              <p class="word-break">{{ item.chart[0].name }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import mixin from '../data-board-mixin'

export default {
  mixins: [mixin],
  methods: {
    graphClick(params) {
      this.$emit('penetrate', params.data)
    },
    clickTable(params, row) {
      params.name = this.dataForm.name + row[0].value
      this.$emit('penetrate', params)
    }
  }
}
</script>

<style lang="scss" scoped>
.customer-stage-board {
  min-height: 100px;
  border-radius: 4px;
  .through {
    color: $link-base-color-6;
    cursor: pointer;
  }
  .list-title {
    margin-top: 36px;
    font-size: 14px;
    color: $text-plain;
    text-align: center;
  }
  .board-content {
    width: 100%;
    margin: 10px auto 0;
    .chance-template-name {
      margin-bottom: 24px;
      font-size: 14px;
      color: rgba(105, 130, 162, 1);
      text-align: center;
    }
    .board-content-chart {
      display: flex;
      flex-direction: row;
      justify-content: center;
      .chart {
        display: inline-block;
        width: 327px;
        height: 260px;
        vertical-align: top;
      }
    }
    .board-table {
      margin-left: 58px;
      :deep(.el-table:before) {
        background-color: $neutral-color-3;
      }
      :deep(.el-table--group::after, .el-table--border::after) {
        background-color: $neutral-color-3;
      }
      :deep(.el-table--border) {
        border-color: $neutral-color-3;
      }
      :deep(.table-td) {
        height: 32px;
        padding: 0;
        line-height: 32px;
        border-color: $neutral-color-3;
      }
      :deep(.table-th) {
        height: 32px;
        padding: 0;
        line-height: 32px;
        background-color: $neutral-color-1;
        border-color: $neutral-color-3;
      }
    }
    .board-info {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      font-size: 12px;
      color: $text-plain;
      vertical-align: top;
      .board-item {
        display: inline-block;
        width: 160px;
        margin-bottom: 30px;
        .item-detail {
          margin-bottom: 6px;
          span {
            display: inline-block;
            max-width: 100px;
            overflow: hidden;
            font-size: 20px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
// .el-carousel {
//   width: 100%;
//   overflow: hidden;
//   .el-carousel__container {
//     height: 320px;
//   }
//   .el-carousel__arrow {
//     top: 175px;
//     width: 32px;
//     height: 32px;
//     color: $neutral-color-5;
//     background-color: #ecf2fa;
//     &:hover {
//       color: $base-white;
//       background-color: $neutral-color-5;
//     }
//   }
//   .el-carousel__arrow--right {
//   }
//   .el-carousel__arrow--left {
//   }
//   .el-carousel__indicators {
//     bottom: 30px;
//     .el-carousel__indicator {
//       padding: 0 7px;
//       .el-carousel__button {
//         width: 20px;
//         height: 8px;
//         background: $neutral-color-3;
//         border-radius: 4px;
//         opacity: 1;
//       }
//     }
//     .is-active .el-carousel__button {
//       background: $brand-color-5;
//     }
//   }
// }
</style>
