<!--
 * @Description: 版本公告
 -->

<template>
  <div class="trends-board">
    <div v-if="dataForm.alias === 'salesReport'" class="board-report">
      <div class="board-head">
        <i class="board-icon web-icon-a-filedone web-iconfont"></i>
        <div class="board-title">{{ salesReport.title || $t('home.createRecord') }}</div>
      </div>
      <div class="board-body">{{ $t('home.referRecord') }}</div>
      <div>
        <el-button type="primary" @click="openSalesReport">{{ $t('home.openDetail') }}</el-button>
        <el-button @click.stop="hideMoudle">{{ $t('home.ignore') }}</el-button>
      </div>
    </div>
    <div
      v-else-if="dataForm.alias === 'announcement'"
      class="board-item new-version"
      @click="openOutChain"
    >
      <span class="item-icon">
        <img alt="" class="item-img" src="../../../../../assets/notice.png" />
      </span>
      <i v-if="!isBoard" class="el-icon-close item-icon-close" @click="hideMoudle"></i>
      <div class="item-content">
        <p>{{ dataForm.name || $t('home.updatedVs') }}</p>
        <p class="content-small">{{ $t('home.seeContent') }}&gt;&gt;</p>
      </div>
    </div>
    <!-- <div class="board-item new-version" v-else> 暂时没有跳转到本部分的帮助中心5分钟上手链接
      <span class="item-icon" @click="openDialog">
        <img class="item-img" src="../../../../../assets/help.png" alt="">
      </span>
      <i class="el-icon-close item-icon-close" v-if="!isBoard" @click="hideMoudle"></i>
      <div class="item-content" @click="openDialog">
        <p>欢迎使用销帮帮CRM</p>
        <p class="content-small">5分钟快速上手指导</p>
      </div>
    </div> -->
  </div>
</template>

<script>
import { singleReadMark } from '@/api/notification.js'
import { getActivityWeb, hideBoard } from '@/api/home.js'
import { mapGetters } from 'vuex'

export default {
  props: {
    salesReport: Object,
    isBoard: Boolean,
    dataForm: Object
  },
  data() {
    return {
      dialogVisible: false,
      htmlCode: '',
      haveIframe: false
    }
  },

  mounted() {},

  computed: {
    ...mapGetters(['getGuideStep'])
  },

  methods: {
    getForm() {
      getActivityWeb()
        .then((res) => {
          // this.htmlCode = res.result.html
          // let iframe = document.createElement('iframe')
          // iframe.setAttribute('width', '610')
          // iframe.setAttribute('height', '600')
          // this.$el.querySelector('.notice').appendChild(iframe)
          // let idocument = iframe.contentDocument
          // idocument.open()
          // idocument.write(this.htmlCode)
          // idocument.close()
          // this.haveIframe = true
        })
        .catch(() => {})
    },

    // 跳转到帮助中心外链
    openOutChain() {
      window.open(this.dataForm.subBusiness.activeUrl)
    },

    hideMoudle() {
      hideBoard({
        chartCategoryId: this.dataForm.chartCategoryId,
        alias: this.dataForm.alias,
        boardType: 2,
        chartCategoryType: 1
      })
        .then(() => {
          this.$emit('hide')
        })
        .catch(() => {})
    },
    openSalesReport() {
      // 先把跳转加上，目前还拿不到id
      singleReadMark({ ids: [this.salesReport.id] })
        .then((data) => {
          this.$router.push(
            `/app/salesReport?start=${this.salesReport.start}&end=${this.salesReport.end}`
          )
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.board-item {
  position: relative;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  height: 90px;
  margin-bottom: 10px;
  cursor: pointer;
  background-color: $base-white;
  border-radius: 4px;
  :deep(.el-dialog__header) {
    background-color: $base-white;
    .el-dialog__title {
      font-size: 20px;
      color: $brand-color-5;
    }
  }
  .item-icon {
    flex: 0 0 auto;
    width: 80px;
    font-size: 34px;
    line-height: 90px;
    text-align: center;
    opacity: 0.7;
    .item-img {
      width: 30px;
    }
  }
  .item-icon-close {
    position: absolute;
    top: 10px;
    right: 8px;
    font-size: 16px;
    color: $text-main;
    cursor: pointer;
    opacity: 0.7;
  }
  .item-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    font-size: 16px;
    color: $text-main;
    .content-small {
      margin-top: 5px;
      font-size: 13px;
      color: $text-plain;
      cursor: pointer;
    }
  }
}
.board-report {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 20px;
  margin-bottom: 10px;
  background: $base-white;
  border-radius: 4px;
  .board-head {
    display: flex;
    align-items: center;
  }
  .board-icon {
    margin-right: 11px;
    color: $brand-color-5;
  }
  .board-title {
    font-size: 16px;
    color: $text-main;
  }
  .board-body {
    margin: 13px 0 16px 0;
    font-size: 14px;
    color: $text-plain;
  }
}
</style>

<style lang="scss">
.index-column-main-right {
  .el-dialog__footer {
    text-align: center;
  }
}
</style>
