<!--
 * @Description: '右侧卡片审批进展'
 -->

<template>
  <div v-if="list.length || isBoard" class="approve-board" :class="{ isBoard: isBoard }">
    <el-carousel
      ref="pendCarousel"
      v-loading="loading"
      arrow="never"
      :autoplay="false"
      indicator-position="none"
    >
      <h1 class="board-title">
        <span>{{ $t('home.approveEvolve') }}（{{ totalCount }}）</span>
        <el-button v-if="!isBoard" size="mini" type="text" @click="turnItem">{{
          $t('message.showMore')
        }}</el-button>
      </h1>
      <el-carousel-item v-for="(item, index) in list" :key="index">
        <div class="board-info">
          <div class="info-title">
            <span class="word-break" v-html="addNodeByWXInText(item.title)"></span>
          </div>
          <p v-for="(childItem, index) in item.summaryList" :key="index" class="info-detail">
            <span>{{ childItem.attrName }}</span
            >:
            <span v-if="childItem.value.length > 0">
              <span v-for="(str, index) in childItem.value" :key="index">
                <el-tooltip :content="str + ''" placement="top">
                  <i class="info-detail-i" v-html="addNodeByWX(str)"></i>
                </el-tooltip>
              </span>
            </span>
            <span v-else>
              <i class="info-detail-i">--</i>
            </span>
          </p>
          <div class="info-approval">
            <ul v-if="currentLog.length > 0" v-loading="logLoading">
              <li
                is="BoardApproval"
                v-for="childItem in currentLog"
                :key="JSON.stringify(childItem)"
                class="info-approval-item"
                :prop-item="childItem"
              ></li>
            </ul>
          </div>
        </div>
        <el-button
          v-if="processOperate.length > 0"
          class="back-btn"
          size="medium"
          @click="taskBtnClick(item.processNodeTask)"
          >{{ processOperate[0].text }}
        </el-button>
      </el-carousel-item>
      <div v-if="list.length > 1 || isShowLine" class="board-line"></div>
      <div v-if="list.length > 1" class="board-operate">
        <i class="board-arrow web-icon-left web-iconfont" @click="prev"></i>
        <i class="board-arrow web-icon-right web-iconfont" @click="next"></i>
      </div>
    </el-carousel>
  </div>
</template>

<script>
import {
  getTaskIndexCreateList,
  getTaskIndexDetail,
  getTaskIndexWorkFlowDetail
} from '@/api/workflow-design.js'
import { getWorkflowIndexCreateList, approvalRevoke } from '@/api/new-approve'
import { mapGetters } from 'vuex'
import { taskUpdate } from '@/api/approve'

import AvatarImg from '@/components/base/avatar-img.vue'
import xbb from '@xbb/xbb-utils'
import BoardApproval from '../../components/board-approval'

export default {
  props: {
    isBoard: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      loading: true,
      logLoading: true,
      num: 0,
      currentLog: {},
      processOperate: {},
      currentIndex: 0
    }
  },

  computed: {
    ...mapGetters([
      'isOpenWorkflow' // 工作流是否升级状态
    ]),
    totalCount() {
      return this.num > 5 ? '5+' : this.num
    },
    isShowLine() {
      return this.processOperate.length > 0
    }
  },

  components: {
    AvatarImg,
    BoardApproval
  },
  mounted() {
    // 如果是看板设置使用假数据，首页使用真实数据
    if (this.isBoard) {
      this.list = [
        {
          title: '张杰的合同订单',
          processNodeTask: {
            addTime: 1554106087
          },
          summaryList: [
            {
              attr: 'text_1',
              attrName: '合同名称',
              fieldType: 1,
              value: ['Misaki9']
            },
            {
              attr: 'text_2',
              attrName: '合同金额',
              fieldType: 1,
              value: [4565968]
            }
          ],
          latestApproveUserList: []
        }
      ]
      this.processOperate = []
      this.currentLog = [
        {
          addTime: 1634889667,
          attachmentList: [],
          counterSignUnFinish: 0,
          id: 132509,
          images: [],
          isMe: 1,
          isMeDesc: '我',
          nodeType: 1,
          operatorUser: {
            active: 1,
            addTime: 1493804366,
            avatar: '/images/default.jpg',
            belongDepartment: '',
            corpName: '',
            corpid: '1',
            del: 0,
            department: '[1495077072428, 1495077072361, 1495077072478, 1495077071909]',
            departmentNames: '',
            dingId: '',
            extattr: '',
            id: 115,
            isAdmin: 0,
            isBoss: 0,
            isFirstFp: 0,
            isLeader: 0,
            isLeaderInDepts:
              '{1495077071909:false,1495077072428:false,1495077072478:false,1495077072361:false}',
            isPushMessage: 1,
            isVisible: 1,
            isWarehouseManager: 0,
            jobnumber: '',
            leader: false,
            manageDepartment: '',
            mobile: '15068821514',
            name: '吴剑',
            orderInDepts: '{1495077071909:1,1495077072428:1,1495077072478:1,1495077072361:1}',
            oriAdmin: true,
            passportId: 35,
            position: '驱蚊器',
            roleIds: '|1|2|3|',
            roleName: '',
            sessionId: '',
            updateTime: 1638754169,
            userId: 'wujian',
            warehouseManager: false
          },
          opinion: '',
          signType: 1,
          signTypeDesc: '或签',
          startNodeDesc: '发起申请',
          taskType: 1,
          taskTypeDesc: '通过',
          templateNodeId: 2808,
          templateNodeName: '流程开始节点',
          unCommitNodeTaskList: [],
          updateTime: 1634889667
        }
      ]
      this.num = 1
      this.loading = false
      this.logLoading = false
    } else {
      this.getTaskIndexCreateList()
    }
  },
  methods: {
    getTaskIndexCreateList() {
      this.loading = true
      this.logLoading = true
      // 升级和未升级调用不同接口
      // let getTaskMap = this.isOpenWorkflow ? getTaskIndexCreateList : getWorkflowIndexCreateList
      const getTaskMap = this.isOpenWorkflow ? getWorkflowIndexCreateList : getTaskIndexCreateList
      getTaskMap({})
        .then((res) => {
          if (res.result.pageHelper && res.result.pageHelper.rowsCount) {
            this.num = res.result.pageHelper.rowsCount
          }
          this.list = res.result.list
          this.loading = false
        })
        .then(() => {
          if (this.list.length === 0) return
          this.getTaskIndexDetail(this.list[0].processNodeTask)
        })
    },
    getTaskIndexDetail(processNodeTask) {
      const getTaskDetail = this.isOpenWorkflow ? getTaskIndexWorkFlowDetail : getTaskIndexDetail
      const { processType, appId, menuId, formId, saasMark, businessType } = processNodeTask
      const params = {
        processType,
        appId,
        menuId,
        formId,
        saasMark,
        businessType,
        processTaskId: processNodeTask.taskId,
        processNodeTaskId: processNodeTask.id,
        needRecords: 1,
        needOperate: 1
      }
      getTaskDetail(params).then((res) => {
        this.logLoading = false
        this.currentLog = res.result.processNodeTaskRecordsPojo.currentLog
        this.processOperate =
          res.result.processNodeTaskOperatePojo.processOperatePermission.processOperate
      })
    },
    turnItem() {
      const { processType, id, taskId } = this.list[this.currentIndex].processNodeTask
      this.$router.push({
        path: '/todo/list',
        query: {
          processType,
          processNodeTaskId: id,
          processTaskId: taskId
        }
      })
    },
    submitTimeFormat(time) {
      const obj = xbb.timestampToTime(time) || {}
      return obj.year
        ? obj.year + '-' + obj.month + '-' + obj.day + ' ' + obj.hours + ':' + obj.minutes
        : ''
    },
    prev() {
      this.$refs.pendCarousel.prev()
      this.currentLog = []
      this.processOperate = []
      this.logLoading = true
      this.currentIndex = this.$refs.pendCarousel.activeIndex
      this.getTaskIndexDetail(this.list[this.currentIndex].processNodeTask)
    },
    next() {
      this.$refs.pendCarousel.next()
      this.currentLog = []
      this.processOperate = []
      this.logLoading = true
      this.currentIndex = this.$refs.pendCarousel.activeIndex
      this.getTaskIndexDetail(this.list[this.currentIndex].processNodeTask)
    },
    taskBtnClick(processNodeTask) {
      const data = {
        menuId: processNodeTask.menuId,
        appId: processNodeTask.appId, // 应用的id 必传
        saasMark: processNodeTask.saasMark,
        businessType: processNodeTask.businessType,
        formId: processNodeTask.formId,
        processTaskId: processNodeTask.taskId, // 流程任务id 必传
        taskType: 9
      }
      const taskId = this.isOpenWorkflow ? 'taskNodeId' : 'processNodeTaskId'
      const recallTask = this.isOpenWorkflow ? approvalRevoke : taskUpdate
      // 流程任务id 必传
      data[taskId] = processNodeTask.id
      recallTask(data).then(() => {
        this.getTaskIndexCreateList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.approve-board {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 382px;
  background: $base-white;
  border-radius: 4px;
  .board-title {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    box-sizing: border-box;
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 54px;
    padding: 0 20px;
    font-size: 16px;
    color: $text-main;
    border-bottom: 1px solid $neutral-color-3;
  }
  .board-btn-list {
    position: absolute;
    top: 13px;
    right: 20px;
  }
  .board-info {
    box-sizing: border-box;
    height: 262px;
    padding-top: 16px;
    margin: 0 20px;
    margin-top: 52px;
    overflow-y: auto;
    font-size: 14px;
    color: $text-plain;
    .info-title {
      margin: 0 0 14px 0;
      font-size: 16px;
      color: $brand-color-5;
      .time {
        float: right;
        font-size: 12px;
        color: $text-auxiliary;
      }
    }
    .info-detail {
      margin-bottom: 16px;
      overflow: hidden;
      line-height: 1.1;
      text-overflow: ellipsis;
      white-space: nowrap;
      .info-detail-i {
        font-style: initial;
      }
    }
    .info-node {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      .info-label {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        overflow: hidden;
        border-radius: 100%;
        .img {
          width: 100%;
          height: 100%;
        }
        .span {
          display: inline-block;
          width: 100%;
          height: 100%;
          font-size: 14px;
          line-height: 32px;
          color: $base-white;
          text-align: center;
          background: $link-base-color-6;
        }
      }
      .text {
        line-height: 18px;
      }
    }
  }
  .board-line {
    position: absolute;
    right: 0;
    bottom: 66px;
    width: calc(100% - 40px);
    margin-right: 20px;
    border-top: 1px solid $neutral-color-3;
  }
  .board-operate {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 65px;
    margin-right: 20px;
  }
  .board-arrow {
    margin-left: 44px;
    color: $text-auxiliary;
    cursor: pointer;
  }
  .back-btn {
    position: absolute;
    bottom: 15px;
    left: 20px;
    z-index: 999;
  }
  .info-approval {
    padding: 16px 0;
    border-top: 1px solid $neutral-color-3;
  }
  .info-approval-item {
    position: relative;
    &:before {
      position: absolute;
      bottom: -24px;
      left: 20px;
      height: 24px;
      content: '';
      border-left: 2px solid $neutral-color-3;
    }
    &:last-child::before {
      border: none;
    }
  }
}
</style>

<style lang="scss">
.approve-board {
  margin-bottom: 10px;
  &.isBoard {
    .el-carousel__container {
      .el-carousel__arrow {
        display: none;
      }
    }
  }
  .el-carousel__container {
    height: 382px !important;
    .board-info {
      overflow-y: auto;
      cursor: pointer;
    }
  }
}
</style>
