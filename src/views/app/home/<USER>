<!--
 * @Description: 首页
 -->

<template>
  <div class="index" :style="customPanelStyle">
    <div class="index-column-main">
      <div ref="indexColumnMainTop" class="index-column-main-top" :style="indexTopBackground">
        <!-- 首页切换tabs -->
        <index-manage
          ref="indexManage"
          :index-width="indexWidth"
          :tab-title-style="indexTopTitle"
          @changeIndex="changeIndex"
          @openIndexManage="isShowIndexDialog = true"
          @setChartOption="setChartOption"
        ></index-manage>
        <div class="option_box">
          <div
            v-if="!chartPanelFullScreen && linkageState"
            class="chart-render__button"
            @click="cancelLinkage"
          >
            <el-button>{{ $t('display.chartCustom.cancelLinkage') }}</el-button>
          </div>
          <div class="chart-render_select">
            <div v-if="globalList.length > 0 && !crmIndex" class="select-item">
              <!-- 重置图标 -->
              <el-button v-if="isShowReset" class="clear-icon" @click="resetNomalSelect">
                <i class="web-icon-qingliclear web-iconfont"></i>
              </el-button>
              <div class="select-item_box" :style="indexTopTitle">
                <!-- <span class="select-item__label">{{ $t('form.chooseTime') }}</span> -->
                <multi-tag
                  ref="multiTag"
                  class="select-form__multi-select"
                  :is-bi="true"
                  :prop-format="mulitFormat"
                  :selected-tag="checkId"
                  :style="indexTopBackground"
                  @click.native="personSelectVisitOpen"
                  @tagDelete="tagDelete"
                >
                </multi-tag>
              </div>
              <div class="select-item_box" :style="indexTopTitle">
                <!-- <span class="select-form__label">{{ $t('form.chooseRange') }}</span> -->

                <time-selector
                  ref="timeSelector"
                  class="select-item__time"
                  :is-bi="true"
                  :is-fiscal-year="true"
                  :set-default="false"
                  @deleteParam="deleteParam"
                  @saveFilter="saveFilter"
                >
                  <span slot="title-lable"></span>
                </time-selector>
              </div>
            </div>
          </div>
          <div class="index-top-right">
            <!-- 全屏下的按钮展示（hover时显示全部按钮） -->
            <template v-if="chartPanelFullScreen">
              <div class="hover-show-box">
                <el-button
                  v-if="!crmIndex && chartPanelFullScreen"
                  type="primary"
                  @click="panelFullScreenHandler"
                >
                  {{ $t('operation.exitFullScreen') }}
                </el-button>
              </div>
            </template>
            <!-- 未全屏时展示的按钮 -->
            <template v-else>
              <!-- 顶部的筛选条件 -->
              <index-top-filter
                v-if="crmIndex"
                :filter-cache="filterCache"
                :filter-param="filterParam"
                :is-fiscal-year="isFiscalYear"
              />
              <!-- 快捷新建入口 -->
              <quick-new @openMenuDialog="isShowMenuDialog = true"></quick-new>
              <!-- 首页看板设置入口 -->
              <router-link
                v-if="showPageSet"
                v-tooltip.bottom="$t('home.setBoardTips')"
                class="set-board"
                to="/board"
              >
                <el-button icon="el-icon-setting" @click="categoryIndexSetUpdate">{{
                  $t('home.setBoard')
                }}</el-button>
              </router-link>
              <!-- 首页管理按钮 -->
              <el-button
                v-tooltip.bottom="$t('display.dashboard.homepageManageTips')"
                class="homepage-manage"
                @click="showIndexManage"
              >
                <i class="el-icon-setting"></i> {{ $t('display.dashboard.homepageManage') }}
              </el-button>
              <!-- 首页仪表盘全屏 -->
              <el-button
                v-if="!crmIndex"
                v-tooltip.bottom="$t('operation.fullScreen')"
                class="bi-full-screen_btn"
                @click="panelFullScreenHandler"
              >
                {{ $t('operation.fullScreen') }}
              </el-button>
            </template>
          </div>
        </div>
      </div>
      <!-- 首页看板 -->
      <div id="main-area" v-loading="loadingFull" class="index-column-main-bottom">
        <template v-if="crmIndex">
          <!-- 左侧看板 -->
          <div class="index-column-main-left">
            <div v-for="(item, index) in leftList" :key="index" class="index-main-left-item">
              <vue-lazy-component>
                <left-board
                  :key="item.alias + index"
                  ref="leftBoard"
                  :filter-cache="filterCache"
                  :home-page-id="homePageId"
                  :is-fiscal-year="isFiscalYear"
                  :item="item"
                  @penetrate="penetrate"
                  @refreshBoard="getInitBoard"
                ></left-board>
                <div slot="skeleton" class="skeleton-left"></div>
              </vue-lazy-component>
            </div>
          </div>
          <!-- 右侧看板 -->
          <div class="index-column-main-right">
            <div v-for="(item, index) in rightList" :key="index" class="index-main-right-item">
              <vue-lazy-component>
                <right-board
                  :key="item.alias + index"
                  ref="rightBoard"
                  :item="item"
                  :sales-report="salesReport || {}"
                  @hide="getInitBoard"
                  @penetrate="penetrate"
                ></right-board>
                <div slot="skeleton" class="skeleton-right"></div>
              </vue-lazy-component>
            </div>
          </div>
        </template>
        <template v-else>
          <chart-panel
            :key="refreshKey"
            ref="chartPanel"
            :chart-option="chartOption"
            class="chart-panel"
            :global-param="globalParam"
            :global-style-option="globalStyleOption"
          />
        </template>
      </div>
      <!-- 穿透 -->
      <div v-if="clickPenetrate" class="penetrate">
        <penetrate
          :is-report="isReport"
          :penetrate-visible.sync="clickPenetrate"
          :throught="penetrateThrought"
        ></penetrate>
      </div>
    </div>
    <menu-setting-dialog
      v-if="isShowMenuDialog"
      :is-active="isShowMenuDialog"
      @closeDialog="handleMenuSetting"
    ></menu-setting-dialog>
    <el-dialog
      :class="{ activeStop: activeAlias === 'stop' || activeAlias === 'updating' }"
      :close-on-click-modal="activeAlias !== 'stop' && activeAlias !== 'updating'"
      :close-on-press-escape="activeAlias !== 'stop' && activeAlias !== 'updating'"
      :show-close="activeAlias !== 'stop' && activeAlias !== 'updating'"
      title="公告"
      :visible.sync="dialogVisible"
      :width="activeAlias !== 'stop' && activeAlias !== 'updating' ? '640px' : '445px'"
    >
      <div class="notice">
        <img
          alt=""
          class="coverPicture"
          :class="{ 'stop-update-img': activeAlias === 'stop' || activeAlias === 'updating' }"
          :src="coverPicture"
        />
      </div>
      <span v-if="isClose" slot="footer">
        <div v-if="activeAlias === 'stop' || activeAlias === 'updating'">
          <div class="checkbox-box">
            <el-checkbox v-model="isknowStop">{{ $t('home.accept') }}</el-checkbox>
            <p v-if="!isknowStop && openActiveStopHint" class="checkbox-box-hint">
              {{ $t('home.acceptPls') }}
            </p>
          </div>
          <el-button round size="medium" type="primary" @click="closeActiveStop">{{
            $t('operation.confirm')
          }}</el-button>
        </div>
        <el-button v-else type="primary" @click="openChain">{{
          $t('operation.seeDetail')
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 通知提示 -->
    <div v-if="adminNotice" class="expiry-remind system-info">
      <span class="admin-notice">
        <i class="el-icon-warning icon"></i>
        <el-tooltip :content="adminMsg.noticeContent" effect="dark" placement="bottom-start">
          <span
            style="
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              white-space: nowrap;
            "
          >
            {{ adminMsg.noticeContent }}
          </span>
        </el-tooltip>
        <el-button size="medium" type="text" @click="adminDialog = true">
          {{ adminMsg.buttonContent }}
        </el-button>
      </span>
      <i class="web-icon-tishi_guanbi web-iconfont" @click="adminClick(0)"></i>
    </div>
    <div v-if="noticeText && noticeText != ''" class="expiry-remind other-info">
      <span class="jump-url" @click="goNoticeUrl">
        <i class="el-icon-warning icon"></i>
        {{ noticeText }}
      </span>
      <i class="web-icon-tishi_guanbi web-iconfont" @click="noticeText = ''"></i>
    </div>
    <!-- 消息提示的弹窗显示  -->
    <el-dialog
      :close-on-click-modal="false"
      :show-close="false"
      title="提醒"
      top="14%"
      :visible.sync="adminDialog"
      width="520px"
      @close="adminClick(1)"
    >
      <div class="service-info">
        <span style="word-break: break-all">
          {{ adminMsg.popupContent }}
        </span>
      </div>
      <div class="dialog-footer" style="border-top: none">
        <el-button type="primary" @click="adminDialog = false">知道了</el-button>
      </div>
    </el-dialog>
    <!-- 返回顶部按钮 -->
    <el-backtop
      target=".index-column-main .index-column-main-bottom"
      :visibility-height="clientHeight"
    >
      <div class="inside">
        <div class="text">
          <span>TOP</span>
          <i class="web-icon-xiangshang web-iconfont"></i>
        </div>
      </div>
    </el-backtop>
    <!-- <person-select
      v-if="personSelectVisit && showTabsName.length > 0"
      ref="personSelect"
      :default-val.sync="checkId"
      :dialog-visible.sync="personSelectVisit"
      :show-tabs-name="showTabsName"
      @dialogSubmit="personSubmit"
    >
    </person-select> -->
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import PersonSelect from '@/components/person-select/index'
import TimeSelector from '@/components/date-time/time-selector.vue'
import MultiTag from '@/components/select-tree/multi-tag.vue'
import {
  initBoard,
  getActivityWeb,
  getNotice,
  closeNotice,
  categoryIndexSetUpdate
} from '@/api/home.js'
import { getEsignRemind } from '@/api/esign.js'
import { getBIConfig } from '@/api/statistics'

import { component as VueLazyComponent } from '@xunlei/vue-lazy-component'
import penetrate from '@/views/penetrate'
import MenuSettingDialog from '@/components/layout/app-layout/components/menu-setting-dialog'

import indexTopFilter from './components/index-top-filter'
import quickNew from './components/quick-new'
import indexManage from './components/index-manage'
import leftBoard from './components/left-board'
import rightBoard from './components/right-board'

import chartPanel from '@/views/chart-management/display/chart-render/chart-panel'
import xbb from '@xbb/xbb-utils'
import panelStyleMixin from '@/views/chart-edit/mixins/panel-style-mixin.js'
import panelFullScreenMixin from '@/views/chart-management/display/mixins/full-screen.js'
// import { gioHomeSystemBoard } from '@/utils/buriedPoint.js'
import { getSearchList } from '@/api/panel-design.js'

export default {
  name: 'Index',

  components: {
    ChartPanel: chartPanel,
    IndexTopFilter: indexTopFilter,
    QuickNew: quickNew,
    IndexManage: indexManage,
    LeftBoard: leftBoard,
    RightBoard: rightBoard,
    Penetrate: penetrate,
    MenuSettingDialog,
    VueLazyComponent: VueLazyComponent,
    TimeSelector,
    MultiTag
  },

  mixins: [panelStyleMixin, panelFullScreenMixin],

  data() {
    return {
      checkId: [],
      activeUrl: '', // 公告跳转链接
      coverPicture: '', // 公告图片
      dialogVisible: false, // 公告弹窗
      leftList: [], // 左侧看板
      rightList: [], // 右侧看板
      filterCache: {},
      filterParam: {},
      isFiscalYear: false,
      loadingFull: true,
      // 简报时间
      salesReport: {},
      clickPenetrate: false,
      penetrateThrought: '',
      isReport: false, // 是否是简报看板
      isShowMenuDialog: false,
      // 首页管理
      isShowIndexDialog: false,
      // expireRemind: false,
      // 多应用到期
      servicesData: [],
      hightVersion: false,
      expireTime: '', // 到期时间
      activeAlias: '',
      isknowStop: false,
      openActiveStopHint: false,
      topShow: false,
      isClose: true, // 公告是否可以关闭
      indexName: '',
      crmIndex: true, // 默认crm首页
      showPageSet: false, // true为展示看板，false为不展示
      chartOption: { chartList: [] },
      refreshKey: 1, // 避免diff算法缓存
      noticeText: '',
      noticeUrl: '',
      // admin 定义提示
      adminNotice: false,
      adminDialog: false,
      adminMsg: {
        buttonContent: '',
        noticeContent: '',
        popupContent: '',
        noticeId: 0,
        title: ''
      },
      globalStyleOption: {}, // 全局样式配置
      chartPanelFullScreen: false, // 仪表盘全屏
      homePageId: 0, // 当前首页ID
      personSelectVisit: false,
      showTabsName: ['dept', 'user'],
      // 默认全局筛选为全年
      globalParam: {
        timeFilter: {
          day: '',
          endTime: '',
          leftActive: 'annual',
          maxYear: '',
          month: '',
          season: '',
          startTime: '',
          timeType: 5,
          year: new Date().getFullYear()
        },
        timeTabel: '',
        global: 0,
        checkedId: []
      },
      // 多选回显值
      mulitFormat: {
        label: 'name'
      },
      searchList: [],
      globalList: [],
      indexWidth: 0, // index的宽度，给vTab用
      selectTime: false
      // isShowReset: true
    }
  },
  watch: {
    // leftList(val) {
    //   console.trace('val', val)
    // }
  },
  computed: {
    ...mapGetters(['penetrateHead', 'getGuideStep', 'linkageState', 'enableESign']),
    appsName() {
      let name = ''
      const length = this.servicesData.length - 1
      this.servicesData.forEach((element, index) => {
        if (index === length) {
          name += element.name
        } else {
          name += element.name + '、'
        }
      })

      return name
    },
    // dom中的.index(用于浏览器全屏避免每次重复获取dom)
    indexDom() {
      return document.querySelector('.index')
    },
    clientHeight() {
      return document.documentElement.clientHeight || 500
    },
    isShowReset: {
      get() {
        return this.checkId.length || this.selectTime
      },
      set() {}
    }
  },
  mounted() {
    if (
      window.location.search.includes('isBackSuccessPage') ||
      window.location.search.includes('auth_code')
    ) {
      this.wechatCallback()
      return
    }
    // 公告
    localStorage.setItem('isActivityHead', false)
    // 延时请求接口，避免造成阻塞
    setTimeout(() => {
      this.getBiConfigList()
      this.getActivityWeb()
      // this.handlerAppRemind()
      this.getEsignRemind()
      this.getAdminNotice()
    }, 3000)

    const resizeObserver = new ResizeObserver(([entry] = []) => {
      const [size] = entry.borderBoxSize || []
      this.indexWidth = size.inlineSize
    })
    resizeObserver.observe(this.$refs.indexColumnMainTop)
  },
  beforeRouteEnter(to, from, next) {
    // 当外链分享编辑人未曾登陆过且需要扫描二维码登陆后，扫描后会默认跳转进首页中，这里判断如果存在外链编辑参数，则重定向到外链编辑页中
    if (localStorage.getItem('isOuterLink')) {
      next({ name: 'outerLinkEdit' })
    } else {
      next()
    }
  },

  beforeDestroy() {
    this.removeFullScreenWatch()
  },
  methods: {
    ...mapActions(['setPenetrateHead', 'formDataEdit']),
    // 首页看板索引设置
    categoryIndexSetUpdate() {
      categoryIndexSetUpdate()
        .then(() => {
          console.log('成功')
        })
        .catch(() => {})
    },

    async getBiConfigList() {
      try {
        const { code, result } = await getBIConfig()
        if (code === 1) {
          const { biConfigList } = result
          const biConfigMap = Object.fromEntries(
            biConfigList.map((item) => {
              return [item.alias, item]
            }) || []
          )

          utils.SS.set('biConfigMap', JSON.stringify(biConfigMap))
        }
      } catch (e) {
        console.log('=============>%c[499]: e', 'color:#fc6528', e)
      }
    },

    // admin 自定义提示
    getAdminNotice() {
      getNotice()
        .then(({ result }) => {
          if (!result.noticePojo) return
          const notice = result.noticePojo
          this.adminNotice = true
          this.adminMsg.buttonContent = notice.buttonContent
          this.adminMsg.noticeContent = notice.noticeContent
          this.adminMsg.popupContent = notice.popupContent
          this.adminMsg.noticeId = notice.id
          this.adminMsg.title = notice.title
        })
        .catch((_) => {})
    },
    adminClick(type) {
      this.adminDialog = false
      this.adminNotice = false
      closeNotice({
        isCreateOpportunity: type,
        noticeId: this.adminMsg.noticeId,
        title: this.adminMsg.title
      })
        .then((_) => {})
        .catch((_) => {})
    },
    closeActiveStop() {
      if (!this.isknowStop) {
        this.openActiveStopHint = true
        return false
      }
      this.dialogVisible = false
      if (this.topShow) localStorage.setItem('isActivityHead', true)
    },
    // 打开公告弹窗
    openDialog(result) {
      if (this.activeUrl && this.coverPicture && result.hasPop) {
        this.dialogVisible = true
      } else if (result.activeAlias === 'updating' && !result.hasPop && result.topShow) {
        localStorage.setItem('isActivityHead', true)
      }
    },

    // 查看公告外链
    openChain() {
      window.open(this.activeUrl)
    },

    // 获取公告
    getActivityWeb() {
      getActivityWeb()
        .then((res) => {
          if (res && res.result) {
            this.activeUrl = res.result.activeUrl
            this.coverPicture = res.result.coverPicture
            this.activeAlias = res.result.activeAlias
            this.topShow = res.result.topShow
            this.isClose = res.result.close
            if (!res.result.topShow) localStorage.setItem('isActivityHead', false)
            if (this.getGuideStep === 0) {
              this.openDialog(res.result)
            }
          }
        })
        .catch(() => {})
    },

    getInitBoard() {
      this.loadingFull = true
      this.leftList = []
      const params = {}
      if (this.homePageId) {
        params.homePageId = this.homePageId
      }
      initBoard(params)
        .then((data) => {
          this.leftList = data.result.leftList
          console.log('=================================================', this.leftList)
          const arrTemp = [].concat(data.result.rightList)
          arrTemp.forEach((item, index) => {
            if (item.alias === 'kefu') {
              arrTemp.splice(index, 0, arrTemp.splice(arrTemp.length - 1, 1)[0])
            }
          })
          this.rightList = arrTemp

          this.isFiscalYear = data.result.isFiscalYear
          this.salesReport = data.result.salesReport || null
          for (const i in data.result.filterContent) {
            this.$set(this.filterCache, `${i}`, data.result.filterContent[i])
            this.$set(this.filterParam, `${i}`, data.result.filterContent[i])
          }
          // this.$refs.indexFilter.userName = data.result.filterContent.userName
          utils.LS.set('companyName', data.result.filterContent.userName)
          // localStorage.setItem('companyName', data.result.filterContent.userName)
          setTimeout(() => {
            this.loadingFull = false
          }, 300)
        })
        .catch(() => {})
    },
    gioPushData(title, name) {
      // gioHomeSystemBoard({ name: title, type: name })
    },
    penetrate(item, type) {
      if (!item.through) {
        return
      }
      const copyPenetrate = this.penetrateHead
      copyPenetrate.itemName = ''
      copyPenetrate.backName = this.$t('nouns.homePage')
      copyPenetrate.filterTime = this.filterCache.timeName
      copyPenetrate.filterPerson = this.filterCache.userName ? this.filterCache.userName : ''
      this.isReport = type === 'isReport' && item.statisticsType === 2
      if (this.isReport) copyPenetrate.itemName = item.name
      this.setPenetrateHead(copyPenetrate)
      this.clickPenetrate = true
      this.penetrateThrought = item.through

      // 埋点代码-统计所有系统图表指标
      // if (item.statisticsType === 1) {
      //   gioChart({ chart_types: item.name, chart_location: '首页' })
      // }
    },
    // 仪表盘全屏时禁止弹出
    personSelectVisitOpen() {
      if (+utils.SS.get('chart-panel-fullscreen') === 1) {
        return
      }
      PersonSelect({
        activeTabName: 'user',
        showTabsName: this.showTabsName,
        tabMultiple: true,
        multiple: {
          dept: true,
          user: true
        },
        otherParams: {
          deptVisibleRule: true,
          organization: true
        }
      }).then((res) => {
        this.personSubmit(res.data)
      })
    },
    // 删除选中范围
    tagDelete(tag) {
      const deleteTag = this.checkId.filter((item) => {
        return item.id === tag.id
      })
      const index = this.checkId.indexOf(deleteTag[0])
      this.checkId.splice(index, 1)
      this.globalParam.checkedId = this.checkId
      this.selectChart()
    },
    handleMenuSetting() {
      this.isShowMenuDialog = !this.isShowMenuDialog
    },
    handlerToAppGrant({ alias, appModuleId }) {
      this.$router.push(
        `/appModule/appGrant?alias=${alias}&appModuleId=${appModuleId}&operateText=upgradeRenewal`
      )
    },
    // 格式化时间
    handlerTimeFormat(time) {
      return xbb.timestampToTimeString(time, 'yyyy-MM-dd HH:mm')
    },
    // 首页更换
    changeIndex(name, id, isCustom) {
      this.showPageSet = id === 0
      this.crmIndex = id === 0 || isCustom === 1
      this.indexName = this.crmIndex ? '' : name
      this.homePageId = id
      // 如果切换回crm首页，则不使用仪表盘自定义背景
      if (this.crmIndex) {
        this.globalStyleOption = {}
      } else {
        // 仪表盘
        // 添加全屏事件监听并进行仪表盘重绘
        this.getSearchList(id)
        this.addFullScreenWatch(() => {
          this.chartPanelFullScreen = false
        })
      }
      if (this.crmIndex) {
        this.getInitBoard()
      }
      this.cancelLinkage()
    },
    // 仪表盘首页数据
    setChartOption(options) {
      this.loadingFull = true
      this.chartOption = options
      this.globalStyleOption = options.globalStyleOption
      this.refreshKey = Math.random()
      this.$nextTick(() => {
        this.$refs['chartPanel'].classifyChart()
        this.loadingFull = false
      })
    },
    getEsignRemind() {
      if (!this.enableESign) return
      getEsignRemind().then((res) => {
        console.log(res)
        this.noticeText = res.result.content
        this.noticeUrl = res.result.url
      })
    },
    goNoticeUrl() {
      this.$router.push(this.noticeUrl)
    },
    // 仪表盘首页全屏
    panelFullScreenHandler() {
      const doc = this.indexDom
      if (this.chartPanelFullScreen) {
        this.chartPanelFullScreen = false
        utils.SS.set('chart-panel-fullscreen', 0)
        this.exitFullScreenFn()
      } else {
        this.chartPanelFullScreen = true
        utils.SS.set('chart-panel-fullscreen', 1)
        this.fullScreenFn(doc)
      }
    },
    // 时间控件
    saveFilter(param, isSave, text) {
      this.selectTime = true
      console.log(this.$t('nouns.time'), param, isSave, text)
      this.globalParam.timeFilter = JSON.parse(JSON.stringify(param))
      this.globalParam.timeTabel = JSON.parse(JSON.stringify(text))
      this.globalParam.global = 1
      this.selectChart(true)
    },
    // 范围控件
    personSubmit(val) {
      this.checkId = val
      this.globalParam.checkedId = this.checkId
      this.globalParam.global = val.length ? 1 : 0
      this.selectChart(true)
    },
    // 获取查询条件
    getSearchList(id) {
      if (!id) return

      getSearchList({
        categoryId: id
      }).then(({ result }) => {
        this.globalList = result.searchList.filter((item) => item.global === 1)
        this.searchList = result.searchList
      })
    },
    // 根据查询调件判断哪个表需要处理
    selectChart(hasPerson = false) {
      const totalSearch = this.searchList.reduce((prev, cur) => {
        prev = prev.concat(cur.fields)
        return prev
      }, [])
      this.finalSearchIds = totalSearch.reduce((prev, cur) => {
        prev[cur.id] = cur.search.concat(prev[cur.id]).filter((i) => i)
        return prev
      }, {})
      if (Object.keys(this.finalSearchIds).length) {
        Object.keys(this.finalSearchIds).forEach((i) => {
          this.chartOption.chartList.forEach((j) => {
            if (j.id === +i) {
              this.$refs['chartPanel'].getChartResult(
                [String(i)],
                this.finalSearchIds[i],
                null,
                [{ id: String(i), statisticsType: j.statisticsType, systemCode: j.systemCode }],
                j.single,
                hasPerson
              )
            }
          })
        })
      }
    },
    // 显示首页管理弹窗
    showIndexManage() {
      this.$refs.indexManage.changeDialogVisible()
    },
    // 清空选择的全局筛选时间
    deleteParam() {
      console.log('dasdsaå')
      this.checkId = []
      this.globalParam = {
        timeFilter: {},
        timeTabel: '',
        global: 0,
        checkedId: []
      }
      setTimeout(() => {
        this.selectChart()
      }, 1000)
    },
    resetNomalSelect() {
      this.selectTime = false
      this.checkId = []
      console.log('timeSelector', this.$refs['timeSelector'])
      this.$refs['timeSelector'].activeText = '全部'
      this.$refs['multiTag'].selectedTag = []
      this.deleteParam()
    },
    // 取消联动
    cancelLinkage() {
      // 重新调用请求
      this.chartOption.chartList.forEach((j) => {
        if (this.$refs['chartPanel']) {
          this.$refs['chartPanel'].getChartResult(
            [String(j.id)],
            this.finalSearchIds ? this.finalSearchIds[j.id] : null,
            null,
            [{ id: String(j.id), statisticsType: j.statisticsType }],
            j.single
          )
          this.$store.commit('SET_LINKAGE_ARGU', {})
          this.$store.commit('SET_LINKAGE_STATE', false)
          this.$root.eventHub.$emit('cancelHighLight')
        }
      })
    },
    // 微信公众号回调跳转
    wechatCallback() {
      if (window.location.search.includes('isBackSuccessPage')) {
        window.location.href = `${window.location.origin}/#/success-page${window.location.search}`
        return
      }
      if (window.location.search.includes('auth_code')) {
        const routePath = window.location.search.includes('isAppletAuth')
          ? 'Setting/autoPortal'
          : 'Setting/autoWork'

        const { auth_code, expires_in } = xbb.param2Obj()
        // 拼接新的URL
        const newUrl = `${window.location.origin}/#/${routePath}?index=3&auth_code=${auth_code}&expires_in=${expires_in}`
        window.location.href = newUrl
      }
    }
  }
}
</script>

<style lang="scss">
.activeStop {
  .el-dialog {
    border-radius: 8px;
  }
  .el-dialog > .el-dialog__header {
    display: none;
  }
  .el-dialog > .el-dialog__body {
    .stop-update-img {
      border-radius: 8px;
    }
    padding: 0;
  }
  .el-dialog > .el-dialog__footer {
    height: auto !important;
    padding: 0 20px 30px 20px;
    line-height: initial;
    border: 0;
    border-radius: 8px;
    .checkbox-box {
      margin-bottom: 30px;
      text-align: left;
      .checkbox-box-hint {
        padding-left: 24px;
        margin-top: 10px;
        color: $brand-color-5;
      }
    }
    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: $brand-color-5 !important;
    }
    .el-button.is-round {
      width: 250px !important;
    }
  }
}
.index {
  .board {
    min-width: 750px;
    padding-top: 16px;
    margin-bottom: 10px;
    background: $base-white;
    .board-title {
      position: relative;
      padding: 0 20px 16px;
      font-size: 16px;
      color: $text-main;
      border-bottom: 1px solid $neutral-color-3;
      .board-time {
        padding: 2px 6px;
        background: $bg-primary;
        border-radius: 2px;
      }
      span {
        position: relative;
        margin-left: 15px;
        font-size: 12px;
        color: $text-auxiliary;
        .time-selector {
          position: absolute;
          left: 0;
          span {
            font-size: 0;
            color: $text-plain;
          }
          .el-button {
            position: absolute;
            left: 0;
            .arrow-icon {
              margin: -100px;
            }
          }
        }
      }
      .followGlobal {
        position: absolute;
        top: 0;
        right: 20px;
        display: inline-block;
        .clickArea {
          font-size: 14px;
          cursor: pointer;
          user-select: none;
          .checkBtn {
            font-size: 16px;
            color: $neutral-color-3;
          }
          .active {
            color: #ed924f;
          }
        }
        .tooltipIcon {
          font-size: 14px;
          color: #cecece;
        }
        .tooltipNewIcon {
          font-size: 14px;
          color: #8c8c8c;
        }
        .tooltipMargin {
          margin-left: 15px;
        }
      }
      .more {
        position: absolute;
        top: 0;
        right: 15px;
        font-size: 16px;
        color: $text-grey;
        cursor: pointer;
        transform: rotate(90deg);
      }
      .memo {
        position: absolute;
        top: 0;
        right: 40px;
        font-size: 16px;
        color: $text-grey;
        cursor: pointer;
      }
    }
    .no-data {
      width: 750px;
      height: 280px;
      margin: 0 auto;
      font-size: 14px;
      line-height: 280px;
      color: $text-plain;
      text-align: center;
    }
  }
  .el-backtop {
    position: fixed;
    bottom: 60px !important;
    .inside {
      z-index: 999;
      width: 40px;
      height: 40px;
      overflow: hidden;
      line-height: 40px;
      color: $brand-color-5;
      text-align: center;
      cursor: pointer;
      background-color: #f2f5f6;
      border-radius: 5px;
      box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
      transition: all 0.3s;
      .text {
        transition: all 0.3s;
      }
      &:hover > .text {
        transform: translateY(-40px);
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.index {
  box-sizing: border-box;
  height: 100%;
  // padding-top: 10px;
  .time-selector {
    // padding: 7px 0;
    border: 1px solid $line-filter;
    border-radius: 3px;
    :deep(.btn-text .detail) {
      color: $text-tip;
    }
  }
  .chart-render_select {
    margin-top: 7px;
    .select-item {
      display: inline-flex;
      flex-wrap: wrap;
      align-items: center;
      padding-top: 8px;
      &_box {
        display: inline-block;
        // padding-right: 18px;
        padding-bottom: 14px;
        padding-left: 12px;
        .select-form__multi-select {
          width: 120px;
        }
      }
    }
  }
  .select-form__multi-select {
    display: inline-block;
    width: 240px;
  }
  .option_box {
    display: flex;
    align-items: center;
    .chart-render__button {
      margin-right: 7px;
    }
  }
  .index-column-main {
    position: relative;
    box-sizing: border-box;
    height: 100%;
    // display: flex;
    // flex-direction: column;
    margin: 0px 10px 0 10px;
    overflow: hidden;
    .skeleton-left {
      height: 350px;
      background-color: $base-white;
    }
    .skeleton-right {
      height: 150px;
      background-color: $base-white;
    }
    .penetrate {
      position: absolute;
      top: 0px;
      right: 0;
      bottom: 0;
      left: 0px;
      z-index: 99;
    }
    .index-column-main-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      margin-bottom: 10px;
      background-color: $base-white;
      .index-name {
        margin-left: 20px;
        font-size: 16px;
        color: $text-main;
      }
      .index-top-right {
        display: flex;
        margin-right: 10px;
        line-height: 32px;
        .hover-show-box {
          display: flex;
          margin-right: 10px;
          opacity: 0;
          transition: all 0.3s;
        }
        .set-board {
          :deep(.el-button) {
            width: 57px;
            height: 24px;
            padding: 0;
            margin-top: 4px;
            color: $text-auxiliary;
            border-radius: 4px;
          }
          :hover {
            color: $brand-base-color-6;
          }
        }
        .homepage-manage,
        .bi-full-screen_btn {
          height: 24px;
          padding: 0;
          margin-top: 5px;
          color: $text-auxiliary;
          border-radius: 4px;
          &:hover {
            color: $brand-base-color-6;
          }
          &:focus {
            color: $brand-base-color-6;
          }
        }
        .homepage-manage {
          width: 57px;
          margin-left: 5px;
        }
        .bi-full-screen_btn {
          width: 51px;
        }
      }
    }
    .index-column-main-top:hover {
      .hover-show-box {
        opacity: 1;
      }
    }
    .index-column-main-bottom {
      display: flex;
      height: calc(100% - 50px);
      overflow: auto;
      .index-column-main-left {
        flex: 1;
        .index-main-left-item:last-child {
          padding-bottom: 10px;
        }
      }
      .index-column-main-right {
        flex: 0 0 auto;
        width: 360px;
        margin: 0 0 0 10px;
        .index-main-right-item {
          overflow: hidden;
          // margin-bottom: 10px;
        }
      }
    }
  }
  .coverPicture {
    width: 100%;
  }
  :deep(.el-dialog__footer) {
    text-align: center;
  }
  .service-info {
    display: flex;
    color: $text-plain;
    span {
      margin-left: 10px;
      font-size: 14px;
    }
    i {
      margin-top: -3px;
      font-size: 20px;
      color: #f7ba2a;
    }
  }
  .services-container {
    padding: 30px 0px 30px 30px;
  }
  .service-contact-us {
    padding-left: 30px;
    margin-top: 40px;
    margin-bottom: 30px;
    img {
      vertical-align: middle;
    }
    span {
      margin-left: 42px;
    }
  }
  .dialog-footer {
    padding: 0 20px;
    margin: 0px -24px -24px;
    line-height: 45px;
    text-align: right;
    border-top: 1px solid $bg-blue;
  }
  .expiry-remind {
    position: absolute;
    top: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    padding: 0 16px;
    padding-right: 75px;
    font-size: 14px;
    background: #ffe2a6;
    &.system-info {
      z-index: 10;
    }
    &.other-info {
      z-index: 9;
    }
    .jump-url {
      cursor: pointer;
    }
    .icon {
      padding-right: 6px;
      color: #feab00;
    }
    > i {
      color: $text-auxiliary;
      cursor: pointer;
    }
  }
}
:deep(.expiry-remind .el-button--text) {
  color: rgb(64, 158, 255);
}
.admin-notice {
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}

.select-item_box {
  display: flex;
  justify-content: center;

  // margin-right: 12px;
  .select-form__multi-select {
    display: flex;
    justify-content: center;
    height: 24px;
    min-height: 24px;
    line-height: 24px;
    color: $text-auxiliary;
    cursor: pointer;
    border: 1px solid $neutral-color-3;
    border-radius: 4px;
    :deep(.el-tag) {
      height: 18px !important;
      line-height: 18px;
    }
    :deep(.tagsNum) {
      height: 18px !important;
      line-height: 18px;
    }
    :deep(.placeholder) {
      height: 24px;
      line-height: 24px;
    }
    &:hover {
      color: $brand-color-5;
    }
  }
  .select-item__time {
    height: 24px;
    line-height: 24px;
    color: $text-auxiliary;
    cursor: pointer;
    border: 1px solid $neutral-color-3;
    border-radius: 4px;
    :deep(.detail) {
      color: $text-auxiliary !important;
    }
    :deep(.arrow-icon) {
      vertical-align: text-top;
    }
    &:hover {
      color: $brand-color-5;
    }
    &:active {
      :deep(.detail) {
        color: $brand-base-color-6 !important;
      }
      :deep(.arrow-icon) {
        color: $brand-base-color-6;
        vertical-align: text-top;
      }
      :deep(.el-icon-circle-close) {
        color: $brand-base-color-6 !important;
      }
    }

    &:focus-within {
      border: 1px solid $brand-base-color-6;
    }
  }
}
.clear-icon {
  width: 24px;
  height: 24px;
  padding: 0;
  margin-right: 5px;
  margin-bottom: 14px;
  color: $text-auxiliary;
  :hover {
    color: $brand-base-color-6;
  }
}
</style>
