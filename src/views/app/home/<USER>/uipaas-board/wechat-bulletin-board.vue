<!--
 * @Description: 简报看板
 -->

<template>
  <uipaasBoard
    :show-setting="personalEdit === 1"
    :show-title="showTitle"
    :title="dataForm.name || uipaasItem.name"
    @settingClick="echartSettingClick"
  >
    <template slot="header">
      <div class="memo-icon">
        <el-tooltip
          content="企微数据简报仅统计企微模板中企微数据同步的数据指标，受接口能力限制，仅展示昨日的最新数据，不支持时间筛选。数据权限以同步企微模板的数据权限为准。"
          effect="dark"
          placement="top"
        >
          <i class="icon-question-line t-iconfont"></i>
        </el-tooltip>
      </div>
    </template>
    <div v-if="isPreview" class="bulletin-board">
      <uipaasIconItemList
        :col="uipaasItem.config?.rowNum || 4"
        :list="dataForm.enableList"
      ></uipaasIconItemList>
    </div>
    <div v-else class="bulletin-board">
      <LicenseDefault
        v-if="!scrm.companyAccount"
        desc="当前企业无企微简报功能权限，可设置互通账号后使用此功能"
      ></LicenseDefault>
      <div v-else-if="!scrm.weChatSyncOpenFlag" class="no-data">请完善企微配置！</div>
      <LicenseDefault
        v-else-if="!scrm.selfAccount"
        desc="无互通账号，暂无功能权限,可联系管理员处理"
      ></LicenseDefault>
      <div v-else-if="!childList || !childList.length" class="no-data">
        <emptyStatus type="wait"></emptyStatus>
      </div>
      <div v-else v-loading="isLoading" class="item-list">
        <uipaasIconItemList
          :col="uipaasItem.config?.rowNum || 4"
          :list="childList"
          :show-tooltip="true"
          @tabClick="tabClick"
        ></uipaasIconItemList>
      </div>
    </div>
    <uipaas-set-board
      ref="UipaasSetBoard"
      :chart-type="chartType"
      :data-form="dataForm"
      :home-page-id="homePageId"
      :type="'card'"
      :uipaas-item="uipaasItem"
      @saveSuccess="refreshBoard"
    ></uipaas-set-board>
  </uipaasBoard>
  <!-- <div class="bulletin-board">
    <div class="board-title">
      {{ dataForm.name }}
      <span v-if="!isBoard" class="wechat-board-time">
        <el-tooltip
          content="企微数据简报仅统计企微模板中企微数据同步的数据指标，受接口能力限制，仅展示昨日的最新数据，不支持时间筛选。数据权限以同步企微模板的数据权限为准。"
          placement="top"
        >
          <i class="tooltipMargin tooltipNewIcon web-icon-question-circle web-iconfont"></i>
        </el-tooltip>
      </span>
      <span v-if="!isBoard">仅展示昨日的最新数据</span>
      <div v-if="$route.name === 'homeIndex'" class="followGlobal">
        <span v-if="!isBoard && dataForm.setFlag" class="tooltipNewIcon" @click="openSetData"
          ><i class="el-icon-setting"></i
        ></span>
      </div>
    </div>
    <set-data-board
      ref="SetDataBoard"
      :data-form="dataForm"
      :home-page-id="homePageId"
      @saveSuccess="refreshBoard"
    ></set-data-board>
    <LicenseDefault
      v-if="!scrm.companyAccount"
      desc="当前企业无企微简报功能权限，可设置互通账号后使用此功能"
    ></LicenseDefault>
    <div v-else-if="!scrm.weChatSyncOpenFlag" class="no-data">请完善企微配置！</div>
    <LicenseDefault
      v-else-if="!scrm.selfAccount"
      desc="无互通账号，暂无功能权限,可联系管理员处理"
    ></LicenseDefault>
    <div v-else v-loading="isLoading" class="item-list">
      <div v-for="item in childList" :key="item.alias" class="list-item">
        <span class="item-icon" :style="item.color ? '' : 'background-color: rgba(0,140,238,0.1)'">
          <div class="icon-bg" :style="'background-color:' + iconColorMap(item.color)"></div>
          <i
            class="icon-style iconfont"
            :class="item.icon"
            :style="'color:' + iconColorMap(item.color)"
          ></i>
        </span>
        <div class="item-detail">
          <div class="item-num">
            <el-tooltip placement="right-start">
              <div slot="content" v-html="commentSet(item)"></div>
              <span
                class="num"
                :class="{ through: item.through }"
                :style="{ 'font-size': adjustFontSize(item.value || 0) }"
                @click="$emit('penetrate', item, 'isReport')"
                >{{ item.value || 0 }}</span
              >
            </el-tooltip>
          </div>
          <div class="item-name word-break">
            <span>{{ item.name }}</span>
            <span class="unit">（{{ item.unit }}）</span>
          </div>
        </div>
      </div>
    </div>
  </div> -->
</template>

<script>
import mixin from '../data-board-mixin'
import colorMixin from '@/components/common/iconColorMap'
import LicenseDefault from '@/views/scrm/components/common/default/license-default'
import uipaasBoard from './uipaas-board.vue'
import uipaasIconItemList from '@/views/app/home/<USER>/uipaas-board/components/icon-item-list.vue'
import EmptyStatus from './components/empty-status.vue'
import UipaasSetBoard from '../uipaas-set-board.vue'

export default {
  components: {
    LicenseDefault,
    UipaasBoard: uipaasBoard,
    UipaasIconItemList: uipaasIconItemList,
    EmptyStatus,
    UipaasSetBoard
  },
  mixins: [mixin, colorMixin],

  data() {
    return {
      // scrm 企微简报
      scrm: {
        weChatSyncOpenFlag: false, //企微同步是否开启
        companyAccount: false, //企业是否互通账号
        selfAccount: false //个人是否互通账号
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.memo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  font-size: 16px;
  line-height: 16px;
  color: $text-auxiliary;
}
.bulletin-board {
  width: 100%;
  height: 100%;
  .item-list {
    width: 100%;
    height: 100%;
  }
}
</style>
