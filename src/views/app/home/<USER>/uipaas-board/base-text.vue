<template>
  <uipaasBoard
    :content-padding="false"
    :show-setting="false"
    :show-title="showTitle"
    :title="dataForm.name"
  >
    <div v-if="content" class="ql-snow">
      <div class="ql-editor" v-html="content"></div>
    </div>
    <emptyStatus v-else :content="'请输入文本'"></emptyStatus>
  </uipaasBoard>
</template>

<script>
import UipaasBoard from './uipaas-board.vue'
import EmptyStatus from './components/empty-status.vue'

export default {
  name: 'BaseText',

  components: {
    UipaasBoard,
    EmptyStatus
  },

  props: {
    dataForm: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    content() {
      return this.dataForm?.config?.content.trim() || ''
    },
    showTitle() {
      return this.dataForm?.displayTitle !== 0
    }
  }
}
</script>
