<!-- eslint vue/no-side-effects-in-computed-properties: 1 -->

<!--
 * @Description: 首页看板设置访客计划的日历组件
 -->
<!-- 首页看板设置访客计划日历组件 -->
<template>
  <div class="uipaas-date-picker">
    <div class="date-picker-head">
      <div class="current-date">{{ year }}年{{ month }}月</div>
      <div v-if="!isBoard" class="choose-date">
        <div class="every-option">
          <i class="icon-arrow-left-s-line t-iconfont" @click="preMonth"></i>
        </div>
        <div class="every-option">
          <p @click="chooseToday">今天</p>
        </div>
        <div class="every-option">
          <i class="icon-arrow-right-s-line t-iconfont" @click="nxtMonth"></i>
        </div>
      </div>
    </div>
    <div class="date-picker-body">
      <p class="date-picker-text" :class="calendarModel === 'week' ? 'text-week' : 'text-mouth'">
        <span v-for="(weekday, index) in weekdayArray" :key="index" class="date-weekday">
          {{ weekday }}
        </span>
      </p>
      <p class="date-picker-num" :class="calendarModel === 'week' ? 'week' : 'mouth'">
        <span
          v-for="(item, index) in preDays"
          :key="index"
          class="day-card"
          @click="chooseDay(index, item)"
        >
          <span
            class="day-num"
            :class="{
              hasHover: !item.isCurMonth,
              isToday: item.isChoose && isChoose,
              hasPlan: isHasPlan(item),
              curToday: isCurToday(item)
            }"
          >
            {{ item.dayNum }}
          </span>
        </span>
      </p>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-side-effects-in-computed-properties: 1 */

import { getCommunicatePlanDaysList } from '@/api/form-special-list.js'
import xbb from '@xbb/xbb-utils'

const curDate = new Date()
const curYear = curDate.getFullYear()
const curMonth = curDate.getMonth()
const curDay = curDate.getDate()
function getMonthDays(year, month) {
  // 获取某年某月有多少天
  if (month !== 0 && !month) {
    return
  }
  if (!year) {
    year = new Date().getFullYear()
  }
  return new Date(year, month, 0).getDate()
}
function getDayInWeek(year, month, day) {
  // 返回某年某月某日是星期几
  if (!year || !day || month - 1 < -1) return 0
  return new Date(year, month, day).getDay()
}
// 月份数组拼接 START
function getDayArry(year, month, hasChoosedDay) {
  // 获取当前月天数数组
  const curMonthDays = getMonthDays(year, month)
  const preMonthDays = getMonthDays(year, month === 0 ? 11 : month - 1)
  const firstDay = getDayInWeek(year, month - 1, 1)
  const allDays = Math.ceil((+curMonthDays + firstDay) / 7) * 7
  const dayArry = []
  for (let i = 1; i <= allDays; i++) {
    const isPre = i <= firstDay
    const isNxt = i > firstDay + curMonthDays
    const isCurMonth = !isPre && !isNxt
    const day = isPre
      ? preMonthDays - firstDay + i
      : isNxt
      ? i - firstDay - curMonthDays
      : i - firstDay
    dayArry.push({
      dayNum: day,
      isChoose: !hasChoosedDay
        ? curDay === i - firstDay && curMonth === month - 1 && curYear === year
        : hasChoosedDay === i - firstDay,
      isCurMonth: isCurMonth,
      color: false
    })
  }
  return dayArry
}
// -------------------------月份数组拼接 END------------------------------------

export default {
  name: 'BoardCalendar',
  props: {
    params: {
      type: Object,
      required: true
    },
    isBoard: {
      type: Boolean,
      default: false
    },
    calendarModel: {
      type: String,
      default: 'week'
    }
  },
  data() {
    return {
      chooseDate: '',
      year: curYear,
      month: curMonth + 1,
      hasChoosedDay: curDay,
      isChoose: true,
      items: [],
      days: getDayArry(curYear, curMonth + 1),
      showChooseBox: false,
      chooseBoxTimer: '',
      datePickerBoxTimer: '',
      chooseType: false,
      YearChangeSyboml: curYear,
      hasPlayDayArr: [],
      currentFirstDate: new Date(),
      mouthChange: 0
    }
  },
  computed: {
    weekdayArray() {
      const week = ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fir', 'Sat']
      return week.map((item) => this.$t('calendarDays.' + item))
    },
    // 生成当前月的日期数据
    preDays() {
      const self = this
      return this.calendarModel === 'week'
        ? this.setDate(this.currentFirstDate)
        : getDayArry(self.year, self.month, this.hasChoosedDay)
    },
    preItems() {
      const self = this
      const tempArry = []
      const startNum = self.chooseType ? +self.YearChangeSyboml - 4 : 1
      const endNum = self.chooseType ? +self.YearChangeSyboml + 4 : 12
      for (let i = startNum; i <= endNum; i++) {
        tempArry.push(i)
      }
      self.items = []
      return tempArry
    }
  },
  mounted() {
    this.chooseDate = this.year + '-' + this.month + '-' + this.hasChoosedDay
  },
  methods: {
    // 显示访客计划
    loadPageCalendar: function (date) {
      if (date === 'plan') {
        date = this.year + '-' + this.month + '-01'
      }
      const params = {
        ...this.params,
        fromIndex: 1,
        date
      }
      getCommunicatePlanDaysList(params)
        .then((data) => {
          this.hasPlayDayArr = data.result.hasPlanDayArray
        })
        .catch(() => {})
    },
    isHasPlan: function (item) {
      for (let i = 0; i < this.hasPlayDayArr.length; i++) {
        const timeArr = this.hasPlayDayArr[i].split('-')
        if (
          parseInt(timeArr[0]) === this.year &&
          parseInt(timeArr[1]) === this.month &&
          parseInt(timeArr[2]) === item.dayNum &&
          item.isCurMonth
        ) {
          console.log('isHasPlan', item)
          return true
        }
      }
    },
    isCurToday: function (item) {
      if (
        this.year === curYear &&
        this.month === curMonth + 1 &&
        item.dayNum === curDay &&
        item.isCurMonth
      ) {
        return true
      }
    },
    // ----------- 切换月 START ---------------
    // 上一月
    preMonth: function () {
      if (this.calendarModel === 'month') {
        const isFirstMonth = this.month === 1
        this.month = isFirstMonth ? 12 : this.month - 1
        this.year = isFirstMonth ? this.year - 1 : this.year
        this.isChoose = true
        this.hasChoosedDay = 1
        this.chooseDate = `${this.year}-${this.month}-1`
        this.loadPageCalendar(this.chooseDate)
      } else {
        this.mouthChange += 1
        this.isChoose = this.highDay()
        this.setDate(this.addDate(this.currentFirstDate, -7))
        this.changeTime()
        this.loadPageCalendar(this.currentFirstDate)
      }
    },
    // 下一月
    nxtMonth: function () {
      if (this.calendarModel === 'month') {
        const isLastMonth = this.month === 12
        this.month = isLastMonth ? 1 : +this.month + 1
        this.year = isLastMonth ? +this.year + 1 : this.year
        this.isChoose = true
        this.hasChoosedDay = 1
        this.chooseDate = `${this.year}-${this.month}-1`
        this.loadPageCalendar(this.chooseDate)
      } else {
        this.mouthChange -= 1
        this.isChoose = this.highDay()
        this.setDate(this.addDate(this.currentFirstDate, 7))
        this.changeTime()
        this.loadPageCalendar(this.currentFirstDate)
      }
    },
    // 选择今天
    chooseToday() {
      this.year = curYear
      this.month = curMonth + 1
      this.hasChoosedDay = curDay
      this.isChoose = true
      this.chooseDate = this.year + '-' + this.month + '-' + this.hasChoosedDay
      this.loadPageCalendar(this.chooseDate)

      if (this.calendarModel === 'week') {
        this.setDate(this.addDate(this.currentFirstDate, this.mouthChange * 7))
        this.mouthChange = 0
        this.isChoose = this.highDay()
        this.changeTime()
        this.loadPageCalendar(this.currentFirstDate)
      } else {
        this.$emit('chooseDay', this.chooseDate)
        return
      }

      this.chooseDay(1, {
        dayNum: curDay,
        isChoose: true,
        isCurMonth: true,
        month: curMonth + 1,
        year: curYear
      })
    },
    // ----------- 切换月 END ---------------
    // 选择天
    @xbb.debounceWrap(200)
    chooseDay(index, item) {
      if (!index && index !== 0) {
        return
      }
      if (!this.preDays[index].isCurMonth) {
        return
      }
      if (this.calendarModel === 'month') {
        const preDay = new Date(this.year, this.month - 1, 1).getDay() - 1
        this.preDays[index].color = !this.preDays[index].color
        this.chooseDate = `${this.year}-${this.month}-${index - preDay}`
        this.hasChoosedDay = index - preDay
      } else {
        this.hasChoosedDay = item.dayNum
        this.setDate(this.currentFirstDate)
        this.chooseDate = `${item.year}-${item.month}-${item.dayNum}`
      }
      this.isChoose = this.highDay()
      this.$emit('chooseDay', this.chooseDate)
    },
    // 计算当前日期是否高亮
    highDay() {
      if (!this.chooseDate) {
        return curYear === this.year && curMonth === this.month - 1 && curDay === this.hasChoosedDay
      }

      const choosedTime = this.chooseDate.split('-')
      if (choosedTime.length !== 3) {
        return curYear === this.year && curMonth === this.month - 1 && curDay === this.hasChoosedDay
      }
      return (
        parseInt(choosedTime[0]) === this.year &&
        parseInt(choosedTime[1]) === this.month &&
        parseInt(choosedTime[2]) === this.hasChoosedDay
      )
    },
    changeMode() {
      this.isChoose = true
      this.chooseDate = this.chooseDate || `${this.year}-${this.month}-1`
      const timeArr = this.chooseDate.split('-').map((item) => parseInt(item))
      if (timeArr[1] !== this.month) {
        this.chooseDate = `${this.year}-${this.month}-1`
        this.hasChoosedDay = 1
      } else {
        this.hasChoosedDay = timeArr[2]
      }
      this.$emit('chooseDay', this.chooseDate)
      this.loadPageCalendar(this.chooseDate)
      // this.calendarModel = this.calendarModel === 'week' ? 'month' : 'week'
      if (this.calendarModel === 'week') {
        this.setDate(new Date(this.chooseDate))
      }
    },
    addDate(date, n) {
      date.setDate(date.getDate() + n)
      return date
    },
    setDate(date) {
      const week = date.getDay() - 1
      date = this.addDate(date, week * -1)
      this.currentFirstDate = new Date(date)
      const arrList = []
      for (let i = 0; i < 7; i++) {
        const time = i === 0 ? this.addDate(date, -1) : this.addDate(date, 1)
        const day = time.getDate()
        const curMonth = date.getMonth() + 1
        const curYear = date.getFullYear()
        arrList.push({
          dayNum: day,
          isCurMonth: curMonth === this.month,
          year: curYear,
          month: curMonth,
          isChoose: this.year === curYear && this.month === curMonth && this.hasChoosedDay === day
        })
      }
      return arrList
    },
    changeTime() {
      const monthChange = this.preDays.every((item) => {
        return item.month !== this.month
      })
      const yearChange = this.preDays.every((item) => {
        return item.year !== this.year
      })
      if (monthChange) {
        this.month = this.preDays[0].month
      }
      if (yearChange) {
        this.year = this.preDays[0].year
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.uipaas-date-picker {
  .date-picker-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 24px;
    .current-date {
      font-size: 14px;
      color: $text-main;
    }
    .choose-date {
      display: flex;
      gap: 4px;
      .every-option {
        padding: 4px;
        color: $text-plain;
        cursor: pointer;
        border-radius: 4px;
        p {
          line-height: 16px;
        }
      }
      .every-option:hover {
        background-color: $neutral-color-2;
      }
    }
  }
  .date-picker-body {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 24px);
    .date-picker-text {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .date-weekday {
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(100% / 7);
        min-height: 24px;
        color: $text-auxiliary;
      }
    }
    .text-week {
    }
    .text-mouth {
      height: calc(100% / 6);
    }
    .week {
    }
    .mouth {
      flex-wrap: wrap;
      height: calc(100% / 6 * 5);
    }
    .date-picker-num {
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-size: 12px;
      line-height: 16px;
      .day-card {
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(100% / 7);
        height: 36px;
      }
      .day-num {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        cursor: pointer;
        border-radius: 4px;
      }
      .day-num:hover {
        background-color: $neutral-color-2;
      }
      .hasHover {
        color: $text-grey;
        // color: $text-main;
      }
      .isToday {
        border: 1px solid #ff8d3c;
      }
      .hasPlan {
        position: relative;
        &:after {
          position: absolute;
          bottom: -6px;
          left: 50%;
          width: 4px;
          height: 4px;
          content: '';
          background: #ff8d3c;
          border-radius: 50%;
          transform: translateX(-50%);
        }
      }
      .curToday {
        color: $brand-base-color-6;
        background: rgba(255, 141, 60, 0.1);
      }
    }
  }
}
</style>
