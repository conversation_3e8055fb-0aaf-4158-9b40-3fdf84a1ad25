<template>
  <div class="uipaas-shortcut-list" :style="getColAndRow">
    <uipaas-shortcut-item
      v-for="(item, index) in list"
      :key="index"
      :item="item"
      :show-icon="showIcon"
      @tabClick="tabClick"
    />
  </div>
</template>

<script>
// import mixin from '../../data-board-mixin'
import colorMixin from '@/components/common/iconColorMap'
import uipaasShortcutItem from './shortcut-item.vue'

export default {
  name: 'UIPaasShortcutItemList',

  components: {
    UipaasShortcutItem: uipaasShortcutItem
  },

  mixins: [colorMixin],

  props: {
    list: {
      type: Array,
      default: () => []
    },
    showIcon: {
      type: Boolean,
      default: true
    },
    col: {
      type: Number,
      default: 4
    },
    compact: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    getColAndRow() {
      const row = Math.ceil(this.list?.length / this.col)
      const height = this.compact ? row * 72 - 12 + 'px' : '100%'
      return {
        'grid-template-rows': `repeat(${row}, 1fr)`,
        'grid-template-columns': `repeat(${this.col}, 1fr)`,
        height: `${height}`
      }
    }
  },

  methods: {
    tabClick(item, businessType) {
      this.$emit('tabClick', item, businessType)
    }
  }
}
</script>

<style lang="scss" scoped>
.uipaas-shortcut-list {
  display: grid;
  gap: 12px;
  width: 100%;
  overflow: auto;
}
.uipaas-shortcut-list :hover {
  // cursor: pointer;
  background-color: $neutral-color-2;
}
</style>
