<!--
 * @Description:日志查看
 -->
<template>
  <div class="warn-detail">
    <div class="warn-detail__title">
      <span>
        <span class="warn-previous" @click="backPrevious">
          {{ $t('dataWarn.dataCenter.homePage') }}</span
        >&gt;
        <span>{{ $t('dataWarn.dataCenter.allWarnings') }}</span>
      </span>
      <el-tooltip
        class="item"
        :content="$t('dataWarn.dataCenter.homeTip')"
        effect="dark"
        placement="right"
      >
        <em class="el-icon-question em-color"></em>
      </el-tooltip>
    </div>
    <div class="detail-content">
      <el-form class="data-form-inline" :inline="true">
        <el-form-item :label="$t('dataWarn.dataCenter.warningObject')">
          <el-input
            v-model="chartNameLike"
            class="warn-input"
            :placeholder="$t('dataWarn.dataCenter.enterWarningObject')"
            @change="changeCondition"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('dataWarn.dataCenter.warningName')">
          <el-input
            v-model="nameLike"
            class="warn-input"
            :placeholder="$t('dataWarn.dataCenter.enterWarningName')"
            @change="changeCondition"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <ConnectConditionFieldFilter
            :key="condition.attr"
            :condition="condition"
            :is-lazy="true"
            @conditionChange="conditionChange"
          />
        </el-form-item>
      </el-form>
      <el-table ref="singleTable" :data="tableData" height="80%" style="width: 100%">
        <el-table-column :label="$t('dataWarn.dataCenter.warningObject')">
          <template slot-scope="scope">
            <span class="warn-color" @click="toChartCenter(scope.row)">
              {{ scope.row.chartName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('dataWarn.dataCenter.warningName')" prop="warningName">
        </el-table-column>
        <el-table-column
          :label="$t('dataWarn.dataCenter.triggerConditions')"
          prop="triggerConditionStr"
        >
        </el-table-column>
        <el-table-column
          :label="$t('dataWarn.dataCenter.warningValue')"
          prop="warnValue"
        ></el-table-column>
        <el-table-column
          :label="$t('dataWarn.dataCenter.triggerTime')"
          prop="triggerTime"
        ></el-table-column>
      </el-table>
      <v-pagination
        v-if="pageHelper.rowsCount"
        class="detail-paging"
        :current-page="pageHelper.currentPageNum"
        layout="slot, sizes, prev, pager, next, jumper"
        :page-size="20"
        :total="pageHelper.rowsCount"
        @current-change="logPageChange"
        @size-change="handleSizeChange"
      >
      </v-pagination>
    </div>
  </div>
</template>

<script>
import { indexList, checkThrough } from '@/api/dataWarn'
import ConnectConditionFieldFilter from '@/components/connectCondition/connect-condition-filter'
import { mapMutations } from 'vuex'
import xbb from '@xbb/xbb-utils'

export default {
  components: {
    ConnectConditionFieldFilter
  },
  data() {
    return {
      nameLike: '',
      tableData: [],
      pageHelper: {
        currentPageNum: 1,
        hasLeft: false,
        hasRight: false,
        leftPageNums: [],
        pageSize: 20,
        pageTotal: 1,
        rightPageNums: [],
        rowsCount: 8
      },
      chartNameLike: '',
      condition: {
        amountFlag: 0,
        attr: 'addTime',
        attrType: 'date',
        dateType: 'yyyy-MM-dd HH:mm',
        fieldType: 10014,
        name: '触发时间',
        whereList: [
          { memo: '大于等于', replaceSymbol: ' >= ', symbol: 'greaterequal' },
          {
            memo: '小于等于',
            replaceSymbol: ' <= ',
            symbol: 'lessequal'
          },
          { memo: '选择范围', replaceSymbol: 'between', symbol: 'range' }
        ],
        whereVal: 'greaterequal',
        symbol: 'greaterequal',
        value: '',
        triggerTime: []
      },
      paging: {
        page: 1,
        pageSize: 20
      }
    }
  },
  mounted() {
    this.getDataWarningSettingDetails()
  },
  methods: {
    ...mapMutations(['SET_WARN_STORAGE']),
    getDataWarningSettingDetails() {
      const params = {
        page: this.paging.page,
        pageSize: this.paging.pageSize,
        triggerTime: this.condition.triggerTime,
        chartNameLike: this.chartNameLike,
        nameLike: this.nameLike,
        symbol: this.condition.symbol
      }
      indexList(params).then((res) => {
        this.tableData = res.result.dataWarningIndexPojoList || []
        this.pageHelper = res.result.pageHelper
        if (this.tableData.length > 0) {
          this.tableData.forEach((element) => {
            if (element.triggerTime) {
              element.triggerTime = xbb.timestampToTimeString(
                element.triggerTime,
                'yyyy-MM-dd HH:mm'
              )
            }
          })
        }
      })
    },
    logPageChange(val) {
      this.paging.page = val
      this.getDataWarningSettingDetails()
    },
    backPrevious() {
      this.$router.go(-1)
    },
    // 每页所显示的条数变更
    handleSizeChange(val) {
      this.paging.pageSize = val
      this.getDataWarningSettingDetails()
    },
    changeCondition() {
      this.paging.page = 1
      this.getDataWarningSettingDetails()
    },
    conditionChange(condition, operate) {
      this.condition.triggerTime.splice(0, this.condition.triggerTime.length)
      if (operate === 'add') {
        if (Array.isArray(condition.value)) {
          this.condition.triggerTime = condition.value
        } else {
          this.condition.triggerTime.push(condition.value)
        }
      }
      this.paging.page = 1
      this.getDataWarningSettingDetails()
    },
    // 图表穿透到数据中心
    toChartCenter(item) {
      // console.log(item)
      if (
        item.chartCenterPermission &&
        item.categoryVisiblePermission &&
        item.chartVisiblePermission
      ) {
        checkThrough({
          categoryId: item.categoryId,
          chartId: item.chartId,
          dataWarningId: item.dataWarningId
        }).then((res) => {
          if (res.result.enableFlag || res.result.delFlag) {
            this.$message.warning(this.$t('dataWarn.dataCenter.noTip'))
          } else {
            this.SET_WARN_STORAGE({ categoryId: res.result.categoryId, chartId: item.chartId })
            this.$router.push({
              path: '/chartCenter',
              query: {
                showWarn: true
              }
            })
          }
        })
      } else {
        this.$message.warning(this.$t('dataWarn.dataCenter.noPermission'))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.warn-detail {
  display: flex;
  flex-direction: column;
  height: 100%;

  .em-color {
    color: #d6d6d6;
    text-indent: 0px;
  }
  &__title {
    height: 50px;
    font-size: 16px;
    line-height: 50px;
    text-indent: 20px;
    background: $base-white;
  }

  .detail-content {
    flex: 1;
    padding: 10px 20px;
    margin: 10px;
    background: $base-white;
  }

  .detail-name {
    font-size: 14px;
  }

  .detail-paging {
    display: flex;
    flex-direction: row-reverse;
    margin-top: 10px;
  }

  .warn-previous {
    cursor: pointer;
  }
  .warn-color {
    color: #0995e2;
    cursor: pointer;
  }
}
</style>
