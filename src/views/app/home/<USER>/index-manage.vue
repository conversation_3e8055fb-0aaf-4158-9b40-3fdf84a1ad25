<!--
 * @Description: 首页管理
-->
<template>
  <div class="index-manage">
    <v-tab
      :active-tab-style="tabTitleStyle"
      :father-width="indexWidth * 0.4"
      :is-active="indexList.length > 1 ? currentId : ''"
      :show-tab-num="5"
      :tabs="indexList"
      @tabClick="handleCommand"
    ></v-tab>
    <!-- 首页管理弹窗 -->
    <div class="set-dialog">
      <el-dialog
        :title="$t('display.dashboard.homepageManage')"
        :visible.sync="dialogVisible"
        width="550px"
      >
        <div class="wrapper-container">
          <p class="wrapper-container_info">
            {{ $t('display.dashboard.homepageCount', { name: showName }) }}：{{ usedNum }}/{{
              totalNum
            }}
          </p>
          <p v-if="showCustom === 1" class="wrapper-container_info">
            {{ $t('display.dashboard.customPageCount') }}：{{ customUsedNum }}/{{ customTotalNum }}
          </p>
          <p class="wrapper-container_notice">
            {{ $t('display.dashboard.sortFirst') }}
          </p>
          <div class="table-list">
            <div class="tb-title">
              <span class="tb-choose">{{ $t('home.isShow') }}</span>
              <span class="tb-name">{{ $t('nouns.homePage') }}</span>
              <span class="tb-index">{{ $t('operation.sort') }}</span>
            </div>
            <VueDraggable
              v-model="indexList"
              v-draggable
              handle=".drag-icon"
              v-bind="{ draggable: '.table-tr.sort' }"
            >
              <div v-for="(item, index) in indexList" :key="item.id" class="sort table-tr">
                <template>
                  <span class="tb-choose">
                    <i
                      v-if="item.id !== 0 && item.isCustom !== 1"
                      class="el-icon-close item-icon-close"
                      @click="hideItem(item, index)"
                    ></i>
                  </span>
                  <span class="tb-name">
                    {{ item.name }}
                  </span>
                  <span class="drag-icon tb-index"><i class="el-icon-rank web-iconfont"></i></span>
                </template>
              </div>
            </VueDraggable>
            <div class="hide-title">{{ $t('home.hideData') }}</div>
            <!-- <div class="board-search">
              <el-input prefix-icon="el-icon-search" v-model="search" :placeholder="$t('placeholder.searchPls',{attr:$t('home.targetName')})" @change="searchHideList"></el-input>
            </div> -->
            <div class="table-roll">
              <template v-if="showHideList.length">
                <div v-for="(item, index) in showHideList" :key="index" class="table-tr">
                  <span class="tb-choose"
                    ><el-checkbox
                      v-if="item.id !== 0"
                      v-model="isChose"
                      @change="showItem(item, index)"
                    ></el-checkbox
                  ></span>
                  <span class="tb-name"
                    >{{ item.name }}
                    <i v-if="item.showChart">{{ $t('home.useDraw') }}</i>
                  </span>
                  <span class="tb-index"></span>
                </div>
              </template>
              <div v-else class="no-more">{{ $t('home.noMoreNow') }}...</div>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">{{ $t('operation.cancel') }} </el-button>
          <el-button type="primary" @click="saveList">{{ $t('operation.confirm') }} </el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getHomePageList, updateHomePageList } from '@/api/panel-design.js'
import { getChartList } from '@/api/statistics'
import { saveExplainPositionError } from '@/utils/chart-manage.js'
import xbb from '@xbb/xbb-utils'
import vTab from '@/components/common/v-tab'

export default {
  name: 'IndexManage',

  components: {
    VTab: vTab
  },

  props: {
    indexWidth: {
      type: Number,
      default: 0
    },
    tabTitleStyle: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      isChose: '',
      currentLabel: 'CRM首页',
      currentId: 0, // 为0是默认的crm首页，不为0是新分配的crm首页或者自定义首页
      isCustom: '', // 为1是新分配的crm首页，为0是自定义首页
      type: 3, // 1是仪表盘，2是uipaas首页，3是左右布局首页
      indexList: [],
      copyIndexList: [],
      showHideList: [], // 未启用
      hideList: [],
      search: '', // 搜索
      dialogVisible: false,
      usedNum: 0, // 仪表盘分配首页数量
      totalNum: 10, // 仪表盘分配首页数量总数
      customUsedNum: 0, // 管理员配首页数量
      customTotalNum: 10, // 管理员配首页数量总数
      showCustom: 0, // 是否展示管理员分配首页
      isCustomList: [],
      preCustomCommand: {}
    }
  },

  computed: {
    showName() {
      let name = 'CRM' // 默认的
      if (this.$store.state.user.newMenuLayout && this.$store.state.user.newLayoutKind) {
        name = this.$store.state.user.newLayoutKind.name
      }
      return name
    }
  },

  created() {
    this.getIndexList()
  },
  methods: {
    // 获取首页列表
    getIndexList() {
      getHomePageList({}).then(({ result }) => {
        this.setHomePermission(result)

        if (!xbb._get(result, 'homePages', false)) {
          return
        }
        const arr = [...result.homePages]
        for (const i in arr) {
          if (arr[i].id === 0 && arr[i].isCustom === 0) {
            this.$store.commit('SET_ASSIGNMENT', false)
          }
        }
        this.usedNum = result.usedNum
        this.totalNum = result.totalNum
        this.customUsedNum = result.customUsedNum
        this.customTotalNum = result.customTotalNum
        this.showCustom = result.showCustom
        this.copyIndexList = JSON.stringify([...result.homePages])
        this.indexList = arr.filter((i) => i.enable === 1)
        this.hideList = arr.filter((i) => i.enable === 0)
        utils.LS.set('isUIPaaS', result.uiPaasHome === true)
        this.indexList.forEach((item) => {
          if (item.main && item.main === 1) {
            this.currentLabel = item.name
            this.currentId = item.id
            this.isCustom = item.isCustom
            this.type = item.type
            /* 这里的categoryId 为了防止多窗口时和仪表盘冲突 采用新的名字 */
            item.id !== 0 && item.isCustom !== 1 && utils.LS.set('index-Page-Id', item.id)
          }
          // 首页管理权限
          this.isCustomList.push(item.isCustom)
        })

        this.$emit('changeIndex', this.currentLabel, this.currentId, this.isCustom, this.type)
        if (this.currentId !== 0 && this.isCustom !== 1) {
          this.getChartList(this.currentId)
        }
        this.searchHideList()
      })
    },
    // 设置首页管理权限
    setHomePermission(result = {}) {
      const { hasCustomPage, isBoss } = result
      // 有无首页管理权限
      this.$store.commit('SET_HAS_CUSTOM_PAGE', hasCustomPage)
      this.$store.commit('SET_ISBOSS', isBoss)
    },

    // 选择首页
    @xbb.throttleWrap(1000)
    handleCommand(command) {
      console.log('command', command)
      if (!command) {
        return
      }
      this.setIndex().then((res) => {
        this.preCustomCommand = {
          id: this.currentId,
          label: this.currentLabel
        }
        this.currentId = command.id
        this.currentLabel = command.name
        this.indexList = this.indexList.map((item) => {
          return {
            ...item,
            main: +(item.id === command.id)
          }
        })
        updateHomePageList({
          homePages: [...this.indexList, ...this.hideList]
        })
          .then((res) => {
            this.currentId !== 0 &&
              command.isCustom !== 1 &&
              utils.LS.set('index-Page-Id', this.currentId)

            this.$emit('changeIndex', command.name, command.id, command.isCustom, command.type)

            this.copyIndexList = JSON.stringify([...this.indexList, ...this.hideList])

            this.dialogVisible = false

            if (this.currentId !== 0 && command.isCustom !== 1) {
              this.getChartList(this.currentId)
            }
          })
          .catch((error) => {
            this.currentId = this.preCustomCommand.id
            this.currentLabel = this.preCustomCommand.label
          })
      })
    },
    // 隐藏
    hideItem(item, index) {
      if (item.id === 0) {
        return
      }
      item.enable = 0
      this.indexList.splice(index, 1)
      this.hideList.push(item)
      this.usedNum = this.indexList.filter((i) => i.isCustom === 0).length
      this.searchHideList()
    },
    // 展示
    showItem(item, index) {
      item.enable = 1
      this.indexList.push(item)
      this.hideList.splice(index, 1)
      this.usedNum = this.indexList.filter((i) => i.isCustom === 0).length
      this.searchHideList()
      // 清空checkbox
      this.isChose = ''
    },
    // 筛选未启用index
    searchHideList() {
      if (this.search) {
        this.showHideList = this.hideList.filter((item) => item.name.indexOf(this.search) > -1)
      } else {
        this.showHideList = JSON.parse(JSON.stringify(this.hideList))
      }
    },
    // 保存首页管理
    saveList() {
      this.handleCommand(this.indexList[0])
    },

    // 设置首页
    setIndex() {
      return Promise.resolve(
        this.indexList.forEach((item) => {
          if (item.main) {
            item.main = 0
          }
        })
      )
    },
    // 取消设置
    cancel() {
      this.dialogVisible = false
      this.indexList = JSON.parse(this.copyIndexList).filter((i) => i.enable === 1)
      this.showHideList = this.hideList = JSON.parse(this.copyIndexList).filter(
        (i) => i.enable === 0
      )
      this.usedNum = this.indexList.filter((i) => i.isCustom === 0).length
    },
    // 获取仪表盘列表
    // ?【接口改造】：因为发布的仪表盘都是自定义仪表盘，所以不需要添加alias等入参
    getChartList(categoryId) {
      const params = {
        categoryId,
        type: 9
      }
      if (this.$route.path === '/app/home') {
        params.originOfChart = 1
      }
      getChartList(params).then((res) => {
        // 钻取使用权限
        utils.LS.set('biChartDrill', res.result.chartPermissions.chartDrill)
        saveExplainPositionError(res.result.chartList, categoryId)
        this.$emit('setChartOption', Object.assign({}, res.result))
      })
    },
    // 筛选未启用index
    // searchHideList() {
    //   this.showHideList = JSON.parse(JSON.stringify(this.hideList))
    // }

    // 显示首页管理弹窗
    changeDialogVisible() {
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss">
.index-manage {
  .el-dialog__body {
    padding: 0px !important;
  }
}
</style>

<style lang="scss" scoped>
.menu-item {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  min-width: 100px;
  padding: 6px;
  span {
    flex-grow: 1;
  }
}
.choose {
  color: $brand-color-5;
  i {
    display: inline-block;
  }
}
.notChoose {
  i {
    display: none;
  }
}
.wrapper-container {
  margin: 15px 22px;
  &_info {
    margin-bottom: 10px;
    font-size: 14px;
    color: $text-main;
  }
  &_notice {
    margin: 12px 0px;
    font-size: 12px;
    color: $text-plain;
  }
  .table-list {
    margin-top: 20px;
    color: $text-plain;
    .table-roll {
      max-height: 180px;
      overflow: auto;
      .no-more {
        margin: 20px;
        font-size: 14px;
        text-align: center;
      }
    }
    .tb-title {
      line-height: 48px;
      background-color: #f1f4f8;
    }
    .table-tr {
      line-height: 40px;
      cursor: pointer;
      border-bottom: 1px solid $bg-tag;
      transition: background-color 0.3s;
      &:hover {
        background-color: #ecf5ff;
      }
      .item-icon-close {
        padding-left: 5px;
        font-size: 16px;
        color: $neutral-color-5;
      }
    }
    .tb-choose,
    .tb-name,
    .tb-index {
      display: table-cell;
      font-size: 14px;
    }
    .drag-icon {
      cursor: move;
    }
    .tb-name i {
      margin-left: 10px;
      font-size: 12px;
      font-style: normal;
      color: $text-auxiliary;
    }
    .tb-choose {
      width: 95px;
      padding-left: 20px;
    }
    .tb-name {
      width: 340px;
    }
    .hide-title {
      font-size: 12px;
      line-height: 48px;
      color: $text-auxiliary;
      text-align: center;
      border-top: 1px solid $bg-tag;
    }
  }
}
</style>
