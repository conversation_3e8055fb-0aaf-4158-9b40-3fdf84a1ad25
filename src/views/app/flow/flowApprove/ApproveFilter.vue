<!--
 * @Description: 审批的筛选条件
 -->
<!--  -->
<template>
  <div>
    <div v-if="!/^(creatFlow)$/.test(flowType)" class="form-item">
      <div class="label">{{ $t('flow.submitter') + ':' }}</div>
      <div v-if="isWxAppUnbind" class="personSelect">
        <!-- 企业微信组织架构名称显示兼容 -->
        <div
          v-if="showWxtext"
          class="showWxText"
          @click="showMenu"
          v-html="addNodeByWX(filterData.commitUserId)"
        ></div>
        <el-select
          ref="elPersonSelect"
          v-model="filterData.commitUserId"
          clearable
          filterable
          remote
          :remote-method="getUserList"
          @blur="showWxtext = true"
          @clear="getUserList"
          @focus="showWxtext = false"
        >
          <el-option v-for="item of userList" :key="item.userId" :value="item.userId">
            <span style="float: left" v-html="addNodeByWX(item.name)"></span>
          </el-option>
        </el-select>
      </div>
      <el-select v-else v-model="filterData.commitUserId" clearable filterable>
        <el-option
          v-for="item of userList"
          :key="item.userId"
          :label="item.name"
          :value="item.userId"
        ></el-option>
      </el-select>
    </div>
    <div v-if="appList.length > 1" class="form-item">
      <div class="label">{{ $t('flow.chooseApp') + ':' }}</div>
      <el-select v-model="appFilterJSON" clearable filterable>
        <el-option :label="$t('label.total')" value=""></el-option>
        <el-option
          v-for="item of appList"
          :key="item.id"
          :label="item.name"
          :value="JSON.stringify(item)"
        ></el-option>
      </el-select>
    </div>
    <div class="form-item">
      <div class="label">{{ $t('flow.chooseForm') + ':' }}</div>
      <el-select v-model="filterData.menuJSON" clearable filterable>
        <el-option :label="$t('label.total')" value=""></el-option>
        <el-option
          v-for="item of menuList"
          :key="item.id"
          :label="item.name"
          :value="JSON.stringify(item)"
        ></el-option>
      </el-select>
    </div>
    <!-- 对提交给我的tab 特殊处理 -->
    <div v-if="/^(giveToFlow)$/.test(flowType)" class="form-item">
      <div class="label">{{ $t('flow.approveState') + ':' }}</div>
      <el-select v-model="filterData.taskType" clearable filterable>
        <el-option :label="$t('label.total')" value=""></el-option>
        <el-option
          v-for="(taskType, index) of taskTypeList"
          :key="index"
          :label="taskType.memo"
          :value="taskType.type"
        ></el-option>
      </el-select>
    </div>
    <div v-else class="form-item">
      <div class="label">{{ $t('flow.approveState') + ':' }}</div>
      <el-select v-model="filterData.flowStatus" clearable filterable>
        <el-option :label="$t('label.total')" value=""></el-option>
        <el-option
          v-for="(taskType, index) of taskTypeList"
          :key="index"
          :label="taskType.memo"
          :value="taskType.type"
        ></el-option>
      </el-select>
    </div>
    <div class="form-item">
      <div class="label">{{ $t('nouns.time') }}</div>
      <el-date-picker
        v-model="filterData.commitTime"
        :default-time="['00:00:00', '23:59:59']"
        :end-placeholder="$t('label.endTime')"
        :range-separator="$t('unit.to')"
        :start-placeholder="$t('label.startTime')"
        type="daterange"
        value-format="timestamp"
      >
      </el-date-picker>
    </div>
  </div>
</template>

<script>
import { userList } from '@/api/system'
// import { getAppList } from '@/api/application'
import {
  processSearchAppFilter,
  processSearchFormFilter,
  processSearchTypeFilter
} from '@/api/approve'
import { approveTab } from '@/constants/common/approveTab'

export default {
  name: 'ApproveFilter',
  props: {
    activeName: {
      type: String
    },
    taskType: {
      type: String
    },
    flowType: {
      // 流程列表tab 类型
      type: String, // giveToFlow | creatFlow | noticeToFlow | allFlow
      required: true
    }
  },
  data() {
    const DEFAULT_COMMIT_TIME_RANGE = 3600 * 1000 * 24 * 180
    const getDefaultTimeArea = (defaultRangeLen) => {
      // 后端要求初次加载时限制查询时长
      // const now = new Date().setMilliseconds(0)
      const now = new Date() // end 精确到当天59，确保用户在默认范围内，更新数据时正常过滤
      now.setMilliseconds(0)
      now.setSeconds(59)
      now.setMinutes(59)
      now.setHours(23)

      const end = now.getTime()

      return [end - defaultRangeLen + 1000, end] // 加1000毫秒，到00:00:00
    }

    return {
      value6: [],
      appFilterJSON: '',
      filterData: {
        menuJSON: '',
        taskType: '',
        flowStatus: '',
        commitTime: getDefaultTimeArea(DEFAULT_COMMIT_TIME_RANGE), // 时间，后端要求初次查时只查半年
        commitUserId: ''
      },
      // 用户列表
      userList: [],
      // 应用列表
      appList: [],
      // 表单列表
      menuList: [],
      // 状态列表
      taskTypeList: [],
      approveTab: approveTab,
      // 是否显示微信的显示遮罩
      showWxtext: true
    }
  },
  computed: {
    isWxAppUnbind() {
      return utils.reminderTipsShow()
    }
  },

  watch: {
    '$route.query.processNodeTaskId': {
      immediate: true,
      handler(val, oldVal) {
        if (this.$route.query.processType === this.taskType) {
          this.filterDataChange({
            processNodeTaskId: val,
            processTaskId: this.$route.query.processTaskId
          })
        } else {
          this.filterDataChange()
        }
      }
    },
    appFilterJSON: {
      handler() {
        this.filterData.menuFilter = ''
        this.getMenuList()
        this.filterDataChange()
      }
    },
    filterData: {
      // immediate: true,
      deep: true,
      handler() {
        // alert(111)

        this.filterDataChange()
      }
    }
  },
  mounted() {
    this.getUserList()
    this.getAppList()
    this.getMenuList()
    this.getTaskList()
  },
  methods: {
    filterDataChange(taskObj) {
      const { menuJSON, taskType, flowStatus, commitTime, commitUserId } = JSON.parse(
        JSON.stringify(this.filterData)
      )
      const app = this.appFilterJSON ? JSON.parse(this.appFilterJSON) : {}
      const menu = menuJSON ? JSON.parse(menuJSON) : {}
      // let { id, saasMark, saasAppType } = app
      const appData = {
        appId: app.id,
        saasMark: app.saasMark,
        saasAppType: app.saasAppType
      }
      // let { id, saasMark, businessType } = menu
      const menuData = {
        menuId: menu.id,
        saasMark: menu.saasMark || appData.saasMark,
        businessType: menu.businessType
      }

      const queryData = {
        taskType,
        flowStatus,
        commitTime: commitTime || [],
        commitUserId,
        ...appData,
        ...menuData,
        ...taskObj
      }
      this.$emit('filterDataChange', queryData)
    },
    // 获取用户列表
    getUserList(val) {
      userList({ nameLike: val, pageSize: 10000, delIgnore: 1 })
        .then((res) => {
          this.userList = res.result.userList
        })
        .catch(() => {})
    },
    // 获取应用列表
    getAppList() {
      processSearchAppFilter({})
        .then((res) => {
          this.appList = res.result.list
          // if (this.appList.length === 1) {
          //   this.appFilterJSON = JSON.stringify(this.appList[0])
          // }
        })
        .catch(() => {})
    },
    // 获取审批表单列表
    getMenuList() {
      // 在每次获取表单列表时清空选择表单的值
      this.filterData.menuJSON = ''
      const data = this.appFilterJSON === '' ? {} : JSON.parse(this.appFilterJSON)
      const { id, saasMark, saasAppType } = data

      processSearchFormFilter({
        appId: id,
        saasMark: saasMark, // 必传 1saas 2PaaS
        saasAppType: saasAppType // saas用
      })
        .then((res) => {
          this.menuList = res.result.list
        })
        .catch(() => {})
    },
    // 获取状态列表
    getTaskList() {
      // "processType" : "todo" // todo： 提交给哦我 ；all：全部；cc：抄送我的；create：我提交的
      // giveToFlow | creatFlow | noticeToFlow | allFlow
      const map = {
        giveToFlow: 'todo',
        creatFlow: 'create',
        noticeToFlow: 'cc',
        allFlow: 'all'
      }
      const processType = map[this.flowType]
      processSearchTypeFilter({ processType: processType })
        .then((res) => {
          this.taskTypeList = res.result.processTypeList
        })
        .catch(() => {})
    },
    showMenu() {
      this.$refs.elPersonSelect.toggleMenu()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-item {
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 10px;
  .label {
    display: inline-block;
    padding-right: 5px;
  }
  .personSelect {
    display: inline-block;
  }
  .showWxText {
    position: absolute;
    top: 2px;
    left: 64px;
    z-index: 100;
    width: 63%;
    height: 88%;
    line-height: 27px;
    white-space: nowrap;
    cursor: pointer;
    background-color: #fff;
  }
}
</style>
