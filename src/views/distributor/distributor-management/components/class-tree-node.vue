<!--
 * @Description:
-->
<template>
  <VueDraggable
    v-model="classList"
    v-draggable
    handle=".icon-option-sort"
    :scroll="true"
    :scroll-sensitivity="150"
    :scroll-speed="1"
    tag="ul"
    v-bind="draggableOption()"
    @end="dragEnd"
    @start="dragStart"
  >
    <template v-if="classList.length">
      <template v-for="levelA in classList">
        <li :key="levelA.id" class="menu-item" :data-id="levelA.id">
          <div
            class="menu-title"
            :class="{ 'hover-title': levelA.id === getCurrentNode.id }"
            @click.stop="groupUnfold(levelA.id, $event)"
            @mouseover="mouseover(levelA)"
          >
            <span class="menu-title__content">
              <span class="menu-title__content--text">{{ levelA.name }}</span>
              <i
                v-if="levelA.childList && levelA.childList.length"
                class="el-icon-caret-right"
                :class="{ 'menu-title__content--icon': getMenuUnfoldList.indexOf(levelA.id) > -1 }"
              ></i>
            </span>
            <classHandle
              v-if="levelA.id === getCurrentNode.id"
              :current-class="levelA"
              :is-search="isSearch"
            >
            </classHandle>
          </div>
          <div class="menu-child">
            <v-tree
              v-show="getMenuUnfoldList.indexOf(levelA.id) > -1"
              :key="levelA.id"
              class="menu-child-item"
              :menu-list.sync="levelA.childList"
              :parent-info="levelA"
              @dragEnd="childDragEnd"
              @getClassList="getClassList"
            ></v-tree>
          </div>
        </li>
      </template>
    </template>
  </VueDraggable>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import regionClassMixin from '@/views/distributor/distributor-management/mixins/region-class.js'

export default {
  name: 'VTree',

  components: {},

  mixins: [regionClassMixin],

  props: {
    menuList: {
      // 同一级所有节点信息
      type: Array,
      default: () => []
    },
    isSearch: {
      type: Boolean,
      default: false
    },
    expandAll: {},
    parentInfo: {
      // 所属节点，父节点的信息
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      rules: {
        name: {
          required: true,
          message: this.$t('placeholder.inputPls', { attr: this.$t('nouns.areaName') }),
          trigger: 'change'
        }
      },
      form: {
        name: ''
      },
      submitType: '',

      parentId: '', // 父节点的id
      dialogVisible: false,
      dialogTitle: ''
    }
  },

  mounted() {},

  computed: {
    ...mapGetters(['getMenuUnfoldList']),
    classList: {
      get() {
        return this.menuList
      },
      set(val) {
        this.$emit('update:menuList', val)
      }
    }
  },

  methods: {
    ...mapActions(['updateMenuUnfold', 'setCurrentNode']),

    // 拖动组件的设置属性
    draggableOption() {
      return {
        swapThreshold: 0.4,
        emptyInsertThreshold: 2,
        invertSwap: true,
        animation: 100,
        ghostClass: 'menu-item--clone',
        fallbackClass: 'menu-item--shadow',
        group: 'abc'
      }
    },

    /**
     * @description: 拖动开始，把展开的菜单全部隐藏
     * @param {type}
     * @return:
     */
    dragStart(evt) {
      const id = +evt.item.dataset.id
      this.getMenuUnfoldList.includes(id) && this.updateMenuUnfold(id)
      // this.$nextTick(() => {

      // })
      console.log(evt)
    },

    /**
     * @description: 鼠标经过每一行的样式改变
     * @param {type}
     * @return:
     */
    mouseover(node) {
      // setTimeout(() => {
      //   this.currentNode = node
      // }, 300)
      // debugger
      this.setCurrentNode(node)
    },
    // 当前组件拖动结束
    dragEnd(val) {
      this.$emit('dragEnd')
    },
    // 子组件拖动结束
    childDragEnd() {
      this.dragEnd()
    },
    getClassList() {
      this.$emit('getClassList')
    },
    groupUnfold(id, event) {
      this.updateMenuUnfold(id)
      event.stopPropagation()
    }
  },

  watch: {
    classList: {
      handler(val) {
        // console.log(val, 'classList')
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.search-empty {
  margin-top: 30px;
  color: $text-grey;
  text-align: center;
}
.menu-item {
  line-height: 40px;
  cursor: pointer;
  &--clone {
    background: rgba(255, 142, 61, 0.4);
  }
}
.menu-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 22px;
  &__content {
    display: flex;
    align-items: center;
    height: 40px;
    overflow: hidden;
    line-height: 40px;
    &--text {
      flex: auto;
      @include singleline-ellipsis;
    }
    &--icon {
      flex: none;
      transform: rotate(90deg);
    }
  }
  &--child {
    padding-left: 44px;
  }
}
.hover-title {
  background: rgba(255, 142, 61, 0.1);
}
.menu-child {
  // margin-top: -5px;
  // padding-top: 5px;
  padding-left: 22px;
  .menu-child-item {
    // margin-top: -5px;
    // padding-top: 20px;
  }
}
</style>
