<!--
 * @Description: 汇总方式 radio
-->
<template>
  <div class="summary-type">
    <div class="attr">
      <slot name="label"
        ><span class="attr__title">{{ labelName }}</span></slot
      >
      <el-radio-group v-model="widgetInfo" @change="changeRadio">
        <el-radio :label="0">{{ $t('display.chartCustom.subtotal') }}</el-radio>
        <el-radio :label="1">{{ $t('chartEdit.summary') }}</el-radio>
        <el-radio :label="2">{{
          $t('display.chartCustom.subtotal') + '+' + $t('chartEdit.summary')
        }}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SummaryType',
  props: {
    // 小模块名字
    labelName: {
      type: String,
      default: ''
    },
    widget: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      widgetInfo: 0
    }
  },
  watch: {
    widget: {
      handler(val) {
        // if (val) {
        this.widgetInfo = val
        // }
      },
      deep: true,
      immediate: true
    },
    widgetInfo: {
      handler(val) {
        this.$emit('update:widget', val)
      },
      deep: true
    }
  },
  mounted() {},
  methods: {
    changeRadio(e) {
      this.$emit('update:widget', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.summary-type {
  .attr {
    margin-top: 20px;
    margin-bottom: 20px;
    // border-bottom: 1px solid $line-filter;
    // padding-bottom: 20px;
    .el-radio-group {
      display: block;
      margin-top: 10px;
      .el-radio {
        display: block;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
