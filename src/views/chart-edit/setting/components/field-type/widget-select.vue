<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 下拉单选
 -->
<template>
  <div class="widget-select">
    <el-select
      v-model="widget[defaultConfig.value]"
      :filterable="searchAble"
      :placeholder="$t('placeholder.choosePls', { attr: '' })"
    >
      <el-option
        v-for="item in computedOptions"
        :key="item.key"
        :disabled="item.disabled"
        :label="item.value"
        :value="item.key"
      >
      </el-option>
    </el-select>
    <slot name="tip"></slot>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

export default {
  name: 'WidgetSelect',

  props: {
    widget: {
      type: Object,
      default: () => ({})
    },
    useCustomOptions: {
      type: Boolean,
      default: false
    },
    customOptions: {
      type: Array,
      default: () => []
    },
    defaultConfig: {
      type: Object,
      default: () => ({
        options: 'options',
        value: 'value'
      })
    },
    searchAble: {
      type: <PERSON><PERSON>an,
      default: true
    }
  },

  data() {
    return {
      searchKey: ''
    }
  },

  computed: {
    options() {
      return this.useCustomOptions ? this.customOptions : this.widget[this.defaultConfig.options]
    },
    computedOptions() {
      if (!this.searchKey) return this.options
      return this.options.filter((item) => item.value.includes(this.searchKey))
    }
  }
}
</script>

<style lang="scss" scoped></style>
