/*
 * @Description: 自定义属性写死
 */
import i18n from '@/lang'
// 通用的汇总方式
const SUMMARY_LIST = [
  { key: 'sum', value: i18n.t('chartEdit.sum') },
  { key: 'avg', value: i18n.t('chartEdit.average') },
  { key: 'max', value: i18n.t('nouns.maxNum') },
  { key: 'min', value: i18n.t('nouns.minNum') },
  { key: 'count', value: i18n.t('chartEdit.count') }
]
// 计算字段使用的汇总方式
const COMPUTED_SUMMARY_LIST = [
  { key: 'sum', value: i18n.t('chartEdit.sum') },
  { key: 'avg', value: i18n.t('chartEdit.average') },
  { key: 'max', value: i18n.t('nouns.maxNum') },
  { key: 'min', value: i18n.t('nouns.minNum') },
  { key: 'agg', value: '聚合' }
]
const COUNT_COMPUTED_SUMMARY_LIST = [
  { key: 'avg', value: i18n.t('chartEdit.average') },
  { key: 'max', value: i18n.t('nouns.maxNum') },
  { key: 'min', value: i18n.t('nouns.minNum') },
  { key: 'agg', value: '聚合' }
]
// 副指标使用的汇总方式
const SUBQUOTA_SUMMARY_LIST = [
  { key: 'sum', value: i18n.t('chartEdit.sum') },
  { key: 'avg', value: i18n.t('chartEdit.average') },
  { key: 'count', value: i18n.t('chartEdit.count') }
]
// 停留时长
const LENGTH_OF_STAY = [
  { key: 1, value: '秒' },
  { key: 2, value: '分钟' },
  { key: 3, value: '小时' },
  { key: 4, value: '天' }
]

export default {
  data() {
    return {
      conditionList: {
        text: [],
        num: [
          {
            selectList: [
              { key: 'groupingUsed', value: this.$t('chartEdit.microOperator') },
              { key: 'percentUsed', value: this.$t('unit.percentage') },
              { key: 'accuracy', value: this.$t('chartEdit.decimalDigits') }
            ],
            summaryList: SUMMARY_LIST,
            // 副指标使用的汇总方式
            subQuotaSummaryList: SUBQUOTA_SUMMARY_LIST,
            // 停留时长
            stageStayTimeUnit: 3,
            stageStayTimeUnitList: LENGTH_OF_STAY,
            // 计算字段使用的汇总方式
            computedSummaryList: COMPUTED_SUMMARY_LIST,
            countComputedSummaryList: COUNT_COMPUTED_SUMMARY_LIST,
            unitList: [
              { key: 'numbers', value: this.$t('unit.an') },
              { key: 'money', value: this.$t('unit.rmb') },
              { key: 'custom', value: this.$t('nouns.custom') }
            ],
            belongList: [], // 归属人从外层的dataSource中获取
            timeList: [], // 统计时间从外层的dataSource中获取
            summaryName: this.$t('chartEdit.summaryWay'),
            summaryType: 'radioType',
            widgetName: this.$t('chartEdit.dataFormat'),
            widgetType: 'combo',
            unitName: this.$t('unit.unit'),
            unitType: 'unit',
            belongName: this.$t('chartEdit.belong'), // 归属人选择-指标图使用
            belongType: 'radioType',
            timeName: this.$t('chartEdit.countTime'), // 统计时间选择-指标图使用
            timeType: 'radioType',
            // 高级计算
            advancedComputing: ['none'], // 默认无
            advancedComputingType: 'cascaderType',
            advancedComputingName: this.$t('chartEdit.advancedComputing'),
            advancedComputingList: [
              { value: 'none', label: this.$t('unit.no') },
              {
                value: 'yearChain',
                label: this.$t('chartEdit.yearChain'),
                disabled: this.yearChainDiabled,
                children: [
                  {
                    value: 'chainValue',
                    label: this.$t('chartEdit.sequentialIncrease')
                  },
                  {
                    value: 'chainRate',
                    label: this.$t('chartEdit.sequentialGrowthRate')
                  },
                  {
                    value: 'yearValue',
                    label: this.$t('chartEdit.yearOnYearGrowth')
                  },
                  {
                    value: 'yearRate',
                    label: this.$t('chartEdit.yearOnYearGrowthRate')
                  }
                ]
              }
            ],
            // 时间维度默认值
            timeDimension: 'y-M-d',
            // 时间维度-环比 (日、周、月、季度、年)
            timeDimensionListForChain: [
              { key: 'y-M-d', value: this.$t('unit.days') },
              { key: 'y-W', value: this.$t('unit.week') },
              { key: 'y-M', value: this.$t('unit.month') },
              { key: 'y-q', value: this.$t('unit.quarter') },
              { key: 'y', value: this.$t('unit.year') }
            ],
            // 时间维度-同比(月、季度、年)
            timeDimensionListForYear: [
              { key: 'y-M', value: this.$t('unit.month') },
              { key: 'y-q', value: this.$t('unit.quarter') },
              { key: 'y', value: this.$t('unit.year') }
            ],
            // 数值格式
            numFormat: 0,
            numFormatList: [
              { key: 0, value: this.$t('unit.numFormat.default') },
              { key: 1, value: this.$t('unit.numFormat.k') },
              { key: 2, value: this.$t('unit.numFormat.w') },
              { key: 3, value: this.$t('unit.numFormat.million') },
              { key: 4, value: this.$t('unit.numFormat.hundredMillion') }
            ]
          }
        ],
        long: [
          {
            selectList: [
              { key: 'groupingUsed', value: this.$t('chartEdit.microOperator') },
              { key: 'percentUsed', value: this.$t('unit.percentage') },
              { key: 'accuracy', value: this.$t('chartEdit.decimalDigits') }
            ],
            summaryList: SUMMARY_LIST,
            // 副指标使用的汇总方式
            subQuotaSummaryList: SUBQUOTA_SUMMARY_LIST,
            // 计算字段使用的汇总方式
            computedSummaryList: COMPUTED_SUMMARY_LIST,
            unitList: [
              { key: 'numbers', value: this.$t('unit.an') },
              { key: 'money', value: this.$t('unit.rmb') },
              { key: 'custom', value: this.$t('nouns.custom') }
            ],
            belongList: [], // 归属人从外层的dataSource中获取
            timeList: [], // 统计时间从外层的dataSource中获取
            summaryName: this.$t('chartEdit.summaryWay'),
            summaryType: 'radioType',
            widgetName: this.$t('chartEdit.dataFormat'),
            widgetType: 'combo',
            unitName: this.$t('unit.unit'),
            unitType: 'unit',
            belongName: this.$t('chartEdit.belong'), // 归属人选择-指标图使用
            belongType: 'radioType',
            timeName: this.$t('chartEdit.countTime'), // 统计时间选择-指标图使用
            timeType: 'radioType',
            // 高级计算
            advancedComputing: ['none'], // 默认无
            advancedComputingType: 'cascaderType',
            advancedComputingName: this.$t('chartEdit.advancedComputing'),
            advancedComputingList: [
              { value: 'none', label: this.$t('unit.no') },
              {
                value: 'yearChain',
                label: this.$t('chartEdit.yearChain'),
                disabled: this.yearChainDisabled,
                children: [
                  {
                    value: 'chainValue',
                    label: this.$t('chartEdit.sequentialIncrease')
                  },
                  {
                    value: 'chainRate',
                    label: this.$t('chartEdit.sequentialGrowthRate')
                  },
                  {
                    value: 'yearValue',
                    label: this.$t('chartEdit.yearOnYearGrowth')
                  },
                  {
                    value: 'yearRate',
                    label: this.$t('chartEdit.yearOnYearGrowthRate')
                  }
                ]
              }
            ],
            // 数值格式
            numFormat: 0,
            numFormatList: [
              { key: 0, value: this.$t('unit.numFormat.default') },
              { key: 1, value: this.$t('unit.numFormat.k') },
              { key: 2, value: this.$t('unit.numFormat.w') },
              { key: 3, value: this.$t('unit.numFormat.million') },
              { key: 4, value: this.$t('unit.numFormat.hundredMillion') }
            ],
            // 时间维度默认值
            timeDimension: 'y-M-d',
            // 时间维度-环比 (日、周、月、季度、年)
            timeDimensionListForChain: [
              { key: 'y-M-d', value: '日' },
              { key: 'y-W', value: '周' },
              { key: 'y-M', value: '月' },
              { key: 'y-q', value: '季度' },
              { key: 'y', value: '年' }
            ],
            // 时间维度-同比(月、季度、年)
            timeDimensionListForYear: [
              { key: 'y-M', value: '月' },
              { key: 'y-q', value: '季度' },
              { key: 'y', value: '年' }
            ]
          }
        ],
        date: [
          {
            selectList: [
              { key: 'yyyy-MM-dd', value: '2020-01-01' },
              { key: 'yyyy/MM/dd', value: '2020/1/1' },
              { key: 'yyyy年MM月dd日', value: '2020年1月1日' },
              { key: 'yyyy年MM月dd日 E', value: '2020年1月1日 星期三' },
              { key: 'yyyy年MM月', value: '2020年1月' },
              { key: 'MM月dd日', value: '1月1日' },
              { key: 'E', value: this.$t('chartEdit.wednesday') }
            ],
            selectListTwo: [
              { key: 'yyyy-MM-dd HH:mm', value: '2020-01-01 01:01' },
              { key: 'yyyy-MM-dd', value: '2020-01-01' },
              { key: 'yyyy/MM/dd HH:mm', value: '2020/1/1 01:01' },
              { key: 'yyyy/MM/dd', value: '2020/1/1' },
              { key: 'yyyy年MM月dd日', value: '2020年1月1日' },
              { key: 'yyyy年MM月dd日 HH:mm', value: '2020年1月1日 01:01' },
              { key: 'yyyy年MM月dd日 E', value: '2020年1月1日 星期三' },
              { key: 'yyyy年MM月', value: '2020年1月' },
              { key: 'MM月dd日', value: '1月1日' },
              { key: 'E', value: this.$t('chartEdit.wednesday') }
            ],
            selectListGroup: [
              { key: 'y', value: this.$t('unit.year') },
              { key: 'y-q', value: this.$t('unit.year') + '-' + this.$t('unit.quarter') },
              { key: 'y-M', value: this.$t('unit.year') + '-' + this.$t('unit.month') },
              { key: 'y-W', value: this.$t('unit.year') + '-' + this.$t('unit.week') },
              {
                key: 'y-M-d',
                value:
                  this.$t('unit.year') + '-' + this.$t('unit.month') + '-' + this.$t('unit.days')
              }
            ],
            widgetName: this.$t('chartEdit.dataFormat'),
            widgetType: 'radioType'
          }
        ]
      }
    }
  }
}
