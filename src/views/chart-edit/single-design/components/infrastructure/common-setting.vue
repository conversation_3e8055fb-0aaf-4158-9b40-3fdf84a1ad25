<!--
 * @Description: 图表右侧公共信息设置
-->
<template>
  <div>
    <div class="right-item">
      <h3>{{ $t('chartEdit.description') }}</h3>
      <el-input
        v-model="memo"
        class="attr__input"
        max-length="500"
        type="textarea"
        @change="inputMemo"
      ></el-input>
    </div>
    <!-- 可见数据权限 -->
    <div v-if="!templateDataPermissionArray || !isDataSetSource" class="right-item">
      <view-control
        :field-arr="belongList"
        :permissions="chartExplain.permissions"
        :single="chartExplain.single"
      ></view-control>
    </div>
    <!-- 操作权限 -->
    <div class="right-item">
      <h3>{{ $t('business.limitOperate') }}</h3>
      <authority-setting
        :editable="false"
        :is-export="true"
        :permissions="chartExplain.permissions"
        :relative="false"
      ></authority-setting>
    </div>
  </div>
</template>

<script>
import ViewControl from '@/views/chart-edit/single-design/components/view-control.vue'
import AuthoritySetting from '@/components/authority-setting/index.vue'

export default {
  props: {
    chartExplain: {
      type: Object,
      default: () => ({})
    },
    belongList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    AuthoritySetting,
    ViewControl
  },
  inject: ['templateDataPermissionArray'],
  data() {
    return {
      memo: '' // 描述信息
    }
  },

  computed: {
    dataSourceType() {
      return this.chartExplain.single
    },
    isDataSetSource() {
      return this.dataSourceType === '2'
    }
  },

  mounted() {
    this.memo = this.chartExplain.memo ? this.chartExplain.memo : ''
  },
  methods: {
    inputMemo() {
      this.$set(this.chartExplain, 'memo', this.memo)
    }
  }
}
</script>

<style lang="scss" scoped>
.right-item {
  margin-top: 24px;
  h3 {
    margin-bottom: 10px;
    font-weight: bold;
    color: $text-main;
  }
}
</style>
