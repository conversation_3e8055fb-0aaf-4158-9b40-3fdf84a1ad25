<template>
  <div class="raw-data-setting-box">
    <!--  原始数据设置 -->
    <el-checkbox
      v-model="rawDataShow"
      class="attr__title block"
      :false-label="0"
      :true-label="1"
      @change="rawDataShowChange"
    >
      在飘窗中显示原始数据
    </el-checkbox>

    <div v-if="rawDataShow" class="data-form-setting">
      <div class="setting-item">
        <span class="label">数值格式:</span>
        <el-select v-model="numFormat" size="small">
          <el-option
            v-for="item in numFormatOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <div class="data-form setting-item">
        <span class="label">数据格式:</span>
        <el-checkbox
          v-model="groupingUsed"
          class="attr__title block"
          :false-label="0"
          :true-label="1"
        >
          千分符
        </el-checkbox>
      </div>

      <div class="sub-setting-item">
        <span class="label">小数位数</span>
        <el-input-number
          v-model="accuracy"
          controls-position="right"
          :max="6"
          :min="0"
          size="small"
        ></el-input-number>
      </div>

      <div class="preview-item">
        <span class="label">预览效果:</span>
        <span class="result"> {{ previewRes }} </span>
      </div>
    </div>
  </div>
</template>

<script>
import { thousandSeparator } from '@/utils/chart-manage'

export default {
  name: 'RawDataSetting',

  components: {},

  props: {
    chartExplain: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      numFormatOptions: [
        {
          label: '默认值',
          value: '0'
        },
        {
          label: '千',
          value: '1'
        },
        {
          label: '万',
          value: '2'
        },
        {
          label: '百万',
          value: '3'
        },
        {
          label: '亿',
          value: '4'
        }
      ],
      formatMap: {
        0: '',
        1: '千',
        2: '万',
        3: '百万',
        4: '亿'
      }
    }
  },

  computed: {
    // 放axisSetting里去了，
    rawDataShow: {
      get() {
        return !!this.chartExplain.explains.axisSetting?.rawData?.show
      },
      set(val) {
        this.$set(this.chartExplain.explains, 'axisSetting', {
          ...(this.chartExplain.explains?.axisSetting || {}),
          rawData: {
            ...(this.chartExplain.explains?.axisSetting?.rawData || {}),
            show: val
          }
        })
      }
    },
    percentUsed: {
      get() {
        return !!this.chartExplain.explains.axisSetting?.rawData?.dataForm?.percentUsed
      },
      set(val) {
        this.$set(this.chartExplain.explains, 'axisSetting', {
          ...(this.chartExplain.explains?.axisSetting || {}),
          rawData: {
            ...(this.chartExplain.explains?.axisSetting?.rawData || {}),
            dataForm: {
              ...(this.chartExplain.explains?.axisSetting?.rawData?.dataForm || {}),
              percentUsed: val
            }
          }
        })
      }
    },
    groupingUsed: {
      get() {
        return !!this.chartExplain.explains.axisSetting?.rawData?.dataForm?.groupingUsed
      },
      set(val) {
        this.$set(this.chartExplain.explains, 'axisSetting', {
          ...(this.chartExplain.explains?.axisSetting || {}),
          rawData: {
            ...(this.chartExplain.explains?.axisSetting?.rawData || {}),
            dataForm: {
              ...(this.chartExplain.explains?.axisSetting?.rawData?.dataForm || {}),
              groupingUsed: val
            }
          }
        })
      }
    },
    accuracy: {
      get() {
        return this.chartExplain.explains.axisSetting?.rawData?.dataForm?.accuracy || 0
      },
      set(val) {
        this.$set(this.chartExplain.explains, 'axisSetting', {
          ...(this.chartExplain.explains?.axisSetting || {}),
          rawData: {
            ...(this.chartExplain.explains?.axisSetting?.rawData || {}),
            dataForm: {
              ...(this.chartExplain.explains?.axisSetting?.rawData?.dataForm || {}),
              accuracy: val
            }
          }
        })
      }
    },
    numFormat: {
      get() {
        return this.chartExplain.explains.axisSetting?.rawData?.dataForm?.numFormat || '0'
      },
      set(val) {
        this.$set(this.chartExplain.explains, 'axisSetting', {
          ...(this.chartExplain.explains?.axisSetting || {}),
          rawData: {
            ...(this.chartExplain.explains?.axisSetting?.rawData || {}),
            dataForm: {
              ...(this.chartExplain.explains?.axisSetting?.rawData?.dataForm || {}),
              numFormat: val
            }
          }
        })
      }
    },
    previewRes() {
      return this.rawDataLabel(
        '9999999.00',
        this.chartExplain.explains.axisSetting?.rawData?.dataForm,
        this.rawDataShow
      )
    }
  },

  watch: {},

  created() {},
  mounted() {},

  methods: {
    formatNumber(value, option) {
      const num = parseFloat(value)
      switch (option) {
        case '0':
          return num.toFixed(2) // 默认值
        case '1':
          return (num / 1000).toFixed(3) // 千
        case '2':
          return (num / 10000).toFixed(4) // 万
        case '3':
          return (num / 1000000).toFixed(6) // 百万
        case '4':
          return (num / 100000000).toFixed(8) // 亿
        default:
          return 'Invalid option'
      }
    },

    rawDataLabel(val, dataForm, rawDataShow) {
      if (rawDataShow && dataForm) {
        let res = this.formatNumber(val, dataForm.numFormat)
        res = parseFloat(res).toFixed(dataForm.accuracy)

        return (
          (dataForm.groupingUsed ? thousandSeparator(res) : res) +
          this.formatMap[dataForm.numFormat]
        )
      }

      return ''
    },

    rawDataShowChange(val) {
      if (val && !this.chartExplain.explains.axisSetting?.rawData?.dataForm) {
        this.$set(this.chartExplain.explains, 'axisSetting', {
          ...(this.chartExplain.explains?.axisSetting || {}),
          rawData: {
            ...(this.chartExplain.explains?.axisSetting?.rawData || {}),
            dataForm: {
              numFormat: '0',
              groupingUsed: 0,
              accuracy: 0
            }
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.raw-data-setting-box {
  .attr__title {
    font-weight: bold;
  }
  .block {
    display: block;
    margin-bottom: 5px;
  }
  .data-form-setting {
    box-sizing: border-box;
    padding-left: 22px;
    .setting-item {
      display: flex;
      height: 32px;
      padding-bottom: 12px;
      .label {
        box-sizing: border-box;
        display: inline-block;
        width: 78px;
        padding-right: 8px;
        font-size: 14px;
        line-height: 32px;
        color: $text-plain;
      }
      .accuracy {
        height: 32px;
        line-height: 32px;
      }
    }
    .data-form {
      height: 20px;
      padding-bottom: 8px;
      line-height: 20px;
      .label {
        height: 20px;
        line-height: 20px;
      }
      .attr__title {
        height: 20px;
        line-height: 20px;
      }
    }

    .sub-setting-item {
      display: flex;
      height: 32px;
      padding-left: 78px;
      .label {
        display: inline-block;
        width: 56px;
        padding-right: 8px;
        line-height: 32px;
        color: $text-plain;
      }
      .el-input-number {
        width: 110px;
        line-height: 32px;
      }
    }

    .preview-item {
      display: flex;
      height: 52px;
      padding-top: 12px;
      .label {
        display: inline-block;
        width: 70px;
        padding-right: 8px;
      }
      .result {
        display: inline-block;
        width: 174px;
        height: 52px;
        font-size: 13px;
        line-height: 52px;
        color: $text-plain;
        text-align: center;
        background: $neutral-color-1;
        border: 1px solid $neutral-color-3;
        border-radius: 2px;
      }
    }
  }
}
</style>
