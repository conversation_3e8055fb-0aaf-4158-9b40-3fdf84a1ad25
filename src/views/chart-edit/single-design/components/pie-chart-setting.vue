<template>
  <div class="pie-chart-setting">
    <BI-tabs :label-list="labelList" @nowSelect="nowSelect" />
    <!-- 公共信息 -->
    <div v-if="activeName === 'left'">
      <common-setting :belong-list="belongList" :chart-explain="chartExplain"></common-setting>
      <!-- 数据显示 -->
      <div class="setting-box">
        <h3>数据显示</h3>
        <el-checkbox v-model="showDataDisplay" class="attr__title" false-label="0" true-label="1">
          <div class="data-display">
            <p class="setting-box_text">显示</p>
            <div @click="dataDisplayClick">
              <el-input-number
                v-model="dataDisplay"
                class="setting-box_input"
                controls-position="right"
                :max="20"
                :min="1"
              ></el-input-number>
            </div>
            <p>条数据</p>
          </div>
        </el-checkbox>
      </div>
      <div class="label-box setting-box">
        <h3>{{ $t('label.dataLabel') }}</h3>
        <el-checkbox v-model="showValue" class="attr__title">
          {{ $t('label.showDataValue') }}
        </el-checkbox>
        <el-checkbox v-model="showPercent" class="attr__title">
          {{ $t('label.showDataPercent') }}
        </el-checkbox>
        <el-checkbox v-model="showTotal"> 显示总计 </el-checkbox>
      </div>
      <!-- 显示图例 -->
      <div class="setting-box">
        <h3>图例</h3>
        <el-checkbox v-model="showLegend" class="attr__title"> 显示图例 </el-checkbox>
      </div>
    </div>
    <div v-else>
      <chart-setting
        :global-style-option="
          Object.keys(globalStyleOption).length ? globalStyleOption : globalDefault
        "
        :item-style-option="chartExplain.itemStyleOption"
      ></chart-setting>
    </div>
  </div>
</template>

<script>
import CommonSetting from './infrastructure/common-setting.vue'
import ChartSetting from '@/views/chart-edit/ui-design/setting-box/chart-setting.vue'
import { GLOBAL_STYLE_OPTION } from '@/views/chart-edit/ui-design/config/theme-color.js'
import BITabs from './infrastructure/BI-tabs.vue'

export default {
  name: 'PieChartSetting',

  components: {
    CommonSetting,
    ChartSetting,
    BITabs
  },

  props: {
    chartExplain: {
      type: Object,
      default: () => {}
    },
    belongList: {
      type: Array,
      default: () => []
    },
    globalStyleOption: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      // 奇怪吧，四年前这么设计的，根本没法改
      showValue: [2, 4].includes(this.chartExplain.explains.dataLabelFlag),
      showPercent: [3, 4].includes(this.chartExplain.explains.dataLabelFlag),
      activeName: 'left',
      labelList: ['功能配置', '组件样式'],
      globalDefault: GLOBAL_STYLE_OPTION[0] // 默认的全局组件样式
    }
  },

  computed: {
    dataLabel() {
      const { showValue, showPercent } = this
      return {
        showValue,
        showPercent
      }
    },
    // 显示总计
    showTotal: {
      get() {
        return this.chartExplain.explains.pie.displayTotal
      },
      set(val) {
        this.$set(this.chartExplain.explains.pie, 'displayTotal', val)
      }
    },
    // 图例
    showLegend: {
      get() {
        return Boolean(this.chartExplain.explains.pie.legend)
      },
      set(val) {
        this.$set(this.chartExplain.explains.pie, 'legend', val)
      }
    },
    // 数据显示条数
    dataDisplay: {
      get() {
        return this.chartExplain.explains.pie.dataDisplay
      },
      set(val) {
        if (val > 20) {
          this.$set(this.chartExplain.explains.pie, 'dataDisplay', 20)
          return
        }
        this.$set(this.chartExplain.explains.pie, 'dataDisplay', val)
      }
    },
    // 数据显示条数 是否开启
    showDataDisplay: {
      get() {
        return `${this.chartExplain.explains.pie.showDataDisplay}`
      },
      set(val) {
        this.$set(this.chartExplain.explains.pie, 'showDataDisplay', val)
      }
    }
  },

  watch: {
    dataLabel: {
      handler(val) {
        const { showValue, showPercent } = val
        if (showValue && showPercent) {
          this.$set(this.chartExplain.explains, 'dataLabelFlag', 4)
        }
        if (showValue && !showPercent) {
          this.$set(this.chartExplain.explains, 'dataLabelFlag', 2)
        }
        if (!showValue && showPercent) {
          this.$set(this.chartExplain.explains, 'dataLabelFlag', 3)
        }
        if (!showValue && !showPercent) {
          this.$set(this.chartExplain.explains, 'dataLabelFlag', 0)
        }
      }
    }
  },

  created() {},
  mounted() {},

  methods: {
    nowSelect(activeName) {
      this.activeName = activeName
    },
    dataDisplayClick(e) {
      e.preventDefault()
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-setting {
  .setting-box {
    margin-top: 24px;
    .setting-box_input {
      width: 92px;
    }
  }
  .data-display {
    display: flex;
    align-items: center;
    .setting-box_text {
      margin-right: 22px;
    }
    .setting-box_input {
      margin-right: 13px;
    }
  }
  h3 {
    margin-bottom: 10px;
    font-weight: bold;
    color: $text-main;
  }
  .label-box {
    display: flex;
    flex-direction: column;
    .attr__title {
      margin-bottom: 12px;
    }
  }
}
</style>
