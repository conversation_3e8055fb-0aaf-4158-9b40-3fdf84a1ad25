<!-- eslint vue/no-mutating-props: 1 -->

<template>
  <div class="view-wrapper">
    <h3>
      {{ $t('business.VisibleRange') }}
      <el-popover
        :content="$t('business.VisibleRangeInfo')"
        popper-class="field-memo__popper"
        trigger="hover"
        width="200"
      >
        <i slot="reference" class="el-icon-question memo-tooltip"></i>
      </el-popover>
    </h3>
    <div>
      <el-select
        v-model="rangeValue"
        class="block-select"
        clearable
        :placeholder="$t('multiUnit.pleaseChoose')"
        :popper-append-to-body="false"
      >
        <el-option
          v-for="item in rangeOption"
          :key="item.value"
          class="select-option"
          :label="item.label"
          :style="item.style"
          :value="item.value"
        >
          <div class="select-item">
            <p class="item-label">
              <span>{{ item.label }}</span>
              <span v-if="item.newIcon" class="icon"> new </span>
            </p>
            <p class="item-tip">
              {{ item.tip }}
            </p>
          </div>
        </el-option>
      </el-select>
    </div>
    <div v-if="rangeValue === 'B'" class="right-second__head">
      <h3>
        {{ $t('chartEdit.belong') }}
      </h3>
      <tree-select
        ref="tree"
        v-model="fieldValue"
        :is-belong-able="true"
        :options="options"
        :props="props"
        :single="single"
        :view-control="true"
      >
      </tree-select>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import TreeSelect from '@/components/select-tree/tree-select'

export default {
  name: 'ViewControl',

  components: {
    TreeSelect
  },
  inject: ['templateDataPermissionArray'],

  props: {
    fieldArr: {
      type: Array,
      default: () => []
    },
    // 权限对象
    permissions: {
      type: Object,
      default: () => ({})
    },
    single: {
      type: String,
      default: '1'
    }
  },

  data() {
    return {
      rangeValue: 'A', // 可见数据范围
      fieldValue: {}, // 已选字段
      options: [],
      props: {
        children: 'fieldList',
        label: 'name',
        parent: 'appId',
        value: 'formId'
      }
    }
  },

  computed: {
    rangeOption() {
      return [
        {
          value: 'A',
          label: this.$t('chartManagement.allDataPermission'),
          newIcon: false,
          tip: '',
          style: {}
        },
        {
          value: 'B',
          label: this.$t('chartManagement.dataFormUserPermission'),
          newIcon: false,
          tip: '',
          style: {}
        },
        {
          value: 'C',
          label: this.$t('chartManagement.dataFormPermission'),
          newIcon: true,
          tip: this.$t('chartManagement.dataFormPermissionTip'),
          style: {
            height: 'auto',
            width: '268px',
            whiteSpace: 'normal',
            boxSizing: 'border-box',
            lineHeight: 'normal',
            display: this.templateDataPermissionArray ? 'block' : 'none'
          }
        }
      ]
    }
  },

  watch: {
    fieldArr: {
      handler(val) {
        if (val) {
          this.options = JSON.parse(JSON.stringify(val))
          this.formatData(this.options)
        }
      },
      immediate: true
    },
    rangeValue: {
      // 每次改变后应该清空
      handler(val, oldVal) {
        if (val === 'C') {
          delete this.permissions.dataPermissionAttr
          this.$set(this.permissions, 'dataPermissionArray', 1)
        } else {
          delete this.permissions.dataPermissionArray
          if (val === 'A') {
            this.$set(this.permissions, 'dataPermissionAttr', {})
          }
          this.fieldValue = this.permissions.dataPermissionAttr || {}
        }
      }
    },
    fieldValue: {
      handler(val) {
        if (!val || val.length === 0) {
          this.$set(this.permissions, 'dataPermissionArray', 1)
        } else {
          this.$set(this.permissions, 'dataPermissionAttr', val)
        }
      }
    }
  },

  mounted() {
    // 回显赋值

    if (this.permissions.dataPermissionArray) {
      this.rangeValue = 'C'
    } else if (
      this.permissions?.dataPermissionAttr &&
      Object.keys(this.permissions?.dataPermissionAttr)?.length
    ) {
      this.rangeValue = 'B'
    } else {
      this.rangeValue = 'A'
    }
  },

  methods: {
    formatData(list = []) {
      list.forEach((item) => {
        item.id = item.formId ? String(item.formId) : ''
        item.parentId = 0
        item.allowSelect = false
        if (item.fieldList) {
          item.fieldList.forEach((child, index) => {
            child.id = child.attr
            child.parentId = Number(this.single) === 2 ? 0 : item.id
            child.formId = Number(this.single) === 2 ? child.formId : item.formId
            child.name = child.attrName
            child.allowSelect = true
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.view-wrapper {
  width: 100%;
  h3 {
    margin-bottom: 10px;
    color: $text-main;
    .memo-tooltip {
      color: $text-tip;
      .memo-tooltip:hover {
        color: $brand-color-5;
      }
    }
  }
  h3:first-child {
    font-weight: bolder;
  }
  :deep(.block-select) {
    width: 100%;
    .select-item {
      padding: 5px 0;
      .item-label {
        line-height: 22px;
        .icon {
          display: inline-block;
          width: 34px;
          height: 16px;
          font-size: 10px;
          line-height: 14px;
          color: $base-white;
          text-align: center;
          vertical-align: text-top;
          background: $error-color-5;
          border-radius: 8px;
        }
      }
      .item-tip {
        padding-top: 2px;
        font-size: 12px;
        line-height: 16px;
        color: $text-auxiliary;
      }
    }
  }
  .right-second__head {
    margin-top: 12px;
  }
}
</style>
