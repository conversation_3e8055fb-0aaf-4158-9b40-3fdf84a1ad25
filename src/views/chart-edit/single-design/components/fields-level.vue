<template>
  <div class="field-level">
    <details class="details" open>
      <summary>
        <i class="level-icon web-icon-wenjianjia- web-iconfont"></i>
        {{ $t('display.chartCustom.hierarchicalStructure') }}
        <i class="el-icon-caret-right triangle"></i>
        <!--          class="float-right web-icon-questMark web-iconfont"-->
        <span class="level-header__right">
          <i
            class="icon-quest-mark web-icon-questMark web-iconfont"
            @click.stop="() => (nodeSpecificationShow = true)"
          ></i>
          <span class="create-level-btn" @click="handleAddLevel"
            >+{{ $t('chartManagement.add') }}</span
          >
        </span>
      </summary>
    </details>
    <div class="wrapper-list">
      <template v-for="(level, levelIndex) in levelDrillShow">
        <details :key="'level' + levelIndex" class="details" open>
          <summary
            class="level-name"
            :class="{
              'hover-status':
                fieldLevelOption.show &&
                !fieldLevelOption.fieldClick &&
                fieldLevelOption.levelId === level.uuid
            }"
          >
            <i class="form-icon web-icon-bianzu24 web-iconfont"></i>
            <span v-tooltip.top="level.name" class="level-name__span">
              {{ level.name }}
            </span>
            <i class="el-icon-caret-right triangle"></i>
            <span v-if="!hideOperate" class="operation-box">
              <i
                :ref="`bt-${level.uuid}-${levelIndex}`"
                class="el-icon-more"
                @click="(e) => levelHeadClick(e, level, levelIndex)"
              ></i>
            </span>
          </summary>
        </details>
        <div v-if="level.field.length" :key="'levelList' + levelIndex" class="wrapper-list">
          <draggable
            v-model="level.field"
            v-bind="dragOptions"
            :clone="cloneDog"
            handle=".handler-item"
            :sort="false"
            @add="({ newIndex }) => onAdd(level.field, newIndex)"
            @end="end"
            @start="(e) => start(e, level.field)"
          >
            <div
              v-for="(item, index) in level.field"
              :key="'levelField' + index"
              class="field-item handler-item"
              :class="{
                'hover-status':
                  fieldLevelOption.show &&
                  fieldLevelOption.levelId === level.uuid &&
                  fieldLevelOption.index === index
              }"
            >
              <!-- 字段类型图标 -->
              <span class="inline-icon">
                <i class="web-iconfont" :class="item | fieldAttrIcon"></i>
              </span>
              <!-- 字段名称 -->
              <span class="inner-text">
                <span v-if="!hideOperate" class="level"
                  >{{ index + 1 }}{{ $t('chartManagement.lev') }}</span
                >
                <!-- 数据集做数据源时的字段名称展示 -->
                <span
                  v-if="isDataSetDataSource"
                  v-tooltip.top-start="`${item.attrName}${extraText(item)}`"
                >
                  {{ dataSetFieldAttrName(item) }}
                </span>
                <span
                  v-else
                  v-tooltip.top-start="
                    `${getFormName(item.formId)}--${item.attrName}${extraText(item)}`
                  "
                  >{{ getFormName(item.formId) }}--{{ item.attrName }}</span
                >
                <span v-if="extraText(item)" class="extra-text">{{ extraText(item, false) }}</span>
              </span>
              <div v-if="!hideOperate" class="operation-box">
                <!-- 排序 -->
                <i
                  v-if="index !== 0"
                  v-tooltip="$t('display.chartCustom.up')"
                  class="el-icon-top"
                  @click.stop="levelMove('up', levelIndex, index)"
                ></i>
                <i
                  v-if="index !== level.field.length - 1"
                  v-tooltip="$t('display.chartCustom.down')"
                  class="el-icon-bottom"
                  @click.stop="levelMove('down', levelIndex, index)"
                ></i>
                <!-- 非数字字段的层级结构设置 -->
                <i
                  :ref="`bt-${level.uuid}-${item.formId}-${
                    item.originalAttr || item.attr
                  }-${index}`"
                  class="el-icon-more"
                  @click.stop="(e) => levelFieldClick(level, item, index, levelIndex)"
                ></i>
              </div>
            </div>
          </draggable>
        </div>
      </template>
    </div>
    <!-- 层级结构设置 -->
    <el-popover
      v-if="showPop"
      ref="field-level-pop"
      placement="right"
      popper-class="field-level-popover"
      :reference="reference"
      trigger="click"
      :width="103"
    >
      <div>
        <field-level-setting
          :chart-explain="chartExplain"
          :drill-special-field="drillSpecialField"
          :level-drill="levelDrill"
          :option="fieldLevelOption"
          :use-in-level="false"
          @close-pop="closePop"
          @level-setting="levelSetting"
        ></field-level-setting>
      </div>
    </el-popover>

    <!-- 层级结构说明 -->
    <node-specification :show.sync="nodeSpecificationShow" type="fieldsLevel"></node-specification>
  </div>
</template>

<script>
import draggable from 'vuedraggable'

import bus from '@/utils/temp-vue'
import { fieldAttrMap, fieldAttrMapBi } from '@/views/edit/new-form-design/fields-design-map.js'
import fieldLevelSetting from './fields-level-setting.vue'

const { complexList, systemList } = fieldAttrMap // 结构字典
const { biBaseList } = fieldAttrMapBi // 结构字典，阶段推进器这次需求导致Bi的基础字段列表与表单共用的基础字段列表存在差异，新建一份来维护
import textFormat from '@/views/chart-edit/mixins/text-format.js'
import nodeSpecification from '@/views/application-manage/data-set/edit/components/node-data-set/components/node-specification.vue'
import dataSetFieldMixin from '../js/dataset-field-mixin'

export default {
  components: {
    Draggable: draggable,
    FieldLevelSetting: fieldLevelSetting,
    NodeSpecification: nodeSpecification
  },
  filters: {
    fieldAttrIcon(item) {
      let icon = 'web-icon-field-text'
      const arr = [...biBaseList, ...complexList, ...systemList]
      arr.forEach((i) => {
        if (i.fieldType === item.fieldType) {
          icon = i.icon
        }
      })
      return icon
    }
  },
  mixins: [textFormat, dataSetFieldMixin],
  props: {
    chartExplain: {
      type: Object,
      default: () => ({})
    },
    // 层级结构（原数据）
    levelDrill: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 层级结构（用于展示、查询）
    levelDrillShow: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 隐藏交互按钮
    hideOperate: {
      type: Boolean,
      default: false
    },
    formNameMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 层级结构中特殊的字段
    drillSpecialField: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      reference: {},
      activeId: '',
      showPop: false,
      dragOptions: {
        group: {
          name: 'fields',
          pull: 'clone'
        },
        put: false,
        forceFallback: true,
        dragClass: 'ghost-item',
        animation: 150
      },
      // 字段层级设置的下拉配置
      fieldLevelOption: {},
      nodeSpecificationShow: false // 说明弹窗显示
    }
  },
  methods: {
    handleAddLevel(e) {
      e.preventDefault()
      this.emitOpenLevelSettingDialog({
        type: 'create',
        currentLevelInfo: {
          name: '',
          field: [],
          uuid: ''
        }
      })
    },
    emitOpenLevelSettingDialog(payload) {
      bus.$emit('open-level-setting-dialog', payload)
    },

    onAdd(field, newIndex) {
      field.splice(newIndex, 1)
    },
    start({ oldIndex }, list) {
      bus.$emit('drag-on', list[oldIndex])
    },
    end() {
      bus.$emit('drag-on', false)
    },
    // 从左侧克隆到右侧 以确保联动性
    cloneDog(field) {
      return field
    },
    fieldLevelSetting(e, item, index, levelId, levelSetting = false) {
      this.fieldLevelOption = {
        show: true,
        levelSetting: levelSetting, // 是否是层级结构的设置
        event: e,
        field: item,
        index: index,
        levelId: levelId // 区别于数据源字段中的option，在层级结构中是可以获取到当前操作字段所在的层级结构id的
      }
    },
    levelMove(type, levelIndex, index) {
      /* eslint-disable */
      const temp = this.levelDrill[levelIndex].field[index]
      this.levelDrill[levelIndex].field.splice(index, 1)
      this.levelDrill[levelIndex].field.splice(index + (type === 'up' ? -1 : 1), 0, temp)
    },
    levelSetting(info) {
      this.$emit('level-setting', info)
    },
    // 获取当前字段所属表单的表单名称
    getFormName(formId) {
      // !!因为数据集数据源没有formId，且其中的字段formId会出现很多不同的情况，所以如果是数据集数据源，则数据源名称直接取map中第一个
      return +this.chartExplain.single === 2
        ? Object.values(this.formNameMap)[0]
        : this.formNameMap[formId]
    },

    /**
     * 层级标题右侧菜单栏点击
     * @param e
     * @param level
     * @param levelIndex
     */
    levelHeadClick(e, level, levelIndex) {
      e.preventDefault()
      this.clickPop({
        field: level.field,
        index: -1,
        levelIndex,
        refStr: `bt-${level.uuid}-${levelIndex}`,
        levelClick: true,
        fieldClick: false,
        levelUUid: level.uuid
      })
    },
    levelFieldClick(level, item, index, levelIndex) {
      this.clickPop({
        field: item,
        index,
        levelIndex,
        refStr: `bt-${level.uuid}-${item.formId}-${item.originalAttr || item.attr}-${index}`,
        levelClick: true,
        fieldClick: true,
        levelUUid: level.uuid
      })
    },
    clickPop({ field, index, levelIndex, refStr, levelClick, fieldClick, levelUUid }) {
      this.fieldLevelOption = {
        show: true,
        field,
        index: index,
        levelSetting: levelClick,
        levelId: levelUUid,
        fieldClick,
        levelIndex,
        levelClick
      }
      // 这个操作是为了避免与源码中的点击reference doToggle方法冲突
      if (this.activeId === refStr && this.showPop) return
      this.showPop = false
      this.activeId = refStr
      // 因为reference是需要获取dom的引用 所以需要是$el
      this.reference = this.$refs[refStr][0]
      this.$nextTick(() => {
        // 等待显示的popover销毁后再 重新渲染新的popover
        this.showPop = true
        this.$nextTick(() => {
          // 此时才能获取refs引用
          this.$refs['field-level-pop'].doShow()
        })
      })
    },

    closePop() {
      this.$nextTick(() => {
        // 此时才能获取refs引用
        this.$refs['field-level-pop'].doClose()
      })
      this.activeId = ''
    }
  }
}
</script>

<style lang="scss" scoped>
/* 具体样式配置 */
.details {
  padding: 3px 0;
}
.details summary {
  position: relative;
  line-height: 22px;
  user-select: none;
  outline: 0;
  .float-right {
    position: absolute;
    right: 12px;
    color: $brand-color-5;
    cursor: pointer;
    opacity: 0;
  }
}
summary::marker {
  content: '';
}
.details + .wrapper-list {
  height: 0px;
  overflow-x: hidden;
  transition: all 0.25s;
}
.wrapper-list {
  box-sizing: border-box;
  // margin: 5px 0;
  padding-left: 15px;
  transition: all 0.25s;
}
.form-icon {
  margin: 0 7px 0 16px;
  font-size: 12px;
  color: $link-base-color-6;
}
.level-icon {
  margin: 0 5px 0 14px;
  font-size: 15px;
  color: $link-base-color-6;
}
.details .triangle {
  position: absolute;
  top: 5px;
  left: -2px;
  color: #999999;
  background: $base-white;
  transition: transform 0.25s;
}
[open] + .wrapper-list {
  height: auto;
  max-height: 650px;
  transition: all 0.25s;
}
[open] .triangle {
  transition: transform 0.25s;
  transform: rotate(90deg);
}
[open] + .normal-height {
  height: 250px !important;
  max-height: 250px;
}
[open] + .chang-height {
  height: 650px !important;
  max-height: 650px;
}
.inline-icon {
  display: inline-block;
  width: 20px;
  vertical-align: text-top;
  i {
    width: 12px;
    margin-right: 5px;
    font-size: 12px;
    color: $link-base-color-6;
  }
}
.wrapper-list .field-item {
  position: relative;
  padding: 7px 0px 7px 2px;
  overflow: hidden;
  cursor: move;
  user-select: none;
  border: 0;
  i {
    width: 12px;
    margin-right: 5px;
    font-size: 12px;
    color: $link-base-color-6;
  }
}
.disable-item {
  color: $text-grey;
  cursor: not-allowed;
}
.level-name {
  display: flex;
  .form-icon {
    margin-left: 18px;
  }
  .triangle {
    left: 0px;
  }
  .level-name__span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.level-name:hover {
  padding-right: 5px;
  background: $warning-color-1;
  .triangle {
    background: $warning-color-1;
  }
  .level-name__span {
    margin-right: 13px;
  }
}
.level-name:hover,
.field-item:hover {
  .operation-box {
    display: inline-block;
    i {
      color: $text-grey;
    }
  }
}
.operation-box {
  position: absolute;
  right: 10px;
  display: none;
  float: right;
  cursor: pointer;
  i {
    color: $text-grey;
  }
  i:hover {
    color: $brand-color-5 !important;
  }
  //i {
  //  color: $brand-color-5 !important;
  //}
}
.wrapper-list .field-item:hover {
  //color: $brand-color-5;
  background: $warning-color-1;
  .inner-text {
    width: 65%;
  }
}
/* 基础样式配置 */
.wrapper {
  text-align: left;
}
.details ::-webkit-details-marker {
  display: none;
}
.details ::-moz-list-bullet {
  font-size: 0;
}
.wrapper-list {
  box-sizing: border-box;
  // margin: 5px 0;
  transition: all 0.25s;
}
.inner-text {
  display: inline-block;
  width: 90%;
  overflow: hidden;
  color: #666666;
  text-overflow: ellipsis;
  white-space: nowrap;
  .level {
    display: inline-block;
    margin-right: 6px;
    color: $link-base-color-6;
  }
  .extra-text {
    box-sizing: border-box;
    display: inline-block;
    padding: 2px 6px;
    font-size: 12px;
    color: $link-color-5;
    background-color: $link-color-1;
    border-radius: 2px;
  }
}
.ghost-item {
  z-index: 1;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  float: left;
  width: 120px !important;
  padding-left: 9px;
  margin-bottom: 5px;
  font-size: 12px;
  line-height: 26px;
  color: $brand-color-5;
  background-color: rgba(255, 142, 61, 0.15);
  border-radius: 13px;
  i {
    display: none;
  }
}
.extra-text {
  box-sizing: border-box;
  padding: 2px 6px;
  font-size: 12px;
  color: $link-color-5;
  background-color: $link-color-1;
  border-radius: 2px;
}
.hover-status {
  //background: $warning-color-1;
  .triangle {
    //background: $warning-color-1;
  }
}
.field-level {
  //position: relative;
  .float-right {
    position: absolute;
    top: 10px;
    right: 12px;
    z-index: 100;
    color: $brand-color-5;
    cursor: pointer;
  }
}

.level-header__right {
  display: inline-block;
  //background: $link-color-8;
  float: right;
  color: $brand-color-5;
  i {
    font-size: 14px;
    line-height: 22px;
    vertical-align: middle;
  }

  .create-level-btn {
    line-height: 22px;
    vertical-align: middle;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.field-level-popover {
  min-width: 103px !important;
  padding: 4px 0;
  border-radius: 4px;
}
</style>
