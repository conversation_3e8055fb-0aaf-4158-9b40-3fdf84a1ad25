<!--
 * @Description: 单个图表编辑预览
-->
<template>
  <div class="single-chart-design">
    <div class="header-top">
      <div class="header">
        <div class="header__icon" @click="backChartCenter">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="header__title">
          <el-tooltip
            :content="$t('chartEdit.clickRename')"
            :disabled="showLang"
            effect="dark"
            placement="bottom"
          >
            <el-input
              ref="chartName"
              v-model="copyChartExplain.name"
              class="header__input header__text"
              minlength="1"
              @input="inputChange"
            >
            </el-input>
          </el-tooltip>
        </div>
        <div v-if="showLang" class="header__rename">
          <FiledChoose
            ref="langSetPopover"
            :form-name="copyChartExplain"
            :re-chart-name="true"
          ></FiledChoose>
        </div>
        <div class="header__save">
          <el-button class="main-head__btn" type="primary" @click="saveChart"
            >{{ $t('operation.save') }}
          </el-button>
        </div>
      </div>
    </div>
    <el-container v-if="designContentShow" class="design-container">
      <el-aside width="300px">
        <single-left
          ref="singleLeft"
          v-loading="leftLoading"
          :chart-explain="copyChartExplain"
          :chart-or-table="chartOrTable"
          :compute-field-permission="computeFieldPermission"
          :current-selected-subform-map="currentSelectedSubFormMap"
          :drill-show="drillPermission"
          :drill-special-field="drillSpecialField"
          :enable-change-sp-group-type="enableChangeSpGroupType"
          :level-drill="levelDrill"
          :ready-fields="readyFields"
          :sp-group-type="spGroupType"
          @open-calculate-field="openCalculateField"
          @open-data-source="openDataSource"
          @sp-group-type-change="spGroupTypeChange"
          @update-level-drill="updateLevelDrill"
        >
        </single-left>
      </el-aside>
      <el-main>
        <single-main
          ref="single-main"
          :chart-explain="copyChartExplain"
          :chart-or-table="chartOrTable"
          :global-style-option="chartOption.globalStyleOption"
          :level-drill="levelDrill"
          :sp-group-type="spGroupType"
          :table-type="copyChartExplain.chartType == 1 ? 'queryTable' : 'statisticsTable'"
          :work-order-version-id="workOrderVersionId"
          v-bind="{
            displayAttrList,
            xGroupAttrList,
            yGroupAttrList,
            summaryAttrList,
            statisticsAttrList,
            queryAttrList,
            penetrateList,
            classList,
            seriesList,
            valueList,
            filterList,
            belongList,
            timeList,
            addressList,
            singeSelectList
          }"
        ></single-main>
      </el-main>
      <el-aside width="300px">
        <single-right
          :belong-list="belongList"
          :chart-explain="copyChartExplain"
          :chart-or-table="chartOrTable"
          :data-warning-permissions="dataWarningPermissions"
          :form-list="formIdBusinessTypeList"
          :global-style-option="chartOption.globalStyleOption"
          :item-style-option="copyChartExplain.itemStyleOption"
          @chart-type-change="chartTypeChange"
        ></single-right>
      </el-aside>
    </el-container>

    <single-formula
      v-if="formulaShow"
      ref="singleFormula"
      :chart-explain="copyChartExplain"
      :computed-field-list="computedFieldList"
      :current-formula="currentFormula"
      :formula-info="formulaInfo"
      :show.sync="formulaShow"
      @add-calculate-field="addCalculateField"
    ></single-formula>
  </div>
</template>

<script>
import { computed } from 'vue'

import SingleLeft from './single-design/single-left.vue'
import SingleMain from './single-design/single-main.vue'
import SingleRight from './single-design/single-right.vue'
import SingleFormula from './single-design/single-formula.vue'
import FiledChoose from '@/components/field-choose/index.vue'

import { getdataSetAttr, getDataSourceAttr, saveChart } from '@/api/statistics'
import { BooleanToNumber } from '@/utils/chart-design.js'
import { getDataSetNumberAttr, getNumberAttr } from '@/api/panel-design.js'

import panelStyleMixin from '@/views/chart-edit/mixins/panel-style-mixin.js'
import {
  DEFAULT_FUNNEL_ITEM_STYLE,
  DEFAULT_ITEM_STYLE
} from '@/views/chart-edit/ui-design/config/theme-color.js'

import biStageMixin from './mixins/bi-stage-methods-mixins'
import xbb from '@xbb/xbb-utils'
import { BI_CHART_TYPE } from '@/constants/common/bi-chart'

export default {
  name: 'SingleChartDesign',

  components: {
    SingleLeft,
    SingleMain,
    SingleRight,
    SingleFormula,
    FiledChoose
  },

  mixins: [panelStyleMixin, biStageMixin],

  props: {
    chartOption: {
      type: Object,
      default: () => ({})
    },
    chartExplain: {
      type: Object,
      default: () => ({})
    },
    // 单维度多指标的功能权限
    singleDimensionPermissions: {
      type: Boolean,
      default: false
    },

    // 计算字段的功能权限
    computeFieldPermission: {
      type: Boolean,
      default: false
    },

    // 是否开启国际化
    showLang: {
      type: Boolean,
      default: false
    },
    // 工单版本id
    workOrderVersionId: {
      type: [Number, String],
      default: ''
    },
    currentSelectedSubFormMap: {
      type: Object,
      default: () => ({})
    }
  },

  provide() {
    return {
      templateDataPermissionArray: computed(() => {
        if (!this.ckFlag) {
          return 0
        }

        if (this.dataSourceType === '2') {
          return this.templateDataPermissionArray
        }

        return 1
      })
    }
  },

  data() {
    return {
      // 表头字段列表
      displayAttrList: [],
      // 分组字段列表
      xGroupAttrList: [],
      yGroupAttrList: [],
      // 汇总字段列表
      summaryAttrList: [],
      // 统计汇总字段列表
      statisticsAttrList: [],
      // 查询汇总字段列表
      queryAttrList: [],
      // 穿透表头字段列表
      penetrateList: [],
      // 分类选项列表
      classList: [],
      // 系列选项列表
      seriesList: [],
      // 值选项列表
      valueList: [],
      // 筛选条件列表
      filterList: [],
      // 备选字段
      readyFields: [],
      // 归属人可选字段
      belongList: [],
      // 统计时间可选字段
      timeList: [],
      // 地理维度可选字段
      addressList: [],
      // 下拉框、单选框类型的字段
      singeSelectList: [],
      // 公式字段弹窗
      formulaShow: false,
      formulaInfo: {},
      currentFormulaIndex: undefined,
      currentFormula: {},
      // 单表数据复制
      copyChartExplain: {},
      computedFieldList: [],
      formIdBusinessTypeList: {},
      // 左侧数据来源与字段的loading
      leftLoading: false,
      // 判断当前套餐是否旗舰版
      dataWarningPermissions: false,
      // 复制图表ID
      copyId: '',
      designContentShow: true, // 图表设计功能模块的显隐（用于切换数据源时，刷新整个组件）
      drillFlag: false, // 图表是否支持钻取功能
      levelDrill: [], // 层级结构（钻取）
      drillSpecialField: {}, //层级结构中特殊的字段
      linkSourceList: [],
      templateDataPermissionArray: 0 // 模板数据权限
    }
  },

  computed: {
    // 带产品规格字段的表的 产品规格字段的分组模式
    spGroupType() {
      return this.copyChartExplain.explains.spType || 0
    },

    // 图表类型
    chartType() {
      return this.chartExplain.chartType
    },

    // 是否为ck环境
    ckFlag() {
      return utils.SS.get('biCkFlag') === 'true'
    },
    chartOrTable() {
      let value = ''
      switch (this.copyChartExplain.chartType) {
        // 表格
        case 1:
        case 2:
        case BI_CHART_TYPE.STATISTICAL_TABLE_LITE:
          value = 'table-setting'
          break
        // 指标图
        case 21:
        case 24:
          value = 'quota-setting'
          break
        // !! 地图属于基础图表之外的特殊图表，区别于graph-setting（基本图表可以通过改变type直接改变图表的渲染）
        // 地图
        case 25:
          value = 'map-graph-setting'
          break
        // 基本图表
        default:
          value = 'graph-setting'
          break
      }
      return value
    },
    dataSourceType() {
      return this.chartExplain.single
    },
    dataWarningList() {
      return this.$store.state.warn.warnList
    },
    // 是否可以使用层级结构的功能
    drillPermission() {
      // 柱形图、折线图、面积图、横向柱形图、气泡图、饼图、数据统计表
      return (
        [2, 3, 4, 5, 7, 23, 28, 29, 30, 31, BI_CHART_TYPE.STATISTICAL_TABLE_LITE].includes(
          this.copyChartExplain.chartType
        ) &&
        this.drillFlag &&
        this.$feeType.checkFeatureEnable('CHART.Drill')
      )
    },
    linkSourceFieldsMap() {
      return Object.fromEntries(
        this.linkSourceList.map((item) => {
          return [item.driveLinkedAttr, item]
        })
      )
    },

    // 是否可以改变产品规格字段的分组模式
    enableChangeSpGroupType() {
      const checkKeys = [
        'chartClassify',
        'chartSeries',
        'chartValue',
        'chartValueSecond',
        'colGroupAttr',
        'rowGroupAttr',
        'displayAttr',
        'groupAttr',
        'penetrateAttr',
        'filter'
      ]
      if (this.chartType === BI_CHART_TYPE.DATA_QUERY_TABLE) {
        return false
      }

      for (const index in checkKeys) {
        const key = checkKeys[index]
        if ((this.copyChartExplain[key] || []).filter((item) => item.fieldType === 20008).length) {
          return false
        }
      }
      return true
    }
  },

  watch: {
    'copyChartExplain.driverSources.formId': {
      handler(val) {
        if (val !== '' && val !== undefined) {
          this.getDataSourceAttr()
        }
      },
      deep: true,
      immediate: true
    },
    drillPermission(val) {
      // 如果切换图表类型时：支持 -> 不支持：需要将levelDrill清空
      if (!val) {
        this.levelDrill = []
      }
    }
  },

  created() {
    this.init()
    // 若图表使用数据集作为数据源，则数据集发生变化时，此图表中的字段配置都需要清空
    this.dataSetChangeHandle()
  },

  mounted() {
    const inputContainer = this.$refs.chartName.$el
    const inputElement = this.$refs.chartName.$el.children[0]
    inputContainer.style.width = inputElement.value.length * 16 + 20 + 'px'
    if (!this.chartExplain.itemStyleOption) {
      if (this.copyChartExplain.chartType !== 26) {
        this.copyChartExplain.itemStyleOption = DEFAULT_ITEM_STYLE
      } else {
        this.copyChartExplain.itemStyleOption = DEFAULT_FUNNEL_ITEM_STYLE
      }
    }
  },

  methods: {
    /**
     * @description 产品规格字段分组模式改变
     * @param value
     */
    spGroupTypeChange(value) {
      this.$set(this.copyChartExplain.explains, 'spType', value)
    },

    updateLevelDrill(levelDrill) {
      this.levelDrill = [...levelDrill]
    },
    init() {
      this.copyChartExplain = JSON.parse(JSON.stringify(this.chartExplain))
      // 兼容老数据 默认为平底
      if (+this.copyChartExplain.chartType === 26 && !this.copyChartExplain.funnel.base) {
        this.copyChartExplain.funnel.base = 0
      }

      if (
        [BI_CHART_TYPE.STATISTICAL_TABLE_LITE, BI_CHART_TYPE.DATA_STATISTICS_TABLE].includes(
          this.copyChartExplain.chartType
        ) &&
        !this.copyChartExplain.itemStyleOption
      ) {
        this.$set(this.copyChartExplain, 'itemStyleOption', {
          userCustomColorScheme: '#EFF2F4',
          itemBackgroundMode: 0,
          itemBackground: '#FFFFFF'
        })
      }

      // 把单维度多指标权限塞到图表解释内，便于下面的子组件拿到
      this.$set(
        this.copyChartExplain,
        'singleDimensionPermissions',
        this.singleDimensionPermissions
      )
      // 数据集调试 临时关闭可用变量调用
      if (this.copyChartExplain.single === '2') {
        this.getDataSetAttr()
      } else {
        this.getAlternative()
      }
      // 气泡大小特殊处理
      if (this.copyChartExplain.chartValue.length === 2 && this.copyChartExplain.chartType === 29) {
        const { chartValue } = this.copyChartExplain
        this.$set(this.copyChartExplain, 'bubbleSize', [chartValue[1]])
        this.copyChartExplain.chartValue.splice(1, 1)
      }
      // 初始化图表钻取levelDrill
      this.levelDrill = this.copyChartExplain.drill || []
      this.designContentShow = false
      this.$nextTick(() => {
        this.designContentShow = true
      })
    },
    openDataSource(data) {
      this.$emit('open-data-source', data)
    },
    // 获取数据集的字段信息
    getDataSetAttr() {
      const request = {
        id: this.copyChartExplain.driverSources.id || this.copyChartExplain.driverSources.dataSetId,
        chartType: this.copyChartExplain.chartType
      }
      // 获取数据源
      getdataSetAttr(request).then((res) => {
        this.dealData(res)
      })
      // 获取计算字段中的可选字段
      getDataSetNumberAttr({
        formIdBusinessTypeList: {
          [request.id]: request.chartType
        }
      }).then((res) => {
        this.computedFieldHandle(res.result.numberAble)
      })
    },
    stageLogFormNameDeal(request) {
      if (this.chartExplain.driverSources.stageLogFormName) {
        return {
          ...request,
          stageLogFormName: this.chartExplain.driverSources.stageLogFormName,
          stageBusinessType: `${this.chartExplain.driverSources.businessType}`
        }
      }
      return request
    },
    // 获取可用变量
    getAlternative() {
      const formIdList = {}
      formIdList[this.chartExplain.driverSources.formId] =
        this.chartExplain.driverSources.businessType
      if (this.chartExplain.slaveSources.length) {
        this.chartExplain.slaveSources.forEach((i) => {
          formIdList[i.formId] = i.businessType
        })
      }
      this.formIdBusinessTypeList = formIdList
      let request = {
        formIdBusinessTypeList: formIdList,
        formIdSubFormAttrList: {
          ...this.currentSelectedSubFormMap
        }
      }
      Object.assign(request, { ...this.judgeOrderParam() })
      request = this.stageLogFormNameDeal(request)
      getNumberAttr(request).then((res) => {
        this.computedFieldHandle(res.result.numberAble)
      })
    },
    // 计算字段可选字段列表的处理
    computedFieldHandle(fieldList) {
      const date = []
      const num = []
      const other = []
      fieldList.forEach((item) => {
        for (const i in item.groupFiledMap) {
          item.groupFiledMap[i].forEach((i) => {
            i.label = item.name + '.' + i.attrName
            i.formId = item.formId
          })
          if (i === 'date') {
            date.push(...item.groupFiledMap[i])
          } else if (i === 'num') {
            num.push(...item.groupFiledMap[i])
          } else if (i === 'other') {
            other.push(...item.groupFiledMap[i])
          }
        }
      })
      const arr = [
        {
          label: '时间戳',
          children: date
        },
        {
          label: '数字',
          children: num
        },
        {
          label: '其他',
          children: other
        }
      ]
      this.computedFieldList = arr
    },
    // 编辑、新建计算字段
    openCalculateField(item, index) {
      this.formulaShow = true
      if (index === undefined) {
        this.formulaInfo = {}
        this.currentFormula = {}
      } else {
        this.formulaInfo = item.formulaInfo
        if (!this.formulaInfo.showFormula) this.formulaInfo.showFormula = ''
        this.currentFormula = item
      }
      this.currentFormulaIndex = index
    },
    addCalculateField(fmlInfo, name, numTemp) {
      const obj = Object.assign({}, numTemp)

      if (fmlInfo.hasSubformAttr) {
        obj.subFormAttr = fmlInfo.subFormAttr
      }

      obj.label = name
      obj.labelName = name
      obj.formulaInfo = fmlInfo
      obj.fieldType = 20 /* 20代表为计算字段 */

      const arr = fmlInfo.formula.match(/[0-9]+\.[a-z]+\w+/g)
      // 这里要解析为后端需要的格式
      obj.formulaInfo.fields = arr?.reduce((prev, cur) => {
        if (prev[cur.split('.')[0]]) {
          prev[cur.split('.')[0]].push(cur.split('.')[1])
        } else {
          prev[cur.split('.')[0]] = [cur.split('.')[1]]
        }
        return prev
      }, {})

      if (this.copyChartExplain.computedAttr === undefined) {
        this.$set(this.copyChartExplain, 'computedAttr', [])
      }

      if (
        this.currentFormulaIndex === undefined &&
        this.copyChartExplain.computedAttr.length === 10
      ) {
        this.$message({
          type: 'warning',
          message: this.$t('fmlOther.tooLong')
        })
        return
      }

      if (this.currentFormulaIndex === undefined) {
        this.copyChartExplain.computedAttr.push(obj)
      } else {
        this.$set(this.copyChartExplain.computedAttr, this.currentFormulaIndex, obj)
      }
      this.copyChartExplain.computedAttr.forEach((i, index) => {
        i.attr = `computedField_${index + 1}`
      })

      this.formulaShow = false
    },
    // 获取表单来源字段
    getDataSourceAttr() {
      const request = {
        chartType: this.copyChartExplain.chartType,
        single: this.copyChartExplain.single,
        driverSources: this.copyChartExplain.driverSources,
        slaveSources: this.copyChartExplain.slaveSources,
        selectSubFormMap: {
          ...this.currentSelectedSubFormMap
        }
      }

      Object.assign(request, { ...this.judgeOrderParam() })

      this.leftLoading = true
      getDataSourceAttr(request)
        .then((res) => {
          this.dataWarningPermissions = res.result.dataWarningPermissions
          this.dealData(res)
        })
        .catch(() => {})
        .finally(() => {
          this.leftLoading = false
        })
    },
    /**
     * @description: 工单的自定义表单，选择版本后，接口需要传特定标识
     * @return {Object} 特定标识对象
     */
    judgeOrderParam() {
      const param = {}
      if (xbb._get(this, 'copyChartExplain.filter[0].attr') === 'versionId') {
        param.specialField = 'versionId'
        this.$emit('get-version-id', xbb._get(this, 'copyChartExplain.filter[0].value[0]'))
      } else if (xbb._get(this, 'copyChartExplain.workOrderVersion')) {
        param.specialField = 'versionId'
        this.$emit('get-version-id', xbb._get(this, 'copyChartExplain.workOrderVersion'))
      }
      return param
    },
    dealData(res) {
      if (this.chartOrTable === 'table-setting') {
        this.displayAttrList = res.result.showAble // 表头字段
        this.xGroupAttrList = res.result.groupAble // 行分组字段
        this.yGroupAttrList = res.result.groupAble // 列分组字段
        this.filterList = res.result.selectAble // 筛选字段
        this.penetrateList = res.result.showAble // 穿透表头字段
        this.statisticsAttrList = res.result.summaryAble // 统计表汇总字段
        this.queryAttrList = res.result.querySummaryAble // 查询表汇总字段
        this.timeList = res.result.timeAble // 统计时间选择（指标图）
        this.transformTree(this.displayAttrList)
        this.transformTree(this.xGroupAttrList)
        this.transformTree(this.yGroupAttrList)
        this.transformTree(this.filterList)
        this.transformTree(this.penetrateList)
        this.transformTree(this.statisticsAttrList)
        this.transformTree(this.queryAttrList)
      } else {
        // 分类、系列、值、穿透表头字段、筛选条件可选项赋值
        this.classList = res.result.groupAble // 分类
        this.seriesList = res.result.groupAble // 系列
        this.filterList = res.result.selectAble // 筛选条件
        this.penetrateList = res.result.showAble // 穿透表头字段
        this.summaryAttrList = res.result.summaryAble // 指标
        this.valueList = res.result.summaryAble // 值
        this.timeList = res.result.timeAble // 统计时间选择（指标图）
        this.addressList = res.result.addressAble // 地理维度字段
        this.singeSelectList = res.result.funnelAble // 过滤的下拉框、单选框类型的字段
        this.transformTree(this.classList)
        this.transformTree(this.seriesList)
        this.transformTree(this.filterList)
        this.transformTree(this.penetrateList)
        this.transformTree(this.summaryAttrList)
        this.transformTree(this.valueList)
      }
      this.belongList = res.result.belongAble // 归属人选择（指标图）
      this.readyFields = res.result.dashboardAble
      this.drillSpecialField = res.result.specialField // 钻取：层级结构中特殊的字段
      this.drillFlag = res.result.drillFlag // 钻取：图表是否支持钻取功能

      this.templateDataPermissionArray = res.result.dataPermissionArray ? 1 : 0

      // 单表做额外关联数据处理
      if (this.chartExplain.single === '1') {
        this.linkSourceList = res.result.slaveSources
        this.copyChartExplain.slaveSources = this.copyChartExplain.slaveSources.map((item) => {
          return {
            ...this.linkSourceFieldsMap[item.driveLinkedAttr]
          }
        })
      }
    },
    // 转换为树形结构需要的参数
    transformTree(list) {
      list.forEach((item) => {
        item.id = item.formId ? String(item.formId) : ''
        item.parentId = 0
        item.allowSelect = false
        item.fieldList.forEach((child) => {
          child.id = child.attr
          child.parentId = item.id
          child.formId = this.chartExplain.single === '2' ? child.formId : item.formId
          child.name = child.attrName
          child.allowSelect = true
        })
      })
    },
    // 保存前的特殊逻辑判断
    specialJudge() {
      let limit = false
      const chart = this.copyChartExplain
      switch (chart.chartType) {
        // 数据查询表
        case 1:
          break
        // 数据统计表
        case 2:
          // 选择行列字段时，必须选择汇总字段
          if (
            chart.colGroupAttr.length > 0 &&
            chart.rowGroupAttr.length > 0 &&
            chart.summaryAttr.length === 0
          ) {
            limit = true
            this.$message({
              type: 'warning',
              message: this.$t('display.dashboard.plsSelectSum')
            })
          }
          // 请设置行分组字段或列分组字段
          if (chart.colGroupAttr.length === 0 && chart.rowGroupAttr.length === 0) {
            limit = true
            this.$message({
              type: 'warning',
              message: this.$t('display.dashboard.plsAddColOrRow')
            })
          }
          // 如果汇总字段内多个字段，判断是否有完全相同的字段
          if (chart.summaryAttr.length > 1) {
            const checkMap = {}
            chart.summaryAttr.forEach((item) => {
              // 完全相同的判断逻辑： 字段所属表单id + 字段attr + 汇总方式 + 高级计算
              const specialString = `${item.formId}-${item.originalAttr || item.attr}-${
                item.aggType
              }-${item.advancedComputing}`
              if (checkMap[specialString]) {
                limit = true
              } else {
                checkMap[specialString] = true
              }
            })
            limit &&
              this.$message({
                type: 'warning',
                message: this.$t('display.dashboard.summaryCantSame')
              })
          }
          break
        // 双轴图
        case 23:
          // 如果左侧右侧统计值内，出现一样的显示名（避免图例上的错误合并问题），则提示需要修改后再保存
          const nameMap = {}
          let sameLabel = ''
          chart.chartValue.forEach((a) => {
            nameMap[a.labelName] = true
          })
          chart.chartValueSecond.forEach((b) => {
            if (nameMap[b.labelName]) {
              sameLabel = b.labelName
              limit = true
              this.$message({
                type: 'warning',
                message: this.$t('display.dashboard.sameDisplayName', { sameLabel: sameLabel })
              })
            }
          })
          break

        default:
          break
      }
      return limit
    },
    /**
     * @description: 工单自定义图表 需要添加一个隐藏不展示的筛选条件。该条件解释后端提供，除了value外，其他都写死
     * @param {Number} id 版本id
     */
    workOrderFormat(id) {
      return {
        accuracy: 2,
        attr: 'versionId',
        attrName: '版本Id',
        attrType: 'num',
        businessType: 20350,
        cancelFlag: 0,
        comboType: 0,
        defaultAttr: {
          defaultValue: '',
          rely: { displayField: '', linkField: '', targetField: '' }
        },
        defaultName: '版本Id',
        designEditable: 0,
        editHide: 0,
        editable: 1,
        editableRule: { dep: [], relative: [], role: [], roleList: [], user: [], userList: [] },
        fieldType: 2,
        fixed: false,
        formId: 0,
        integerOnly: 0,
        isOpen: 1,
        isRedundant: 0,
        isSingleRow: 0,
        memo: '',
        noEditable: 0,
        noRepeat: 0,
        numericalLimits: { max: 9.007199254740991e15, min: -9.007199254740991e15 },
        numericalLimitsFlag: 0,
        recentTime: false,
        redFlag: 0,
        required: 1,
        saasAttr: 'versionId',
        screenType: 3,
        setType: 1,
        showType: 0,
        sort: 3,
        strictController: 0,
        symbol: 'equal',
        value: [id],
        visible: 1,
        visibleScopeEnable: 0,
        visibleScopeRule: {
          dep: [],
          relative: [],
          role: [],
          roleList: [],
          user: [],
          userList: []
        }
      }
    },
    /**
     * @description: 添加子表单属性
     * @param selectedSubFormMap
     * @param chartExplain
     */
    addSelectSubFormAttr(selectedSubFormMap, chartExplain) {
      const driverSources = selectedSubFormMap[chartExplain.driverSources.formId]
        ? {
            ...chartExplain.driverSources,
            subFormAttr: selectedSubFormMap[chartExplain.driverSources.formId]
          }
        : {
            ...chartExplain.driverSources,
            subFormAttr: ''
          }

      const slaveSources = chartExplain.slaveSources.map((source) => {
        return selectedSubFormMap[source.formId]
          ? {
              ...source,
              subFormAttr: selectedSubFormMap[source.formId]
            }
          : {
              ...source,
              subFormAttr: ''
            }
      })
      return {
        ...chartExplain,
        driverSources,
        slaveSources
      }
    },

    /**
     * @description: 数据集保存逻辑
     * @param obj
     */
    dataSetSaveHandler(obj) {
      // 数据集保存逻辑
      if (obj.chartList[0].single === '2') {
        obj.chartList[0].driverSources = {
          // 初始化用driverSources中的id 编辑进入用的是driverSources中的dataSetId
          dataSetId: obj.chartList[0].driverSources.id || obj.chartList[0].driverSources.dataSetId,
          name: obj.chartList[0].driverSources.name
        }
      }
    },

    /**
     * @description: 气泡图保存逻辑
     * @param obj
     * @returns {boolean}
     */
    scatterGraphSave(obj) {
      if (obj.chartList[0].chartType === 29) {
        const copyObj = JSON.parse(JSON.stringify(obj))
        const { chartList } = copyObj
        const { bubbleSize, chartValue } = chartList[0]
        // 对气泡大小字段做特殊处理
        if (bubbleSize && bubbleSize.length > 0) {
          chartValue.push(bubbleSize[0])
        }
        obj.chartList = copyObj.chartList
        // 气泡图编辑页值字段为空直接强拦截阻止保存
        if (!this.copyChartExplain.chartValue.length) {
          this.$message({
            type: 'warning',
            message: '值不能为空'
          })
          return false
        }
      }

      return true
    },

    /**
     * @description: 计算字段处理
     * @param obj
     * @returns {*}
     */
    computedFieldsHandle(obj) {
      obj.chartList.forEach((item) => {
        /* 处理后端需要的计算字段 start */
        if (item.computedAttr && item.computedAttr.length) {
          item.computedAttr.forEach((oitem) => {
            if (oitem.formulaInfo && oitem.formulaInfo.formula) {
              const arr = oitem.formulaInfo.formula.match(/[0-9]+\.[a-z]+\w+/g)
              this.forEachArr(arr, item)
            }
            if (this.currentSelectedSubFormMap[oitem.formId]) {
              oitem.subFormAttr = this.currentSelectedSubFormMap[oitem.formId]
            }
          })
        }
        item.computedNumAttr.filter(
          (itemObj, index, currentArr) => this.checkFlag(currentArr, itemObj) === index
        )
        /* 处理后端需要的计算字段 end */
        BooleanToNumber(item)
      })
      return obj
    },

    /**
     * @description: 删除图表解释中无用的属性
     */
    chartExplainDeleteHandle(chartExplain) {
      if (chartExplain.hasOwnProperty('chart')) {
        return {
          ...chartExplain,
          chart: {}
        }
      }

      const { permissions } = chartExplain

      if (this.templateDataPermissionArray) {
        delete permissions.dataPermissionAttr
      }

      return {
        ...chartExplain,
        permissions,
        table: {}
      }
    },

    @xbb.debounceWrap()
    saveChart() {
      if (this.specialJudge()) return
      // 添加计算字段属性
      this.copyChartExplain.computedNumAttr = []
      // 根据当前全局样式为其设置默认的ui样式
      if (!this.copyChartExplain.itemStyleOption) {
        this.$set(
          this.copyChartExplain,
          'itemStyleOption',
          this.createItemStyleOption(this.chartOption.globalStyleOption)
        )
      }
      if (this.copyChartExplain.copyId) {
        this.copyId = this.copyChartExplain.copyId
        this.copyChartExplain.id = ''
        delete this.copyChartExplain.copyId
      }
      // 对指标图做传参处理
      if (!this.quotaDefaultHandle()) return

      let obj = Object.assign({}, this.chartOption)
      obj.chartList = []
      obj.chartList.push(
        this.addSelectSubFormAttr(
          this.currentSelectedSubFormMap,
          this.chartExplainDeleteHandle(this.copyChartExplain)
        )
      )
      // 格式转换
      BooleanToNumber(obj)

      obj = this.computedFieldsHandle(obj)

      // 补充后端需要的参数
      this.$set(obj, 'categoryId', utils.SS.get('VUE-chart-selectedChart').categoryId)
      this.$set(obj, 'search', [])
      this.$set(obj, 'searchActive', false)
      this.addNamePostfix(obj)
      utils.LS.remove('customStyle')

      this.dataSetSaveHandler(obj)

      if (this.dataWarningList.length !== 0) {
        this.addWarnList(obj)
      }

      // 气泡图保存逻辑
      if (!this.scatterGraphSave(obj)) return

      // 增加图表钻取相关参数
      this.$set(obj.chartList[0], 'drill', this.levelDrill)

      if (this.$refs['single-main'].isChange) {
        this.$delete(obj.chartList[0].explains, 'sortMapList')
      }

      const params = xbb.deepClone(obj)
      if (!params.chartList[0].driverSources.versionId && params.chartList[0].workOrderVersion) {
        params.chartList[0].filter.push(this.workOrderFormat(params.chartList[0].workOrderVersion))
      } else if (params.chartList[0].driverSources.versionId) {
        params.chartList[0].filter.push(
          this.biStageFormat(params.chartList[0].driverSources.versionId)
        )
      }

      saveChart(params).then((res) => {
        this.$message({
          type: 'success',
          message: res.msg
        })
        // 保存成功清空数据预警字段
        this.$store.dispatch('clearWarnData')
        this.$emit('update-confirm', { chartExplain: this.copyChartExplain, copyId: this.copyId })
      })
    },
    forEachArr(arr, item) {
      this.computedFieldList.forEach((iitem) => {
        arr?.forEach((iiitem) => {
          if (
            Number(iiitem.split('.')[0]) === iitem.formId &&
            iiitem.split('.')[1] === iitem.attr
          ) {
            item.computedNumAttr.push(iitem)
          }
        })
      })
    },

    checkFlag(currentArr, itemObj) {
      return currentArr.findIndex(
        (itemObj2) => itemObj2.attr === itemObj.attr && itemObj2.formId === itemObj.formId
      )
    },
    addWarnList(obj) {
      const arr = this.dataWarningList.map((item) => {
        const list = {
          ...item
        }
        list.enable = item.enable ? 1 : 0
        if (obj.chartList[0].id) {
          list.chartId = obj.chartList[0].id
        }
        if (typeof item.id === 'string') {
          delete list.id
        }
        if (item.againEdit) {
          delete list.againEdit
        }
        return list
      })
      obj.chartList[0].dataWarningList = arr
    },
    // 重复名字增加后缀
    addNamePostfix(obj) {
      if (obj.chartList.length) {
        // 增加后缀"_"
        obj.chartList.forEach((item) => {
          // 有重复值的对象列表
          if (!item.name.includes('_')) {
            const itemListObj = obj.chartList.filter((chart) => {
              if (item.name.includes('_')) {
                return chart.name.includes(item.name.split('_')[0])
              } else {
                return chart.name.includes(item.name)
              }
            })
            // 重复值得后缀列表
            const itemNameList = itemListObj.map((itemObj) => {
              return itemObj.name.split('_')[1]
            })
            if (itemNameList.length > 1) {
              // 当前重复值后缀列表最大值
              let maxIndex = 1
              const hasNumber = itemNameList.filter((item) => {
                return item !== undefined
              })
              if (hasNumber.length) {
                maxIndex = Math.max.apply(null, hasNumber) + 1
              }
              itemNameList.forEach((postfix, itemIndex) => {
                if (
                  itemNameList.indexOf(postfix) !== itemNameList.lastIndexOf(postfix) &&
                  itemNameList.indexOf(postfix) !== itemIndex
                ) {
                  itemListObj[itemIndex].name = `${
                    itemListObj[itemIndex].name.split('_')[0]
                  }_${maxIndex++}`
                }
              })
            }
          }
        })
      }
    },
    // 返回图表渲染页
    backChartCenter() {
      // 如果预警数据被修改
      const index = this.dataWarningList.findIndex((item) => {
        return typeof item.id === 'string' || item.operationFlag === 1 || item.operationFlag === 2
      })
      utils.LS.remove('customStyle')
      if (
        JSON.stringify(this.copyChartExplain) !== JSON.stringify(this.chartExplain) ||
        index !== -1
      ) {
        this.$confirm(this.$t('chartEdit.leaveSave'), this.$t('chartEdit.confirmInfo'), {
          distinguishCancelAndClose: true,
          confirmButtonText: this.$t('operation.save'),
          cancelButtonText: this.$t('chartEdit.abandonModify')
        })
          .then(() => {
            this.saveChart()
          })
          .catch(() => {
            this.$store.dispatch('clearWarnData')
            setTimeout(() => {
              this.$root.eventHub.$emit('graphResize')
            }, 0)
            this.$parent.panelDesign = true
          })
      } else {
        setTimeout(() => {
          this.$root.eventHub.$emit('graphResize')
          this.$store.dispatch('clearWarnData')
        }, 0)
        this.$parent.panelDesign = true
      }
    },
    inputChange() {
      if (this.copyChartExplain.name.length) {
        const inputContainer = this.$refs.chartName.$el
        inputContainer.style.width = ''
        event.target.style.width = '0px'
        event.target.style.width = event.target.scrollWidth + 'px'
      }
      this.copyChartExplain.explains.isEditName = true
    },
    /**
     * @description 对指标图做传参处理
     * @returns {boolean}
     */
    quotaDefaultHandle() {
      if ([21, 24].includes(this.copyChartExplain.chartType)) {
        if (!this.belongList.length || !this.timeList.length) {
          this.$message({
            type: 'warning',
            message: `${this.$t('chartManagement.checkDataSource')}`
          })
          return false
        }

        // 对没有归属人和统计时间的解释，赋默认的选择
        if (
          this.copyChartExplain.belongAttr === undefined ||
          this.copyChartExplain.timeAttr === undefined
        ) {
          this.belongList[0].fieldList.length &&
            this.$set(this.copyChartExplain, 'belongAttr', this.belongList[0].fieldList[0])
          this.timeList[0].fieldList.length &&
            this.$set(this.copyChartExplain, 'timeAttr', this.timeList[0].fieldList[0])
        }
        // 如果选择的是副指标指标图，则保存时，需要将分组字段清空，否则会影响后端统计
        if (this.copyChartExplain.chartType === 24) {
          this.$set(this.copyChartExplain, 'rowGroupAttr', [])
        }

        return true
      }
      return true
    },
    // 使用数据集作为数据源，则数据集发生变化时，此图表中的字段配置都需要清空
    dataSetChangeHandle() {
      this.chartExplain.dataSetEditFlag && this.$emit('init')
    },
    // 切换图表类型
    chartTypeChange() {
      this.inputChange()
      // 切换图表类型，需要重新获取数据源，更新钻取功能的使用权限
      if (this.copyChartExplain.single === '2') {
        this.getDataSetAttr()
      } else {
        this.getDataSourceAttr()
      }
      // 指标图和副指标图互相切换类型时，需要清空指标及分组字段
      if ([21, 24].includes(this.copyChartExplain.chartType)) {
        // this.$set(this.copyChartExplain, 'summaryAttr', [])
        this.$set(this.copyChartExplain, 'rowGroupAttr', [])
        this.$set(
          this.copyChartExplain,
          'secondTargetAttr',
          this.copyChartExplain.secondTargetAttr.map((item) => {
            return {
              ...item,
              advancedComputing: ['none']
            }
          })
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.single-chart-design {
  position: relative;
  height: 100%;
  background: #f5f6f9;
  // border-bottom: 1px solid $neutral-color-3;
  .header-top {
    position: absolute;
    top: -60px;
    z-index: 999;
    box-sizing: border-box;
    width: 100%;
    height: 60px;
    background: $base-white;

    .header {
      height: 60px;
      padding: 0px 20px;
      line-height: 60px;
      background-color: $base-white;
      border-bottom: 1px solid $neutral-color-3;

      &__icon {
        display: inline-block;
        padding-right: 10px;
        font-size: 22px;
        color: $text-plain;
        cursor: pointer;
      }

      &__title {
        position: relative;
        display: inline-block;

        width: auto;
        min-width: 15px;
        max-width: 200px;
        overflow: hidden;
        font-size: 20px;
        color: $text-plain;
        vertical-align: top;

        :deep(.el-input__inner) {
          max-width: 200px;
          padding: 0;
          font-size: 20px;
          color: $text-plain;
          text-align: center;
          background-color: $base-white;
          border: none;
          border-bottom: 1px dashed $text-plain;
          border-bottom-right-radius: 0;
          border-bottom-left-radius: 0;
        }
      }

      &__rename {
        display: inline-block;
        padding-left: 10px;
        font-size: 22px;
        color: $text-plain;
        cursor: pointer;
      }

      &__input {
        max-width: none;
      }

      &__name {
        position: absolute;
        left: 0;
      }

      &__save {
        float: right;
        color: $text-plain;

        span {
          margin-right: 30px;
        }

        &:hover {
          span {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .design-container {
    height: 100%;
  }
}
</style>
