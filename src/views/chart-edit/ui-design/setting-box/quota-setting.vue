<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 指标图 单组件样式设置
-->
<template>
  <div v-if="itemStyleOption" class="global-setting">
    <p class="big-title">{{ $t('chartEdit.overallStyle') }}</p>
    <background-setting
      :background.sync="itemStyleOption.itemBackground"
      :cover-mode.sync="itemStyleOption.coverMode"
      :custom-colors="globalStyleOption.globalCustomColor"
      :mode.sync="itemStyleOption.itemBackgroundMode"
      :reset-color="themeMap[globalStyleOption.globalTheme].globalChartBackground"
    ></background-setting>
    <background-setting
      :background.sync="itemStyleOption.itemTitle"
      :custom-colors="globalStyleOption.globalCustomColor"
      :reset-color="themeMap[globalStyleOption.globalTheme].globalChartTitle"
      :show-picture-setting="false"
      :title="$t('chartEdit.titleColor')"
    ></background-setting>
    <div class="line"></div>
    <p class="big-title">{{ indexSetting ? '指标图样式' : $t('chartEdit.quotaComponent') }}</p>
    <quota-detail-setting v-if="!indexSetting" :chart-explain="chartExplain"></quota-detail-setting>
    <background-setting
      :background.sync="itemStyleOption.itemQuotaContentFont"
      :custom-colors="globalStyleOption.globalCustomColor"
      :reset-color="globalStyleOption.globalQuotaContentFont"
      :show-picture-setting="false"
      :title="$t('chartEdit.numericalColor')"
    ></background-setting>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import backgroundSetting from '../components/background-setting'
import quotaDetailSetting from '../components/quota-detail-setting'
import { GLOBAL_STYLE_OPTION, DEFAULT_ITEM_STYLE } from '../config/theme-color'

export default {
  components: {
    BackgroundSetting: backgroundSetting,
    QuotaDetailSetting: quotaDetailSetting
  },
  props: {
    globalStyleOption: Object,
    itemStyleOption: Object,
    // 指标图的图表解释
    chartExplain: Object,
    indexSetting: Boolean
  },
  data() {
    return {
      defaultItemStyle: DEFAULT_ITEM_STYLE,
      themeMap: GLOBAL_STYLE_OPTION
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.global-setting {
  .theme-box {
    display: flex;
    flex-wrap: wrap;
    padding: 0 10px;
    .theme-item {
      width: 115px;
      height: 65px;
      margin-right: 20px;
      margin-bottom: 10px;
      cursor: pointer;
      background: black;
      border-radius: 2px;
    }
    .active-theme {
      box-shadow: 0px 0px 0px 2px #fa8e3e;
    }
    .theme-item:nth-child(even) {
      margin-right: 0px;
    }
  }
  .big-title {
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    line-height: 30px;
  }
  .line {
    width: 100%;
    height: 1px;
    margin: 10px 0;
    background: #e1e5ec;
  }
}
</style>
