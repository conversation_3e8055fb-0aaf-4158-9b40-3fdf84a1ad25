<template>
  <div>
    <ul class="file-list">
      <el-popconfirm
        v-for="(item, index) in fileList"
        :key="index"
        ref="popover"
        :confirm-button-text="$t('operation.iknow')"
        :disabled="item.size < 1024 * 1024 * 20"
        placement="bottom"
        popper-class="popconfirm-hide-cancel-button"
        :title="$t('mail.fileTooBig')"
        trigger="click"
        width="280"
      >
        <li
          :key="index"
          slot="reference"
          v-loading="item.isUpload && !item.uploadSuccessFlag"
          class="file-item"
          :class="{
            'is-loading': item.isUpload && !item.uploadSuccessFlag
          }"
        >
          <div class="file-type-icon" :class="getFileType(item.ext)" />
          <div class="file-info">
            <div v-tooltip="item.fileName" class="title">
              {{ item.fileName }}
            </div>
            <div class="sub-title">
              {{ formatSize(item.size) }}
            </div>
          </div>
          <div v-if="item.attachIndex" class="operation-group">
            <span
              v-if="ifCanPreview(item.ext)"
              v-tooltip="$t('operation.preview')"
              class="icon-button"
              @click="handlePreview(index)"
              ><i class="icon-eye-line t-iconfont"
            /></span>
            <span
              v-tooltip="$t('operation.download')"
              class="icon-button"
              @click="handleDownload(item.attachIndex, item.fileName)"
              ><i class="icon-download-2-line t-iconfont"
            /></span>
            <span
              v-if="canRemoveFile"
              v-tooltip="$t('mail.remove')"
              class="icon-button"
              @click="handleRemoveFile(index)"
              ><i class="icon-delete-bin-line t-iconfont"
            /></span>
          </div>
        </li>
      </el-popconfirm>
    </ul>

    <lg-preview
      v-if="previewVisible"
      append-to-body
      :index="currentIndex"
      :list="fileList"
      @close="previewVisible = false"
    />
  </div>
</template>

<script>
import { formatUnit } from '@/utils/file-list.js'

export default {
  name: 'FileList',

  components: {
    LgPreview: () =>
      import(
        /* webpackChunkName: "file-picture-preview" */ '@/components/files/file-picture-preview'
      )
  },

  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    canRemoveFile: Boolean
  },

  data() {
    return {
      previewVisible: false,
      currentIndex: 0
    }
  },

  methods: {
    getFileType(ext) {
      return utils.getFileType(ext)
    },
    ifCanPreview(ext) {
      const suffix = ext.toLowerCase()
      return !!utils.getFileViewType(suffix)
    },
    formatSize(size) {
      return formatUnit(size)
    },
    handlePreview(index) {
      this.currentIndex = index
      this.$nextTick(() => {
        this.previewVisible = true
      })
    },
    handleDownload(src, fileName) {
      utils.fileDownload(src, fileName)
    },
    handleRemoveFile(index) {
      // eslint-disable-next-line vue/no-mutating-props
      this.fileList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .file-item {
    box-sizing: border-box;
    display: flex;
    gap: 8px;
    align-items: center;
    width: 240px;
    height: 52px;
    padding: 8px;
    cursor: pointer;
    background: $base-white;
    border: 1px solid $neutral-color-3;
    border-radius: 4px;

    &:hover {
      background: $neutral-color-2;
    }
    &:not(.is-loading):hover {
      .operation-group {
        display: flex;
      }
    }

    .file-type-icon {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
    }
    .file-info {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 4px;
      min-width: 0;
    }
    .title {
      @include singleline-ellipsis();
      font-size: 13px;
      line-height: 18px;
      color: $text-plain;
    }
    .sub-title {
      @include singleline-ellipsis();
      font-size: 12px;
      line-height: 16px;
      color: $text-auxiliary;
    }
    .operation-group {
      display: none;
      flex-shrink: 0;
      gap: 8px;

      .t-iconfont {
        font-size: inherit;
      }
      .icon-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        font-size: 16px;
        color: $text-auxiliary;
        cursor: pointer;
        border-radius: 4px;

        &:hover {
          background: $neutral-color-3;
        }
      }
    }
  }
}
</style>
