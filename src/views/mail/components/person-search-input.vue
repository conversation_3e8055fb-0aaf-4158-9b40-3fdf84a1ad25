<template>
  <div class="person-search-input">
    <input
      v-model="inputValue"
      class="input"
      @blur="handleInputBlur"
      @focus="handleInputFocus"
      @keyup.enter="handleInputEnterKeyUp"
    />

    <RecentContactListPopover
      ref="recentContactListPopover"
      :search-name="inputValue"
      @addPerson="handleAddPerson"
    >
    </RecentContactListPopover>
  </div>
</template>

<script>
import RecentContactListPopover from './recent-contact-list-popover.vue'

export default {
  name: 'PersonSearchInput',

  components: { RecentContactListPopover },

  mixins: [],

  props: {},

  data() {
    return {
      inputValue: ''
    }
  },

  computed: {},

  watch: {},

  created() {},

  beforeDestroy() {},

  methods: {
    handleInputFocus() {
      this.$refs.recentContactListPopover.getRecentContactList()
      this.$refs.recentContactListPopover.showPopover(this.$el)
    },
    handleInputBlur() {
      this.handleInputEnterKeyUp()
      setTimeout(() => {
        this.$refs.recentContactListPopover.hidePopover(this.$el)
      }, 300)
    },
    handleInputEnterKeyUp() {
      const mailReg =
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

      if (!mailReg.test(this.inputValue)) return

      const index = this.inputValue.indexOf('@')
      const personData = {
        name: this.inputValue.slice(0, index),
        mailAddress: this.inputValue
      }
      this.handleAddPerson(personData)
    },
    handleAddPerson(personData) {
      this.$emit('addPerson', personData)
      this.inputValue = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.person-search-input {
  position: relative;
  box-sizing: border-box;
  flex: 1;
}

.input {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: $text-plain;
  outline: none;
}
</style>
