// 话务中心

import * as types from '../mutation-types'

const state = {
  CTIStatus: false, // CTI开启状态
  refInfos: {}, // CTI 客户信息
  CTIEmit: false, // 监听度言窗口的新建/查看事件
  moduleType: 0,
  moduleTypeList: [], // 可开启的呼叫中心列表
  CTIRole: {},
  ctiModal: false,
  CTIAccount: {}, // CTI 用户登录信息
  // 未接来电数
  missCallCount: 0
}

// getters
const getters = {
  getCTIStatus: (state) => state.CTIStatus,
  getRefInfos: (state) => state.refInfos,
  getCTIEmit: (state) => state.CTIEmit,
  getModuleType: (state) => state.moduleType,
  getCTIRole: (state) => state.CTIRole,
  getCtiModal: (state) => state.ctiModal,
  getCTIAccount: (state) => state.CTIAccount,
  getModuleTypeList: (state) => state.moduleTypeList,
  getMissCallCount: (state) => state.missCallCount
}

// actions
const actions = {
  setCTIStatus({ commit }, payload) {
    commit(types.CTI_STATUS, payload)
  },
  setRefInfos({ commit }, payload) {
    commit(types.REF_INFOS, payload)
  },
  setCTIEmit({ commit }) {
    commit(types.CTI_EMIT)
  },
  setModuleType({ commit }, payload) {
    commit(types.MODULE_TYPE, payload)
  },
  setCTIRole({ commit }, payload) {
    commit(types.CTI_ROLE, payload)
  },
  toggleCtiModal({ commit }, payload) {
    commit(types.CTI_MODAL, payload)
  },
  setCTIAccount({ commit }, payload) {
    commit(types.CTI_ACCOUNT, payload)
  },
  setModuleTypeList({ commit }, payload) {
    commit(types.MODULE_TYPE_LIST, payload)
  },
  updateMissCallCount({ commit }, payload) {
    commit(types.MISS_CALL_COUNT, payload)
  }
}

// mutations
const mutations = {
  // 设置度言CTI 开启状态
  [types.CTI_STATUS](state, bool) {
    state.CTIStatus = bool
  },

  // 设置度言CTI 的客户信息
  [types.REF_INFOS](state, obj) {
    state.refInfos = obj
  },

  // 触发度言窗口的新建/查看事件
  [types.CTI_EMIT](state) {
    state.CTIEmit = !state.CTIEmit
  },

  // 设置话务中心类型码
  [types.MODULE_TYPE](state, num) {
    state.moduleType = num
  },

  // 是否是坐席
  [types.CTI_ROLE](state, obj) {
    state.CTIRole = obj
  },

  // 是否展开CTI弹窗
  [types.CTI_MODAL](state, bool) {
    state.ctiModal = bool
  },

  // 设置CTI用户登录信息
  [types.CTI_ACCOUNT](state, obj) {
    state.CTIAccount = obj
  },

  // 设置可用呼叫中心的类型
  [types.MODULE_TYPE_LIST](state, obj) {
    state.moduleTypeList = obj
  },

  // 更新未接来电数量
  [types.MISS_CALL_COUNT](state, obj) {
    state.missCallCount = obj
  }
}

export default {
  state,
  getters,
  actions,
  mutations
}
