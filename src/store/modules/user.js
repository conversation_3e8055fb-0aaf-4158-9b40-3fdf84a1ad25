/*
 * @Description: 当前登录用户信息 -- vuex
 */

import { Message } from 'element-ui'
import { verifyHasLicense } from '@/api/scrm/license/index.js'
import bus from '@/utils/temp-vue'
import i18n from '@/lang'
import { getUserInfo, getSubFormConfig, checkLicense } from '@/api/system'
// enum
import { ENUM_LOGIN_COMPANY_TYPE } from '@/views/scrm/constants/enum.license.js'
// import {
//   gioBindUserId,
//   gioPushUserInfo,
//   // gioPushPageInfo,
//   gioClearUserId,
//   gioComeFromDD,
//   gioComeFromWeb,
//   gioUserNum
// } from '@/utils/buriedPoint.js'
import xbb from '@xbb/xbb-utils'

let isCreateWaterMark = false
const createWaterMark = (corpName, userName, config, guid) => {
  if (isCreateWaterMark) {
    return
  } else {
    isCreateWaterMark = true
  }
  function createMark() {
    const can = document.createElement('canvas')
    const width = 220
    const height = 140
    Object.assign(can, { width, height })
    const cans = can.getContext('2d')
    if (cans) {
      cans.rotate((-20 * Math.PI) / 120)
      cans.translate(-60, -30)
      cans.fillStyle = 'rgba(0, 0, 0, 0.07)'
      cans.textAlign = 'left'
      cans.textBaseline = 'middle'
      cans.font = 'normal 14px sans-serif'
      config.companyNameWaterMarkDisplay && cans.fillText(corpName, width / 20, height)
      config.userInfoWaterMarkDisplay && cans.fillText(userName, width / 20, height + 20)
    }
    return can.toDataURL('image/png')
  }

  const mark = createMark()
  // 不能用 querySelectorAll 动态改，无法覆盖后来添加的 dom
  const markMask = document.createElement('div')
  markMask.id = guid
  markMask.style.backgroundImage = `url(${mark})`
  markMask.style.pointerEvents = 'none'
  markMask.style.width = window.innerWidth + 'px'
  markMask.style.height = window.innerHeight + 'px'
  markMask.style.position = 'fixed'
  markMask.style.left = '0'
  markMask.style.top = '0'
  markMask.style.zIndex = '9999999'
  document.body.appendChild(markMask)
  window.addEventListener('resize', () => {
    markMask.style.width = window.innerWidth + 'px'
    markMask.style.height = window.innerHeight + 'px'
  })
}

const getCurrentEnv = (key) => {
  const platform = {
    2: 'h5',
    3: 'wx',
    4: 'lark'
  }
  return platform[key] || 'h5'
}

const kindOfBidEnum = {
  2: 'dta_2_17482', //服务云
  4: 'dta_2_19460' //专属钉
}

/**
 * @description: 独立上架需要切换监控中心 主要就是bid不同，其他都一样
 */
const changeWPK = (kind) => {
  // 1. 先移除已有的监控中心脚本
  const scriptWPK = document.querySelector(
    'script[src="https://xbongbong.oss-cn-hangzhou.aliyuncs.com/cdnfile/js/others/1.0.0/dingtalk-wpkReporter.js"]'
  )
  if (scriptWPK) {
    scriptWPK.remove()
  }
  // 2. 删除脚本已有的实例 通过下面方法重新生成
  delete window.__wpk
  // 3. 插入新的脚本 代码跟static/js/webWpkReporter.js里的一模一样，就只有bid不一样
  !(function (c, i, e, b) {
    // 这里由于想在测试环境监控先注释掉
    const isProduction = c.location.host.indexOf('pfweb.xbongbong.com') > -1
    if (!isProduction) return
    const h = i.createElement('script')
    const f = i.getElementsByTagName('script')[0]
    h.type = 'text/javascript'
    h.crossorigin = true
    h.onload = function () {
      c[b] || (c[b] = new c.wpkReporter({ bid: kindOfBidEnum[+kind] }))
      c[b].installAll()
    }
    f.parentNode.insertBefore(h, f)
    h.src = e
  })(
    window,
    document,
    'https://xbongbong.oss-cn-hangzhou.aliyuncs.com/cdnfile/js/others/1.0.0/dingtalk-wpkReporter.js',
    '__wpk'
  )
}

export default {
  state: {
    companyIcon: null, // 公司icon
    userInfo: null, // 用户个人信息
    useLimit: true, // 授权账户额度是否受限制
    feeCompany: null, // 套餐信息
    companyName: null, // 公司名
    betaInfo: null, // 是否为公测版
    mainAdmin: null, // 是否为主管理员
    openLowCode: false, // 是否开启低代码
    isDeveloper: false, // 当前用户是否是开发者
    isDisPlay: null, // 是否显示右下角云客服
    isOpenWorkflow: false, // 是否升级工作流
    feeType: null, //套餐版本
    companyLicenseType: null, // 当前登录账户的公司（type）【企微、独立版本、飞书、钉钉】详见枚举scrm/constants/enum.license
    companyLicenseAccount: [], // 当前登录账户的企微公司,是否拥有企微【基础账号/互通账号】
    companyLicenseBusinessType: [], // 当前业务模块是否拥有权限【业务id】（包含则有）
    curRouter: null, // 记录当前路由对象，用于一些特殊判断（如工作流是否处于老审批页面）
    subFormConfig: {
      selectProductRowNum: 260,
      subFormRowNum: 200
    },
    hasCustomPage: false,
    isBoss: false,
    assignment: false,
    isGray: false, // 是否是灰度标识
    enableESign: false, //是否开启易签宝
    isCustomerPublicSeniorMode: false, // 公海池是否是标准模式(客户)
    newMenuLayout: false, // 是否新菜单布局
    hasCrmFee: false, // 是否有CRM套餐
    newLayoutKind: null, // 新布局类型
    noApp: false, // 缺省页
    layoutList: [], // 新布局 可用菜单列表
    showHomeDefaultPage: false, // 老菜单是否显示空白页
    isCluePublicSeniorMode: false, // 公海池是否是标准模式(线索)
    isOpenMenuWhite: false, // 是否可以跨应用拖拽（二级菜单可以放到其他应用下，但是不能放到其他应用的二级菜单下）
    waterMarkId: null, // 水印的 domid，提供给 bi 等场景在全屏时做dom 平移用的
    waterMarkConfig: {},
    isSmartService: false, // 是否需要在独立场景下开启客服悬浮【合同比对】
    isOpenOnlineEditor: false, // 是否开启了合同模版功能
    syncInfo: {
      // 企微同步设置
      haveWxSyncConfig: false, // 【企微同步配置】是否设置了
      isAutoCreate: null, // 【企微同步配置】是否开启了自动创建(true/false) （特殊ps: 可以根据情况，先判断有没有设置企微同步信息，否则isAutoCreate的值是Null）
      syncFormBusinessType: undefined
    },
    listFilter: { listFilterGuide: 0, listFilterMode: 0 }, // 列表页筛选配置: listFilterGuide 是否显示引导页、listFilterMode 是否为固定筛选模式
    liveStreamingInfo: {
      liveStatus: 'historyLive', //直播状态
      liveStartTime: -1
    }, // 直播信息
    hasIntelligentForm: false,
    useNuwa: false,
    openLinkDataMultiTemplate: false,
    versionType: null,
    detailOpenMethod: '1',
    uiPaasHome: false // 首页是否开启了UI-Paas
  },
  getters: {
    getUserInfo: (state) => {
      return state.userInfo
    },
    getListFilterConfig: (state) => {
      return state.listFilter
    },
    adminOrBoss: (state) => {
      return state.userInfo && state.userInfo.adminOrBoss
    },
    smsSetUpdate: (state) => {
      // 短信模板新建权限
      return state.userInfo && state.userInfo.smsSetUpdate
    },
    customerDuplicate: (state) => {
      return state.userInfo && state.userInfo.customerDuplicate
    },
    clueDuplicate: (state) => {
      return state.userInfo && state.userInfo.clueDuplicate
    },
    feeType: (state) => {
      // 0标准版 1高级版 3旗舰版
      return state.feeCompany && state.feeCompany.feeType
    },
    smartServiceShow: (state) => {
      return (
        state.feeCompany && !state.feeCompany.isFree && !state.isDisPlay && !state.isSmartService
      )
    },
    //智能云客服跳转链接变一下，之前是付费才有，现在是不管免费付费都要有，根据state.feeCompany.isFree来判断
    smartIsPay: (state) => {
      //后端返回的isFree为0代表付过费了
      return state.feeCompany && !state.feeCompany.isFree
    },
    feeTypeName: (state) => {
      return state.feeCompany && state.feeCompany.feeTypeName
    },
    feeName: (state) => {
      return state.feeCompany && state.feeCompany.feeName
    },
    // 是否不阉割设置功能 0 （标准版阉割） 1 (高级版阉割) || undefined
    isProfessionalEdition: (state, getter) => {
      return getter.feeType !== 3
        ? state.feeCompany && state.feeCompany.isProfessionalEdition
        : undefined
    },
    companyName: (state) => {
      return state.companyName && state.companyName.corpName
    },
    companyIcon: (state) => {
      return state.companyName && state.companyName.corpLogoUrl
    },
    getBetaInfo: (state) => {
      return state.betaInfo
    },
    isMainAdmin: (state) => {
      return state.mainAdmin
    },
    // 当前用户是否是开发者（低代码用）
    isDeveloper: (state) => {
      return state.isDeveloper
    },
    // 是否开启工作流
    isOpenWorkflow: (state, getter) => {
      const isWorkflowShowOld = getter.curRouter.meta && getter.curRouter.meta.isWorkflowShowOld

      return !!(state.isOpenWorkflow && !isWorkflowShowOld)
    },
    curRouter: (state) => {
      return state.curRouter || {}
    },
    getCompanyLicenseType: (state) => {
      return state.companyName && state.companyName.source
    },
    getCompanyLicenseAccount: (state) => {
      return state.companyLicenseAccount
    },
    getCompanyLicenseBusinessType: (state) => {
      return state.companyLicenseBusinessType
    },
    // 子表单配置 比如子表单允许新增多少行数据 通过后端控制
    subFormConfig: (state) => {
      return state.subFormConfig
    },
    // 首页管理权限设置成getters
    hasHomePage: (state) => {
      return state.hasCustomPage
    },
    hasIsBoss: (state) => {
      return state.isBoss
    },
    isUiPaasHome: (state) => {
      return state.uiPaasHome
    },
    isCustomer: (state) => {
      return state.assignment
    },
    // 是否是灰度标识
    isGray: (state) => {
      return state.isGray
    },
    // 是否启用易签宝
    enableESign: (state) => {
      return state.enableESign
    },
    // 是否使用nuwa
    useNuwa: (state) => {
      return state.useNuwa
    },
    openLinkDataMultiTemplate: (state) => {
      return state.openLinkDataMultiTemplate
    },
    // 工单版本: 1.新用户标准版 2.历史用户标准版 3.高级版 4.旗舰版
    versionType: (state) => {
      return state.versionType
    },
    waterMarkId: (state) => {
      return state.waterMarkId
    },
    waterMarkConfig: (state) => state.waterMarkConfig,
    hasSCRM: (state) => {
      return Number(state.feeCompany.subType) === 1
    },
    // 是否需要在独立场景下显示客服悬浮
    isSmartService: (state) => {
      return state.isSmartService
    },
    // 企微同步设置-info
    getSyncInfo: (state) => {
      return state.syncInfo
    },
    // 是否开启了智能表单服务
    hasIntelligentForm: (state) => {
      return state.hasIntelligentForm
    },
    detailOpenMethod: (state) => {
      return state.detailOpenMethod
    }
  },
  mutations: {
    SET_USELIMIT(state, val) {
      state.useLimit = val
    },
    SET_FEE_COMPANY(state, val) {
      state.feeCompany = val
    },
    SET_CUSTOMER_PUBLIC_SENIOR_MODE(state, val) {
      state.isCustomerPublicSeniorMode = val
    },
    SET_NEW_MENU_LAYOUT(state, val) {
      state.newMenuLayout = val
    },
    SET_HAS_CRM_FEE(state, val) {
      state.hasCrmFee = val
    },
    SET_NEW_LAYOUT_KIND(state, val) {
      state.newLayoutKind = val
    },
    SET_NOAPP(state, val) {
      state.noApp = val
    },
    SET_LAYOUT_LIST(state, val) {
      state.layoutList = val
    },
    SET_SHOW_HOME_DEFAULT_PAGE(state, val) {
      state.showHomeDefaultPage = val
    },
    SET_IS_OPEN_MENU_WHITE(state, val) {
      state.isOpenMenuWhite = val
    },
    SET_CLUE_PUBLIC_SENIOR_MODE(state, val) {
      state.isCluePublicSeniorMode = val
    },
    SET_USER_INFO(state, val) {
      state.userInfo = val
    },
    SET_LIST_FILTER(state, val = {}) {
      state.listFilter = { ...state.listFilter, ...val }
    },
    SET_COMPANY(state, val) {
      state.companyName = val
    },
    BETA_INFO(state, val) {
      state.betaInfo = val
    },
    SET_MAIN_ADMIN(state, val) {
      state.mainAdmin = val
    },
    SET_AVATAR(state, url) {
      state.userInfo.avatar = url
    },
    SET_LOW_CODE_STATUS(state, val) {
      state.openLowCode = val
    },
    SET_LOW_CODE_FEE(state, val) {
      state.lowCodeFee = val
    },
    SET_BI_FEE(state, val) {
      state.biFee = val
    },
    SET_WORK_FLOW_FEE(state, val) {
      state.workflowFee = val
    },
    SET_DEVELOPER_STATUS(state, val) {
      state.isDeveloper = val
    },
    SET_DISPLAY(state, val) {
      state.isDisPlay = val
    },
    // 设置开启工作流是否升级的状态
    SET_OPEN_WORKFLOW_STATE(state, val) {
      state.isOpenWorkflow = val
    },
    //设置套餐版本
    SET_FEE_TYPE(state, val) {
      state.feeType = val
    },
    // 设置 当前登录账户的企微公司,是否拥有企微【基础账号/互通账号】
    SET_COMPANY_LICENSE_ACCOUNT: (state, val) => {
      state.companyLicenseAccount = val
    },
    // 设置 当前业务模块是否拥有权限【业务id】（包含则有）
    SET_COMPANY_LICENSE_BUSINESS_TYPE: (state, val) => {
      state.companyLicenseBusinessType = val
    },
    SET_CUR_ROUTER(state, val) {
      state.curRouter = val
    },
    SET_SUB_FORM_CONFIG(state, val) {
      state.subFormConfig = val
    },
    // 更改state中的数据
    SET_HAS_CUSTOM_PAGE(state, val) {
      state.hasCustomPage = val
    },
    SET_ISBOSS(state, val) {
      state.isBoss = val
    },
    SET_ASSIGNMENT(state, val) {
      state.assignment = val
    },
    SET_IS_GRAY(state, val) {
      state.isGray = val
    },
    SET_ENABLE_ESING(state, val) {
      state.enableESign = val
    },
    SET_USE_NUWA(state, val) {
      state.useNuwa = val
    },
    SET_DETAIL_DIALOG_TYPE(state, val) {
      state.detailOpenMethod = val
    },
    SET_USE_OPENLINKDATAMULTITEMPLATE(state, val) {
      state.openLinkDataMultiTemplate = val
    },
    SET_WORk_ORDER_VERSION(state, val) {
      state.versionType = val
    },
    SET_WATER_MARK_ID(state, val) {
      state.waterMarkId = val
    },
    SET_WATER_MARK_CONFIG(state, val) {
      state.waterMarkConfig = val
    },
    SET_IS_SMART_SERVICE(state, val) {
      state.isSmartService = val
    },
    SET_IS_OPEN_ONLINE_EDITOR(state, val) {
      state.isOpenOnlineEditor = val
    },
    SET_SYNC_INFO(state, val) {
      state.syncInfo = val
    },
    SET_LIVE_STREAMING_INFO(state, val) {
      state.liveStreamingInfo = val
    },
    SET_HAS_INTELLIGENT_FORM(state, val) {
      state.hasIntelligentForm = val
    },
    SET_UIPAAS_HOME(state, val) {
      state.uiPaasHome = val
    }
  },
  actions: {
    /**
     * 拉取用户详细
     */
    getUserInfo({ dispatch, commit }, isSyncCode) {
      return new Promise((resolve, reject) => {
        // 由于目前签证拦截都没加 暂时放放根据id来查
        getUserInfo({
          // userDetailFlag: false // 是否返回复杂数据
        })
          .then(
            ({
              result,
              result: {
                user,
                feeCompany,
                company,
                betaInfo,
                developer,
                isDisPlay,
                isOpenWorkflow,
                isGray,
                enableESign,
                officePreviewUrl,
                waterMark,
                isOpenMenuWhite = false,
                isCluePublicSeniorMode,
                isCustomerPublicSeniorMode,
                isOpenOnlineEditor,
                syncInfo,
                listFilterShowVO = {},
                hasIntelligentForm,
                newMenuLayout,
                hasCrmFee,
                hasLive,
                liveStatus,
                liveStartTime,
                useNuwa,
                uiPaasHome,
                openLinkDataMultiTemplate,
                versionType,
                detailOpenMethod,
                lowCodeFee,
                biFee,
                workflowFee,
                aiInsideWebUrl
              }
            }) => {
              if (isSyncCode) {
                // 企微同步设置的同步对象参数
                company.wechatSyncCode && utils.LS.set('wechatSyncCode', company.wechatSyncCode)
                return
              }
              commit('SET_USER_INFO', user)
              commit('SET_LIST_FILTER', listFilterShowVO)
              commit('SET_FEE_COMPANY', feeCompany)
              commit('SET_CLUE_PUBLIC_SENIOR_MODE', isCluePublicSeniorMode)
              commit('SET_CUSTOMER_PUBLIC_SENIOR_MODE', isCustomerPublicSeniorMode)
              commit('SET_NEW_MENU_LAYOUT', newMenuLayout)
              commit('SET_HAS_CRM_FEE', hasCrmFee)
              commit('SET_IS_OPEN_ONLINE_EDITOR', isOpenOnlineEditor)
              commit('SET_IS_OPEN_MENU_WHITE', isOpenMenuWhite)
              commit('SET_COMPANY', company)
              commit('SET_HAS_INTELLIGENT_FORM', hasIntelligentForm)
              commit('BETA_INFO', betaInfo)
              commit('SET_FEE_TYPE', feeCompany.feeType)
              // lowCode: 注册是否开启低代码相关信息
              commit('SET_LOW_CODE_FEE', lowCodeFee)
              commit('SET_BI_FEE', biFee)
              commit('SET_WORK_FLOW_FEE', workflowFee)
              commit('SET_DEVELOPER_STATUS', developer)
              utils.SS.set('isAdmin', user.admin)
              utils.LS.set('env', getCurrentEnv(company.source))
              // localStorage.setItem('openLowCode', feeCompany.feeType === 3)
              developer || localStorage.setItem('js-file-status', 'release') // 如果不是开发者强制使用线上js代码
              localStorage.setItem('roleIds', user.roleIds)
              localStorage.setItem('userName', user.name)
              localStorage.setItem('userId', user.userId)
              localStorage.setItem('corpName', company.corpName)
              if (xbb.isDevEnv()) {
                localStorage.setItem('aiInsideWebUrl', 'http://localhost:5173/')
              } else {
                localStorage.setItem('aiInsideWebUrl', aiInsideWebUrl || '')
              }
              localStorage.setItem('companySource', company.source)
              // 旧用户标识
              if (feeCompany?.addTime < 1737417600) {
                sessionStorage.setItem('isOldUser', 1)
              }
              if (+company.kind === 2) {
                // 是否独立上架，即是否服务云 2：独立上架
                utils.LS.set('systemKind', 2)
                // 设置图标
                utils.setStandAloneFavicon(
                  require('@/assets/xbb-work-order.png'),
                  i18n.t('login.XBBSC')
                )
                changeWPK(company.kind)
              } else if (+company.kind === 4) {
                // 专属钉
                localStorage.setItem('systemKind', 4)
                changeWPK(company.kind)
              } else {
                localStorage.removeItem('systemKind')
              }
              if (xbb.isDingTalk()) {
                // 销帮帮CRM 和 服务云 同时在钉钉工作台打开，需要将先打开跳转报错页面去
                bus.$emit('judge-multi')
              }
              officePreviewUrl && utils.SS.set('officePreviewUrl', officePreviewUrl)
              commit('SET_DISPLAY', isDisPlay)
              commit('SET_OPEN_WORKFLOW_STATE', isOpenWorkflow) // 登录时，存储工作流是否升级的状态
              commit('SET_IS_GRAY', isGray) // 是否是灰度中
              commit('SET_ENABLE_ESING', enableESign) // 是否启用易签宝
              commit('SET_USE_NUWA', useNuwa) // 是否使用nuwa
              company.wechatSyncCode && utils.LS.set('wechatSyncCode', company.wechatSyncCode) // 企微同步设置的同步对象参数
              // save企微同步设置
              if (syncInfo) {
                const { haveWxSyncConfig, isAutoCreate, syncFormBusinessType } = syncInfo
                commit('SET_SYNC_INFO', {
                  haveWxSyncConfig,
                  syncFormBusinessType,
                  isAutoCreate: haveWxSyncConfig ? isAutoCreate : null
                })
              }
              commit('SET_USE_OPENLINKDATAMULTITEMPLATE', openLinkDataMultiTemplate) // 是否使用nuwa
              commit('SET_WORk_ORDER_VERSION', versionType) // 工单版本: 1.新用户标准版 2.历史用户标准版 3.高级版 4.旗舰版
              commit('SET_DETAIL_DIALOG_TYPE', detailOpenMethod)

              commit('SET_UIPAAS_HOME', uiPaasHome)
              // gio 埋点部分
              // 用户id
              // gioBindUserId(user.userId)
              // 用户信息
              // gioPushUserInfo({
              //   corpid: company.corpid,
              //   feeTypeName: feeCompany.feeTypeName,
              //   feeName: feeCompany.feeName,
              //   companyName: company.corpName,
              //   industry: company.industry,
              //   scale: company.scale
              // })
              // gioUserNum()
              // 用户登录版本 页面变量上传
              getSubFormConfig().then((res) => {
                console.log(res)
                commit('SET_SUB_FORM_CONFIG', res.result)
              })

              console.log('[/user/info] 成功请求到用户信息', result)
              bus.$emit('login-success', result)
              // 全局水印
              if (waterMark && waterMark.waterMarkState) {
                const domid = xbb.guid()
                commit('SET_WATER_MARK_ID', domid)
                commit('SET_WATER_MARK_CONFIG', waterMark)
                createWaterMark(company.corpName, user.name, waterMark, domid)
              }
              // 直播信息
              if (liveStatus && liveStartTime) {
                commit('SET_LIVE_STREAMING_INFO', {
                  liveStatus,
                  liveStartTime
                })
              }
              resolve(result)
              // 当前登录账户的【企微】公司,是否拥有企微【基础账号/互通账号】
              if (company.source !== ENUM_LOGIN_COMPANY_TYPE.THIRD_WEXIN_SOURCE.code) return
              verifyHasLicense().then(
                ({ result = { licenseCodeType: [], apiPayBusinessTypeList: [] } }) => {
                  commit('SET_COMPANY_LICENSE_ACCOUNT', result.licenseCodeType)
                  commit('SET_COMPANY_LICENSE_BUSINESS_TYPE', result.apiPayBusinessTypeList)
                }
              )
            }
          )
          .catch((error) => {
            console.error('[/user/info] 无法请求到用户信息', error)
            bus.$emit('login-failed', error)
            reject(error)
          })
      })
    },
    /**
     * 登出
     */
    signOut(state, data = { code: '', msg: '' }) {
      const { code, msg } = data
      utils.LS.remove('userId')
      const env = utils.LS.get('env')
      // 独立版默认登陆页面
      let url = '/stand-alone-login.html'
      let loginPath = '/login.html'
      if (+localStorage.getItem('systemKind') === 2) {
        utils.LS.remove('systemKind')
        // 服务云独立上架 登出需要到独立上架登录页
        loginPath = '/work-order-login.html'
      }
      // 独立版
      if (env === 'h5') {
        // 给一些错误提示
        if (msg || code === 100112) {
          Message({
            message: msg,
            type: 'error',
            duration: 3000,
            onClose: (_) => {
              utils.LS.remove('crtCompany')
              window.location = url
            }
          })
          return
        }
      } else if (env === 'lark') {
        url = `${loginPath}?isLarkLogin=true` // 飞书登出
      } else if (env === 'wx') {
        // 企业微信判断
        utils.SS.remove('useReminderDialogShow')
        url = `${loginPath}?isWXLogin=true`
      }
      window.location = url
    },
    checkLicense({ dispatch, commit }) {
      return new Promise((resolve, reject) => {
        checkLicense()
          .then((res) => {
            const { memo, setAccount, useLimit } = res.result
            let userType = 'normal'
            if (setAccount) {
              userType = 'admin'
            }
            utils.LS.set('userType', userType)
            utils.LS.set('userMemo', memo)
            commit('SET_USELIMIT', useLimit)
            if (useLimit) {
              reject()
            } else {
              resolve()
            }
          })
          .catch(() => {})
      })
    },
    setCurRouter({ commit }, payload) {
      commit('SET_CUR_ROUTER', payload)
    },

    setListFilter({ commit }, payload) {
      commit('SET_LIST_FILTER', payload)
    },
    setUipaasHome({ commit }, payload) {
      commit('SET_UIPAAS_HOME', payload)
    }
  }
}
