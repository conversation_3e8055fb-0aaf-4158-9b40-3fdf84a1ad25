/*
 * @Description: 导入的数据
 */
import BUSINESS_TYPE from '@/constants/common/business-type'
import { getTemplateList } from '@/api/formData.js'
// 工单获取模板
import { getWorkOrderV2ExportTemplate } from '@/api/work-order-v2/common.js'

// const commonImport = function (params) {
//   return new Promise((resolve, reject) => {
//     getTemplateList(params).then((data) => {
//       resolve(data)
//     })
//   })
// }
export default {
  namespaced: true,
  state: {
    importInfo: {
      title: '',
      importType: 'common',
      appId: -1,
      formId: -1,
      menuId: -1,
      saasMark: 1,
      businessType: -1,
      subBusinessType: -1
    },
    templateList: [], // 模板列表
    ImportPercent: 0, // 导入的进度
    step: 0, // 导入所处的阶段
    dialogShow: false, // 导入的弹窗显示
    isFileImport: false, // 是否带附件导入
    advertisementView: false // 广告助手弹框
  },
  getters: {
    getDialogShow: (state) => state.dialogShow,
    getImportInfo: (state) => state.importInfo,
    getAdvertisementView: (state) => state.advertisementView
  },
  mutations: {
    SET_DIALOG_SHOW(state, show) {
      // 设置弹窗显示的状态
      state.dialogShow = show
    },
    SET_IMPORT_INFO(state, info) {
      Object.assign(state.importInfo, info)
    },
    SET_TEMPLATE_LIST(state, templateList) {
      state.templateList = templateList
    },
    SET_IMPORT_PERCENT(state, percent) {
      // 设置导入百分比
      state.ImportPercent = percent
    },
    SET_IMPORT_STEP(state, step) {
      // 设置导入百分比
      state.step = step
    },
    SET_IS_FILE_IMPORT(state, isFileImport) {
      // 是否带附件导入
      state.isFileImport = isFileImport
    },
    SET_ADVERTISEMENT_VIEW(state, show) {
      // 设置弹窗显示的状态
      state.advertisementView = show
    }
  },
  actions: {
    setDialogShow: ({ commit }, show) => {
      // 设置弹窗显示的状态
      commit('SET_DIALOG_SHOW', show)
    },

    /**
     * @description: 打开导入弹窗
     * @param {type}
     * @return:
     */
    openImportDialog: (
      { commit },
      {
        title,
        appId,
        menuId,
        formId,
        saasMark,
        distributorMark,
        businessType,
        subBusinessType,
        importType = 'common',
        knowledgeBaseId // 知识库场景时才有
      }
    ) => {
      const info = {
        title,
        appId,
        menuId,
        formId,
        saasMark,
        distributorMark,
        businessType,
        subBusinessType,
        importType
      }

      if (Number(businessType) === BUSINESS_TYPE.KNOWLEDGE_BASE) {
        info.knowledgeBaseId = knowledgeBaseId
      }

      commit('SET_IMPORT_INFO', info)
      let promise
      // 业绩目标的导入
      if (importType === 'performance') {
        promise = new Promise((resolve, reject) => {
          // commit('SET_TEMPLATE_LIST')
          resolve()
        })
      } else {
        // 正常列表页的获取模板
        promise = new Promise((resolve, reject) => {
          const params = {
            appId,
            menuId,
            saasMark,
            distributorMark,
            subBusinessType,
            businessType
          }
          promise = params.businessType === 20300 ? getWorkOrderV2ExportTemplate : getTemplateList
          promise(params).then((data) => {
            commit('SET_TEMPLATE_LIST', data.result.formList)
            commit('SET_IS_FILE_IMPORT', data.result.isFileImport)
            // 导入时加入是否开启多单位的标识
            const info = { enbaleMultiUnit: data.result.enbaleMultiUnit }
            commit('SET_IMPORT_INFO', info)
            resolve()
          })
        })
      }
      // 打开弹窗
      promise.then(() => {
        commit('SET_DIALOG_SHOW', true)
      })
    },
    setImportInfo: ({ commit }, info) => {
      // 设置导入需要的参数
      commit('SET_IMPORT_INFO', info)
    },

    /**
     * @description: 设置导入进度百分比
     * @param {type}
     * @return:
     */
    setImportPercent({ commit }, percent) {
      commit('SET_IMPORT_PERCENT', percent)
    },

    /**
     * @description: 设置导入进度百分比
     * @param {type}
     * @return:
     */
    setImportStep({ commit }, step) {
      commit('SET_IMPORT_STEP', step)
    },
    setAdvertisementView: ({ commit }, show) => {
      // 设置弹窗显示的状态
      commit('SET_ADVERTISEMENT_VIEW', show)
    }
  }
}
