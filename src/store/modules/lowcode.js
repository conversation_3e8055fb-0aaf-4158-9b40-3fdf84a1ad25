import { getLowCodeConfig } from '@/api/low-code.js'
import { addMessage, clearMessage, deleteMessage, lowCodeInit } from '@/utils/low-code-utils'
import lowCodeFactory from '@/utils/low-code-factory'
import xbb from '@xbb/xbb-utils'
import vue from 'vue'

export default {
  state: {
    instant: {},
    initLock: []
  },
  getters: {
    lowCodeConfig: (state) => state.instant
  },
  mutations: {
    INIT_LOW_CODE(state, val = { formInfo: {}, component: {}, callback: () => {} }) {
      console.log(
        '%c [ component ] 🐱-17',
        'font-size:13px; background:pink; color:#bf2c9f;',
        val.component
      )
      if (!val.formInfo) {
        val.callback(false)
        return
      }
      // 处理并发 init 列表页的低代码还在初始化 就进入了新建页 这个时候会造成初始化两次的情况
      if (state.initLock.indexOf(val.formInfo.formId) !== -1) {
        const interval = setInterval(() => {
          if (state.initLock.indexOf(val.formInfo.formId) === -1) {
            clearInterval(interval)
            const formInstant = state.instant[val.formInfo.formId]
            if (formInstant) {
              if (!formInstant.history.includes(val.component)) {
                formInstant.history.push(val.component)
                addMessage(formInstant.config.lowCodeJsFile.fileId, val.component.lowCodeMessage)
                // formInstant.containerInstant.changeMessageHandle(val.component.lowCodeMessage)
                val.component.lowCodeMessage('js-runner-ready')
                val.callback(formInstant)
              }
            } else {
              val.callback(false)
            }
          }
        }, 500)
        return
      }
      const formInstant = state.instant[val.formInfo.formId]
      const releaseInitLock = () => {
        const idx = state.initLock.findIndex((n) => n === val.formInfo.formId)
        state.initLock.splice(idx, 1)
      }
      if (formInstant) {
        console.log({ formInstant })
        if (!formInstant.history.includes(val.component)) {
          formInstant.history.push(val.component)
          addMessage(formInstant.config.lowCodeJsFile.fileId, val.component.lowCodeMessage)
          // formInstant.containerInstant.changeMessageHandle(val.component.lowCodeMessage)
          val.component.lowCodeMessage('js-runner-ready')
          val.callback(formInstant)
        } else {
          val.callback(formInstant)
        }
      } else {
        state.initLock.push(val.formInfo.formId)
        getLowCodeConfig(val.formInfo)
          .then((res) => {
            if (xbb._isEmpty(res.result.lowCodeConfig.lowCodeJsFile)) {
              releaseInitLock()
              val.callback && val.callback(false)
            } else {
              const config = Object.assign(
                {
                  messageHandler: val.component.lowCodeMessage,
                  isDev:
                    val.component.formMode === 'preview'
                      ? true
                      : localStorage.getItem('js-file-status') === 'dev'
                },
                res.result
              )
              lowCodeInit(config)
                .then((instant) => {
                  if (instant) {
                    vue.set(state.instant, val.formInfo.formId, {
                      config: res.result.lowCodeConfig,
                      history: [val.component],
                      lowCodeMessage: val.component.lowCodeMessage,
                      containerInstant: instant
                    })
                    releaseInitLock()
                    val.callback(state.instant[val.formInfo.formId])
                    val.component.lowCodeMessage('js-runner-ready')
                  } else {
                    releaseInitLock()
                    val.callback(false)
                  }
                })
                .catch((_) => {
                  releaseInitLock()
                  val.callback(false)
                })
            }
          })
          .catch((_) => {
            releaseInitLock()
            val.callback(false)
          })
      }
    },
    UN_BIND_LOW_CODE(state, val = { formInfo: {}, component: {} }) {
      if (!state.instant[val.formInfo.formId]) return
      // state.instant[val.formInfo.formId].history.length -= 1
      const filedId = state.instant[val.formInfo.formId].config.lowCodeJsFile.fileId
      const instant = state.instant[val.formInfo.formId].history.pop()
      if (!instant) return
      deleteMessage(filedId, instant.lowCodeMessage)
      const length = state.instant[val.formInfo.formId].history.length
      if (length === 0) {
        state.instant[val.formInfo.formId].containerInstant.destroy()
        state.instant[val.formInfo.formId] = null
        clearMessage(filedId)
      }
    },
    EXECUTE_LOW_CODE(state, val) {
      const { handle, info, action, formId, dataJSON } = val
      if (state.instant[formId]) {
        const fileId = state.instant[formId].config.lowCodeJsFile.fileId
        lowCodeFactory({ handle, info, action, dataJSON, containerID: `iframe${fileId}` })
          .then((res) => {
            val.callback && val.callback(res)
          })
          .catch((err) => {
            console.error(err)
            val.callback && val.callback(err)
          })
      }
    },
    EXECUTE_LOW_CODE_PROMISE(state, val) {
      const { handle, info, action, formId, dataJSON } = val
      if (state.instant[formId]) {
        const fileId = state.instant[formId].config.lowCodeJsFile.fileId
        return lowCodeFactory({ handle, info, action, dataJSON, containerID: `iframe${fileId}` })
      }
    }
  },
  actions: {
    async EXECUTE_LOW_CODE_PROMISE({ commit, state }, val) {
      const { handle, info, action, formId, dataJSON } = val
      if (state.instant[formId]) {
        const fileId = state.instant[formId].config.lowCodeJsFile.fileId
        return lowCodeFactory({ handle, info, action, dataJSON, containerID: `iframe${fileId}` })
      }
    }
  }
}
