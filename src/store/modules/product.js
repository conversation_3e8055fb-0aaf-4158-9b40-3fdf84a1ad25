/*
 * @Description: 产品相关的vuex
 */
import * as types from '../mutation-types'

const state = {
  couldSwitchNode: true,
  currentNode: {}, // 产品分类当前操作的节点
  menuUnfold: [], // 展开的菜单分类
  productNo: '' //
}

// getters
const getters = {
  getProductNo: (state) => state.productNo,
  getMenuUnfoldList: (state) => state.menuUnfold,
  getCurrentNode: (state) => state.currentNode,
  ifCouldSwitchNode: (state) => state.couldSwitchNode // 是否可以更改操作节点
}

// actions
const actions = {
  setProductNo({ commit }, payload) {
    commit(types.PRODUCT_NO, payload)
  },
  // openFoldMenu ({ commit }, id) { // 打开折叠分类菜单

  // },
  updateMenuUnfold({ commit }, id) {
    // 更新折叠分类菜单的id
    if (id) {
      // 更新
      commit('UPDATE_MENU_UNFOLD', id)
    } else {
      // 清空
      commit('CLEAR_MENU_UNFOLD')
    }
  },
  setCurrentNode({ commit }, currentNode) {
    // 设置当前操作的节点
    commit('SET_CURRENT_NODE', currentNode)
  },
  changeSwitchNodeStatus({ commit }, status) {
    // 更改操作当前节点的状态开关
    commit('CHANGE_NODE_STATUS', status)
  }
}

// mutations
const mutations = {
  [types.PRODUCT_NO](state, status) {
    state.productNo = status
  },
  CLEAR_MENU_UNFOLD() {
    // 清空折叠分类
    state.menuUnfold = []
  },
  // OPEN_FOLD_MENU(state, id) { // 打开折叠菜单
  //   let index = state.menuUnfold.indexOf(id)
  //   if (index > -1) {
  //     state.menuUnfold.splice(index, 1)
  //   } else {
  //     state.menuUnfold.push(id)
  //   }
  // },
  UPDATE_MENU_UNFOLD(state, id) {
    // 更新折叠分类菜单的id
    if (Array.isArray(id)) {
      // 数组时，直接覆盖，批量菜单状态更新
      state.menuUnfold = id
    } else {
      // 单个菜单的更新
      const index = state.menuUnfold.indexOf(id)
      if (index > -1) {
        state.menuUnfold.splice(index, 1)
      } else {
        state.menuUnfold.push(id)
      }
    }
  },
  SET_CURRENT_NODE(state, currentNode) {
    state.couldSwitchNode && (state.currentNode = currentNode)
  },
  CHANGE_NODE_STATUS(state, status) {
    state.couldSwitchNode = status
  }
}

export default {
  state,
  getters,
  actions,
  mutations
}
