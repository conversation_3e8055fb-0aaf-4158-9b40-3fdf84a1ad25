/*
 * @Description: 表单新建、编辑，跟选择客户相关的vuex
 */

const state = {
  communicateTime: null, // 拜访时间
  customerType: null, // 客户类型
  customerTypeTemp: null, // 客户类型暂存
  transClueId: null // 转换客户的线索id
}
const getters = {
  /**
   * 获取选择人的关联参数
   */
  customerRelevantParams:
    (state) =>
    ({ saasAttr, businessType, relyBusiness, relySubModel } = {}) => {
      const params = {
        parentBusinessType: relyBusiness
      }
      if (saasAttr === 'finishCommunicatePlanId') {
        // 同步完成访客计划需限定过滤条件
        return Object.assign(params, {
          conditions: [
            // 过滤属性
            {
              attr: 'date_1',
              subAttr: '',
              fieldType: 4,
              symbol: 'range',
              value: [
                state.communicateTime && state.communicateTime.dayStart.toString(),
                state.communicateTime && state.communicateTime.dayEnd.toString()
              ]
            },
            {
              attr: 'text_11',
              subAttr: '',
              fieldType: 2,
              symbol: 'in',
              value: [1, 4]
            }
          ]
        })
      }
      return params
    },
  communicateTime: (state) => {
    return state.communicateTime
  },
  customerType: (state) => {
    return state.customerType
  }
}
const mutations = {
  /**
   * CRM跟进业务，设置选择的客户
   */
  SET_CUSTOMER_SELECTED(state, val) {
    if (!val) {
      state.customerType = null
      state.customerTypeTemp = null
    }
  },
  /**
   * 初始化参数
   */
  SET_CUSTOMER_COMMUNICATE_DEFAULT(state) {
    state.communicateTime = null
    state.customerType = null
    state.customerTypeTemp = null
  },
  /**
   * CRM跟进业务， 设置拜访时间
   */
  SET_CUSTOMER_COMMUNICATE_TIME(state, val) {
    state.communicateTime = val
  },
  /**
   * CRM跟进业务， 设置客户状态
   */
  SET_CUSTOMER_TYPE(state) {
    state.customerType = state.customerTypeTemp
  },
  SET_CUSTOMER_TYPE_TEMP(state, val) {
    state.customerTypeTemp = val
  },
  /**
   * CRM跟进业务， 编辑时客户状态回显
   */
  SET_DEFAULT_CUSTOMER_TYPE(state, val) {
    state.customerTypeTemp = val
    state.customerType = state.customerTypeTemp
  },
  // 转换客户
  SET_TRANS_ClUEID(state, val) {
    state.transClueId = val
  }
}
const actions = {}

export default {
  state,
  getters,
  mutations,
  actions
}
