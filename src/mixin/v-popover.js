/*
 * @Description: 自定义的popover的mixin
 */
import vPopover from '@/components/common/v-popover'
import baseTableCell from '@/components/list/components/base-table-cell'
import xbb from '@xbb/xbb-utils'
import PopupManager from 'element-ui/lib/utils/popup/popup-manager'

export default {
  data() {
    return {
      quickEditFieldMount: false, // 快速编辑的字段是否加载完成
      popShow: {
        quickEdit: false,
        previewTable: false
      },
      isFilterPopShow: false,
      popoverStyle: {},
      currentEditRow: {},
      fieldInfo: '',
      dataId: '',
      previewTable: {
        params: {}
      },
      clickPosition: null // 点击的位置
    }
  },
  components: {
    vPopover,
    baseTableCell
  },
  methods: {
    /**
     * @description: 快速编辑字段加载完成（因为是懒加载，所以这里需要特殊处理
     * @param {type}
     * @return:
     */
    fieldMounted() {
      this.quickEditFieldMount = true
    },

    /**
     * @description: 关闭快速编辑的弹窗
     * @param {type}
     * @return:
     */
    closeEdit() {
      this.popShow = {
        quickEdit: false,
        previewTable: false
      }
      this.$nextTick(() => {
        this.popoverStyle = {}
      })
    },

    /**
     * @description: 更新单元格
     * @param {type}
     * @return:
     */
    updateCell({ dataId, changeValueList }) {
      const tableData = xbb.deepClone(this.tableData).map((item) => {
        if (item.dataId === dataId) {
          changeValueList.forEach((changeValueItem) => {
            item[changeValueItem.attr] = changeValueItem.value
            item.sourceData[changeValueItem.attr] = changeValueItem.sourceData
          })
        }
        return item
      })
      this.$emit('editClick', {
        tableData
      })
    },

    /**
     * @description: 打开查看子表单更多弹窗
     * @param {type}
     * @return:
     */
    openPreviewTable({ dataId, attr, saasMark, businessType, formId }, event) {
      // 存子表单查询参数
      this.previewTable.params = {
        dataId,
        attr,
        saasMark,
        businessType,
        formId
      }
      this.popShow.previewTable = true
      const rectObject = event.target.getBoundingClientRect()
      this.$nextTick(() => {
        this.positionPopover(rectObject)
      })
    },
    openAssignPopover(event) {
      this.popShow.quickEdit = true
      const rectObject = event.target.getBoundingClientRect()
      this.$nextTick(() => {
        this.positionPopover(rectObject)
      })
    },
    /**
     * @description: 打开快速编辑的弹窗
     * @param {type}
     * @return:
     */
    quickEdit({ id, attr }, event) {
      let head
      let row
      this.tableHead.forEach((item) => {
        item.attr === attr && (head = item)
      })
      this.tableData.forEach((item) => {
        item.id === +id && (row = item)
      })
      this.currentEditRow = row
      this.fieldInfo = head
      this.dataId = row.dataId
      this.popShow.quickEdit = true
      const rectObject = event.target.getBoundingClientRect()
      // 下面需要检测是否已经加载完成字段
      let quickEditWatchTime = null
      if (this.quickEditFieldMount) {
        this.quickEditFieldMount = false
        this.positionPopover(rectObject)
      } else {
        quickEditWatchTime = setInterval(() => {
          if (this.quickEditFieldMount) {
            clearInterval(quickEditWatchTime)
            this.quickEditFieldMount = false
            this.positionPopover(rectObject)
          }
        }, 50)
      }
    },

    /**
     * @description: 给弹窗定位
     * @param {type}
     * @return:
     */
    positionPopover(sendClickPosition, ref = 'vPopover') {
      // 如果没有点击的位置传过来，则使用上一次的位置
      if (sendClickPosition) {
        this.clickPosition = sendClickPosition
      }
      const { left, right, top, bottom } = this.clickPosition
      const box = this.$refs[ref].$el
      setTimeout(() => {
        // 弹窗
        const boxSize = {
          width: box.offsetWidth,
          height: box.offsetHeight
        }
        // let fixLeft = (left + right - boxSize.width) / 2 - mainPageLeft - 60
        let fixLeft = (left + right - boxSize.width) / 2
        // let fixTop = bottom + 5 - mainPageTop
        let fixTop = bottom + 5
        boxSize.right = fixLeft + boxSize.width
        boxSize.bottom = fixTop + boxSize.height
        // 右侧超出窗口
        // boxSize.right > window.innerWidth && (fixLeft = window.innerWidth - boxSize.width)
        boxSize.right > window.innerWidth && (fixLeft = left - boxSize.width)
        // 底部超出窗口
        if (boxSize.bottom > window.innerHeight) {
          fixTop = top - boxSize.height
          if (fixTop < 0) fixTop = 0
        }
        // boxSize.bottom > window.innerHeight && (fixTop -= boxSize.bottom - window.innerHeight)
        // 左侧超出菜单栏
        if (fixLeft < 170) {
          fixLeft = 170
        }
        this.popoverStyle = {
          zIndex: PopupManager.nextZIndex(),
          left: `${fixLeft}px`,
          top: `${fixTop}px`
        }
      }, 100)
    },

    /**
     * @description: 打开表头筛选弹窗
     * @param {type}
     * @return:
     */
    openPopoverForFilter(column, event) {
      this.isFilterPopShow = true
      const rectObject = event.target.getBoundingClientRect()
      this.positionPopover(rectObject, 'filter')
    },
    closeFilterPop() {
      console.log('close')
      setTimeout(() => {
        this.isFilterPopShow = false
        this.$nextTick(() => {
          this.popoverStyle = {}
        })
      }, 150)
    }
  }
}
