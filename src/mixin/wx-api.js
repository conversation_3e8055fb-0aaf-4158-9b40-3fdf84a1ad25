/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-03-12 11:30:09
 * @LastEditors: zihao.chen
 * @LastEditTime: 2021-08-16 12:46:44
 * @Description: 微信需要使用的JS接口
 */
const JsApiList = [
  'openDefaultBrowser' // 打开系统默认浏览器
  // 'openEnterpriseChat'
]

const AgentJsApiList = [
  'setClipboardData', // 剪切板
  'selectPrivilegedContact',
  'openAppManage',
  'openEnterpriseChat', // 一键群发，调起员工会话
  'openExistedChatWithMsg' // 打开群聊
]

const WecomJsApiList = [
  'openDefaultBrowser',
  'setClipboardData', // 剪切板
  'selectPrivilegedContact',
  'openAppManage',
  'openEnterpriseChat', // 一键群发，调起员工会话
  'openExistedChatWithMsg', // 打开群聊
  'wwapp.invokeJsApiByCallInfo'
]

export { JsApiList, AgentJsApiList, WecomJsApiList }
