export default {
  computed: {
    filterExplainList() {
      const currentFieldInfo = this.formHead.find(
        (fieldInfo) => fieldInfo.attr === this.fieldInfo.attr
      )
      if (!currentFieldInfo) return []
      const allParentDepField = this.findAllParentDepField(currentFieldInfo)
      const allChildDepField = this.findAllChildDepField(currentFieldInfo)
      return [...allParentDepField, currentFieldInfo, ...allChildDepField]
    }
  },
  methods: {
    findAllParentDepField(currentFieldInfo) {
      // 查找所有的上级字段
      const parentFieldAttr = currentFieldInfo && currentFieldInfo.superAttr
      const parentFieldInfo = this.formHead.find((fieldInfo) => fieldInfo.attr === parentFieldAttr)
      if (!parentFieldAttr || !parentFieldInfo) return []
      return [...this.findAllParentDepField(parentFieldInfo), parentFieldInfo]
    },
    findAllChildDepField(currentFieldInfo) {
      // 查找所有下级字段
      if (!currentFieldInfo.items) return []
      const childFieldAttrs = currentFieldInfo.items
        .filter((item) => item.subAttrList)
        .map((item) => Object.keys(item.subAttrList))
        .flat()
      const filterFieldList = this.formHead.filter((fieldInfo) => {
        // 子表单字段筛选不做
        return childFieldAttrs.includes(fieldInfo.attr)
      })
      // 最终返回所有下级字段解释
      return filterFieldList.reduce((prev, fieldInfo) => {
        if (prev.findIndex((field) => field.attr === fieldInfo.attr) > -1) return [...prev]
        return [...prev, fieldInfo, ...this.findAllChildDepField(fieldInfo)]
      }, [])
    }
  }
}
