/*
 * @Description:
 */
import { FieldTypeEnum } from '@/constants/enum/fieldType.js'
import BaseInfo from './BaseInfo.js'

class DateTypeEnum {
  static datePicker = 'yyyy-MM-dd'

  static dateTimePicker = 'yyyy-MM-dd HH:mm'
}
export default class InputDate extends BaseInfo {
  constructor(options = {}) {
    super(options)
    this.initDefaultAttr(options)
    this.init(options)
  }

  init({ dateType = DateTypeEnum.datePicker }) {
    this.dateType = dateType

    this.fieldType = FieldTypeEnum.inputDate
  }
}
