/*
 * @Description: 总停留时长——阶段推进器专用
 */
// import i18n from '@/lang'
import BaseInfo from './BaseInfo.js'
import { FieldTypeEnum } from '@/constants/enum/fieldType.js'

export default class StageStayTime extends BaseInfo {
  constructor(options) {
    super(options)
    this.init(options)
  }

  init() {
    this.fieldType = FieldTypeEnum.stageStayTime
    // this.unableEditMemo = i18n.t('utils.systemCreatedAttr')
    this.editable = 0
    this.showEditable = 0 // 不展示可编辑
    this.showType = 6 // 列表、详情显示
    this.isOpen = 1
    this.screenType = 0 // 不支持BI
  }
}
