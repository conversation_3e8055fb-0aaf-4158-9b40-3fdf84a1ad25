/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 */
body{margin:1rem;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Oxygen,Ubuntu,Cantarell,'Open Sans','Helvetica Neue',sans-serif;line-height:1.4}table{border-collapse:collapse}table:not([cellpadding]) td,table:not([cellpadding]) th{padding:.4rem}table[border]:not([border="0"]):not([style*=border-width]) td,table[border]:not([border="0"]):not([style*=border-width]) th{border-width:1px}table[border]:not([border="0"]):not([style*=border-style]) td,table[border]:not([border="0"]):not([style*=border-style]) th{border-style:solid}table[border]:not([border="0"]):not([style*=border-color]) td,table[border]:not([border="0"]):not([style*=border-color]) th{border-color:#ccc}figure{display:table;margin:1rem auto}figure figcaption{display:block;margin-top:.25rem;color:#969799;text-align:center}hr{border-color:#cccccc;border-style:solid;border-width:1px 0 0 0}code{padding:.1rem .2rem;background-color:#e8e8e8;border-radius:3px}.mce-content-body:not([dir=rtl]) blockquote{padding-left:1rem;margin-left:1.5rem;border-left:2px solid #cccccc}.mce-content-body[dir=rtl] blockquote{padding-right:1rem;margin-right:1.5rem;border-right:2px solid #cccccc}
