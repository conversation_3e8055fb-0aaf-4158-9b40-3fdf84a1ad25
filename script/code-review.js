/*
 * @Description: code-review脚本 在需要review的分支上 运行命令。例如: npm run review hotfix/v4.42.0
 */
const git = require('simple-git')()
// const chalk = require('chalk')

const BASE_URL = '************'

// fetch = 'git@************:xbongbong/xbb-pro-web-front.git'
// fetch = 'http://************/xbongbong/xbb-pro-web-front.git'
const regex = new RegExp(`git@${BASE_URL}:|http:\/\/${BASE_URL}\/|.git`, 'g') // 匹配 fetch 上，检出仓库名

/**
 * 获取当前分支 - 本地.git
 * */
async function getNowBrancn () {
  const ret = await git.branch()
  if (!ret) {
    return false
  }
  return ret.current
}

const targetRemote = process.argv[2] || 'master' // 目标分支 一般视为master 或者 需要上线的分支

/**
 * @description 获得仓库地址
 * @returns 仓库名 'xbongbong/xbb-pro-web-front'
 */
async function getRepoName () {
  const isRepo = await git.checkIsRepo() // git判断 是否仓库
  if (!isRepo) {
    console.log(chalk.red('当前目录不存在git仓库!'))
    process.exit()
  }
  const remotes = await git.getRemotes(true)
  if (!remotes || remotes.length < 1) {
    return false
  } else {
    return remotes[0].refs.push.replace(regex, '') // xbongbong/xbb-pro-web-front
  }
}

/**
 * @description 分支比较
 * @param {String} [targetRemote] 目标分支 hotfix/v4.42.0
 */
async function codeCompare (targetRemote) {
  const repoName = await getRepoName() // xbongbong/xbb-pro-web-front
  const nowBranch = await getNowBrancn() // hotfix/v4.41.0
  // let compareUrl = BASE_URL
  const targetEncode = encodeURIComponent(targetRemote)
  const nowBranchEncode = encodeURIComponent(nowBranch)
  const compareUrl = `http://${BASE_URL}/${repoName}/compare/${targetEncode}...${nowBranchEncode}`
  // console.log(chalk.green(`点击链接，查看分支比较结果：${compareUrl}`))
  console.log(`点击链接，查看分支比较结果：${compareUrl}`)
}

codeCompare(targetRemote) // 分支代码比较 用于code review
